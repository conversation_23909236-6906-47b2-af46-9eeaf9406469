import { debounce, uniq } from "lodash";
import { useEffect, useState } from "react";
import { getCircularArray } from "app/utils";

const RANGE_LAZY_RIBBONS = 1;

const handleMoving = debounce(
  (callback) => {
    callback();
  },
  350,
  { leading: false, trailing: true }
);
interface Options {
  ribbonsLength: number;
  focusIndex: number;
}

export default function useLazyRibbons({
  ribbonsLength,
  focusIndex,
}: Options): number[] {
  const [listLazy, setListLazy] = useState<number[]>([]);

  useEffect(() => {
    const list = uniq(
      getCircularArray(RANGE_LAZY_RIBBONS, ribbonsLength, focusIndex)
    );
    handleMoving(() => {
      setListLazy(list);
    });
  }, [ribbonsLength, focusIndex]);

  return listLazy;
}
