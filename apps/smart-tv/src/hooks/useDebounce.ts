import { useEffect, useState } from "react";

export default function useDebounce(
  value: any,
  delay: number,
  startDebounce?: (value?: any) => any,
  endDebounce?: (value?: any) => any
) {
  // State and setters for debounced value
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(
    () => {
      // Set debouncedValue to value (passed in) after the specified delay
      if (startDebounce) {
        startDebounce(debouncedValue);
      }
      const handler = setTimeout(() => {
        setDebouncedValue(value);
        if (endDebounce) {
          endDebounce(value);
        }
      }, delay);

      // Return a cleanup function that will be called every time ...
      // ... useEffect is re-called. useEffect will only be re-called ...
      // ... if value changes (see the inputs array below).
      // This is how we prevent debouncedValue from changing if value is ...
      // ... changed within the delay period. Timeout gets cleared and restarted.
      // To put it in context, if the user is typing within our app's ...
      // ... search box, we don't want the debouncedValue to update until ...
      // ... they've stopped typing for more than 500ms.
      return () => {
        clearTimeout(handler);
      };
    },
    // Only re-call effect if value changes
    // You could also add the "delay" var to inputs array if you ...
    // ... need to be able to change that dynamically.
    [value]
  );
  return debouncedValue;
}
