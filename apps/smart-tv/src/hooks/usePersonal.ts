import { useEffect, useMemo } from "react";
import { useSelector } from "react-redux";
import { useHistory } from "react-router-dom";
import { RootState } from "app/store/store";
import DialogCommon from "app/components/DialogCommon";
import SegmentManager from "app/utils/SegmentManager";
import platform from "services/platform";
import { DialogImg, DialogTitle } from "app/utils/DialogInfo";
import DialogV2 from "app/components/DialogV2";
import { ROUTES } from "app/utils/constants";
import { AccountModel } from "app/models";
import useStateRef from "./useStateRef";

export default function usePersonal(
  isShowDialog?: boolean
): [
  canShowDialog: boolean,
  isShowPersonalPopup: boolean,
  showDialog: () => void,
  setShownGuestFlow: (value?: boolean, isError?: boolean) => void
] {
  const history = useHistory();
  const isAuthen = useSelector((state: RootState) => state.app.isAuthen);
  const guestFlow = useSelector((state: RootState) => state.app.guestFlow);
  const userFlow = useSelector((state: RootState) => state.app.userFlow);
  const profile = useSelector(
    (state: RootState) => state.app.profile
  ) as AccountModel;
  const [isShowPersonalPopup, setIsShowPersonalPopup] = useStateRef(false);
  const loc = {
    pathname: ROUTES.PERSONALIZATION,
  };
  const showDialog = (type = 0) => {
    if (type === 1) {
      DialogV2.guestWelcome({
        onOk: () => {
          setIsShowPersonalPopup(false);
          history.push(ROUTES.LOGIN_STEP);
          SegmentManager.segmentAction("registration button selected", {
            current_page: "Trang chủ",
            popup_name: "Dùng thử",
          });
        },
        onCancel: () => {
          setIsShowPersonalPopup(false);
          history.replace(loc);
          SegmentManager.segmentAction("trial button selected");
        },
      });
    } else {
      DialogCommon.commonMinimalDialog({
        keyName: "dialogPersonal",
        type: "normal",
        title: DialogTitle.PERSONAL,
        image: DialogImg.NON_VIP,
        imagePosition: "bottom",
        maskBackground: "#222",
        titleAlign: "center",
        actions: [
          {
            title: "Đăng nhập/Đăng ký",
            onClick: () => {
              setIsShowPersonalPopup(false);
              history.push(ROUTES.LOGIN_STEP);
              setShownGuestFlow(false);
              SegmentManager.segmentAction("registration button selected", {
                current_page: "Trang chủ",
                popup_name: "Dùng thử",
              });
            },
          },
          {
            title: "Dùng thử",
            onClick: () => {
              setIsShowPersonalPopup(false);
              history.replace(loc);
              SegmentManager.segmentAction("trial button selected");
            },
          },
        ],
        onOpen: () => {
          setIsShowPersonalPopup(true);
        },
      });
    }
  };
  const canShowDialog = useMemo(() => {
    const shownGuestFlow = !!localStorage.getItem("shownGuestFlow");
    const shownGuestFlowSession = !!sessionStorage.getItem("shownGuestFlow");
    const { onPromotion, onAutoPromotion } = platform;
    if (onPromotion || onAutoPromotion) {
      return false;
    }
    // / OFF flow theo config
    if (isAuthen) {
      return userFlow
        ? !shownGuestFlowSession && profile?.showGuestidFlow
        : false;
    }
    return guestFlow ? !shownGuestFlow : false;

    //
    // return isAuthen
    //   ? !shownGuestFlowSession && profile?.show_guestid_flow
    //   : !shownGuestFlow;
  }, [isAuthen, profile]);
  useEffect(() => {
    if (canShowDialog && isShowDialog) {
      if (!isAuthen) {
        const isStreamLogout = !!sessionStorage.getItem("isStreamLogout");
        showDialog(isStreamLogout ? 1 : 0);
        sessionStorage.removeItem("isStreamLogout");
      } else {
        history.replace(loc);
      }
    }
  }, [isShowDialog, canShowDialog]);
  const setShownGuestFlow = (value?: boolean, isError?: boolean) => {
    if (value) {
      if (isError && isAuthen) {
        sessionStorage.setItem("shownGuestFlow", `${true}`);
      } else {
        localStorage.setItem("shownGuestFlow", `${true}`);
      }
    } else {
      localStorage.removeItem("shownGuestFlow");
      sessionStorage.removeItem("shownGuestFlow");
    }
  };

  return [canShowDialog, isShowPersonalPopup, showDialog, setShownGuestFlow];
}
