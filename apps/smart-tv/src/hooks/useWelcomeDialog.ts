import { LocationState } from "history";
import get from "lodash/get";
import { useEffect, useRef, useCallback, useMemo } from "react";
import { useSelector } from "react-redux";
import { useHistory, useLocation } from "react-router-dom";
import dayjs from "dayjs";
import { Content, DialogType } from "context/WelcomeDialogContext";
import { RootState } from "app/store/store";
import TVODDialogSlot from "app/components/TVODDialog/data";
import WelcomeAdsSlot from "app/components/Ads/Welcome/data";
import platform from "services/platform";
import { ROUTES } from "app/utils/constants";
import {
  getInfoOfferForSegmentedUser,
  getPackageProm,
} from "services/endpoint";
import { FetchResult, FetchResultStatus, LocalStorageKey } from "types/common";
import { checkHomePage } from "app/utils/Route";
import { ProfileModel } from "app/models/MultiProfile/ProfileModel";
import SegmentManager from "app/utils/SegmentManager";
import {
  TrackingOfferForSegmentedUserEventNames,
  TrackingOfferForSegmentedUserFlowName,
  TrackingOfferForSegmentedUserPropertyKeys,
} from "app/components/Tracking/TrackingOfferForSegmentedUser";
import MastheadAdsSlot from "app/components/Ads/MastheadAds/data";
import { OfferSegmentedUserResponse } from "types/endpoint/OfferSegmentedUser";
import useMakeCurrentPage from "./useMakeCurrentPage";
import useStateRef from "./useStateRef";
import useGetUserTypeName from "./useGetUserTypeName";
import useIsGlobal from "./useIsGlobal";
import { showDialogAnonymousUserOnboarding } from "../app/containers/MultiProfile/share";

const useWelcomeDialog = () => {
  const location = useLocation<LocationState>();
  const locationRef = useRef<LocationState>(location);
  const isAuthen = useSelector((state: RootState) => state.app.isAuthen);
  const restrictedCountry = useSelector(
    (state: RootState) => state.app.geo.restricted
  );
  const currentProfile = useSelector(
    (state: RootState) => state.app.currentProfile
  );
  const currentProfileRef = useRef<ProfileModel | null>(currentProfile);
  const history = useHistory();
  const waitCallbackFuncRef = useRef<any>(null);
  const isShowOnboardingRef = useRef<boolean>(false);
  const [queueDialog, setQueueDialog, queueDialogRef] = useStateRef<
    Array<Content>
  >([]);

  const [isDialogShowing, setIsDialogShowing, isDialogShowingRef] =
    useStateRef<boolean>(false);

  const { needShowOverlap, overlapInfo } = useSelector(
    (state: RootState) => state.app
  );

  const [dialog, setDialog] = useStateRef<Content | null>(null);
  const mastheadAdsSlot = useRef<any>();
  const welcomeAdsSlot = useRef<any>();
  const tvodDialogSlot = useRef<any>();
  const isRenderDialogAllowed = useRef<boolean>(false);

  const currentPage = useMakeCurrentPage(location);

  const typeOfUser = useGetUserTypeName();

  const isGlobal = useIsGlobal();

  useEffect(() => {
    mastheadAdsSlot.current = new MastheadAdsSlot();
    welcomeAdsSlot.current = new WelcomeAdsSlot();
    tvodDialogSlot.current = new TVODDialogSlot();
  }, []);

  useEffect(() => {
    currentProfileRef.current = currentProfile;
  }, [currentProfile]);

  useEffect(() => {
    locationRef.current = location;
  }, [location]);

  const getOfferForSegmentedUserDialog = useCallback(async () => {
    const isHomePage = checkHomePage(
      get(locationRef.current, "pathname", ""),
      currentPage.id
    );
    const dataLocalStorage = localStorage.getItem(
      LocalStorageKey.PromotionOfferSegmentedUser
    );
    const isShowOffer = Boolean(
      dataLocalStorage && dayjs(+dataLocalStorage).isSame(dayjs(), "day")
    );
    if (
      isShowOffer ||
      !isAuthen ||
      !currentProfileRef.current ||
      currentProfileRef.current?.isKid ||
      !isHomePage ||
      isGlobal
    ) {
      return null;
    }
    let info: FetchResult = {
      status: FetchResultStatus.FAIL,
      data: null,
    };
    try {
      const resProm = await getPackageProm({
        platform: platform.platformName,
        device_id: platform.vieONDeviceID,
        check_frequency: true,
      });

      const filterPromos = (resProm?.result?.promos ?? []).filter(
        (f: any) => f?.type === "PACKAGE_DISCOUNT"
      );
      const data = filterPromos?.[0];
      if (data) {
        const offerOEM = new OfferSegmentedUserResponse(data);
        offerOEM.userId = data.user?.id || "";
        offerOEM.packageGroupId = data.info?.[0].detail?.origin_package_id || 0;
        offerOEM.packageId = data.info?.[0].detail?.promo_package?.id || 0;
        if (offerOEM.packageGroupId !== 0 && offerOEM.packageId !== 0) {
          info.data = offerOEM;
          info.status = FetchResultStatus.SUCCESS;
        }
      }
    } catch (e) {
      // error
    }
    if (!info.data) {
      info = await getInfoOfferForSegmentedUser();
    }
    if (info.data) {
      localStorage.setItem(
        LocalStorageKey.PromotionOfferSegmentedUser,
        Date.now().toString()
      );
      SegmentManager.segmentAction(
        TrackingOfferForSegmentedUserEventNames.OfferSubscriptionScreenLoaded,
        {
          [TrackingOfferForSegmentedUserPropertyKeys.flowName]:
            TrackingOfferForSegmentedUserFlowName.OfferFreeToSub,
        }
      );
      return info.data;
    }
    return null;
  }, [isAuthen, isGlobal]);

  // Closed for task https://vieon.atlassian.net/browse/VFE-11996
  const checkOnboardingMultiProfile = useCallback(() => {
    const keyStorage = "show_multi_profile_onboarding";
    const hadOneDisplayBefore = localStorage.getItem(keyStorage);
    if (!hadOneDisplayBefore) {
      showDialogAnonymousUserOnboarding(typeOfUser);
      // countIncrease();
      localStorage.setItem(keyStorage, "showed");
      if (!isShowOnboardingRef.current) {
        isShowOnboardingRef.current = true;
      }
    }
  }, []);

  const isShowPromotionInit = useMemo(
    () => sessionStorage.getItem("isShowPromotionOnHome"),
    []
  );

  // On page loaded User OnBoarding
  useEffect(() => {
    const isHomePage = checkHomePage(location.pathname, currentPage.id);
    const initDialogOnboarding = async () => {
      try {
        const { onPromotion, onAutoPromotion } = platform;

        if ((onPromotion || onAutoPromotion) && !isShowPromotionInit) {
          return;
        }

        // logic for TVOD dialog only
        if (tvodDialogSlot.current && !isGlobal) {
          await tvodDialogSlot.current.getSlot(location);
        }

        // #region logic for TVOD dialog and multiprofile onboarding
        // Closed for task https://vieon.atlassian.net/browse/VFE-11996
        // if (tvodDialogSlot.current) {
        //   const tvodDialog = await tvodDialogSlot.current.getSlot(location);
        //   if (!tvodDialog) {
        //     checkOnboardingMultiProfile();
        //   }
        // } else {
        //   checkOnboardingMultiProfile();
        // }
        // #endregion
      } catch (error) {
        // handle error
      }
    };

    if (!isShowOnboardingRef.current && isHomePage) initDialogOnboarding();
  }, []);

  // on page loaded
  useEffect(() => {
    (async () => {
      const isHomePage = checkHomePage(location.pathname, currentPage.id);

      const initDialog = async () => {
        try {
          if (!isRenderDialogAllowed.current) {
            return;
          }

          const { onPromotion, onAutoPromotion } = platform;
          const isShowPromotion = sessionStorage.getItem(
            "isShowPromotionOnHome"
          );

          // check redirect to promotion page or show dialog
          if ((onPromotion || onAutoPromotion) && !isShowPromotion) {
            sessionStorage.setItem("isShowPromotionOnHome", "1");
            setTimeout(() => {
              history.push({
                pathname: onPromotion
                  ? ROUTES.PROMOTION
                  : ROUTES.AUTO_PROMOTION,
              });
            }, 500);
          } else {
            const listDialog: Array<Content> = [];

            if (restrictedCountry) {
              listDialog.push({
                item: {},
                type: DialogType.global_country_limit,
              });
            }

            const mastheadAds = await mastheadAdsSlot.current.getSlot(location);
            if (mastheadAds && mastheadAds.id) {
              listDialog.push({
                item: mastheadAds,
                type: DialogType.masthead_ads,
              });
            } else if (welcomeAdsSlot.current) {
              const welcomeAd = await welcomeAdsSlot.current.getSlot(location);
              welcomeAd &&
                listDialog.push({ item: welcomeAd, type: DialogType.ads });
            }
            if (tvodDialogSlot.current && !isGlobal) {
              const tvodDialog = await tvodDialogSlot.current.getSlot(location);
              if (needShowOverlap) {
                listDialog.push({
                  item: overlapInfo,
                  type: DialogType.payment,
                });

                // only show reminder if needShowOverlap
                if (tvodDialog && tvodDialog.had_tvod) {
                  listDialog.push({
                    item: tvodDialog,
                    type: tvodDialog.config_type,
                  });
                }
              } else {
                tvodDialog &&
                  listDialog.push({
                    item: tvodDialog,
                    type: tvodDialog.config_type,
                  });
              }
            }
            const infoOfferForSegmentedUser =
              await getOfferForSegmentedUserDialog();
            if (infoOfferForSegmentedUser) {
              listDialog.push({
                item: infoOfferForSegmentedUser,
                type: DialogType.offerForSegmentedUser,
              });
            }

            setQueueDialog(listDialog);
          }
        } catch (error) {
          // handle error
        }
      };

      if (isHomePage) await initDialog();
    })();
  }, []);

  useEffect(() => {
    // change dialog
    setDialog(queueDialog[0]);
    if (queueDialog.length >= 1) {
      setIsDialogShowing(true);
    } else {
      setIsDialogShowing(false);
    }
  }, [queueDialog]);

  useEffect(() => {
    if (needShowOverlap && isRenderDialogAllowed.current) {
      // push into queue
      setQueueDialog((queue) => [
        ...queue,
        { item: overlapInfo, type: DialogType.payment },
      ]);
    }
  }, [needShowOverlap]);

  useEffect(() => {
    const isHomePage = checkHomePage(location.pathname, currentPage.id);

    async function pushDialog() {
      try {
        const listDialog: Array<Content> = [];

        if (restrictedCountry) {
          listDialog.push({
            item: {},
            type: DialogType.global_country_limit,
          });
        }

        const mastheadAds = await mastheadAdsSlot.current.getSlot(location);
        if (mastheadAds && mastheadAds.id) {
          listDialog.push({
            item: mastheadAds,
            type: DialogType.masthead_ads,
          });
        } else if (welcomeAdsSlot.current) {
          const welcomeAd = await welcomeAdsSlot.current.getSlot(location);
          welcomeAd &&
            listDialog.push({ item: welcomeAd, type: DialogType.ads });
        }
        if (tvodDialogSlot.current && !isGlobal) {
          const tvodDialog = await tvodDialogSlot.current.getSlot(location);
          tvodDialog &&
            listDialog.push({
              item: tvodDialog,
              type: tvodDialog.config_type,
            });
        }
        const infoOfferForSegmentedUser =
          await getOfferForSegmentedUserDialog();
        if (infoOfferForSegmentedUser) {
          listDialog.push({
            item: infoOfferForSegmentedUser,
            type: DialogType.offerForSegmentedUser,
          });
        }
        if (listDialog.length && isRenderDialogAllowed.current) {
          // push into queue
          setQueueDialog(listDialog);
        }
      } catch (error) {
        // handle error
      }
    }

    if (isHomePage && !isDialogShowingRef.current) {
      if (waitCallbackFuncRef.current) {
        pushDialog();
        waitCallbackFuncRef.current = false;
      }
    }
  }, [currentPage]);

  const removeDialog = (type: DialogType) => {
    const arr: Array<Content> = [...queueDialogRef.current];

    const currentIndex = arr.findIndex((item) => item.type === type);

    if (currentIndex > -1 && isRenderDialogAllowed.current) {
      // remove dialog from queue
      arr.splice(currentIndex, 1);
      setQueueDialog(arr);
    }
  };

  const addDialog = (dialog: Content, unshift = false) => {
    if (!isRenderDialogAllowed.current) {
      return;
    }
    if (unshift) {
      setQueueDialog((queue) => [dialog, ...queue]);
    } else {
      setQueueDialog((queue) => [...queue, dialog]);
    }
  };

  const handleCountTVOD = (dialog: Content) => {
    if (tvodDialogSlot.current) tvodDialogSlot.current.countShow(dialog.item);
  };

  const handleSaveListReminder = (
    list: string[],
    comingSoon: boolean = false
  ) => {
    if (!tvodDialogSlot.current) return;
    if (comingSoon) {
      tvodDialogSlot.current.saveListComingSoon(list);
    } else {
      tvodDialogSlot.current.saveListReminder(list);
    }
  };

  const handleShowAds = useCallback(
    async (location: LocationState) => {
      if (isRenderDialogAllowed.current) {
        if (restrictedCountry) {
          setQueueDialog([
            {
              item: {},
              type: DialogType.global_country_limit,
            },
          ]);
        }

        const mastheadAds = await mastheadAdsSlot.current.getSlot(location);
        if (mastheadAds && mastheadAds.id) {
          // push into queue
          setQueueDialog([
            {
              item: mastheadAds,
              type: DialogType.masthead_ads,
            },
          ]);
        } else {
          const welcomeAd = await welcomeAdsSlot.current.getSlot(location);
          if (welcomeAd) {
            // push into queue
            setQueueDialog([{ item: welcomeAd, type: DialogType.ads }]);
          }
        }
      }
    },
    [setQueueDialog]
  );

  const handleShowDialogs = useCallback(
    async (location: LocationState) => {
      try {
        const isHomePage = checkHomePage(
          get(location, "pathname", ""),
          currentPage.id
        );

        if (!isHomePage) {
          waitCallbackFuncRef.current = true;
          return;
        }

        if (!isRenderDialogAllowed.current) {
          return;
        }

        const { onPromotion, onAutoPromotion } = platform;
        const isShowPromotion = sessionStorage.getItem("isShowPromotionOnHome");

        // check redirect to promotion page or show dialog
        if (
          (onPromotion || onAutoPromotion) &&
          !isShowPromotion &&
          !currentProfileRef.current?.isKid
        ) {
          sessionStorage.setItem("isShowPromotionOnHome", "1");
          setTimeout(() => {
            history.push({
              pathname: onPromotion ? ROUTES.PROMOTION : ROUTES.AUTO_PROMOTION,
            });
          }, 500);
        } else {
          const listDialog: Array<Content> = [];

          if (restrictedCountry) {
            listDialog.push({
              item: {},
              type: DialogType.global_country_limit,
            });
          }

          const mastheadAds = await mastheadAdsSlot.current.getSlot(location);

          if (mastheadAds && mastheadAds.id) {
            const fromLogout =
              (location ?? ({} as any))?.state?.from === ROUTES.SETTING_LOGOUT;
            const isFromLobby = (location ?? ({} as any))?.state?.isFromLobby;

            const wasActionSignIn = (location ?? ({} as any))?.state
              ?.wasActionSignIn;

            let isPushAds = true;

            if (fromLogout || (isFromLobby && wasActionSignIn)) {
              isPushAds = false;
            }
            if (isPushAds) {
              listDialog.push({
                item: mastheadAds,
                type: DialogType.masthead_ads,
              });
            }
          } else if (welcomeAdsSlot.current) {
            const welcomeAd = await welcomeAdsSlot.current.getSlot(location);
            welcomeAd &&
              listDialog.push({ item: welcomeAd, type: DialogType.ads });
          }
          if (tvodDialogSlot.current && !isGlobal) {
            const tvodDialog = await tvodDialogSlot.current.getSlot(location);
            tvodDialog &&
              listDialog.push({
                item: tvodDialog,
                type: tvodDialog.config_type,
              });
          }
          const infoOfferForSegmentedUser =
            await getOfferForSegmentedUserDialog();
          if (infoOfferForSegmentedUser) {
            listDialog.push({
              item: infoOfferForSegmentedUser,
              type: DialogType.offerForSegmentedUser,
            });
          }
          if (listDialog.length) {
            // push into queue
            setQueueDialog(listDialog);
          }
        }
      } catch (error) {
        // handle error
      }
    },
    [setQueueDialog, getOfferForSegmentedUserDialog]
  );

  const clearAllDialogs = useCallback(() => {
    setQueueDialog([]);
    isRenderDialogAllowed.current = false;
  }, [setQueueDialog]);

  const allowRenderDialogs = useCallback(() => {
    isRenderDialogAllowed.current = true;
  }, []);

  return {
    isDialogShowing,
    setIsDialogShowing,
    dialog,
    addDialog,
    removeDialog,
    handleCountTVOD,
    handleSaveListReminder,
    handleShowAds,
    handleShowDialogs,
    clearAllDialogs,
    allowRenderDialogs,
  };
};

export default useWelcomeDialog;
