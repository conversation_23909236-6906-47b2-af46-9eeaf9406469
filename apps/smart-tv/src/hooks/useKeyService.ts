import React, { useCallback, useEffect, useRef } from "react";
import set from "lodash/set";
import keyService from "services/keyService";
import onPopout, { Direct } from "factory/onPopout";
import useStateRef from "./useStateRef";

interface Options {
  keyHandler: KeyHandler;
  defaultFocusIndex?: number;
  priority?: number;
}

export default function useKeyService(
  options: Options
): [
  number,
  React.Dispatch<React.SetStateAction<number>>,
  React.MutableRefObject<number>,
  (C: string, H: KeyHandler) => void,
  (C: string) => void,
  { [key: string]: KeyHandler },
  (C: Direct) => PopoutHandler
] {
  const { keyHandler, defaultFocusIndex = 0, priority = 2 } = options;
  const [focusIndex, setFocusIndex, focusIndexRef] =
    useStateRef<number>(defaultFocusIndex);
  const keyHandlerFn = useRef<{ [key: string]: KeyHandler }>({});

  useEffect(() => {
    keyService.getInstance().pushHandle(priority, keyHandler);
    return () => {
      keyService.getInstance().popHandle(priority, keyHandler);
    };
  }, []);

  const registerKeyHandler = useCallback(
    (type: string, keyHandler: KeyHandler) => {
      set(keyHandlerFn, ["current", type], keyHandler);
    },
    []
  );
  const unregisterKeyHandler = useCallback((type: string) => {
    delete keyHandlerFn.current[type];
  }, []);
  const onPopoutFactory = onPopout.bind(null, setFocusIndex);
  return [
    focusIndex,
    setFocusIndex,
    focusIndexRef,
    registerKeyHandler,
    unregisterKeyHandler,
    keyHandlerFn.current,
    onPopoutFactory,
  ];
}
