import { useEffect, useRef, useContext } from "react";
import { MenuItem, SubMenu } from "types/endpoint";
import { ContextStateApp } from "../context/StateAppContext";

export default function useCurrentPageRef() {
  const contextStateApp = useContext(ContextStateApp);
  const { currentPage } = contextStateApp || {};
  const currentPageRef = useRef<MenuItem | SubMenu>();
  if (currentPage) {
    currentPageRef.current = currentPage;
  }
  useEffect(() => {
    currentPageRef.current = currentPage;
  }, [currentPage]);
  return currentPageRef;
}
