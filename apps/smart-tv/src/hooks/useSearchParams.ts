import { useCallback, useMemo } from "react";
import { useHistory, useLocation } from "react-router-dom";
import { setUrlSearchKey } from "app/utils/Dom";

const useSearchParams = () => {
  const history = useHistory();
  const location = useLocation();

  const currentSearchParams = useMemo(() => new URLSearchParams(location.search), [location.search]);

  const getParam = useCallback(
    (key: string, defaultValue: string | null = null) => {
      return currentSearchParams.get(key) || defaultValue;
    },
    [currentSearchParams]
  );

  const batchReplaceSearchParams = useCallback(
    (pairs: Array<{ key: string; value: string }>) => {
      const newParams = new URLSearchParams(location.search);

      pairs.forEach((pair) => {
        setUrlSearchKey(newParams, pair.key, pair.value);
      });

      history.replace({
        ...location,
        search: newParams.toString()
      });
    },
    [history, location]
  );

  const replaceSearchParam = useCallback(
    (key: string, value: string) => {
      batchReplaceSearchParams([{ key, value }]);
    },
    [batchReplaceSearchParams]
  );

  return {
    currentSearchParams,
    getParam,
    batchReplaceSearchParams,
    replaceSearchParam
  };
};

export default useSearchParams;
