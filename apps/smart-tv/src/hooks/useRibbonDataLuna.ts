import { useState, useEffect, useReducer, useRef, useCallback } from "react";
import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import produce from "immer";
import get from "lodash/get";
import remove from "lodash/remove";
import concat from "lodash/concat";
import isArray from "lodash/isArray";
import dayjs from "dayjs";
import findLast from "lodash/findLast";
import pick from "lodash/pick";
import useStateRef from "hooks/useStateRef";
import * as api from "services/endpoint";
import AppError, { ErrorType } from "app/utils/AppError";
import { RootState } from "app/store/store";
import {
  PageRibbons,
  PageBanners,
  SubscriptionResponse,
  RegistrationRibbonList,
  Ribbon as IRibbon,
  WatchMore as IWatchMore,
  WatchMoreItem as IWatchMoreItem,
  WatchLater as IWatchLater,
  WatchLaterItem as IWatchLaterItem,
  Item as IRibbonItem,
  SubMenu,
} from "types/endpoint";
import { RibbonType, ICategory, VideoType } from "types/page";
import GAManager from "app/utils/GAManager";
import { makeid } from "app/utils";
import { MAX_RIBBON } from "app/utils/constants";
import platform from "services/platform";
import SegmentManager from "app/utils/SegmentManager";
import TriggerConfig, { TriggerKey } from "app/utils/TriggerConfig";
import OutStreamsAdsConfig, {
  OutStreamsAdsKey,
} from "app/utils/OutStreamsAdsConfig";
import KeepAlive, { KeepAliveData } from "app/utils/KeepAlive";
import { generatePathForCategoryItem } from "app/utils/Route";
import { makeUuidv4 } from "app/utils/formatNumber";
import {
  getCampaigns,
  getQuestionByCampaign,
  getOptionByCampaign,
} from "services/endpoint";
import { unixUTCToLocal, isSameOrBefore } from "app/utils/formatTime";
import useIsGlobal from "./useIsGlobal";
import useConfigAdsMasterBanner from "./useConfigAdsMasterBanner";

// return LAST enable campaign || first campaign
const _getLastEnableCampaign = async () => {
  try {
    const res = await getCampaigns();
    if (res.code === 0) {
      const { results } = res;
      if (isArray(results)) {
        return findLast(results, (i) => i.status_enable) || get(results, [0]);
      }
      // Handle false response data
    }
    // Handle res.code ERROR
  } catch (error) {
    // Handle request ERROR
  }
};

// return LAST enable question || undefined
const _getLastEnableQuestion = async (campaignId: string) => {
  try {
    const res = await getQuestionByCampaign(campaignId);
    if (res.code === 0) {
      const { results } = res;
      if (isArray(results)) {
        return findLast(results, (i) => i.status_enable) || get(results, [0]);
      }
      // Handle false response data
    }
    // Handle res.code ERROR
  } catch (error) {
    // Handle request ERROR
  }
};

const _getOptionsByCampaign = async (campaignId: string) => {
  try {
    const res = await getOptionByCampaign(campaignId);
    if (res.code === 0) {
      return res.results || [];
    }
  } catch (error) {
    // error
  }
};

// Check if now <= started_at => campaign is coming soon
const _checkCampaignComingSoon = (campaign: { started_at: number }) => {
  const startedAt = get(campaign, "started_at", 0);
  const startedAtDayjs = unixUTCToLocal(startedAt);
  return isSameOrBefore(dayjs(), startedAtDayjs);
};
export const getRibbonRapViet = async (option?: { namePrefix?: string }) => {
  const campaign = await _getLastEnableCampaign();
  const namePrefix = get(option, "namePrefix", "");
  if (campaign && campaign.id) {
    const isCampaignComingSoon = _checkCampaignComingSoon(campaign);
    if (isCampaignComingSoon) {
      return {
        ...pick(campaign, ["started_at", "ended_at", "content_award"]),
        name: remove([namePrefix, get(campaign, "name", "")], (i) => i).join(
          " - "
        ),
        id: `rap-viet-${get(campaign, "id", 0)}`,
        display_type: 0,
        items: [],
        type: RibbonType.RapViet,
      };
    }
    const question = await _getLastEnableQuestion(campaign.id);
    if (question && question.id) {
      switch (question.display_type) {
        case 1: // Option list
        case 2: // On voting
        case 3: // Rank list
          const options = await _getOptionsByCampaign(campaign.id);
          return {
            ...pick(campaign, [
              "started_at",
              "ended_at",
              "id",
              "content_award",
            ]),
            name: remove(
              [
                namePrefix,
                get(campaign, "name", ""),
                "Các thí sinh vào vòng trong",
              ],
              (i) => i
            ).join(" - "),
            display_type: question.display_type,
            items: options,
            type: RibbonType.RapViet,
          };
        default:
          break;
      }
    }
  }
};

type Ribbon = { id: string; type: RibbonType; items: any[] };

const makeRibbon = (ribbon: IRibbon) => {
  const total = Math.min(get(ribbon, "metadata.total", 0), MAX_RIBBON);
  if (total && total > 0) {
    const itemsLen = get(ribbon, "items.length", 0);
    if (itemsLen && itemsLen > 0 && itemsLen < total) {
      // const tailArray: IRibbonItem[] = new Array(total - itemsLen).fill(
      //   null as unknown as IRibbonItem
      // );
      return {
        ...ribbon,
        // items: [...ribbon.items, ...tailArray],
      };
    }
  }
  return ribbon;
};
const makeTvodRibbon = (ribbon: IRibbon) => {
  const total = Math.min(get(ribbon, "metadata.total", 0), MAX_RIBBON);
  if (total && total > 0) {
    const itemsLen = get(ribbon, "items.length", 0);
    if (itemsLen && itemsLen > 0 && itemsLen < total) {
      // const tailArray = Array<IRibbonItem>(total - itemsLen).fill(
      //   null as unknown as IRibbonItem
      // );
      return {
        ...ribbon,
        // items: concat(ribbon.items, tailArray),
        name: ribbon.name || "Nội dung đang thuê",
        id: ribbon.id || "renting-content",
        type: ribbon.type || RibbonType.Tvod,
      };
    }
  }
  return {
    ...ribbon,
    name: ribbon.name || "Nội dung đang thuê",
    id: ribbon.id || "renting-content",
    type: ribbon.type || RibbonType.Tvod,
  };
};
const makeWatchMore = (watchMore: IWatchMore) => {
  const total = Math.min(get(watchMore, "metadata.total", 0), MAX_RIBBON);
  if (total && total > 0) {
    const itemsLen = get(watchMore, "items.length", 0);
    if (itemsLen && itemsLen > 0 && itemsLen < total) {
      // const tailArray = Array<IWatchMoreItem>(total - itemsLen).fill(
      //   null as unknown as IWatchMoreItem
      // );
      return {
        id: "watch-more",
        type: RibbonType.WatchMore,
        name: "ĐANG XEM",
        // items: concat(watchMore.items, tailArray),
        items: watchMore.items,
        metadata: watchMore.metadata,
      };
    }
  }
  return {
    id: "watch-more",
    type: RibbonType.WatchMore,
    name: "ĐANG XEM",
    items: get(watchMore, "items", []) || [],
    metadata: watchMore.metadata,
  };
};
const makeWatchLater = (watchLater: IWatchLater) => {
  const total = Math.min(get(watchLater, "metadata.total", 0), MAX_RIBBON);
  if (total && total > 0) {
    const itemsLen = get(watchLater, "items.length", 0);
    if (itemsLen && itemsLen > 0 && itemsLen < total) {
      // const tailArray = Array<IWatchLaterItem>(total - itemsLen).fill(
      //   null as unknown as IWatchLaterItem
      // );
      return {
        id: "watch-later",
        type: RibbonType.WatchLater,
        name: "DANH SÁCH CỦA TÔI",
        // items: concat(watchLater.items, tailArray),
        items: watchLater.items,
        metadata: watchLater.metadata,
      };
    }
  }
  return {
    id: "watch-later",
    type: RibbonType.WatchLater,
    name: "DANH SÁCH CỦA TÔI",
    items: get(watchLater, "items", []) || [],
    metadata: watchLater.metadata,
  };
};

const makeCategory = (subMenus: any[], slug: string): ICategory => {
  const items: {
    id: string;
    slug: string;
    name: string;
    thumbs: string[];
    type: VideoType;
    titleCard: string;
  }[] = [];
  const subItems: {
    id: string;
    slug: string;
    name: string;
    thumbs: string[];
    type: VideoType;
    titleCard: string;
  }[] = [];
  if (subMenus && subMenus.length > 0) {
    subMenus.forEach((subMenu: any) => {
      const id = get(subMenu, "id", "");
      if (id) {
        const path = generatePathForCategoryItem(subMenu, slug, "subpage");
        const item = {
          id,
          slug: path,
          avatar: get(subMenu, "avatar", ""),
          name: get(subMenu, "name", ""),
          thumbs: get(subMenu, "thumbs", []) || [],
          type: VideoType.CATEGORY,
          titleCard: get(subMenu, "img_title_card_dark", ""),
        };
        items.push(item);
        const subRibbons = get(subMenu, "sub_menu_ribbon");
        if (subRibbons && subRibbons.length > 0) {
          subRibbons.forEach((ribbon: any) => {
            const id = get(ribbon, "id", "");
            const path = generatePathForCategoryItem(ribbon, slug, "ribbon");
            if (id) {
              const subRibbonItem = {
                id,
                slug: path,
                name: get(ribbon, "name", ""),
                avatar: get(ribbon, "avatar", ""),
                thumbs: get(ribbon, "thumbs", []) || [],
                type: VideoType.RIBBON,
                titleCard: get(ribbon, "img_title_card_dark", ""),
              };
              subItems.push(subRibbonItem);
            }
          });
        }
      }
    });
  }
  return {
    id: "category",
    type: RibbonType.Category,
    name: "THỂ LOẠI",
    items: concat(items, subItems),
  };
};
const makeRibbonMasterBanner = (items: any, pageId: string) => {
  return {
    id: `ribbon master banner|${pageId}`,
    type: RibbonType.NewMasterBanner,
    auto_reload: true,
    items,
  };
};
const makeFunctionalRibbon = (ribbon: any) => {
  const items = get(ribbon, "items", []);
  if (items && items.length > 0) {
    ribbon.items = items.map((i: any) => {
      return {
        ...i,
        id: i.id || makeid(8),
      };
    });
  }
  return ribbon;
};
const makePaymentRibbon = (res: SubscriptionResponse, ribbons: any[]) => {
  const subscription = get(res, ["result", "subscriptions", "0"], {});
  const errorCode = get(res, ["error_code"]);
  const cloneRibbons: Ribbon[] = ribbons.map((item: any) => item);
  if (errorCode === 0 && Object.keys(subscription).length) {
    const paymentRibbonItem = {
      id: subscription.id,
      subscription,
      network: get(res, ["result", "network"], ""),
      current_time: get(res, ["result", "current_time"], ""),
    };
    const paymentRibbon = {
      id: "payment-ribbon",
      name: "Payment Banner",
      type: 95,
      items: [paymentRibbonItem],
    };
    let indexOfWatchMoreRibbon: number | null = null;
    let indexOfPosterRibbon: number | null = null;
    cloneRibbons.forEach((ribbon: any, index: number) => {
      if (ribbon.type === RibbonType.WatchMore) {
        indexOfWatchMoreRibbon = index;
      }
      if (!indexOfPosterRibbon && ribbon.type === RibbonType.Poster) {
        indexOfPosterRibbon = index;
      }
    });
    // start handle tracking payment conversion
    const status = get(subscription, ["status"], null);
    const userType = get(subscription, ["package_name"], "");
    SegmentManager.segmentAction(
      `${
        status === 0 ? "expired_banner_loaded" : "nearly_expire_banner_loaded"
      }`,
      {
        user_type: userType,
      }
    );
    // end handle tracking payment conversion

    const isWatchMoreRibbonEmpty = get(
      cloneRibbons,
      ["indexOfWatchMoreRibbon", "items", "length"],
      0
    );
    if (indexOfWatchMoreRibbon !== null && !isWatchMoreRibbonEmpty) {
      cloneRibbons.splice(indexOfWatchMoreRibbon + 1, 0, paymentRibbon);
    } else {
      const firstRibbonIsBillboard =
        get(cloneRibbons, ["0", "type"], "") === RibbonType.Billboard;
      if (firstRibbonIsBillboard) {
        cloneRibbons.splice(2, 0, paymentRibbon);
      } else {
        cloneRibbons.splice(
          (indexOfPosterRibbon || 0) + 1 || 1,
          0,
          paymentRibbon
        );
      }
    }
    return cloneRibbons;
  }
  return ribbons;
};
const makeRegistrationRibbon = (bannerData: any, ribbons: any[]) => {
  let indexOfSuggestedRibbonList: number | null = null;
  ribbons.forEach((ribbon, index) => {
    if (ribbon.id === bannerData.id_check) {
      indexOfSuggestedRibbonList = index;
    }
  });
  if (indexOfSuggestedRibbonList !== null) {
    const cloneRibbons: any[] = [...ribbons];
    const registrationRibbonList: RegistrationRibbonList = {
      id: "registration-ribbon",
      name: "Registration Banner",
      type: RibbonType.RegistrationBanner,
      items: [
        {
          id: bannerData.id_check,
          image: bannerData.image,
          title: bannerData.title,
          button: bannerData.button,
        },
      ],
    };
    cloneRibbons.splice(indexOfSuggestedRibbonList, 0, registrationRibbonList);
    return cloneRibbons;
  }
  return ribbons;
};

const makeOutStreamAdsRibbon = async (ribbon: any, slotInfo: any) => {
  const { screenType } = platform;
  const ribbonId = ribbon.id;

  const slotSize = get(slotInfo, `size.${screenType}`, null);
  const divId = get(slotInfo, "id_div", `div-gpt-ad-${makeUuidv4()}`);

  let slotItems = get(slotInfo, `ribbon.${ribbonId}.items`, []);

  if (slotItems && slotItems.length) {
    slotItems = slotItems.map((item: any): any => {
      const uuid = makeUuidv4();
      return {
        ...item,
        size: get(item, "size", slotSize),
        id_div: get(item, "id_div", `div-gpt-ad-${uuid}`),
        id: uuid,
        adsId: slotInfo.id || item?.id,
      };
    });
  }

  if (slotSize) {
    const slotWithSize = {
      ...ribbon,
      id: ribbonId,
      image_default: get(slotInfo, `ribbon.${ribbonId}.image_default`, ""),
      items: slotItems,
      size: slotSize,
      id_div: divId,
    };
    return slotWithSize;
  }
  return ribbon;
};

type State = {
  pageTitle: string;
  ribbons: Ribbon[];
  isLoading: boolean;
  error: number;
};

type Action =
  | { type: "request" }
  | { type: "success"; ribbons: Ribbon[]; pageTitle: string }
  | { type: "failure"; error: number }
  | {
      type: "update";
      payload: {
        id: string | number;
        items: any[];
      };
    };
type Reducer<S, A> = (prevState: S, action: A) => S;
const initialState: State = {
  pageTitle: "",
  ribbons: [],
  isLoading: true,
  error: 0,
};
const reducer: Reducer<State, Action> = (state, action) => {
  switch (action.type) {
    case "request":
      return {
        ...state,
        isLoading: true,
        error: 0,
      };
    case "success":
      return {
        ...state,
        isLoading: false,
        error: 0,
        ribbons: [...action.ribbons],
        pageTitle: action.pageTitle,
      };
    case "failure":
      return {
        ...state,
        isLoading: false,
        error: action.error || ErrorType.Unexpected,
      };
    case "update":
      return produce(state, (draft) => {
        const ribbons = get(draft, "ribbons", []) as any[];
        const id = get(action, "payload.id", "");
        const items = get(action, "payload.items", []);
        if (ribbons && isArray(ribbons) && id) {
          const newRibbons = ribbons.map((r: any) => {
            if (r.id === id) {
              return {
                ...r,
                items: items.map((i: any, index: number) => {
                  if (i) {
                    return i;
                  }
                  return r.items[index];
                }),
              };
            }
            return r;
          });
          draft.ribbons = newRibbons;
        }
      });
    default:
      return state;
  }
};

const useRibbonDataLuna = (
  isVideoPage = true
): [any[], boolean, number, string, (S: boolean) => void, KeepAliveData] => {
  const configAdsMasterBanner = useConfigAdsMasterBanner();
  const menus = useSelector((state: RootState) => state.app.menus);
  const isAuthen = useSelector((state: RootState) => state.app.isAuthen);
  const profile = useSelector((state: RootState) => state.app.profile);
  const currentProfile = useSelector(
    (state: RootState) => state.app.currentProfile
  );

  const [slotBannerInfo, setSlotBannerInfo] = useStateRef<any>(null);
  const isFetchSlotBannerInfoRef = useRef<boolean>(false);

  const keepAliveDataRef = useRef({
    path: window.location.hash,
    focus: {},
    extra: {},
  } as KeepAliveData);

  // const userType = useSelector((state: RootState) => state.app.userType);
  // const needPackageId = useSelector(
  //   (state: RootState) => state.app.packageGroupId
  // );
  const { id, slug } = useParams<{
    id: string;
    slug: string;
  }>();

  const [{ ribbons, isLoading, error, pageTitle }, dispatch] = useReducer(
    reducer,
    initialState
  );
  const [isInit, setIsInit] = useState<boolean>(false);
  const isGlobal = useIsGlobal();

  const fetchData = useCallback(async () => {
    try {
      dispatch({ type: "request" });

      let pageId: string = "";
      let pageTitle: string = "";
      let pageSlug: string = "";

      let category: SubMenu[] = [];

      if (isVideoPage) {
        const menu = menus.find((m: any) => m.id === id);
        // Homepage is hard code Index === 1 (second item in array)
        // const isHomePage = menuIndex === 1;

        if (!menu) {
          throw new AppError(ErrorType.GetMenuError);
        }
        const [subMenu, ...categoryList] = get(menu, "sub_menu");
        if (!subMenu) {
          throw new AppError(ErrorType.SubMenuEmpty);
        }
        category = categoryList;
        pageId = get(subMenu, "id", "");
        pageTitle = get(subMenu, "name", "");
        pageSlug = get(menu, "slug", "");
      } else {
        const menu = menus.find((m: any) => m.slug === slug);
        if (!menu) {
          throw new AppError(ErrorType.GetMenuError);
        }
        let subMenu: any = get(menu, "sub_menu", []).find((sm) => sm.id === id);
        if (!subMenu) {
          subMenu = get(menu, "sub_menu", []).find((sm) => {
            const menuIdRedirect = get(sm, ["menu_id_redirect"], "");
            return menuIdRedirect !== "" && menuIdRedirect === id;
          });
        }
        if (!subMenu) {
          throw new AppError(ErrorType.SubMenuEmpty);
        }
        pageId = id;
        pageTitle = get(subMenu, "name", "");
      }

      const pageRibbons: PageRibbons = await api
        .getPageRibbons(pageId)
        .catch(() => []);
      const pageBanners: PageBanners = await api
        .getPageBanners(pageId, 11)
        .catch(() => []);
      if (configAdsMasterBanner.id) {
        pageBanners.splice(configAdsMasterBanner.index, 0, {
          id: configAdsMasterBanner.id_div,
          type: VideoType.ADS,
          images: {
            thumbnail_v4: configAdsMasterBanner.thumbnail,
          },
        });
        if (pageBanners.length > 11) {
          pageBanners.pop();
        }
      }

      let ribbons: any[] = pageRibbons.map((m, index) => {
        return { ...m, auto_reload: index === 0 };
      });

      const ribbonAutoReloadIds: string[] = [];

      if (ribbons.length > 0) {
        ribbonAutoReloadIds.push(ribbons[0].id);
      }

      const getRibbonApiParam = (
        id: string = "",
        initItemRibbon: number = 0
      ) => {
        const params: any[] = [id, initItemRibbon, 0];
        if (
          ribbonAutoReloadIds.length > 0 &&
          ribbonAutoReloadIds.includes(id)
        ) {
          params.push(false);
        }
        return params;
      };

      ribbons.splice(0, 0, makeRibbonMasterBanner(pageBanners, pageId));
      ribbons.splice(1, 0, makeCategory(category, pageSlug));

      const { limitItemRibbon } = platform;
      // set keep alive
      const keepAliveDt = KeepAlive.getData(window.location.hash);
      if (keepAliveDt) keepAliveDataRef.current = keepAliveDt;

      const lastFocusRibbonId = get(keepAliveDt, "extra.ribbonId", "");
      const beId = get(keepAliveDt, "extra.beId", "");
      const cachedPages = get(keepAliveDt, "extra.cachedPages", []);
      const crashRibbonPaging = get(
        keepAliveDt,
        "extra.crashRibbonPaging",
        false
      );

      // init ribbon may empty items
      const ribbonPromises = ribbons.map((ribbon) => {
        if (
          ribbon.type &&
          (ribbon.type === RibbonType.Billboard ||
            ribbon.type === RibbonType.Category ||
            ribbon.type === RibbonType.NewMasterBanner)
        )
          return ribbon;

        if (ribbon.type === RibbonType.RapViet) {
          return getRibbonRapViet({
            namePrefix: ribbon.name,
          }).catch(() => ({}));
        }

        if (ribbon.type === RibbonType.OutstreamAds) {
          // Do not show outstream ads on vip profile
          if (profile?.isPremium) return {};
          return makeOutStreamAdsRibbon(ribbon, slotBannerInfo);
        }

        // check ribbon has any item and ribbon in current focus list
        if (ribbon.is_items === 0 || ribbon.id === lastFocusRibbonId) {
          let initItemRibbon = limitItemRibbon;
          if (ribbon.id === lastFocusRibbonId) {
            const focusOrder = get(keepAliveDt, "focus.x", 0) + 1;
            initItemRibbon =
              Math.ceil(focusOrder / limitItemRibbon) * limitItemRibbon;
          }
          if (
            beId === ribbon.id &&
            crashRibbonPaging &&
            cachedPages.length > 0
          ) {
            initItemRibbon =
              cachedPages[cachedPages.length - 1] * limitItemRibbon;
          }

          if (ribbon.type === RibbonType.WatchMore) {
            return api
              .getWatchMore(0, initItemRibbon)
              .then(makeWatchMore)
              .then((data) => {
                return {
                  ...data,
                  beId: ribbon.id,
                };
              })
              .catch(() => {});
          }
          if (ribbon.type === RibbonType.WatchLater) {
            if (isAuthen) {
              return api
                .getWatchLater(0, initItemRibbon)
                .then(makeWatchLater)
                .then((data) => {
                  return {
                    ...data,
                    alreadyInitData: true,
                    beId: ribbon.id,
                  };
                })
                .catch(() => {});
            }
            return {};
          }
          if (ribbon.type === RibbonType.FunctionalRibbon) {
            return api.getRibbon // .getRibbon(ribbon.id, initItemRibbon)
              .apply(null, getRibbonApiParam(ribbon.id, initItemRibbon) as any)
              .then(makeFunctionalRibbon)
              .then((data) => ({ ...data, alreadyInitData: true }))
              .catch(() => ({}));
          }

          if (ribbon.type === RibbonType.Tvod) {
            if (isAuthen) {
              return api
                .getRentingContents({ limit: initItemRibbon })
                .then((ribbonRes) => {
                  return makeTvodRibbon({ ...ribbon, ...ribbonRes });
                })
                .then((data) => ({ ...data, alreadyInitData: true }))
                .catch(() => ({}));
            }
            return {};
          }

          return api.getRibbon // .getRibbon(ribbon.id, initItemRibbon)
            .apply(null, getRibbonApiParam(ribbon.id, initItemRibbon) as any)
            .then(makeRibbon)
            .then((data) => ({ ...data, alreadyInitData: true }))
            .catch(() => ({}));
        }

        if (ribbon.type === RibbonType.WatchMore) {
          return {
            id: "watch-more",
            type: RibbonType.WatchMore,
            name: "ĐANG XEM",
            items: Array<IWatchMoreItem>(1).fill(
              null as unknown as IWatchMoreItem
            ),
          };
        }
        if (ribbon.type === RibbonType.WatchLater) {
          if (isAuthen) {
            return {
              id: "watch-later",
              type: RibbonType.WatchLater,
              name: "DANH SÁCH CỦA TÔI",
              items: Array<IWatchLaterItem>(1).fill(
                null as unknown as IWatchLaterItem
              ),
            };
          }
          return {};
        }

        if (ribbon.type === RibbonType.FunctionalRibbon) {
          return {
            ...ribbon,
            items: Array<IRibbonItem>(1).fill(null as unknown as IRibbonItem),
          };
        }

        return {
          ...ribbon,
          items: Array<IRibbonItem>(1).fill(null as unknown as IRibbonItem),
        };
      });

      ribbons = await Promise.all(ribbonPromises);

      // remove empty ribbon
      remove(ribbons, (ribbon) => {
        if (!ribbon) return true;
        const type = get(ribbon, "type");
        if (type === RibbonType.InAppBanner) {
          return false;
        }
        if (type === RibbonType.Billboard) {
          return true;
        }
        if (type === RibbonType.RibbonPromote) {
          return true;
        }
        if (type === RibbonType.RapViet) {
          return false;
        }
        const { length } = get(ribbon, "items", []) || [];
        return length === 0;
      });

      // Payment banner & Registration banner
      let cloneRibbons = ribbons;

      if (isVideoPage) {
        if (pageSlug === "trang-chu-new-ui") {
          try {
            if (isAuthen) {
              // Payment banner
              // Check profile KID -> not call api getPaymentBanner
              const isCurrentProfileKid = !!(
                currentProfile && currentProfile.isKid
              );
              if (!isCurrentProfileKid) {
                const promisePaymentRibbon = await api.getPaymentBanner();
                const res: SubscriptionResponse =
                  (await promisePaymentRibbon) as SubscriptionResponse;
                cloneRibbons = makePaymentRibbon(res, ribbons);
              }
            } else if (!isGlobal) {
              // Registration banner
              const registrationRibbonData = TriggerConfig.get(
                TriggerKey.AnonymousUserRegistrationConversionTrigger
              );
              cloneRibbons = makeRegistrationRibbon(
                registrationRibbonData,
                ribbons
              );
            }
          } catch (err) {
            // handle errors here
          }
        }
        /* Tracking Start */
        GAManager.gaPageView(`/${pageSlug}`);
        /* Tracking End */
      }

      dispatch({ type: "success", ribbons: cloneRibbons, pageTitle });
    } catch (error: any) {
      if (error.name === "App Error") {
        dispatch({ type: "failure", error: error.code });
      } else {
        dispatch({ type: "failure", error: ErrorType.Unexpected });
      }
    }
  }, [
    id,
    isAuthen,
    isVideoPage,
    menus,
    profile?.isPremium,
    slug,
    slotBannerInfo,
    configAdsMasterBanner,
  ]);

  useEffect(() => {
    (async () => {
      const slotBannerInfo = await OutStreamsAdsConfig.fetchAndGetConfig(
        OutStreamsAdsKey.Banner
      );
      isFetchSlotBannerInfoRef.current = true;
      setSlotBannerInfo(slotBannerInfo);
    })();
  }, []);

  useEffect(() => {
    if (
      !isInit &&
      isFetchSlotBannerInfoRef.current &&
      configAdsMasterBanner.isFetching
    ) {
      fetchData();
      setIsInit(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInit, fetchData, configAdsMasterBanner.isFetching]);

  return [
    ribbons,
    isLoading,
    error,
    pageTitle,
    setIsInit,
    keepAliveDataRef.current,
  ];
};

export default useRibbonDataLuna;
