import React, {
  ReactNode,
  createContext,
  useState,
  useEffect,
  useContext,
  useRef,
} from "react";
import { useHistory, useLocation } from "react-router-dom";

interface ContextTrackLocationRefType {
  previous: string;
  current: string;
  message: string;
}

const ContextTrackLocationState = createContext<any>(undefined);

ContextTrackLocationState.displayName = "ContextTrackLocationState";

const ContextTrackLocationAction = createContext<
  React.Dispatch<any> | undefined
>(undefined);

ContextTrackLocationAction.displayName = "ContextTrackLocationAction";

const ContextTrackLocationRef = createContext<
  React.MutableRefObject<ContextTrackLocationRefType> | undefined
>(undefined);

ContextTrackLocationRef.displayName = "ContextTrackLocationRef";

const calculateLocation = (location: any) => {
  if (
    location &&
    !location?.pathname?.includes("login") &&
    !location?.pathname?.includes("signup") &&
    !location?.pathname?.includes("auth")
  ) {
    return location;
  }
  return "";
};

export const ContextTrackLocationProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const location = useLocation();
  // const history = useHistory();
  const [trackedLocation, setTrackedLocation] = useState<any>(() =>
    calculateLocation(location)
  );
  const trackRouteRef = useRef<ContextTrackLocationRefType>({
    previous: location.pathname,
    current: location.pathname,
    message: "",
  });

  useEffect(() => {
    const calculatedLocation = calculateLocation(location);
    // TODO: Test tracking history changes
    // console.log(location.pathname, history.action, location.state);
    if (calculatedLocation) {
      setTrackedLocation(calculatedLocation);
    }
    trackRouteRef.current = {
      previous: trackRouteRef.current.current,
      current: location.pathname,
      message: "",
    };
  }, [location]);

  return (
    <ContextTrackLocationAction.Provider value={setTrackedLocation}>
      <ContextTrackLocationRef.Provider value={trackRouteRef}>
        <ContextTrackLocationState.Provider value={trackedLocation}>
          {children}
        </ContextTrackLocationState.Provider>
      </ContextTrackLocationRef.Provider>
    </ContextTrackLocationAction.Provider>
  );
};

export const useContextTrackLocationAction = () => {
  const contextTrackLocationAction = useContext(ContextTrackLocationAction);
  if (contextTrackLocationAction !== undefined) {
    return contextTrackLocationAction;
  }
  throw new Error("ContextTrackLocationAction is undefined");
};

export const useContextTrackLocationState = () => {
  const contextTrackLocationState = useContext(ContextTrackLocationState);
  if (contextTrackLocationState !== undefined) {
    return contextTrackLocationState;
  }
  throw new Error("ContextTrackLocationState is undefined");
};

export const useContextTrackLocationRef = () => {
  const contextTrackLocationRef = useContext(ContextTrackLocationRef);
  if (contextTrackLocationRef !== undefined) {
    return contextTrackLocationRef;
  }
  throw new Error("ContextTrackLocationRef is undefined");
};
