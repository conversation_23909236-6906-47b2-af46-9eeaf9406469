import { LocationState } from "history";
import { createContext } from "react";
import { Config as TVODConfig } from "app/components/TVODDialog/data";
import { Config as AdsConfig } from "app/components/Ads/Welcome/data";
import { OfferSegmentedUserResponse } from "types/endpoint/OfferSegmentedUser";

export enum DialogType {
  "global_country_limit",
  "ads",
  "masthead_ads",
  "tvod_onboarding",
  "tvod_reminder",
  "payment",
  "check_live",
  "offerForSegmentedUser",
}

interface GlobalCountryLimit {
  item: any;
  type: DialogType.global_country_limit;
}
interface TVODContent {
  item: TVODConfig;
  type: DialogType.tvod_onboarding | DialogType.tvod_reminder;
}

interface AdsContent {
  item: AdsConfig;
  type: DialogType.ads;
}

interface MastheadAdsContent {
  item: { inventoryId: number | undefined; countdown: number };
  type: DialogType.masthead_ads;
}

export interface OfferForSegmentedUserContent {
  item: OfferSegmentedUserResponse;
  type: DialogType.offerForSegmentedUser;
}

interface PaymentConventionContent {
  item: any;
  type: DialogType.payment;
}

export interface CheckLiveContent {
  checkLiveType:
    | "end"
    | "expire"
    | "pre_order"
    | "order"
    | "pre_order_base_price";
  item: any;
  type: DialogType.check_live;
}

export type Content =
  | GlobalCountryLimit
  | TVODContent
  | AdsContent
  | MastheadAdsContent
  | PaymentConventionContent
  | CheckLiveContent
  | OfferForSegmentedUserContent;

interface DialogContextInterface {
  isDialogShowing: boolean;
  setIsDialogShowing: (_I: boolean) => void;
  dialog: Content | null;
  addDialog: ({ item, type }: Content, unshift?: boolean) => void;
  removeDialog: (type: DialogType) => void;
  handleCountTVOD: (dialog: Content) => void;
  handleSaveListReminder: (list: string[], comingSoon?: boolean) => void;
  handleShowAds: (location: LocationState) => void;
  handleShowDialogs: (location: LocationState) => void;
  clearAllDialogs: () => void;
  allowRenderDialogs: VoidFunction;
}
const WelcomeDialogContext = createContext<DialogContextInterface>({
  isDialogShowing: false,
  setIsDialogShowing: () => {},
  dialog: null,
  addDialog: () => {},
  removeDialog: () => {},
  handleCountTVOD: () => {},
  handleSaveListReminder: () => {},
  handleShowAds: () => {},
  handleShowDialogs: () => {},
  clearAllDialogs: () => {},
  allowRenderDialogs: () => {},
});
export default WelcomeDialogContext;
