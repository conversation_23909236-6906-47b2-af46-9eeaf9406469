import React, { createContext, useContext } from "react";

interface ContextMultiProfileType {
  isProfileSelectedInLobby: boolean;
  setIsProfileSelectedInLobby: React.Dispatch<React.SetStateAction<boolean>>;
  isProfileSelectedInLobbyRef: React.MutableRefObject<boolean>;
}

export const ContextMultiProfile = createContext<
  ContextMultiProfileType | undefined
>(undefined);

ContextMultiProfile.displayName = "ContextMultiProfile";

export const useContextMultiProfileProvider = () => {
  const contextMultiProfile = useContext(ContextMultiProfile);
  if (contextMultiProfile) {
    return contextMultiProfile;
  }
  throw new Error("context multi profile is undefined");
};
