import React, {
  createContext,
  useContext,
  useRef,
  useState,
  useEffect,
  useCallback,
} from "react";

interface RibbonVisibleContextType {
  register: (
    id: string,
    ref: React.RefObject<HTMLDivElement>,
    ribbonData?: any
  ) => void;
  unregister: (id: string) => void;
  visibleRibbonIds: string[];
  setViewportNode: (node: HTMLDivElement | null) => void;
  checkVisible?: () => void;
}

const RibbonVisibleContext = createContext<RibbonVisibleContextType>({
  register: () => {},
  unregister: () => {},
  visibleRibbonIds: [],
  setViewportNode: () => {},
  checkVisible: () => {},
});

export const useRibbonVisible = () => {
  const contextRibbonVisible = useContext(RibbonVisibleContext);
  return contextRibbonVisible;
};

export const RibbonVisibleProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const ribbons = useRef<Map<string, React.RefObject<HTMLDivElement>>>(
    new Map()
  );
  const [visibleRibbonIds, setVisibleRibbonIds] = useState<string[]>([]);
  const viewportRef = useRef<HTMLDivElement | null>(null);

  const setViewportNode = useCallback((node: HTMLDivElement | null) => {
    viewportRef.current = node;
  }, []);

  const register = useCallback(
    (id: string, ref: React.RefObject<HTMLDivElement>, ribbonData?: any) => {
      ribbons.current.set(id, ref);
    },
    []
  );
  const unregister = useCallback((id: string) => {
    ribbons.current.delete(id);
  }, []);

  const checkVisible = useCallback(() => {
    const parent = viewportRef.current;
    if (!parent) return;

    const parentRect = parent.getBoundingClientRect();
    const visibleTop = Math.max(parentRect.top, 0);
    const visibleBottom = Math.min(parentRect.bottom, window.innerHeight);

    const visible: string[] = [];
    ribbons.current.forEach((ref, id) => {
      const el = ref.current;
      if (!el) return;
      const rect = el.getBoundingClientRect();

      const overlapTop = Math.max(rect.top, visibleTop);
      const overlapBottom = Math.min(rect.bottom, visibleBottom);
      const visibleHeight = Math.max(0, overlapBottom - overlapTop);
      const percentVisible = visibleHeight / rect.height;

      if (percentVisible >= 0.9) {
        visible.push(id);
      }
    });
    setVisibleRibbonIds(visible);
  }, []);

  useEffect(() => {
    if (!viewportRef.current) return;
    checkVisible();
  }, [checkVisible]);

  useEffect(() => {
    checkVisible();
  }, [ribbons.current.size, checkVisible]);

  const contextValue = React.useMemo(
    () => ({
      register,
      unregister,
      visibleRibbonIds,
      setViewportNode,
      checkVisible,
    }),
    [register, unregister, visibleRibbonIds, setViewportNode, checkVisible]
  );

  return (
    <RibbonVisibleContext.Provider value={contextValue}>
      {children}
    </RibbonVisibleContext.Provider>
  );
};
