/// @group setting
// @use 'settings/variables' as var;
@use "../../settings/function" as fn;

/// @group mixin
@use "../../mixin/grid" as grid;

.grid {
  display: flex;
  flex-flow: row wrap;
  box-sizing: border-box;
  .col {
    flex: 0 0 auto;
    min-width: 0;
    min-height: 0;
    width: 100%;
    box-sizing: border-box;
    ///
    &-1 {
      @include grid.grid($col: 1);
    }
    &-2 {
      @include grid.grid($col: 2);
    }
    &-3 {
      @include grid.grid($col: 3);
    }
    &-4 {
      @include grid.grid($col: 4);
    }
    &-5 {
      @include grid.grid($col: 5);
    }
    &-6 {
      @include grid.grid($col: 6);
    }
    &-7 {
      @include grid.grid($col: 7);
    }
    &-8 {
      @include grid.grid($col: 8);
    }
    &-9 {
      @include grid.grid($col: 9);
    }
    &-10 {
      @include grid.grid($col: 10);
    }
    &-11 {
      @include grid.grid($col: 11);
    }
    &-12 {
      @include grid.grid($col: 12);
    }
    &.auto {
      flex: 1 1 0px;
      width: auto;
    }
    &.shrink {
      flex: 0 0 auto;
      width: auto;
    }
  }

  &.align-middle {
    align-items: center;
  }
  &.justify-content {
    &-between {
      justify-content: space-between;
    }

    &-center {
      justify-content: center;
    }

    &-end {
      justify-content: flex-end;
    }

    &-start {
      justify-content: flex-start;
    }
  }

  &.grid-x {
    // This will update style module user agent
    // --gt-x: fn.percent-unit(24);
    // margin-left: calc(var(--gt-x)/-1);
    // margin-right: calc(var(--gt-x)/-1);
    margin-left: fn.percent-unit(-12);
    margin-right: fn.percent-unit(-12);
    .col {
      padding-left: fn.percent-unit(12);
      padding-right: fn.percent-unit(12);
    }
  }
  &.grid-margin {
    &-x {
      margin-left: fn.percent-unit(-12);
      margin-right: fn.percent-unit(-12);
      & > .col {
        margin-left: fn.percent-unit(12);
        margin-right: fn.percent-unit(12);

        &-1 {
          @include grid.grid($col: 1, $margin-x: 12);
        }
        &-2 {
          @include grid.grid($col: 2, $margin-x: 12);
        }
        &-3 {
          @include grid.grid($col: 3, $margin-x: 12);
        }
        &-4 {
          @include grid.grid($col: 4, $margin-x: 12);
        }
        &-5 {
          @include grid.grid($col: 5, $margin-x: 12);
        }
        &-6 {
          @include grid.grid($col: 6, $margin-x: 12);
        }
        &-7 {
          @include grid.grid($col: 7, $margin-x: 12);
        }
        &-8 {
          @include grid.grid($col: 8, $margin-x: 12);
        }
        &-9 {
          @include grid.grid($col: 9, $margin-x: 12);
        }
        &-10 {
          @include grid.grid($col: 10, $margin-x: 12);
        }
        &-11 {
          @include grid.grid($col: 11, $margin-x: 12);
        }
        &-12 {
          @include grid.grid($col: 12, $margin-x: 12);
        }
      }
      &-8 {
        margin-left: fn.percent-unit(-8);
        margin-right: fn.percent-unit(-8);

        & > .col {
          margin-left: fn.percent-unit(8);
          margin-right: fn.percent-unit(8);

          &-1 {
            @include grid.grid($col: 1, $margin-x: 8);
          }
          &-2 {
            @include grid.grid($col: 2, $margin-x: 8);
          }
          &-3 {
            @include grid.grid($col: 3, $margin-x: 8);
          }
          &-4 {
            @include grid.grid($col: 4, $margin-x: 8);
          }
          &-5 {
            @include grid.grid($col: 5, $margin-x: 8);
          }
          &-6 {
            @include grid.grid($col: 6, $margin-x: 8);
          }
          &-7 {
            @include grid.grid($col: 7, $margin-x: 8);
          }
          &-8 {
            @include grid.grid($col: 8, $margin-x: 8);
          }
          &-9 {
            @include grid.grid($col: 9, $margin-x: 8);
          }
          &-10 {
            @include grid.grid($col: 10, $margin-x: 8);
          }
          &-11 {
            @include grid.grid($col: 11, $margin-x: 8);
          }
          &-12 {
            @include grid.grid($col: 12, $margin-x: 8);
          }
        }
      }
    }
    &-x-6 {
      margin-left: fn.percent-unit(-6);
      margin-right: fn.percent-unit(-6);
      & > .col {
        margin-left: fn.percent-unit(6);
        margin-right: fn.percent-unit(6);

        &-1 {
          @include grid.grid($col: 1, $margin-x: 6);
        }
        &-2 {
          @include grid.grid($col: 2, $margin-x: 6);
        }
        &-3 {
          @include grid.grid($col: 3, $margin-x: 6);
        }
        &-4 {
          @include grid.grid($col: 4, $margin-x: 6);
        }
        &-5 {
          @include grid.grid($col: 5, $margin-x: 6);
        }
        &-6 {
          @include grid.grid($col: 6, $margin-x: 6);
        }
        &-7 {
          @include grid.grid($col: 7, $margin-x: 6);
        }
        &-8 {
          @include grid.grid($col: 8, $margin-x: 6);
        }
        &-9 {
          @include grid.grid($col: 9, $margin-x: 6);
        }
        &-10 {
          @include grid.grid($col: 10, $margin-x: 6);
        }
        &-11 {
          @include grid.grid($col: 11, $margin-x: 6);
        }
        &-12 {
          @include grid.grid($col: 12, $margin-x: 6);
        }
      }
    }
    &-y {
      & > .col {
        margin-bottom: fn.percent-unit(6);
        margin-top: fn.percent-unit(6);
      }
    }
  }

  ///
  &-5 {
    & > .col {
      @include grid.grid($grid-type: block, $col: 5, $margin-x: 6);
    }
  }
}
