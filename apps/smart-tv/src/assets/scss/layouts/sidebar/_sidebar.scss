////
/// @group sidebar
////

/// @group settings
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/layer" as layer;
@use "mixin/position" as position;
@use "mixin/pseudo" as pseudo;

/// @group components
///

/// @group logo
@use "components/logo/logo" as logo;
/// @group icon
@use "components/icons/icon" as icons;

@use "settings/variables" as variables;

%sidebar-expanded {
  .nav--main {
    .nav {
      &__item {
        padding: 0 fn.percent-unit(15);
        flex-flow: nowrap;
        padding: 0 fn.percent-unit(15);
        pointer-events: all;
        &.focus {
          &::before {
            opacity: 1;
            left: -24px;
          }
          // &::after {
          //   opacity: 0;
          // }
          .nav__icon .vie,
          .nav-text,
          .nav__text-expanded {
            color: palettes.$black;
          }
        }
        &.active {
          &::after {
            left: fn.percent-unit(16);
          }
        }
      }

      &__text {
        opacity: 0;
        display: none;

        &-expanded {
          margin-right: fn.percent-unit(24);
          visibility: visible;
          transform: translateX(0);
          max-width: fit-content;
          display: flex;
          flex-direction: column;
          .hint {
            font-size: fn.percent-unit(16);
            font-weight: 400;
            line-height: fn.percent-unit(150%);
            display: block !important;
          }
        }
      }
      &__icon .vie {
        transition: color 0.2s;
      }
    }
  }

  .nav--user {
    visibility: visible;
    transform: translateX(0);
    width: auto;
    .nav {
      &__item {
        padding: 0 fn.percent-unit(15);
        pointer-events: all;
        &.focus {
          .nav__text {
            color: palettes.$black;
          }
        }
      }
    }
  }

  .sidebar-wrap {
    max-width: fn.percent-unit(var.$sidebar-max-width-expanded);
  }

  .nav__text-expanded {
    width: 100%;
    display: block !important;
    max-width: none !important;
  }

  .sidebar__overlay {
    width: 100vw;
    opacity: 1;
  }
  .sidebar__control {
    visibility: hidden;
  }
}

/// mixin sidebar
///
/// @param {Expanded} $expanded - This check is "expanded" apply style of class "expanded".
/// @param {Position} $position - This check is "position" apply style of "position".
/// @param {Box-type} $position - This check is "box-type" apply style of $box-type.

@mixin sidebar(
  $expanded: var.$expanded,
  $position: var.$sidebar-position,
  $box-type: var.$sidebar-box
) {
  @include layer.layers(layer-9);
  * {
    box-sizing: border-box;
  }
  .sidebar__control {
    width: fn.percent-unit(var.$sidebar-max-width - 24px);
    @include position.absolute(top 0 bottom 0 left 0);
    background-color: palettes.$gray-33;
    transition: opacity 0.15s;
    opacity: 0;
    z-index: 1;
  }
  .sidebar__overlay {
    width: var.$sidebar-max-width;
    height: 100vh;
    @include position.absolute(top 0 right 0 bottom 0 left 0);
    @include layer.layers(layer-min);
    opacity: 1;
    z-index: -1;
    background: linear-gradient(90deg, #000000 0%, rgba(0, 0, 0, 0) 100%);
    transition: opacity 0.32s;
  }
  .sidebar-wrap {
    @include position.relative;
    padding-top: fn.percent-unit(140px);
    padding-bottom: fn.percent-unit(24px);
    padding-left: fn.percent-unit(24);
    padding-right: fn.percent-unit(24);
    width: 100%;
    max-width: fn.percent-unit(144);
    height: 100%;
    display: flex;
    flex-direction: column;
    .nav--main,
    .nav--user {
      margin-bottom: 0;
    }
    .nav__item {
      pointer-events: none;
    }

    .nav__item-left {
      min-width: 0;
    }

    .nav__text {
      width: 100%;
    }

    .nav__icon {
      @include icons.icon($size: "basic");
      position: relative;
      z-index: 1;
      .vie {
        transition: none;
      }
      img {
        position: absolute;
        bottom: 0;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .nav__icon--vtv {
        width: fn.percent-unit(38);
        max-height: fn.percent-unit(38);
      }

      &.runningman {
        img {
          width: fn.percent-unit(69) !important;
        }
      }

      &.rapviet {
        img {
          width: fn.percent-unit(81) !important;
        }
      }

      &.hbo {
        img {
          width: fn.percent-unit(32) !important;
        }
      }
      &.kplus {
        img {
          width: fn.percent-unit(32) !important;
        }
      }
      &.nala {
        img {
          width: fn.percent-unit(40) !important;
        }
      }
      &.profile-avatar {
        .nav__icon {
          border-radius: fn.percent-unit(50%);
          margin: 0 auto;
          border: fn.percent-unit(1) solid #ffffff;
          width: fn.percent-unit(32) !important;
        }
        .kid-icon {
          position: absolute;
          top: 0;
          left: auto;
          right: 0;
          width: fn.percent-unit(10) !important;
          height: fn.percent-unit(10) !important;
          transform: none;
          z-index: 1;
        }
      }
    }
    .icon__dot {
      display: block;
      height: fn.percent-unit(10px);
      width: fn.percent-unit(10px);
      border-radius: 50%;
      position: absolute;
      top: fn.percent-unit(10px);
      right: fn.percent-unit(10px);
      background-color: transparent;
      z-index: 1;
    }
  }

  //When add expanded this set sidebar expand
  @if $expanded == "expanded" {
    &.expanded {
      @extend %sidebar-expanded;
    }
  }

  //This position fixed
  @if $position == "fixed" {
    @include position.fixed(top 0 bottom 0 left 0);
    height: 100vh;
  }

  @if $box-type == "flexible" {
    display: flex;
    flex-direction: column;
  }
}

.sidebar {
  @include sidebar($expanded: expanded, $position: fixed);
  &:not(.expanded) .sidebar__control:hover {
    opacity: 1;
  }
}
