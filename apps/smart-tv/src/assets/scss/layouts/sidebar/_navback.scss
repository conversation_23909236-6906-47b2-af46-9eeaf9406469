/// @group settings
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/layer" as layer;
@use "mixin/position" as position;
@use "mixin/pseudo" as pseudo;

@use "components/icons/icon" as icon;

.back-sidebar {
  @include position.absolute(top 0 left 0);
  z-index: 2;
  width: fn.percent-unit(var.$sidebar-max-width);
  height: 100vh;
  &::before {
    @include pseudo.pseudo($width: 100%, $height: 100%, $display: block);
    @include position.absolute(top 0 right 0 bottom 0 left 0);
    @include layer.layers(layer-min);
    opacity: 1;
    //background: linear-gradient(90deg, #000000 0%, rgba(0, 0, 0, 0) 100%);
    transition: width 0.25s;
    will-change: width;
  }
  &__wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
  }
  &__icon {
    @include icon.icon;
    height: fn.percent-unit(88px);
    width: fn.percent-unit(88px);
    .vie {
      height: fn.percent-unit(84px);
      width: fn.percent-unit(84px);
    }
  }
  &.focus {
    .back-sidebar__wrap {
      background: rgba(155, 155, 155, 0.5);
    }
    .back-sidebar__icon .vie {
      color: #080e17;
    }
  }
}
