///
///@group setting
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

///
/// @group mixin
@use "mixin/box" as box;
@use "mixin/layer" as layer;
@use "mixin/position" as position;

///
///@group containers
@use "containers/videoIntro/intro" as intro;

///
///@group components
@use "components/progress/progress" as progress;

.main--user {
  @at-root .main#{&} {
    // safe area
    $spacing: fn.percent-unit(46) fn.percent-unit(144+20);

    &-auth-menu {
      padding-right: fn.percent-unit(25);
    }

    padding: $spacing;
    &.non-bg {
      background: none;
    }
    .main__header {
      padding-top: 0;
      // margin-bottom: fn.percent-unit(24);
      &--auth {
        padding-left: 0 !important;
      }

      &--title {
        color: palettes.$white;
        font-size: fn.percent-unit(36);
        font-weight: 500;
      }
    }

    .main__body {
      $mainBody: calc(100vh - 166px);
      height: $mainBody;
      &__login {
        height: 90vh;
      }
    }

    &-login {
      height: 100vh;
      overflow: hidden;
      .logo--top {
        left: auto;
        right: fn.percent-unit(40px);
      }
      .main--user-login__background {
        @include layer.layers(layer-negative);
        @include position.absolute(top 0 left 0);
        @include box.box(100vw, 100vh);
      }
    }

    &-profile {
      padding: 0;
      .main__header {
        padding-top: fn.percent-unit(40);
      }
      .main__body {
        min-height: calc(100vh - 6.66667vw);
        padding-left: 7.5vw;
        .card-groups {
          flex-flow: wrap;
          .card {
            padding-right: 2em;
            padding-bottom: 2em;
            margin-bottom: 0;
            flex: 0 0 50%;
          }
        }
      }
    }
  }
}
