///
///@group setting
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

///
/// @group mixin
@use "mixin/position" as position;
@use "mixin/pseudo" as pseudo;

///
///@group containers
@use "containers/videoIntro/intro" as intro;

///
///@group components
@use "components/progress/progress" as progress;

.main--intro {
  @at-root .main#{&} {
    $spacing: fn.percent-unit(24);
    @include intro.intro;

    &::before {
      @include pseudo.pseudo($width: 100%, $height: 100%, $display: block);
      @include position.absolute(top 0 right 0 bottom 0 left 0);
      background-image: linear-gradient(
        90deg,
        rgba(palettes.$gray-11, 1) 0%,
        rgba(palettes.$gray-11, 0) 100%
      );
    }

    .nav--audio {
      .nav__item {
        // width: 100%;
        width: auto;
      }
    }

    &.hide {
      display: none;
    }

    .card-group {
      width: fn.percent-unit(59.125%);
    }

    .tag-remaining-time {
      color: palettes.$warning;
      display: flex;
      align-items: center;
      font-size: fn.percent-unit(28px);
      font-weight: 700;
      line-height: 1.2;
      margin-top: fn.percent-unit(16px);

      .icon {
        margin-right: fn.percent-unit(12px);

        .vie {
          color: palettes.$warning;
          width: fn.percent-unit(32px);
          height: fn.percent-unit(32px);
        }
      }
    }

    // .card--intro{
    //   height: fn.percent-unit(237.55px);
    //   .card__thumbnail{
    //     width: fn.percent-unit(422.31px);
    //     position: relative;
    //     height: 0;
    //     .card__img {
    //       img {
    //         width: 100%;
    //       }
    //     }
    //   }
    //   .card__section{
    //     width: calc(58.375% - 1.25vw);
    //     padding-left: $spacing;
    //     .card__desc{
    //       display: -webkit-box;
    //       -webkit-line-clamp: 3;
    //       -webkit-box-orient: vertical;
    //       overflow: hidden;
    //     }
    //   }

    //   .progress{
    //     @include progress.progress;
    //     @include position.absolute(top auto right 3% bottom 3% left 3%);
    //     width: 94%;
    //   }
    // }
  }
}
