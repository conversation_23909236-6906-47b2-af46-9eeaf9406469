/// @param palettes
@use "settings/palettes" as palettes;

/// @group variables
@use "settings/variables" as var;

/// @group function
@use "settings/function" as fn;

///
/// @group mixin
@use "mixin/position" as position;
@use "mixin/pseudo" as pseudo;

.content__control {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: fn.percent-unit(var.$sidebar-max-width);
}

.main {
  // display: flex;
  // justify-content: center;
  // align-items: center;
  // flex-direction: column;
  z-index: 1;
  position: relative;

  // background-color: palettes.$gray-11;
  &__wrapper {
    position: relative;
    margin-top: fn.percent-unit(4);
  }
  &__header {
    padding: fn.percent-unit(16) 0;
  }

  .category-title {
    position: absolute;
    top: fn.percent-unit(69px);
    left: fn.percent-unit(149px);
    margin-left: fn.percent-unit(24px);

    font-style: normal;
    font-weight: 500;
    font-size: fn.percent-unit(36px);
    line-height: 140%;
    letter-spacing: 0.297px;
    color: palettes.$white;

    display: flex;
    justify-content: center;
    align-items: center;

    &::after {
      @include pseudo.pseudo(
        $width: fn.percent-unit(4px),
        $height: fn.percent-unit(32px),
        $display: flex
      );
      @include position.absolute(left fn.percent-unit(-24px));
      background-color: palettes.$green-3a;
    }
  }
}
