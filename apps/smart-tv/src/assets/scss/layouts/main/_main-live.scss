/// @param settings
@use "settings/palettes" as palettes;
@use "settings/variables" as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/position" as position;

/// @group main-live style
.main--live {
  @at-root .main#{&} {
    overflow: hidden;
    height: 100vh;
    background: #111;
    position: relative;
    &.collapse-masthead-ads {
      margin-top: fn.percent-unit(200);
    }

    &::after {
      display: block;
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 1;
      content: "";
      height: fn.percent-unit(120px);
      width: 100%;
      background: linear-gradient(0deg, #111111 0%, rgba(17, 17, 17, 0) 100%);
    }
    .main__header {
      padding-top: fn.percent-unit(40);
      padding-bottom: fn.percent-unit(40);
      position: relative;
      z-index: 2;
      &::before {
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        content: "";
        height: 100%;
        width: 100%;
        background: #111;
      }
    }
    .main__body {
      min-height: calc(100vh - 6.66667vw);
      z-index: 1;
      position: relative;
      padding-left: fn.percent-unit(
        var.$logo-max-width + 76 - var.$sidebar-max-width
      );
      margin-top: fn.percent-unit(-1px);
      .ribbon.ribbon--livetv-highlight {
        padding-top: fn.percent-unit(12px);
        z-index: 3;
        margin-bottom: fn.percent-unit(60);
        // background: #111;
        opacity: 0.4;
        &.visible {
          opacity: 1;
        }
        &::before {
          opacity: 1;
          display: block;
          position: absolute;
          top: 0;
          left: fn.percent-unit(-1px);
          content: "";
          height: fn.percent-unit(355px);
          width: 102%;
          background: #111;
        }
      }
      .nav.nav--tabs {
        margin-left: fn.percent-unit(var.$sidebar-max-width);
        padding-top: fn.percent-unit(4);
        z-index: 2;
        position: relative;
        transform: translateY(fn.percent-unit(-1));

        .nav__body {
          background: #111111;
          padding-top: fn.percent-unit(1);
        }
        &::after {
          display: block;
          content: "";
          height: fn.percent-unit(50px);
          margin-top: fn.percent-unit(-2px);
          background: linear-gradient(
            180deg,
            #111111 0%,
            rgba(17, 17, 17, 0) 100%
          );
        }
        &::before {
          display: block;
          position: absolute;
          top: 0;
          left: fn.percent-unit(-1px);
          content: "";
          height: fn.percent-unit(72px);
          width: 102%;
          background: #111;
        }
      }
      .grid__wrapper {
        margin-left: fn.percent-unit(var.$sidebar-max-width + 1px + 6px);
        margin-right: fn.percent-unit(8px);
        z-index: 1;
        position: relative;
        opacity: 0.4;
        &.visible {
          opacity: 1;
        }
      }
    }

    .title-meta--live {
      @include position.absolute(top 0 right 0 bottom 0 left 0);
      padding-left: fn.percent-unit(var.$sidebar-max-width);
    }
  }
  .ribbon-group {
    @include position.absolute(top fn.percent-unit(750));
  }
}

.main--livestream {
  height: 100vh;
}
