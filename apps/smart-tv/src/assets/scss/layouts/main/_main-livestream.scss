/// @param settings
@use "../../settings/palettes" as palettes;
@use "../../settings/variables" as var;
@use "../../settings/function" as fn;

/// @group mixin
@use "../../mixin/position" as position;

/// @group main-live style
.main--livestream {
  @at-root .main#{&} {
    overflow: hidden;
    height: 100vh;

    .main__header {
      padding-top: fn.percent-unit(40);
    }
    .main__body {
      min-height: calc(100vh - 6.66667vw);
    }

    .title-meta--livestream {
      @include position.absolute(top 0 right 0 bottom 0 left 0);
      padding-left: fn.percent-unit(var.$sidebar-max-width);
    }
  }
  .ribbon-group {
    @include position.absolute(top fn.percent-unit(750));
  }
}
