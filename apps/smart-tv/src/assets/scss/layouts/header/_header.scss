///
/// HEADER
/// 1.SETTING
/// 2.MIXIN
/// 3.STYLE
///

/// 1. @group setting
@use "settings/palettes" as palettes;
@use "settings/variables" as var;
@use "settings/function" as fn;

/// 2. @group mixin

/// 3. @group header
.header {
  @at-root header#{&} {
    overflow: hidden;
  }

  &--payment {
    @at-root header#{&} {
      background-color: fn.el-color($color: v-white, $shade: base);
      z-index: 2;
      position: relative;
    }
  }
}
