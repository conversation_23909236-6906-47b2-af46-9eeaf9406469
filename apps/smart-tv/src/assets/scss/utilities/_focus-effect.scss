/**
* ============ VERSION ============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  =============== FOCUS EFFECT ===============
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* SETTING
* MIXIN
* VARIABLE
* STYLES
*
*/

/// @group setting
@use "../settings/function" as fn;
@use "../settings/palettes" as palettes;

///
/// @group mixin
///
@use "../mixin/box" as box;
@use "../mixin/position" as position;
@use "../mixin/pseudo" as pseudo;

///
/// @group variable
///

///
/// @group .focus-effect style
///

/// Find the greatest common divisor of two integers.
///
/// @param {String} $effect - Updating...
/// @param {String} $across - Updating...
/// @param {String} $along - Updating...
/// @returns {Number} The greatest common divisor.

///
/// #STYLE
///
.focus-effect {
  @include position.relative;

  &:before {
    @include position.absolute(top 0 left 0);
    @include pseudo.pseudo($display: block);
    border: fn.percent-unit(6) solid palettes.$yellow-da;
    opacity: 0;
    transition: opacity 0.32s, transform 0.32s, border-color 0.32s;
    will-change: transform, opacity, height, width;
    z-index: 9;
    pointer-events: none;
  }

  &:not(.across) {
    &:before {
      @include box.box(
        $width: calc(100% - #{fn.percent-unit(16px)}),
        $height: 0
      );
      transform: translate(0%, -10%);
      transition: height 0.32s;
    }
  }

  &.across {
    &:before {
      @include box.box(
        $width: 0,
        $height: calc(100% - #{fn.percent-unit(16px)})
      );
      transform: translate(-10%, 0);
      transition: width 0.32s;
    }
  }

  &.round {
    &:before {
      border-radius: fn.percent-unit(8);
    }
  }

  &.focus {
    &:before {
      border-color: palettes.$white;
      opacity: 1;
    }
    &:not(.across) {
      &:before {
        height: calc(100% - #{fn.percent-unit(16px)});
        transform: translateY(0);
      }
    }
    &.across {
      &:before {
        width: calc(100% - #{fn.percent-unit(16px)});
        transform: translateX(0);
      }
    }
  }
}
