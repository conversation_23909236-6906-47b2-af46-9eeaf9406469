@use "../settings/function" as fn;

.scroll-element-wp {
  // display: flex;
  position: relative;
  .scrollbar {
    border-radius: fn.percent-unit(999);
    width: fn.percent-unit(24);
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    .track {
      width: fn.percent-unit(8);
      height: 100%;
      background-color: #333;
      position: absolute;
      right: 0;
      top: 0;
      left: 0;
      margin: 0 auto;
    }
    .thumb {
      position: absolute;
      background-color: rgba(100, 100, 100, 1);
      width: fn.percent-unit(8);
      left: 0;
      right: 0;
      margin: 0 auto;
      border-radius: fn.percent-unit(99);
      top: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      &.focus {
        background-color: #ffffff;
        width: fn.percent-unit(24);
        .icon {
          width: 100%;
          height: fn.percent-unit(16);
          &.up {
            // background: url(/src/assets/images/icon/ic_up.svg) no-repeat center;
            background: url(../../images/icon/ic_up.svg) no-repeat center;
          }

          &.down {
            bottom: 0;
            // background: url(/src/assets/images/icon/ic_down.svg) no-repeat
            //   center;
            background: url(../../images/icon/ic_down.svg) no-repeat center;
          }
        }
      }
    }
  }

  &.focus {
    .scrollbar {
      background-color: rgba(51, 51, 51, 1);
    }
  }
}
