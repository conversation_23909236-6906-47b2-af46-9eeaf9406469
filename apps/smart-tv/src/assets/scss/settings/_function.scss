// VieON for Smart TV (LG & Samsung)
// Version 1.0.0
// https://vieon.vn
@use "sass:math";
@use "sass:meta";
@use "sass:list";
@use "sass:map";

/// @use variables
@use "../settings/palettes" as palettes;
@use "../settings/variables" as var;

////
/// @group functions
////

/// @group aspect-ratio
/// Return the percent unit of $pixels
///
/// Transform pixels to vw, other length units (ex: em, rem) will return original, others return error
/// @param {Number} $pixels - Used pixels between "[number]px" or "[number]".

@function aspect-ratio($a, $b) {
  @return math.div($b, $a) * 100%;
}

/// @grid

@function grid($a, $b) {
  @if meta.type-of($a) ==number or meta.type-of($b) ==number {
    @return math.div($b, $a) * 100%;
  } @else {
    @error "Grid is: `#{$a}`, `#{$b}` not number, Please check again!";
  }
}

/// @group percent-unit
/// Return the percent unit of $pixels
///
/// Transform pixels to vw, other length units (ex: em, rem) will return original, others return error
/// @param {Number} $pixels - Used pixels between "[number]px" or "[number]".
/// @param {Number} $percent - Default value.
/// @return {Number} after converting, the result is a number in vw

@function percent-unit($pixels, $percent: var.$screen-default) {
  $unit: math.unit($pixels);

  @if ($unit== "") {
    @return math.div($pixels * 100, $percent) * 1vw;
  }

  @if ($unit== "px") {
    $pixels: strip-unit($pixels);
    @return math.div($pixels * 100, $percent) * 1vw;
  }

  @if (
    $unit== "em" or $unit== "rem" or $unit== "vw" or $unit== "vh" or $unit== "%"
  ) {
    @return $pixels;
  }

  @error "Pixels: `#{$pixels}`, `#{$unit}`";
}

/// Return the value is valid CSS length or not
/// @param {String} $value - The value need to check
/// @return {Boolean} - true if $value is valid CSS length
@function is-valid-length($value) {
  @return (meta.type-of($value) == "number" and not math.is-unitless($value)) or
    (list.index(auto initial inherit 0, $value) !=false);
}

///
@function multiplication($a, $b) {
  @if meta.type-of($a) == "number" and meta.type-of($b) == "number" {
    @return $a * $b;
  } @else {
    @error ('Please check `#{$a}`, `#{$b}` not number');
  }
}

/// El color
@function el-color($color, $shade: "base", $map: palettes.$palettes) {
  //Check color exits
  @if (map.has-key($map: $map, $key: $color)) {
    // $value: map-get($map, $color);
    $value: map.get(
      $map: $map,
      $key: $color,
    );

    // check if color or map
    @if meta.type-of($value: $value) ==color {
      // return color
      @return $value;
    }

    // check shade of color exits
    @if (map.has-key($map: $value, $key: $shade)) {
      // return shade of color
      @return map.get($map: $value, $key: $shade);
    }
  }

  // else do nothing
  @return null;
}

/// Remove the unit of a length
/// @param {Number} $number - Number to remove unit from
/// @return {Number} - Unitless number
@function strip-unit($number) {
  @if meta.type-of($number) == "number" and not math.is-unitless($number) {
    @return math.div($number, $number * 0 + 1);
  }

  @return $number;
}

/// @group z-index
///
@function z-index($name) {
  //
  $z: ();
  //
  $z: map.get(
    $map: var.$layers,
    $key: $name,
  );

  //Check $z
  @if $z {
    @return $z;
  } @else {
    @error ('Could not find a z-index for `#{$name}`');
  }
}

/// @function tags
@function tags-height($name) {
  //$value is map
  $val: ();

  // Get the value when passed an argument
  $val: map.get(
    $map: var.$tags-height,
    $key: $name,
  );

  //
  // Check value
  @if $val {
    @return $val;
  } @else {
    @error ('Could not find a tags height for `#{$name}`');
  }
}
