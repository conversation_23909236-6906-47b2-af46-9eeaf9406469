/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ================== VARIABLES ==================
/*--------------------------------------------*/
/**
*
* ============ CONTENTS ============
*
* GlOBAL
* BUTTON
* ICONS
* LOGO
* NAVIGATION
*
*/

/// @group Global
///
// @use 'function' as fn;

///
$expanded: null !default;
$flexible: null !default;
$grid-col-default: 12 !default;
$screen-default: 1920 !default;

/// @group icon
///
/// @param {Icon-Size} $icon-size - Set width basic size.
/// @param {Icon-Child-Size} $icon-child-size - Set height basic size.

$icon-size: (
  tiny: 12,
  small: 20,
  basic: 32,
  large: 56,
  big: 68,
  huge: 102,
) !default;

/// @group logo
///
/// @param {Logo-Max-Width} $logo-max-width - Set max-width default.
/// @param {Logo-Max-Expanded} $logo-max-width-expanded - Set max-width when sidebar expanded.
///
$logo-margin-bottom: 46 !default;
$logo-max-width: 94 !default;
$logo-max-width-expanded: auto !default;
$logo-max-height-expanded: 37 !default;

/// @group nav
///
/// @param {Direction} $nav-dir - Set the Direction for nav
/// @param {Spacing} $nav-spacing - Set spacing all for: top right bottom left.

$nav-dir: basic !default;
$nav-spacing: 24 !default;

/// @group sidebar
///
/// @param {Sidebar-Max-Width} $sidebar-max-width - Set maximum width limit for sidebar
/// @param {Sidebar-Max-Width-Expanded} $sidebar-max-width-expanded - Set maximum width
/// limit for sidebar when expanded.
/// @param {Sidebar-Position} $sidebar-position - The type of the sidebar display position:
///   @param position: null !default [not set position]
///   @param position: static [position: static] |
///                    absolute [position: relative] |
///                    fixed [position: fixed] |
///                    relative [position: relative] |
///                    sticky [position: sticky] |
///                    initial [position: initial] |
///                    inherit [position: inherit] |
///
/// @param {Sidebar-Box} $sidebar-box - Set flexible box display: flex; flex-direction: column;
///
$sidebar-max-width: 180px !default;
$sidebar-max-width-expanded: 390px !default;
$sidebar-position: null !default;
$sidebar-box: null !default;

$logo-max-width: 120px;

/// @group icon-size
///
/// The icon-size property specifies the stack order of an element.
///
/// @param {Map : Key}

$icon-size: (
  tiny: 24,
  small: 32,
  medium: 44,
  large: 62,
  big: 94,
) !default;

/// @group z-index
///
/// The z-index property specifies the stack order of an element.
///
/// @param {Map : Key}

$layers: (
  layer-negative: -1,
  layer-min: 0,
  layer-1: 1,
  layer-2: 2,
  layer-3: 3,
  layer-4: 4,
  layer-9: 9,
  layer-19: 19,
  layer-max: 9999,
) !default;

/// @group tags-size
///
/// The $tags-size property specifies the stack order of an element.
///
/// @param {Map : Key}

$tags-size: (
  tiny: 14,
  small: 18,
  basic: 24,
  large: 32,
  big: 44,
  huge: 60,
) !default;

/// @group tags-height
///
/// The $tags-height property specifies the height of tag element.
///
/// @param {Map : Key}

$tags-height: (
  billboard: 40,
  ribbon: 26,
) !default;

/// @group weight
///
/// The font weight property specifies the stack order of an element.
///
/// @param {Map : Key}

$weight: (
  thin: 100,
  light: 300,
  regular: 400,
  medium: 500,
  bold: 700,
  black: 900,
) !default;

/// @group themes
///
/// The themes specifies the stack order of an element.
///
/// @param {Map : Key}

$themes: base, dark, light;
$block-themes: p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11;
