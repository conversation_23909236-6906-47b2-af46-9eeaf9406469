////
/// @group spinner animate
////

////
/// @param properties
////
@use "properties" as prop;

/// @keyframes spin
////
@keyframes spin {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}

/// @mixin spinner
////
@mixin spinner(
  $count: prop.$countDefault,
  $duration: prop.$durationDefault,
  $function: prop.$functionDefault
) {
  @include prop.animation-name(spin);
  @include prop.count($count);
  @include prop.duration($duration);
  @include prop.function($function);
  // animation-name: spin;
  // animation-iteration-count: infinite;
  // animation-duration: 1s;
  // animation-timing-function: linear;
}

/// @Style animate-spin
/// Return animate-spin
///
/// Desc updating...
/// @param {Value} $count - Property value: number|infinite|initial|inherit.
/// @param {Value} $percent - Property value: time|initial|inherit.
/// @return {Value} $function - Property value: linear|ease|ease-in|ease-out|ease-in-out|step-start|
///                             step-end|steps(int,start|end)|cubic-bezier(n,n,n,n)|initial|inherit
////
.animate-spin {
  @include spinner($count: infinite, $duration: 1s, $function: linear);
}
