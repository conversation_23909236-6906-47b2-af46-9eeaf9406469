////
/// @group box sliding
////

////
///
@use "../settings/function" as fn;

////
/// @param properties
////
@use "properties" as prop;

/// @group slideOut
////

////
///@group keyframes
////

//
@include prop.keyframes(slideOutUp) {
  from {
    @include prop.transform(translateY(0));
  }
  to {
    visibility: hidden;
    @include prop.transform(translateY(-100vh));
  }
}

//
@mixin slideOut(
  $name: slideOutDown,
  $delay: fn.$delayDefault,
  $count: fn.$countDefault,
  $duration: fn.$durationDefault,
  $function: fn.$functionDefault,
  $fill: fn.$fillDefault,
  $visibility: fn.$visibilityDefault
) {
  @include prop.animation-name($name);
  @include prop.delay($delay);
  @include prop.count($count);
  @include prop.duration($duration);
  @include prop.function($function);
  @include prop.fill-mode($fill);
  @include prop.visibility($visibility);
}

//slideOUt Down
.animated-slide-out-down {
  @include slideOut;
}
