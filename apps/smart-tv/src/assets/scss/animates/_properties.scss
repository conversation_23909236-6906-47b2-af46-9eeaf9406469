// duration     Specifies how many seconds or milliseconds an animation takes to complete one cycle. Default 0
//
// delay        Specifies when the animation will start. Default 0
//
// function     Describes how the animation will progress over one cycle of its duration. Default "ease"
//
// count        The animation-iteration-count property specifies the number of times an animation should be played
//
// fill-mode    Specifies if the effects of an animation are before the animation starts and after it ends.
//
// visibility   Determines whether or not a transformed element is visible when it is not facing the screen.

$countDefault: 1 !default;
$durationDefault: 1s !default;
$delayDefault: 0s !default;
$functionDefault: ease !default;
$fillDefault: both;
$visibilityDefault: hidden !default;

@mixin count($count: 1) {
  -webkit-animation-iteration-count: $count;
  animation-iteration-count: $count;
}

@mixin duration($duration: 1s) {
  -webkit-animation-duration: $duration;
  animation-duration: $duration;
}

@mixin delay($delay: 0.2s) {
  -webkit-animation-delay: $delay;
  animation-delay: $delay;
}

@mixin function($function: ease) {
  -webkit-animation-timing-function: $function;
  animation-timing-function: $function;
}

@mixin fill-mode($fill: both) {
  -webkit-animation-fill-mode: $fill;
  animation-fill-mode: $fill;
}

@mixin visibility($visibility: hidden) {
  -webkit-backface-visibility: $visibility;
  backface-visibility: $visibility;
}

@mixin transform($property) {
  -webkit-transform: $property;
  transform: $property;
}

@mixin transform-origin($transform-origin: center center) {
  -webkit-transform-origin: $transform-origin;
  transform-origin: $transform-origin;
}

@mixin transform-style($transform-style: flat) {
  -webkit-transform-style: $transform-style;
  transform-style: $transform-style;
}

@mixin animation-name($animation-name) {
  -webkit-animation-name: $animation-name;
  animation-name: $animation-name;
}

@mixin keyframes($animation-name) {
  @-webkit-keyframes #{$animation-name} {
    @content;
  }

  // @-moz-keyframes #{$animation-name} {
  //   @content;
  // }

  // @-ms-keyframes #{$animation-name} {
  //   @content;
  // }

  // @-o-keyframes #{$animation-name} {
  //   @content;
  // }

  @keyframes #{$animation-name} {
    @content;
  }
}
