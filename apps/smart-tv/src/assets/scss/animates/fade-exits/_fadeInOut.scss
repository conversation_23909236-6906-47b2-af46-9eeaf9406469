///
/// @group box sliding
////

////
///
@use "../../settings/function" as fn;

////
/// @param properties
////
@use "../properties" as prop;

/// @group slideOut
////

////
///@group keyframes
////

@include prop.keyframes(fadeInOut) {
  0% {
    opacity: 0;
  }
  15% {
    opacity: 1;
  }
  85% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@mixin fadeInOut(
  $name: fadeInOut,
  $delay: prop.$delayDefault,
  $count: prop.$countDefault,
  $duration: prop.$durationDefault,
  $function: prop.$functionDefault,
  $fill: prop.$fillDefault,
  $visibility: prop.$visibilityDefault
) {
  @include prop.animation-name($name);
  @include prop.count($count);
  @include prop.duration($duration);
  @include prop.delay($delay);
  @include prop.function($function);
  @include prop.fill-mode($fill);
  @include prop.visibility($visibility);
}
