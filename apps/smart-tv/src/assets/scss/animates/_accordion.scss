////
/// @group box animate accordion
////

////
///
@use "../settings/function" as fn;

////
/// @param properties
////
@use "properties" as prop;

/// @keyframes accordion-width
////
@include prop.keyframes(accordion-height) {
  from {
    height: 0;
  }
  to {
    height: 100%;
  }
}

/// @keyframes accordion-width
////
@include prop.keyframes(accordion-width) {
  from {
    width: fn.percent-unit(238);
  }
  to {
    width: fn.percent-unit(1468);
  }
}

/// @keyframes accordion-width-reverse
////
@include prop.keyframes(accordion-width-reverse) {
  from {
    width: fn.percent-unit(1478);
  }
  to {
    width: fn.percent-unit(238);
  }
}

/// @mixin accordion
////
@mixin accordionist(
  $name: accordion-height,
  $delay: prop.$delayDefault,
  $count: prop.$countDefault,
  $duration: prop.$durationDefault,
  $function: prop.$functionDefault
) {
  @include prop.animation-name($name);
  @include prop.delay($delay);
  @include prop.count($count);
  @include prop.duration($duration);
  @include prop.function($function);
}

/// @Style animate-spin
/// Return animate-spin
///
/// Desc updating...
/// @param {Value} $count - Property value: number|infinite|initial|inherit.
/// @param {Value} $percent - Property value: time|initial|inherit.
/// @return {Value} $function - Property value: linear|ease|ease-in|ease-out|ease-in-out|step-start|
///                             step-end|steps(int,start|end)|cubic-bezier(n,n,n,n)|initial|inherit
////

.animate-accordion-horizontal {
  @include accordionist(
    $name: accordion-width,
    $delay: 2,
    $count: 1,
    $duration: 0.5s,
    $function: linear
  );
}

.animate-accordion-horizontal-reverse {
  @include accordionist(
    $name: accordion-width-reverse,
    $delay: 2,
    $count: 1,
    $duration: 0.5s,
    $function: linear
  );
}

.animate-accordion-vertical {
  @include accordionist($count: 1, $duration: 1s, $function: linear);
}
