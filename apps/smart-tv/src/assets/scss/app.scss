/**
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
*
*/

///
/// CONTENT
/// 1. Vendor
/// 2. Setting
/// 3. Global
/// 4. Utilities
/// 5. Layouts
/// 6. Containers
/// 7. Components
/// 8. Player
///

///
/// 1. #VENDOR
/// @Use Vendor
///

///
/// 2. #SETTING
///
// @use 'settings/settings' as settings;

///
/// 3. #GLOBAL
///
@use "base/global";
@use "base/font";
@use "base/typography";

///
/// 4. #UTILITIES
///
@use "utilities/ratio";
@use "./utilities/focus-effect";
@use "utilities/scroll";

///
/// 5. #LAYOUTS
///
@use "layouts/header/header";
@use "layouts/main/main";
@use "layouts/main/main-home";
@use "layouts/main/main-intro";
@use "layouts/main/main-live";
@use "layouts/main/main-user";
@use "layouts/main/main-sub-page";
@use "layouts/main/main-category";
@use "layouts/main/main-promotion";
@use "layouts/grid/grids";
@use "layouts/sidebar/sidebar";
@use "layouts/sidebar/navback";

///
/// 6. #CONTAINERS
///
@use "containers/blocks/block";
@use "containers/blocks/block-login";
@use "containers/blocks/block-payment";
@use "containers/blocks/block-payment-bill";
@use "containers/blocks/block-payment-gs";
@use "containers/blocks/block-payment-ipc";
@use "containers/blocks/block-payment-method";
@use "containers/blocks/block-payment-pack";
@use "containers/blocks/block-payment-result";
@use "containers/blocks/block-payment-sms";
@use "containers/blocks/block-payment-voucher";
@use "containers/blocks/block-payment-wallet";
@use "containers/blocks/block-themes";
@use "containers/blocks/block-transaction";
@use "containers/blocks/block-payment-duration";
@use "containers/ribbons/ribbon";
@use "containers/ribbons/ribbon-live";
@use "containers/ribbons/ribbon-live-tv-highlight";
@use "containers/grid/grid";
@use "containers/sections/section";
@use "containers/sections/section-payment";
@use "containers/sections/section-payment-method";
@use "containers/sections/section-payment-result";
@use "containers/sections/section-payment-pack";
@use "containers/videoIntro/intro";
@use "containers/payment/payment";
@use "containers/playerVOD/playerVOD";
@use "containers/voucher/voucher";
@use "containers/voucher/keyboard-voucher";
@use "containers/userProfile/profile";
@use "containers/userSetting/setting";
@use "containers/search/search";
@use "containers/liveStream/liveStream";
@use "containers/liveStream/game/game";
@use "containers/livetvNewUI/thumnailGrid/thumnailGrid";

///
/// 7. #COMPONENTS
///
@use "components/badges/badges";
@use "components/badges/badges-horizontal";
@use "components/badges/badges-privilege";
@use "components/avatar/avatar";
@use "components/banner/banner";
@use "components/banner/banner-coming";
@use "components/banner/banner-appeal";
@use "components/buttons/button";
@use "components/buttons/button-hollow";
@use "components/billboard/billboard";
@use "components/cards/card";
@use "components/cards/card-candidate";
@use "components/cards/card-category";
@use "components/cards/card-channel";
@use "components/cards/card-coming-soon";
@use "components/cards/card-detail";
@use "components/cards/card-direction";
@use "components/cards/card-epg";
@use "components/cards/card-groups";
// @use 'components/cards/card-history';
// @use 'components/cards/card-horizontal';
@use "components/cards/card-intro";
@use "components/cards/card-ipc";
@use "components/cards/card-layout";
@use "components/cards/card-live-stream";
@use "components/cards/card-live-tv";
@use "components/cards/card-origin";
@use "components/cards/card-payment";
@use "components/cards/card-payment-method";
@use "components/cards/card-payment-pack";
@use "components/cards/card-payment-price";
@use "components/cards/card-payment-qr-code";
@use "components/cards/card-qr-code";
@use "components/cards/card-sign";
@use "components/cards/card-step";
@use "components/cards/card-history";
@use "components/cards/card-horizontal";
@use "components/cards/card-live-tv-highlight";
@use "components/cards/card-grid-channel";
@use "components/cards/card-banner";
///
/// Checkbox
@use "components/checkbox/checkbox";
@use "components/checkbox/custom-checkbox";
///
@use "components/comments/comment";
@use "components/emulators/emulator";
@use "components/emulators/emulator-devices";
@use "components/epg/epg";
///
/// Dialog-v-2
@use "components/dialog-v2/dialog";
@use "components/dialog-common/common";
@use "components/dialog-common/minimal-common";
@use "components/dialog-common/grid-card-common";

@use "components/focus-box/focus-box";
@use "components/forms/form";
@use "components/forms/form-control";
@use "components/forms/form-ipc";
@use "components/icons/icon";
@use "components/inputs/input";
@use "components/keyboard/keyboard";
@use "components/list/list";
@use "components/list/list-banks";
@use "components/list/list-color";
@use "components/list/list-pack";
@use "components/logo/logo";
@use "components/logo/logo-partner";
@use "components/nav/nav";
@use "components/nav/nav-audio";
@use "components/nav/nav-tabs";
@use "components/popup/popup";
@use "components/popup/popup-login";
@use "components/popup/popup-promotion-ads";
@use "components/popup/popup-payment-result";
@use "components/popup/popup-report";
@use "components/price/price";
@use "components/price/price-horizontal";
@use "components/toast-message/toast-message";
@use "components/cards/card-top-views";
@use "components/rank/rank";
@use "components/rate/rate";
@use "components/spinner/spinner";
@use "components/spinner/dot-flashing";
@use "components/screen-saver/screen-saver";
@use "components/stages/stages";
@use "components/stages/stages-payment";
@use "components/steps/steps";
@use "components/steps/steps-payment";
@use "components/popup/popup-player";
@use "components/tags/tags";
@use "components/tags/tags-color";
@use "components/tags/tags-line";
@use "components/tags/tags-custom";
@use "components/tags/tags-size";
@use "components/tags/tags-variant";
@use "components/tips/tips";
@use "components/tips/tips-live";
@use "components/tips/tip-livestream";
@use "components/tips/tips-tour-guide";
@use "components/tip-box/tip-box";
@use "components/tip-box/tip-primere";
@use "components/title-meta/title-meta";
@use "components/personalization/personal";
@use "components/page-transition/page-transition";
@use "components/text-area/custom-textarea";
@use "components/cards/card-live-tv-new-ui";
@use "components/disclamer/disclamer";

//empty-page
@use "components/empty-page/empty-page";

// vote
@use "components/vote/vote";

// ads
@use "components/ads/ads";

///
/// 7. #PLAYER
///
@use "player/player";
@use "player/player-button";
@use "player/player-channels";
@use "player/player-channels-new-ui";
@use "player/player-control";
@use "player/player-control-bottom";
@use "player/player-control-highway";
@use "player/player-control-top";
@use "player/player-info";
@use "player/player-nav";
@use "player/player-poster";
@use "player/player-schedules";
@use "player/player-seek-bar";
@use "player/player-soft-logo";
@use "player/player-tip";
@use "player/player-time";
@use "player/player-video";
@use "player/player-preview";
@use "player/v-player";
@use "player/player-box-tvod";
