/// @group setting
@use "src/assets/scss/settings/function" as fn;

/// @group mixin
@use "position" as position;

/// Shorthand mixin for pseudo class
/// @param {String} $width [100%] - Width of pseudo
/// @param {String} $height [100%] - Height of pseudo
/// @param {String} $display [block] - Display type of pseudo (block|inline-block|none...)
/// @param {String} $pos [absolute] - Position type of pseudo (absolute|relative)
/// @param {String} $content [""] - Content of pseudo element
/// @require {function} percent-unit

@mixin pseudo(
  $width: 100%,
  $height: 100%,
  $display: block,
  // $position: absolute,
  $content: ""
) {
  content: $content;
  display: $display;
  // @if $position != null {
  //   @include position.position(top right bottom left);
  // }
  // position: $position;
  width: fn.percent-unit($width);
  height: fn.percent-unit($height);
}

@mixin pseudo-options($content, $display: null, $width: null, $height: null) {
  content: $content;
  display: $display;
  width: $width;
  height: $height;
}
