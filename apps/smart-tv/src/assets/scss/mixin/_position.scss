/// https://hugogiraudel.com/2014/05/19/new-offsets-sass-mixin/
/// @use variables
@use "sass:list";
@use "../settings/function" as fn;

/// Shorthand mixin for offset positioning
/// @param {String} $position - Either `relative`, `absolute` or `fixed`
/// @param {Offset} $offset [null] - Special string in template "top {Length?} {Length?} left {Length?} right {Length?}"
/// @require {function} is-valid-length
@mixin position($position, $args: ()) {
  $offsets: top right bottom left;
  position: $position;

  @each $offset in $offsets {
    // All this code happens inside the loop
    $index: list.index($args, $offset);

    // If offset is found in the list
    @if $index {
      // If it is found at last position
      @if $index == list.length($args) {
        #{$offset}: 0;
      }

      // If it is followed by a value
      @else {
        $next: list.nth($args, $index + 1);

        // If the next value is value length
        @if fn.is-valid-length($next) {
          #{$offset}: $next;
        }

        // If the next value is another offset
        @else if index($offsets, $next) {
          #{$offset}: 0;
        }

        // If it is invalid
        @else {
          @warn "Invalid value `#{$next}` for offset `#{$offset}`.";
        }
      }
    }
  }
}

@mixin absolute($args: ()) {
  @include position(absolute, $args);
}

@mixin fixed($args: ()) {
  @include position(fixed, $args);
}

@mixin relative($args: ()) {
  @include position(relative, $args);
}
