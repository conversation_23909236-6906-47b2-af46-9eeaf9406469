///
/// FLEX MIXIN
/// 1.FLEX
/// 2.FLEX FLOW
///

/// @group flex flex-grow flex-shrink flex-basis|auto|initial|inherit;
///  - The flex property is a shorthand property for:
///
/// @param {Number | String} $flex-grow
///  - A number specifying how much the item will grow relative to the rest of the flexible items.
///    Default value is 0.
///
/// @param {Number | String} $flex-shrink number|initial|inherit
///   - A number specifying how much the item will shrink relative to the rest of the flexible items.
///     Default value is 1
///
/// @param {Number | String} $flex-basis number | initial | inherit
///   - A length unit, or percentage, specifying the initial length of the flexible item(s);
///

@mixin flex($dir, $flex-grow: 1, $flex-shrink: 0, $flex-basis: auto) {
  @if $dir == "grow" {
    flex-grow: $flex-grow;
  } @else if $dir == "shrink" {
    flex-shrink: $flex-shrink;
  } @else if $dir == "basis" {
    flex-basis: $flex-basis;
  } @else {
    flex-grow: $flex-grow;
    flex-shrink: $flex-shrink;
    flex-basis: $flex-basis;
  }
}

// Mixin flex-grow
@mixin flex-grow($args...) {
  @include flex(grow, $args...);
}

// Mixin flex-shrink
@mixin flex-shrink($args...) {
  @include flex(shrink, $args...);
}

// Mixin flex-basis
@mixin flex-basis($args...) {
  @include flex(basis, $args...);
}

// Mixin flex-basis
@mixin flex-options($args...) {
  @include flex(options, $args...);
}

/// @group flex-flow flex-direction flex-wrap|initial|inherit;
///  - The flex-flow property is a shorthand property for:
///
/// @param {String} $flex-direction row|row-reverse|column|column-reverse|initial|inherit;
///  - The flex-direction property specifies the direction of the flexible items.
///
/// @param {String} $flex-wrap nowrap|wrap|wrap-reverse|initial|inherit;
///  - The flex-wrap property specifies whether the flexible items should wrap or not.
///

@mixin flex-flow($dir, $flex-direction, $flex-wrap: initial) {
  //
  @if $dir == "direction" {
    flex-direction: $flex-direction;
  } @else if $dir == "wrap" {
    flex-wrap: $flex-wrap;
  } @else {
    flex-flow: $flex-direction $flex-wrap;
  }
}

// Mixin flex-direction
@mixin flex-direction($args...) {
  @include flex-flow("direction", $args...);
}

// Mixin flex-wrap
@mixin flex-wrap($args...) {
  @include flex-flow("wrap", $args...);
}

// Mixin flex-options
@mixin flex-options($args...) {
  @include flex-flow("options", $args...);
}

@mixin justify-content($value: flex-start) {
  @if $value == flex-start {
    -webkit-box-pack: start;
    -moz-box-pack: start;
    -ms-flex-pack: start;
  } @else if $value == flex-end {
    -webkit-box-pack: end;
    -moz-box-pack: end;
    -ms-flex-pack: end;
  } @else if $value == space-between {
    -webkit-box-pack: justify;
    -moz-box-pack: justify;
    -ms-flex-pack: justify;
  } @else if $value == space-around {
    -ms-flex-pack: distribute;
  } @else {
    -webkit-box-pack: $value;
    -moz-box-pack: $value;
    -ms-flex-pack: $value;
  }
  -webkit-justify-content: $value;
  justify-content: $value;
}

// Packing Flex Lines
// - applies to: multi-line flex containers
// flex-start | flex-end | center | space-between | space-around | stretch
@mixin align-content($value: stretch) {
  // No Webkit Box Fallback.
  -webkit-align-content: $value;
  @if $value == flex-start {
    -ms-flex-line-pack: start;
  } @else if $value == flex-end {
    -ms-flex-line-pack: end;
  } @else {
    -ms-flex-line-pack: $value;
  }
  align-content: $value;
}

// Cross-axis Alignment
// - applies to: flex containers
// flex-start | flex-end | center | baseline | stretch
@mixin align-items($value: stretch) {
  @if $value == flex-start {
    -webkit-box-align: start;
    -moz-box-align: start;
    -ms-flex-align: start;
  } @else if $value == flex-end {
    -webkit-box-align: end;
    -moz-box-align: end;
    -ms-flex-align: end;
  } @else {
    -webkit-box-align: $value;
    -moz-box-align: $value;
    -ms-flex-align: $value;
  }
  -webkit-align-items: $value;
  align-items: $value;
}
