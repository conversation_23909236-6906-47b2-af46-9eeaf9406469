/// @group settings
@use "settings/function" as fn;

@mixin truncated($line, $line-height: null, $height: null) {
  @if $line == null or $line <= 0 {
    @error "#{$line} is an invalid value (don't null or less than).";
  } @else if $line == 1 {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: $line-height;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $line;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: $line-height;
    // @if $line-height != null{
    //   height: fn.multiplication($line, $line-height);
    // }
  }
}
