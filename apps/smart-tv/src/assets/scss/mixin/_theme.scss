/**
* ============ VERSION ============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ================== THEME ==================
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* VARIABLE
* THEMES MIXIN
*
*/
/// @group variables
@use "settings/variables" as var;
@use "settings/function" as fn;

/// @group mixin

/// @group theme-mixin
@mixin themes(
  $themes: base,
  $element: null,
  $element-theme: null,
  $element-bg: null,
  $element-color: null,
  $element-footer-bg: null,
  $element-header-bg: null
) {
  @each $theme in $themes {
    @if $theme == base {
      background-color: fn.el-color(
        $color: $element-bg,
        $shade: $theme,
        $map: $element-theme
      );
      @if $element-footer-bg != null {
        #{$element}__footer {
          background-color: fn.el-color(
            $color: $element-footer-bg,
            $shade: $theme,
            $map: $element-theme
          );
        }
      }
      @if $element-header-bg != null {
        #{$element}__header {
          background-color: fn.el-color(
            $color: $element-header-bg,
            $shade: $theme,
            $map: $element-theme
          );
        }
      }
      @if $element-color != null {
        #{$element}__text {
          color: fn.el-color(
            $color: $element-color,
            $shade: $theme,
            $map: $element-theme
          );
        }
      }
    } @else {
      #{$element}--for-#{$theme} {
        @at-root #{$element}#{&} {
          background-color: fn.el-color(
            $color: $element-bg,
            $shade: $theme,
            $map: $element-theme
          );
          @if $element-footer-bg != null {
            #{$element}__footer {
              background-color: fn.el-color(
                $color: $element-footer-bg,
                $shade: $theme,
                $map: $element-theme
              );
            }
          }
          @if $element-header-bg != null {
            #{$element}__header {
              background-color: fn.el-color(
                $color: $element-header-bg,
                $shade: $theme,
                $map: $element-theme
              );
            }
          }
          @if $element-color != null {
            #{$element}__text {
              color: fn.el-color(
                $color: $element-color,
                $shade: $theme,
                $map: $element-theme
              );
            }
          }
        }
      }
    }
  }
}
