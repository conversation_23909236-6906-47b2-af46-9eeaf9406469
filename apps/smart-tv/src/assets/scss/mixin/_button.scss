/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ================== BUTTON ==================
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* VARIABLE
* INPUT STYLES
*
*/

/// @group variables
@use "settings/variables" as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/position" as position;

/// @group Button-Variables

/// @group button-colors
$button-colors: (
  backgrounds: (
    "base": rgba(
        fn.el-color(
          $color: v-black,
          $shade: base,
        ),
        0.5
      ),
    // Set background color default
    "dark": rgba(
        fn.el-color(
          $color: v-black,
          $shade: base,
        ),
        0.5
      ),
    // Set background color for dark
    "light": fn.el-color(
        $color: v-gray,
        $shade: 60,
      ),
    // Set background color for light
  ),
  background-focus: (
    "base": fn.el-color(
        $color: v-medium-sea-green,
        $shade: 149,
      ),
    // Set background color when focusing on a default
    "dark": fn.el-color(
        $color: v-medium-sea-green,
        $shade: 149,
      ),
    // Set background color when focusing on a dark
    "light": fn.el-color(
        $color: v-medium-sea-green,
        $shade: 149,
      ),
    // Set background color when focusing on a light,,,
  ),
  background-actives: (
    "base": fn.el-color(
        $color: v-medium-sea-green,
        $shade: 149,
      ),
    // Set background color when active on a default
    "dark": fn.el-color(
        $color: v-medium-sea-green,
        $shade: 149,
      ),
    // Set background color when active on a dark
    "light": fn.el-color(
        $color: v-medium-sea-green,
        $shade: 149,
      ),
    // Set background color when active on a light,,,
  ),
  background-disables: (
    "base": rgba(
        fn.el-color(
          $color: v-black,
          $shade: base,
        ),
        0.5
      ),
    // Set background color when disabled on a default
    "dark": rgba(
        fn.el-color(
          $color: v-black,
          $shade: base,
        ),
        0.5
      ),
    // Set background color when disabled on a dark
    "light": fn.el-color(
        $color: v-gray,
        $shade: 80,
      ),
    // Set background color when disabled on a light
  ),
  borders: (
    "base": fn.el-color(
        $color: v-gray,
        $shade: 61,
      ),
    // Set border color default
    "dark": fn.el-color(
        $color: v-gray,
        $shade: 61,
      ),
    // Set border color dark
    "light": fn.el-color(
        $color: v-gray,
        $shade: 60,
      ),
    // Set border color light
  ),
  border-focus: (
    "base": fn.el-color(
        $color: v-medium-sea-green,
        $shade: 149,
      ),
    // Set border color when focusing on a default
    "dark": fn.el-color(
        $color: v-medium-sea-green,
        $shade: 149,
      ),
    // Set border color when focusing on a dark
    "light": fn.el-color(
        $color: v-medium-sea-green,
        $shade: 149,
      ),
    // Set border color when focusing on a light
  ),
  border-actives: (
    "base": rgba(
        fn.el-color(
          $color: v-black,
          $shade: base,
        ),
        0.5
      ),
    // Set border color when active on a default
    "dark": rgba(
        fn.el-color(
          $color: v-black,
          $shade: base,
        ),
        0.5
      ),
    // Set border color when active on a dark
    "light": fn.el-color(
        $color: v-gray,
        $shade: 80,
      ),
    // Set border color when active on a light
  ),
  border-disables: (
    "base": rgba(
        fn.el-color(
          $color: v-black,
          $shade: base,
        ),
        0.5
      ),
    // Set border color when disabled on a default
    "dark": rgba(
        fn.el-color(
          $color: v-black,
          $shade: base,
        ),
        0.5
      ),
    // Set border color when disabled on a dark
    "light": fn.el-color(
        $color: v-gray,
        $shade: 80,
      ),
    // Set border color when disabled on a light
  ),
  colors: (
    "base": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    // Set color color default
    "dark": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    // Set color color dark
    "light": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    // Set color color light
  ),
  color-focus: (
    "base": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    // Set color color when focusing on a default
    "dark": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    // Set color color when focusing on a dark
    "light": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    // Set color color when focusing on a light
  ),
  color-actives: (
    "base": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    // Set color color when active on a default
    "dark": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    // Set color color when active on a default
    "light": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    // Set color color when active on a default
  ),
  color-disables: (
    "base": fn.el-color(
        $color: v-gray,
        $shade: 39,
      ),
    // Set color color when disabled on a default
    "dark": fn.el-color(
        $color: v-gray,
        $shade: 39,
      ),
    // Set color color when disabled on a default
    "light": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    // Set color color when disabled on a default
  ),
  outlines: (
    "base": rgba(
        fn.el-color(
          $color: v-black,
          $shade: base,
        ),
        0.5
      ),
    // Set outline color default
    "dark": rgba(
        fn.el-color(
          $color: v-black,
          $shade: base,
        ),
        0.5
      ),
    // Set outline color default
    "light": fn.el-color(
        $color: v-gray,
        $shade: 80,
      ),
    // Set outline color default
  ),
);

/// @group mixin-button
@mixin button($themes: var.$themes) {
  @include box.box($width: auto, $height: fn.percent-unit(62));
  align-items: center;
  border-style: solid;
  border-width: fn.percent-unit(2);
  border-radius: fn.percent-unit(4);
  box-sizing: border-box;
  display: flex;
  font-size: fn.percent-unit(28);
  font-weight: 500;
  justify-content: center;
  outline-style: solid;
  outline-width: 0;
  padding: 0 fn.percent-unit(30);

  .icon {
    margin-right: fn.percent-unit(10);
  }

  @each $theme in $themes {
    @if $theme == base {
      background-color: fn.el-color(
        $color: backgrounds,
        $shade: "base",
        $map: $button-colors
      );
      border-color: fn.el-color(
        $color: borders,
        $shade: "base",
        $map: $button-colors
      );
      color: fn.el-color($color: colors, $shade: "base", $map: $button-colors);
      outline-color: fn.el-color(
        $color: outlines,
        $shade: "base",
        $map: $button-colors
      );

      .icon {
        color: fn.el-color(
          $color: colors,
          $shade: "base",
          $map: $button-colors
        );
      }

      &:not(.disabled),
      &:not(:disabled) {
        &:focus,
        &.focus,
        &.active {
          background-color: fn.el-color(
            $color: background-focus,
            $shade: "base",
            $map: $button-colors
          );
          border-color: fn.el-color(
            $color: border-focus,
            $shade: "base",
            $map: $button-colors
          );
          color: fn.el-color(
            $color: color-focus,
            $shade: "base",
            $map: $button-colors
          );
          outline-color: fn.el-color(
            $color: outlines,
            $shade: "base",
            $map: $button-colors
          );

          .icon {
            color: fn.el-color(
              $color: color-focus,
              $shade: "base",
              $map: $button-colors
            );
          }
        }
      }
      &.disabled,
      &:disabled {
        background-color: fn.el-color(
          $color: background-disables,
          $shade: "base",
          $map: $button-colors
        );
        border-color: fn.el-color(
          $color: border-disables,
          $shade: "base",
          $map: $button-colors
        );
        color: fn.el-color(
          $color: color-disables,
          $shade: "base",
          $map: $button-colors
        );
        outline-color: fn.el-color(
          $color: outline-disables,
          $shade: "base",
          $map: $button-colors
        );

        .icon {
          color: fn.el-color(
            $color: color-disables,
            $shade: "base",
            $map: $button-colors
          );
        }
      }
    } @else {
      &.button--for-#{$theme} {
        background-color: fn.el-color(
          $color: backgrounds,
          $shade: $theme,
          $map: $button-colors
        );
        border-color: fn.el-color(
          $color: borders,
          $shade: $theme,
          $map: $button-colors
        );
        color: fn.el-color(
          $color: colors,
          $shade: $theme,
          $map: $button-colors
        );
        outline-color: fn.el-color(
          $color: outlines,
          $shade: $theme,
          $map: $button-colors
        );

        .icon {
          color: fn.el-color(
            $color: colors,
            $shade: $theme,
            $map: $button-colors
          );
        }

        &:not(.disabled),
        &:not(:disabled) {
          &:focus,
          &.focus,
          &.active {
            background-color: fn.el-color(
              $color: background-focus,
              $shade: $theme,
              $map: $button-colors
            );
            border-color: fn.el-color(
              $color: border-focus,
              $shade: $theme,
              $map: $button-colors
            );
            color: fn.el-color(
              $color: color-focus,
              $shade: $theme,
              $map: $button-colors
            );
            outline-color: fn.el-color(
              $color: outline-focus,
              $shade: $theme,
              $map: $button-colors
            );

            .icon {
              color: fn.el-color(
                $color: color-focus,
                $shade: $theme,
                $map: $button-colors
              );
            }
          }
        }
        &.disabled,
        &:disabled {
          background-color: fn.el-color(
            $color: background-disables,
            $shade: $theme,
            $map: $button-colors
          );
          border-color: fn.el-color(
            $color: border-disables,
            $shade: $theme,
            $map: $button-colors
          );
          color: fn.el-color(
            $color: color-disables,
            $shade: $theme,
            $map: $button-colors
          );
          outline-color: fn.el-color(
            $color: outline-disables,
            $shade: $theme,
            $map: $button-colors
          );

          .icon {
            color: fn.el-color(
              $color: color-disables,
              $shade: $theme,
              $map: $button-colors
            );
          }
        }
      }
    }
  }
}
