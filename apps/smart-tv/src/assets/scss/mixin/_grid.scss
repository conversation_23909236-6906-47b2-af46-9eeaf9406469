/// @group setting
@use "../settings/variables" as var;
@use "../settings/function" as fn;

///
/// @group grid
/// @param {default/block} $grid-type - `default`, `block`.
/// @param {Column} $col - Column width if the default grid, whereas the block grid is the number of columns
/// @require {Grid Column} $grid-col - Grid divided by column type: 12/24/32 / other
///
@mixin grid(
  $grid-type: default,
  $col,
  $grid-col: var.$grid-col-default,
  $margin-x: 0
) {
  @if $grid-type == default {
    $width: fn.grid($grid-col, $col);
    $margin: fn.percent-unit($margin-x) * 2;
    flex: 0 0 $width;
    @if $margin-x == 0 {
      max-width: $width;
    } @else {
      max-width: calc(#{$width} - #{$margin});
    }
  } @else if $grid-type == block {
    $width: fn.grid($col, 1);
    $margin: fn.percent-unit($margin-x) * 2;

    flex: 0 0 $width;

    @if $margin-x == 0 {
      max-width: $width;
    } @else {
      max-width: calc(#{$width} - #{$margin});
      margin-left: fn.percent-unit($margin-x);
      margin-right: fn.percent-unit($margin-x);
    }
  }
}
