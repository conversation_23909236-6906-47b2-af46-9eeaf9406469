////
/// #FONTS MIXIN
////

////
/// #CONTENTS
////
/// 0. FONT-VARIABLE
/// 1. FONT-FACE
/// 2. FONT-FAMILY
/// 3. FONT-WEB-SAFE
/// 4. FONT-FALLBACKS
/// 5. FONT-STYLE
/// 6. FONT-SIZE
/// 7. FONT-PAIRING
/// 8. FONT-SHORTHAND

////
/// 0. FONT-VARIABLE
////

/// @group variable-common
$root-path-font: "./../fonts/";
$font-format: format("truetype");

/// @group font-family
$font-family: (
  "roboto-regular": "Roboto Regular",
  "roboto-light": "Roboto Light",
  "roboto-medium": "Roboto Medium",
  "roboto-bold": "Roboto Bold",
  "roboto-black": "Roboto Black",
) !default;

/// @group font-files
$font-files: (
  "roboto-regular": "Roboto-Regular",
  "roboto-light": "Roboto-Light",
  "roboto-medium": "Roboto-Medium",
  "roboto-bold": "Roboto-Bold",
  "roboto-black": "Roboto-Black",
) !default;

/// @group font-types
$font-types: (
  "open-type": "otf",
  "true-type": "ttf",
  "embedded-open-type": "eot",
  "web-open-font-format": "woff",
  "web-open-font-format-2": "woff2",
) !default;

/// @group font-weight
$font-weights: (
  "roboto-light": "300",
  "roboto-regular": "400",
  "roboto-medium": "500",
  "roboto-bold": "700",
  "roboto-black": "900",
) !default;

/// @group function
@use "sass:map";
@use "settings/function" as fn;

////
/// 1.FONT-FACE
////
@mixin font-face($type) {
  // get font type
  $font-type: map-get(
    $map: $font-types,
    $key: $type,
  );
  @each $font-family-key, $font-family-name in $font-family {
    // get file name
    $font-files-name: map-get(
      $map: $font-files,
      $key: $font-family-key,
    );
    // @debug "font-file-name: #{$font-files-name}";
    // get font weight
    $font-weight: map-get(
      $map: $font-weights,
      $key: $font-family-key,
    );
    // @debug "font-weight: #{$font-weight}";
    @font-face {
      font-family: "#{$font-family-name}";
      src: url(#{$root-path-font}#{$font-files-name}.#{$font-type}) #{$font-format};
      font-weight: #{$font-weight};
      font-style: normal;
    }
  }
}

////
/// 2.FONT-FAMILY
////
@mixin font-family($name) {
  @if $name != null {
    $font-family: map.get(
      $map: $font-family,
      $key: $name,
    );
    font-family: "#{$font-family}", sans-serif;
  } @else {
    @error "The #{name} property has no null value, please check again :(";
  }
}

////
/// .FONT-SIZE
////
@mixin font-size($size) {
  font-size: fn.percent-unit($size);
}
