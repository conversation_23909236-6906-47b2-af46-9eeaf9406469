///
/// <PERSON><PERSON><PERSON>GROUND MIXIN
/// 1.#BACKGROUND GRADIENTS
/// 2.#BACKGROUND PROPERTIES
/// 3.#BACKGROUNDS
///

///
/// #BACKGROUND GRADIENTS
///
/// @gradients: Special case when using background-image
/// - linear-gradient(direction, color-stop1, color-stop2, ...)
///   $dir: to right(top | bottom | left) | [num]deg
/// - radial-gradient(shape size at position, start-color, ..., last-color)
///   $dir: shape size at position
///         shape: Defines the shape of the gradient. Possible values:
///                ellipse (default) | circle
///         size: Defines the size of the gradient. Possible values:
///               farthest-corner (default) | closest-side | closest-corner |
///               farthest-side
///         position: Defines the position of the gradient. Default is "center"
/// - repeating-linear-gradient (angle | to side-or-corner, color-stop1,
///   color-stop2, ...)
///   $dir: angle([num]deg) | to side-or-corner
///         (horizontal side: left | right, vertical side:top or bottom )
/// - repeating-radial-gradient(shape size at position, start-color, ...,
///   last-color)
///

@use "sass:color";
@use "sass:math";

@mixin gradients($dir, $type, $repeating: null, $color-stops...) {
  @if $repeating != null {
    background-image: #{$repeating}-#{$type}-gradient(
        $dir,
        $side-or-corner,
        $color-stops
      );
  } @else {
    background-image: #{$type}-gradient($dir, $color-stops);
  }
}

///
/// #BACKGROUND PROPERTIES
///

///
/// @bg-properties: The background property is a shorthand property for:
///                 bg-color bg-image position/bg-size bg-repeat bg-origin
///                 bg-clip bg-attachment initial|inherit;
/// - $name: attachment, clip, color, image, origin, position, repeat, size;
/// - $value: The passed values correspond to the values of $name
/// Ex:
///   @include bg-properties($name: color, $value: white); or
///   @include bg-properties(color, white );
/// Output:
///   background-color: white;
///

@mixin bg-properties($name, $value) {
  background-#{$name}: $value;
}

///
/// #BACKGROUNDS
///

///
/// @backgrounds
/// - The background property is a shorthand property for:
///   background-color
///   background-image
///   background-position
///   background-size
///   background-repeat
///   background-origin
///   background-clip
///   background-attachment
///
/// Ex: @include backgrounds(
///       $properties: gradient,
///       $value: radial,
///       $grad-dir: circle closest-side at left,
///       $color: white black white);
/// Output: background-image:
///          radial-gradient(circle closest-side at left, white black white);
///

@mixin backgrounds(
  $properties: none,
  $value,
  $grad-dir: null,
  $grad-repeat: null,
  $color: null
) {
  @if $properties == none {
    background: $value;
  } @else if $properties == gradient {
    @include gradients($grad-dir, $value, $grad-repeat, $color...);
  } @else {
    @include bg-properties($name: $properties, $value: $value);
  }
}

@mixin scrimGradient($startColor: "#000000", $direction: "to bottom") {
  $scrimCoordinates: (
    0: 1,
    19: 0.738,
    34: 0.541,
    47: 0.382,
    56.5: 0.278,
    65: 0.194,
    73: 0.126,
    80.2: 0.075,
    86.1: 0.042,
    91: 0.021,
    95.2: 0.008,
    98.2: 0.002,
    100: 0,
  );

  // $hue: hue($startColor);
  $hue: color.channel($startColor, "hue", $space: hsl);
  // $saturation: saturation($startColor);
  $saturation: color.channel($startColor, "saturation", $space: hsl);
  // $lightness: lightness($startColor);
  $lightness: color.channel($startColor, "lightness", $space: hsl);
  $stops: ();

  @each $colorStop, $alphaValue in $scrimCoordinates {
    $stop: hsla($hue, $saturation, $lightness, $alphaValue)
      percentage(math.div($colorStop, 100));
    $stops: append($stops, $stop, comma);
  }

  background: linear-gradient(unquote($direction), $stops);
}
