///
/// BOX MIXIN CONTENT
/// 1.GROUP SETTING
/// 2.GROUP MIXIN
/// 3.GROUP BOX
///

/// 1.@group setting
@use "../settings/palettes" as palettes;
@use "../settings/variables" as var;
@use "../settings/function" as fn;

/// 2.@group mixin

////
/// #GROUP BOX
////
@mixin box($width: null, $height: $width) {
  // $unit: unit($width);
  @if type-of($width) != number {
    width: $width;
  } @else {
    width: fn.percent-unit($width);
  }
  @if type-of($height) != number {
    height: $height;
  } @else {
    height: fn.percent-unit($height);
  }
}
