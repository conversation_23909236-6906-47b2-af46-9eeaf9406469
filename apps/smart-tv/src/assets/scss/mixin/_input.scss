/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ================== INPUT ==================
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* VARIABLE
* INPUT MIXIN
*
*/

/// @group setting
@use "settings/function" as fn;
@use "settings/variables" as var;

/// @group fonts
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/position" as position;

/// @group variable-color

// Base background
$input-bg-transparent: transparent;

// Base color
$input-color: fn.el-color(
  $color: v-white,
  $shade: base,
);
$input-color-place-holder: fn.el-color(
  $color: v-gray,
  $shade: 61,
);
$input-color-hover: fn.el-color(
  $color: v-white,
  $shade: base,
);
$input-color-focus: fn.el-color(
  $color: v-black,
  $shade: base,
);
$input-color-active: fn.el-color(
  $color: v-black,
  $shade: base,
);
$input-color-disabled: fn.el-color(
  $color: v-gray,
  $shade: 20,
);
$input-color-error: fn.el-color(
  $color: v-tomato,
  $shade: 165,
);

// Base border color
$input-border-color: fn.el-color(
  $color: v-gray,
  $shade: 39,
);
$input-border-color-hover: fn.el-color(
  $color: v-medium-sea-green,
  $shade: 149,
);
$input-border-color-focus: fn.el-color(
  $color: v-medium-sea-green,
  $shade: 149,
);
$input-border-color-warning: fn.el-color(
  $color: v-goldenrod,
  $shade: 156,
);
$input-border-color-error: fn.el-color(
  $color: v-tomato,
  $shade: 165,
);
$input-border-color-disabled: fn.el-color(
  $color: v-gray,
  $shade: 20,
);

// Label color
$input-label-color: fn.el-color(
  $color: v-black,
  $shade: base,
);

// Base shadow color
$input-shadow-color: rgba(
  fn.el-color(
    $color: v-black,
    $shade: base,
  ),
  0.3
);

/// @group input-variable
$input-border-radius: fn.percent-unit(4);
$input-height-default: fn.percent-unit(62);
$input-padding-y: fn.percent-unit(4);
$input-padding-x: fn.percent-unit(24);
$input-spacing: fn.percent-unit(40);

// Base font size
$input-font-size: fn.percent-unit(28);

/// @group input
@mixin input($modifier: false, $themes: base, $size: base) {
  %childStyleCommon {
    @include fonts.font-family("roboto-regular");
    @include fonts.font-size($input-font-size);
  }

  @if $modifier {
    &.input--custom {
      background-color: $input-bg-transparent;

      &:not(:last-child) {
        margin-bottom: $input-spacing;
      }

      &:not(.input-horizontal) {
        display: block;

        & > * {
          &:not(:last-child) {
            margin-bottom: fn.percent-unit(4);
          }
        }
      }

      .input {
        &__label {
          @extend %childStyleCommon;
          color: $input-label-color;
        }

        &__control {
          @extend %childStyleCommon;
          @include position.relative;
          align-items: center;
          border: 1px solid $input-border-color;
          border-radius: $input-border-radius;
          box-sizing: border-box;
          display: flex;
          height: $input-height-default;
          justify-content: flex-start;
          line-height: 1;
          padding: $input-padding-y $input-padding-x;

          .code {
            margin: 0 fn.percent-unit(12) 0 0;
          }

          & > span {
            &.center {
              display: flex;
              align-items: center;
              justify-content: center;
            }

            // &:not(:last-child) {
            //   margin-right: fn.percent-unit(16);
            // }

            .place-holder {
              @include fonts.font-family("roboto-medium");
              color: $input-color-place-holder;

              &:not(:last-child) {
                margin-right: fn.percent-unit(3);
              }

              &.completed {
                color: $input-color-active;
              }

              &.space {
                width: fn.percent-unit(5);
              }

              &.focus {
                color: $input-color-focus;
              }

              &.dotted {
                &::after {
                  display: block;
                  content: "";
                  width: fn.percent-unit(16);
                  height: fn.percent-unit(16);
                  border-radius: 50%;
                  background-color: #000;
                }
              }
            }
          }

          .icon {
            @include box.box($width: fn.percent-unit(40));
            @include position.absolute(left #{$input-padding-x} top 50%);
            transform: translateY(-50%);

            .vie {
              @include box.box($width: fn.percent-unit(40));
              @include fonts.font-size(40);
            }

            &:not(.align-right) {
              & + span,
              & ~ span {
                padding-left: fn.percent-unit(48);
              }
            }

            &.align-right {
              padding-right: fn.percent-unit(48);
            }
          }
        }

        &__notify {
          @include fonts.font-family("roboto-medium");
          @include fonts.font-size(24);
          padding-top: fn.percent-unit(24);

          &.text-center {
            text-align: center;
          }

          &.text-right {
            text-align: right;
          }
        }
      }

      &.focus {
        .input__control {
          border-color: $input-border-color-focus;
        }
      }

      &.warning {
        .input__control {
          border-color: $input-border-color-warning;
        }
      }

      &.error {
        .input__notify,
        &.input__notify {
          color: $input-color-error;
        }

        .input__control {
          border-color: $input-border-color-error;
        }
      }

      &.active {
        .input__control {
          border-color: $input-border-color-focus;
        }
      }

      &.disabled {
        .input__control {
          border-color: $input-border-color-disabled;

          .icon {
            .vie {
              color: $input-color-disabled;
            }
          }
        }
      }
    }
  }

  &.input--horizontal {
    display: flex;

    & > * {
      flex: 0 0 auto;
    }
  }

  // .invalid-feedback {
  //   color: $input-color-error;
  //   @include fonts.font-family("roboto-medium");
  //   @include fonts.font-size(24);
  //   padding-top: fn.percent-unit(24);

  //   &.text-center {
  //     text-align: center;
  //   }
  //   &.text-right {
  //     text-align: right;
  //   }
  // }
}
