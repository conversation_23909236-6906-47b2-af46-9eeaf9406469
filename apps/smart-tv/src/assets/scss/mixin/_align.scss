///
/// <PERSON>IG<PERSON> MIXIN
/// 1.ALIGN CONTENT
/// 2.ALIGN ITEM
/// 3.<PERSON>IGN SELF
///

///
/// #ALIGN CONTENT
///

///
/// @align-content
/// - The align-content property modifies the behavior of the flex-wrap property.
///   It is similar to align-items, but instead of aligning flex items, it aligns
///   flex lines.
/// - $dir: stretch | center | flex-start | flex-end | space-between |
///   space-around | initial | inherit;
/// Ex:
///    @include align-content($dir: stretch) or
///    @include align-content(stretch) (offer)
///

@mixin align-content($dir) {
  align-content: $dir;
}

///
/// #ALIGN ITEM
///

///
/// @align-item
/// - The align-items property specifies the default alignment for items inside the
///   flexible container.
/// - $dir: stretch | center | flex-start | flex-end | baseline | initial | inherit
/// Ex:
///    @include align-item($dir: stretch); or
///    @include align-item(stretch); (offer)
///

@mixin align-item($dir) {
  align-items: $dir;
}

///
/// #ALIGN SELF
///

///
/// - The align-self property specifies the alignment for the selected item inside the
///   flexible container.
/// - $dir: auto | stretch | center | flex-start | flex-end | baseline | initial |
///   inherit
/// Ex:
///    @include align-self($dir: auto); or
///    @include align-self(auto);
///

@mixin align-self($dir) {
  align-self: $dir;
}
