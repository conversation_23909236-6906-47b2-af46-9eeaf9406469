///
/// #PLAYER CHANNELS
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/layer" as layer;
@use "mixin/position" as position;

/// @group components
@use "components/progress/progress" as progress;

/// @group player-channels
//extended
%itemChildContentCenter {
  display: flex;
  align-items: center;
  justify-content: center;
}
.player-channels {
  @include position.absolute(right 0 right 0 bottom 0 left 0);
  @include box.box(100vw, 100vh);
  background-color: rgba(palettes.$black, 0.9);
  display: flex;
  flex-flow: column;
  // justify-content: center;
  align-items: center;
  padding: fn.percent-unit(60) fn.percent-unit(90);
  box-sizing: border-box;

  &__wrap {
    width: fn.percent-unit(1198);
    will-change: transform;
  }
  &__item {
    display: flex;
    flex-flow: row nowrap;

    & > * {
      // &:not(:first-child) {
      //   margin-left: fn.percent-unit(12);
      // }
      // &:not(:last-child) {
      //   margin-right: fn.percent-unit(12);
      // }
      &:not(.shrink) {
        flex: 1 1 0px;
      }
      &.shrink {
        flex: 0 0 auto;
      }
    }

    &:not(:last-child) {
      margin-bottom: fn.percent-unit(10);
    }
    &:not(.active) {
      .player-channels__content,
      .player-channels__thumbnail {
        visibility: hidden;
        opacity: 1;
      }
    }
  }
  &__num {
    @extend %itemChildContentCenter;
    @include box.box($width: auto, $height: fn.percent-unit(156));
    margin-right: percent-unit(12);
    font-size: fn.percent-unit(36);
    color: palettes.$white;
  }
  &__name {
    @extend %itemChildContentCenter;
    @include box.box(
      $width: fn.percent-unit(208),
      $height: fn.percent-unit(156)
    );
    margin-right: fn.percent-unit(69);
    img {
      // min-height: 100%;
      height: auto;
      width: auto;
      max-width: 100%;
      max-height: 100%;
    }
  }
  &__thumbnail {
    @extend %itemChildContentCenter;
    @include box.box(
      $width: fn.percent-unit(276),
      $height: fn.percent-unit(156)
    );
    will-change: visibility, opacity;

    img {
      @include box.box($width: 100%, $height: 100%);
    }
  }
  &__content {
    background-color: palettes.$white;
    padding: fn.percent-unit(10) fn.percent-unit(18);
    will-change: visibility, opacity;
  }
  &__duration {
    font-size: fn.percent-unit(24);
    color: palettes.$gray-22;
    line-height: 1.5;
  }
  &__title {
    font-size: fn.percent-unit(32);
    color: palettes.$gray-22;
    line-height: 1.2;
    // margin-bottom: fn.percent-unit(32);
    height: fn.percent-unit(77);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  &__progress {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: row wrap;
    box-sizing: border-box;
    .progress {
      @include progress.progress($height: fn.percent-unit(6));
      transform: none;
      flex: 1 1 0px;
      margin-right: fn.percent-unit(10);
    }
    .time-remain {
      flex: 0 0 auto;
      font-size: fn.percent-unit(20);
      margin-left: fn.percent-unit(10);
    }
  }
}
