///
/// #PLAYER CHANNELS
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/layer" as layer;
@use "mixin/position" as position;

/// @group components
@use "components/progress/progress" as progress;

/// @group player-channels
//extended
%itemChildContentCenter {
  display: flex;
  align-items: center;
  justify-content: center;
}
.player-channels-new-ui {
  @include position.absolute(right 0 top 0 bottom 0 left 0);
  background-color: rgba(palettes.$gray-11, 1);
  padding-top: fn.percent-unit(38);
  padding-left: fn.percent-unit(208);
  z-index: 9;
  .card.card--grid-channel {
    width: fn.percent-unit(400);
    height: fn.percent-unit(100);
    box-sizing: border-box;
    margin-right: fn.percent-unit(16px);
    margin-bottom: fn.percent-unit(12px);
  }

  &.forPlayer {
    background-color: transparent;
    padding-top: fn.percent-unit(16);
    padding-left: fn.percent-unit(30);
    .card.card--grid-channel {
      padding-left: fn.percent-unit(14);
      width: fn.percent-unit(330);
      height: fn.percent-unit(83);
      &.focus {
        .odr-number {
          color: palettes.$white;
        }
        .channel-title {
          color: palettes.$white;
        }
      }
      &.active {
        background: palettes.$gray-64;
        color: palettes.$white;
      }
    }
    .grid {
      margin-right: fn.percent-unit(50);
    }
  }

  .grid {
    margin-right: fn.percent-unit(76);
    &__wrapper {
      position: relative;
    }
    .channel-title {
      font-size: fn.percent-unit(23);
      line-height: fn.percent-unit(30);
      font-weight: 400;
    }
  }

  .focus-box {
    width: fn.percent-unit(400px);
    height: fn.percent-unit(100px);
  }

  .nav.nav--tabs {
    position: relative;
    z-index: 2;
    &::after {
      display: block;
      content: "";
      height: fn.percent-unit(50px);
      margin-top: fn.percent-unit(-2px);
      background: linear-gradient(180deg, #111111 0%, rgba(17, 17, 17, 0) 100%);
    }
    &::before {
      display: block;
      position: absolute;
      top: 0;
      left: fn.percent-unit(-1px);
      content: "";
      height: fn.percent-unit(72px);
      width: 102%;
      background: #111;
    }
    &.forPlayer {
      &::before {
        background: transparent;
      }
      &::after {
        height: fn.percent-unit(32);
        background: transparent;
      }
    }
  }
}
