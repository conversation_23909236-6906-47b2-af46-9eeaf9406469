///
/// #PLAYER Styles
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/layer" as layer;
@use "mixin/position" as position;

.player {
  @include box.box(100vw, 100vh);
  @include position.absolute(top 0 right 0 bottom 0 left 0);
  @include layer.layers(layer-1);

  &__inner {
    @include box.box(100%, 100%);
    position: relative;
  }
  &__wrap {
    @include box.box(100%, 100%);
    top: 0;
    position: absolute;
    bottom: 0;

    &--lshape-ads {
      left: fn.percent-unit(160);
      transform: scale(0.833333);
      top: -8.3vh;
      overflow: hidden;
      .list-channel {
        &.static-mode {
          top: fn.percent-unit(460);
        }

        .list-channel__container {
          overflow: hidden;
          .channel-item:first-of-type .focus-box {
            border-left: none;
          }
        }
      }

      .schedule-pannel-wrapper {
        > div {
          position: relative;
          left: fn.percent-unit(-2);
          padding: fn.percent-unit(60) 0 0 fn.percent-unit(164);
          max-height: fn.percent-unit(1021);
          overflow: hidden;
        }
      }
      .list-channel-panel {
        position: relative;
        left: fn.percent-unit(-2);
        overflow: hidden;
      }
      transition:
        left 0.3s linear,
        top 0.3s linear,
        transform 0.3s ease-in;
    }

    // STT channel
    .channel-odr {
      position: absolute;
      top: fn.percent-unit(60);
      right: fn.percent-unit(120);
      z-index: 1;
      color: #ffffff;
      font-size: fn.percent-unit(62);
      line-height: fn.percent-unit(74);
      font-family: Roboto;
      font-weight: Bold;
    }

    // Animation fade-in-out-up
    .fade-in-down {
      opacity: 0;
      transform: translate3d(0, -100%, 0);
    }

    .fade-in-down-enter {
      transition: all 0.5s ease-in-out !important;
    }
    .fade-in-down-enter-active {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
    .fade-in-down-enter-done {
      opacity: 1;
      transform: translate3d(0, 0, 0);
    }
    .fade-in-down-exit {
      transition: all 0.5s ease-in-out !important;
    }
    .fade-in-down-exit-active {
      opacity: 0;
      transform: translate3d(0, -100%, 0);
    }
    .fade-in-down-exit-done {
      opacity: 0;
      transform: translate3d(0, -100%, 0);
    }

    // Animation fade-in-out-down
    .fade-in-up {
      opacity: 0;
      transform: translate3d(0, 100%, 0);
      &.film-strip {
        transform: translate3d(-50%, 100%, 0);
        &.hd {
          transform: translate3d(-50%, 100%, 0) scale(0.66);
        }
      }
    }
    .fade-in-up-enter {
      transition: all 0.5s ease-in-out !important;
    }
    .fade-in-up-enter-active {
      opacity: 1;
      transform: translate3d(0, 0, 0);
      &.film-strip {
        transform: translate3d(-50%, 0, 0);
        &.hd {
          transform: translate3d(-50%, 0, 0) scale(0.66);
        }
      }
    }
    .fade-in-up-enter-done {
      opacity: 1;
      transform: translate3d(0, 0, 0);
      &.film-strip {
        transform: translate3d(-50%, 0, 0);
        &.hd {
          transform: translate3d(-50%, 0, 0) scale(0.66);
        }
      }
    }
    .fade-in-up-exit {
      transition: all 0.5s ease-in-out !important;
    }
    .fade-in-up-exit-active {
      opacity: 0;
      transform: translate3d(0, 100%, 0);
      &.film-strip {
        transform: translate3d(-50%, 100%, 0);
        &.hd {
          transform: translate3d(-50%, 100%, 0) scale(0.66);
        }
      }
    }
    .fade-in-up-exit-done {
      opacity: 0;
      transform: translate3d(0, 100%, 0);
      &.film-strip {
        transform: translate3d(-50%, 100%, 0);
        &.hd {
          transform: translate3d(-50%, 100%, 0) scale(0.66);
        }
      }
    }
    // Animation fade-in
    .fade-in {
      opacity: 0;
    }
    .fade-in-enter {
      transition: all 0.5s ease-in-out !important;
    }
    .fade-in-enter-active {
      opacity: 1;
    }
    .fade-in-enter-done {
      opacity: 1;
    }
    .fade-in-exit {
      transition: all 0.5s ease-in-out !important;
    }
    .fade-in-exit-active {
      opacity: 0;
    }
    .fade-in-exit-done {
      opacity: 0;
    }

    // Animation fade-in-right
    .fade-in-right {
      opacity: 0;
      transform: translate3d(100%, 0, 0);
    }
    .fade-in-right-enter {
      transition: all 0.5s ease-in-out !important;
    }
    .fade-in-right-enter-active {
      opacity: 1;
      transform: translate3d(0%, 0, 0);
    }
    .fade-in-right-enter-done {
      opacity: 1;
      transform: translate3d(0%, 0, 0);
    }
    .fade-in-right-exit {
      transition: all 0.5s ease-in-out !important;
    }
    .fade-in-right-exit-active {
      opacity: 0;
      transform: translate3d(100%, 0, 0);
    }
    .fade-in-right-exit-done {
      opacity: 0;
      transform: translate3d(100%, 0, 0);
    }
  }

  &-channels,
  &-schedules {
    @include layer.layers(layer-4);
  }
}
