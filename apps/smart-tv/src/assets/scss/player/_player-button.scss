///
/// #PLAYER CONTROL
///

/// @group setting
@use "../settings/variables" as var;
@use "../settings/function" as fn;
@use "../settings/palettes" as palettes;

/// @group mixin
@use "../mixin/box" as box;
// @use 'mixin/layer' as layer;
@use "../mixin/position" as position;

.player-button {
  background-color: transparent;
  box-shadow: none;
  border: none;

  // .icon {
  //   // margin-right: 0.5rem;
  // }

  .tags--quality {
    @include position.absolute(top 0 right fn.percent-unit(12));
  }

  &.focus,
  &.active {
    .icon {
      .vie {
        color: palettes.$green-3a;
      }
    }
  }
}

.player-control__button {
  display: flex;
  padding: fn.percent-unit(12) fn.percent-unit(20);
  align-items: center;

  .icon {
    width: fn.percent-unit(32);

    .vie {
      width: fn.percent-unit(26);
      font-size: fn.percent-unit(18);
    }

    & + .player__text,
    & ~ .player__text {
      margin-left: fn.percent-unit(12);
    }
  }

  .player__text {
    color: palettes.$white;
    font-size: fn.percent-unit(28);
  }

  &.round {
    border-radius: fn.percent-unit(4);
  }

  &.focus {
    background-color: palettes.$white;
    .icon {
      width: fn.percent-unit(32);
      .vie {
        width: fn.percent-unit(32);
        color: palettes.$black;
      }
    }

    .player__text {
      color: palettes.$black;
    }
  }
  &--back {
    .icon {
      @include box.box(
        $width: fn.percent-unit(32),
        $height: fn.percent-unit(27)
      );

      .vie {
        @include box.box(
          $width: fn.percent-unit(32),
          $height: fn.percent-unit(27)
        );
        font-size: fn.percent-unit(32);
      }
    }
  }
}
