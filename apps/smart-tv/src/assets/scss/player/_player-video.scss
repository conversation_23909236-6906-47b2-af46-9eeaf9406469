///
/// #PLAYER CONTROL
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/position" as position;
@use "mixin/layer" as layer;

.player__video {
  & + .player__soft-logo {
    @include layer.layers(layer-1);
  }
  & + .player__control {
    @include layer.layers(layer-2);
  }
}
