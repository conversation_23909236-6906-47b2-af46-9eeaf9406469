///
/// #PLAYER NAVIGATION
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/position" as position;
// @use 'mixin/layer' as layer;

.player-time {
  margin-top: fn.percent-unit(8);
  &__item {
    font-size: fn.percent-unit(18);
    font-weight: 400;
    color: palettes.$white;
    line-height: fn.percent-unit(28);
  }
}
