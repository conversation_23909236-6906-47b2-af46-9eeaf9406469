///
/// #PLAYER NAVIGATION
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/position" as position;
@use "mixin/layer" as layer;

.player-control__highway {
  @include layer.layers(layer-1);
  @include position.absolute(top 0 right 0 bottom 0 left 0);
  @include box.box(100%, 100%);

  &-inner {
    @include box.box(100%, 100%);
    display: flex;
    box-sizing: border-box;
    align-items: center;
    justify-content: center;
  }

  .player-button {
    .icon {
      @include box.box(fn.percent-unit(62));
      .vie {
        font-size: fn.percent-unit(54);
        @include box.box(fn.percent-unit(62));
      }
    }
  }
}
