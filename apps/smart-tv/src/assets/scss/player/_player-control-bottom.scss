///
/// #PLAYER NAVIGATION
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/position" as position;
@use "mixin/layer" as layer;

/// @group components
@use "components/progress/progress" as progress;

.player-control__bottom {
  @include layer.layers(layer-2);
  @include position.absolute(right 0 bottom fn.percent-unit(86) left 0);
  width: 100%;
  height: fn.percent-unit(80);
  &.livetv {
    &::before {
      content: "";
      display: block;
      position: fixed;
      width: fn.percent-unit(1920);
      height: fn.percent-unit(510);
      left: fn.percent-unit(0);
      bottom: 0;
      background: linear-gradient(360deg, #111111 0%, rgba(17, 17, 17, 0) 100%);
      z-index: -1;
    }
  }
  &-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-flow: row wrap;
    box-sizing: border-box;
    .wrapper-seekbar {
      display: flex;
      justify-content: center;
      flex-direction: column;
      margin-top: fn.percent-unit(29);
      flex: 1;
      .wrap-player-time {
        display: flex;
        justify-content: space-between;
      }
    }
    & > * {
      // flex: 1 1 0px;
      flex: 0 0 auto;
      // width: auto;
      &:not(:first-child) {
        margin-left: fn.percent-unit(12px);
      }
      &:not(:last-child) {
        margin-right: fn.percent-unit(12px);
      }
    }
    .shrink {
      flex: 0 0 auto;
      width: auto;
    }
    .auto {
      flex: 1 1 0px;
      width: auto;
    }

    .player-time-box {
      position: absolute;
      font-size: fn.percent-unit(36);
      top: fn.percent-unit(-24);
      left: 50%;
      transform: translate(-50%, -100%);
      opacity: 0;
      transition: opacity 0.3s;
      color: #fff;

      &.show {
        opacity: 1;
      }
    }
  }

  .player-button {
    @include box.box(fn.percent-unit(80));
    border-radius: 100%;
    // background-color: palettes.$white;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
      @include box.box(fn.percent-unit(40));
      .vie {
        font-size: fn.percent-unit(28);
        @include box.box(fn.percent-unit(28));
        // color: palettes.$gray-22;
        color: palettes.$white;
      }
    }
    &--play {
      .icon {
        padding-left: fn.percent-unit(3);
      }
    }

    &.focus {
      background-color: palettes.$white;
      .icon {
        .vie {
          color: palettes.$gray-22;
        }
      }
    }
  }
}

.progress {
  @include progress.progress;
  transform: translateY(fn.percent-unit(-10px));
}
