///
/// #V-PLAYER Styles
///

/// @group setting
@use "../settings/" as sets;
@use "src/assets/scss/settings/function" as fn;

/// @group mixin
@use "../mixin/" as mix;

/// @group v-player style
.v-player {
  width: 100vw;
  height: 100vh;
  z-index: -1;
  position: absolute;
  top: 0;
  $bg-player: rgb(17, 17, 17);
  background-color: $bg-player;

  @keyframes append-animate {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  &.hidden {
    visibility: hidden;
  }

  &.collapse-masthead-ads {
    top: fn.percent-unit(200);
  }

  //
  &-player {
    top: 0px;
    right: 0px;
    width: 100%;
    height: 100%;
    z-index: 1;
    position: absolute;
    pointer-events: none;
    background-color: $bg-player;

    // Animate
    .player-wrap-animation.fade-in {
      height: 100%;
      width: 100%;
      animation: append-animate 0.3s linear;
    }

    &.masterbanner {
      @include mix.box(1400, 787.5);

      &::after {
        @include mix.pseudo($width: 420, $height: 100%, $display: block);
        @include mix.absolute(top 0 left -2px);
        @include mix.scrimGradient(sets.$gray-11, "to right");
      }

      &::before {
        @include mix.pseudo($width: 100%, $height: 143, $display: block);
        @include mix.absolute(bottom -2px left 0);
        z-index: 1;
        @include mix.scrimGradient(sets.$gray-11, "to top");
      }
    }

    &.thumbnail {
      @include mix.box(960, 540);

      &::after {
        @include mix.pseudo($width: 204, $height: 100%, $display: block);
        @include mix.absolute(top 0 left -2px);
        @include mix.scrimGradient(sets.$gray-11, "to right");
      }

      &::before {
        @include mix.pseudo($width: 100%, $height: 143, $display: block);
        @include mix.absolute(bottom -2px left 0);
        z-index: 1;
        @include mix.scrimGradient(sets.$gray-11, "to top");
      }
    }
  }

  //
  &-poster {
    @include mix.absolute(right 0 top 0 z-index 0);
    @include mix.layers(layer-1);
    @include mix.box(1400, 787.5);

    &::before {
      @include mix.pseudo($width: 100vw, $height: 100vh, $display: block);
      @include mix.absolute(right 0 top 0 z-index 0);
      @include mix.layers(layer-1);
      background-color: rgb(17, 17, 17);
    }

    &__img {
      @include mix.box($width: 100%);
      display: block;
    }

    img {
      @include mix.box($width: 100%);
    }

    &.thumbnail {
      @include mix.box(960, 540);
    }

    .v-player-poster__img {
      @include mix.relative;

      &::after {
        @include mix.pseudo($width: 204, $height: 100%, $display: block);
        @include mix.absolute(top 0 left -2px);
        z-index: 2;
        @include mix.scrimGradient(sets.$gray-11, "to right");
      }

      &::before {
        @include mix.pseudo($width: 100%, $height: 143, $display: block);
        @include mix.absolute(bottom -2px left 0);
        @include mix.scrimGradient(sets.$gray-11, "to top");
        z-index: 2;
      }

      .v-player-poster__img-wrapper {
        width: 100%;
        height: 100%;
        overflow: hidden;
        position: relative;
        z-index: 1;

        /* animate */
        &.fade-in {
          animation: append-animate 0.3s linear;
        }
      }
    }

    &.masterbanner {
      .v-player-poster__img {
        &::after {
          @include mix.pseudo($width: 420, $height: 100%, $display: block);
        }
      }
    }

    & + .v-player__info,
    & ~ .v-player__info {
      @include mix.layers(layer-2);
    }
  }

  &--lshape-ads {
    transition: all 0.3s linear;
    &.inactive {
      right: 0;
      width: 100vw;
      height: 100vh;
    }
    &.active {
      right: 0;
      width: calc(100vw * 5 / 6);
      height: calc(100vh * 5 / 6);
    }
  }
}
