///
/// #PLAYER POSTER
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/layer" as layer;
@use "mixin/position" as position;

///
.player__poster {
  @include box.box(100%, 100%);
  @include position.absolute(top 0 right 0 bottom 0 left 0);
  @include layer.layers(layer-1);

  &-inner {
    height: 100%;

    img {
      @include box.box(100%, 100%);
    }
  }
}
