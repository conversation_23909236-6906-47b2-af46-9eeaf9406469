/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ================ PLAYER TIP ================
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* VARIABLE
* INPUT STYLES
*
*/

/// @group setting
@use "../settings/variables" as var;
@use "../settings/function" as fn;
@use "../settings/palettes" as palettes;

/// @group mixin
@use "../mixin/box" as box;
// @use 'mixin/layer' as layer;
@use "../mixin/position" as position;

/// @group style
.player-tip {
  @include box.box($width: fn.percent-unit(556), $height: auto);
  padding: fn.percent-unit(12);

  &__wrap {
    background-color: palettes.$black;
    border: 2px solid palettes.$white;
    border-radius: fn.percent-unit(8);
    box-shadow: 0 fn.percent-unit(10) fn.percent-unit(30)
      rgba(palettes.$gray-29, 0.5);
    min-height: fn.percent-unit(110);
    @include position.relative;

    &::after,
    &::before {
      content: "";
      border-style: solid;
      position: absolute;
      display: none;
    }
    &::after {
      border-width: fn.percent-unit(16);
    }
    &::before {
      border-width: fn.percent-unit(20);
    }
  }

  .player__text {
    color: palettes.$white;
    font-size: fn.percent-unit(24);
  }

  .content-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &.center-bottom {
    @include position.absolute(top 133% left 50%);
    transform: translate(-50%, 0);

    .player-tip__wrap {
      &::after,
      &::before {
        transform: translateX(-50%);
        display: block;
      }

      &::after {
        @include position.absolute(top fn.percent-unit(-35) left 50%);
        border-color: transparent transparent palettes.$black transparent;
        border-width: fn.percent-unit(18);
      }
      &::before {
        @include position.absolute(top fn.percent-unit(-40) left 50%);
        border-color: transparent transparent palettes.$white transparent;
        border-width: fn.percent-unit(20);
      }

      left: 50%;
      bottom: 100%;
      transform: translateX(-50%);
    }
  }

  &.absolute {
    @include position.absolute(top 0 right fn.percent-unit(12));
  }
}
