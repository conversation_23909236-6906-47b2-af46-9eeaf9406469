/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ============== PLAYER CONTROL ==============
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* VARIABLE
* INPUT STYLES
*
*/

/// @group setting
@use "../settings/function" as fn;

/// @group mixin
@use "../mixin/box" as box;
@use "../mixin/layer" as layer;
@use "../mixin/position" as position;
@use "settings/variables" as var;
@use "settings/palettes" as pales;
///
$c-green-3a: #3ac882;

.player-control {
  @include box.box(100%, 100%);
  @include layer.layers(layer-2);
  @include position.absolute(top 0 right 0 bottom 0 left 0);
  padding: fn.percent-unit(80) fn.percent-unit(100);
  box-sizing: border-box;

  &__container {
    @include box.box(100%, 100%);
    display: block;
    position: relative;

    &__channel {
      position: absolute;
      bottom: fn.percent-unit(186);
      left: 0;
      width: fn.percent-unit(100%);
      z-index: 3;
      .channel-info {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        align-items: center;
        width: fn.percent-unit(1720);
        height: fn.percent-unit(164);
        .title {
          color: #ffffff;
          margin-bottom: fn.percent-unit(16);
          font-size: fn.percent-unit(32);
          line-height: fn.percent-unit(42);
          height: fn.percent-unit(84);
          letter-spacing: fn.percent-unit(-0.48);
        }
        &-current,
        &-next {
          width: fn.percent-unit(688);
          height: fn.percent-unit(132);
          border-radius: fn.percent-unit(16);
          padding: fn.percent-unit(16);
          font-size: fn.percent-unit(12);
          color: #000;
          background: rgb(255, 255, 255, 0.1);
          display: -webkit-box;
          display: -ms-flexbox;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          &.error {
            height: fn.percent-unit(84);
          }
          .title {
            height: auto;
            margin: auto 0;
          }
          .tag-channel {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: fn.percent-unit(32);
            font-size: fn.percent-unit(24);
            line-height: fn.percent-unit(32);
            margin: auto 0;

            .left {
              display: -webkit-box;
              display: -ms-flexbox;
              display: flex;
              justify-content: space-between;
              align-items: center;
              > *:not(:last-child) {
                margin-right: fn.percent-unit(12px);
              }
              .replay-icon {
                width: fn.percent-unit(24);
                height: fn.percent-unit(24);
                img {
                  width: 100%;
                  height: 100%;
                  object-fit: contain;
                }
                path {
                  fill: $c-green-3a;
                }
              }
              .bell {
                width: fn.percent-unit(32);
                height: fn.percent-unit(32);
                img {
                  width: 100%;
                  height: 100%;
                  object-fit: contain;
                }
                &.subscribed {
                  fill: #ffffff;
                }
              }
              .tag-channel-active {
                width: fn.percent-unit(32);
                height: fn.percent-unit(32);
                img {
                  width: 100%;
                  height: 100%;
                  object-fit: contain;
                }
              }
              .title--live {
                color: $c-green-3a;
              }
              .time {
                color: #ffffff;
              }
            }
            .right {
              font-size: fn.percent-unit(24);
              color: #ffffff73;
            }
          }
        }
        &-next {
          width: fn.percent-unit(568);
        }
        .break-line {
          height: fn.percent-unit(164);
          width: fn.percent-unit(1);
          background-color: #ffffff73;
          margin: 0 fn.percent-unit(36);
        }
      }
    }
    &__action {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      position: absolute;
      bottom: 0;
      left: 0;
      > *:not(:last-child) {
        margin-right: fn.percent-unit(20);
      }
      z-index: 3;
      .btn {
        &.btn-action {
          width: fn.percent-unit(350);
          height: fn.percent-unit(66);
          border-radius: fn.percent-unit(8);
          background-color: #111111;
          &[data-focus="true"] {
            background-color: #ffffff;
            color: #222222 !important;
          }
        }
      }
    }
  }

  &__wrap {
    display: flex;
    flex-flow: row nowrap;

    .player-control__item {
      flex-wrap: wrap;
    }
  }

  &__item {
    @include position.relative;
  }

  // &__top {
  //   // padding-left: fn.percent-unit(54);
  // }

  &.absolute-full {
    @include position.absolute(top 0 right 0 bottom 0 left 0);
  }

  .cta-button {
    position: absolute;
    top: 79%;
    left: fn.percent-unit(var.$sidebar-max-width);
    font-size: fn.percent-unit(28px);
    padding: fn.percent-unit(12px) fn.percent-unit(28px);
    background: rgba(0, 0, 0, 0.6);
    color: pales.$white;
    border: fn.percent-unit(2px) solid pales.$white;
    border-radius: fn.percent-unit(2px);
    &.flex {
      display: flex;
      .icon {
        width: auto;
        padding-right: fn.percent-unit(9.3px);
      }
    }
    &.focused {
      background-color: pales.$white;
      color: pales.$gray-33;
      .icon .vie {
        color: pales.$gray-33;
      }

      &.live {
        svg.vie-circle-solid {
          color: pales.$red-live;
        }
      }
    }
    &--premieme {
      display: flex;
      background: rgba(34, 34, 34, 0.7);
      border-color: #9b9b9b;
      border-radius: fn.percent-unit(8);
      svg {
        margin-right: fn.percent-unit(16.5px);
        font-size: fn.percent-unit(33px);
        &.vie-arrow-pre-light {
          margin-right: fn.percent-unit(11px);
        }
        &.vie-circle-solid {
          font-size: fn.percent-unit(20px);
        }
      }
      &.focused {
        svg.vie-circle-solid {
          color: pales.$gray-33;
        }
      }
      &.live {
        svg.vie-circle-solid {
          color: pales.$red-live;
        }
      }
    }
  }
}

// .player__control {
//   padding-left: fn.percent-unit(140);
//   padding-top: fn.percent-unit(67);
// }
