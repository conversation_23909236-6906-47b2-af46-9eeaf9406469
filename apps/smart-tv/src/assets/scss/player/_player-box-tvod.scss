/// @group variables
@use "../settings/function" as fn;
/// @group mixin
@use "../mixin/position" as position;

.box-tvod-container {
  @include position.fixed(bottom fn.percent-unit(65) right fn.percent-unit(70));
  z-index: 10;
  &.hidden {
    display: none !important;
  }
  .box-noti {
    width: fit-content;
    background: #111111;
    border: 1px solid #cccccc;
    border-radius: fn.percent-unit(8);
    padding: fn.percent-unit(12) fn.percent-unit(26);
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: center;
    opacity: 0.95;
    max-width: fn.percent-unit(1060);
    p {
      margin: 0 fn.percent-unit(80) 0 0;
      text-shadow: 0px 0px 3px rgba(0, 0, 0, 0.5);
      font-size: fn.percent-unit(32);
      line-height: 1.1;
      color: #fff;
    }

    .btn {
      margin-left: fn.percent-unit(20);
      min-width: fn.percent-unit(178);
      font-size: fn.percent-unit(28);
      font-weight: 500;
      white-space: nowrap;
    }
  }
}
