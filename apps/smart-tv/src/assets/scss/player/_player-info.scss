/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ================ PLAYER INFO ===============
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* SETTING
* MIXIN
* VARIABLE
* STYLES
*
*/

/// @group setting
@use "../settings/function" as fn;
@use "../settings/palettes" as palettes;
@use "../settings/variables" as var;

/// @group mixin
@use "../mixin/box" as box;
@use "../mixin/layer" as layer;
@use "../mixin/position" as position;

///
.player-info {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  $spacing-y: fn.percent-unit(60);
  $spacing-x: fn.percent-unit(var.$sidebar-max-width);

  @include layer.layers(layer-3);
  padding: #{$spacing-y} #{$spacing-x};

  &__wrap {
    width: fn.percent-unit(700);
    padding-top: fn.percent-unit(60);
  }

  .tags-group {
    margin-bottom: fn.percent-unit(30);
    margin-left: 0;
    margin-right: 0;
    // height: fn.percent-unit(44);
  }

  .player__title {
    margin-bottom: fn.percent-unit(26);
    font-size: fn.percent-unit(32);
    height: fn.percent-unit(244);

    & > *:not(:last-child) {
      margin-bottom: fn.percent-unit(16);
    }

    img {
      height: fn.percent-unit(244);
    }

    .text {
      display: block;
      width: 100%;
      color: palettes.$white;
    }
  }

  .title-meta__time-count {
    width: fn.percent-unit(566px);
    font-size: fn.percent-unit(24px);
    // line-height: 1.5;

    display: flex;
    flex-direction: row;
    margin-bottom: fn.percent-unit(10px);
  }

  .player__desc {
    font-size: fn.percent-unit(28);
    color: palettes.$white;
    line-height: 1.25;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  &.align-item-center {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
  }

  &.absolute-full {
    // $box-h: calc(100vw - (#{$spacing-y}*2));
    // $box-w: calc(100vw - (#{$spacing-x}*2));
    @include position.absolute(top 0 right 0 bottom 0 left 0);
    // @include box.box($width: #{$box-w}, $height: #{$box-h});
  }
}
