///
/// #PLAYER SCHEDULES
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/layer" as layer;
@use "mixin/position" as position;

/// @group player-schedules

.player-schedules {
  @include position.absolute(right 0 right 0 bottom 0 left 0);
  @include box.box(100vw, 100vh);
  //background-color: rgba(palettes.$black, 0.9);
  display: flex;
  flex-flow: column;
  // justify-content: center;
  align-items: center;
  padding: fn.percent-unit(0) fn.percent-unit(90);
  box-sizing: border-box;

  &__wrap {
    width: fn.percent-unit(1200);
    margin: 0 auto 0 auto;
    padding-top: fn.percent-unit(60);
    //background-color: palettes.$black;
    min-height: 100vh;
  }

  //
  &-pagination {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    margin: 0 auto fn.percent-unit(60);
    width: fn.percent-unit(950);

    & > * {
      &:not(:first-child) {
        margin-left: fn.percent-unit(12);
      }

      &:not(:last-child) {
        margin-right: fn.percent-unit(12);
      }
    }

    &-container {
      display: flex;
      flex-flow: row nowrap;
      flex: 1 1 0px;
      overflow: hidden;
      width: fn.percent-unit(860);
    }

    &__arrow {
      @include box.box($width: fn.percent-unit(36));
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 0 0 auto;

      .vie {
        @include box.box($width: fn.percent-unit(30));
        color: palettes.$white;
      }
    }

    &__item {
      font-size: fn.percent-unit(32);
      color: palettes.$gray-64;
      flex: 1 1 0px;

      &:not(:first-child) {
        margin-left: fn.percent-unit(12);
      }

      &:not(:last-child) {
        margin-right: fn.percent-unit(12);
      }

      &:first-child {
        margin-left: fn.percent-unit(12);
      }

      &:nth-child(2) {
        text-align: center;
      }

      &:nth-child(3) {
        text-align: right;
      }

      &.active {
        color: palettes.$white;
      }
    }

    &.forPlayer {
      width: fn.percent-unit(100%);
      margin-bottom: fn.percent-unit(34);

      &__item {
        width: fn.percent-unit(183);
        height: fn.percent-unit(36);
      }

      &-container {
        width: fn.percent-unit(623);
      }
    }
  }

  //
  &-list {
    // &__container {
    // }

    &__empty {
      text-align: center;
      padding-top: 25%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      // width: fn.percent-unit(280);
      // height: fn.percent-unit(290);
      img {
        width: fn.percent-unit(280);
        height: fn.percent-unit(290);
      }

      span {
        color: #ffffff;
        font-size: fn.percent-unit(32);
        margin: fn.percent-unit(48px) 0;
      }
    }

    &__item {
      display: flex;
      flex-flow: row nowrap;
      box-sizing: border-box;
      height: fn.percent-unit(176);
      padding: fn.percent-unit(18) fn.percent-unit(126);
      position: relative;

      .player-schedules-list__content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        margin-right: fn.percent-unit(32);

        .epg-title {
          font-size: fn.percent-unit(32);
          line-height: fn.percent-unit(37);
          display: -webkit-box;
          height: fn.percent-unit(74);
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          font-weight: 500;
        }

        .epg-time {
          height: fn.percent-unit(42);
          margin-bottom: fn.percent-unit(8px);
          display: flex;
          align-items: center;
          justify-content: center;

          .tag {
            margin-left: fn.percent-unit(16);
          }
        }
      }

      .player-schedules-list__time,
      .player-schedules-list__thumbnail {
        flex: 0 0 auto;
      }

      .player-schedules-list__replay {
        position: absolute;
        top: 50%;
        right: fn.percent-unit(34);
        transform: translateY(-50%);
      }

      &:not(:first-child) {
        margin-top: fn.percent-unit(10);
      }

      &:not(:last-child) {
        margin-bottom: fn.percent-unit(10);
        border-bottom: 1px solid rgba(#ccc, 0.15);
      }

      &.focus {
        background-color: palettes.$white;

        .player-schedules-list__content,
        .player-schedules-list__time,
        .player-schedules-list__thumbnail,
        .player-schedules-list__replay {
          color: palettes.$gray-22;

          .icon {
            .vie {
              color: palettes.$gray-22;
            }
          }
        }
      }
      &.active {
        background-color: palettes.$gray-64;
      }

      &.forPlayer {
        height: fn.percent-unit(133);
        padding: fn.percent-unit(14) fn.percent-unit(59);
        margin: 0 !important;
      }
    }

    &__time {
      font-size: fn.percent-unit(32);
      line-height: 1.4;
      color: palettes.$white;
      width: fn.percent-unit(78);
    }

    &__thumbnail {
      @include box.box(
        $width: fn.percent-unit(244),
        $height: fn.percent-unit(137)
      );

      img {
        min-width: 100%;
        min-height: 100%;
        max-width: 100%;
        max-height: 100%;
      }
    }

    &__content {
      font-size: fn.percent-unit(32);
      line-height: 1.4;
      color: palettes.$gray-9b;
      width: fn.percent-unit(700);
    }

    &__replay {
      .icon {
        @include box.box($width: fn.percent-unit(42), $height: 100%);
        display: flex;
        align-items: center;
        justify-content: center;

        .vie {
          color: palettes.$white;
          @include box.box($width: fn.percent-unit(42));
          font-size: fn.percent-unit(42);
          transition: none;
        }
      }
    }
  }
}

.forPlayer {
  &.player-schedules {
    top: 0;
    width: fn.percent-unit(100%);
    padding: fn.percent-unit(0);

    &__wrap {
      width: fn.percent-unit(100%);
      padding-top: fn.percent-unit(26);
    }

    &-pagination {
      width: fn.percent-unit(678);

      &__item {
        flex: none;
        margin-left: fn.percent-unit(0);
        font-size: fn.percent-unit(24);
        line-height: fn.percent-unit(36);
        font-weight: 700;
      }

      &-container {
        justify-content: space-between;
        //flex: none;
        //justify-content: space-between;
        width: fn.percent-unit(623);
      }
    }
  }

  &.player-schedules-list {
    &__container {
      width: fn.percent-unit(100%);
    }

    &__content {
      color: palettes.$gray-cc;

      .epg-title {
        font-size: fn.percent-unit(24);
        line-height: fn.percent-unit(36);
        font-weight: 500;
      }

      .epg-time {
        margin-bottom: 0;
        font-size: fn.percent-unit(20);
      }
    }
  }
}
