///
/// #PLAYER NAVIGATION
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/position" as position;
@use "mixin/layer" as layer;

.player-nav {
  display: flex;
  flex-flow: row wrap;

  &__item {
    font-size: fn.percent-unit(28);
    color: palettes.$white;
    padding: fn.percent-unit(12) fn.percent-unit(20);
    display: flex;
    border-radius: 4px;

    .icon {
      margin-right: fn.percent-unit(12);
      .vie {
        color: palettes.$white;
      }
    }

    &.active,
    &.focus {
      background-color: palettes.$white;
      color: palettes.$gray-11;
      .icon .vie {
        color: palettes.$gray-11;
      }
    }
  }
}
