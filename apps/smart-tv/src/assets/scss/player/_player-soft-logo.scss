///
/// #PLAYER CONTROL
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/position" as position;
@use "mixin/layer" as layer;

.player__soft-logo {
  @include box.box(100vw, 100vh);
  @include position.absolute(top 0 right 0 bottom 0 left 0);
  @include layer.layers(layer-1);
  display: none;

  &__inner {
    display: block;
    @include box.box(100%, 100%);
  }

  & + .player__control {
    @include layer.layers(layer-2);
  }
}
