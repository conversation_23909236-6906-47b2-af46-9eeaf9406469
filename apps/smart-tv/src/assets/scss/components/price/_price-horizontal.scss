/// @param settings
@use "settings/function" as fn;

/// @group mixin

///
/// @group Price
.price--horizontal {
  @at-root .price#{&} {
    display: flex;
    align-items: center;
    .price__item {
      box-sizing: border-box;
      display: flex;
      flex: 0 0 auto;
      // width: fn.percent-unit(94);
      height: fn.percent-unit(40);
      & > svg {
        width: 100%;
        height: 100%;
      }

      &:not(:last-child) {
        margin-right: fn.percent-unit(20);
      }
    }
  }
}
