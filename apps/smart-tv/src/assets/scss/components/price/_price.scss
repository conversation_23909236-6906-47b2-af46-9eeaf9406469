/// @param settings
@use "settings/palettes" as pales;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
// @use 'mixin/position' as posit;
// @use 'mixin/pseudo' as pseudo;

///
/// @group Price
.price {
  &__item {
    &-pay {
      @include fonts.font-family("roboto-medium");
      @include fonts.font-size(36);
      color: pales.$green-3a;
      line-height: 1.4;
    }

    &-discount {
      line-height: normal;
      width: fn.percent-unit(94);
      text {
        @include fonts.font-family("roboto-medium");
        // @include fonts.font-size(24);
        font-size: 24px;
      }
    }
  }
  &__currency {
    padding-left: fn.percent-unit(4);
  }
}

.price--total {
  @at-root .price#{&} {
    display: flex;
    align-items: center;
    .price__item {
      &:not(.price__item-label) {
        color: fn.el-color($color: v-medium-sea-green, $shade: 149);
        text-align: right;
        @include fonts.font-family("roboto-medium");
        @include fonts.font-size(48);
        .price__unit {
          @include fonts.font-size(36);
          &.full {
            @include fonts.font-size(48);
          }
        }
      }

      // .price__unit{

      // }

      &-label {
        color: fn.el-color($color: v-black, $shade: base);
        // @include fonts.font-family("roboto-black");
        @include fonts.font-size(28);
        font-weight: 700;
      }
    }
  }
}
