/// @group setting
// @use 'settings/variables' as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/position" as position;

///
.list {
  &:not(:last-child) {
    margin-bottom: fn.percent-unit(20);
  }
}

.list--type-number {
  @at-root .list#{&} {
    counter-reset: item;
    list-style: decimal;

    .list__item {
      padding-left: fn.percent-unit(56);
      position: relative;
      line-height: 1.25;
      &:not(:last-child) {
        margin-bottom: fn.percent-unit(20);
      }
      &::before {
        content: counters(item, ".") ". ";
        counter-increment: item;
        @include position.absolute(top 0 left 0);
      }
      &.non-dot {
        &::before {
          content: counters(item, "") " ";
          counter-increment: item;
          padding-right: fn.percent-unit(32);
        }
        &.text-child-large {
          &::before {
            top: fn.percent-unit(38);
          }
        }
      }

      &.text-child-large {
        span {
          font-size: fn.percent-unit(72);
        }
      }
    }
  }
}
