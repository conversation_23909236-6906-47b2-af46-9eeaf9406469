/// @param settings
@use "settings/palettes" as pales;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/position" as posit;

/// @group List
.list--pack {
  @at-root .list#{&} {
    box-sizing: border-box;
    .list__item {
      padding-top: fn.percent-unit(9);
      padding-bottom: fn.percent-unit(9);
      position: relative;

      .icon {
        @include box.box(
          $width: fn.percent-unit(32),
          $height: fn.percent-unit(32)
        );

        &.absolute {
          @include posit.absolute(top 50% left 0);
          transform: translateY(-50%);
        }

        & + .text {
          padding-left: fn.percent-unit(44);
        }
      }
      .text {
        @include fonts.font-size(18);
        color: pales.$white;
      }
    }
  }
}
