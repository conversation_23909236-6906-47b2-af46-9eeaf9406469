////
/// @group rate
////

/// @group setting
///

/// @param palettes
@use "settings/palettes" as palettes;

/// @param variables
@use "settings/variables" as var;

/// @param function
@use "settings/function" as fn;

/// @mixin rate

@mixin rating() {
  display: flex;
  & > * {
    &:not(:last-child) {
      margin-right: fn.percent-unit(12);
    }
  }
  &__summary {
    font-size: fn.percent-unit(28);
    color: #cccccc;
  }

  &__star {
    display: flex;
    & > * {
      &:not(:last-child) {
        margin-right: fn.percent-unit(4);
      }
    }
    .vie {
      color: #da9e1c;
      width: fn.percent-unit(24);
      height: fn.percent-unit(24);
      font-size: fn.percent-unit(24);
    }
  }
}

/// @style rating
.rating {
  @include rating;
}
