/**
* ============ VERSION ============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ============== CARD CANDIDATE ==============
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* SETTING
* MIXIN
* VARIABLE
* STYLES
*
*/

/// @group setting
@use "../../settings/function" as fn;
@use "../../settings/palettes" as palette;

/// @group mixin
@use "../../mixin/box" as box;
@use "../../mixin/position" as position;
@use "../../mixin/pseudo" as pseudo;

.vote {
  &-group {
    display: flex;

    // & > .vote {
    // }

    // this update next version
    // &.vertical {
    //   & > .vote {
    //   }
    // }

    // &.horizontal {
    // }

    &.justify-content-center {
      justify-content: center;
    }
  }
  &.divide-vertical {
    @include position.relative;
    padding-left: fn.percent-unit(20);
    padding-right: fn.percent-unit(20);

    &:not(:last-child) {
      &:before {
        @include pseudo.pseudo(
          $width: fn.percent-unit(2),
          $height: fn.percent-unit(28)
        );
        @include position.absolute(top 50% right 0);
        transform: translateY(-50%);
      }
    }
  }
  // &.divide-horizontal {
  //   @include position.relative;

  //   &:not(:last-child) {
  //     &:before {
  //       @include pseudo.pseudo($width: fn.percent-unit(2), $height: fn.percent-unit(38));
  //       @include position.absolute(top 50% right 0);
  //     }
  //   }
  // }
}
