/// @group setting
@use "src/assets/scss/settings/function" as fn;
@use "../../settings/palettes" as palets;

.ads-overflow {
  overflow: hidden;
}

.wrapper-ads {
  position: relative;
  transition: all 0.3s linear;

  .counter {
    position: absolute;
    left: fn.percent-unit(60);
    bottom: fn.percent-unit(60);
    background-color: rgb(117, 116, 116);
    color: #ffffff;
    padding: 10px;
    border-radius: 50%;
    font-size: fn.percent-unit(28);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .skip {
    position: absolute;
    right: 3%;
    bottom: 5%;
  }

  &--welcome,
  &--fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;

    &.alert-enter,
    &.alert-appear {
      opacity: 0;

      .popup__inner {
        transform: scale(0.9);
      }
    }

    &.alert-enter-active,
    &.alert-appear-active {
      opacity: 1;
      transition: opacity 200ms;

      .popup__inner {
        transform: scale(1);
        transition: transform 200ms;
      }
    }

    &.alert-exit {
      opacity: 1;
    }

    &.alert-exit-active {
      opacity: 0;
      transition: opacity 200ms;

      .popup__inner {
        transform: scale(0.9);
        transition: transform 200ms;
      }
    }

    &.alert-exited {
      opacity: 0;
    }
  }

  &--endscreen {
    width: 100%;
    height: 100%;
  }

  &.hidden {
    visibility: hidden;
  }

  .btn--close {
    box-shadow: 1px 6px 20px rgba(0, 0, 0, 0.5);
  }
}
