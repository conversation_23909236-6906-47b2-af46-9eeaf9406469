@use "../../settings/function" as fn;
@use "../../settings/palettes" as palettes;

.toast-message {
  display: block;
  position: fixed;
  bottom: fn.percent-unit(60px);
  left: fn.percent-unit(40px);
  font-size: fn.percent-unit(28px);
  line-height: 1.66;
  width: fn.percent-unit(662px);
  z-index: 100000;
  background: palettes.$gray-33;
  color: #fff;
  &::before {
    content: "";
    display: block;
    position: absolute;
    width: fn.percent-unit(6px);
    height: 100%;
    border-radius: fn.percent-unit(4px) 0px 0px fn.percent-unit(4px);
    background-color: palettes.$green-3a;
  }
  &--inner {
    padding: fn.percent-unit(16px) fn.percent-unit(23px) fn.percent-unit(16px)
      fn.percent-unit(120px);
    min-height: fn.percent-unit(120px);
    position: relative;
    display: flex;
    align-items: center;
    b {
      font-weight: bold;
      display: inline;
    }
    &::before {
      content: "";
      // background-image: url("/src/assets/images/icon/bell.svg");
      background-image: url(../../../images/icon/bell.svg);
      background-size: cover;
      position: absolute;
      font-size: fn.percent-unit(48px);
      width: fn.percent-unit(48px);
      height: fn.percent-unit(48px);
      left: fn.percent-unit(36px);
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
// RC Notification Animation
.rc-notification {
  position: absolute;
  z-index: 99999;
  top: 0;
  left: 0;
  width: 100%;
  .rc-notification-notice {
    width: 100%;
  }
}
.slide-right-enter,
.slide-right-appear {
  .toast-message {
    transform: translateX(-120%);
    animation-duration: 0.25s;
    animation-fill-mode: both;
    animation-timing-function: ease-in;
    animation-play-state: paused;
  }
}
.slide-right-leave {
  .toast-message {
    animation-duration: 0.25s;
    animation-fill-mode: both;
    animation-timing-function: ease-out;
    animation-play-state: paused;
  }
}
.slide-right-enter.slide-right-enter-active,
.slide-right-appear.slide-right-appear-active {
  .toast-message {
    animation-name: slideInRight;
    animation-play-state: running;
  }
}
.slide-right-leave.slide-right-leave-active {
  .toast-message {
    animation-name: slideOutRight;
    animation-play-state: running;
  }
}

@keyframes slideInRight {
  0% {
    transform: translateX(-120%);
  }
  100% {
    transform: translateX(0%);
  }
}

@keyframes slideOutRight {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-120%);
  }
}

.slide-left {
  .toast-message {
    width: fn.percent-unit(564px);
    top: fn.percent-unit(63px);
    right: 0;
    left: auto;
    bottom: auto;
    transform: translateX(120%);
    animation-duration: 0.25s;
    animation-fill-mode: both;
    animation-timing-function: ease-in;
    animation-play-state: paused;
    &--inner {
      padding-top: fn.percent-unit(18px);
      padding-bottom: fn.percent-unit(18px);
      padding-left: fn.percent-unit(38px);
      padding-right: fn.percent-unit(68px);
      min-height: auto; // fn.percent-unit(120px);
      &::before {
        content: "";
        background-image: none;
        background-size: cover;
        position: absolute;
        font-size: fn.percent-unit(65px);
        width: fn.percent-unit(65px);
        height: fn.percent-unit(65px);
        left: fn.percent-unit(36px);
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}

.slide-left-enter,
.slide-left-appear {
  .toast-message {
    transform: translateX(120%);
    animation-duration: 0.25s;
    animation-fill-mode: both;
    animation-timing-function: ease-in;
    animation-play-state: paused;
  }
}
.slide-left-leave {
  .toast-message {
    animation-duration: 0.25s;
    animation-fill-mode: both;
    animation-timing-function: ease-out;
    animation-play-state: paused;
  }
}
.slide-left-enter.slide-left-enter-active,
.slide-left-appear.slide-left-appear-active {
  .toast-message {
    animation-name: slideInLeftToast;
    animation-play-state: running;
  }
}
.slide-left-leave.slide-left-leave-active {
  .toast-message {
    animation-name: slideOutLeftToast;
    animation-play-state: running;
  }
}

@keyframes slideInLeftToast {
  0% {
    transform: translateX(120%);
  }
  100% {
    transform: translateX(0%);
  }
}

@keyframes slideOutLeftToast {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(120%);
  }
}
