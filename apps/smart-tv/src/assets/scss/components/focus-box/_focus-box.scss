@use "../../settings/function" as fn;
@use "../../settings/palettes" as palettes;
/// @group mixin
@use "mixin/pseudo" as pseudo;
/// @param palettes
@use "settings/palettes" as pales;

.focus-box {
  position: absolute;
  z-index: 8;
  pointer-events: none;
  .info-box {
    pointer-events: none;
  }
  .info-box.info-box--category,
  .info-box.info-box--collections {
    position: absolute;
    top: 0;
    left: 0;
    width: fn.percent-unit(1470px);
    height: fn.percent-unit(501);
    opacity: 0;
    display: flex;
    .info-box__wrap-box {
      width: 100%;
      height: 100%;
      position: relative;
      .info-box__thumbnail {
        width: 100%;
        height: 100%;
        & > img {
          height: 100%;
          width: auto;
        }
        .clip {
          position: absolute;
          clip-path: polygon(0 0, 96% 0, 100% 100%, 0 100%);
          overflow: hidden;
          &.fade-enter {
            opacity: 0;
          }
          &.fade-exit {
            opacity: 1;
          }
          &.fade-enter-active {
            opacity: 1;
          }
          &.fade-exit-active {
            opacity: 0;
          }
          &.fade-enter-active,
          &.fade-exit-active {
            transition: opacity 500ms;
          }
        }
      }
      .mask {
        height: fn.percent-unit(497px);
        width: fn.percent-unit(616px);
        background-color: palettes.$white;
        position: absolute;
        top: fn.percent-unit(2px);
        right: 0;
        // left: fn.percent-unit(467px);
        z-index: -1;
      }
      .info-box__box {
        position: absolute;
        top: 0;
        left: fn.percent-unit(892px);
        padding: 0 fn.percent-unit(14px);
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .info-box__heading {
          font-size: fn.percent-unit(48px);
          font-weight: 500;
          margin-left: fn.percent-unit(16px);
        }
      }
    }
  }
  .info-box.info-box--poster,
  .info-box.info-box--livetv,
  .info-box.info-box--epg,
  .info-box.info-box--live-stream,
  .info-box.info-box--watch-later,
  .info-box.info-box--watch-more,
  .info-box.info-box--coming-soon {
    position: absolute;
    top: 0;
    left: 0;
    width: fn.percent-unit(1470px);
    height: fn.percent-unit(501px);
    opacity: 0;
    display: flex;
    .info-box__wrap-box {
      width: 100%;
      height: 100%;
      position: relative;
      .info-box__thumbnail {
        width: fn.percent-unit(893px);
        height: 100%;
        & > img {
          width: fn.percent-unit(893px);
          height: 100%;
        }
        .clip {
          position: absolute;
          clip-path: polygon(0 0, 96% 0, 100% 100%, 0 100%);
          overflow: hidden;
        }
      }

      .mask {
        height: fn.percent-unit(497px);
        width: fn.percent-unit(614px);
        background-color: palettes.$white;
        position: absolute;
        top: fn.percent-unit(2px);
        right: 0;
        // left: fn.percent-unit(467px);
        z-index: -1;
        clip-path: polygon(0 0, 100% 0, 100% 100%, fn.percent-unit(36px) 100%);
      }
      .info-box__box {
        position: absolute;
        top: 0;
        left: fn.percent-unit(892px);
        padding: 0 fn.percent-unit(14px);
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .info-box__title-card {
          height: fn.percent-unit(157px);
          max-width: fn.percent-unit(500px);
          margin-bottom: fn.percent-unit(24px);
          & > img {
            height: 100%;
            max-width: 100%;
          }
        }
        .info-box__meta {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-wrap: wrap;
          margin-bottom: fn.percent-unit(16px);
          font-size: fn.percent-unit(20px);
          .tag {
            padding: 0 fn.percent-unit(8px);
            &:not(:last-child) {
              border-right: 1px solid palettes.$gray-11;
            }
            &:first-child {
              padding-left: 0;
            }
            &.tag--vip {
              height: fn.percent-unit(21px);
              padding-right: 0;
              border-right: none;
              & > img {
                height: 100%;
                width: auto;
              }
            }
          }
        }
        .info-box__ranking {
          height: fn.percent-unit(32px);
          display: flex;
          align-items: center;
          margin-bottom: fn.percent-unit(16px);
          font-size: fn.percent-unit(24px);
          font-weight: 400;
          .tag-icon {
            height: fn.percent-unit(32px);
            width: fn.percent-unit(32px);
            margin-right: fn.percent-unit(16px);
            .vie {
              height: 100%;
              width: 100%;
              color: palettes.$green-3a;
            }
          }
        }
        .info-box__description {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 3;
          font-size: fn.percent-unit(20px);
          height: fn.percent-unit(90px);
          margin-bottom: fn.percent-unit(16px);
          line-height: 1.5;
        }
        .info-box__award {
          display: flex;
          align-items: center;
          font-size: fn.percent-unit(20px);
          padding-top: fn.percent-unit(16px);
          border-top: 1px solid palettes.$gray-cc;
          .award-icon {
            height: fn.percent-unit(50px);
            width: fn.percent-unit(50px);
            .vie {
              height: 100%;
              width: 100%;
              color: palettes.$green-3a;
            }
          }
          .award-text {
            width: 100%;
          }
        }
      }
    }
  }
  .info-box.info-box--coming-soon,
  .info-box.info-box--epg.info-box--epg-coming-soon,
  .info-box.info-box--live-stream.info-box--live-stream-coming-soon {
    .info-box__wrap-box {
      color: palettes.$white;
      .mask {
        background-color: palettes.$gray-33;
      }
      .info-box__box {
        .info-box__meta {
          .tag:not(:last-child) {
            border-right: 1px solid palettes.$white;
          }
        }
      }
      .info-box__button {
        .btn {
          width: fn.percent-unit(290px);
          height: fn.percent-unit(62px);
        }
        &.loading {
          opacity: 0.5;
        }
      }
      .info-box__release-day {
        font-size: fn.percent-unit(24px);
        line-height: 1.5;
        font-weight: 700;
      }
    }
  }
  .info-box.info-box--watch-more {
    .watch-more__description {
      font-size: fn.percent-unit(20px);
      margin-bottom: fn.percent-unit(24px);
      &--title {
        font-size: fn.percent-unit(28px);
        margin-bottom: fn.percent-unit(24px);
      }
    }
    .watch-more__progress {
      position: relative;
      height: fn.percent-unit(8px);
      background: #cccccc;
      width: 100%;
      min-width: fn.percent-unit(550px);
      margin-bottom: fn.percent-unit(24px);
      &--active {
        position: absolute;
        top: 0;
        left: 0;
        height: fn.percent-unit(8px);
        background: #3ac882;
      }
    }
  }
  .info-box {
    .info-box__box {
      .info-box__programme-title {
        font-size: fn.percent-unit(48px);
        font-weight: 500;
        line-height: 1.3;
        margin-bottom: fn.percent-unit(28px);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 3;
      }
      .info-box__programme-time {
        font-size: fn.percent-unit(24px);
        line-height: 1.5;
        align-items: center;
        & > img {
          margin-right: fn.percent-unit(14px);
          height: fn.percent-unit(32px);
          width: auto;
        }
        &:not(:last-child) {
          margin-bottom: fn.percent-unit(32px);
        }
        .text-big {
          font-size: fn.percent-unit(28px);
        }
      }
    }
  }
  .info-box--livetv,
  .info-box.info-box--epg,
  .info-box.info-box--live-stream {
    .info-box__box {
      .info-box__programme-time {
        display: flex;
      }
    }
  }
  .info-box.info-box--epg
    .info-box__wrap-box
    .info-box__box
    .info-box__title-card {
    height: fn.percent-unit(67px);
  }
}

.focus-box {
  &.card.card--top-views {
    width: 12.3958333333vw;
    height: 17.7083333333vw;
  }
}

.CardFocusBox {
  left: fn.percent-unit(-10);
  bottom: fn.percent-unit(-10);
  padding: fn.percent-unit(4);
}
