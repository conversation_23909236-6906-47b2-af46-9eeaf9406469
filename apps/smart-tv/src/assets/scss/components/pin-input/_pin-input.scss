@use "../../settings/function" as fn;

.pincode-input-container {
  display: flex;
  box-sizing: border-box;
  & *,
  & *::before,
  & *::after {
    box-sizing: border-box;
  }

  .pincode-input-label {
    position: relative;
    display: block;
    width: fn.percent-unit(76);
    height: fn.percent-unit(76);

    &.pincode-input-label--empty {
      &:before {
        opacity: 1;
      }
    }

    &:before {
      content: "";
      display: block;
      position: absolute;
      opacity: 0;
      z-index: 10;
      pointer-events: none;
      width: fn.percent-unit(32);
      height: fn.percent-unit(32);
      border-radius: 50%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border: solid 2px #9b9b9b;
    }

    &:not(:last-child) {
      margin-right: fn.percent-unit(16) !important;
    }
  }
  .pincode-input-text {
    padding: 0 !important;
    text-align: center !important;
    border: 2px #646464 solid !important;
    border-radius: fn.percent-unit(4);
    background: #222222 !important;
    width: 100% !important;
    height: 100% !important;
    transition: none;
    font-size: fn.percent-unit(32);
    font-weight: 500;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;

    &:focus {
      outline: none !important;
      box-shadow: none !important;
    }
  }
}
