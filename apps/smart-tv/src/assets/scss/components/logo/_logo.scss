////
/// @group logo
////

/// @use variables
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// use mixin
@use "mixin/box" as box;
@use "mixin/position" as position;
@use "mixin/layer" as layer;

/// mixin logo
///

@mixin logo($dir, $width: 85px, $height: 27px) {
  @if $dir == "default" {
    // width: fn.percent-unit($width);
    // height: fn.percent-unit($height);
    display: flex;
    align-items: center;

    img {
      height: auto;
      max-width: 100%;
      // max-height: 100%;
    }
  } @else if $dir == "sidebar" {
    // width: fn.percent-unit($width);
    // height: fn.percent-unit($height);
  }
}

/// style for logo
.logo {
  @include logo($dir: default);

  &--top {
    @include position.fixed;
    @include layer.layers(layer-19);
    width: auto;
    height: fn.percent-unit(60px);
    margin-bottom: fn.percent-unit(44px);
    // display: flex;
    // align-items: flex-end;
    top: fn.percent-unit(40px);
    left: fn.percent-unit(30px);

    img {
      width: fn.percent-unit(var.$logo-max-width);
    }

    &.masthead-ads {
      display: flex;
      flex-direction: column;

      .cate-logo {
        border-left: none;
        padding-left: 0;
        margin-top: fn.percent-unit(20);
        margin-left: 0;
      }
    }

    & + .main-container > .main,
    & ~ .main-container > .main {
      .main__header {
        padding-left: fn.percent-unit(var.$logo-max-width + 76);
      }
    }

    & + .main,
    & ~ .main {
      .main__header {
        padding-left: fn.percent-unit(var.$logo-max-width + 76);
      }
    }
  }

  &--bottom {
    @include position.absolute;
    @include layer.layers(layer-19);
    width: auto;
    height: fn.percent-unit(60px);
    margin-bottom: fn.percent-unit(44px);
    display: flex;
    align-items: flex-end;
    bottom: fn.percent-unit(40px);

    img {
      width: fn.percent-unit(var.$logo-max-width);
    }

    &-left {
      left: fn.percent-unit(40px);
    }

    &-right {
      right: fn.percent-unit(40px);
    }
  }

  &--payment {
    @include box.box(
      $width: fn.percent-unit(152),
      $height: fn.percent-unit(48)
    );
    @include position.absolute(
      top fn.percent-unit(26) left fn.percent-unit(104)
    );

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
}

.cate-logo {
  padding-left: fn.percent-unit(24);
  margin-left: fn.percent-unit(24);
  height: 100%;
  display: flex;
  align-items: center;
  border-left: 1px solid palettes.$white;

  img {
    width: fn.percent-unit(130);
  }
}
