/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ================== PARTNER =================
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* STYLES
*
*/
/// @param settings
@use "settings/function" as fn;
@use "settings/variables" as var;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;

/// @group style

.logo--partner {
  @at-root .logo#{&} {
    @include box.box($width: 100%);
    text-align: center;

    img {
      @include box.box($width: auto);
      max-width: 100%;
    }
  }
}

.logo--partner-payment {
  @at-root .logo#{&} {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: fn.percent-unit(291px);
    margin-right: auto;
    margin-left: auto;
  }

  &.logo--partner-payment-shopeepay {
    width: fn.percent-unit(220px);
  }
}

.moca {
  img {
    filter: drop-shadow(0px 1px 24px rgba(0, 0, 0, 0.35));
  }
}

.logo--partner-bank {
  @at-root .logo--partner#{&} {
    width: fn.percent-unit(80px);
    height: fn.percent-unit(39px);
    border-radius: fn.percent-unit(4);
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    // padding: fn.percent-unit(6);
  }
}

.logo--partner-grocery {
  @at-root .logo--partner#{&} {
    width: fn.percent-unit(133px);
    height: fn.percent-unit(65px);
    border-radius: fn.percent-unit(6);
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    margin-left: auto;
    margin-right: auto;
  }
}

.logo--partner-sms {
  @at-root .logo--partner#{&} {
    margin-left: auto;
    margin-right: auto;
    max-width: fn.percent-unit(228);
    max-height: fn.percent-unit(44);
  }
}
