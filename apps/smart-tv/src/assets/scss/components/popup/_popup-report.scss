////
/// @group nav
////

/// @param palettes
@use "settings/palettes" as palettes;

/// @group variables
@use "settings/variables" as var;

/// @group function
@use "settings/function" as fn;

/// @group mixin
@use "mixin/position" as position;
@use "mixin/font-size" as font-size;
@use "mixin/pseudo" as pseudo;
@use "mixin/layer" as layer;

/// @group animated
@use "../../animates/fade-exits/fadeOutLeft" as fol; //use fade out left animates
@use "../../animates/fade-exits/fadeOutRight" as for; //use fade out left right
@use "../../animates/slides/slideInLeft" as slideInLeft; //use fade out left right

.popup--report {
  @at-root .popup#{&} {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-direction: column;
    background-color: rgba(0, 0, 0, 0.85);
    .popup__inner {
      width: fn.percent-unit(1127px);
      position: relative;
      &::before {
        @include pseudo.pseudo($height: 5px);
        position: absolute;
        transform: translateY(-100%);
        left: 0;
        top: 0;
        background: linear-gradient(90deg, #85fc6e 0%, #0ad418 100%);
        border-radius: fn.percent-unit(4px) fn.percent-unit(4px) 0 0;
      }
    }
    .popup__header {
      height: fn.percent-unit(300px);
      position: relative;
    }
    .popup__body {
      background: #333;
      transition: all 1s;
      .inner {
        box-sizing: border-box;
        padding: fn.percent-unit(30px);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transition: transform 0.3s;
      }
    }
    .popup__title {
      font-size: fn.percent-unit(36px);
      line-height: 1.4;
      font-weight: 500;
      margin-bottom: fn.percent-unit(5px);
      color: palettes.$white;
    }

    .popup__description {
      font-size: fn.percent-unit(24px);
      line-height: 1.5;
      color: palettes.$gray-9b;
      text-align: center;
      margin-bottom: fn.percent-unit(53px);
      &.text-left {
        text-align: left;
      }
      &.text-right {
        text-align: right;
      }
      &.text-justify {
        text-align: justify;
      }
      &.text-center {
        text-align: center;
      }
    }

    .popup__list-item {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;

      .popup__item {
        display: flex;
        flex-direction: row;
        width: 50%;
        color: palettes.$white;
        font-weight: normal;
        margin: fn.percent-unit(10px) 0;
        padding: fn.percent-unit(13px) fn.percent-unit(20px);
        box-sizing: border-box;

        &.focus {
          color: palettes.$gray-22;
          background: palettes.$white;
          box-shadow: fn.percent-unit(4px) fn.percent-unit(4px)
            fn.percent-unit(8px) rgba(0, 0, 0, 0.56);
          border-radius: fn.percent-unit(2px);
        }

        &--title {
          font-size: fn.percent-unit(28px);
          line-height: 150%;
        }

        &--description {
          font-size: fn.percent-unit(24px);
          line-height: 150%;
          opacity: 0.7;
          max-height: fn.percent-unit(72px);
          overflow: hidden;
        }

        &--checkbox {
          margin-right: fn.percent-unit(16px);
        }
      }
    }

    .popup__input-text {
      width: fn.percent-unit(820px);
      font-size: fn.percent-unit(28px);
      line-height: 150%;
      textarea {
        color: palettes.$white;
      }
      &--desc {
        text-align: center;
        color: palettes.$white;
      }
    }
    .popup__button-group {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .popup__inner.keyboard {
      &::after {
        position: absolute;
        left: 0;
        top: 0;
        background: linear-gradient(
          0deg,
          rgba(51, 51, 51, 0) 13.56%,
          #333333 100%
        );
        height: fn.percent-unit(209);
        content: "";
        width: 100%;
      }
      .popup__body {
        height: fn.percent-unit(600px);
        overflow-y: scroll;
      }
    }

    .keyboard-layout {
      width: 100%;
      background-color: #111111;

      .keyboard-container {
        background: #181818;
        padding: 1.6666666667vw;
        box-sizing: border-box;
        width: fn.percent-unit(818px);
        margin: 0 auto;
      }
    }
  }
}
