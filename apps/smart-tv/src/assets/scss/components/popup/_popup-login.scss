////
/// @group nav
////

/// @param palettes
@use "settings/palettes" as palettes;

/// @group variables
@use "settings/variables" as var;

/// @group function
@use "settings/function" as fn;

/// @group mixin
@use "mixin/position" as position;
@use "mixin/font-size" as font-size;
@use "mixin/pseudo" as pseudo;
@use "mixin/layer" as layer;

/// @group animated
@use "../../animates/fade-exits/fadeOutLeft" as fol; //use fade out left animates
@use "../../animates/fade-exits/fadeOutRight" as for; //use fade out left right
@use "../../animates/slides/slideInLeft" as slideInLeft; //use fade out left right

.popup--login {
  @at-root .popup#{&} {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.85);
    &.popup--success {
      .popup__inner::after {
        @include pseudo.pseudo($height: 5px);
        position: absolute;
        transform: translateY(-100%);
        left: 0;
        top: 0;
        background: linear-gradient(90deg, #85fc6e 0%, #0ad418 100%);
        border-radius: fn.percent-unit(4px) fn.percent-unit(4px) 0 0;
      }
    }
    &.popup--warning {
      .popup__inner::after {
        @include pseudo.pseudo($height: 5px);
        position: absolute;
        transform: translateY(-100%);
        left: 0;
        top: 0;
        background: palettes.$warning;
        border-radius: fn.percent-unit(4px) fn.percent-unit(4px) 0 0;
      }
    }
    &.popup--conditional {
      padding-top: fn.percent-unit(28px);
      .popup__button-group {
        flex-direction: column;
        width: 100%;
        .btn {
          width: 100%;
          margin-top: 0;
          margin-right: 0;
          margin-bottom: fn.percent-unit(24px);
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
      .conditional-text {
        height: fn.percent-unit(36px);
        color: #dedede;
        text-align: center;
        font-size: fn.percent-unit(24px);
        line-height: 1.5;
        margin-bottom: fn.percent-unit(24px);
      }
    }
    .popup__inner {
      width: fn.percent-unit(700px);
      position: relative;
    }
    .popup__header {
      height: fn.percent-unit(300px);
      position: relative;
      .mask {
        width: 100%;
        height: 100%;
        z-index: 1;
      }
      .img {
        position: absolute;
        left: 50%;
        z-index: 9;
        &--center {
          top: 50%;
          transform: translate(-50%, -50%);
        }
        &--bottom {
          bottom: fn.percent-unit(-4px);
          transform: translateX(-50%);
          width: 100%;
        }
        &--lottie {
          width: fn.percent-unit(200px);
          height: fn.percent-unit(200px);
        }
        & > img {
          width: 100%;
        }
      }
    }
    .popup__body {
      background: #333;
      .inner {
        box-sizing: border-box;
        padding: fn.percent-unit(44px);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
    }
    .popup__title {
      font-size: fn.percent-unit(36px);
      line-height: 1.4;
      font-weight: 500;
      margin-bottom: fn.percent-unit(24px);
      color: palettes.$white;
      &.text-left {
        text-align: left;
      }
      &.text-right {
        text-align: right;
      }
      &.text-justify {
        text-align: justify;
      }
      &.text-center {
        text-align: center;
      }
    }

    .popup__description {
      font-size: fn.percent-unit(24px);
      line-height: 1.5;
      color: palettes.$gray-9b;
      text-align: center;
      margin-bottom: fn.percent-unit(24px);
      font-weight: 500;
      &.text-left {
        text-align: left;
      }
      &.text-right {
        text-align: right;
      }
      &.text-justify {
        text-align: justify;
      }
      &.text-center {
        text-align: center;
      }
    }
    .popup__button-group {
      display: flex;
      justify-content: center;
      align-items: center;

      .btn {
        padding-left: fn.percent-unit(17px);
        padding-right: fn.percent-unit(17px);
        font-weight: 500;
      }
    }
  }
  &.alert-enter,
  &.alert-appear {
    opacity: 0;
    .popup__inner {
      transform: scale(0.9);
    }
  }
  &.alert-enter-active,
  &.alert-appear-active {
    opacity: 1;
    transition: opacity 200ms;
    .popup__inner {
      transform: scale(1);
      transition: transform 200ms;
    }
  }
  &.alert-exit {
    opacity: 1;
  }
  &.alert-exit-active {
    opacity: 0;
    transition: opacity 200ms;
    .popup__inner {
      transform: scale(0.9);
      transition: transform 200ms;
    }
  }
  &.alert-exited {
    opacity: 0;
  }
}
