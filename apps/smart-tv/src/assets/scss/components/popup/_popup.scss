@use "../../settings/function" as fn;
@use "../../settings/palettes" as pales;
$screen-hd: 1280px !default;

.popup {
  position: absolute;
  z-index: 99;
  top: 0;
  left: 0;
  &--fullscreen {
    width: 100vw;
    height: 100vh;
  }

  .d-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-block {
    display: block !important;
  }

  .justify-content-space-between {
    justify-content: space-between;
  }

  .align-items-end {
    align-items: flex-end;
  }
}

.popup-personal {
  font-size: fn.percent-unit(28px);
  line-height: 150%;
  color: #ffffff;
  p {
    margin: 0;
  }
  &__img {
    height: fn.percent-unit(417px);
    margin-bottom: fn.percent-unit(-50px);
    & > img {
      height: 100%;
      width: auto;
    }
  }
  &__content {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    width: 100%;
  }
  &__title {
    font-weight: 500;
    font-size: fn.percent-unit(36px);
    line-height: 140%;
  }

  &__background {
    position: absolute;
    top: 0;
    left: 0;
    background: #000000;
    opacity: 0.9;
    z-index: -1;
    width: 100vw;
    height: 100vh;
  }
  &__progress {
    position: relative;
    height: fn.percent-unit(8px);
    background: transparent;
    width: fn.percent-unit(412px);
    min-width: fn.percent-unit(550px);
    margin-top: fn.percent-unit(48px);
    border: 1px solid #3ac882;
    &--active {
      position: absolute;
      top: 0;
      left: 0;
      height: fn.percent-unit(8px);
      background: linear-gradient(90deg, #3ac882 0%, #97e98a 100%);
    }
  }
}

.popup-voucher {
  &--success {
    .ps-buy-success {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #111;
      .img.img--voucher {
        width: fn.percent-unit(126px);
        margin-bottom: fn.percent-unit(60px);
        & > img {
          width: 100%;
          height: auto;
        }
      }
      .text.text--voucher {
        color: #fff;
        font-size: fn.percent-unit(28px);
        line-height: 1.5;
        .title {
          font-size: fn.percent-unit(36px);
          margin-bottom: fn.percent-unit(60px);
          text-align: center;
        }
        .text-center {
          text-align: center;
        }
      }
      .page-voucher--info {
        margin-bottom: fn.percent-unit(130px);
        .d-flex {
          margin-bottom: fn.percent-unit(9px);
          line-height: 1.5;
          & > div:first-child {
            color: pales.$gray-64;
            width: fn.percent-unit(220px);
          }
          & > div:nth-child(2) {
            text-align: right;
            width: fn.percent-unit(320px);
            &.text-gold {
              color: pales.$yellow-da;
            }
          }
        }
      }
    }
    .btn {
      &--voucher {
        width: fn.percent-unit(400px);
      }
      &.focused {
        background: #ffffff !important;
        color: #000000 !important;
        font-size: fn.percent-unit(28px) !important;
        border-color: #ffffff !important;
      }
    }
  }
}

.popup__phone-used {
  .popup__description {
    font-weight: normal;
    font-size: fn.percent-unit(28px) !important;
    line-height: 150%;
  }
}
