////
/// @group nav
////

/// @param palettes
@use "settings/palettes" as pales;

/// @group variables
@use "settings/variables" as var;

/// @group function
@use "settings/function" as fn;

/// @group mixin
@use "mixin/position" as position;
@use "mixin/font-size" as font-size;
@use "mixin/pseudo" as pseudo;
@use "mixin/layer" as layer;

/// @group animated
@use "../../animates/fade-exits/fadeOutLeft" as fol; //use fade out left animates
@use "../../animates/fade-exits/fadeOutRight" as for; //use fade out left right
@use "../../animates/slides/slideInLeft" as slideInLeft; //use fade out left right

.popup-player {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: #111111;
  z-index: fn.z-index(layer-1);
  display: flex;

  &__button {
    position: absolute;
    left: fn.percent-unit(100);
    top: fn.percent-unit(60);
  }
  .btn {
    &-exit {
      width: fn.percent-unit(200px);
      height: fn.percent-unit(66px);
      padding: fn.percent-unit(12px) fn.percent-unit(24px);

      .icon {
        // display: flex;
        // align-items: center;
        // justify-content: center;
        // height: fn.percent-unit(32);
        // width: fn.percent-unit(32);
        // color: #fff;
        margin-right: fn.percent-unit(10);
      }

      &.focus {
        background-color: white;
        color: #222222;
        border-color: white;
        .icon {
          .vie {
            color: #222222;
          }
        }
      }
    }
  }

  // &.endscreen-suggestion {
  // }

  &.retry-player {
    align-items: center;
    display: flex;
    justify-content: center;
  }
}

.endscreen-suggestion {
  .endscreen-suggestion--card-tag {
    margin-top: fn.percent-unit(4);
    margin-left: fn.percent-unit(4);
  }
  &__backdrop {
    @include position.absolute(left);
    width: 100%;
    height: 100%;
    display: flex;
    // z-index: fn.z-index(layer-1);

    img {
      width: 100%;
      max-width: 100%;
      max-height: 100%;
      min-height: 100%;
    }
  }

  &__gradient {
    &::before {
      @include pseudo.pseudo(
        $width: 100%,
        // fn.percent-unit(420),
        $height: 50%,
        // fn.percent-unit(440),
        $display: block
      );
      bottom: 0;
      background: linear-gradient(0deg, #080e17 0%, rgba(#080e17, 0) 100%);
      position: absolute;
    }
  }

  &__ribbon {
    position: absolute;
    // left: 0;
    right: fn.percent-unit(44);
    bottom: fn.percent-unit(44);
    display: block;
    align-items: center;
    justify-content: center;
    text-align: left;
    transition: 0.25s ease 0s;
    height: fn.percent-unit(286);
    .title {
      font-family: Roboto;
      font-style: normal;
      font-weight: 500;
      font-size: fn.percent-unit(28);
      line-height: fn.percent-unit(42);
      color: #fff;
      margin-bottom: fn.percent-unit(15);
    }

    .list {
      display: block;
      align-items: center;
      justify-content: center;
      text-align: left;
      padding-top: fn.percent-unit(16);
      width: fit-content;
      margin: 0 auto;

      &-item {
        display: flex;
        align-items: center;
        justify-content: center;
        padding-bottom: fn.percent-unit(16);
        .card {
          padding: fn.percent-unit(4);
          height: fn.percent-unit(180);
          width: fn.percent-unit(320);
          margin-right: fn.percent-unit(16);
          position: relative;
          overflow: hidden;
          box-shadow: 0 0 0 fn.percent-unit(0) #ffffff;
          border-width: fn.percent-unit(4);
          // &::before {
          //   @include pseudo.pseudo(
          //     $width: fn.percent-unit(420),
          //     $height: 100%,
          //     $display: block
          //   );
          //   bottom: 0;
          //   background: linear-gradient(
          //     0deg,
          //     #080e17 0%,
          //     rgba(#080e17, 0) 100%
          //   );
          //   position: absolute;
          // }
          img {
            width: 100%;
            height: 100%;
          }
        }
        .focus {
          // box-shadow: 0 0 0 fn.percent-unit(5) #ffffff;
          outline: fn.percent-unit(5) solid #ffffff;

          &::before {
            height: 50%; // fn.percent-unit(100);;
          }
        }
      }

      .desc {
        font-family: Roboto;
        font-style: normal;
        font-weight: 500;
        font-size: fn.percent-unit(20);
        line-height: fn.percent-unit(24);
        color: #fff;
        // margin-bottom: fn.percent-unit(15);
        height: fn.percent-unit(40);
      }
    }
  }

  .force-login-group {
    position: absolute;
    top: fn.percent-unit(468);
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    p {
      margin-bottom: fn.percent-unit(12);
      padding: fn.percent-unit(12) fn.percent-unit(16);
      font-size: fn.percent-unit(32);
      line-height: fn.percent-unit(42);
      background-color: #11111199;
      box-shadow: 0 fn.percent-unit(2) fn.percent-unit(6) 0 #00000038;
      color: pales.$white;
      border-radius: fn.percent-unit(8);
    }
    &__actions {
      display: flex;
      div:not(:last-child) {
        margin-right: fn.percent-unit(8);
      }
      .btn.btn--ghost {
        border-color: pales.$white;
        border-radius: fn.percent-unit(8);
      }
    }
  }
}

.retry-player {
  &__content {
    z-index: 3;
    text-align: center;
    min-width: 50%;
    color: #fff;
    &-img {
      width: 15em;
      padding-bottom: 1.5em;
      z-index: 1;
    }
    &-desc {
      font-size: 28px;
      line-height: 1.5;
      margin-bottom: 1.43em;
    }
    &-button {
      margin-bottom: 7.5em;
      display: flex;
      justify-content: center;
      .button-retry {
        font-size: fn.percent-unit(28);
        background: rgba(0, 0, 0, 0.7);
        border-radius: fn.percent-unit(2);
        color: #fff;
        width: fn.percent-unit(192);
        height: fn.percent-unit(62);
        border: fn.percent-unit(1) solid #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;

        .spinner {
          width: fn.percent-unit(38);
          height: fn.percent-unit(38);
          border-top: fn.percent-unit(2) solid #fff;
          border-left: fn.percent-unit(2) solid #fff;
          border-right: fn.percent-unit(2) solid #fff;
          border-bottom: fn.percent-unit(2) solid transparent;
          margin-right: fn.percent-unit(16);

          align-items: center;
          justify-content: center;
          text-align: center;
        }

        &.focus {
          color: #000;
          background: #fff;
        }
      }
    }
  }
}
