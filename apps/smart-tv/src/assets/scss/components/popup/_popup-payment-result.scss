////
/// @group nav
////

/// @param palettes
@use "settings/palettes" as palettes;

/// @group variables
@use "settings/variables" as var;

/// @group function
@use "settings/function" as fn;

/// @group mixin
@use "mixin/position" as position;
@use "mixin/font-size" as font-size;
@use "mixin/pseudo" as pseudo;
@use "mixin/layer" as layer;

.popup--payment-result {
  @at-root .popup#{&} {
    .main.main--payment {
      margin-left: 0;
    }
  }
}
