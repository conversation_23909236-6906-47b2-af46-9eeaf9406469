////
/// @group nav
////

/// @param palettes
@use "settings/palettes" as palettes;

/// @group variables
@use "settings/variables" as var;

/// @group function
@use "settings/function" as fn;

/// @group mixin
@use "mixin/position" as position;
@use "mixin/font-size" as font-size;
@use "mixin/pseudo" as pseudo;
@use "mixin/layer" as layer;

.popup--payment-promotion-ads {
  @at-root .popup#{&} {
    .popup__inner {
      .ads-images {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        z-index: 1;
        & > img {
          width: 100%;
          height: 100%;
          background-color: palettes.$gray-9b;
        }
      }
      .button-group {
        justify-content: center;
        align-items: center;
        position: absolute;
        bottom: fn.percent-unit(60px);
        left: 50%;
        transform: translateX(-50%);
        z-index: 2;
        .btn {
          font-size: fn.percent-unit(28px);
          width: fn.percent-unit(430px);
        }
      }
    }
  }
}
