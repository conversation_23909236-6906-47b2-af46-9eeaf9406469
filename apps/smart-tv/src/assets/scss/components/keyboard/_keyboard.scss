////
/// Global Keyboard
////

/// @group variables
@use "../../settings/variables" as var;
@use "../../settings/function" as fn;
@use "../../settings/palettes" as pales;

/// @group mixin
@use "../../mixin/font-size" as fs;
@use "../../mixin/position" as position;

///
@use "../../components/icons/icon" as icon;

@mixin key-item(
  $keySizeHeight: 68,
  $keySizeWidth: 68,
  $keyPaddingX: 17,
  $keyPaddingY: 27,
  $fontSize: 32,
  $fontWeight: 500,
  $marginRight: 8
) {
  align-items: center;
  background: #202224;
  color: #dadce0;
  box-sizing: border-box;
  display: flex;
  height: fn.percent-unit($keySizeHeight);
  justify-content: center;
  width: fn.percent-unit($keySizeWidth);
  margin: 0;
  padding: fn.percent-unit($keyPaddingX) fn.percent-unit($keyPaddingY);
  text-align: center;
  font-size: fn.percent-unit($fontSize);
  font-style: normal;
  font-weight: $fontWeight;
  line-height: normal;

  .space {
    display: inline-block;
    border: fn.percent-unit(4px) solid pales.$gray-cc;
    border-top: none;
    width: fn.percent-unit(90px);
    height: 20%;
    position: relative;
  }

  &:not(:last-child) {
    margin-right: fn.percent-unit($marginRight);
  }

  &.focus {
    background: pales.$white !important;
    color: pales.$black !important;
  }

  &.x2 {
    width: fn.percent-unit((2 * $keySizeWidth) + (1 * $marginRight));
  }

  &.x3 {
    width: fn.percent-unit((3 * $keySizeWidth) + (2 * $marginRight));
  }

  &.x4 {
    width: fn.percent-unit((4 * $keySizeWidth) + (3 * $marginRight));
  }

  &.x5 {
    width: fn.percent-unit((5 * $keySizeWidth) + (4 * $marginRight));
  }

  &.x6 {
    width: fn.percent-unit((6 * $keySizeWidth) + (5 * $marginRight));
  }

  // &::before {
  //   display: block;
  //   width: fn.percent-unit(72);
  //   height: fn.percent-unit(72);
  //   @include position.absolute(top 50% left 50%);
  //   content: "";
  //   transform: translate(-50%, -50%);
  // }

  .keyboard__text,
  .keyboard__icon {
    position: relative;
    z-index: fn.z-index(layer-1);
    color: pales.$gray-cc;
  }

  .keyboard__text {
    font-weight: 500;
    font-size: fn.percent-unit(32);
  }

  .icon {
    .vie {
      color: pales.$gray-cc;
      height: fn.percent-unit(32);
      width: fn.percent-unit(32);
      &.vie-capslock-off-default,
      &.vie-capslock-off-disabled,
      &.vie-capslock-off-focused,
      &.vie-capslock-on-default,
      &.vie-capslock-on-disabled {
        height: fn.percent-unit(28);
        width: fn.percent-unit(28);
      }
    }
  }

  &.focus {
    background-color: pales.$gray-300;

    &::before {
      background-color: pales.$gray-300;
    }

    .keyboard__text,
    .keyboard__icon {
      color: pales.$gray-33;

      svg {
        color: pales.$gray-33;
      }
    }

    .space {
      border-color: pales.$gray-33;
    }
  }
  .vie-capslock-off-default,
  .vie-capslock-off-focused,
  .vie-capslock-off-disabled {
    color: transparent !important;
  }
  .vie-capslock-on-disabled {
    fill: pales.$gray-33;
  }

  &.disabled {
    .space {
      border: fn.percent-unit(4px) solid pales.$gray-33;
      border-top: none;
    }

    .keyboard__icon {
      svg {
        color: pales.$gray-33;
      }
    }

    .keyboard__text {
      color: pales.$gray-33;
    }
  }

  &-2 {
    width: fn.percent-unit(148);
  }

  &-3 {
    width: fn.percent-unit(240);
  }
}

.keyboard {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;

  &__wrap {
    width: fn.percent-unit(752);
  }

  &__grid {
    display: flex;
    flex-flow: row wrap;
  }

  &__item {
    @include key-item;
  }

  &--number {
    .keyboard__wrap {
      width: fn.percent-unit(754);
    }
  }

  &--number-small {
    .keyboard__wrap {
      width: fn.percent-unit(507);
    }
  }

  &--number-abc {
    .keyboard__wrap {
      width: fn.percent-unit(900);
    }
  }

  &--number-qwerty {
    .keyboard__wrap {
      width: fn.percent-unit(900);
    }

    &.capslock {
      .keyboard__text {
        text-transform: uppercase;
      }
    }
  }

  &--search,
  &--report {
    .keyboard__wrap {
      width: auto;
    }
  }

  .rows {
    .row {
      display: flex;
      &:not(:last-child) {
        margin-bottom: fn.percent-unit(8px);
      }
    }
  }
  .disabled {
    background-color: #1a1b1c !important;
  }
}

.keyboard--number .keyboard__item {
  @include key-item(100, 246, 17, 27, 32, 400, 8);
}

.keyboard--search .keyboard__item {
  @include key-item(36, 2.5);
}

.keyboard--standard .keyboard__item,
.keyboard--email .keyboard__item,
.keyboard--payment .keyboard__item,
.keyboard--password .keyboard__item,
.keyboard--report .keyboard__item,
.keyboard--search .keyboard__item {
  @include key-item(68, 68, 15, 20, 32, 500, 8);
}

.keyboard--email .keyboard__item .keyboard__text.upper-case {
  text-transform: uppercase;
}

.keyboard--number .keyboard--control,
.keyboard--standard .keyboard--control,
.keyboard--email .keyboard--control,
.keyboard--payment .keyboard--control,
.keyboard--password .keyboard--control,
.keyboard--report .keyboard--control,
.keyboard--search .keyboard--control {
  background: #2d2d2d;
  color: #dadce0;
  display: flex;
  box-sizing: border-box;
  padding: fn.percent-unit(18) fn.percent-unit(10);
  font-weight: 400;
  font-size: fn.percent-unit(24);
}

.keyboard--number-small .keyboard__item {
  @include key-item(63, 164, 17, 27, 32, 400, 8);
}

.keyboard--number-small .keyboard--control {
  background: #2d2d2d;
  color: #dadce0;
  display: flex;
  box-sizing: border-box;
  padding: fn.percent-unit(18) fn.percent-unit(10);
  font-size: fn.percent-unit(24);
}

.keyboard--number-small .keyboard--action {
  background: #424c42;
  color: #dadce0;
  display: flex;
  box-sizing: border-box;
  padding: fn.percent-unit(18) fn.percent-unit(10);
  font-size: fn.percent-unit(24);
}

.keyboard--email .keyboard--control-extra {
  background: #202224;
  color: #dadce0;
  display: flex;
  box-sizing: border-box;
  padding: fn.percent-unit(18) fn.percent-unit(10);
  font-size: fn.percent-unit(24);
  width: fn.percent-unit(245);
  &:nth-child(1) {
    width: fn.percent-unit(246);
  }
}

.keyboard--payment .keyboard--action,
.keyboard--email .keyboard--action,
.keyboard--report .keyboard--action {
  @include key-item(68, 68, 15, 22, 28, 400, 8);
  background: #424c42;
  font-size: fn.percent-unit(24);
}
