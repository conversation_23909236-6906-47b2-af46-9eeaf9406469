///@group nav--user-login

/// @group variables
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/position" as position;
@use "mixin/pseudo" as pseudo;
@use "mixin/layer" as layer;

///
.nav--tabs {
  @at-root .nav#{&} {
    display: block;
    margin: 0;
    overflow: hidden;
    .nav__body {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      position: relative;
      margin-right: fn.percent-unit(50px);
    }
    .nav__item {
      border-radius: fn.percent-unit(4);
      color: palettes.$gray-9b;
      font-size: fn.percent-unit(28);
      font-weight: 500;
      line-height: 1.5;
      position: relative;
      padding: fn.percent-unit(12) fn.percent-unit(20);
      min-width: fit-content;
      box-sizing: border-box;
      margin-right: fn.percent-unit(1);

      &.active {
        color: palettes.$gray-de;
        background-color: palettes.$gray-33;
      }

      &.active-underline {
        color: palettes.$gray-de;
        &:after {
          @include pseudo.pseudo(
            $width: fn.percent-unit(80%),
            $height: fn.percent-unit(8),
            $display: block
          );
          border-top-left-radius: fn.percent-unit(8);
          border-top-right-radius: fn.percent-unit(8);
          @include position.absolute(bottom fn.percent-unit(0) left 50%);
          @include layer.layers(layer-min);
          background-color: palettes.$green-3a;
          transform: translateX(-50%);
        }
        &.thin-line {
          &:after {
            height: fn.percent-unit(4);
          }
        }
      }

      &.disabled {
        color: palettes.$gray-9b;
      }

      &.focus {
        color: palettes.$gray-22;
        background-color: palettes.$white;
      }
    }
  }
}
