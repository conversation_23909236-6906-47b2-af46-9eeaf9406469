/// @group variables
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/position" as position;

/// @group components
@use "components/icons/icon" as icons;

///
.nav--audio {
  @at-root .nav#{&} {
    padding: 0;

    .nav__item {
      width: auto;

      .nav__text {
        color: #ccc;
      }
      //nav icon
      .nav__icon {
        @include icons.icon($size: "basic");
        transition: margin 0.2s;
      }
    }
    & > .nav__item {
      &:not(:last-child) {
        margin-bottom: fn.percent-unit(32);
      }
      .nav__text {
        font-size: fn.percent-unit(32);
      }
    }
    .nav--sub {
      padding-top: fn.percent-unit(16);
      margin: 0;
      .nav__item {
        display: block;
        padding: fn.percent-unit(12) fn.percent-unit(16);
        &.active {
          .nav__icon {
            visibility: visible;
            z-index: 10;
          }
          .nav__icon .vie,
          .nav__text {
            color: #9b9b9b;
          }
          &.focus {
            .nav__icon .vie,
            .nav__text {
              color: #333333;
              z-index: 10;
              position: relative;
            }
          }
        }
        &.focus {
          .nav__icon .vie,
          .nav__text {
            color: #333333;
            z-index: 10;
            position: relative;
          }
        }
      }
      .nav__text {
        font-size: fn.percent-unit(28);
        color: #9b9b9b;
      }
      .nav__icon {
        $icon-size: 32;

        @include position.absolute(top 50% left fn.percent-unit(16));
        height: fn.percent-unit($icon-size);
        margin: 0;
        transform: translateY(-50%);
        width: fn.percent-unit($icon-size);
        visibility: hidden;

        .vie {
          $vie-size: fn.percent-unit($icon-size - 4);
          width: $vie-size;
          height: $vie-size;
          font-size: $vie-size;
        }
        & ~ .nav__text,
        & + .nav__text {
          padding-left: fn.percent-unit(48);
        }
      }
      .tags {
        @include position.absolute(top 50% right fn.percent-unit(16));
        transform: translateY(-50%);
        & ~ .nav__text,
        & + .nav__text {
          padding-right: fn.percent-unit(60);
        }
      }
    }
  }
}
