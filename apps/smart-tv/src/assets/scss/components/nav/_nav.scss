////
/// @group nav
////

/// @param palettes
@use "settings/palettes" as palettes;

/// @group variables
@use "settings/variables" as variables;

/// @group function
@use "settings/function" as fn;

/// @group mixin
@use "mixin/position" as position;
@use "mixin/font-size" as font-size;
@use "mixin/pseudo" as pseudo;
@use "mixin/layer" as layer;

/// @group components
@use "components/icons/icon" as icons;

// mixin nav
@mixin nav($dir: variables.$nav-dir, $spacing: variables.$nav-spacing) {
  margin-bottom: fn.percent-unit($spacing);

  @if $dir == "basic" {
    &__item {
      position: relative;
    }
    &__icon {
      margin: 0 fn.percent-unit(24);
      img {
        max-height: fn.percent-unit(40);
        object-fit: contain;
      }
    }
    &__text {
      @include font-size.font-size(16px);
      @include layer.layers(layer-1);
      color: palettes.$white;
      line-height: 1.5;
      margin-top: fn.percent-unit(5);
      &-expanded {
        transform: translateX(fn.percent-unit(-30));
        font-weight: 500;
        white-space: nowrap;
        font-size: fn.percent-unit(24);
        color: #9b9b9b;
        visibility: hidden;
        max-width: 0;
        transition:
          transform 0.2s,
          margin 0.2s,
          color 0.2s;
      }
    }
  } @else if $dir == "vertical" {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-items: center;
  }
}

// style nav

.nav {
  @include nav;
}

//
.nav--main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: fn.percent-unit(725);
  @at-root .nav#{&} {
    .nav__ {
      &item {
        height: fn.percent-unit(65);
        display: flex;
        align-items: center;
        margin-bottom: fn.percent-unit(16);
        &-left {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          &::after {
            @include pseudo.pseudo(
              $width: fn.percent-unit(32),
              $height: fn.percent-unit(4),
              $display: block
            );
            @include position.absolute(bottom fn.percent-unit(-9) left 50%);
            @include layer.layers(layer-min);
            background-color: palettes.$green-3a;
            opacity: 0;
            transform: translateX(-50%);
            z-index: 1;
          }
        }

        &.active {
          .nav__item-left::after {
            opacity: 1;
          }

          .nav__icon.vip .vie-vip-o {
            color: #da9e1c;
          }
        }
        &.focus {
          .nav__item-left::after {
            background-color: palettes.$white;
          }
        }
      }

      &__text {
        width: fn.percent-unit(100px);
        text-align: center;
      }
    }
  }
}

//
.nav--user {
  @at-root .nav#{&} {
    margin-top: auto;
    width: 0;
    margin-bottom: 0;
    visibility: hidden;
    transform: translateX(fn.percent-unit(-30));
    transition:
      transform 0.25s,
      opacity 0.25s;
    .nav {
      &__item {
        width: fn.percent-unit(100%);
        height: fn.percent-unit(50px);
        display: flex;
        align-items: center;
      }
      &__text {
        white-space: nowrap;
        margin: 0 fn.percent-unit(24);
        font-size: fn.percent-unit(20);
      }
    }
  }
}
