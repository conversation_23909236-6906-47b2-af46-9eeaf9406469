/////
/// @group billboard
////

/// @param pales
@use "src/assets/scss/settings/palettes" as pales;

/// @group variables
@use "src/assets/scss/settings/variables" as var;

/// @group function
@use "src/assets/scss/settings/function" as fn;

/// @group icon
@use "src/assets/scss/components/icons/icon" as icon;

/// @group mixin
@use "src/assets/scss/mixin/pseudo" as pseudo;

@keyframes append-animate {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/// mixin billboard
@mixin billboard() {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  color: pales.$white;
  font-size: fn.percent-unit(24px);
  line-height: 1.5;
}

/// mixin backdrop
@mixin backdrop() {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  height: 0;
  bottom: 0;
  z-index: -1;

  img {
    width: 100%;
    height: auto;
  }
}

/// mixin sidebar__content
@mixin content() {
  position: relative;
  z-index: 2;
  max-width: fn.percent-unit(604);
  padding-left: fn.percent-unit(var.$sidebar-max-width);
  padding-top: fn.percent-unit(114);

  .award-icon svg,
  .top-icon svg {
    color: #3ac882 !important;
  }

  .award-icon svg {
    transform: scale(1.5);
  }

  padding-bottom: fn.percent-unit(32);

  // .tag--tvod-price {
  //   padding: 0 fn.percent-unit(13px);
  //   height: fn.percent-unit(fn.tags-height(billboard));
  //   background-color: #DA9E1C;
  //   color: #fff;
  //   font-size: fn.percent-unit(21px);
  //   font-weight: 700;
  //   line-height: 1.2;
  //   display: flex;
  //   align-items: center;
  //   box-sizing: border-box;

  //   .price-unit {
  //     font-size: fn.percent-unit(11px);
  //     font-weight: 500;
  //   }
  // }
}

/// mixin title-card
@mixin title-card($margin-bottom: 0px) {
  height: fn.percent-unit(172);
  margin-bottom: fn.percent-unit($margin-bottom);
  transition: all 0.3s;
  animation: append-animate 0.3s linear;

  img {
    height: 100%;
    width: auto;
    display: block;
  }
}

@mixin meta($margin-bottom: 0px) {
  width: fn.percent-unit(700px);
  margin-bottom: fn.percent-unit($margin-bottom);

  .tags-group {
    color: pales.$white;
    display: flex;
    align-items: center;
    margin: 0;

    .tag-icon {
      @include icon.icon($color: pales.$white);

      &:not(:last-child) {
        margin-right: fn.percent-unit(16px);
      }
    }

    .tag-meta {
      font-size: fn.percent-unit(24px);
      position: relative;

      &:not(:last-child) {
        margin-right: fn.percent-unit(38px);

        &::after {
          @include pseudo.pseudo($width: 6px, $height: 6px);
          position: absolute;
          top: 50%;
          right: fn.percent-unit(-21px);
          transform: translateY(-50%);
          border-radius: 50%;
          background-color: pales.$gray-cc;
        }
      }
    }

    .text {
      font-size: fn.percent-unit(28px);
      line-height: 1.5;
    }
  }
}

@mixin description($margin-bottom: 0px) {
  color: pales.$white;
  max-height: fn.percent-unit(102px);
  margin-bottom: fn.percent-unit($margin-bottom);
  font-size: fn.percent-unit(24px);
  line-height: 1.4;
  font-weight: 500;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

@mixin button-group() {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: fn.percent-unit(700px);

  .btn:not(:last-child) {
    margin-right: fn.percent-unit(24px);
  }

  .btn__icon {
    height: fn.percent-unit(32);
    width: fn.percent-unit(32);
    color: #fff;

    svg {
      fill: #fff;
    }

    &:not(:last-child) {
      margin-right: fn.percent-unit(16);
    }
  }

  .btn.focus .btn__icon {
    color: #222;

    svg {
      fill: #222;
    }
  }
}

.billboard {
  @include billboard();

  &.collapse-masthead-ads {
    top: fn.percent-unit(200);
  }

  &__content {
    @include content();

    &.shrink {
      .billboard__title-card {
        margin-top: fn.percent-unit(150);
        height: fn.percent-unit(103px);
      }
    }

    &--basic {
      padding-top: fn.percent-unit(212);

      .billboard__title-card img {
        width: fn.percent-unit(411);
        height: auto;
      }
    }

    .billboard__info-box {
      .tag {
        font-size: fn.percent-unit(22);
        padding-top: 0;
        padding-bottom: 0;
        height: fn.percent-unit(fn.tags-height(billboard));
        display: flex;
        align-items: center;
        box-sizing: border-box;
      }
    }

    &--live,
    &--livetv {
      max-width: fn.percent-unit(645px);

      .billboard__info-box {
        margin-bottom: fn.percent-unit(24);

        .tags,
        .icon {
          margin-right: fn.percent-unit(14);
        }
      }

      &.livetv-page {
        padding-top: fn.percent-unit(82);
        .billboard__info-box {
          border-radius: fn.percent-unit(4);
          border-width: fn.percent-unit(1);
          border-style: solid;
          // border-image: linear-gradient(to right, #454545 0%, #111111 100%) 1;
          border-image: linear-gradient(to right, #454545 0%, transparent 100%)
            1;
          width: fn.percent-unit(596);
          padding: fn.percent-unit(17) fn.percent-unit(20);
          box-sizing: content-box;
          display: flex;
          flex-direction: column;
          .row {
            width: fn.percent-unit(100%);
            display: flex;
            align-items: center;

            &.info {
              margin-bottom: fn.percent-unit(8);
              .v-tag {
                height: fn.percent-unit(28);
                margin-right: fn.percent-unit(24);
                font-size: fn.percent-unit(20);
                line-height: fn.percent-unit(23);
                font-weight: 700;
                padding: fn.percent-unit(4);
              }
              span {
                color: #cccccc;
                font-size: fn.percent-unit(20);
                font-weight: 400;
                line-height: fn.percent-unit(30);
              }
            }
            &.title {
              font-size: fn.percent-unit(28);
              line-height: fn.percent-unit(42);
              font-weight: 700;
              line-clamp: 2;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
              overflow: hidden;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              &.next {
                font-size: fn.percent-unit(24);
                line-height: fn.percent-unit(36);
                font-weight: 500;
              }
            }
          }
        }
      }

      .billboard__info-box__title {
        font-size: fn.percent-unit(48);
        font-weight: 500;
        max-height: fn.percent-unit(202px);
        line-height: 1.4;
        font-weight: 500;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
      }
    }

    &--livetv {
      .billboard__title-card {
        height: fn.percent-unit(129px);
        line-height: 1.2;
      }

      &.billboard__content--livetv--master-banner {
        max-width: fn.percent-unit(604px);

        .billboard__title-card {
          height: fn.percent-unit(173px);
        }
      }
    }

    &--livetv-v2 {
      display: flex;
      align-items: center;
      color: #fff;
      &.billboard-homepage {
        padding: fn.percent-unit(40) fn.percent-unit(54) fn.percent-unit(48)
          fn.percent-unit(180);
      }
      &.billboard-list-channel-panel {
        padding: fn.percent-unit(60) fn.percent-unit(100) fn.percent-unit(48)
          fn.percent-unit(100);
      }
      .channel-image {
        width: fn.percent-unit(517);
        min-width: fn.percent-unit(517);
        height: fn.percent-unit(290);
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
        .empty-program-image {
          background-color: #22222280;
          width: 100%;
          height: 100%;
        }
      }

      .content-tip-container {
        margin-left: fn.percent-unit(40);
        display: flex;

        .billboard__info-box {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: flex-end;
          margin-bottom: 0;

          .row {
            &.title {
              font-size: fn.percent-unit(32);
              line-height: fn.percent-unit(42);
              font-weight: 500;
            }
            &.info {
              margin-top: fn.percent-unit(16);
              display: flex;
              height: fn.percent-unit(32);
              align-items: center;
              font-size: fn.percent-unit(24);
              line-height: fn.percent-unit(32);

              .icon {
                path {
                  fill: transparent;
                }
                .active {
                  path {
                    fill: #fff;
                  }
                }
              }
              .playing {
                display: flex;
                align-items: center;
                img {
                  width: fn.percent-unit(32);
                  height: fn.percent-unit(32);
                  margin-right: fn.percent-unit(12);
                }
              }

              .text-time-left {
                color: #ffffff73;
              }

              > *:not(:first-child) {
                margin-left: fn.percent-unit(12);
                &.text-time-left {
                  margin-left: fn.percent-unit(36);
                }
              }
            }
          }

          &.current-program {
            flex: 1;
            width: fn.percent-unit(630);
            min-width: fn.percent-unit(630);
          }
          &.next-program {
            flex: 1;
            min-width: fn.percent-unit(420);
            border-left: solid fn.percent-unit(1) #ffffff73;
            padding-left: fn.percent-unit(36);
            margin-left: fn.percent-unit(36);
          }
          &.empty-program {
            justify-content: center;
            font-weight: 500;
            font-size: fn.percent-unit(32);
            line-height: fn.percent-unit(42);
            letter-spacing: fn.percent-unit(-0.48);
            color: pales.$white;
          }
        }
      }

      .schedule-loading {
        margin-left: fn.percent-unit(40);
        // background-image: url("/src/assets/images/live_tv/schedule-loading.svg");
        background-image: url("../../../images/live_tv/schedule-loading.svg");
        background-repeat: no-repeat;
        width: 100%;
        height: fn.percent-unit(90);
      }
    }
  }

  &__title-card {
    @include title-card($margin-bottom: 25px);
  }

  &__meta {
    @include meta($margin-bottom: 16px);
  }

  &__description {
    @include description($margin-bottom: 16px);
    // &--livetv {
    //   -webkit-line-clamp: 2;
    //   max-height: fn.percent-unit(72px);
    // }
  }

  &__headline {
    @include description($margin-bottom: 16px);
    font-size: fn.percent-unit(48);
    line-height: fn.percent-unit(56);
    letter-spacing: fn.percent-unit(0.297);
    max-height: fn.percent-unit(115px);
  }

  &__button-group {
    @include button-group();
  }

  &__live {
    width: fn.percent-unit(65px);
    height: fn.percent-unit(26px);

    img {
      width: 100%;
      height: auto;
    }
  }

  &__info-box {
    display: flex;
    align-items: center;
    margin-bottom: fn.percent-unit(16);

    .text {
      margin-left: fn.percent-unit(16);
    }

    .tag-info {
      padding: 0 fn.percent-unit(12px);
      position: relative;
      font-size: fn.percent-unit(20px);

      &:not(:last-child)::after {
        position: absolute;
        content: "";
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        height: 20px;
        width: 0px;
        border-right: 1px solid pales.$gray-cc;
      }

      &:first-child {
        padding-left: 0;
      }

      &.tag--vip {
        height: fn.percent-unit(21px);
        padding-right: 0;

        &::after {
          display: none;
        }

        & > img {
          display: block;
          height: 100%;
          width: auto;
        }
      }
    }

    .ranking {
      height: fn.percent-unit(32px);
      display: flex;
      align-items: center;
      font-size: fn.percent-unit(24px);
      font-weight: 400;

      .tag-icon {
        height: fn.percent-unit(32px);
        width: fn.percent-unit(32px);
        margin-right: fn.percent-unit(16px);

        .vie {
          height: 100%;
          width: 100%;
        }
      }
    }
  }

  .content-tip-container {
    &.fade-enter {
      .content-tip {
        opacity: 0;
      }
    }

    &.fade-enter-active {
      .content-tip {
        opacity: 1;
        transition: opacity 200ms;
      }
    }

    &.fade-enter-done {
      .content-tip {
        opacity: 1;
      }
    }

    &.fade-exit {
      .content-tip {
        opacity: 1;
      }
    }

    &.fade-exit-active {
      .content-tip {
        opacity: 0;
        transition: opacity 200ms;
      }
    }

    &.fade-exit-done {
      .content-tip {
        opacity: 0;
      }
    }
  }

  .billboard__content {
    &.fade-enter {
      .billboard__wrap {
        opacity: 0;
      }
    }

    &.fade-enter-active {
      .billboard__wrap {
        opacity: 1;
        transition: opacity 300ms;
      }
    }

    &.fade-enter-done {
      .billboard__wrap {
        opacity: 1;
      }
    }

    &.fade-exit {
      .billboard__wrap {
        opacity: 1;
      }
    }

    &.fade-exit-active {
      .billboard__wrap {
        opacity: 0;
        transition: opacity 300ms;
      }
    }

    &.fade-exit-done {
      .billboard__wrap {
        opacity: 0;
      }
    }
  }

  &__overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: transparent;
    z-index: 3;

    &.froze,
    &.blur {
      background-color: rgba(0, 0, 0, 0.4);
    }
  }

  &__premiere {
    width: fn.percent-unit(65px);
    height: fn.percent-unit(26px);

    img {
      width: auto;
      height: auto;
    }
  }

  &.fade-enter {
    .billboard-wrap {
      opacity: 0;
      transform: translateY(-50%);
    }
  }

  &.fade-enter-active {
    .billboard-wrap {
      opacity: 1;
      transition: opacity 200ms, transform 200ms;
      transform: translateY(0);
    }
  }

  &.fade-enter-done {
    .billboard-wrap {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &.fade-exit {
    .billboard-wrap {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &.fade-exit-active {
    .billboard-wrap {
      opacity: 0;
      transform: translateY(-50%);
      transition: opacity 200ms, transform 200ms;
    }
  }

  &.fade-exit-done {
    .billboard-wrap {
      opacity: 0;
      transform: translateY(-50%);
    }
  }

  .watch-more__description {
    font-size: fn.percent-unit(20px);
    margin-bottom: fn.percent-unit(24px);
    color: #dedede;

    &--title {
      font-size: fn.percent-unit(24px);
      margin-bottom: fn.percent-unit(20px);
      font-weight: 600;
    }
  }

  .watch-more__progress {
    position: relative;
    height: fn.percent-unit(8px);
    background: #cccccc;
    width: 100%;
    width: fn.percent-unit(411px);
    margin-bottom: fn.percent-unit(12px);

    &--active {
      position: absolute;
      top: 0;
      left: 0;
      height: fn.percent-unit(8px);
      background: #3ac882;
      max-width: fn.percent-unit(411px);
    }
  }
}

#billboard-offset {
  // transition: height .3s;
  &.category {
    // margin-top: fn.percent-unit(600);
    height: fn.percent-unit(600);
  }

  &.medium {
    // margin-top: fn.percent-unit(576);
    height: fn.percent-unit(576);
  }

  &.expand {
    // margin-top: fn.percent-unit(810);
    height: fn.percent-unit(810);
  }

  &.collapse {
    // margin-top: fn.percent-unit(140);
    height: fn.percent-unit(140);
  }
}
