////
/// @group button
////

/// @param palettes
@use "settings/palettes" as pales;

/// @group variables
@use "settings/variables" as var;

/// @group function
@use "settings/function" as fn;

/// @group mixin
@use "../../mixin/button" as btn;

/// @group icon
@use "components/icons/icon" as icon;

/// @group button style
// @mixin button() {
// align-items: center;
// border-radius: fn.percent-unit(2px);
// box-sizing: border-box;
// color: pales.$white;
// display: flex;
// font-size: fn.percent-unit(28px);
// width: fn.percent-unit(230px);
// &__icon {
//   @include icon.icon();
//   &:not(:last-child) {
//     margin-right: fn.percent-unit(16px);
//   }
// }
// }

@mixin ghost() {
  background: rgba(0, 0, 0, 0.5);
  border: 2px solid pales.$gray-9b;
  .btn__icon .vie {
    color: inherit;
    transition: none;
  }
  &.focus {
    background: pales.$white;
    border-color: pales.$white;
    color: #222222;
    .btn__icon {
      color: #222222;
    }
    &.text-green {
      color: pales.$green-3a;
      .btn__icon {
        color: pales.$green-3a;
      }
    }
  }
  &.disabled {
    opacity: 0.5;
    border-color: #ccc;
    color: #b6b6b6;
  }
}

.btn {
  @include btn.button;
  &.btn--ghost {
    @include ghost();

    &.user-AnonymousUserRequireSignUp {
      width: fn.percent-unit(520px);
    }
  }
  &.text-green {
    color: pales.$green-3a;
  }
  &.btn--popup {
    width: fn.percent-unit(294px);
    &:not(:last-child) {
      margin-right: fn.percent-unit(24px);
    }

    &.disabled {
      opacity: 0.5;
    }
  }
  &.btn--popup-countdown {
    min-width: fn.percent-unit(294px);
    width: fit-content;
    &:not(:last-child) {
      margin-right: fn.percent-unit(24px);
    }
  }
  &.btn--light-bg {
    border: fn.percent-unit(2px) solid pales.$gray-64;
    color: pales.$black;
    &.focus {
      color: pales.$white;
      background: pales.$green-3a;
      border-color: pales.$green-3a;
    }
    &.disabled {
      border-color: #ccc;
      color: #b6b6b6;
    }
  }
  &.btn--promotion {
    width: fn.percent-unit(400px);
    height: fn.percent-unit(66px);
    border: fn.percent-unit(2px) solid pales.$gray-9b;
    background: rgba(0, 0, 0, 0.5);
    color: pales.$white !important;
    border-radius: fn.percent-unit(2px);
    &.focus {
      background: pales.$green-3a;
      border-color: pales.$green-3a;
    }
    &:not(:last-child) {
      margin-right: fn.percent-unit(24px);
    }
  }
}

.button {
  @include btn.button;

  &-group {
    display: flex;

    &:not(.vertical) {
      flex-flow: row wrap;
      .button {
        &:not(:last-child) {
          margin-right: fn.percent-unit(24);
        }
      }
    }

    &.vertical {
      flex-flow: column nowrap;

      .button {
        flex: 0 0 auto;
        &:not(:last-child) {
          margin-bottom: fn.percent-unit(16);
        }
      }
    }

    &.justify-content-center {
      justify-content: center;
    }
    &.align-center {
      align-items: center;
    }
  }
}
