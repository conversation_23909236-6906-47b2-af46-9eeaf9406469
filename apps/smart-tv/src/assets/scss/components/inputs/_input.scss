/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ================ INPUT STYLE ===============
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* VARIABLE
* INPUT STYLES
*
*/

/// @param settings
@use "settings/function" as fn;

/// @group mixin
@use "mixin/fonts" as fonts;
@use "mixin/input" as input;

/// @group input
.input {
  @include input.input($modifier: true);

  ///
  &-group {
    display: flex;

    & > * {
      &:not(:last-child) {
        margin-right: fn.percent-unit(12);
      }
    }

    & > .form-control,
    .input--custom {
      flex: 1 1 auto;
      min-width: 0;
      width: 1%;

      &:not(:last-child) {
        margin-bottom: 0;
      }

      .input__notify {
        @include fonts.font-size(20);
        padding-top: fn.percent-unit(0);
        line-height: 1.25;
        height: fn.percent-unit(25px);
      }
    }
    .button {
      border-radius: fn.percent-unit(4);
      display: inline-block;
      padding-left: fn.percent-unit(30);
      padding-right: fn.percent-unit(30);
      vertical-align: middle;
    }
  }
  &.input--payment-credit-card,
  &.input--payment-discount,
  &.input--payment-send-invoice-email {
    position: relative;
    .input__control {
      border: 2px solid #000;
    }
    &.error {
      .input__control {
        border: 2px solid fn.el-color($color: v-tomato, $shade: 165);
      }
    }
    &.focus {
      .input__control {
        border: 2px solid fn.el-color($color: v-medium-sea-green, $shade: 149);
      }
    }
  }
  &.input--payment-credit-card,
  &.input--payment-send-invoice-email {
    overflow: visible;
    position: relative;
    .input__notify {
      position: absolute;
      top: 100%;
      padding-top: fn.percent-unit(2px);
      color: #000;
    }
    &.input--custom {
      margin-bottom: 0;
    }
  }

  &.input--payment-send-invoice-email {
    .input__container {
      display: flex;
      width: 100%;
      overflow: hidden;
      height: 100%;
      align-items: center;
    }
  }
}
