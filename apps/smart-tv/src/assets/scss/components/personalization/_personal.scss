////
/// @group nav
////

/// @param palettes
@use "../../settings/palettes" as pales;

/// @group variables
@use "../../settings/variables" as var;

/// @group function
@use "../../settings/function" as fn;

/// @group mixin
@use "../../mixin/position" as position;
@use "../../mixin/font-size" as font-size;
@use "../../mixin/pseudo" as pseudo;
@use "../../mixin/layer" as layer;

/// @group animated
@use "../../animates/fade-exits/fadeOutLeft" as fol; //use fade out left animates
@use "../../animates/fade-exits/fadeOutRight" as for; //use fade out left right
@use "../../animates/slides/slideInLeft" as slideInLeft; //use fade out left right

//
.personal {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw - fn.percent-unit(161px) - fn.percent-unit(60px);
  height: 100%;
  padding-left: fn.percent-unit(161px);
  padding-right: fn.percent-unit(60px);
  padding-top: fn.percent-unit(47px);
  color: pales.$white;
  &-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  &-title {
    font-weight: 500;
    font-size: fn.percent-unit(36px);
    line-height: 140%;
    margin: 0;
    padding: 0;
  }

  &-logo {
    height: fn.percent-unit(60px);
    width: fn.percent-unit(189px);
    & > img {
      height: 100%;
      width: 100%;
    }
  }
  &-description {
    font-weight: 500;
    font-size: fn.percent-unit(32px);
    line-height: fn.percent-unit(37px);
    margin-top: fn.percent-unit(55px);
    margin-bottom: fn.percent-unit(28px);
    padding: 0;
  }

  &-button {
    margin-top: fn.percent-unit(44px);
    font-weight: 500;
    font-size: fn.percent-unit(28px);
    line-height: 140%;
    &.disabled {
      background: #434343;
      color: #8a8a8a;
    }
  }
}
.personal-item-container {
  border-radius: fn.percent-unit(6.51515px);
  width: fit-content;
  height: fit-content;
  padding: fn.percent-unit(4px);
  margin-bottom: fn.percent-unit(12px);
  margin-right: fn.percent-unit(12px);
  background: transparent;
  &.focus {
    background: pales.$white;
  }
  &.selected {
    background: transparent;
    .personal-item__desc {
      background: pales.$white;
      color: pales.$black;
    }
  }
  &.focus.selected {
    background: pales.$white;
  }
  &.selecting {
    position: relative;
    &:after {
      content: "Đã chọn";
      position: absolute;
      top: 0;
      left: 0;
      background: pales.$white;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: fn.percent-unit(6.51515px);
      //font
      font-weight: 500;
      font-size: fn.percent-unit(36px);
      line-height: 140%;
      color: pales.$black;
    }
  }
  &.unselecting {
    position: relative;
    &:after {
      content: "Bỏ chọn";
      position: absolute;
      top: 0;
      left: 0;
      background: pales.$white;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: fn.percent-unit(6.51515px);
      //font
      font-weight: 500;
      font-size: fn.percent-unit(36px);
      line-height: 140%;
      color: pales.$black;
    }
  }
}

.personal-item {
  color: pales.$white;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: fn.percent-unit(390px);
  height: fn.percent-unit(117px);
  border-radius: fn.percent-unit(3.51515px);

  &__image {
    width: fn.percent-unit(117px);
    height: fn.percent-unit(117px);
    img {
      width: 100%;
      height: 100%;
      border-top-left-radius: fn.percent-unit(3.51515px);
      border-bottom-left-radius: fn.percent-unit(3.51515px);
    }
  }
  &__desc {
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: fn.percent-unit(24px);
    line-height: 150%;
    padding-left: fn.percent-unit(24px);
    flex: 1;
    background: pales.$gray-33;
    height: 100%;
    border-top-right-radius: fn.percent-unit(3.51515px);
    border-bottom-right-radius: fn.percent-unit(3.51515px);
  }
}

.personal-list {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  height: fn.percent-unit(700px);
  align-content: flex-start;
}
