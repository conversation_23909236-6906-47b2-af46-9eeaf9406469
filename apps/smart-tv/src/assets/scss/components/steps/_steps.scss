/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  =================== STEPS ==================
/*--------------------------------------------*/

/**
* ============ STEPS ============
*
* USE SETTING
* STEPS
*
*/

/// @param settings
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/position" as posit;
@use "mixin/pseudo" as pseudo;

/// @group steps
.steps {
  &__item {
    text-align: center;

    .icon {
      @include box.box($width: fn.percent-unit(48));
      @include fonts.font-size(24);
      border: 1px solid fn.el-color($color: v-gray, $shade: 87);
      border-radius: 100%;
      margin: 0 auto;
    }

    .text {
      @include fonts.font-size(24);
      color: fn.el-color($color: v-white, $shade: base);
      line-height: 1.5;
    }

    &.focus {
      .icon {
        background-color: fn.el-color($color: v-medium-sea-green, $shade: 149);
        border-color: fn.el-color($color: v-medium-sea-green, $shade: 149);
      }
    }

    &-horizontal {
      align-items: center;
      display: flex;
      justify-content: flex-start;

      .icon + .text {
        margin-left: fn.percent-unit(16);
      }
    }
  }
  &--horizontal {
    @at-root .steps#{&} {
      align-items: center;
      display: flex;
      justify-content: center;
      padding: fn.percent-unit(26) fn.percent-unit(48);
      .steps__item {
        position: relative;
        &:not(:last-child) {
          margin-right: fn.percent-unit(16);
          padding-right: fn.percent-unit(106);

          &::after {
            @include posit.absolute(top 50% right 0);
            @include pseudo.pseudo($width: fn.percent-unit(76), $height: 1px);
            background-color: fn.el-color($color: v-white, $shade: base);
            transform: translateY(-50%);
          }
        }
        &:not(:first-child) {
          margin-left: fn.percent-unit(16);
        }
        .hidden-box {
          width: fn.percent-unit(50);
          height: fn.percent-unit(50);
        }
      }
    }
  }
}
