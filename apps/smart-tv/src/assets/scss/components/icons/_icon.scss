@use "sass:meta";

////
/// @group icon
////

/// @group setting
///

/// @param palettes
@use "settings/palettes" as palettes;

/// @param variables
@use "settings/variables" as variables;

/// @param function
@use "settings/function" as fn;

/// @group functions icon
@function icon-size($name) {
  //$value
  $value: ();
  // Vet the value when passed an argument
  $value: map-get(
    $map: variables.$icon-size,
    $key: $name,
  );

  // check value
  @if $value {
    @return $value;
  } @else {
    @error ('Could not find a size for `#{$name}`');
  }
}

/// @mixin icon

@mixin icon(
  $size: basic,
  $color: palettes.$white,
  $border-radius: 0,
  $bg-color: null
) {
  $icon-size: fn.percent-unit(icon-size($size));
  $icon-size-child: fn.percent-unit(icon-size($size) - 4);

  display: flex;
  align-items: center;
  justify-content: center;
  height: $icon-size;
  width: $icon-size;

  @if meta.type-of($color) == "color" {
    color: $color;
  }

  @if meta.type-of($bg-color) == "color" {
    background-color: $bg-color;
  }
  @if $border-radius > 0 {
    border-radius: fn.percent-unit($border-radius);
  }

  ///
  .vie {
    @if meta.type-of($color) == "color" {
      color: $color;
    }
    fill: currentColor;
    stroke-width: 0;
    stroke: currentColor;
    transition: color 0.2s;
    font-size: $icon-size-child;
    height: $icon-size-child;
    width: $icon-size-child;
  }
}

.icon {
  @include icon;
}
