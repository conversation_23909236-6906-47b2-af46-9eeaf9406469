////
/// @group Form
////

/// @param palettes
@use "settings/palettes" as pales;

/// @group variables
@use "settings/variables" as var;

/// @group function
@use "settings/function" as fn;

.form {
  @at-root form#{&} {
    &:not(:last-child) {
      margin-bottom: fn.percent-unit(24);
    }
    .input {
      // align-items: center;
      // border: fn.percent-unit(2) solid #646464;
      // border-radius: fn.percent-unit(2);
      // color: #646464;
      // display: flex;
      // font-size: fn.percent-unit(28);
      // height: fn.percent-unit(66);
      // min-width: fn.percent-unit(456);
      // padding: fn.percent-unit(4) fn.percent-unit(24);
      // font-weight: 300;
      // background: pales.$gray-22;

      .placeholder {
        -webkit-text-security: none;
      }

      &.focus {
        border-color: #3ac882;
        color: pales.$white;
      }
      &.active {
        border-color: #3ac882;
        color: pales.$white;
      }
      &.invalid {
        color: pales.$white;
        border-color: #e74c3c;
      }
      &.completed {
        color: pales.$white;
        border-color: #646464;
      }
      &.disabled {
        color: #333333;
        border-color: #333333;
      }
      &.warning {
        border-color: pales.$warning;
      }
      &.password {
        -webkit-text-security: disc;
        font-size: fn.percent-unit(80);
      }
    }
  }
}

.form--light {
  @at-root .form#{&} {
    .input {
      color: #9b9b9b;

      &.focus {
        border-color: #3ac882;
        color: #9b9b9b;
      }
      &.active {
        border-color: #3ac882;
        color: pales.$black;
      }
      &.completed {
        color: pales.$black;
        border-color: #646464;
      }
      &.invalid {
        color: pales.$black;
        border-color: #e74c3c;
      }
      &.disabled {
        color: #cccccc;
        border-color: #cccccc;
      }
    }
  }
}
