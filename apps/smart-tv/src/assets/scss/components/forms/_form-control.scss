/// @param settings
@use "settings/palettes" as pales;
// @use 'settings/variables' as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/position" as posit;
// @use 'mixin/pseudo' as pseudo;

/// @group form-control

.form-control {
  @include box.box($width: 100%, $height: initial);
  @include fonts.font-family("roboto-regular");
  @include fonts.font-size(28);
  appearance: none;
  color: fn.el-color($color: v-black, $shade: base);
  background-clip: padding-box;
  background-color: fn.el-color($color: v-white, $shade: base);
  border: 1px solid fn.el-color($color: v-gray, $shade: input-border);
  border-radius: fn.percent-unit(4);
  caret-color: fn.el-color($color: v-medium-sea-green, $shade: 149);
  display: block;
  line-height: 1.5;
  padding: fn.percent-unit(6) fn.percent-unit(20);
  transition:
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out,
    outline 0.15s ease-in-out,
    border-radius 0.15s ease-in-out;
  will-change: border-color, box-shadow, outline, border-radius;

  &::placeholder {
    color: fn.el-color($color: v-gray, $shade: 61);
  }

  &:focus {
    border-color: fn.el-color($color: v-medium-sea-green, $shade: 149);
    border-radius: fn.percent-unit(2);
    box-shadow: 0 0 fn.percent-unit(3) fn.percent-unit(3)
      rgba(fn.el-color($color: v-medium-sea-green, $shade: 149), 0.53);
    outline: fn.el-color($color: v-medium-sea-green, $shade: 149) 0 solid;
  }

  &.disable {
    background-color: transparent;
  }
  &.focus {
    border-color: fn.el-color($color: v-medium-sea-green, $shade: 149);
    border-radius: fn.percent-unit(2);
    box-shadow: 0 0 fn.percent-unit(3) fn.percent-unit(3)
      rgba(fn.el-color($color: v-medium-sea-green, $shade: 149), 0.53);
    outline: fn.el-color($color: v-medium-sea-green, $shade: 149) 0 solid;
  }
}
