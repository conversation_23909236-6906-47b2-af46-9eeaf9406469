////
/// @group nav
////

/// @param palettes
@use "settings/palettes" as palettes;

/// @group variables
@use "settings/variables" as var;

/// @group function
@use "settings/function" as fn;

.custom-textarea-control {
  border: none;
  overflow: auto;
  outline: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  resize: none;
  width: 100%;
  background-color: transparent;
  border: fn.percent-unit(2px) solid palettes.$gray-64;
  padding: fn.percent-unit(14px);
  margin: fn.percent-unit(3px);

  &.focus {
    border: fn.percent-unit(5px) solid palettes.$white;
    border-radius: fn.percent-unit(2px);
    margin: 0;
  }
}
