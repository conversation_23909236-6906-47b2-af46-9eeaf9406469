////
/// @group icon
////

/// @group setting
///

/// @param palettes
@use "settings/palettes" as palettes;

/// @param variables
@use "settings/variables" as var;

/// @param function
@use "settings/function" as fn;

/// @group tags

@mixin progress($height: fn.percent-unit(8)) {
  background-color: rgba(#ccc, 0.5);
  width: 100%;
  height: $height;

  &-meter {
    height: 100%;
    display: block;
    position: relative;
    background-color: #3ac882;
    .bar {
      height: fn.percent-unit(12px);
      opacity: 1;
      background: linear-gradient(
        90deg,
        #63d681 0%,
        #52d085 53.33%,
        #3ac88a 100%
      );
      position: relative;
      &.point::after {
        display: block;
        content: "";
        width: fn.percent-unit(24px);
        height: fn.percent-unit(24px);
        border-radius: 50%;
        position: absolute;
        top: 50%;
        right: 0;

        border: fn.percent-unit(4px) solid palettes.$white;
        transform: translate(50%, -50%);
        background-color: palettes.$green-3a;
      }
    }
  }
}
