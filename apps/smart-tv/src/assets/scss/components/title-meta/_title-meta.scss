/// @group setting
// @use 'settings/variables' as var;
@use "../../settings/function" as fn;
@use "../../settings/palettes" as palettes;

/// @group mixin
@use "../../mixin/box" as box;

.title-meta {
  display: flex;
  flex-flow: column;
  width: 100%;
  box-sizing: border-box;
  &__wrap {
    display: flex;
    flex-flow: row wrap;
    height: fn.percent-unit(163);
    & > * {
      flex: 0 0 auto;
    }
  }
  &__img {
    max-width: fn.percent-unit(164);
    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
  &__content {
    max-width: fn.percent-unit(664);
  }
  &__text {
    color: palettes.$white;
    font-size: fn.percent-unit(48);
    font-weight: 400;
    line-height: 1.2;
    margin-bottom: fn.percent-unit(14);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  &__schedule {
    color: palettes.$white;
    font-size: fn.percent-unit(28);
    font-weight: 400;
    line-height: 1.25;
    display: flex;
  }
}

.title-meta--live {
  @at-root .title-meta#{&} {
    align-items: flex-start;
    justify-content: center;
    height: 100%;
    transform: translateY(-60px);
    .title-meta__wrap {
      display: flex;
      flex-flow: row wrap;
    }
    .title-meta__img {
      @include box.box(
        $width: fn.percent-unit(150),
        $height: fn.percent-unit(83)
      );
      & + .title-meta__content,
      & ~ .title-meta__content {
        margin-left: fn.percent-unit(28);
        padding-left: fn.percent-unit(28);
        border-left: 2px solid palettes.$white;
      }
    }
  }
}

.title-meta--livestream {
  @at-root .title-meta#{&} {
    position: absolute;
    top: fn.percent-unit(243px);
    left: fn.percent-unit(144px);
    .title-meta__wrap {
      display: flex;
      flex-flow: column nowrap !important;
    }
    .title-meta__tags {
      display: flex;
      flex-flow: row nowrap;
      margin-bottom: fn.percent-unit(22px);
    }

    .title-meta__title-card {
      img {
        height: fn.percent-unit(244px);
        width: auto;
      }
    }
    .title-meta__description {
      width: fn.percent-unit(566px);
      height: fn.percent-unit(108px);
      font-size: fn.percent-unit(24px);
      line-height: 1.5;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .title-meta__time-count {
      width: fn.percent-unit(566px);
      font-size: fn.percent-unit(24px);
      line-height: 1.5;

      display: flex;
      flex-direction: row;
      margin-bottom: fn.percent-unit(10px);
    }

    .title-meta__tag-live {
      background: linear-gradient(90deg, #ed0303 0.01%, #ff6262 99.99%);
      height: fn.percent-unit(32px);
      width: fn.percent-unit(83px);
      position: relative;
      &::before {
        content: "\2022";
        font-weight: bold;
        font-size: fn.percent-unit(45px);
        position: absolute;
        top: fn.percent-unit(-9px);
        left: fn.percent-unit(9px);
      }

      &::after {
        content: "LIVE";
        font-weight: bold;
        font-size: fn.percent-unit(24px);
        line-height: fn.percent-unit(28px);
        display: flex;
        align-items: center;
        padding-left: fn.percent-unit(25px);
      }
    }
    .title-meta__tag-view {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-style: normal;
      font-weight: bold;
      font-size: 24px;
      line-height: 28px;
      margin-left: fn.percent-unit(15px);

      .vie {
        transform: translateY(fn.percent-unit(7px));
      }
    }
  }
}
