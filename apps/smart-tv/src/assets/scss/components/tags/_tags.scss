@use "sass:math";

////
/// @group icon
////

/// @group setting
///

/// @param palettes
@use "../../settings/palettes" as palettes;

/// @param variables
@use "settings/variables" as var;

/// @param function
@use "../../settings/function" as fn;

/// @param mixin
@use "mixin/background" as bg;

/// @group tags

/// @function tags
@function tags-size($name) {
  //$value is map
  $val: ();

  // Get the value when passed an argument
  $val: map-get(
    $map: var.$tags-size,
    $key: $name,
  );

  //
  // Check value
  @if $val {
    @return $val;
  } @else {
    @error ('Could not find a tags size for `#{$name}`');
  }
}

/// @group tags
@mixin tags($size, $text-color: palettes.$white) {
  // set value
  $tags-font-size: fn.percent-unit(tags-size($size));
  // --tags-font-size: #{$tags-font-size};
  $tags-padding: math.div($tags-font-size, 4) math.div($tags-font-size, 2);
  // @debug "Debug variable padding-y for tags #{calc($tags-font-size / 4)}.";
  // @debug "Debug variable padding-x for tags #{calc($tags-font-size / 4)}.";
  // Styles
  // background-color: $bg-color;
  // color: $text-color;
  font-size: $tags-font-size;
  padding: $tags-padding;
  line-height: 1;
  position: relative;

  &-group {
    $tags-group-gutter: 1.25vw;
    // @debug "Debug variable margin-x for tags-group #{calc(var(--tags-group-gutter) / -2)}.";
    margin: 0 math.div($tags-group-gutter, -2);
  }
}

/// @group tags-color mixin
@mixin tags-color(
  $text-color,
  $bg-color,
  $gradients: false,
  $gradients-dir: null,
  $gradients-type: null,
  $gradients-color: null
) {
  @at-root .tags#{&} {
    ///
    @include bg.backgrounds(color, $bg-color);

    ///
    @if $gradients !=false {
      @include bg.backgrounds(
        $properties: gradient,
        $value: $gradients-type,
        $grad-dir: $gradients-dir,
        $color: $gradients-color
      );
    }

    color: $text-color;
  }
}

.tags {
  @include tags($size: basic);

  sup {
    top: -0.375em;
    text-decoration: underline;
  }

  &-group {
    display: flex;
    flex-flow: row wrap;
    box-sizing: border-box;
    align-items: center;
    &.recommended {
      width: fn.percent-unit(575);
    }
    .tag.tag--vip,
    .tag.tag--early-access {
      height: fn.percent-unit(28px);
      line-height: 1;
      margin-left: 0.625vw;

      & > img {
        height: 100%;
        width: auto;
      }
    }
  }

  &-flex {
    display: flex;
    flex-direction: row;
  }

  &-flex-center {
    align-items: center;
  }
}
