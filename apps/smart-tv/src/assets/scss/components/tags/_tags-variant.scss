/// @group setting
// @use 'settings/variables' as var;
@use "settings/function" as fn;
@use "settings/palettes" as pales;

.variant {
  @at-root .tags#{&} {
    display: flex;
    flex-flow: column;

    & > span {
      font-weight: 900;
      text-shadow: 0 1px 2px 0 rgba(pales.$black, 0.25);
      line-height: 1.2;
      text-align: center;
    }

    &:not(.tags--small) {
      padding: fn.percent-unit(12) fn.percent-unit(8);
      & > span {
        font-size: fn.percent-unit(17);
        &:last-child {
          font-size: fn.percent-unit(40);
        }
      }
    }
    &.tags--small {
      padding: fn.percent-unit(8) fn.percent-unit(6);
      & > span {
        font-size: fn.percent-unit(14);
        &:last-child {
          font-size: fn.percent-unit(27);
        }
      }
    }
    &.crescent-bottom-left {
      &:not(.tags--small) {
        border-radius: 0 0 0 90%/0 0 0 fn.percent-unit(8);
      }
      &.tags--small {
        border-radius: 0 0 0 90%/0 0 0 fn.percent-unit(6);
      }
    }
  }
}
