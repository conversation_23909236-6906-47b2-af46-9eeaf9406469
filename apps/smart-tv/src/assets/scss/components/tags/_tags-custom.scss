/// @group setting
// @use 'settings/variables' as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
// @use '../../mixin/fonts';
// @use 'mixin/position' as position;
@use "mixin/pseudo" as pseudo;
@use "mixin/background" as bg;
@use "tags" as tags;

///@group style
.tags--live {
  @at-root .tags#{&} {
    display: flex;
    align-items: center;
    // content: "Live";
    text-transform: uppercase;
    font-family: "Roboto Bold", sans-serif;
    font-size: fn.percent-unit(24);

    &:before {
      @include pseudo.pseudo(
        $width: fn.percent-unit(10),
        $height: fn.percent-unit(10)
      );
      // @include box.box($width: 0, $height: 0);
      display: inline-block;
      background-color: palettes.$white;
      border-radius: 100%;
      margin-right: fn.percent-unit(4);
    }
  }
}

.tags--view-count {
  @at-root .tags#{&} {
    $icon-size: fn.percent-unit(32);
    // @include fonts.font-size(24); this update after payment ui.
    line-height: 1.16;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    .icon {
      @include box.box($width: #{$icon-size});

      .vie {
        @include box.box(
          $width: fn.percent-unit(26),
          $height: fn.percent-unit(16)
        );
        font-size: fn.percent-unit(26);
      }

      & + .text,
      & ~ .text {
        margin-left: fn.percent-unit(8);
      }
    }

    .text {
      color: palettes.$white;
      font-size: fn.percent-unit(24);
    }
  }
}

.tag--vip,
.tag--early-access {
  padding: fn.percent-unit(2) fn.percent-unit(6);
  font-weight: 700;
  font-size: fn.percent-unit(18);
  color: #fff;
  background: linear-gradient(90deg, #da9e1c 0%, #ecbd57 100%);
  text-shadow: 0px 1px 2px rgba(77, 77, 77, 0.5);
  display: flex;
  align-items: center;
  line-height: normal;
}

.tag--early-access {
  background: #da9e1c;
  &.green {
    background: #5bcd27;
  }
  &.info {
    height: fn.percent-unit(24px);
    margin-bottom: fn.percent-unit(12px);
    font-size: fn.percent-unit(13px);
    text-transform: uppercase;
  }
}

.tag--live,
.tag--premiere {
  @at-root .tag#{&} {
    $redGradient:
      #ed0303 0,
      #ff6262 100%;
    display: flex;
    align-items: center;
    text-transform: uppercase;
    font-family: "Roboto Bold", sans-serif;
    font-size: fn.percent-unit(18);
    padding: fn.percent-unit(4) fn.percent-unit(8) fn.percent-unit(4)
      fn.percent-unit(9);
    color: #fff;
    line-height: 1;
    @include bg.backgrounds(
      $properties: gradient,
      $value: linear,
      $grad-dir: to right,
      $color: $redGradient
    );

    &:before {
      @include pseudo.pseudo(
        $width: fn.percent-unit(7.5),
        $height: fn.percent-unit(7.5)
      );
      display: inline-block;
      background-color: palettes.$white;
      border-radius: 100%;
      margin-right: fn.percent-unit(4);
    }
  }
}

.tag--epg-channel-player {
  @at-root .tag#{&} {
    color: palettes.$white;
    display: flex;
    align-items: center;
    text-transform: uppercase;
    font-family: "Roboto Bold", sans-serif;
    font-size: fn.percent-unit(18);
    padding: fn.percent-unit(4) fn.percent-unit(8) fn.percent-unit(4)
      fn.percent-unit(9);
    color: #fff;
    line-height: 1;
    @include bg.backgrounds(
      $properties: gradient,
      $value: linear,
      $grad-dir: to right
    );

    &:before {
      @include pseudo.pseudo(
        $width: fn.percent-unit(7.5),
        $height: fn.percent-unit(7.5)
      );
      display: inline-block;
      border-radius: 100%;
      background-color: palettes.$white;
      margin-right: fn.percent-unit(4);
    }
  }
}

.msg--remaining-time {
  color: palettes.$warning;
  display: flex;
  align-items: center;
  font-weight: 700;
  font-size: fn.percent-unit(20);
  line-height: fn.percent-unit(26);
  margin-top: fn.percent-unit(16px);
  &.notSameTextColor {
    color: palettes.$white;
  }

  .icon {
    margin-right: fn.percent-unit(12px);

    .vie {
      color: palettes.$warning;
      width: fn.percent-unit(32px);
      height: fn.percent-unit(32px);
    }
  }
}

.tag--remaining-time {
  bottom: fn.percent-unit(13px);
  left: 50%;
  transform: translate(-50%, 0);
  padding: fn.percent-unit(3.5) 0;
  width: fn.percent-unit(152px);
  text-align: center;
  background-color: palettes.$warning;
  color: #523b08;
  font-weight: 700;
  font-size: fn.percent-unit(13px);
  line-height: 1.2;
  white-space: nowrap;
}

.tag--released-time {
  color: palettes.$green-3a;
  display: flex;
  align-items: center;
  font-size: fn.percent-unit(24px);
  font-weight: 700;
  line-height: 1.2;
  margin-top: fn.percent-unit(16px);

  .icon {
    margin-right: fn.percent-unit(12px);

    .vie {
      color: palettes.$green-3a;
      width: fn.percent-unit(32px);
      height: fn.percent-unit(32px);
    }
  }
}

.tag--tvod-price {
  background-color: #da9e1c;
  padding: 0 fn.percent-unit(13px);
  color: #fff;
  font-weight: 700;
  line-height: 1;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  font-size: fn.percent-unit(22px);
  height: fn.percent-unit(fn.tags-height(billboard));

  .price-unit {
    font-size: fn.percent-unit(11px);
    font-weight: 500;
  }

  // &.tag-billboard {
  // }

  &.tag-ribbon {
    font-size: fn.percent-unit(18px);
    height: fn.percent-unit(fn.tags-height(ribbon));
    top: fn.percent-unit(13px);
    left: 0;

    .price-unit {
      font-size: fn.percent-unit(9px);
    }
  }

  &.tag-video-intro {
    .price-unit {
      font-size: fn.percent-unit(15px);
    }
  }
}

.msg--start-time {
  color: #3ac882;
  display: flex;
  align-items: center;
  font-size: fn.percent-unit(24px);

  .bold {
    font-weight: 700;
  }

  .icon {
    margin-right: fn.percent-unit(14);

    .vie {
      color: #3ac882;
    }
  }
}
