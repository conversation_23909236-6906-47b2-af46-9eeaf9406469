/// @group setting
@use "settings/palettes" as palette;

/// @use tags
@use "tags" as tags;

/// @styles tags-gold
.tags {
  ///For color not background
  &--for {
    /// Font color on dark background
    &-dark {
      color: palette.$gray-cc;
    }
    /// Font color on light background
    &-light {
      color: palette.$gray-11;
    }
  }

  /// Glass dark
  &--glass-dark {
    background-color: rgba(palette.$black, 0.5);
  }
  /// Green color
  &--green {
    @include tags.tags-color($text-color: palette.$white, $bg-color: #04a614);
  }
  /// Gold color
  &--gold {
    @include tags.tags-color($text-color: palette.$white, $bg-color: #da9e1c);

    &-gradient {
      $listGold:
        palette.$white 0,
        palette.$black 100%;
      @include tags.tags-color(
        $text-color: palette.$white,
        $bg-color: #da9e1c,
        $gradients: true,
        $gradients-dir: to right,
        $gradients-type: linear,
        $gradients-color: $listGold
      );
    }
  }

  &--red {
    @include tags.tags-color($text-color: palette.$white, $bg-color: #ed0303);
    &-gradient {
      $redGradient:
        #ed0303 0,
        #ff6262 100%;
      @include tags.tags-color(
        $text-color: palette.$white,
        $bg-color: #ed0303,
        $gradients: true,
        $gradients-dir: to right,
        $gradients-type: linear,
        $gradients-color: $redGradient
      );
    }
  }

  &--green {
    @include tags.tags-color($text-color: palette.$white, $bg-color: #04a614);
  }
}
