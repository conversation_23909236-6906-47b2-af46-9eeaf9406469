/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/position" as position;
@use "mixin/pseudo" as pseudo;

///@group style
.tags--line {
  @at-root .tags#{&} {
    &-right {
      &:not(:last-child) {
        &::before {
          @include position.absolute(top 50% right 0);
          @include pseudo.pseudo(
            $width: 1px,
            $height: fn.percent-unit(20),
            $display: block
          );
          background-color: palettes.$white;
          transform: translateY(-50%);
        }

        // &[data-direction="vertical"] {
        //   &::before {
        //   }
        // }
      }
    }
  }
}
