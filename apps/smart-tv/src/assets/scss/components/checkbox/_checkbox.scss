/// @param settings
@use "settings/palettes" as pales;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/position" as posit;
@use "mixin/pseudo" as pseudo;

///
/// @group Checkbox
.checkbox {
  label {
    @include box.box($width: auto, $height: auto);
    @include fonts.font-family("roboto-medium");
    @include fonts.font-size(20);
    margin: 0;
    padding-top: fn.percent-unit(4);
    padding-bottom: fn.percent-unit(4);
    padding-left: fn.percent-unit(34);
  }

  .checkmark {
    @include box.box($width: fn.percent-unit(26));
    @include posit.absolute(top fn.percent-unit(2) left fn.percent-unit(4));
    align-items: center;
    display: flex;
    justify-content: center;
    padding-bottom: fn.percent-unit(2);
    box-sizing: border-box;
    border-width: fn.percent-unit(3);

    &::after {
      @include posit.absolute(top fn.percent-unit(5) left fn.percent-unit(2));
      position: initial;
      top: initial;
      left: initial;
    }

    // &::before {
    //   @include posit.absolute(top fn.percent-unit(-3) left fn.percent-unit(-3));
    //   @include pseudo.pseudo-options(
    //     $content: "",
    //     $display: block,
    //     $width: fn.percent-unit(26),
    //     $height: fn.percent-unit(26)
    //   );
    // }
  }

  ///
  &.available {
    label {
      color: pales.$green-3a;

      input:checked {
        & + .checkmark,
        & ~ .checkmark {
          background-color: pales.$green-3a;
          border-color: rgba(pales.$green-3a, 0.3);

          &::before {
            background-color: rgba(pales.$green-3a, 0.3);
          }
        }
      }
    }
  }

  &--circle {
    .checkmark {
      border-radius: 100%;
      &::before {
        border-radius: 100%;
      }
    }
  }
}
