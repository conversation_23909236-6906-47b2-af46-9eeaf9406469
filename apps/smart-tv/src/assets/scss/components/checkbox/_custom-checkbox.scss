////
/// @group nav
////

/// @param palettes
@use "../../settings/palettes" as palettes;

/// @group variables
@use "settings/variables" as var;

/// @group function
@use "settings/function" as fn;

.custom-checkbox-control {
  display: block;
  position: relative;
  height: fn.percent-unit(28px);
  width: fn.percent-unit(28px);
  margin-top: fn.percent-unit(5px);
}

/* Hide the browser's default checkbox */
.custom-checkbox-control input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: fn.percent-unit(24px);
  width: fn.percent-unit(24px);
  background-color: transparent;
  border: fn.percent-unit(2px) solid palettes.$gray-64;

  &.focus {
    border-color: palettes.$gray-33;
  }
}

/* On mouse-over, add a grey background color */
//   .custom-checkbox-control:hover input ~ .checkmark {
//     // background-color: red;
//   }

/* When the checkbox is checked, add a blue background */
.custom-checkbox-control input:checked ~ .checkmark {
  background-color: palettes.$green-0a;
  border-color: palettes.$green-0a;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.custom-checkbox-control input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.custom-checkbox-control .checkmark:after {
  left: fn.percent-unit(9px);
  top: fn.percent-unit(5px);
  width: fn.percent-unit(5px);
  height: fn.percent-unit(10px);
  border: solid white;
  border-width: 0 fn.percent-unit(3px) fn.percent-unit(3px) 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
