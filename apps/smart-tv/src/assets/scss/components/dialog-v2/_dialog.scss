@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

.dialog-v2 {
  width: 100vw;
  height: 100vh;
  position: absolute;
  z-index: 99;
  top: 0;
  left: 0;
  background-color: palettes.$gray-11;
  background-size: cover;

  &__inner {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding-left: fn.percent-unit(237px);
    position: relative;
  }

  &__title {
    font-size: fn.percent-unit(48px);
    font-weight: 500;
    line-height: 1.16;
    color: palettes.$white;

    &--payment-require {
      margin-bottom: fn.percent-unit(36);
    }
  }

  &__description,
  &__or {
    font-size: fn.percent-unit(28px);
    line-height: 1.5;
    color: palettes.$gray-de;
  }

  &__or {
    width: fn.percent-unit(612px);
    text-align: center;
  }

  &__title + &__description {
    margin-top: fn.percent-unit(36px);
  }

  &__button-wrapper {
    width: fn.percent-unit(612px);
    margin-top: fn.percent-unit(60px);
    display: flex;

    &--vertical {
      flex-direction: column;

      .btn + .btn {
        margin-top: fn.percent-unit(24);
      }
    }

    &--group-two-button {
      & > .btn {
        width: fn.percent-unit(294px);
        padding: 0;
      }

      .btn + .btn {
        margin-left: fn.percent-unit(24px);
      }
    }

    &--payment-require {
      & > .divider {
        color: palettes.$gray-de;
        font-size: fn.percent-unit(28);
        margin-bottom: fn.percent-unit(20);
        line-height: 1.5;
      }

      & > .btn {
        margin-bottom: fn.percent-unit(20);
      }
    }
  }

  &__device-list {
    font-size: fn.percent-unit(32px);
    line-height: 150%;
    color: palettes.$white;
    font-weight: 500;

    & > .item + .item {
      margin-top: fn.percent-unit(12px);
    }
  }

  &__description + &__device-list {
    margin-top: fn.percent-unit(36px);
  }

  &__button-wrapper + &__or {
    margin-top: fn.percent-unit(38px);
  }

  &__or + &__button-wrapper {
    margin-top: fn.percent-unit(16px);
  }

  &--anonymous-user_vip_dialog,
  &--anonymous-user_quality-vip_dialog,
  &--anonymous-user_sub-audio-vip_dialog,
  &--anonymous-user_vip-hbo_dialog,
  &--anonymous-user_vip-kplus_dialog,
  &--anonymous-user_family_dialog,
  &--anonymous-user_sport_dialog,
  &--non-vip-user_vip_dialog,
  &--non-vip-user_quality-vip_dialog,
  &--non-vip-user_sub-audio-vip_dialog,
  &--non-vip-user_vip-hbo_dialog,
  &--non-vip-user_vip-kplus_dialog,
  &--vip-user_vip-hbo_dialog,
  &--vip-user_vip-kplus_dialog,
  &--vip-kplus-user_vip-hbo_dialog,
  &--vip-hbo-user_vip-kplus_dialog,
  &--anonymous-user_free-trial_dialog,
  &--non-vip-user-free-trial_dialog {
    .dialog-v2__inner {
      justify-content: initial;
      margin-top: fn.percent-unit(768px);
      padding-left: fn.percent-unit(119px);
    }

    .btn {
      min-width: fn.percent-unit(520px);
      white-space: nowrap;
    }
  }

  &--anonymous-user_require-sign-up,
  &--anonymous-user_discount_require-login-voucher,
  &--anonymous-user_discount_require-login-voucher-vieon {
    .dialog-v2__inner {
      justify-content: initial;
      margin-top: fn.percent-unit(423px);
      padding-left: fn.percent-unit(248px);
    }

    .btn {
      min-width: fn.percent-unit(294px);
      white-space: nowrap;
    }
  }

  &--anonymous-user_payment_require-login {
    .btn {
      width: fn.percent-unit(520px);
      white-space: nowrap;
    }
  }

  &--tvod {
    .dialog-v2__title {
      max-width: fn.percent-unit(760);
      padding-right: fn.percent-unit(55);
    }

    .dialog-v2__button-wrapper {
      margin-top: 36px;
    }

    .dialog-v2__background {
      height: 100%;
      width: 100%;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      &::before {
        content: "";
        position: absolute;
        left: -1px;
        top: 0;
        height: 100%;
        background: linear-gradient(
          90deg,
          rgba(17, 17, 17, 0.99) 0%,
          rgba(17, 17, 17, 0) 100%
        );
        width: fn.percent-unit(210);
      }
    }

    .dialog-v2__image {
      width: fn.percent-unit(540);
      height: fn.percent-unit(773);

      img {
        position: relative;
        z-index: 1;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      &::before {
        content: "";
        position: absolute;
        left: -1px;
        top: 0;
        height: 100%;
        background: linear-gradient(
          90deg,
          rgba(17, 17, 17, 0.99) 0%,
          rgba(17, 17, 17, 0) 100%
        );
        width: fn.percent-unit(210);
      }
    }
  }

  &--tvod-welcome,
  &--tvod-single {
    display: flex;

    .dialog-v2__inner ~ .dialog-v2__inner {
      padding-left: 0;
      flex: 0 0 fn.percent-unit(960) !important;
      display: flex;
      height: 100%;
      justify-content: center;
      align-items: center;
    }

    .dialog-v2__description {
      font-weight: 600;

      .remain-time {
        display: flex;
        align-items: center;

        .vie {
          margin-right: fn.percent-unit(5);
        }
      }

      b {
        color: #ffffff;
      }

      .list-item {
        display: flex;
        font-size: fn.percent-unit(32px);
        font-weight: 400;
        line-height: 1.2;
        margin-bottom: fn.percent-unit(12px);
        color: palettes.$white;

        .icon {
          margin-right: fn.percent-unit(12px);

          .vie {
            color: palettes.$warning;
          }
        }
      }
    }
  }

  &--tvod-multi {
    background: url("../../../images/tvod-reminder-bg.png") 100% / cover
      no-repeat;
    // background: url("/src/assets/images/tvod-reminder-bg.png") 100% / cover
    //   no-repeat;

    .dialog-v2__inner {
      display: block;
      margin-top: fn.percent-unit(280);

      &.max-item {
        margin-top: fn.percent-unit(103);
      }
    }

    .dialog-v2__button-wrapper {
      margin-top: 24px;
    }

    .dialog-v2__title {
      max-width: 1100px;
      padding-right: fn.percent-unit(100);
    }

    .sect {
      position: relative;
      margin-right: fn.percent-unit(110);
      margin-top: 60px;
    }

    .btn {
      white-space: nowrap;
      margin-right: fn.percent-unit(24);
      min-width: fn.percent-unit(294px);
    }

    .card--poster {
      &.card {
        width: fn.percent-unit(238px);
        height: fn.percent-unit(340px);

        .card__thumbnail {
          width: fn.percent-unit(238px);
          height: fn.percent-unit(340px);

          & > .card__img {
            width: 100%;
            height: 100%;

            .bg-box {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background-repeat: no-repeat;
              background-position: center top;
              background-size: cover;
              z-index: -1;
            }

            & > img {
              width: 100%;
              height: 100%;
            }
          }
        }

        &:not(:last-child) {
          margin-right: fn.percent-unit(8px);
          margin-bottom: fn.percent-unit(8px);
        }
      }
    }

    .card-focus-box {
      width: fn.percent-unit(232px);
      height: fn.percent-unit(334px);
      position: absolute;
      top: fn.percent-unit(0px);
      left: 0;
      border: 4px solid palettes.$white;
    }
  }
}
