/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ================== COMMENT =================
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* VARIABLE
* INPUT STYLES
*
*/

/// @group variables
@use "../../settings/function" as fn;

/// @group mixin
@use "../../mixin/box" as box;
// @use '../../mixin/position' as position;

/// @group Button-Variables

.comment {
  &__content {
    padding-left: fn.percent-unit(16);
    padding-right: fn.percent-unit(16);
  }
  &__user-name {
    font-size: fn.percent-unit(20);
    color: #cccccc;
    margin-bottom: fn.percent-unit(8);
  }
  &__text {
    font-size: fn.percent-unit(20);
    color: #ffffff;
    margin: 0;
    line-height: 1.25;
  }
  &__pin {
    display: flex;
    justify-content: flex-end;
    margin-bottom: fn.percent-unit(4);
    .icon {
      @include box.box($width: fn.percent-unit(20));

      .vie {
        @include box.box($width: fn.percent-unit(20));
        font-size: fn.percent-unit(20);
        color: #3ac882;
      }
    }
  }

  &__time {
    color: #ffffff;
    font-size: fn.percent-unit(20);
    text-align: right;
  }

  &__item {
    padding: fn.percent-unit(16);
    opacity: 0;
    &:not(:last-child) {
      margin-bottom: fn.percent-unit(10);
    }
    &:nth-last-child(5) {
      opacity: 0.1;
    }
    &:nth-last-child(4) {
      opacity: 0.35;
    }
    &:nth-last-child(3) {
      opacity: 0.75;
    }
    &:nth-last-child(2),
    &:nth-last-child(1) {
      opacity: 1;
    }
  }
}
