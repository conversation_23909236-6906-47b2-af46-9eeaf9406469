@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

.disclamer {
  $seagreen: #3ac882;
  width: 100%;
  height: fn.percent-unit(44px);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $seagreen;
  color: #fff;
  font-size: fn.percent-unit(24px);
  .icon {
    margin-right: fn.percent-unit(12px);
  }
  &--payment-region {
    border: none;
    background-color: palettes.$gray-22;
    color: $seagreen;
    .icon .vie {
      color: $seagreen;
    }
  }
}
