@use "../../settings/function" as fn;
@use "../../settings/palettes" as palettes;

#tip-premieme,
.tip-premieme {
  z-index: 8;
  position: absolute;
  bottom: fn.percent-unit(70px);
  right: fn.percent-unit(40px);
  .tip-premieme-row {
    display: flex;
    align-items: center;
    color: palettes.$white;
    font-size: fn.percent-unit(24px);
    text-shadow: fn.percent-unit(7px) fn.percent-unit(3px) fn.percent-unit(15px)
      black;
    .shortcut {
      margin: 0 fn.percent-unit(8px);
      height: fn.percent-unit(44px);
      & > img {
        height: 100%;
        width: auto;
      }
    }
    &.fade-in {
      opacity: 0;
    }
    &.fade-in-enter {
      transition: all 0.5s ease-in-out !important;
    }
    &.fade-in-enter-active {
      opacity: 1;
    }
    &.fade-in-enter-done {
      opacity: 1;
    }
    &.fade-in-exit {
      transition: all 0.5s ease-in-out !important;
    }
    &.fade-in-exit-active {
      opacity: 0;
    }
    &.fade-in-exit-done {
      opacity: 0;
    }
  }
}
