@use "../../settings/function" as fn;
@use "../../settings/palettes" as palettes;
/// @group mixin
@use "mixin/position" as position;
@use "mixin/pseudo" as pseudo;
////
/// @param properties
////
@use "animates/properties" as prop;

#tip-box,
.tip-box {
  z-index: 8;
  position: absolute;
  bottom: fn.percent-unit(30px);
  right: fn.percent-unit(40px);

  .tip-box-row {
    display: flex;
    align-items: center;
    color: palettes.$white;
    font-size: fn.percent-unit(24px);
    text-shadow: fn.percent-unit(7px) fn.percent-unit(3px) fn.percent-unit(15px)
      black;

    .shortcut {
      margin: 0 fn.percent-unit(8px);
      height: fn.percent-unit(44px);

      & > img {
        height: 100%;
        width: auto;
      }
    }

    &.fade-in {
      opacity: 0;
    }

    &.fade-in-enter {
      transition: all 0.5s ease-in-out !important;
    }

    &.fade-in-enter-active {
      opacity: 1;
    }

    &.fade-in-enter-done {
      opacity: 1;
    }

    &.fade-in-exit {
      transition: all 0.5s ease-in-out !important;
    }

    &.fade-in-exit-active {
      opacity: 0;
    }

    &.fade-in-exit-done {
      opacity: 0;
    }
  }

  &.tip-box--reverse {
    .tip-box-row {
      color: #000;
      text-shadow: none;
    }
  }

  &--favorite {
    background-color: rgba(0, 0, 0, 0.6);
    clip-path: polygon(fn.percent-unit(94px) 0, 100% 0, 100% 100%, 0 100%);
    padding-left: fn.percent-unit(94px);
    bottom: 0;
    right: 0;

    &--no-bg {
      background-color: transparent;
      clip-path: none;

      .tip-box__wrap {
        &::before {
          display: none !important;
        }
      }
    }

    .tip-box__wrap {
      padding: fn.percent-unit(32) fn.percent-unit(94) fn.percent-unit(32)
        fn.percent-unit(32);

      &::before {
        @include pseudo.pseudo(
          $width: fn.percent-unit(100%),
          $height: fn.percent-unit(7)
        );
        @include position.absolute(top 0 left 0);
        background-color: rgba(palettes.$white, 0.2);
      }

      ul {
        margin: 0;
        padding: 0;
        list-style: none;

        li {
          list-style-type: none;
          font-size: fn.percent-unit(24px);
          color: palettes.$white;
          display: flex;
          flex-flow: row wrap;
          justify-content: flex-end;
          align-items: center;

          img {
            margin-left: fn.percent-unit(8);
            margin-right: fn.percent-unit(8);
            width: fn.percent-unit(44px);
            height: fn.percent-unit(36px);
          }

          &:not(:last-child) {
            margin-bottom: fn.percent-unit(18);
          }
        }
      }
    }

    &.fade-in {
      opacity: 0;
    }

    &.fade-in-enter {
      transition: all 0.5s ease-in-out !important;
    }

    &.fade-in-enter-active {
      opacity: 1;
    }

    &.fade-in-enter-done {
      opacity: 1;
    }

    &.fade-in-exit {
      transition: all 0.5s ease-in-out !important;
    }

    &.fade-in-exit-active {
      opacity: 0;
    }

    &.fade-in-exit-done {
      opacity: 0;
    }
  }
}
