////
/// Global style for Emulator
////

/// @group variables
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palette;

/// @group mixin
@use "mixin/position" as position;
@use "mixin/box" as box;

.emulator {
  margin: 0 auto;
  border-radius: fn.percent-unit(12);

  img {
    max-width: 100%;
    @include box.box(100%, 100%);
  }

  .code--view {
    $spacing: fn.percent-unit(10);
    @include box.box(fn.percent-unit(558), fn.percent-unit(152));
    display: flex;

    .code__item {
      @include box.box(auto, 100%);
      align-items: center;
      background-color: palette.$gray-29;
      color: palette.$white;
      display: flex;
      font-size: fn.percent-unit(70);
      font-weight: 400;
      justify-content: center;
      // margin: 0 $spacing;
      border-radius: fn.percent-unit(5);
      flex: 1 1 0px;
      &:not(:first-child) {
        margin-left: $spacing;
      }
      &:not(:last-child) {
        margin-right: $spacing;
      }
    }
  }
  .code--confirm {
    @include position.absolute(bottom fn.percent-unit(200) left 50%);
    @include box.box(fn.percent-unit(558), fn.percent-unit(100));
    background-color: palette.$gray-11;
    transform: translateX(-50%);
  }
}
