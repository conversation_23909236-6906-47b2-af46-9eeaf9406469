////
/// Global style for Emulator Phone
////

/// @group variables
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palette;

/// @group mixin
@use "mixin/position" as position;
@use "mixin/box" as box;

%emulatorGlobal {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.emulator--phone {
  @at-root .emulator#{&} {
    @extend %emulatorGlobal;
    height: fn.percent-unit(834);
    width: fn.percent-unit(674);

    .code--view {
      @include position.absolute(bottom fn.percent-unit(332) left 50%);
      transform: translateX(-50%);
    }
  }
}

.emulator--laptop {
  @at-root .emulator#{&} {
    @extend %emulatorGlobal;
    height: fn.percent-unit(715);
    width: fn.percent-unit(1264);

    .code--view {
      @include position.absolute(
        bottom fn.percent-unit(332) left fn.percent-unit(348)
      );
    }
  }
}
