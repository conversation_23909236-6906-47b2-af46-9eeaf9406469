////
/// @group nav
////

/// @param palettes
@use "settings/palettes" as palettes;

/// @group variables
@use "settings/variables" as var;

/// @group function
@use "settings/function" as fn;

/// @group mixin
@use "mixin/position" as position;
@use "mixin/font-size" as font-size;
@use "mixin/pseudo" as pseudo;
@use "mixin/layer" as layer;
@use "mixin/background" as bg;

/// @group animated
@use "../../animates/properties" as prop;
@use "../../animates/fade-exits/fadeInOut" as fio; //use fade in then out
@use "../../animates/slides/slideInLeft" as slideInLeft; //use fade out left right

///
.screen-saver {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  z-index: 20;
  &:before {
    position: absolute;
    content: " ";
    width: 100%;
    height: 100%;
    background: palettes.$black;
  }

  // &__wrap {
  // }

  &__item {
    &-wrap {
      display: flex;
      width: 100%;
      height: 100%;
    }
  }

  &__backdrop {
    @include position.absolute(left);
    width: 128%;
    height: 128%;
    display: flex;
    z-index: fn.z-index(layer-1);
    transform: translateY(-50%);
    @include slideInLeft.slideInLeft($delay: 0s, $duration: 13s);

    &.ads {
      width: 100%;
      height: 100%;
      animation: none;
      transform: translateY(0);
    }

    &:before {
      position: absolute;
      content: " ";
      width: 100%;
      height: 100%;
      $darkGlassGradient:
        rgba(palettes.$gray-11, 1),
        rgba(palettes.$gray-11, 0) 50%;
      @include bg.backgrounds(
        $properties: gradient,
        $value: linear,
        $grad-dir: to top,
        $color: $darkGlassGradient
      );
    }

    img {
      width: 100%;
      max-width: 100%;
      max-height: 100%;
      min-height: 100%;
    }
  }

  &__content {
    @include position.absolute(
      bottom fn.percent-unit(220) left fn.percent-unit(150)
    );
    z-index: fn.z-index(layer-2);
    max-width: fn.percent-unit(768);
    width: fn.percent-unit(768);
  }

  &__title {
    margin-bottom: fn.percent-unit(16px);
    height: fn.percent-unit(224px);
    @include fio.fadeInOut($delay: 3s, $duration: 9s);

    & > img {
      height: 100%;
      width: auto;
    }
  }

  &__desc {
    font-size: fn.percent-unit(36);
    display: -webkit-box;
    width: 100%;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.25;
    color: palettes.$white;
    font-weight: 400;
    @include fio.fadeInOut($delay: 4s, $duration: 7s);
  }

  .ranking {
    height: fn.percent-unit(32px);
    display: flex;
    align-items: center;
    margin-top: fn.percent-unit(16px);
    margin-bottom: fn.percent-unit(16px);
    font-size: fn.percent-unit(24px);
    font-weight: 400;

    .tag-icon {
      height: fn.percent-unit(32px);
      width: fn.percent-unit(32px);
      margin-right: fn.percent-unit(16px);

      .vie {
        height: 100%;
        width: 100%;
        // color: palettes.$green-3a;
      }
    }
  }
}
