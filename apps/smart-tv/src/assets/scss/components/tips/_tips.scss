/// @group variables
// @use 'settings/variables' as var;
@use "settings/function" as fn;
@use "settings/palettes" as pales;

/// @group mixin
@use "mixin/background" as bg;

.tips {
  // &__wrap {
  // }
  // &__union {
  // }
  &__content {
    color: pales.$white;
    font-size: fn.percent-unit(32);
    text-align: center;
    line-height: 1.2;

    img {
      max-width: fn.percent-unit(232);
    }

    .remote-red-button {
      $listRed:
        #ed0303 0,
        #ff6262 100%;

      width: fn.percent-unit(44);
      height: fn.percent-unit(36);
      display: inline-flex;
      border-radius: 3px;
      @include bg.backgrounds(
        $properties: gradient,
        $value: linear,
        $grad-dir: to right,
        $color: $listRed
      );
    }
  }
  &__title {
    font-size: fn.percent-unit(48);
    margin-top: fn.percent-unit(18);
  }
}

.tip-livetv {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center; /* <PERSON><PERSON>n gi<PERSON>a theo chiề<PERSON> ngang */
  // align-items: center; /* Căn giữa theo chiều dọc */
  align-items: flex-end; /* Căn giữa theo chiều dọc */
  top: 0;
  z-index: 10;

  .hide-button {
    position: absolute;
    right: fn.percent-unit(260);
    bottom: fn.percent-unit(68);
  }

  img {
    position: absolute;
    left: fn.percent-unit(260);
    bottom: fn.percent-unit(0);
    max-height: fn.percent-unit(672);
    height: auto;
  }

  p {
    position: absolute;
    left: 0;
    bottom: fn.percent-unit(
      30
    ); /* Cách đáy màn hình 10px, bạn có thể điều chỉnh giá trị này */
    text-align: center; /* Căn giữa văn bản */
    width: fn.percent-unit(100%);
    font-size: fn.percent-unit(28);
    color: white; /* Màu chữ, bạn có thể thay đổi theo ý muốn */
    font-weight: 400;
    line-height: 150%;
  }
}
