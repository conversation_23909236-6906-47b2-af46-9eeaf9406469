/// @group variables
// @use 'settings/variables' as var;
@use "settings/function" as fn;
@use "settings/palettes" as pales;

/// @group mixin
@use "mixin/position" as position;
@use "mixin/pseudo" as pseudo;

////
/// @param properties
////
@use "animates/properties" as prop;

.tips--tour-guide {
  @at-root .tips#{&} {
    @include position.fixed(right 0 bottom 0);
    background-color: rgba(pales.$black, 0.6);
    clip-path: polygon(fn.percent-unit(94) 0, 100% 0, 100% 100%, 0 100%);
    padding-left: fn.percent-unit(94);
    z-index: fn.z-index(layer-9);
    overflow: hidden;
    //
    &-live {
      clip-path: none;
      padding-left: 0;
    }
    .tips__wrap {
      position: relative;
      display: flex;
      flex-flow: row nowrap;
      padding: fn.percent-unit(32) fn.percent-unit(32) fn.percent-unit(32)
        fn.percent-unit(32);

      &::before {
        @include pseudo.pseudo(
          $width: fn.percent-unit(100%),
          $height: fn.percent-unit(7)
        );
        @include position.absolute(top 0 left 0);
        background-color: rgba(pales.$white, 0.2);
      }

      .icon {
        width: fn.percent-unit(175);
        height: fn.percent-unit(175);
        @include position.absolute(top 50% right fn.percent-unit(32));
        @include prop.transform(translateY(-50%));

        img {
          max-width: 100%;
          height: auto;
          max-height: 100%;
        }

        & + ul,
        & ~ ul {
          padding-right: fn.percent-unit(198);
        }
      }

      ul {
        margin: 0;
        padding: 0;
        list-style: none;
        li {
          list-style-type: none;
          font-size: fn.percent-unit(24px);
          color: pales.$white;
          display: flex;
          flex-flow: row wrap;

          img {
            margin-left: fn.percent-unit(8);
            margin-right: fn.percent-unit(8);
          }

          &:not(:last-child) {
            margin-bottom: fn.percent-unit(18);
          }
        }
      }
    }
  }
}

.tips--tour-move {
  @at-root .tips#{&} {
    .tips__wrap {
      padding-top: fn.percent-unit(64);
      padding-bottom: fn.percent-unit(64);

      ul {
        li {
          align-items: center;
          img {
            width: fn.percent-unit(60);
            height: fn.percent-unit(60);
          }

          &:not(:last-child) {
            margin-bottom: fn.percent-unit(18);
          }
        }
      }
    }
  }
}

.tips--tour-add {
  @at-root .tips#{&} {
    .tips__wrap {
      padding-right: fn.percent-unit(94);
      ul {
        li {
          align-items: center;
          justify-content: flex-end;
          img {
            width: fn.percent-unit(44);
            height: fn.percent-unit(36);
          }
        }
      }
    }
  }
}

.tips--tour-premiere {
  width: fn.percent-unit(803);
  height: fn.percent-unit(328);
  @at-root .tips#{&} {
    position: absolute;
    .tips__wrap {
      padding: 0;
      font-size: fn.percent-unit(28px);
      line-height: 150%;
      color: pales.$white;
      height: 100%;
      align-items: center;
      justify-content: center;
      img {
        width: fn.percent-unit(170);
        height: fn.percent-unit(170);
        padding-left: fn.percent-unit(35);
      }
    }
  }
}

.tips--tour-premiere-live {
  width: fn.percent-unit(897);
  height: fn.percent-unit(328);
  &.tips.tips--tour-guide {
    background-color: transparent;
  }
  .tip-premiere-bg {
    width: 100%;
    display: block;
  }
  @at-root .tips#{&} {
    position: absolute;
    .tips__wrap {
      position: absolute;
      display: block;
      top: fn.percent-unit(104);
      left: fn.percent-unit(285);
      padding: 0;
      font-size: fn.percent-unit(28px);
      font-weight: 400;
      line-height: 150%;
      color: pales.$white;
      height: 100%;
      // align-items: center;
      // justify-content: center;
      // img {
      //   width: fn.percent-unit(40);
      //   height: fn.percent-unit(40);
      //   padding-left: fn.percent-unit(12);
      //   padding-right: fn.percent-unit(12);
      // }
      &::before {
        display: none;
      }
    }
  }
}
