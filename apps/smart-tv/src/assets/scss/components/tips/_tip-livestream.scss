/// @group variables
// @use 'settings/variables' as var;
@use "settings/function" as fn;
@use "settings/palettes" as pales;

/// @group mixin
@use "mixin/position" as position;

.tips--livestream {
  @at-root .tips#{&} {
    @include position.absolute(top 0 right 0 bottom 0 left 0);
    padding-left: fn.percent-unit(480);
    padding-bottom: fn.percent-unit(370);
    display: flex;
    height: 100vh;
    width: 100vw;
    box-sizing: border-box;
    z-index: 1;
    .tips__wrap {
      display: flex;
      flex-flow: row wrap;
      margin-right: fn.percent-unit(-10);
      margin-left: fn.percent-unit(-10);
      align-items: flex-end;

      & > * {
        flex: 0 0 auto;
        width: auto;
        margin-right: fn.percent-unit(10);
        margin-left: fn.percent-unit(10);
      }
      .tips__content {
        padding-bottom: fn.percent-unit(72);
        p {
          margin-bottom: 0;
        }
      }
    }

    &-not-assess {
      padding-left: fn.percent-unit(464);
      pointer-events: none;
      .tips__wrap {
        // padding-bottom: fn.percent-unit(48);
        .tips__union {
          max-width: fn.percent-unit(500);
          img {
            width: 100%;
            height: auto;
          }
        }
        .tips__content {
          padding-bottom: fn.percent-unit(44);
          text-align: left;

          img {
            margin-left: fn.percent-unit(-72);
            min-width: fn.percent-unit(720);
            max-width: fn.percent-unit(840);
          }
        }
      }
    }
  }
}
