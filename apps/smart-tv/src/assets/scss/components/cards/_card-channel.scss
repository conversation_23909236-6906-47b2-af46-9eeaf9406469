/// @param settings
@use "settings/palettes" as pales;
@use "settings/variables" as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/truncated" as truncate;

/// @group components
@use "components/progress/progress" as progress;

/// @group extended-focus
%focus {
  &.focus,
  &.active {
    background-color: pales.$white;
    .card__schedule,
    .card__title,
    .card__time-remain__text {
      color: pales.$gray-22;
    }
  }
}
/// @group card-channel
.card--channel {
  @at-root .card#{&} {
    background-color: pales.$gray-22;
    // display: flex;
    // flex-flow: row nowrap;
    .card__thumbnail {
      // @include box.box($width: fn.percent-unit(276), $height: fn.percent-unit(156));
      overflow: hidden;
      text-align: center;
      background-color: pales.$gray-22;
      flex: 0 0 auto img {
        max-width: 100%;
        max-height: 100%;
      }
      // & + .card__section,
      // & ~ .card__section {
      // }
    }
    .card__schedule {
      font-size: fn.percent-unit(24);
      color: pales.$gray-cc;
      margin-bottom: fn.percent-unit(8);
      line-height: 1.25;
    }
    .card__title {
      font-size: fn.percent-unit(32);
      color: pales.$white;
      font-weight: 500;
    }
    .progress {
      @include progress.progress($height: fn.percent-unit(6));
    }

    .card__section {
      padding: fn.percent-unit(12) fn.percent-unit(16);
      box-sizing: border-box;
      display: flex;
      flex-flow: column;
      max-width: fn.percent-unit(582);
    }

    .card__time-remain {
      align-items: center;
      display: flex;
      box-sizing: border-box;
      flex-flow: row wrap;

      & > * {
        &:first-child {
          flex: 1 1 0px;
        }
        &:last-child {
          flex: 0 0 auto;
        }
      }
      &__text {
        font-size: fn.percent-unit(20);
        font-weight: 700;
        color: pales.$white;
        margin-left: fn.percent-unit(24);
      }

      .progress {
        transform: none;
      }
    }

    &.card--horizontal {
      border: none;
      .card__thumbnail {
        @include box.box(
          $width: fn.percent-unit(276),
          $height: fn.percent-unit(156)
        );
        flex: 0 0 auto;
        img {
          width: 100%;
          height: 100%;
        }
      }
      @extend %focus;
    }

    @extend %focus;
    &:not(.card--channel-upcoming) {
      .card__title {
        @include truncate.truncated($line: 1);
        margin-bottom: fn.percent-unit(28);
        height: fn.percent-unit(38);
      }
    }
  }
}
.card--channel-upcoming {
  @at-root .card#{&} {
    @include box.box(fn.percent-unit(411), $height: fn.percent-unit(156));
    .card__title {
      @include truncate.truncated($line: 2, $line-height: 1.4);
    }
    &:not(:last-child) {
      margin-right: fn.percent-unit(12);
    }
    &.card--horizontal {
      .card__section {
        @include box.box($width: 100%, $height: 100%);
      }
    }
  }
}
