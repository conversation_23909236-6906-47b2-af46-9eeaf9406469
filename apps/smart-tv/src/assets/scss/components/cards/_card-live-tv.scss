/// @group variables
// @use 'settings/variables' as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;
/// @group mixin
@use "mixin/position" as posit;

.card--live-tv {
  @at-root .card#{&} {
    width: fn.percent-unit(300px);
    height: fn.percent-unit(168.7px);
    position: relative;

    &:not(:last-child) {
      margin-right: fn.percent-unit(16px);
    }

    .card__thumbnail {
      width: fn.percent-unit(300px);
      height: fn.percent-unit(168.7px);

      .card__img {
        width: 100%;
        height: 100%;

        & > img {
          width: 100%;
          height: 100%;
        }
      }

      .card__title-livetv {
        background: linear-gradient(
          0deg,
          rgba(17, 17, 17, 0.5) 0.81%,
          rgba(17, 17, 17, 0) 100.05%
        );
        height: fn.percent-unit(101px);
        width: 100%;
        position: absolute;
        bottom: 0;

        // transform: matrix(1, 0, 0, -1, 0, 0);
        span {
          font-size: fn.percent-unit(20);
          margin: fn.percent-unit(16);
          font-weight: 500;
          color: palettes.$white;
          position: absolute;
          bottom: 0;
          line-height: 130%;
        }
      }
    }
  }
}
