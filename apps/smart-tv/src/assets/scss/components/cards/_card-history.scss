/// @group variables
// @use 'settings/variables' as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

.card--service-history {
  @at-root .card#{&} {
    position: relative;
    padding-right: 2em;
    margin-bottom: 2em;
    .card-thumbnail {
      position: relative;
      padding-bottom: 56.25%;
      height: 0;
      width: 100%;
      overflow: hidden;
      border: 0.25em solid #646464;
      border-radius: 0.75em;
      img {
        z-index: 1;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
        max-height: initial;
      }
      &:before {
        z-index: 2;
        content: "";
        display: block;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
      }
    }
    .card-title {
      font-size: 1.75em;
      color: palettes.$white;
      text-transform: capitalize;
      margin-bottom: 0.375em;
      text-shadow: 0 1px 2px rgba(palettes.$black, 0.2);
      text-overflow: ellipsis;
      word-break: break-word;
      overflow: hidden;
      max-height: 2.5em;
      line-height: 1.2em;
      display: block;
      margin-top: 0;
      & + .text {
        font-size: 1.5em;
        color: palettes.$white;
        font-family: "Roboto", sans-serif;
        text-shadow: 0 0 2px rgba(palettes.$black, 0.2);
        margin: 0;
      }
    }
    .card-body {
      padding: 1.5em;
      z-index: 3;
      top: 0;
      &.full-x {
        right: 0;
        left: 0;
      }
      &.bottom {
        bottom: 0;
      }
      .flex {
        display: flex;
        flex-flow: row wrap;
        &.align-bottom {
          align-items: flex-end;
        }
        .flex-item {
          flex: 0 0 auto;
          width: 100%;
          &.auto {
            flex: 1 1;
          }
          &.shrink {
            flex: 0 0 auto;
            width: auto;
          }
        }
      }
    }
    .button {
      width: 9em;
      height: 2em;
      font-size: 1.5em;
      background: rgba(0, 0, 0, 0.5);
      border: 2px solid palettes.$gray-9b;
      color: palettes.$white;
      position: absolute;
      bottom: 0.5em;
      left: 20%;
    }
    .p {
      font-size: 1.5em;
      color: palettes.$white;
      position: absolute;
      bottom: 0.5em;
      left: 1em;
      right: 1em;
      padding: 0;
      margin: 0;
    }

    &.focus,
    &.active {
      .card-thumbnail {
        border: 0.25em solid palettes.$white;
        outline: 0em solid palettes.$white;
      }
      .button {
        // background-color: palettes.$white;
        color: palettes.$gray-33;
        background: palettes.$white;
        border-color: palettes.$white;
      }
    }
  }
}
