/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  =============== CARD PAYMENT ===============
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* STYLES
*
*/

/// @param settings
@use "settings/palettes" as pales;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/position" as position;
@use "mixin/pseudo" as pseudo;

/// @group style
.card--payment {
  @at-root .card#{&} {
    &.focus--effect {
      @include position.relative;
      &::before {
        @include position.absolute(
          top fn.percent-unit(-10px) left fn.percent-unit(-10px)
        );
        @include pseudo.pseudo-options(
          $content: "",
          $display: block // $height: 0
        );
        @include box.box($width: calc(100% + #{fn.percent-unit(8px)}));
        border: fn.percent-unit(6) solid pales.$green-3a;
        border-radius: fn.percent-unit(10);
        //////////////
        // Old logic temp remove
        //$shadow-color: rgba(
        //  fn.el-color(
        //    $color: v-medium-sea-green,
        //    $shade: 149,
        //  ),
        //  0.55
        //);
        //box-shadow: fn.percent-unit(4) 0 fn.percent-unit(4) $shadow-color,
        //  fn.percent-unit(-4) 0 fn.percent-unit(4) $shadow-color,
        //  0 fn.percent-unit(4) fn.percent-unit(4) $shadow-color,
        //  0 fn.percent-unit(-4) fn.percent-unit(4) $shadow-color;
        //////////////
        opacity: 0;
        // transition: opacity 0.5s, transform 0.35s, height 0.35s;
        // will-change: transform, opacity, height;
      }

      // &:not(.up) {
      //   &::before {
      //     // transform: translateY(-10%);
      //   }
      // }

      // &.up {
      //   &::before {
      //     // transform: translateY(-110%);
      //   }
      // }

      &.focus {
        &::before {
          // @include box.box($width: calc(100% + 8px));
          opacity: 1;
          // transform: translate(-50%, -50%);
          // transform: translateY(0);
        }

        &.up {
          &::before {
            transform: translateY(0);
          }
        }

        &-within {
          &::before {
            opacity: 0.5;
          }
        }
      }
    }
  }
  &.card--payment-method {
    &.focus--effect {
      &::before {
        background: rgb(pales.$green-3a, 20%);
        @include position.absolute(
          top fn.percent-unit(-1px) left fn.percent-unit(-2px)
        );
        @include box.box($width: calc(100% + #{fn.percent-unit(0px)}));
        border-radius: fn.percent-unit(4);
        border: fn.percent-unit(2) solid pales.$green-3a;
      }
    }
  }
}
