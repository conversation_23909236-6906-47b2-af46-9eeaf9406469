/// @group variables
// @use 'settings/variables' as var;
@use "../../settings/function" as fn;
@use "../../settings/palettes" as palettes;

///
.card--live-stream,
.card--live-stream-page {
  @at-root .card#{&} {
    width: fn.percent-unit(300px);
    height: fn.percent-unit(168.7px);
    position: relative;

    &:not(:last-child) {
      margin-right: fn.percent-unit(16px);
    }

    .card__thumbnail {
      width: fn.percent-unit(300px);
      height: fn.percent-unit(168.7px);

      .card__img {
        width: 100%;
        height: 100%;

        & > img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

.card--is-coming-soon {
  @at-root .card#{&} {
    height: fn.percent-unit(270px);
  }
}
