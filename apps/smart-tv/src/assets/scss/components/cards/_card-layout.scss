////
/// Global style for Card
////

/// @group variables
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

[data-layout="horizontal"] {
  @at-root .card#{&} {
    %flexibleBox {
      // flex-grow: 1;
      // flex-shrink: 1;
      // flex-basis: 0px;
      flex: 0 0 auto;
    }
    display: flex;
    flex-flow: row wrap;
    align-items: flex-start;

    .card__thumbnail {
      @extend %flexibleBox;
    }
    .card__section {
      @extend %flexibleBox;
    }
  }
}
