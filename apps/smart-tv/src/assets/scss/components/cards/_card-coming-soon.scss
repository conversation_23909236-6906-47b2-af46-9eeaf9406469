/// @group variables
// @use 'settings/variables' as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

.card--coming-soon {
  @at-root .card#{&} {
    width: fn.percent-unit(230px);
    height: fn.percent-unit(328px);
    position: relative;

    &:not(:last-child) {
      margin-right: fn.percent-unit(16px);
    }

    .card__thumbnail {
      width: fn.percent-unit(230px);
      height: fn.percent-unit(328.57px);

      .card__img {
        width: 100%;
        height: 100%;

        & > img {
          width: 100%;
          height: 100%;
        }
      }

      .card__tag-box {
        .tag-noti {
          color: palettes.$black;
          background-color: palettes.$white;
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: fn.percent-unit(52px);
          transform: translateY(100%);
          display: flex;
          justify-content: center;
          align-items: center;

          .icon {
            margin-right: fn.percent-unit(12px);

            .vie {
              color: palettes.$black;
              transition: none;
            }
          }

          .text {
            font-size: fn.percent-unit(26px);

            &.pre-order {
              font-size: fn.percent-unit(20px);
              font-weight: 400;

              b {
                font-weight: 700;
              }
            }

            &.payment-order {
              color: palettes.$green-3a;
            }
          }

          &.subscribed {
            color: palettes.$green-3a;

            .icon .vie {
              color: palettes.$green-3a;
            }
          }

          // &.simulcast {
          // }
        }
      }
    }
  }
}
