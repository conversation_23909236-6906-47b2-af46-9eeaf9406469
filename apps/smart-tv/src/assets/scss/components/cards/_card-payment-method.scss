/// @param settings
@use "settings/palettes" as pales;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/position" as posit;
@use "mixin/pseudo" as pseudo;

///
/// @group Badges

.card--payment-method {
  @at-root .card--payment#{&} {
    /// @group Extended
    %contentStyle {
      color: pales.$black;
      line-height: 1.5;
      //overflow: hidden;
      //text-overflow: ellipsis;
      //white-space: nowrap;
    }

    /// Style card-method
    @include box.box(
      $width: fn.percent-unit(100%),
      //$height: fn.percent-unit(98)
      $height: auto
    );
    min-height: fn.percent-unit(98);
    border: fn.percent-unit(2) solid pales.$gray-9b;
    border-radius: fn.percent-unit(6);
    padding: fn.percent-unit(8) fn.percent-unit(32);
    margin-bottom: fn.percent-unit(18px) !important;
    /// Set margin bottom to item not last-child
    // &:not(:last-child) {
    //   margin-bottom: fn.percent-unit(18);
    // }

    .card__container {
      flex: 1;
      min-width: 0;
      align-items: center;
      display: flex;
      flex-flow: row wrap;
    }

    .card__info-box {
      width: fn.percent-unit(100%);

      .content {
        padding: fn.percent-unit(13) 0;

        span {
          color: pales.$gray-33;
          font-weight: 400;
          font-size: fn.percent-unit(24);
        }
      }
    }

    .card__thumbnail {
      display: flex;
      align-items: center;
      justify-content: left;

      .card__img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .card__title {
      @extend %contentStyle;
      //@include box.box($width: 100%, $height: fn.percent-unit(42));
      @include fonts.font-size(26);
      @include fonts.font-family("roboto-medium");
      margin: 0;
    }

    .card__decs {
      @extend %contentStyle;
      @include fonts.font-size(24);
      @include fonts.font-family("roboto-regular");

      &.highlight {
        //background: -webkit-linear-gradient(45deg, #6830b4, #c93186 80%);
        //-webkit-background-clip: text;
        //-webkit-text-fill-color: transparent;
        color: fn.el-color($color: v-sky-blue, $shade: payment);
      }
    }

    // &::before{

    // }

    ///
    &.card--horizontal,
    .card__container {
      flex: 1;
      min-width: 0;
      align-items: center;

      & > :not(:last-child) {
        margin-right: fn.percent-unit(24);
      }

      ///
      .card__thumbnail {
        flex: 0 0 auto;
        @include box.box(
          $width: fn.percent-unit(84),
          $height: fn.percent-unit(48)
        );
      }

      .card__thumbnail-shopeepay {
        justify-content: flex-start;
      }

      ///
      .card__section {
        @include box.box($width: fn.percent-unit(532), $height: auto);
        flex: 1;
      }
    }

    ///
    // &.focus {
    //   border-color: pales.$green-3a;
    //   $shadow-color: rgba(fn.el-color($color: v-medium-sea-green, $shade: 149), 0.55);
    //   box-shadow: 3px 0 3px $shadow-color, -3px 0 3px $shadow-color, 0 3px 3px $shadow-color, 0 -3px 3px $shadow-color;
    // }

    ///
    &.disabled {
      opacity: 0.35;
    }

    &.card--napas {
      // height: fn.percent-unit(147);
      height: auto;
      align-items: flex-start;
      min-height: fn.percent-unit(98);
      padding-top: fn.percent-unit(16);
      padding-bottom: fn.percent-unit(16);

      .card__title_napas {
        font-size: fn.percent-unit(28);
        font-family: "Roboto Medium", sans-serif;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        height: fn.percent-unit(66);
        text-overflow: ellipsis;
        overflow: hidden;
        word-wrap: break-word;
      }

      .card__token {
        display: flex;
        flex-direction: column;
        margin-top: fn.percent-unit(20);

        .napas-token-item {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          height: fn.percent-unit(52);
          margin-left: fn.percent-unit(-24);
          padding-left: fn.percent-unit(24);
          position: relative;

          .napas-token-checkbox {
            box-sizing: border-box;
            width: fn.percent-unit(24);
            height: fn.percent-unit(24);
            border: fn.percent-unit(2) solid #646464;
            border-radius: 50%;
            margin-right: fn.percent-unit(16);
          }

          .napas-token-image {
            width: fn.percent-unit(38);
            height: fn.percent-unit(24);
            margin-right: fn.percent-unit(12);

            & > img {
              width: 100%;
              height: auto;
            }
          }

          .napas-token-text {
            display: flex;
            align-items: center;
            justify-content: center;

            .dot {
              display: inline-block;
              margin-right: fn.percent-unit(4);
              width: fn.percent-unit(12);
              height: fn.percent-unit(12);
              border-radius: 50%;
              background-color: #333;

              &:nth-child(4n) {
                margin-right: fn.percent-unit(12);
              }
            }

            .text {
              font-size: fn.percent-unit(24);

              &:nth-child(4n) {
                margin-right: fn.percent-unit(12);
              }
            }
          }

          &.focused {
            &::before {
              position: absolute;
              top: fn.percent-unit(-8px);
              left: fn.percent-unit(-8px);
              content: "";
              display: block;
              width: calc(100% + #{fn.percent-unit(4)});
              height: calc(100% + #{fn.percent-unit(4)});
              border: fn.percent-unit(1) solid pales.$green-3a;
              border-radius: 0.3125vw;
              box-shadow:
                fn.percent-unit(2) 0 fn.percent-unit(2) rgb(58 200 130 / 55%),
                fn.percent-unit(-2) 0 fn.percent-unit(2) rgb(58 200 130 / 55%),
                0 fn.percent-unit(2) fn.percent-unit(2) rgb(58 200 130 / 55%),
                0 fn.percent-unit(-2) fn.percent-unit(2) rgb(58 200 130 / 55%);
            }

            .napas-token-checkbox {
              border: fn.percent-unit(6) solid #3ac882;
            }
          }

          & + .napas-token-item {
            margin-top: fn.percent-unit(16);
          }
        }
      }

      &.down {
        &::before {
          opacity: 0.5;
        }
      }
    }
  }
}
