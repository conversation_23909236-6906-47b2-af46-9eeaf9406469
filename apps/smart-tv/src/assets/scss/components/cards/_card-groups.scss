/// @group setting
// @use 'settings/variables' as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/grid" as grid;
.card-groups {
  display: flex;

  &.vertical {
    flex-flow: column;
    .card {
      &:not(:last-child) {
        margin-bottom: fn.percent-unit(20);
      }
    }
  }
  &:not(.vertical) {
    // flex-flow: row wrap;
    flex-flow: column;

    .card {
      margin-bottom: fn.percent-unit(24);
      max-width: 100%;
      flex-grow: 1;
      flex-basis: 0;
      display: flex;
      box-sizing: border-box;
    }

    // data-child-index
    // &[data-child-index="1"],
    // &[data-child-index="2"],
    // &[data-child-index="3"],
    // &[data-child-index="4"],
    // &[data-child-index="5"],
    // &[data-child-index="6"],
    // &[data-child-index="7"],
    // &[data-child-index="8"],
    // &[data-child-index="9"],
    // &[data-child-index="10"],
    // &[data-child-index="11"],
    // &[data-child-index="12"]{
    //   .card{
    //     display: flex;
    //   }
    // }

    ///
    &[data-child-index="1"] {
      .card {
        @include grid.grid($grid-type: block, $col: 1);
      }
    }
    &[data-child-index="2"] {
      .card {
        @include grid.grid($grid-type: block, $col: 2);
      }
    }
    &[data-child-index="3"] {
      .card {
        @include grid.grid($grid-type: block, $col: 3);
      }
    }
    &[data-child-index="4"] {
      .card {
        @include grid.grid($grid-type: block, $col: 4);
      }
    }
    &[data-child-index="5"] {
      .card {
        @include grid.grid($grid-type: block, $col: 5);
      }
    }
    &[data-child-index="6"] {
      .card {
        @include grid.grid($grid-type: block, $col: 6);
      }
    }
  }
}
