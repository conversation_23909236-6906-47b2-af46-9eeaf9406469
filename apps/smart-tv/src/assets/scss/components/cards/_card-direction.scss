////
/// Global style for Card
////

/// @group variables
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

// extended local
%shrinkGrid {
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0px;
  width: auto;
}

.card--accordion {
  @at-root .card#{&} {
    height: fn.percent-unit(502);
    width: fn.percent-unit(1468);
    background-color: palettes.$white;
  }
}

// .card--vertical {
//   @at-root .card#{&} {
//     // display: flex;
//     // flex-flow: row nowrap;
//   }
// }

.card--horizontal {
  @at-root .card#{&} {
    display: flex;
    flex-flow: row wrap;
    box-sizing: border-box;

    .card__thumbnail {
      @extend %shrinkGrid;
    }
    .card__section {
      @extend %shrinkGrid;
    }
  }
}
