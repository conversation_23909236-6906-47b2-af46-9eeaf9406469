/// @group variables
// @use 'settings/variables' as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

///
.card--livetv-highlight {
  @at-root .card#{&} {
    width: fn.percent-unit(516px);
    height: fn.percent-unit(290px);
    position: relative;

    &:not(:last-child) {
      margin-right: fn.percent-unit(20px);
    }

    .card__thumbnail {
      width: fn.percent-unit(516px);
      height: fn.percent-unit(290px);

      .card__img {
        width: 100%;
        height: 100%;

        & > img {
          width: 100%;
          height: 100%;
        }
      }

      .card__logo {
        position: absolute;
        top: fn.percent-unit(22px);
        right: fn.percent-unit(22px);
        width: fn.percent-unit(66px);
        height: fn.percent-unit(66px);
        border-radius: 999px;
        border: fn.percent-unit(2px) solid #fff;
        background: none;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .card__image-logo {
        background-color: #ffffff;
        height: fn.percent-unit(62px);
        width: fn.percent-unit(62px);
        border-radius: 999px;
        display: flex;
        justify-content: center;
        align-items: center;

        & > img {
          height: fn.percent-unit(43.7px);
          width: fn.percent-unit(43.7px);
        }
      }
    }

    .card__tag-box {
      .tag.tag--epgTitle {
        box-sizing: border-box;
        width: 100%;
        height: fn.percent-unit(120px);
        padding: fn.percent-unit(20px);
        padding-top: fn.percent-unit(40px);

        .epg-title {
          font-size: fn.percent-unit(32px);
          font-weight: 500;
          line-height: fn.percent-unit(37px);
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .epg-time {
          font-size: fn.percent-unit(24px);
          line-height: 1.5;
          font-weight: 500;

          &--coming-soon {
            color: palettes.$green-3a;
          }

          &--live {
            color: palettes.$red-live;
          }
        }
      }
    }
  }

  &.disabled {
    display: none;
  }
}
