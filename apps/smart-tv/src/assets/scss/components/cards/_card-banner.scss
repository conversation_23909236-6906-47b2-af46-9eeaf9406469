@use "../../../../assets/scss/settings/function" as fn;

.card.card--registration-banner {
  width: fn.percent-unit(1716);
  height: fn.percent-unit(340);
  position: relative;
  box-sizing: border-box;
  font-size: fn.percent-unit(28px);
  & * {
    box-sizing: border-box;
  }
  .card--text-btn {
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    color: white;
    width: fn.percent-unit(1100);
    padding: 0 0 0 fn.percent-unit(70);
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .card--text {
    margin: 0 0 fn.percent-unit(24);
    line-height: 150%;
    font-size: fn.percent-unit(48px);
  }
  .card--btn {
    font-size: fn.percent-unit(48px);
    background: transparent;
    border: none;
    color: #22c4e5;
    font-weight: 500;
    padding: 0;
  }
  .card--icon {
    display: inline-block;
    .vie {
      color: #22c4e5;
      width: fn.percent-unit(33px);
      height: auto;
    }
  }
  .card__img {
    width: 100%;
    height: 100%;
    & > img {
      width: 100%;
      height: 100%;
    }
  }
}

.card.card--payment-banner {
  width: fn.percent-unit(1716);
  height: fn.percent-unit(340);
  position: relative;
  box-sizing: border-box;
  font-size: fn.percent-unit(28px);
  & * {
    box-sizing: border-box;
  }
  .card--text-btn {
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    color: white;
    width: fn.percent-unit(717);
    padding: 0 0 0 fn.percent-unit(70);
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .card--text {
    margin: 0 0 fn.percent-unit(24);
    line-height: 150%;
  }
  .card--btn {
    font-size: fn.percent-unit(48px);
    background: transparent;
    border: none;
    color: #22c4e5;
    font-weight: 500;
    padding: 0;
  }
  .card--icon {
    display: inline-block;
    .vie {
      color: #22c4e5;
      width: fn.percent-unit(33px);
      height: auto;
    }
  }
  .card__img {
    width: 100%;
    height: 100%;
    & > img {
      width: 100%;
      height: 100%;
    }
  }
}

.card.card--promotion-banner {
  .card__img {
    width: 100%;
    height: 100%;
    & > img {
      width: 100%;
      height: 100%;
    }
  }
}
