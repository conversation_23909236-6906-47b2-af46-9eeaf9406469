////
/// CARD INTRO
////

/// @group variables
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/position" as position;
@use "mixin/truncated" as truncated;

/// @group card--intro
.card--intro {
  @at-root .card#{&} {
    .card__section {
      .card__desc {
        margin-bottom: 0;
        & + .card__duration,
        & ~ .card__duration {
          margin-top: fn.percent-unit(7);
          color: #cccccc;
        }
      }
      .card__cast {
        margin-top: fn.percent-unit(7);
        color: #cccccc;
      }
    }
    &[data-layout="horizontal"] {
      @include box.box($width: fn.percent-unit(100%), $height: auto);
      height: fn.percent-unit(231);
      .card__thumbnail {
        @include box.box(
          $width: fn.percent-unit(411),
          $height: fn.percent-unit(231)
        );
      }

      .card__section {
        width: fn.percent-unit(555);
        padding-left: fn.percent-unit(24);
        box-sizing: border-box;

        .card__desc {
          @include truncated.truncated(
            $line: 2,
            $line-height: 1.375,
            $height: fn.percent-unit(126)
          );
        }
        .card__cast {
          @include truncated.truncated(
            $line: 2,
            $line-height: 1.375,
            $height: fn.percent-unit(126)
          );
        }
      }
    }
  }
}
