/**
* ============ VERSION ============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ============== CARD CANDIDATE ==============
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* SETTING
* MIXIN
* VARIABLE
* STYLES
*
*/

/// @group setting
@use "../../settings/function" as fn;
@use "../../settings/palettes" as palette;

/// @group mixin
@use "../../mixin/box" as box;
@use "../../mixin/position" as position;

/// @group variable

/// @group card--candidate

.card--candidate {
  @at-root .card#{&} {
    @include box.box($width: fn.percent-unit(350), $height: auto);
    @include position.relative;
    padding-top: fn.percent-unit(72);

    &.round {
      border-radius: fn.percent-unit(8);

      .card__section {
        @include box.box($width: 100%, $height: fn.percent-unit(424));
        border-radius: fn.percent-unit(8);
        border: fn.percent-unit(6) solid #333;
      }
    }

    &.focus {
      .card__section {
        border-color: #fff;
      }
    }
    .card__section {
      @include box.box($width: 100%, $height: 100%);
      background-color: #333;
      box-sizing: border-box;
      padding: fn.percent-unit(12);
    }

    .card__title {
      color: #0ad418;
      font-size: fn.percent-unit(36);
      font-family: "Roboto Medium";
      text-align: center;
    }
    .avatar {
      &.absolute {
        @include position.absolute(top 0 left 50%);
        transform: translate(-50%, 0);
      }
      & + .card__section,
      & ~ .card__section {
        padding-top: fn.percent-unit(72);
      }
    }
    .rank {
      margin-right: fn.percent-unit(20);

      .rank__num {
        color: #ffffff;
        margin-right: fn.percent-unit(20);
      }
      .rank__sync {
        font-size: fn.percent-unit(22);
        color: #646464;
      }

      .icon {
        @include box.box($width: fn.percent-unit(16));
        .vie {
          @include box.box($width: fn.percent-unit(16));
          font-size: fn.percent-unit(16);
          color: #646464;
        }
      }

      &.up {
        .rank__sync {
          color: #0ad418;

          .icon .vie {
            color: #0ad418;
          }
        }
      }

      &.down {
        .rank__sync {
          color: #e74c3c;

          .icon .vie {
            color: #e74c3c;
          }
        }
      }
    }

    .tags {
      color: #dedede;
      font-size: fn.percent-unit(24);
      font-family: "Roboto Medium";
      text-align: center;
      margin-bottom: fn.percent-unit(4);
    }

    .vote {
      &.divide-vertical {
        &:not(:last-child) {
          &:before {
            background-color: #dedede;
          }
        }
      }
    }

    .button {
      @include box.box(
        $width: fn.percent-unit(236),
        $height: fn.percent-unit(62)
      );
      background-color: rgba(#222222, 0.7);
      border-color: #ffffff;
      box-sizing: border-box;
      color: #fff;
      font-size: fn.percent-unit(28);
      font-family: "Roboto Medium";

      & > *:not(:last-child) {
        margin-right: fn.percent-unit(20);
      }

      .icon {
        .vie {
          color: #fff;
        }

        &--play {
          padding-left: fn.percent-unit(2);
          .vie {
            font-size: fn.percent-unit(14);
            @include box.box($width: fn.percent-unit(14));
          }

          &.circle {
            border-radius: 100%;
            border: fn.percent-unit(2) solid #fff;
          }
        }
      }

      &.focus {
        background-color: #ffffff;
        color: #222222;

        .icon {
          .vie {
            color: #222222;
          }
          &.circle {
            border-color: #222;
          }
        }
      }

      &-group {
        .button {
          &:not(:last-child) {
            margin-bottom: fn.percent-unit(12);
          }
        }
      }
    }
  }

  &-champion {
    @at-root .card#{&} {
      @include box.box($width: fn.percent-unit(724), $height: auto);

      .card__thumbnail {
        // $spacing-top: fn.percent-unit(72);
        // height: calc(100% - #{$spacing-top});
        @include box.box($width: 100%, $height: fn.percent-unit(424));
        background-color: #333;
        z-index: 1;
        img {
          @include box.box($width: 100%, $height: 100%);
        }

        & + *,
        & ~ * {
          z-index: 2;
          position: relative;
        }

        & + .card__section,
        & ~ .card__section {
          background-color: transparent;
        }

        &.absolute {
          @include position.absolute(
            top fn.percent-unit(72) right 0 bottom 0 left 0
          );
        }
      }

      .avatar {
        &.absolute {
          @include position.absolute(top 0 left 50%);
          transform: translate(-50%, 0);
        }
      }

      .card__title {
        color: #ffffff;
        margin-top: fn.percent-unit(128);
        font-family: "Goldman";
        font-size: fn.percent-unit(48);
        text-shadow: 0 0 fn.percent-unit(11) #8cff00;

        &__sub {
          color: #da9e1c;
          font-size: fn.percent-unit(36);
          text-shadow: none;
        }
      }

      .vote {
        color: #da9e1c;
        font-size: fn.percent-unit(30);

        & > span {
          // font-family: "Roboto Bold";
          font-weight: bold;
        }
        &-group {
          margin-bottom: fn.percent-unit(16);
        }
      }

      .card__section {
        overflow: hidden;
        .tags {
          @include box.box(
            $width: fn.percent-unit(224),
            $height: fn.percent-unit(42)
          );
          align-items: center;
          background-image: linear-gradient(
            90.89deg,
            rgba(133, 252, 110, 0) -18.57%,
            #0ad418 44.52%,
            rgba(133, 252, 110, 0) 110.3%
          );
          display: flex;
          font-family: "Roboto";
          font-weight: bold;
          font-size: fn.percent-unit(24);
          color: #222;
          justify-content: center;
          text-transform: uppercase;
          transform: rotate(45deg);

          &.absolute {
            @include position.absolute(
              top fn.percent-unit(42) right fn.percent-unit(-54)
            );
          }
        }
      }

      &.round {
        .card__thumbnail {
          border-radius: fn.percent-unit(8);
          overflow: hidden;
        }
      }
    }
  }
}
