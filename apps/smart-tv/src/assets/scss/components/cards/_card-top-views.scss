/// @group variables
// @use 'settings/variables' as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

///
.card--top-views {
  @at-root .card#{&} {
    position: relative;

    &:not(:last-child) {
      margin-right: fn.percent-unit(27px);
    }

    .card__ranking-mask {
      position: absolute;
      top: 50%;
      left: 0;
      height: fn.percent-unit(336px);
      transform: translateY(-50%);

      & > img {
        height: 100%;
        width: auto;
      }
    }

    &[data-rank="1"] {
      width: fn.percent-unit(378px);

      .card__thumbnail {
        margin-left: fn.percent-unit(140px);
      }
    }

    &[data-rank="2"],
    &[data-rank="3"],
    &[data-rank="4"],
    &[data-rank="5"],
    &[data-rank="6"],
    &[data-rank="7"],
    &[data-rank="8"],
    &[data-rank="9"] {
      width: fn.percent-unit(418px);

      // .card__ranking-mask {
      // }
      .card__thumbnail {
        margin-left: fn.percent-unit(180px);
      }
    }

    &[data-rank="10"] {
      width: fn.percent-unit(518px);

      // .card__ranking-mask {
      // }
      .card__thumbnail {
        margin-left: fn.percent-unit(280px);
      }
    }

    .card__thumbnail {
      width: fn.percent-unit(230px);
      height: fn.percent-unit(328.57px);

      // margin-left: auto;
      & > .card__img {
        width: 100%;
        height: 100%;

        & > img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
