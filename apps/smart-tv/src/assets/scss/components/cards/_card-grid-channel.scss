/// @group variables
// @use 'settings/variables' as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;
/// @group mixin
@use "mixin/position" as posit;

.card--grid-channel {
  @at-root .card#{&} {
    height: fn.percent-unit(100px);
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background-color: #333;
    color: #fff;
    padding-left: fn.percent-unit(17.33px);
    .odr-number {
      font-weight: bold;
      font-size: fn.percent-unit(28px);
      line-height: 150%;
      width: fn.percent-unit(49px);
      margin-right: fn.percent-unit(12px);
    }
    .channel-logo {
      width: fn.percent-unit(95.33px);
      height: fn.percent-unit(71.5px);
      margin-right: fn.percent-unit(8px);
      position: relative;
      & > img {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
      }
    }
    .channel-title {
      font-size: fn.percent-unit(28px);
      line-height: 130%;
      overflow: hidden;
      width: fn.percent-unit(206px);
      height: fn.percent-unit(72px);
      margin-right: fn.percent-unit(12.33px);
      display: flex;
      align-items: center;
    }
  }
}
