/// @param settings
@use "settings/palettes" as pales;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/position" as posit;
@use "mixin/pseudo" as pseudo;

/// @group Badges
.card--payment-pack {
  @at-root .card--payment#{&} {
    @include box.box($width: 100%, $height: fn.percent-unit(332));
    position: relative;
    box-sizing: border-box;

    .card__thumbnail {
      background-color: pales.$gray-22;
      opacity: 0.9;
      border-radius: fn.percent-unit(8);
      overflow: hidden;
      position: relative;

      img {
        @include box.box($width: 100%, $height: 50%);
        display: block;
        position: relative;
      }
    }

    .checkbox {
      &.checkbox--absolute {
        @include posit.absolute(
          top fn.percent-unit(20) right fn.percent-unit(20)
        );
      }
    }

    .card__thumbnail::after {
      position: absolute;
      content: "";
      width: 100%;
      height: 50%;
      left: 0;
      top: 0;

      background: #222222;
      opacity: 0.9;
      border-radius: 8px 8px 0px 0px;
    }

    &.focus {
      .card__thumbnail::after {
        background: transparent;
      }
    }
  }
}
