////
/// Global style for Card
////

/// @group variables
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as pales;

/// @group mixin
@use "mixin/high-light" as hl;
@use "mixin/font-size" as fs;
@use "mixin/position" as position;

///
@use "components/icons/icon" as icon;

/// Style card-step
.card--step {
  @at-root .card#{&} {
    %highLight {
      @include hl.high-light($dir: pales.$green-3a);
    }
    padding: 0 fn.percent-unit(24);
    .card__header {
      @include fs.font-size(24);
      color: pales.$white;
      position: relative;

      .high-light {
        @extend %highLight;
      }

      .icon {
        @include icon.icon(
          $size: large,
          $color: pales.$black,
          $border-radius: 100%,
          $bg-color: pales.$gray-cc
        );
        @include position.absolute(top fn.percent-unit(-12) left 0);

        & + .card__text,
        & ~ .card__text {
          padding-left: fn.percent-unit(icon.icon-size(large) + 14);
        }
      }

      .card__text {
        margin: 0;
        line-height: 1.375;
      }
    }
    .card__body {
      position: relative;

      .high-light {
        @extend %highLight;
      }
    }
  }
}

/// Style card--step-login
.card--step-login {
  @at-root .card#{&} {
    .card__header {
      height: fn.percent-unit(164);
      margin-bottom: fn.percent-unit(16);
    }

    %childElement {
      @include position.absolute(top 50% right 0 left 0);
      transform: translateY(-50%);
    }
    .hand-demo-pressed {
      @include position.absolute(
        right fn.percent-unit(68) bottom fn.percent-unit(114)
      );
    }
    .img-select-smart-tv {
      @include position.absolute(top fn.percent-unit(232) right 0 left 0);
      height: auto;
      width: 100%;

      img {
        width: 100%;
        height: auto;
      }
    }
    &--web {
      .card__text {
        font-size: fn.percent-unit(40);
        color: pales.$white;
      }

      .card__body {
        .card__text {
          font-size: fn.percent-unit(164);
          letter-spacing: fn.percent-unit(52);
        }
      }
    }
  }
}
