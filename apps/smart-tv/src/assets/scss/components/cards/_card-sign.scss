/// @group setting
// @use 'settings/variables' as var;
@use "settings/function" as fn;

.card--sign {
  @at-root .card#{&} {
    &:not(.card--sign-web) {
      .emulator--phone {
        padding-top: fn.percent-unit(102);
      }
    }
    .list {
      padding-left: fn.percent-unit(64);
      padding-right: fn.percent-unit(72);
      .list__item {
        font-size: fn.percent-unit(28);

        &:not(:last-child) {
          margin-bottom: fn.percent-unit(56);
        }
      }
    }
    &-web {
      .emulator--laptop {
        padding-top: fn.percent-unit(22);
      }
    }
  }
}
