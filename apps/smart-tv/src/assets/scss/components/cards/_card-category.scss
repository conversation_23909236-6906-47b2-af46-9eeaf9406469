/// @group variables
// @use 'settings/variables' as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

///
.card--category {
  @at-root .card#{&} {
    width: fn.percent-unit(230px);
    height: fn.percent-unit(80px);
    position: relative;

    &:not(:last-child) {
      margin-right: fn.percent-unit(16px);
    }

    .card__thumbnail {
      width: 100%;
      height: 100%;

      .card__img {
        width: 100%;
        height: 100%;

        & > img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .card__text {
      position: absolute;
      top: 50%;
      left: fn.percent-unit(16px);
      width: fn.percent-unit(152px);
      transform: translate(0, -50%);
      font-size: fn.percent-unit(20px);
      font-weight: 400;
      color: palettes.$white;
      text-align: left;
      font-family: "Goldman";
    }
  }
}
