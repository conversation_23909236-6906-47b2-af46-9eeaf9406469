/// @param settings
@use "settings/variables" as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/position" as posit;
@use "mixin/pseudo" as pseudo;

/// Style
.card--ipc {
  @at-root .card#{&} {
    @include box.box(
      $width: fn.percent-unit(384),
      $height: fn.percent-unit(216)
    );
    @include posit.relative;
    box-sizing: border-box;
    margin: 0 auto;
    padding-right: fn.percent-unit(112);
    padding-bottom: fn.percent-unit(46);

    .card__header {
      @include box.box($width: 100%, $height: fn.percent-unit(24));
      box-sizing: border-box;
      padding-right: fn.percent-unit(18);
      padding-left: fn.percent-unit(18);
    }

    .card__body {
      padding-right: fn.percent-unit(18);
      padding-left: fn.percent-unit(18);

      & > * {
        &:not(:last-child) {
          margin-bottom: fn.percent-unit(20);
        }
      }
    }

    .card__section {
      @include box.box(
        $width: fn.percent-unit(272),
        $height: fn.percent-unit(172)
      );
      background-color: fn.el-color($color: v-gray, $shade: 60);
      border-radius: fn.percent-unit(12);
      box-sizing: border-box;
      padding-bottom: fn.percent-unit(20);
      padding-top: fn.percent-unit(20);

      &.front {
        @include posit.relative;
        box-shadow: fn.percent-unit(4) fn.percent-unit(4) fn.percent-unit(6)
          rgba(fn.el-color($color: v-black, $shade: base), 0.15);
        z-index: fn.z-index(layer-2);

        .card__header {
          margin-bottom: fn.percent-unit(26);
        }

        .ipc-logo {
          text-align: right;
        }
      }

      &.back {
        @include posit.absolute(bottom 0 right 0);
        padding-top: fn.percent-unit(25);
        padding-bottom: fn.percent-unit(25);
        z-index: fn.z-index(layer-1);

        .card__header {
          background-color: rgba(fn.el-color($color: v-gray, $shade: 20), 0.4);
          margin-bottom: fn.percent-unit(22);
        }
        .card__body {
          padding-left: fn.percent-unit(10);
          padding-right: fn.percent-unit(10);
        }

        .valid__thru,
        .valid__cvv {
          @include box.box(
            $width: fn.percent-unit(94),
            $height: fn.percent-unit(28)
          );
          background-color: rgba(fn.el-color($color: v-gray, $shade: 20), 0.4);
          border: 1px solid rgba(fn.el-color($color: v-gray, $shade: 87), 0.4);
          box-sizing: border-box;
          display: flex;
          align-items: center;
          padding: fn.percent-unit(4) fn.percent-unit(3);
        }
        .valid__thru {
          justify-content: center;
        }
        .valid__cvv {
          justify-content: flex-end;
        }
      }
    }

    .ipc-number {
      &__item {
        display: inline-flex;
        align-items: center;
        height: fn.percent-unit(24px);
        margin-right: fn.percent-unit(6px);
        & > span {
          @include fonts.font-family("roboto-medium");
          @include fonts.font-size(20);
          color: fn.el-color($color: v-white, $shade: base);
          display: inline-block;
          line-height: 1.2;

          &:not(:last-child) {
            margin-right: fn.percent-unit(2);
          }

          .icon {
            @include box.box($width: fn.percent-unit(10));
            align-items: center;
            .vie {
              @include fonts.font-size(10);
              @include box.box($width: fn.percent-unit(10));
              color: fn.el-color($color: v-white, $shade: base);
            }
          }
        }
      }
    }

    .valid {
      // &__item {
      // }
      &__label {
        @include fonts.font-size(17);
        color: fn.el-color($color: v-white, $shade: base);
        margin-bottom: fn.percent-unit(4);
      }
      &__thru,
      &__cvv {
        vertical-align: middle;
        height: fn.percent-unit(19px);
        & > span {
          @include fonts.font-size(18);
          color: fn.el-color($color: v-white, $shade: base);
          display: inline-block;
          line-height: 1;

          &:not(:last-child) {
            margin-right: fn.percent-unit(2);
          }

          .icon {
            @include box.box($width: fn.percent-unit(8), $height: 100%);

            align-items: center;
            .vie {
              @include fonts.font-size(8);
              @include box.box($width: fn.percent-unit(8));
              color: fn.el-color($color: v-white, $shade: base);
            }
          }
        }
      }
    }
  }
}

/// Card themes
.card--ipc-visa {
  @at-root .card#{&} {
    .card__section {
      &.front {
        background-image: linear-gradient(
          67deg,
          fn.el-color($color: v-dodger-blue, $shade: 62),
          fn.el-color($color: v-sky-blue, $shade: 80)
        );
      }
      &.back {
        background-image: linear-gradient(
          248deg,
          fn.el-color($color: v-dodger-blue, $shade: 62),
          fn.el-color($color: v-sky-blue, $shade: 80)
        );

        .card__header {
          background-color: fn.el-color($color: v-black, $shade: base);
        }

        .valid__thru,
        .valid__cvv {
          background-color: rgba(
            fn.el-color($color: v-white, $shade: base),
            0.9
          );

          & > span {
            color: fn.el-color($color: v-black, $shade: base);
          }
        }
        .valid__cvv {
          & > span .icon .vie {
            color: fn.el-color($color: v-black, $shade: base);
          }
        }
      }
    }
  }
}
///
.card--ipc-master {
  @at-root .card#{&} {
    .card__section {
      &.front {
        background-image: linear-gradient(
          270deg,
          fn.el-color($color: v-dark-orange, $shade: 89),
          fn.el-color($color: v-dark-orange, $shade: 99)
        );
      }
      &.back {
        background-image: linear-gradient(
          90deg,
          fn.el-color($color: v-dark-orange, $shade: 89),
          fn.el-color($color: v-dark-orange, $shade: 99)
        );

        .card__header {
          background-color: rgba(fn.el-color($color: v-gray, $shade: 20), 0.4);
        }

        .valid__thru,
        .valid__cvv {
          background-color: rgba(
            fn.el-color($color: v-white, $shade: base),
            0.9
          );

          & > span {
            color: fn.el-color($color: v-black, $shade: base);
          }
        }
        .valid__cvv {
          & > span .icon .vie {
            color: fn.el-color($color: v-black, $shade: base);
          }
        }
      }
    }
  }
}

.card--ipc-cake {
  @at-root .card#{&} {
    .card__section {
      &.front {
        background-image: linear-gradient(67deg, #ed0082 2.29%, #ff37a5 91.64%);
      }
      &.back {
        background-image: linear-gradient(
          248deg,
          #ff37a5 4.05%,
          #ed0082 98.93%
        );

        .card__header {
          background-color: rgba(fn.el-color($color: v-gray, $shade: 20), 0.4);
        }

        .valid__thru,
        .valid__cvv {
          background-color: rgba(
            fn.el-color($color: v-white, $shade: base),
            0.9
          );

          & > span {
            color: fn.el-color($color: v-black, $shade: base);
          }
        }
        .valid__cvv {
          & > span .icon .vie {
            color: fn.el-color($color: v-black, $shade: base);
          }
        }
      }
    }
  }
}
