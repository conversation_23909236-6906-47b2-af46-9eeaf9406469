/// @group setting
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

.card--horizontal {
  // min-width: fn.percent-unit(361px);
  // height: fn.percent-unit(203px);
  border: fn.percent-unit(6px) solid transparent;

  &.focus {
    border-color: palettes.$white;
  }
  .card__img .bg-box {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-repeat: no-repeat;
    background-position: center top;
    background-size: cover;
    z-index: -1;
  }
}
