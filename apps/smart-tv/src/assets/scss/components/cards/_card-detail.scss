////
/// Global style for Card
////

/// @group variables
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

///
.card--detail {
  %globalText {
    font-size: fn.percent-unit(20);
    color: #111111;
  }
  @at-root .card#{&} {
    .card__title {
      height: fn.percent-unit(156);
      overflow: hidden;
    }
    .card__section {
      background-color: palettes.$white;
      padding: fn.percent-unit(24);

      & > * {
        &:not(:last-child) {
          margin-bottom: fn.percent-unit(24);
        }
      }
    }

    .card__flag {
      font-size: fn.percent-unit(24);
      position: relative;
      padding-left: fn.percent-unit(48);
      padding-top: fn.percent-unit(2);
      padding-bottom: fn.percent-unit(2);

      .icon {
        position: absolute;
        top: fn.percent-unit(50%);
        left: fn.percent-unit(0);
        width: fn.percent-unit(32);
        height: fn.percent-unit(32);
        transform: translateY(-50%);
        .vie {
          font-size: fn.percent-unit(32);
          color: #3ac882;
          width: fn.percent-unit(32);
          height: fn.percent-unit(32);
        }
      }
    }

    .card__desc {
      @extend %globalText;
    }
    .card__prize {
      border-top: fn.percent-unit(1) solid #cccccc;
      padding-top: fn.percent-unit(16);
      padding-left: fn.percent-unit(50);
      position: relative;

      .icon {
        position: absolute;
        top: fn.percent-unit(14);
        left: fn.percent-unit(0);
        width: fn.percent-unit(50);
        height: fn.percent-unit(50);
        .vie {
          font-size: fn.percent-unit(42);
          color: #3ac882;
          width: fn.percent-unit(28);
          height: fn.percent-unit(42);
        }
      }

      .card__text {
        @extend %globalText;
      }
    }
  }
}
