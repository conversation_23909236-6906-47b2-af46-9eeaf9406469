/// @group variables
// @use 'settings/variables' as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

@use "mixin/pseudo" as pseudo;

.card--original {
  @at-root .card#{&} {
    position: relative;

    .tag.tag--new,
    .tag.tag--live {
      height: fn.percent-unit(40px);
    }

    .tag.tag--top {
      height: fn.percent-unit(80px);
    }

    .tag.tag--info {
      font-size: fn.percent-unit(17px);
      height: fn.percent-unit(29px);

      & > div {
        padding: 0 fn.percent-unit(8px);
        line-height: fn.percent-unit(29px);
      }
    }

    .card__thumbnail {
      .card__img {
        height: 100%;
      }

      .card__img .bg-box {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-repeat: no-repeat;
        background-position: center top;
        background-size: cover;
        z-index: -1;
      }

      .card__title-card-bottom {
        position: absolute;
        left: 0;
        right: 0;
        margin-left: auto;
        margin-right: auto;
        bottom: fn.percent-unit(55px);
        width: fn.percent-unit(275px);
        text-align: center;

        & > img {
          height: auto;
          // width: fn.percent-unit(411px);
          width: 100%;
          display: block;
        }

        z-index: 3;
      }

      .card__tag-box {
        z-index: 4;
      }

      .card__mask {
        position: absolute;
        width: 100%;
        height: 33%;
        bottom: 0;
        background: linear-gradient(
          180deg,
          rgba(0, 0, 0, 0) 0%,
          rgba(0, 0, 0, 0.8) 100%
        );
        z-index: 1;
      }
    }

    .info-box.info-box--original {
      position: absolute;
      left: fn.percent-unit(36);
      bottom: fn.percent-unit(36);
      color: palettes.$white;
      width: fn.percent-unit(604px);
      padding-top: fn.percent-unit(362px);
      z-index: 5;
      .info-box__programme-title {
        font-size: fn.percent-unit(48px);
        font-weight: 500;
        line-height: 1.3;
        margin-bottom: fn.percent-unit(28px);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 3;
      }
      .info-box__programme-time {
        font-size: fn.percent-unit(24px);
        line-height: 1.5;
        align-items: center;
        & > img {
          margin-right: fn.percent-unit(14px);
          height: fn.percent-unit(32px);
          width: auto;
        }
        &:not(:last-child) {
          margin-bottom: fn.percent-unit(32px);
        }
        .text-big {
          font-size: fn.percent-unit(28px);
        }
      }
      .card__title-card-bottom {
        position: static;
        margin: 0;
        padding-bottom: fn.percent-unit(25);
        width: fn.percent-unit(411);
      }
      .award-icon svg,
      .top-icon svg {
        color: #3ac882 !important;
      }
      .meta {
        width: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: wrap;
        margin-bottom: fn.percent-unit(26px);
        font-size: fn.percent-unit(20px);
        .tag {
          display: flex;
          margin: 0 fn.percent-unit(8px);

          &:first-child {
            margin-left: 0;
          }

          &.tag--labelPublicDay {
            color: palettes.$white;
            display: flex;
            align-items: center;
            justify-content: center;

            & > .icon-box {
              height: fn.percent-unit(16px);
              // padding-bottom: fn.percent-unit(1px);
              margin-right: 2px;

              & > img {
                height: 100%;
                width: auto;
              }
            }

            & > div {
              height: fn.percent-unit(16px);
            }
          }
        }
      }
      .ranking {
        height: fn.percent-unit(32px);
        display: flex;
        align-items: center;
        margin-bottom: fn.percent-unit(16px);
        font-size: fn.percent-unit(24px);
        font-weight: 400;
        .tag-icon {
          height: fn.percent-unit(32px);
          width: fn.percent-unit(32px);
          margin-right: fn.percent-unit(16px);
          .vie {
            height: 100%;
            width: 100%;
          }
        }
      }
      .description {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 3;
        font-size: fn.percent-unit(24px);
        line-height: 150%;
        height: fn.percent-unit(102px);
        margin-bottom: fn.percent-unit(16px);
      }
      .award {
        display: flex;
        align-items: center;
        font-size: fn.percent-unit(24px);
        // padding-top: fn.percent-unit(16px);
        // border-top: 1px solid palettes.$gray-cc;
        .award-icon {
          height: fn.percent-unit(50px);
          width: fn.percent-unit(50px);
          .vie {
            height: 100%;
            width: 100%;
          }
        }
      }
      .category {
        color: #fff;
        display: flex;
        align-items: center;
        margin: 0;

        .tag-meta {
          font-size: fn.percent-unit(24px);
          position: relative;

          &:not(:last-child) {
            margin-right: fn.percent-unit(38px);

            &::after {
              @include pseudo.pseudo($width: 6px, $height: 6px);
              position: absolute;
              top: 50%;
              right: fn.percent-unit(-21px);
              transform: translateY(-50%);
              border-radius: 50%;
              background-color: palettes.$gray-cc;
            }
          }
        }
      }
    }
  }
}
