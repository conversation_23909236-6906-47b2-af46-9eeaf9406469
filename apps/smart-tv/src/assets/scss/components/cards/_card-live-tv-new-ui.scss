/// @group variables
// @use 'settings/variables' as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

///
.card--live-tv-new-ui {
  @at-root .card#{&} {
    width: fn.percent-unit(361px);
    height: fn.percent-unit(203px);
    position: relative;
    border: none;
    //padding: fn.percent-unit(6px);
    box-sizing: content-box;
    margin: 0 fn.percent-unit(8px) fn.percent-unit(8px) 0;

    .card__mask {
      position: absolute;
      width: 100%;
      height: 60%;
      bottom: 0;
      left: 0;
      background: linear-gradient(0deg, rgba(0, 0, 0, 0.8), transparent);
    }

    .card__thumbnail {
      position: relative;
      width: 100%;
      height: 100%;

      .card__img {
        width: 100%;
        height: 100%;

        & > img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .card__logo {
      position: absolute;
      top: fn.percent-unit(10px);
      right: fn.percent-unit(10px);
      width: fn.percent-unit(66px);
      height: fn.percent-unit(66px);
      border-radius: 999px;
      border: fn.percent-unit(2px) solid #fff;
      background: none;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .card__image-logo {
      background-color: #ffffff;
      height: fn.percent-unit(62px);
      width: fn.percent-unit(62px);
      border-radius: 999px;
      display: flex;
      justify-content: center;
      align-items: center;

      & > img {
        height: fn.percent-unit(43.7px);
        width: fn.percent-unit(43.7px);
      }
    }

    .card__title-bottom {
      position: absolute;
      left: fn.percent-unit(16px);
      bottom: 0;
      margin-bottom: fn.percent-unit(12px);

      .title {
        font-weight: 500;
        font-size: fn.percent-unit(20px);
        line-height: 130%;
        color: #ffffff;
        text-shadow: fn.percent-unit(0) fn.percent-unit(0) fn.percent-unit(3px)
          rgba(0, 0, 0, 0.5);
      }

      .title--live {
        background: #ed0303;
        background: -webkit-linear-gradient(to right, #ed0303 0%, #ff6262 100%);
        background: -moz-linear-gradient(to right, #ed0303 0%, #ff6262 100%);
        background: linear-gradient(to right, #ed0303 0%, #ff6262 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .title--coming-soon {
        color: #3ac882;
      }
    }
  }
}
