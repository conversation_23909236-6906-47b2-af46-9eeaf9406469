////
/// Global style for Card
////

/// @group variables
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/position" as position;

///
.card {
  position: relative;
  flex: 0 0 auto;
  transition: opacity 0.2s;

  .ads-info {
    background: #00000080;
    color: white;
    width: fn.percent-unit(120);
    height: fn.percent-unit(24);
    position: absolute;

    z-index: 1;
    font-size: fn.percent-unit(14);
    line-height: fn.percent-unit(24);
    font-weight: 500;
    display: flex;

    &.top-left {
      top: 0;
      left: 0;
      border-bottom-right-radius: fn.percent-unit(16);
    }
    &.bottom-left {
      bottom: 0;
      left: 0;
      border-top-right-radius: fn.percent-unit(16);
    }

    &.at-bill-board {
      background: none;
      font-weight: 400;
      line-height: fn.percent-unit(24);
    }

    .vie {
      width: fn.percent-unit(24);
      height: fn.percent-unit(24);
      margin: 0 fn.percent-unit(4);
      margin-top: fn.percent-unit(3);
    }
  }

  &:hover {
    cursor: pointer;
  }

  %truncateContent {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }

  &__thumbnail {
    width: 100%;
    height: 100%;
    position: relative;

    [class^="ratio-"],
    [class*=" ratio-"] {
      height: 0;
      position: relative;

      //
      &.card__img {
        overflow: hidden;
        @include box.box($width: 100%, $height: auto);

        img {
          @include position.absolute(top 0 right 0 bottom 0 left 0);
          width: auto;
          height: 100%;
        }
      }
    }

    //
    .tags {
      &.top-left {
        @include position.absolute(top fn.percent-unit(12) left 0);
      }

      &.top-right {
        @include position.absolute(top fn.percent-unit(12) right 0);
      }

      &.variant {
        @include position.absolute(top 0 right 0);
      }

      &-group {
        padding: 0 fn.percent-unit(8);
        align-items: center;
        justify-content: center;

        &.bottom {
          @include position.absolute(right 0 bottom fn.percent-unit(12) left 0);
        }
      }
    }
  }

  &__title {
    font-size: fn.percent-unit(32);
    margin-bottom: fn.percent-unit(10);
    font-weight: 400;
    color: palettes.$white;

    &-truncate {
      @extend %truncateContent;
    }
  }

  &__desc {
    font-size: fn.percent-unit(28);
    margin-bottom: fn.percent-unit(24);
    line-height: 1.5;
    color: #ccc;
  }

  &__cast {
    font-size: fn.percent-unit(24);
    color: #ccc;
  }

  &__duration {
    font-size: fn.percent-unit(24);
    color: #ccc;
  }

  &__index {
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 0 fn.percent-unit(5);
    height: fn.percent-unit(50);
    background-color: red;
    font-size: fn.percent-unit(50);
    text-align: center;
    color: white;
  }

  &__tag-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    &__remaining-time {
      & > .v-tag {
        width: 50%;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        margin: 0 auto;
      }
    }

    .tag {
      position: absolute;

      & > img {
        height: 100%;
      }

      // &.tag--tvod-price {
      //   top: fn.percent-unit(13px);
      //   left: 0;
      //   padding: fn.percent-unit(2.5px) fn.percent-unit(8px);
      //   background-color: #DA9E1C;
      //   color: #fff;
      //   font-size: fn.percent-unit(18px);
      //   font-weight: 700;
      //   line-height: 1.2;
      //   .price-unit {
      //     font-size: fn.percent-unit(9px);
      //     font-weight: 500;
      //   }
      // }
      // &.tag--remaining-time {
      //   bottom: fn.percent-unit(13px);
      //   left: 50%;
      //   transform: translate(-50%, 0);
      //   padding: fn.percent-unit(3.5) 0;
      //   width: fn.percent-unit(152px);
      //   text-align: center;
      //   background-color: palettes.$warning;
      //   color: #523B08;
      //   font-weight: 700;
      //   font-size: fn.percent-unit(13px);
      //   line-height: 1.2;
      //   white-space: nowrap;
      // }
      &.tag--left {
        top: fn.percent-unit(12);
        left: 0;
      }

      &.tag--right {
        top: fn.percent-unit(12);
        right: 0;
        text-align: right;
      }

      &.tag--bottom {
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
      }

      &.tag--center {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      &.tag--new,
      &.tag--labelPublicDay {
        height: fn.percent-unit(26px);
      }

      &.tag--top {
        height: fn.percent-unit(52px);
      }

      &.tag--free {
        background-color: #3ea6ff;
        color: white;
        height: fn.percent-unit(28);
        padding: 0 fn.percent-unit(8);
        display: flex;
        align-items: center;
        line-height: 1;
        font-weight: 700;
        font-size: fn.percent-unit(20);
        text-shadow: 0px 1.720588207244873px 3.441176414489746px
          rgba(77, 77, 77, 0.5);
      }

      &.tag--labelPublicDay {
        background-color: palettes.$green-04;
        padding: 0 fn.percent-unit(8px);
        color: palettes.$white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: fn.percent-unit(16px);

        & > .icon-box {
          height: fn.percent-unit(16px);
          // padding-bottom: fn.percent-unit(1px);
          margin-right: 2px;

          & > img {
            height: 100%;
            width: auto;
          }
        }

        & > div {
          height: fn.percent-unit(16px);
        }
      }

      &.tag--epgTitle {
        font-size: fn.percent-unit(20px);
        line-height: 1.5;
        color: palettes.$white;
        padding: fn.percent-unit(16px);
        height: fn.percent-unit(45px);
        width: fn.percent-unit(329px);
        overflow: hidden;
        text-overflow: ellipsis;
        background-image: linear-gradient(
          to top,
          rgba(0, 0, 0, 0.8),
          rgba(0, 0, 0, 0)
        );

        &.tag--bottom {
          left: 0;
          transform: none;
        }
      }

      &.tag--info {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: fn.percent-unit(24px);
        margin-bottom: fn.percent-unit(12px);
        font-size: fn.percent-unit(13px);
        text-transform: uppercase;

        & > div {
          // padding: 0 fn.percent-unit(6px);
          // height: 100%;
          // font-weight: 500;
          // line-height: fn.percent-unit(22px);
          // vertical-align: middle;
          // box-sizing: border-box;

          padding: 0 fn.percent-unit(6px);
          padding-top: fn.percent-unit(1px);
          height: 100%;
          font-weight: 500;
          display: flex;
          justify-content: center;
          align-items: center;
          // vertical-align: middle;
          // box-sizing: border-box;

          &:first-child {
            background-color: palettes.$green-04;
            color: palettes.$white;
          }

          &:nth-child(2) {
            background-color: palettes.$gray-da;
            color: rgba(0, 0, 0, 0.7);
          }
        }
      }
    }
  }

  &.expanded {
    width: fn.percent-unit(32px);
  }

  &.hidden {
    opacity: 0.15;
  }

  &.card--in-app-banner,
  &.card--promotion-banner,
  &.card--outstream-ads {
    width: fn.percent-unit(1716px);
    height: fn.percent-unit(340px);
    margin-right: fn.percent-unit(13px);

    & > img {
      width: 100%;
      height: 100%;
    }
  }

  // &.card--poster,
  // &.card--banner,
  // &.card--top-views,
  // &.card--watch-later,
  // &.card--watch-more,
  // &.card--live-tv,
  // &.card--epg,
  // &.card--coming-soon,
  // &.card--live-stream {
  //   .tag.tag--vip,
  //   .tag.tag--new,
  //   .tag.tag--live,
  //   .tag.tag--labelPublicDay {
  //   }

  //   .tag.tag--top {
  //   }
  // }
}
