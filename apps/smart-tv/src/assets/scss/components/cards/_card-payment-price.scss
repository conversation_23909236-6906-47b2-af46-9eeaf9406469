/// @param settings
@use "settings/palettes" as pales;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/position" as posit;
@use "mixin/pseudo" as pseudo;

///
/// @group Badges
.card--payment-price {
  @at-root .card--payment#{&} {
    @include box.box($width: 100%, $height: fn.percent-unit(410));
    background-color: pales.$gray-de;
    border-radius: fn.percent-unit(8);
    padding: fn.percent-unit(12) fn.percent-unit(20);
    box-sizing: border-box;

    .card__title {
      @include fonts.font-family("roboto-medium");
      @include fonts.font-size(36);
      align-items: center;
      color: pales.$gray-33;
      display: flex;
      margin-bottom: fn.percent-unit(8);
      line-height: 1.4;
      font-weight: 500;
      padding-top: fn.percent-unit(27);
      text-transform: uppercase;

      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;

      .card__most-buyers {
        margin-left: fn.percent-unit(20);
        text-transform: none;
      }
    }

    .card__section {
      @include box.box($height: fn.percent-unit(132));
      position: relative;
    }

    .card__title_section {
      max-height: fn.percent-unit(165);
      overflow: hidden;
    }

    .card__desc {
      @include fonts.font-size(18);
      color: pales.$gray-33;
      line-height: 1.3;
      font-weight: 500;
      min-height: fn.percent-unit(70);
      margin-bottom: fn.percent-unit(10);

      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
    }

    .price {
      display: flex;
      justify-content: flex-start;
      @include box.box($width: 100%, $height: auto);
      border-top: 1px solid rgba(pales.$gray-33, 0.2);
      line-height: 1.2;
      margin-bottom: fn.percent-unit(50);
      padding-top: fn.percent-unit(15);

      .price__item-pay {
        @include fonts.font-size(36);
        line-height: 1.2;
      }

      .price--old {
        font-size: fn.percent-unit(24);
        text-decoration: line-through;
        color: pales.$gray-33;
      }
    }

    .offer {
      p {
        margin-bottom: fn.percent-unit(5);
      }

      ul {
        padding-left: 0;
      }

      li {
        list-style: none;
        display: flex;
        align-items: center;
        margin-bottom: fn.percent-unit(5);
      }

      img {
        margin-right: fn.percent-unit(12);
        height: fn.percent-unit(25);
      }

      color: fn.el-color($color: v-gray, $shade: 27);
      line-height: 1.5;
      font-weight: 400;
      @include fonts.font-size(20);
    }

    &:not(:last-child) {
      margin-bottom: fn.percent-unit(24);
    }

    // &::before {
    //   @include posit.absolute(top 50% left 50%);
    //   @include pseudo.pseudo-options(
    //     $content: "",
    //     $display: block,
    //     $width: calc(100% + 10px),
    //     $height: calc(100% + 10px)
    //     );
    //   border: fn.percent-unit(2) solid pales.$green-3a;
    //   border-radius: fn.percent-unit(8);
    //   $shadow-color: rgba(fn.el-color($color: v-medium-sea-green, $shade: 149), 0.55);
    //   box-shadow: 3px 0 3px $shadow-color, -3px 0 3px $shadow-color, 0 3px 3px $shadow-color, 0 -3px 3px $shadow-color;
    //   transform: translate(-50%, -50%);
    //   z-index: fn.z-index(layer-1);
    //   opacity: 0;
    // }

    // &.focus {
    //   &::before {
    //     opacity: 1;
    //   }
    // }

    .card__most-buyers {
      @include box.box($width: auto, $height: fn.percent-unit(36));
      @include fonts.font-family("roboto-bold");
      @include fonts.font-size(20);
      align-items: center;
      background-image: linear-gradient(
        90deg,
        pales.$yellow-da 0,
        pales.$yellow-ec 100%
      );
      border-radius: fn.percent-unit(8);
      color: pales.$dark-wasabi-34;
      display: flex;
      line-height: 1.5;
      padding: fn.percent-unit(8) fn.percent-unit(20);

      &.absolute {
        @include posit.absolute(top fn.percent-unit(-24) right 0);
        z-index: fn.z-index(layer-min);
      }
    }
  }
}
