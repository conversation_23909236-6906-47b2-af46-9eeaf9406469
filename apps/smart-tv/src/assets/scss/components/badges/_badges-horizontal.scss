/// @param settings
// @use 'settings/palettes' as pales;
// @use 'settings/variables' as var;
@use "settings/function" as fn;

/// @group mixin
// @use 'mixin/box' as box;
// @use 'mixin/font-size' as fs;
// @use 'mixin/grid' as grid;
// @use 'mixin/position' as posit;
// @use 'mixin/pseudo' as pseudo;

/// @group Badges--Horizontal
.badges--horizontal {
  @at-root .badges#{&} {
    display: flex;
    justify-content: flex-start;

    .badges__item {
      &:not(:first-child) {
        margin-left: fn.percent-unit(12);
      }
      &:not(:last-child) {
        margin-right: fn.percent-unit(12);
      }
    }
  }
}
