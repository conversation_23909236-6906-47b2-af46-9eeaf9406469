/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ============= BADGES PRIVILEGE =============
/*--------------------------------------------*/

/**
* ============ CONTENTS ============
*
* USE SETTING
* BADGES PRIVILEGE
*
*/

/// SETTING
@use "settings/function" as fn;

/// BADGES PRIVILEGE STYLES
.badges--privilege {
  @at-root .badges#{&} {
    background-color: rgba(fn.el-color($color: v-gold, $shade: base), 0.1);
    padding-bottom: fn.percent-unit(12);
    padding-top: fn.percent-unit(12);
    justify-content: center;
    max-height: fn.percent-unit(56);

    &.badges--horizontal {
      .badges__item {
        &:not(:first-child) {
          margin-left: fn.percent-unit(15);
        }
        &:not(:last-child) {
          margin-right: fn.percent-unit(15);
        }
        &:first-child {
          margin-right: fn.percent-unit(35);
        }
      }
    }
    .badges__item .text {
      font-weight: 500;
    }
    .badges__item:first-child .text {
      font-size: fn.percent-unit(28);
    }
  }
  @at-root .wrap--payment & {
    $text-color: #480d0d;
    /* Gradient-gold */
    background: linear-gradient(90deg, #da9e1c 0%, #ecbd57 100%);
    .badges__item .text {
      color: $text-color;
      font-size: fn.percent-unit(20);
      &.black {
        color: #000000;
      }
    }
    .badges__item .icon {
      width: fn.percent-unit(36);
      height: fn.percent-unit(36);
    }
  }
}
