/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ================== BADGES ==================
/*--------------------------------------------*/

/**
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* BADGES
*
*/

/// @param settings
@use "settings/palettes" as pales;
@use "settings/variables" as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/font-size" as fs;
@use "mixin/position" as posit;
@use "mixin/pseudo" as pseudo;

/// @group Badges
.badges {
  padding: fn.percent-unit(12);

  &__item {
    position: relative;
    display: flex;
    align-items: center;
    .icon {
      border-radius: 100%;
      transform: translateY(-50%);
      @include box.box($width: fn.percent-unit(38));
      @include posit.absolute(top 50% left 0);

      .vie {
        color: pales.$yellow-da;
        @include box.box($width: fn.percent-unit(24));
        @include fs.font-size(32);
      }
      & > img {
        width: 100%;
        height: 100%;
      }

      & + .text {
        padding-left: fn.percent-unit(48);
      }
    }
    .text {
      color: pales.$yellow-56;
      font-family: "Roboto", sans-serif;
      font-weight: 400;
      @include fs.font-size(24);
    }

    &.muted {
      .text {
        @include fs.font-size(28);
      }
    }
  }
}
