@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;
@use "components/icons/icon" as icon;
@use "mixin/pseudo" as pseudo;

.dialog-common-minimal {
  position: fixed;
  z-index: 1001;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.85);
  width: 100vw;
  height: 100vh;

  &.alert-enter,
  &.alert-appear {
    opacity: 0;

    .popup__inner {
      transform: scale(0.9);
    }
  }

  &.alert-enter-active,
  &.alert-appear-active {
    opacity: 1;
    transition: opacity 200ms;

    .popup__inner {
      transform: scale(1);
      transition: transform 200ms 200ms;
    }
  }

  &.alert-exit {
    opacity: 1;
  }

  &.alert-exit-active {
    opacity: 0;
    transition: opacity 200ms;

    .popup__inner {
      transform: scale(0.9);
      transition: transform 200ms 200ms;
    }
  }

  &.alert-exited {
    opacity: 0;
  }

  .d-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .d-block {
    display: block !important;
  }

  .justify-content-space-between {
    justify-content: space-between;
  }

  .align-items-end {
    align-items: flex-end;
  }

  &__inner {
    width: fn.percent-unit(700px);
    position: relative;

    &.success {
      &::after {
        @include pseudo.pseudo($height: 5px);
        position: absolute;
        transform: translateY(-100%);
        left: 0;
        top: 0;
        background: linear-gradient(90deg, #85fc6e 0%, #0ad418 100%);
        border-radius: fn.percent-unit(4px) fn.percent-unit(4px) 0 0;
      }
    }

    &.warning {
      &::after {
        @include pseudo.pseudo($height: 5px);
        position: absolute;
        transform: translateY(-100%);
        left: 0;
        top: 0;
        background: palettes.$warning;
        border-radius: fn.percent-unit(4px) fn.percent-unit(4px) 0 0;
      }
    }
  }

  &__header {
    height: fn.percent-unit(300px);
    position: relative;

    .mask {
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    .img {
      position: absolute;
      left: 50%;
      z-index: 9;

      &--center {
        top: 50%;
        transform: translate(-50%, -50%);
      }

      &--bottom {
        bottom: fn.percent-unit(-4px);
        transform: translateX(-50%);
        width: 100%;
      }

      &--lottie {
        width: fn.percent-unit(200px);
        height: fn.percent-unit(200px);
      }

      & > img {
        width: 100%;
      }
    }
  }

  &__body {
    background: #333;

    .inner {
      box-sizing: border-box;
      padding: fn.percent-unit(44px);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }

  &__title {
    font-size: fn.percent-unit(36px);
    line-height: 1.4;
    font-weight: 500;
    margin-bottom: fn.percent-unit(24px);
    color: palettes.$white;
    width: fn.percent-unit(100%);

    &.text-left {
      text-align: left;
    }

    &.text-right {
      text-align: right;
    }

    &.text-justify {
      text-align: justify;
    }

    &.text-center {
      text-align: center;
    }
  }

  &__description {
    font-size: fn.percent-unit(24px);
    line-height: 1.5;
    color: palettes.$gray-9b;
    text-align: center;
    margin-bottom: fn.percent-unit(24px);
    font-weight: 500;
    width: fn.percent-unit(100%);

    &.text-left {
      text-align: left;
    }

    &.text-right {
      text-align: right;
    }

    &.text-justify {
      text-align: justify;
    }

    &.text-center {
      text-align: center;
    }
  }

  &__button-group {
    display: flex;
    justify-content: center;
    align-items: center;
    width: fn.percent-unit(100%);

    .btn {
      padding-left: fn.percent-unit(17px);
      padding-right: fn.percent-unit(17px);
      font-weight: 500;
    }

    &.vertical {
      flex-direction: column;

      .btn {
        margin-right: fn.percent-unit(0px);
        width: fn.percent-unit(100%);
        margin-bottom: fn.percent-unit(24px);

        &:last-child {
          margin-bottom: fn.percent-unit(0px);
        }
      }
    }

    &.horizontal {
      flex-direction: row;

      .btn {
        margin-right: fn.percent-unit(24px);

        &:last-child {
          margin-right: fn.percent-unit(0px);
        }
      }
    }
  }
}
