@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;
@use "components/icons/icon" as icon;

.dialog-grid-card {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 1001;
  top: 0;
  left: 0;
  background-color: palettes.$gray-11;
  background-size: cover;

  &.alert-enter,
  &.alert-appear {
    opacity: 0;

    .popup__inner {
      transform: scale(0.9);
    }
  }

  &.alert-enter-active,
  &.alert-appear-active {
    opacity: 1;
    transition: opacity 200ms;

    .popup__inner {
      transform: scale(1);
      transition: transform 200ms;
    }
  }

  &.alert-exit {
    opacity: 1;
  }

  &.alert-exit-active {
    opacity: 0;
    transition: opacity 200ms;

    .popup__inner {
      transform: scale(0.9);
      transition: transform 200ms;
    }
  }

  &.alert-exited {
    opacity: 0;
  }

  .icon {
    @include icon.icon;
  }

  &__basic {
    display: flex;
    flex-direction: column;
    height: fn.percent-unit(100vh);
    align-items: center;
    justify-content: center;

    .space {
      width: fn.percent-unit(1480px);
    }

    &__header {
      margin-bottom: fn.percent-unit(24px);
      padding-top: fn.percent-unit(30px);

      .title {
        font-weight: 500;
        font-size: fn.percent-unit(48px);
        line-height: fn.percent-unit(56px);
        color: palettes.$white;
        margin-bottom: fn.percent-unit(8px);
      }

      .description {
        font-weight: 400;
        font-size: fn.percent-unit(32px);
        line-height: fn.percent-unit(40px);
        color: palettes.$gray-de;
      }
    }

    &__grid {
      margin-bottom: fn.percent-unit(12px);
      //max-height: fn.percent-unit(720px);
      position: relative;

      .title {
        font-weight: 500;
        font-size: fn.percent-unit(32px);
        line-height: fn.percent-unit(40px);
        color: palettes.$gray-de;
        margin-bottom: fn.percent-unit(16px);
      }

      .sect {
        position: relative;
        padding-left: fn.percent-unit(4px);

        .grid-list {
          .grid-list--container {
            height: auto !important;
            min-height: 100% !important;
          }
        }
      }

      .card--poster {
        position: relative;
        .focus {
          width: calc(100% + 8px);
          height: calc(100% + 8px);
          position: absolute;
          top: -4px;
          left: -4px;
          border: 4px solid palettes.$white;
          box-sizing: border-box;
        }
        &.card {
          width: fn.percent-unit(238px);
          height: fn.percent-unit(340px);

          .card__thumbnail {
            width: fn.percent-unit(238px);
            height: fn.percent-unit(340px);

            & > .card__img {
              width: 100%;
              height: 100%;

              .bg-box {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-repeat: no-repeat;
                background-position: center top;
                background-size: cover;
                z-index: -1;
              }

              & > img {
                width: 100%;
                height: 100%;
              }
            }
          }

          &:not(:last-child) {
            margin-right: fn.percent-unit(8px);
            margin-bottom: fn.percent-unit(8px);
          }
        }
      }

      .card-focus-box {
        width: fn.percent-unit(232px);
        height: fn.percent-unit(334px);
        position: absolute;
        top: fn.percent-unit(0px);
        left: 0;
        border: 4px solid palettes.$white;
      }
    }

    &__actions {
      display: flex;

      &.above {
        margin-bottom: fn.percent-unit(24px);
      }

      .btn {
        min-width: fn.percent-unit(249px);
        margin-right: fn.percent-unit(24px);
      }
    }
  }
}
