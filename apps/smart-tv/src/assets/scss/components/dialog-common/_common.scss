@use "src/assets/scss/settings/variables" as var;
@use "src/assets/scss/settings/function" as fn;
@use "src/assets/scss/settings/palettes" as palettes;
@use "src/assets/scss/components/icons/icon" as icon;

.dialog-common {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 1001;
  top: 0;
  left: 0;
  background-color: palettes.$gray-11;
  background-size: cover;
  transition: opacity 200ms;

  &.alert-enter,
  &.alert-appear {
    opacity: 0;

    .popup__inner {
      transform: scale(0.9);
    }
  }

  &.alert-enter-active,
  &.alert-appear-active {
    opacity: 1;
    transition: opacity 200ms;

    .popup__inner {
      transform: scale(1);
      transition: transform 200ms;
    }
  }

  &.alert-exit {
    opacity: 1;
  }

  &.alert-exit-active {
    opacity: 0;

    .popup__inner {
      transform: scale(0.9);
      transition: transform 200ms;
    }
  }

  &.alert-exited {
    opacity: 0;
  }

  .icon {
    @include icon.icon;
  }

  .terms-of-use {
    position: absolute;
    color: #dedede;
    bottom: fn.percent-unit(62px);
    left: fn.percent-unit(150px);
    font-size: fn.percent-unit(28px);
    font-weight: 400;
    line-height: fn.percent-unit(42px);

    &.columnEnd {
      bottom: fn.percent-unit(32px);
    }

    span {
      text-decoration: underline;
    }

    &.focus {
      padding: 2px;
      background: palettes.$white;
      color: palettes.$gray-11;
    }
  }

  .terms-of-use-content {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: #111111fa;
    color: palettes.$white;
    display: flex;
    flex-direction: row;
    align-items: self-start;
    justify-content: center;

    &__container {
      margin-top: fn.percent-unit(150px);
      width: fn.percent-unit(1088px);
      height: fn.percent-unit(604px);
      font-size: fn.percent-unit(32px);
      line-height: fn.percent-unit(37.5px);
      font-weight: 500;
      overflow-y: scroll;

      &::-webkit-scrollbar {
        width: 15px;
      }

      &::-webkit-scrollbar-thumb {
        background: palettes.$white;
      }
    }
  }

  &__basic {
    display: flex;
    width: fn.percent-unit(100%);
    height: fn.percent-unit(100vh);
    position: relative;

    .btn {
      width: fn.percent-unit(520px);
      white-space: nowrap;
    }

    &__logo {
      width: fn.percent-unit(120);
      position: absolute;
      top: fn.percent-unit(40);
      left: fn.percent-unit(40);
    }

    &__left-area {
      width: fn.percent-unit(50%);
      height: 100vh;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: self-start;
      flex-direction: column;

      &.layout-has-options {
        padding-bottom: fn.percent-unit(160);
        padding-top: fn.percent-unit(95);
        box-sizing: border-box;
      }

      &__description {
        font-size: fn.percent-unit(32px);
        line-height: fn.percent-unit(37.5px);
        color: palettes.$white;
        margin-bottom: fn.percent-unit(40px);

        .remain-time {
          color: #3ac882;
          display: flex;
          align-items: center;
          &.light {
            color: #dedede;
            b {
              color: #fff;
            }
          }
 
          .vie {
            margin-right: fn.percent-unit(5);
            margin-top: fn.percent-unit(8);
          }
        }

        &.vertical {
          width: fn.percent-unit(100%);
        }

        &.horizontal {
          padding-left: fn.percent-unit(150px);
          width: fn.percent-unit(80%);
        }

        .list-item {
          display: flex;
          font-size: fn.percent-unit(32px);
          font-weight: 400;
          line-height: 1.2;
          margin-bottom: fn.percent-unit(12px);
          color: palettes.$white;

          .icon {
            margin-right: fn.percent-unit(12px);

            .vie {
              color: palettes.$warning;
            }
          }
        }

        .list-item {
          display: flex;
          font-size: fn.percent-unit(32px);
          font-weight: 400;
          line-height: 1.2;
          margin-bottom: fn.percent-unit(12px);
          color: palettes.$white;

          .icon {
            margin-right: fn.percent-unit(12px);

            .vie {
              color: palettes.$warning;
            }
          }
        }

        .list-item {
          display: flex;
          font-size: fn.percent-unit(32px);
          font-weight: 400;
          line-height: 1.2;
          margin-bottom: fn.percent-unit(12px);
          color: palettes.$white;

          .icon {
            margin-right: fn.percent-unit(12px);

            .vie {
              color: palettes.$warning;
            }
          }
        }

        .list-item {
          display: flex;
          font-size: fn.percent-unit(32px);
          font-weight: 400;
          line-height: 1.2;
          margin-bottom: fn.percent-unit(12px);
          color: palettes.$white;

          .icon {
            margin-right: fn.percent-unit(12px);

            .vie {
              color: palettes.$warning;
            }
          }
        }

        .list-item {
          display: flex;
          font-size: fn.percent-unit(32px);
          font-weight: 400;
          line-height: 1.2;
          margin-bottom: fn.percent-unit(12px);
          color: palettes.$white;

          .icon {
            margin-right: fn.percent-unit(12px);

            .vie {
              color: palettes.$warning;
            }
          }
        }

        .list-item {
          display: flex;
          font-size: fn.percent-unit(32px);
          font-weight: 400;
          line-height: 1.2;
          margin-bottom: fn.percent-unit(12px);
          color: palettes.$white;

          .icon {
            margin-right: fn.percent-unit(12px);

            .vie {
              color: palettes.$warning;
            }
          }
        }
      }

      &__title {
        font-size: fn.percent-unit(48px);
        font-weight: 500;
        line-height: 1.16;
        color: palettes.$white;
        margin-bottom: fn.percent-unit(36);

        &.global-onboarding {
          font-size: fn.percent-unit(36px);
          margin-bottom: fn.percent-unit(10px);
        }

        &.vertical {
          //width: fn.percent-unit(600px);
          //padding-left: fn.percent-unit(150px);
          width: fn.percent-unit(85%);
        }

        &.horizontal {
          padding-left: fn.percent-unit(150px);
          width: fn.percent-unit(80%);
        }

        &.columnEnd {
          width: 120%;
          padding-left: fn.percent-unit(120px);
        }
      }

      &.vertical {
        padding-left: fn.percent-unit(150px);

        .space {
          width: 100%;
          display: flex;
          flex-direction: column;

          //align-items: center;
          .divider {
            color: #dedede;
            font-size: fn.percent-unit(28);
            margin-bottom: fn.percent-unit(20);
            line-height: 1.2;
          }

          .btn__global-onboarding {
            background-color: transparent;

            &.focus {
              background: #fff;
            }
          }

          .btn-wrapper + .btn-wrapper {
            margin-top: fn.percent-unit(24);
          }
        }
      }

      &.horizontal {
        .space {
          display: flex;
          width: 100%;
          flex-direction: row;
          //justify-content: center;
          padding-left: fn.percent-unit(150px);

          .btn {
            width: fn.percent-unit(294px);
          }

          .btn-wrapper + .btn-wrapper {
            margin-left: fn.percent-unit(24px);
          }
        }
      }

      &.columnEnd {
        flex-direction: column;
        //justify-content: end;
        //margin-top: fn.percent-unit(50px);
        margin-top: fn.percent-unit(22%);

        .space {
          display: flex;
          width: 100%;
          padding-left: fn.percent-unit(120px);
          padding-bottom: fn.percent-unit(150px);

          .btn {
            min-width: fn.percent-unit(520px);
            width: auto;
          }

          .btn-wrapper {
            flex: 0 0 auto;
          }

          .btn-wrapper + .btn-wrapper {
            margin-left: fn.percent-unit(24px);
          }
        }
      }
    }

    &__right-area {
      &__have-bg {
        content: "";
        position: absolute;
        left: -1px;
        top: 0;
        height: 100%;
        background: linear-gradient(
          90deg,
          rgba(17, 17, 17, 0.99),
          rgba(17, 17, 17, 0)
        );
        width: 10.9375vw;
      }

      width: fn.percent-unit(50%);
      height: 100vh;
      //background: #00a1e4;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      background-size: 100% 100%;

      &__image-center {
        //&::before {
        //  content: "";
        //  position: absolute;
        //  left: -1px;
        //  top: 0;
        //  height: 100%;
        //  background: -webkit-linear-gradient(left, rgba(17, 17, 17, .99), rgba(17, 17, 17, 0));
        //  background: linear-gradient(90deg, rgba(17, 17, 17, .99), rgba(17, 17, 17, 0));
        //  width: 10.9375vw;
        //}
        width: fn.percent-unit(539px);
        height: fn.percent-unit(772px);

        img {
          width: inherit;
          height: inherit;
          object-fit: contain;
        }
      }
    }

    &__center-area {
      display: flex;
      width: fn.percent-unit(716);
      align-items: center;
      align-self: center;
      justify-content: center;
      flex-direction: column;
      margin: 0 auto;

      &__title {
        font-size: fn.percent-unit(48);
        font-weight: 500;
        line-height: fn.percent-unit(56);
        color: palettes.$white;
        margin-bottom: fn.percent-unit(36);
      }

      &__description {
        font-size: fn.percent-unit(28px);
        line-height: fn.percent-unit(150%);
        color: palettes.$gray-de;
        text-align: center;
      }

      .space {
        margin-top: fn.percent-unit(60);
      }

      .btn:first-child {
        margin-bottom: fn.percent-unit(20);
      }
    }
    .columnEndRight {
      position: absolute;
      right: fn.percent-unit(60);
      bottom: fn.percent-unit(70);
      width: auto;
      height: auto;

      .space {
        display: flex;
      }

      .btn-wrapper:first-child {
        margin-right: fn.percent-unit(23);
      }

      .btn {
        width: fn.percent-unit(337);
        border-radius: 2px;
        border: 2px solid var(--Gray60, #9b9b9b);
        background: rgba(0, 0, 0, 0.5);
        color: #fff;

        &.focus {
          background: #3ac882;
          border-color: transparent;
        }
      }
    }
  }

  &__options {
    position: absolute;
    bottom: fn.percent-unit(65);
    left: fn.percent-unit(140);
    display: flex;
    align-items: center;
    font-size: fn.percent-unit(24);
    line-height: fn.percent-unit(36);
    font-weight: 500;
    color: #fff;
    box-sizing: border-box;

    & * {
      box-sizing: border-box;
    }

    &__title {
      margin-right: fn.percent-unit(24);
      display: flex;
      align-items: center;

      &__icon {
        width: fn.percent-unit(40);
        height: fn.percent-unit(40);
        margin-right: fn.percent-unit(16);
      }
    }

    &__btn {
      background: rgba(0, 0, 0, 0.3) !important;
      width: fn.percent-unit(151) !important;
      height: auto !important;
      padding: fn.percent-unit(12) fn.percent-unit(20);
      border: 1px solid rgba(255, 255, 255, 0.15) !important;
      border-radius: fn.percent-unit(8);
      display: flex;
      line-height: 150%;
      flex-direction: column;
      font-size: fn.percent-unit(24);

      .bar {
        content: "";
        display: block;
        width: fn.percent-unit(111);
        height: fn.percent-unit(4);
        background: #3ac882;
        border-radius: fn.percent-unit(16);
        margin-top: fn.percent-unit(8);
        opacity: 0;
      }

      &:not(:last-child) {
        margin-right: fn.percent-unit(12);
      }

      &.selected {
        .bar {
          opacity: 1;
        }
      }

      &.focus {
        background-color: #fff !important;
      }

      & > span {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
    }
  }
}
