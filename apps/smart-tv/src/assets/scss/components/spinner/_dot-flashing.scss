////
/// @group nav
////

/// @param palettes
@use "settings/palettes" as palettes;

/// @group variables
@use "settings/variables" as variables;

/// @group function
@use "settings/function" as fn;

/// @group animations
@use "animates";

/**
 * ==============================================
 * Dot Flashing
 * ==============================================
 */
.dot-flashing {
  position: relative;
  width: fn.percent-unit(15px);
  height: fn.percent-unit(15px);
  border-radius: 50%;
  background-color: palettes.$gray-64;
  color: palettes.$gray-64;
  animation: dotFlashing 1s infinite linear alternate;
  animation-delay: 0.5s;
}

.dot-flashing::before,
.dot-flashing::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
}

.dot-flashing::before {
  left: fn.percent-unit(-150%);
  width: fn.percent-unit(15px);
  height: fn.percent-unit(15px);
  border-radius: 50%;
  background-color: palettes.$gray-64;
  color: palettes.$gray-64;
  animation: dotFlashing 1s infinite alternate;
  animation-delay: 0s;
}

.dot-flashing::after {
  left: fn.percent-unit(150%);
  width: fn.percent-unit(15px);
  height: fn.percent-unit(15px);
  border-radius: 50%;
  background-color: palettes.$gray-64;
  color: palettes.$gray-64;
  animation: dotFlashing 1s infinite alternate;
  animation-delay: 1s;
}

@keyframes dotFlashing {
  0% {
    background-color: palettes.$gray-64;
  }
  50%,
  100% {
    background-color: palettes.$gray-da;
  }
}
