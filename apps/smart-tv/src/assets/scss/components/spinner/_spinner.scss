////
/// @group nav
////

/// @param palettes
@use "settings/palettes" as palettes;

/// @group variables
@use "settings/variables" as variables;

/// @group function
@use "settings/function" as function;

/// @group animations
@use "animates";

.spinner-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.spinner {
  width: function.percent-unit(96px);
  height: function.percent-unit(96px);
  border-top: function.percent-unit(8px) solid palettes.$green-3a;
  border-bottom: function.percent-unit(8px) solid palettes.$green-3a;
  border-left: function.percent-unit(8px) solid palettes.$green-3a;
  border-right: function.percent-unit(8px) solid rgba(0, 0, 0, 0);
  border-radius: 50%;
  // @include animates.spinner();
}
.spinner-percent {
  position: absolute;
  transform: translate(-50%, -50%);
  left: 50%;
  top: 50%;
  color: palettes.$green-3a;
  font-size: function.percent-unit(28px);
  font-weight: 500;
}
.spinner-text {
  margin-top: function.percent-unit(28px);
  font-size: function.percent-unit(28px);
  color: #646464;
  text-align: center;
}

.fullscreen-spinner {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
