.page-transition {
  &.fade {
    &-enter {
      opacity: 0;
    }
    &-enter-active {
      opacity: 1;
      transition: opacity 500ms;
    }
    &-enter-done {
      opacity: 1;
    }
    &-exit {
      opacity: 1;
    }
    &-exit-active {
      opacity: 0;
      transition: opacity 500ms;
    }
    &-exit-done {
      opacity: 0;
    }
  }
  &.slide {
    &-enter {
      transform: translateX(10%);
      opacity: 0;
    }
    &-enter-active {
      transform: translateX(0);
      opacity: 1;
      transition: opacity 500ms transform 500ms;
    }
    &-enter-done {
      opacity: 1;
      transform: translateX(0);
    }
    &-exit {
      opacity: 1;
      transform: translateX(0);
    }
    &-exit-active {
      opacity: 0;
      transform: translateX(-10%);
      transition: opacity 500ms transform 500ms;
    }
    &-exit-done {
      opacity: 0;
      transform: translateX(-10%);
    }
  }
}
