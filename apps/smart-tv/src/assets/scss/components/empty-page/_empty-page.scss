@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

.empty-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.empty {
  &__title {
    color: palettes.$white;
    font-weight: 500;
    font-size: fn.percent-unit(48);
    line-height: 150%;
    margin-top: fn.percent-unit(36);
  }
  &__image {
    max-width: fn.percent-unit(578px);
    height: auto;
    text-align: center;
  }
  &__server__message {
    width: fn.percent-unit(800) !important;
  }
  &__message {
    font-size: fn.percent-unit(32);
    line-height: 150%;
    color: palettes.$gray-de;
    text-align: center;
    margin: fn.percent-unit(16) 0 fn.percent-unit(48px) 0;
    strong {
      font-size: fn.percent-unit(48);
      font-weight: 400;
    }
  }
  &__button {
    .btn {
      font-size: fn.percent-unit(28);
      font-weight: 500;
      min-width: fn.percent-unit(192px);
      padding: fn.percent-unit(12px) fn.percent-unit(30px);
    }
  }
}

.empty__livetv-list {
  padding-top: fn.percent-unit(170);
}

.error-container.hide-sidebar {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 20;
  background: #000000;
}
