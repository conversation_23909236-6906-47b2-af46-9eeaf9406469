/// @group settings
@use "settings/palettes" as palettes;
@use "settings/variables" as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/background" as bg;
@use "mixin/box" as box;
@use "mixin/pseudo" as pseudo;
@use "mixin/position" as position;

/// @group epg
.epg {
  padding-left: fn.percent-unit(var.$sidebar-max-width);

  &-label-group {
    display: flex;
    box-sizing: border-box;
    padding-top: fn.percent-unit(40);
    padding-bottom: fn.percent-unit(12);
    padding-left: fn.percent-unit(277);

    &--new-ui {
      padding-left: fn.percent-unit(206);

      .epg-label__item:first-child {
        width: fn.percent-unit(585) !important;
      }
    }
  }

  &-label__item {
    color: #9b9b9b;
    font-size: fn.percent-unit(32);
    font-weight: 400;

    &:first-child {
      width: fn.percent-unit(858);
      font-size: fn.percent-unit(32);
      color: palettes.$white;
    }

    &:not(:first-child) {
      margin-left: fn.percent-unit(12);
    }
  }

  &__body {
    padding-top: fn.percent-unit(52);
    padding-bottom: fn.percent-unit(32);
    position: relative;
    overflow: hidden;
    height: fn.percent-unit(748);

    $darkGlassGradient: rgba(palettes.$gray-11, 1), rgba(palettes.$gray-11, 0);

    &--new-ui {
      padding-top: fn.percent-unit(63) !important;

      &:before,
      &:after {
        @include box.box($width: 100%, $height: fn.percent-unit(70));
      }

      &:before {
        top: fn.percent-unit(-10) !important;
      }
    }

    &:before,
    &:after {
      @include pseudo.pseudo($width: 100%, $height: 100%, $display: block);
      @include box.box($width: 100%, $height: fn.percent-unit(54));
      z-index: 2;
    }

    &:before {
      @include bg.backgrounds(
        $properties: gradient,
        $value: linear,
        $grad-dir: to bottom,
        $color: $darkGlassGradient
      );
      @include position.absolute(top 0 right 0 left 0);
    }

    &:after {
      @include bg.backgrounds(
        $properties: gradient,
        $value: linear,
        $grad-dir: to top,
        $color: $darkGlassGradient
      );
      @include position.absolute(right 0 bottom 0 left 0);
    }
  }

  &__wrap {
    position: relative;
    z-index: 1;
  }

  &-channel {
    display: flex;
    flex-flow: row wrap;
    height: fn.percent-unit(156) !important;

    &--new-ui {
      margin-right: 0 !important;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        height: fn.percent-unit(93.6) !important;
        width: fn.percent-unit(124.8) !important;
      }
    }

    &__item {
      flex: 0 0 auto;

      &-name {
        @include box.box($width: 208, $height: 156);
        opacity: 0.4;
        margin-right: fn.percent-unit(69);

        img {
          @include box.box($width: 100%, $height: 100%);
        }
      }

      &-playing {
        width: fn.percent-unit(858);

        &--new-ui {
          width: fn.percent-unit(582);

          .card--channel {
            height: fn.percent-unit(156) !important;
          }
        }
      }

      &.ribbon {
        padding-left: 0;
        margin-left: fn.percent-unit(12);
        width: fn.percent-unit(629);

        .card {
          flex: 0 0 auto;
        }
      }
    }

    &:not(:last-child) {
      margin-bottom: fn.percent-unit(12);
    }
  }

  &--schedule {
    padding-bottom: fn.percent-unit(22);
  }

  &--new-ui {
    padding-left: fn.percent-unit(150);
  }
}
