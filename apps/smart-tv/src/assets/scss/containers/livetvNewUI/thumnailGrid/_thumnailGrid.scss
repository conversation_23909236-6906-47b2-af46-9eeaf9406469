@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as pales;

.livetv-new-grid {
  position: relative;
  margin-left: fn.percent-unit(144px);
  margin-top: fn.percent-unit(2px);

  .thumnail-focus-box {
    width: fn.percent-unit(361px);
    position: absolute;
    top: 0;
    left: 0;
    height: fn.percent-unit(203px);
    border: fn.percent-unit(6px) solid pales.$white;
    box-sizing: content-box;
    pointer-events: none;
    will-change: transform;
    // transition: transform 0.15s linear !important;
  }

  .grid {
    will-change: transform;
    // transition: transform 0.15s linear !important;
  }

  .jump {
    transition: none !important;
  }
}
