/// @group setting
// @use 'settings/variables' as var;
@use "../../settings/function" as fn;
@use "../../settings/palettes" as palettes;

.block--transaction {
  @at-root .block#{&} {
    font-size: 1.275em;
    color: #999;
    .table-header,
    .table-content {
      padding-right: fn.percent-unit(20);
      scroll-behavior: smooth;
    }
    table {
      border-collapse: collapse;
      box-sizing: border-box;
    }
    th {
      padding: 0em 1.125em;
      box-sizing: border-box;
    }
    tr.focus {
      background: #707070;
      box-sizing: border-box;
    }
    td {
      padding: 0.75em 1.125em;
      box-sizing: border-box;
    }
    .focus {
      color: palettes.$white;
    }
  }
}

.wrapper-section.wrapper-section--transaction {
  padding-top: fn.percent-unit(32px);
  padding-left: fn.percent-unit(121px);
}
