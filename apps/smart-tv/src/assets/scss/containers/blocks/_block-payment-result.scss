/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ============ BLOCK PAYMENT SMS =============
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* STYLES
*
*/

/// @param settings
@use "settings/function" as fn;
@use "settings/variables" as var;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/position" as position;

/// @group style

.block--payment-result {
  @at-root .block--payment#{&} {
    max-width: fn.percent-unit(700);
    margin-left: auto;
    margin-right: auto;
    height: fn.percent-unit(978);
    // min-height: 89vh;

    .block__header {
      margin-bottom: fn.percent-unit(28);
      padding-top: fn.percent-unit(36);
      text-align: center;

      .icon {
        @include box.box($width: fn.percent-unit(90));
        align-items: center;
        border: fn.percent-unit(6) solid
          fn.el-color($color: v-medium-sea-green, $shade: 149);
        border-radius: 100%;
        display: flex;
        justify-content: center;
        margin-left: auto;
        margin-right: auto;

        .vie {
          @include fonts.font-size(50);
          @include box.box($width: fn.percent-unit(50));
          color: fn.el-color($color: v-medium-sea-green, $shade: 149);
        }
      }

      & > * {
        &:not(:last-child) {
          margin-bottom: fn.percent-unit(16);
          margin-top: 0;
        }
      }

      & + .block__body,
      & ~ .block__body {
        border-top: 1px solid
          rgba(fn.el-color($color: v-black, $shade: base), 0.3);
        padding-bottom: fn.percent-unit(20);
        padding-top: fn.percent-unit(20);

        &.border-none {
          border-top: none;
        }
      }
    }

    .block__body {
      background-color: transparent;
      margin-bottom: fn.percent-unit(0);
      padding: 0 fn.percent-unit(20);

      .grid {
        justify-content: space-between;
        flex-wrap: nowrap;

        .col-6 {
          flex: 1 1 auto;
          max-width: unset;

          &:first-child {
            white-space: nowrap;
            margin-right: fn.percent-unit(70);
          }
        }
      }

      & > * {
        &:not(:last-child) {
          margin-bottom: fn.percent-unit(16);
        }
      }
    }

    .block__footer {
      position: relative;
      padding-top: fn.percent-unit(0);

      .button {
        margin: 0 auto;
        width: fn.percent-unit(300px);

        &.full {
          width: 100%;
        }
      }
    }

    &.block--payment-result {
      .block__footer {
        margin-top: 0;
      }
    }

    .block__text {
      @include fonts.font-size(24);
      line-height: 1.25;
      margin: 0;
      margin-left: fn.percent-unit(-90px);
      margin-right: fn.percent-unit(-90px);

      &:not(:last-child) {
        margin-bottom: fn.percent-unit(20);
      }
    }

    .block__text-1 {
      @include fonts.font-size(24);
      line-height: 1.25;
      margin: 0;
      border-bottom: 1px solid
        rgba(fn.el-color($color: v-black, $shade: base), 0.3);
      padding-bottom: fn.percent-unit(24);
    }

    .block__title {
      &.font-size-48 {
        font-size: fn.percent-unit(48);
      }

      &.font-size-36 {
        font-size: fn.percent-unit(36);
      }
    }

    &--fail {
      .block__title {
        margin-top: fn.percent-unit(92);
      }

      .block__header {
        padding-top: fn.percent-unit(130);
      }

      .block__body {
        border-top: none !important;
        border-bottom: 1px solid
          rgba(fn.el-color($color: v-black, $shade: base), 0.3);
      }

      .block__footer {
        padding-left: 0;
        padding-right: 0;

        .block__text {
          font-size: fn.percent-unit(28);
          text-align: center;
          line-height: fn.percent-unit(56);
          margin-bottom: fn.percent-unit(40);
        }

        .button-group .button {
          flex: 1;
        }
      }
    }

    &--pending {
      width: fn.percent-unit(1116) !important;
      max-width: none !important;

      .block__title {
        margin-top: fn.percent-unit(53);
      }

      .block__header {
        padding-top: fn.percent-unit(88);
      }

      .block__body {
        border-top: none !important;

        // border-bottom: 1px solid
        //   rgba(fn.el-color($color: v-black, $shade: base), 0.3);
        .block__text {
          text-align: center;
          white-space: nowrap;
          font-size: fn.percent-unit(28);
          line-height: fn.percent-unit(45);
        }

        & + .block__footer {
          margin-top: 0;
        }
      }

      .block__footer {
        padding-left: 0;
        padding-right: 0;
        padding-top: fn.percent-unit(20px);
        position: relative;

        .block__text {
          font-size: fn.percent-unit(28);
          text-align: center;
          line-height: fn.percent-unit(56);
          margin-bottom: fn.percent-unit(40);
        }

        &::before {
          display: block;
          content: "";
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          height: 1px;
          width: fn.percent-unit(637px);
          background: rgba(fn.el-color($color: v-black, $shade: base), 0.3);
        }
      }
    }

    &--voucher {
      padding-top: fn.percent-unit(25);

      .block__header .icon {
        margin-bottom: fn.percent-unit(20);
      }
    }
  }
}
