/// @param settings
@use "settings/palettes" as pales;
// @use 'settings/variables' as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
// @use 'mixin/position' as posit;
// @use 'mixin/pseudo' as pseudo;

/// @group Block-Payment
.block--payment {
  @at-root .block#{&} {
    .block__header {
      margin-bottom: fn.percent-unit(18);
      padding: 0 fn.percent-unit(6);
    }

    .block__title {
      // @include fonts.font-family("roboto-medium");
      @include fonts.font-size(32);
      color: pales.$black;
      font-weight: 500;
      margin: 0;
    }

    .text--note {
      margin-top: fn.percent-unit(20);
    }

    .block__body {
      & + .block__footer & ~ .block__footer {
        margin-top: fn.percent-unit(12);
      }
    }
  }
}
