/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ========== BLOCK PAYMENT VOUCHER ===========
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* STYLES
*
*/

/// @param settings
@use "settings/function" as fn;
@use "settings/variables" as var;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;

/// @group style

.block--payment-voucher {
  @at-root .block--payment#{&} {
    background-color: rgba(fn.el-color($color: v-gray, $shade: 87), 0.2);
    margin-right: auto;
    margin-left: auto;
    margin-top: fn.percent-unit(36);
    min-height: fn.percent-unit(740);
    padding-left: fn.percent-unit(144);
    padding-right: fn.percent-unit(144);
    padding-top: fn.percent-unit(42);
    .block__header {
      padding: fn.percent-unit(0) fn.percent-unit(32);
      margin-bottom: 0;
      & + .block__body,
      & ~ .block__body {
        padding-top: fn.percent-unit(15);
        padding-bottom: fn.percent-unit(28);
      }
    }
    .flex-box {
      display: flex;
      align-items: center;
    }

    .block__title {
      @include fonts.font-size(32);
      text-align: center;
    }

    .block__body {
      background-color: transparent;
      padding-left: fn.percent-unit(12);
      padding-right: fn.percent-unit(12);

      .form-label {
        @include fonts.font-size(28);
        display: block;
        margin-bottom: fn.percent-unit(4);
      }
      .form-policy {
        @include fonts.font-size(19);
        display: block;
      }
    }
    .block__footer {
      text-align: center;
      display: flex;
      justify-content: center;
    }
  }
}
