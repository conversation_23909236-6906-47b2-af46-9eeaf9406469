/// @param settings
@use "settings/palettes" as pales;
// @use 'settings/variables' as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
// @use 'mixin/position' as posit;
// @use 'mixin/pseudo' as pseudo;

/// @group Block-Payment
.block--payment-duration {
  @at-root .block#{&} {
    .block__body.blur .focus::before {
      opacity: 0.5;
    }

    // .block__header{
    //   margin-bottom: fn.percent-unit(18);
    //   padding: 0 fn.percent-unit(6);
    // }

    // .block__title{
    //   @include fonts.font-family("roboto-medium");
    //   @include fonts.font-size(32);
    //   color: pales.$black;
    //   margin: 0;
    // }
    .block__title {
      line-height: 1.5;
      margin-top: fn.percent-unit(15);
      @include fonts.font-size(32);
    }

    .block__sapo {
      line-height: 1.5;
      margin: 0 0 fn.percent-unit(15) 0;
      @include fonts.font-size(20);
    }

    .block__body {
      width: 100%;
      display: flex;
      //flex-wrap: wrap;
      margin: fn.percent-unit(30) fn.percent-unit(-10) 0;

      .wrapper {
        margin-bottom: fn.percent-unit(20);
        flex: 0 0 auto;
        width: 25%;
        box-sizing: border-box;
        padding: 0 fn.percent-unit(10);
      }

      // .card--payment-price {
      // }
    }
  }
}
