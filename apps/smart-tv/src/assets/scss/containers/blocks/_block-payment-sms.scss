/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ============ BLOCK PAYMENT SMS =============
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* STYLES
*
*/

/// @param settings
@use "settings/function" as fn;
@use "settings/variables" as var;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/position" as position;

/// @group style

.block--payment-sms {
  @at-root .block--payment#{&} {
    $child-gutter-x: fn.percent-unit(34);
    $child-gutter-y: fn.percent-unit(24);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .block__body {
      margin-bottom: fn.percent-unit(20);
      //padding-left: $child-gutter-x;
      //padding-right: $child-gutter-x;
      padding-top: $child-gutter-y;
      text-align: center;

      & > * {
        &:not(:last-child) {
          margin-bottom: fn.percent-unit(32);
        }
      }
    }

    .block__footer {
      padding: $child-gutter-y $child-gutter-x $child-gutter-y;
      position: relative;

      .icon {
        @include box.box($width: fn.percent-unit(96));
        @include position.absolute(
          top fn.percent-unit(15) left fn.percent-unit(34)
        );
        border-radius: 100%;
        background-color: rgba(fn.el-color($color: v-white, $shade: base), 0.2);

        .vie {
          @include box.box($width: fn.percent-unit(52));
          @include fonts.font-size(52);
          color: fn.el-color($color: v-yellows, $shade: 95);
        }

        &.announ-icon {
          @include position.absolute(left fn.percent-unit(34));
          transform: translateY(fn.percent-unit(-10));
        }

        & + .block__text,
        & ~ .block__text {
          padding-left: fn.percent-unit(124);
          margin-bottom: 0;
        }
      }
      &--recharge {
        padding-left: fn.percent-unit(90px);
        padding-right: fn.percent-unit(90px);
        //min-height: fn.percent-unit(100px);
      }
    }

    .block__text {
      &.regis__package {
        span {
          font-size: fn.percent-unit(36px);
        }
        .upper-text {
          font-size: fn.percent-unit(28px);
        }
      }
      .txt-send {
        span {
          font-size: fn.percent-unit(26);
          top: fn.percent-unit(-15);
        }
        position: relative;
        .union-icon {
          // background: url("/src/assets/images/icon/ic_union.png") no-repeat;
          background: url("../../../images/icon/ic_union.png") no-repeat;
          position: absolute;
          left: 0px;
          bottom: fn.percent-unit(-8);
          width: 100%;
          height: 10px;
          display: block;
        }
      }
      .block__line {
        padding-bottom: 8px;
        position: relative;
        &.center {
          text-align: center;
        }
        img {
          left: fn.percent-unit(-40);
          top: fn.percent-unit(2);
          position: absolute;
        }
      }
      @include fonts.font-size(24);
      line-height: 1.25;
      margin-bottom: fn.percent-unit(20);

      > span {
        @include fonts.font-size(36);
        $padding-x: fn.percent-unit(10);
        padding-left: $padding-x;
        padding-right: $padding-x;
        font-weight: 500;
      }
      .upper-text {
        position: relative;
      }
      .bottom-text {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        width: 110%;
        display: inline-block;
        text-align: center;
        @include fonts.font-size(24);
      }
      .text-left {
        text-align: left;
      }
    }

    .card--payment-qr {
      margin-left: auto;
      margin-right: auto;
      padding-right: 0;
      .card__text {
        font-weight: 400;
        font-size: 24px;
        color: fn.el-color($color: v-white, $shade: base);
      }
    }
    .card--payment-viettelsms {
      .card__text {
        color: fn.el-color($color: v-gray, $shade: 20);
      }
    }
  }
}
