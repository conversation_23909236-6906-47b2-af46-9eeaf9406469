/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ============= BLOCK LOGIN STYLE ============
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* VARIABLE
* INPUT STYLES
*
*/

/// @param settings
@use "settings/function" as fn;
@use "settings/palettes" as palettes;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/input" as input;
@use "mixin/position" as position;

.block--login {
  @at-root .block#{&} {
    %childStyle {
      text-align: left;
      margin-bottom: fn.percent-unit(24);
      color: #fff;
    }

    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    transition: all 0.27s linear;
    align-items: flex-start;
    justify-content: center;

    .panel-left {
      border-radius: fn.percent-unit(4);
      width: fn.percent-unit(818);
      position: absolute;
      top: fn.percent-unit(180);
      left: fn.percent-unit(160);

      .keyboard-container {
        background: #181818;
        padding: fn.percent-unit(32);
        box-sizing: border-box;
      }

      &.triangle-right-1 {
        &::before {
          content: " ";
          position: absolute;
          border-top: fn.percent-unit(12) solid transparent;
          border-left: fn.percent-unit(12) solid #181818; //
          border-bottom: fn.percent-unit(12) solid transparent;
          top: fn.percent-unit(135);
          right: fn.percent-unit(-11);
        }
      }
      &.triangle-right-2 {
        &::before {
          content: " ";
          position: absolute;
          border-top: fn.percent-unit(12) solid transparent;
          border-left: fn.percent-unit(12) solid #181818; //
          border-bottom: fn.percent-unit(12) solid transparent;
          top: fn.percent-unit(220);
          right: fn.percent-unit(-11);
        }
      }
      &.triangle-right-3 {
        &::before {
          @extend .triangle-right-2;
          top: fn.percent-unit(290);
        }
      }

      // &::after {
      //   content: "";
      //   float: right;
      //   background: green;
      //   width: fn.percent-unit(16);
      //   height: fn.percent-unit(16);
      //   position: absolute;
      //   transform: rotate(45deg);
      // }
    }

    .panel-right {
      float: left;
      width: fn.percent-unit(846);
      position: absolute;
      top: fn.percent-unit(268);
      right: fn.percent-unit(50);
      // .icon {
      //   position: fixed;
      //   margin: fn.percent-unit(1px) fn.percent-unit(20px);
      // }
      // .form {
      //   .icon {
      //     margin-top: fn.percent-unit(18px);
      //   }
      // }
    }

    .panel-bottom {
      position: absolute;
      bottom: fn.percent-unit(120);
      left: fn.percent-unit(160);
    }

    h2.block__title {
      position: absolute;
      top: fn.percent-unit(52);
      color: palettes.$white;
      font-size: fn.percent-unit(36);
      line-height: 1.4;
      font-weight: 500;
      margin-left: fn.percent-unit(20);
    }

    .block--sign-up {
      font-size: fn.percent-unit(32) !important;
    }

    .block__title {
      @extend %childStyle;
      float: left;
      color: palettes.$gray-cc;
      font-size: fn.percent-unit(32);
      line-height: normal;
      margin: 0 0 fn.percent-unit(32);
      font-weight: 400;
      &--desc {
        margin-top: fn.percent-unit(12);
        color: palettes.$gray-cc;
        font-size: fn.percent-unit(28);
        line-height: 1.7;
      }
      &--auth {
        color: palettes.$white;
        font-weight: 500;
        line-height: 1.4;
        max-width: fn.percent-unit(570);
        letter-spacing: -1px;
      }
    }

    .auth-button-group {
      margin-top: fn.percent-unit(56);
      display: flex;
      flex-direction: column;
      width: 100%;
      max-width: fn.percent-unit(570);
      .auth-button {
        border: fn.percent-unit(2) solid #333333;
        background: rgba(34, 34, 34, 0.7);
        border-radius: fn.percent-unit(3);
        color: palettes.$gray-de;
        &:not(:last-child) {
          margin-bottom: fn.percent-unit(30);
        }
      }
      .auth-button.focus {
        background: palettes.$white;
        border-color: palettes.$white;
        color: palettes.$black;
      }
    }

    .block--divider {
      position: absolute;
      left: fn.percent-unit(150);
      width: 1px;
      height: fn.percent-unit(200);
    }

    .form {
      margin-bottom: fn.percent-unit(34);
    }

    .input--focus {
      .input__control {
        background: #222222;
      }
    }

    .input--non-border {
      .input__control {
        border: none !important;
        background: transparent !important;
      }
    }

    .input {
      color: palettes.$white;
      .input__control {
        font-size: fn.percent-unit(32);
        height: fn.percent-unit(72);
        padding-left: fn.percent-unit(72px);
        .input__container {
          color: palettes.$white;
        }
      }

      &.input--custom {
        margin-bottom: fn.percent-unit(20);
        &.password {
          span {
            font-size: fn.percent-unit(60);
            -webkit-text-security: disc;
          }
          .placeholder {
            -webkit-text-security: none;
            font-size: fn.percent-unit(32);
          }
        }
        &.label {
          .input__control {
            border: none;
          }
        }
      }
      // span.placeholder,
      // span > span {
      //   margin: fn.percent-unit(28) 0 fn.percent-unit(28) fn.percent-unit(45);
      // }
    }
    .hint,
    .placeholder {
      color: #646464;
      display: flex;
      font-size: fn.percent-unit(32);
      line-height: fn.percent-unit(37);
      &.hint {
        margin: fn.percent-unit(22) 0 fn.percent-unit(28) fn.percent-unit(70);
      }
      &.censored {
        font-size: fn.percent-unit(60) !important;
        -webkit-text-security: disc;
      }
    }

    .button-group {
      margin-top: fn.percent-unit(28);
      &.column {
        flex-direction: column;
      }
      &-3 {
        .button {
          width: fn.percent-unit(262) !important;
        }
      }
      .button {
        width: fn.percent-unit(403);
        height: fn.percent-unit(63);
        //margin-bottom: fn.percent-unit(30px);
        font-size: fn.percent-unit(28);
        font-weight: 500;
        padding: fn.percent-unit(14) fn.percent-unit(25);
        &.focus {
          color: palettes.$gray-33;
          background: palettes.$white;
          border-color: palettes.$white;
        }
        &:not(:last-child) {
          margin-right: fn.percent-unit(12);
        }
        &.disabled,
        &:disabled {
          color: palettes.$gray-8a;
          background: palettes.$gray-43;
          border-color: palettes.$gray-43;
          opacity: 1;
        }
        &.small {
          height: fn.percent-unit(48);
          font-size: fn.percent-unit(24);
        }
      }
    }

    p.warning {
      text-align: right;
      color: palettes.$warning;
      font-size: fn.percent-unit(24);
      line-height: 1.5;
    }

    p.error {
      text-align: center;
      color: palettes.$error;
      font-size: fn.percent-unit(24);
      line-height: 1.5;
      &.pin-input-error {
        text-align: left !important;
        margin-left: fn.percent-unit(120);
      }
      &.otp-limit-error {
        text-align: left !important;
        margin-left: fn.percent-unit(20);
        color: #cccccc !important;
      }
    }

    &-required {
      img {
        @extend %childStyle;
        width: fn.percent-unit(172);
        height: fn.percent-unit(172);
      }
    }

    .space-box {
      $input-padding-x: fn.percent-unit(24);
      $input-padding-y: fn.percent-unit(4);
      height: fn.percent-unit(72);
      padding: 0.5em;
      position: relative;

      .checkbox {
        @include box.box($width: fn.percent-unit(40));
        @include position.absolute(left #{$input-padding-x} top 56%);
        transform: translateY(-50%);
        & + .hint,
        & ~ .hint {
          padding-left: fn.percent-unit(72);
        }
      }

      .hint {
        @include fonts.font-family("roboto-medium");
        @include fonts.font-size(32);
        align-items: center;
        color: fn.el-color($color: v-gray, $shade: 61);
        display: flex;
        height: 100%;
        padding: $input-padding-y $input-padding-x;
        margin: 0;
      }
      &.focus {
        background: white;
        .hint {
          color: black !important;
        }
      }
    }

    .muted {
      $input-padding-x: fn.percent-unit(24);
      $input-padding-y: fn.percent-unit(4);
      height: fn.percent-unit(72);
      padding: 0.5em;
      position: relative;

      .icon {
        @include box.box($width: fn.percent-unit(40));
        @include position.absolute(left #{$input-padding-x} top 50%);
        transform: translateY(-50%);

        .vie {
          @include box.box($width: fn.percent-unit(40));
          @include fonts.font-size(40);
        }

        & + .hint,
        & ~ .hint {
          padding-left: fn.percent-unit(72);
        }
      }
      .hint {
        @include fonts.font-family("roboto-medium");
        @include fonts.font-size(32);
        align-items: center;
        color: fn.el-color($color: v-gray, $shade: 61);
        display: flex;
        height: 100%;
        padding: $input-padding-y $input-padding-x;
        margin: 0;

        &.block__title {
          position: relative;
          width: fn.percent-unit(846);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        &:not(:last-child) {
          margin-right: fn.percent-unit(3);
        }
      }
    }

    .panel-right {
      padding-left: fn.percent-unit(12);
    }
    // &-otp {
    // }
  }
}

.block--login-request {
  @at-root .block#{&} {
    padding-left: fn.percent-unit(34);
    position: relative;

    &:before {
      @include box.box($width: 86, $height: 100%);
      @include position.absolute(top 0 bottom 0 left fn.percent-unit(32));
      background-image: linear-gradient(
        90deg,
        #1f1f1f 0%,
        rgba(31, 31, 31, 0) 100%
      );
      content: "";
    }
  }
}

.block--login-request {
  @at-root .block#{&} {
    padding-left: fn.percent-unit(34);
    position: relative;

    &:before {
      @include box.box($width: 86, $height: 100%);
      @include position.absolute(top 0 bottom 0 left fn.percent-unit(32));
      background-image: linear-gradient(
        90deg,
        #1f1f1f 0%,
        rgba(31, 31, 31, 0) 100%
      );
      content: "";
    }
  }
}

.block--login-auth {
  width: fn.percent-unit(570) !important;
}

.block__desc {
  @extend %childStyle;
  font-size: fn.percent-unit(24px);
  color: #fff;
  line-height: 1.5;
  margin: 0 0 fn.percent-unit(32);
  font-weight: normal;
  transform: translateY(fn.percent-unit(0px));
  display: flex;
  &--bold {
    font-weight: 500;
  }
  &--btn {
    margin: 0 6px;
    background: rgba(34, 34, 34, 0.7);
    border: 2px solid #333333;
    border-radius: 4px;
    height: fn.percent-unit(56);
    width: fn.percent-unit(240);
    padding: fn.percent-unit(10);
    font-weight: 700;
    font-size: fn.percent-unit(24);
    line-height: 1.5;
    color: #fff;
    &.focus {
      background: white;
      color: #222222;
      border: 2px solid transparent;
    }
  }
  &--auth {
    color: #dedede;
  }
}

.block--divider {
  margin-left: fn.percent-unit(125);
  width: 1px;
  height: fn.percent-unit(800);
  margin-top: fn.percent-unit(45);
  border-right: 1px solid #cccccc;
  opacity: 0.4;
}

.block__info {
  display: flex;
  width: fn.percent-unit(503);
  position: relative;
  align-items: center;
  justify-content: center;
  flex: 0 0 auto;
  width: fn.percent-unit(924);
}

.content__center {
  position: relative;
  width: fn.percent-unit(680);
  height: fn.percent-unit(400);
  margin: 0 auto;
  &--app {
    margin: 0 auto;
    height: fn.percent-unit(810);
    width: fn.percent-unit(480);
    margin-top: fn.percent-unit(-60);
    .app-description {
      margin-top: fn.percent-unit(80);
      height: fn.percent-unit(490);
      width: fn.percent-unit(680);
      display: flex;
    }
  }
  &--term {
    margin: 0 auto;
    height: fn.percent-unit(810);
    width: fn.percent-unit(480);
    margin-top: fn.percent-unit(130);
  }
  &--web {
    margin: 0 auto;
    margin-top: fn.percent-unit(-130);
  }
}

.content__center--other {
  color: #dedede;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  line-height: 1.5;

  .title {
    font-size: fn.percent-unit(28);
    margin: 0 0 fn.percent-unit(36);
  }

  .qr-code {
    width: fn.percent-unit(396);
    border-radius: fn.percent-unit(8);
    margin-bottom: fn.percent-unit(36);
    overflow: hidden;
    canvas {
      width: 100% !important;
      height: 100% !important;
      display: block;
    }
  }

  .text-line {
    position: relative;
    margin: 0 0 fn.percent-unit(18);
    font-size: fn.percent-unit(28);
    padding: 0 fn.percent-unit(8);
    font-weight: 500;
    &:before,
    &:after {
      content: "";
      display: block;
      width: fn.percent-unit(151);
      height: fn.percent-unit(1);
      position: absolute;
      top: 50%;
      background-color: #dedede;
    }

    &:before {
      left: 0;
      transform: translate(-100%, 0);
    }

    &:after {
      right: 0;
      transform: translate(100%, 0);
    }
  }

  .text {
    font-size: fn.percent-unit(24);
    color: #9b9b9b;
    margin: 0 0 fn.percent-unit(24);
  }

  .highlight {
    color: #fff;
    font-size: fn.percent-unit(28);
    font-weight: 600;
  }

  .highlight-1 {
    font-weight: 700;
  }

  .text-time {
    font-size: fn.percent-unit(24);
    margin: 0;
  }
}

.content__center--error {
  justify-content: center;

  .error-img {
    width: fn.percent-unit(230);
    margin: 0 0 fn.percent-unit(61);

    img {
      display: block;
      width: 100%;
    }
  }

  .error-title {
    font-size: fn.percent-unit(36);
    color: #fff;
    font-weight: 500;
    margin: 0 0 fn.percent-unit(16);
  }

  .error-text {
    font-size: fn.percent-unit(28);
    margin: 0;
  }
}

.app-description {
  display: flex;
  flex-direction: column;
  margin-top: fn.percent-unit(80);

  &--step {
    color: #ffffff;
    display: flex;
    flex-direction: row;
    font-size: fn.percent-unit(32);
    font-weight: 500;
    &:not(:last-child) {
      margin-bottom: fn.percent-unit(50);
    }
    &:last-child {
      margin-top: fn.percent-unit(20);
    }
  }
  &--index {
    margin-right: fn.percent-unit(34);
  }
  &--link {
    font-size: fn.percent-unit(48);
  }
}

.auth-code {
  font-size: fn.percent-unit(70);
  font-weight: 500;
  line-height: 0.2;
}

.img--center {
  width: fn.percent-unit(340);
  height: fn.percent-unit(310);
  display: block;
  margin: 0 auto;
}

.term-detail,
.term-cta-qr {
  font-size: fn.percent-unit(28);
  color: white;
}

.term-cta-qr {
  margin-top: fn.percent-unit(48);
  margin-bottom: fn.percent-unit(24);
}

.term-img {
  width: fn.percent-unit(410);
}

.app-auth-code {
  position: relative;
  width: fn.percent-unit(310);
  height: fn.percent-unit(240);
  .img-logo-app {
    width: 100%;
    height: 100%;
  }
  .code-number {
    position: absolute;
    color: white;
    font-size: fn.percent-unit(58);
    font-weight: 500;
    width: fn.percent-unit(57);
    display: flex;
    justify-content: center;
    align-items: center;
    &:nth-child(1) {
      top: fn.percent-unit(91);
      left: fn.percent-unit(33);
    }
    &:nth-child(2) {
      top: fn.percent-unit(91);
      left: fn.percent-unit(98);
    }
    &:nth-child(3) {
      top: fn.percent-unit(91);
      left: fn.percent-unit(165);
    }
    &:nth-child(4) {
      top: fn.percent-unit(91);
      left: fn.percent-unit(227);
    }
  }
}

.web-auth-code {
  position: relative;
  width: fn.percent-unit(410);
  height: fn.percent-unit(240);
  .web-link {
    position: absolute;
    color: #3ac882;
    font-size: fn.percent-unit(23);
    font-weight: 500;
    top: fn.percent-unit(16);
    left: fn.percent-unit(60);
  }
  .img-logo-app {
    width: 100%;
    height: 100%;
  }
  .code-number {
    position: absolute;
    color: white;
    font-size: fn.percent-unit(46);
    font-weight: 500;
    &:nth-child(1) {
      top: fn.percent-unit(65);
      left: fn.percent-unit(80);
    }
    &:nth-child(2) {
      top: fn.percent-unit(65);
      left: fn.percent-unit(155);
    }
    &:nth-child(3) {
      top: fn.percent-unit(65);
      left: fn.percent-unit(230);
    }
    &:nth-child(4) {
      top: fn.percent-unit(65);
      left: fn.percent-unit(305);
    }
  }
}

.short-description {
  color: #ffffff;
  font-size: fn.percent-unit(32);
  font-weight: 500;
  text-align: center;
  margin-top: fn.percent-unit(60);
}

.main--user-login {
  height: 100vh;
}
