/// @param settings
@use "settings/palettes" as palettes;
// @use 'settings/variables' as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
// @use 'mixin/position' as posit;
// @use 'mixin/pseudo' as pseudo;

/// @group Block-Payment
.block--payment-bill {
  @at-root .block#{&} {
    @include box.box($width: fn.percent-unit(700), $height: auto);
    // background-color: palettes.$gray-f2;
    margin-left: auto;

    .block__body {
      padding: fn.percent-unit(32);
      background-color: fn.el-color($color: v-gray, $shade: 95);

      & > * {
        &:not(:last-child) {
          margin-bottom: fn.percent-unit(15);
        }
      }

      & + .block__footer,
      & ~ .block__footer {
        margin-top: fn.percent-unit(20);
      }
    }

    .text {
      @include fonts.font-family("roboto-regular");
      @include fonts.font-size(28);
      color: palettes.$black;
      line-height: 1.5;

      &.text--muted {
        color: palettes.$gray-64;
      }

      &:not(.text--muted) {
        text-align: right;
      }
    }

    .price--total {
      padding-top: fn.percent-unit(20);
    }

    .input.input--payment-bill .input__control {
      border: 2px solid palettes.$gray-64;
    }

    .block__footer {
      // padding-left: fn.percent-unit(26);
      // padding-right: fn.percent-unit(26);
      padding-left: fn.percent-unit(19);
      // padding-right: fn.percent-unit(19);
      color: #111;

      .badge {
        @include fonts.font-size(23);
        text-align: left;

        span {
          @include fonts.font-family("roboto-medium");
        }
      }
    }
  }
}
