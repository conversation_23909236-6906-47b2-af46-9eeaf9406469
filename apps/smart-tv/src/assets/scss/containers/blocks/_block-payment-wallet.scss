/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  =========== BLOCK PAYMENT WALLET ===========
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* STYLES
*
*/

/// @param settings
@use "settings/function" as fn;
@use "settings/variables" as var;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;

/// @group style

.block--payment-wallet {
  &.block--payment-wallet--napas {
    background-color: #a4cd39;

    .block__text,
    .card.card--qr-code .card__text {
      color: #1f417d;
    }

    .block__header {
      background-color: rgba($color: #1f417d, $alpha: 0.2);
    }
  }

  @at-root .block--payment#{&} {
    .block__header {
      padding: fn.percent-unit(20) fn.percent-unit(32);

      img {
        @include box.box($width: fn.percent-unit(94));
        flex: 0 0 fn.percent-unit(94);

        & + .block__text,
        & ~ .block__text {
          margin-left: fn.percent-unit(10);
        }
      }

      .block__text {
        font-size: fn.percent-unit(22);
        line-height: 1.5;
      }

      & + .block__body,
      & ~ .block__body {
        padding-top: fn.percent-unit(42);
        padding-bottom: fn.percent-unit(144);
      }
    }

    .block__header-shopeepay {
      background-color: rgba(
        $color: fn.el-color($color: v-tomato, $shade: shopee-pay-header),
        $alpha: 0.2
      );
    }

    .block__header-momo {
      background-color: rgba(
        $color: fn.el-color($color: v-pinks, $shade: momo-header),
        $alpha: 0.2
      );
    }

    .flex-box {
      display: flex;
      align-items: center;
    }

    .block__body {
      padding-left: fn.percent-unit(12);
      padding-right: fn.percent-unit(12);
    }

    .card--qr-code {
      width: fn.percent-unit(374px);
      height: fn.percent-unit(452px);
    }

    .block__body.block__body--napas {
      padding-top: fn.percent-unit(18);
      padding-bottom: fn.percent-unit(36);
      display: flex;

      &.block__body--napas--no-recurring {
        flex-direction: column;

        .card.card--qr-code {
          width: 100%;
          max-width: 100%;

          &:after {
            display: none;
          }
        }
      }

      .card.card--qr-code {
        width: 50%;
        max-width: 50%;
        padding-right: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        height: auto;

        &:after {
          content: "";
          display: block;
          width: fn.percent-unit(3);
          height: calc(100% + 3.75vw);
          position: absolute;
          top: 0;
          right: 0;
          background-color: #004d8c;
          opacity: 0.3;
          transform: translate(200%, -1.875vw);
        }

        .card__text {
          padding: 0;
        }
      }

      .block__other-solution {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: fn.percent-unit(80px) 0 0 fn.percent-unit(21px);
        height: 100%;
        width: 50%;
        max-width: 50%;
        box-sizing: border-box;

        .block__text {
          font-size: fn.percent-unit(24px);
          line-height: 150%;
          text-align: center;

          &--napas-link {
            font-size: fn.percent-unit(36px);
            font-weight: 500;
          }
        }

        .block__text + .block__text {
          margin-top: fn.percent-unit(12);
          word-break: break-all;
        }
      }
    }

    .block__footer.block__footer--napas {
      display: flex;
      justify-content: center;
      align-items: center;
      border-top: fn.percent-unit(4px) solid rgba(0, 77, 140, 0.3);
      font-size: fn.percent-unit(20px);
      height: fn.percent-unit(152px);

      &.block__footer--napas--no-recurring {
        flex-direction: column;
        padding: 0 fn.percent-unit(15px);

        .block__text {
          font-size: fn.percent-unit(24px);
          line-height: 150%;
          text-align: center;

          &--napas-link {
            font-size: fn.percent-unit(36px);
            font-weight: 500;
          }
        }

        .block__text + .block__text {
          margin-top: fn.percent-unit(12);
          word-break: break-all;
        }
      }

      .block__text {
        line-height: 150%;
        text-align: center;

        p {
          margin: 0;
        }

        .block__text-highlight {
          font-weight: 700;
        }
      }
    }

    .block__body.block__body--zalopay-confirm-box {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      margin-top: fn.percent-unit(20%);
      align-items: center;
      padding: fn.percent-unit(160) 0;
      .block__text {
        font-size: fn.percent-unit(24);
        line-height: 150%;
        margin-bottom: fn.percent-unit(40px);
        margin-top: fn.percent-unit(30px);

        &.text-center {
          text-align: center;
        }
      }

      .logo {
        width: fn.percent-unit(291);
        height: fn.percent-unit(97);

        & > img {
          height: 100%;
          width: auto;
        }
      }
    }

    .block__body.block__body--shopeepay-confirm-box {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      margin-top: fn.percent-unit(20%);
      align-items: center;

      .block__text {
        font-size: fn.percent-unit(24);
        line-height: 150%;
        margin-bottom: fn.percent-unit(40px);
        margin-top: fn.percent-unit(30px);

        &.text-center {
          text-align: center;
        }
      }

      .logo {
        width: fn.percent-unit(220);
        height: fn.percent-unit(220);

        & > img {
          height: 100%;
          width: auto;
        }
      }
    }
  }
}
