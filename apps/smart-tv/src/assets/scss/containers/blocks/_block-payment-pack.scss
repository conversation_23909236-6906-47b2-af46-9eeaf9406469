/// @param settings
@use "settings/palettes" as pales;
// @use 'settings/variables' as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
// @use 'mixin/position' as posit;
// @use 'mixin/pseudo' as pseudo;

/// @group Block-Payment
.block--payment-pack {
  @at-root .block#{&} {
    //.block__body.blur .focus::before {
    //  opacity: 0.5;
    //}

    // .block__header{
    //   margin-bottom: fn.percent-unit(18);
    //   padding: 0 fn.percent-unit(6);
    // }

    // .block__title{
    //   @include fonts.font-family("roboto-medium");
    //   @include fonts.font-size(32);
    //   color: pales.$black;
    //   margin: 0;
    // }
    .block__title {
      margin-top: fn.percent-unit(10);
      line-height: 1.5;
      @include fonts.font-size(32);
    }

    .block__sapo {
      line-height: 1.5;
      margin: 0 0 fn.percent-unit(15) 0;
      @include fonts.font-size(20);
    }

    .block__body {
      width: 100%;
      display: flex;
      //flex-wrap: wrap;
      margin: 0 fn.percent-unit(-10);

      .wrapper {
        flex: 0 0 calc(20%);
        box-sizing: border-box;
        padding: 0 fn.percent-unit(10);
        margin-bottom: fn.percent-unit(20);
      }

      // .card--payment-pack {
      // }
    }
  }
}
