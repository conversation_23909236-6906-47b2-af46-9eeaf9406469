/// @param settings
@use "settings/palettes" as pales;
// @use 'settings/variables' as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/position" as posit;
// @use 'mixin/pseudo' as pseudo;

/// @group Block-Payment
.block--payment-method {
  @at-root .block#{&} {
    .block__header {
      margin-bottom: fn.percent-unit(20);
    }

    .block__body {
      @include box.box($width: auto, $height: fn.percent-unit(863));
      padding-top: fn.percent-unit(8);
      padding-bottom: fn.percent-unit(8);
      padding-left: fn.percent-unit(8);
      padding-right: fn.percent-unit(10);
      overflow: hidden;
      position: relative;
    }
  }
}
