/**
* ============VERSION============
* VieON for Smart TV (LG & Samsung)
* Version 1.0.0
* https://vieon.vn
* Author: VieON FE Team.
*/

/*--------------------------------------------*\
  ============= BLOCK PAYMENT IPC =============
/*--------------------------------------------*/

/**
*
* ============ CONTENTS ============
*
* USE SETTING
* USE MIXIN
* VARIABLE
* IPC STYLES
*
*/

/// @param settings
@use "settings/function" as fn;
@use "settings/variables" as var;

/// @group mixin
@use "mixin/theme" as theme;

/// @group variable
$block-themes: (
  block-bg: (
    "base": rgba(
        fn.el-color(
          $color: v-gray,
          $shade: 87,
        ),
        0.2
      ),
    // Description
    "p1": fn.el-color(
        $color: v-partner,
        $shade: vp3,
      ),
    // Description
    "p2": fn.el-color(
        $color: v-partner,
        $shade: vp4,
      ),
    // Description
    "p3": fn.el-color(
        $color: v-partner,
        $shade: vp2,
      ),
    // Description
    "p4": fn.el-color(
        $color: v-partner,
        $shade: vp9,
      ),
    // Description
    "p5": fn.el-color(
        $color: v-partner,
        $shade: vp4,
      ),
    // Description
    "p6": fn.el-color(
        $color: v-red,
        $shade: viettel,
      ),
    // Description
    "p7": fn.el-color(
        $color: v-partner,
        $shade: vp1,
      ),
    // Description
    "p8": fn.el-color(
        $color: v-partner,
        $shade: vp10,
      ),
    // Description
    "p9": fn.el-color(
        $color: v-red,
        $shade: viettel-pay,
      ),
    // ShopeePay
    "p10": fn.el-color(
        $color: v-tomato,
        $shade: shopee-pay,
      ),
    // MoMo
    "p11": fn.el-color(
        $color: v-pinks,
        $shade: momo,
      ),
  ),
  block-color: (
    "p1": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    "p2": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    "p3": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    "p4": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    "p5": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    "p6": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    "p7": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    "p8": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    "p9": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    "p10": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
    "p11": fn.el-color(
        $color: v-white,
        $shade: base,
      ),
  ),
  block-footer-bg: (
    "p1": fn.el-color(
        $color: v-partner,
        $shade: vp5,
      ),
    // Description
    "p2": fn.el-color(
        $color: v-partner,
        $shade: vp6,
      ),
    // Description
    "p3": rgba(
        fn.el-color(
          $color: v-partner,
          $shade: vp8,
        ),
        0.2
      ),
    // Description
    "p5": fn.el-color(
        $color: v-partner,
        $shade: vp5,
      ),
    // Description
    "p6": fn.el-color(
        $color: v-red,
        $shade: viettel-footer,
      ),
    // Description
    "p7": fn.el-color(
        $color: v-partner,
        $shade: vp7,
      ),
    // Description
  ),
  block-header-bg: (
    "p1": fn.el-color(
        $color: v-partner,
        $shade: vp5,
      ),
    // Description
    "p2": fn.el-color(
        $color: v-partner,
        $shade: vp6,
      ),
    // Description
    "p3": rgba(
        fn.el-color(
          $color: v-partner,
          $shade: vp8,
        ),
        0.2
      ),
    // Description
    "p5": fn.el-color(
        $color: v-partner,
        $shade: vp5,
      ),
    // Description
    "p6": fn.el-color(
        $color: v-gray,
        $shade: 87,
      ),
    // Description
    "p7": fn.el-color(
        $color: v-partner,
        $shade: vp7,
      ),
    // Description
    "p8": rgba(
        fn.el-color(
          $color: v-partner,
          $shade: vp11,
        ),
        0.2
      ),
    "p9": fn.el-color(
        $color: v-red,
        $shade: viettel-pay-header,
      ),
    "p10": fn.el-color(
        $color: v-tomato,
        $shade: shopee-pay-header,
      ),
    "p11": fn.el-color(
        $color: v-pinks,
        $shade: momo-header,
      ),
  ),
);

/// @group theme

@include theme.themes(
  $themes: var.$block-themes,
  $element: ".block",
  $element-theme: $block-themes,
  $element-bg: block-bg,
  $element-color: block-color,
  $element-footer-bg: block-footer-bg,
  $element-header-bg: block-header-bg
);
