@use "../../settings/variables" as var;
@use "../../settings/function" as fn;
@use "../../settings/palettes" as pales;

$screen-hd: 1280px !default;

$EasingLinear: linear;

@mixin flexPosition($justify-content, $align-item) {
  -webkit-justify-content: $justify-content;
  justify-content: $justify-content;
  -webkit-align-items: $align-item;
  align-items: $align-item;
}

@mixin displayFlex() {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

@mixin flexItem() {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
}

@mixin TransitionWithDurationAndEase($duration, $easing) {
  -webkit-transition: all $duration $easing;
  -moz-transition: all $duration $easing;
  -ms-transition: all $duration $easing;
  -o-transition: all $duration $easing;
  transition: all $duration $easing;
}

@mixin box-shadow($args) {
  -webkit-box-shadow: $args;
  -moz-box-shadow: $args;
  box-shadow: $args;
}

@mixin radius($radius) {
  -webkit-border-radius: $radius;
  -moz-border-radius: $radius;
  -ms-border-radius: $radius;
  -o-border-radius: $radius;
  border-radius: $radius;
}

@mixin focused-item() {
  @include box-shadow(0 0.5em 2em rgba(0, 0, 0, 1));
}

.main--user-profile {
  // Responsive HD
  @media screen and (max-width: $screen-hd) {
    font-size: 66.67%;
  }

  .ribbon {
    padding-left: 0;
  }

  .page-wrap,
  .content-right {
    height: 100%;
  }

  .content-right {
    display: flex;
    flex-direction: column;
  }

  .sect {
    &-signup {
      &.sect-profile.sect-single {
        .profile-inner {
          .icon-profile-page {
            &.icon-profile-user {
              padding-bottom: 1.325em;
              color: #6c6c6c;
              background-color: #2f2f2f;
              border-color: #6c6c6c;

              .vie {
                font-size: 10em;
              }
            }
          }
        }
      }
    }
  }

  .sect-single,
  .section {
    @include flexItem;
    flex: 1;

    &.sect-sign-out,
    &.sect-profile,
    &.sect-login,
    &.sect-devices,
    &.section--profile {
      flex-direction: column;
      -webkit-flex-direction: column;

      .sign-out-inner,
      .profile-inner,
      .login-inner,
      .profile-empty {
        @include flexItem;

        &.profile-inner {
          -webkit-align-items: center;
          align-items: center;
        }

        height: 100%;
        flex-direction: column;
        @include TransitionWithDurationAndEase(0.27s, $EasingLinear);

        .avatar-circle {
          margin: 0 0 2.5em;
          width: 15em;
          height: 15em;
          border: 0.3em solid #cecece;
        }

        .icon-profile-page {
          margin: 0 0 2.5em;
          color: #6c6c6c;
          @include flexItem;

          &.icon-profile-user {
            width: 15em;
            height: 15em;
            border: 0.3em solid #9b9b9b;
            padding: 0.5em;
            @include radius(50%);
            overflow: hidden;
            background-color: #6c6c6c;
          }

          [class^="icon-"],
          .vie {
            height: fn.percent-unit(283px);
            width: fn.percent-unit(283px);
            stroke-width: 0;
            stroke: currentColor;
            fill: currentColor;
          }
        }

        .qr-code {
          display: block;
          margin-top: fn.percent-unit(40px);
          height: fn.percent-unit(200px);
          width: fn.percent-unit(200px);

          &-inner {
            max-width: 12em;
          }
        }

        .sign-out-name,
        .title-inner {
          font-size: fn.percent-unit(48px);
          font-weight: 400;
          color: #e3e3e3;
          margin-bottom: 0.3em;
          text-align: center;
          // text-transform: uppercase;
        }

        .title-code {
          font-size: 2.9em;
          font-weight: 300;
          margin-bottom: 1em;

          img {
            max-width: 1.3em;
          }
        }

        .sign-out-mail,
        .link {
          font-size: fn.percent-unit(32px);
          margin: 0 !important;
          margin-bottom: fn.percent-unit(20px) !important;
          color: #6c6c6c;
          max-width: 90%;
          text-align: center;
        }

        .title-inner {
          font-size: 2.5em;
          margin-bottom: 0.1em;
          color: #6c6c6c;
        }

        .link {
          color: #e3e3e3;
        }

        .btn {
          min-width: fit-content;
          margin-top: fn.percent-unit(30px);

          &.focus {
            background: #ffffff;
          }
        }

        .profile-empty-img {
          display: block;
          height: fn.percent-unit(400px);
          width: auto;
          margin-bottom: fn.percent-unit(40px);

          > img {
            display: block;
            height: 100%;
            margin: auto;
          }
        }
      }
      .profile-empty--renting-contents {
        margin-top: fn.percent-unit(280px);
        .profile-empty-img {
          height: fn.percent-unit(245px);
          margin-bottom: fn.percent-unit(63px);
        }
      }

      .login-inner {
        max-width: 100em;
        text-align: center;

        .sign-out-mail {
          margin-bottom: fn.percent-unit(32px);
          max-width: 100%;
        }

        .title-inner {
          color: #e3e3e3;
          margin-bottom: 0.3em;
        }

        .form-control {
          &--ip {
            min-width: 17.5em;
            @include radius(2px);
            border: 2px solid #4a4a4a;
            margin-bottom: 1.6em;
            font-size: 2.5em;
            background: #4a4a4a00;
            text-align: center;
            color: #e3e3e3;
            letter-spacing: 0.25em;

            & ~ .icon,
            & + .icon {
              font-size: 3em;
              color: #ffffff;

              &.middle {
                width: 1.375em;
                height: 1.375em;
                top: 50%;
                bottom: initial;
                margin-top: -0.6875em;
                right: 0.25em;
                display: flex;
                align-items: center;
                justify-content: center;
              }

              &.focus {
                color: #000000;
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.5);
              }
            }
          }

          .btn {
            margin-bottom: 0;
          }

          .group-btn {
            max-width: 45em;

            .btn {
              margin-bottom: 0;
              min-width: 12.5em;

              &:first-child {
                margin-bottom: 1em;
              }
            }
          }
        }

        .keyboard {
          font-size: 2.5em;
          color: #9b9b9b;
          position: relative;
          padding-right: 4.25em;
          margin-bottom: 1em;

          .lst {
            .chr {
              display: inline-flex;
              padding: 0.25em;
              width: 1.625em;
              height: 1.625em;
              @include radius(5em);
              font-weight: 500;
              font-size: 1.125em;
              justify-content: center;
              align-items: center;
              line-height: normal;
              margin: 0.12em 0.125em 0.25em;

              &.focused,
              &.hover {
                // color: #9b9b9b;
                // background:  #3ac88a;
                color: #000000;
                background: rgba(#ffffff, 1);
                @include focused-item;
              }

              &:hover {
                background: rgba(#ffffff, 0.1);
              }

              &.login {
                &.focused {
                  color: #000000;
                  background: #ffffff;
                  box-shadow: 0.125em 0.125em 0.5em rgba(#000000, 0.5);
                  // @include focused-item;
                }
              }
            }

            &.lst-rt {
              position: absolute;
              right: 0;
              top: 0;

              .chr {
                display: block;
                width: auto;
                padding: 0.125em 0.25em;

                [class^="icon-"] {
                  display: block;
                  font-size: 1.1em;
                }

                &-last {
                  &.remove {
                    height: 1.625em;
                    width: auto;
                    margin-bottom: 0.5em;
                    padding: 0;

                    // font-size: ;
                    .vie {
                      font-size: 1.5em;
                      line-height: normal;
                    }
                  }

                  &.clear {
                    display: flex;
                    height: 1.5em;
                  }
                }
              }
            }
          }
        }
      }
    }

    &.sect-profile {
      .profile-inner {
        .icon-profile-page {
          &.icon-profile-user {
            // padding-top: 3.5em;
            border-color: #9b9b9b;
            background-color: #6c6c6c;
            color: #9b9b9b;

            .vie {
              font-size: 12em;
            }

            &.pt-3 {
              padding-top: 3.5em;
            }
          }
        }

        .sign-out-name {
          // text-transform: uppercase;
          margin: 0 !important;
          margin-bottom: fn.percent-unit(20px) !important;
        }

        .sign-out-mail {
          max-width: 100%;
          font-size: fn.percent-unit(32px);
          margin-bottom: 1.5em;
        }
      }
    }

    &.sect-empty-page {
      .profile-empty-img {
        display: block;
        width: 26em;
        height: 21em;
        margin: 0 auto 1.5em;

        > img {
          display: block;
          height: 100%;
          margin: auto;
        }
      }

      .sign-out-name {
        font-size: fn.percent-unit(32px);
        font-weight: 500;
        color: #e3e3e3;
        text-align: center;
      }
    }

    &.sect-devices {
      color: pales.$white;
      width: fn.percent-unit(1136px);
      margin-left: fn.percent-unit(76px);
      justify-content: flex-start;
    }
  }

  .sect-devices {
    background-color: pales.$gray-11;
    height: fn.percent-unit(951);

    &__group-subtitle {
      padding-left: fn.percent-unit(18);
    }

    &__group-title {
      flex: 0 0 auto;
      margin-top: fn.percent-unit(38);
      background-color: pales.$gray-11;
      position: relative;
      z-index: 1;
      padding-bottom: fn.percent-unit(10);
      height: fn.percent-unit(72);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &__title {
      flex: 0 0 auto;
      font-weight: 500;
      font-size: fn.percent-unit(36px);
      line-height: 140%;
      letter-spacing: fn.percent-unit(0.3);

      &--current {
        padding-bottom: fn.percent-unit(10);
      }
    }

    &__subtitle {
      font-weight: 500;
      font-size: fn.percent-unit(28px);
      line-height: 140%;
      letter-spacing: fn.percent-unit(0.3);
      margin-bottom: fn.percent-unit(5);
    }

    &__description {
      font-weight: 400;
      font-size: fn.percent-unit(28px);
      line-height: 150%;
      letter-spacing: fn.percent-unit(0.3);
      opacity: 0.7;
      color: pales.$gray-de;
    }

    .device-list {
      overflow: hidden;
      position: relative;
      padding-top: fn.percent-unit(4);
      box-sizing: border-box;
      .grid {
        display: block !important;
      }
      &--current {
        flex: 0 0 auto;
      }
      &--scroll {
        &::after {
          content: "";
          position: absolute;
          width: 100%;
          height: fn.percent-unit(79);
          bottom: 0;
          left: 0;
          background: linear-gradient(0deg, #111111 0%, transparent 100%);
          z-index: 1;
        }

        &.scrolling::before {
          content: "";
          position: absolute;
          width: 100%;
          height: fn.percent-unit(79);
          top: 0;
          left: 0;
          background: linear-gradient(180deg, #111111 0%, transparent 100%);
          z-index: 1;
        }
      }
      &--empty {
        .sect-devices__subtitle {
          color: #9b9b9b;
        }
      }
    }

    .device-focus-box {
      width: fn.percent-unit(1124px);
      position: absolute;
      top: 0;
      left: fn.percent-unit(4);
      height: fn.percent-unit(158px);
      outline: fn.percent-unit(5px) solid pales.$white;
      pointer-events: none;
      border-radius: fn.percent-unit(4);
    }

    .device-wrapper {
      margin: fn.percent-unit(4) fn.percent-unit(4) fn.percent-unit(12)
        fn.percent-unit(4);
    }

    .device-item {
      padding: 0 fn.percent-unit(46);
      height: fn.percent-unit(158px);
      box-sizing: border-box;
      border: fn.percent-unit(2px) solid
        rgba($color: pales.$gray-de, $alpha: 0.5);
      border-radius: 4px;
      display: flex;
      flex: 0 0 100%;
      align-items: center;
      position: relative;

      &.focus {
        outline: fn.percent-unit(5) solid pales.$white;
      }

      &__icon {
        // width: fn.percent-unit(60px);
        // height: fn.percent-unit(50.32px);
        svg {
          overflow: overlay;

          path {
            fill: pales.$gray-9b;
            stroke: pales.$gray-9b;
          }
        }

        &.active {
          svg {
            overflow: overlay;

            path {
              fill: pales.$green-3a;
              stroke: pales.$green-3a;
            }
          }
        }
      }
    }

    .btn {
      font-weight: 500;
      font-size: fn.percent-unit(28px);
      line-height: 140%;
      letter-spacing: fn.percent-unit(0.3);
      white-space: nowrap;
      width: fit-content;
      color: pales.$white;
    }
  }

  // .nav-show {
  //   .sect-profile {
  //     .profile-inner {
  //       /*padding-left: 7.5em;
  //               @include translate(-19em, 0);*/
  //     }
  //   }
  // }

  .profile-empty {
    .text {
      font-size: 2.25em;
      line-height: 1.5em;
      color: #ffffff;

      & ~ .btn,
      & + .btn {
        margin-top: 1.5em;
      }
    }

    .profile-empty-img {
      width: 41em;
      text-align: center;
      margin-bottom: 2.5em;

      .icon {
        &.icon-profile-empty {
          width: 18.625em;
          height: 18.625em;
          display: inline-block;

          .vie {
            color: #5f5f5f;
            font-size: 18.625em;
          }
        }
      }
    }
  }

  .card--horizontal {
    min-width: fn.percent-unit(361px);
    height: fn.percent-unit(203px);
    border: fn.percent-unit(6px) solid transparent;

    .card__img .bg-box {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-repeat: no-repeat;
      background-position: center top;
      background-size: cover;
      z-index: -1;
    }
  }

  .grid-list {
    &--container {
      overflow-y: auto;
      position: relative;
    }

    .grid {
      // transition-duration: 0.2s;
      transform: translateY(0);
    }
  }

  .card--poster {
    &.card {
      width: fn.percent-unit(238px);
      height: fn.percent-unit(340px);

      .card__thumbnail {
        width: fn.percent-unit(238px);
        height: fn.percent-unit(340px);

        & > .card__img {
          width: 100%;
          height: 100%;

          .bg-box {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-repeat: no-repeat;
            background-position: center top;
            background-size: cover;
            z-index: -1;
          }

          & > img {
            width: 100%;
            height: 100%;
          }
        }
      }

      &:not(:last-child) {
        margin-right: fn.percent-unit(8px);
        margin-bottom: fn.percent-unit(8px);
      }
    }
  }

  .card-focus-box {
    width: fn.percent-unit(232px);
    height: fn.percent-unit(334px);
    position: absolute;
    top: fn.percent-unit(0px);
    left: 0;
    border: 4px solid pales.$white;
  }

  // Profile subs
  .wrapper-section.wrapper-section--subs {
    height: fn.percent-unit(1080-128-30);
    padding-left: fn.percent-unit(265-144);
    overflow-y: hidden;
  }
  .sub-list {
    &__title-box {
      display: flex;
      align-items: center;
      color: #fff;
      padding-bottom: fn.percent-unit(12px);
    }
    &__title {
      font-size: fn.percent-unit(24px);
      font-weight: 500;
    }
    &__title-note {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: fn.percent-unit(24px);
      font-size: fn.percent-unit(20px);
      font-weight: 400;
      & > .icon {
        color: #3ac882;
        width: fn.percent-unit(32px);
        height: fn.percent-unit(32px);
        margin-right: fn.percent-unit(10px);
        & > img {
          width: 100%;
          height: 100%;
        }
      }
    }
    &__list {
      display: flex;
      flex-flow: row wrap;
    }
  }
  .sub-item {
    box-sizing: border-box;
    width: fn.percent-unit(450px);
    height: fn.percent-unit(246px);
    padding: fn.percent-unit(32px) fn.percent-unit(16px) fn.percent-unit(16px)
      fn.percent-unit(32px);
    margin-right: fn.percent-unit(24px);
    margin-bottom: fn.percent-unit(24px);
    border: fn.percent-unit(4px) solid;
    border-radius: fn.percent-unit(14px);
    color: #fff;
    display: flex;
    flex-direction: column;
    &__title {
      font-size: fn.percent-unit(32px);
      line-height: fn.percent-unit(37.5px);
      font-weight: 500;
      margin-bottom: fn.percent-unit(12px);
    }
    &__info {
      font-size: fn.percent-unit(23px);
      line-height: 1.5;
    }
    &__note {
      margin-top: auto;
      font-size: fn.percent-unit(20px);
      font-weight: 500;
    }
    &--active {
      border-color: rgba(58, 200, 130, 0.5);
    }
    &--remain {
      border-color: #dedede;
      .sub-item__info {
        color: #ccc;
      }
    }
    &--expired {
      border-color: #333333;
      .sub-item__title {
        color: #ccc;
      }
      .sub-item__info {
        color: #9b9b9b;
      }
    }
    &.focus {
      background: #fff;
      border-color: #fff;
      .sub-item__title {
        color: #111;
      }
      .sub-item__info {
        color: #222;
      }
      .sub-item__note {
        color: #111;
      }
    }
  }
}

.popup-profile {
  &__desc {
    font-size: fn.percent-unit(45px);
    text-align: center;
  }

  .popup__content {
    width: 100%;
    height: 100%;
    display: flex;
    align-content: center;
    justify-content: center;
    flex-direction: column;
    background: #000000;
    color: #ffffff;
  }

  .popup__button {
    display: flex;
    flex-direction: row;
    justify-content: center;

    .btn {
      margin-right: fn.percent-unit(15px);
      min-width: fn.percent-unit(150px);
    }
  }
}
