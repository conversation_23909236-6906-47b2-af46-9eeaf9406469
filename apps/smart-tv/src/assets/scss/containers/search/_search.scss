/// @group Settings
@use "../../settings/variables" as var;
@use "../../settings/function" as fn;
@use "../../settings/palettes" as pales;
@use "../../mixin/flex" as flexbox;

$keySizeWidth: 72;
$keySizeHeight: 52;
$keyMargin: 2.5;

%display-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}
.search-page {
  color: pales.$white;
  .page-content {
    height: 100vh;
    width: 100vw;
  }
  .page-wrap {
    padding: 0 fn.percent-unit(20px) 0 fn.percent-unit(250px);
    @extend %display-flex;
    @include flexbox.flex-direction(row);
  }

  &--left {
    //@include flexbox.flex('grow');
    height: 100%;
    min-width: fn.percent-unit(457px);

    &.noscroll {
      transform: translateY(fn.percent-unit(35));
      transition-duration: 0.2s;
    }
    &.scroll {
      transform: translateY(fn.percent-unit(-420));
      transition-duration: 0.2s;
    }
  }
  &--right {
    @include flexbox.flex("grow");
    height: 100%;
    padding-left: fn.percent-unit(95px);
    min-width: fn.percent-unit(1213px);
  }
  &__title {
    font-weight: 500;
    font-size: fn.percent-unit(36px);
    line-height: 1.4;
  }

  &__no-result {
    font-size: fn.percent-unit(28px);
    line-height: 1.5;
    .disabled {
      opacity: 0.3;
    }
    &.hidden {
      display: none;
    }
    .no-result {
      &__title {
        font-weight: 500;
        font-size: fn.percent-unit(36px);
        line-height: 1.4;
        padding-top: fn.percent-unit(48px);
      }
      &__image {
        @extend %display-flex;
        @include flexbox.flex-direction(column);
        @include flexbox.align-items(center);
        p {
          margin-bottom: 0;
        }
      }
      &__suggest {
        ul {
          margin: fn.percent-unit(10px) 0;
        }
        p {
          margin-bottom: 0;
        }
        color: pales.$gray-9b;
      }
    }
  }

  .keyboard {
    &__item {
      font-size: fn.percent-unit(28px);
      line-height: 150%;
    }
  }

  .suggest-key {
    &__list {
      @extend %display-flex;
      @include flexbox.flex-direction(column);
      margin-top: fn.percent-unit(4);
      &.opacity {
        .suggest-key__item {
          color: #616161;
          &.active {
            background-color: #333333;
            color: #dedede;
          }
          &.focus {
            background-color: pales.$white;
            color: #222222;
          }
        }
      }
    }
    &__item {
      height: fn.percent-unit(58px);
      border-radius: fn.percent-unit(4px);
      font-size: fn.percent-unit(28px);
      line-height: 1.5;
      color: #9b9b9b;
      margin-bottom: fn.percent-unit(
        1px
      ); // need for focus key working correctly. Please don't remove
      @extend %display-flex;
      @include flexbox.align-items(center);
      p {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 fn.percent-unit(20px);
        margin: 0;
      }
      &.active {
        background-color: #333333;
        color: #dedede;
      }
      &.focus {
        background-color: pales.$white;
        color: #222222;
      }
    }
  }

  .result-list {
    &--container {
      overflow-y: hidden;
      position: relative;
    }
    .grid {
      // transition-duration: 0.2s;
      transform: translateY(0);
      margin-top: fn.percent-unit(135px);
    }
  }
  .card--horizontal {
    width: fn.percent-unit(361px - 10px);
    height: fn.percent-unit(203px - 10px);
    margin: fn.percent-unit(10px);
    border: none;

    &:nth-child(1),
    &:nth-child(2),
    &:nth-child(3) {
      margin-top: 0;
    }

    .card__img .bg-box {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-repeat: no-repeat;
      background-position: center top;
      background-size: cover;
      z-index: -1;
    }
  }
}
.grid-list__title {
  font-weight: 500;
  font-size: fn.percent-unit(36px);
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: absolute;
  top: 0;
  height: fn.percent-unit(75px);
  width: 100%;
  left: 0;
  padding-top: fn.percent-unit(48px);
  z-index: 1;

  &.scroll {
    background: rgba(11, 11, 11, 0.8);
  }
}

.search-focus-box {
  width: fn.percent-unit(349px);
  position: absolute;
  top: fn.percent-unit(122px);
  left: 0;
  height: fn.percent-unit(191px);
  border: 4px solid pales.$white;
}
