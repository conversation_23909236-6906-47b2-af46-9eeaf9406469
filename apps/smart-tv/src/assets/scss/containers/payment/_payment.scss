@use "settings/function" as fn;
@use "settings/variables" as var;
@use "settings/palettes" as pales;

$screen-hd: 1280px !default;
$bg-nav: #080e17;
$c-g80: #646464;
$c-green-3a: #3ac882;
$c-credit-card: #e6ba47;
$c-momo: #ae2070;
$c-vnpay: #2181d8;
$c-mobifone: #8eb8f8;
$c-moca: #1092d4;
$c-zalopay: #62c2ff;
$c-vinaphone: #005e96;
$c-viettelsms: #ee0033;
$c-payoo: #004f9e;
$c-gold-da: #da9e1c;
$c-9b: #9b9b9b;
.main.main--payment {
  height: 100vh;
  margin-left: fn.percent-unit(var.$sidebar-max-width);
  position: relative;
  *,
  *::after,
  *::before {
    box-sizing: border-box;
  }
  .d-flex {
    display: flex;
  }
  .justify-content-space-between {
    justify-content: space-between;
  }
  .align-items-end {
    align-items: flex-end;
  }
  .payment-steps {
    background-color: pales.$white;
    padding: fn.percent-unit(60px);
    height: 100%;
    &.bg--momo {
      background: #fff
        url("https://static-smarttv.vieon.vn/vieon-images/smarttv_payment_bg_momo.png")
        no-repeat top right;
      background-size: cover;
    }
    &.bg--vnpay {
      background: #fff
        url("https://static2.vieon.vn/vieon-images/smarttv_payment_bg_vnpay.png")
        no-repeat top right;
      background-size: cover;
    }

    &.bg--moca {
      background: #fff
        url("https://static-smarttv.vieon.vn/vieon-images/smarttv_payment_bg_moca.png")
        no-repeat top right;
      background-size: cover;
    }

    &.bg--zalopay {
      background: #fff
        url("https://static-smarttv.vieon.vn/vieon-images/smarttv_payment_bg_zalopay.png")
        no-repeat top right;
      background-size: cover;
    }

    &.bg--linked-account {
      background: none;
      background-color: #fff;
    }

    &.bg--payoo {
      padding: 0;
    }

    .ps-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: fn.percent-unit(120px);
      margin-top: fn.percent-unit(-60px);
      margin-left: fn.percent-unit(-60px);
      margin-right: fn.percent-unit(-60px);
      margin-bottom: fn.percent-unit(24px);
      padding: 0 fn.percent-unit(115px);
      background-color: pales.$gray-11;
      .box {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        color: pales.$white;
        font-size: fn.percent-unit(24px);
        position: relative;
        &:not(:first-child)::before {
          display: block;
          content: "";
          position: absolute;
          top: 25%;
          left: -50%;
          transform: translateY(-50%);
          width: 100%;
          height: fn.percent-unit(4px);
          background: pales.$white;
          z-index: 1;
        }
        .title {
          margin-top: fn.percent-unit(5px);
          line-height: 1.5;
          position: relative;
        }
        .step-number {
          display: flex;
          justify-content: center;
          align-items: center;
          width: fn.percent-unit(46px);
          height: fn.percent-unit(46px);
          border-radius: 50%;
          border: fn.percent-unit(2px) solid pales.$white;
          z-index: 2;
          position: relative;
          background-color: pales.$gray-11;
          .icon {
            width: fn.percent-unit(26px);
            height: fn.percent-unit(26px);
          }
        }
        &.focused,
        &.active {
          .step-number {
            border-color: pales.$green-3a;
            background: pales.$green-3a;
          }
          &:not(:first-child)::before {
            background: pales.$green-3a;
          }
        }
      }
    }
    .ps-package-title {
      font-size: fn.percent-unit(36px);
      line-height: 1.15;
      font-weight: 500;
      margin-top: fn.percent-unit(43px);
      margin-bottom: fn.percent-unit(16px);
    }
    .ps-package-sub-title-group {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: fn.percent-unit(38px);
      .ps-package-sub-title {
        padding-left: fn.percent-unit(28px);
        padding-right: fn.percent-unit(28px);
        position: relative;
        &:not(:last-child)::after {
          position: absolute;
          content: "";
          background: pales.$green-3a;
          width: fn.percent-unit(10px);
          height: fn.percent-unit(10px);
          border-radius: 50%;
          top: 50%;
          right: fn.percent-unit(-5px);
          transform: translateY(-50%);
        }
        &:first-child {
          padding-left: 0;
        }
      }
    }
    .ps-package-sub-title {
      font-size: fn.percent-unit(28px);
      color: pales.$green-3a;
      font-weight: 500;
    }
    .ps-package-list {
      padding-top: fn.percent-unit(80px);
      &--box {
        display: flex;
        justify-content: space-around;
        .item {
          width: fn.percent-unit(806px);
          height: fn.percent-unit(526px);
          overflow: hidden;
          &:not(:last-child) {
            margin-right: fn.percent-unit(24px);
          }
          & > img {
            width: 100%;
            height: auto;
          }
          &.focused {
            outline: fn.percent-unit(5px) solid pales.$green-3a;
            border: fn.percent-unit(3px) solid pales.$white;
          }
        }
      }
    }

    .ps-package-disclamer {
      background-color: #3ac882;
      width: 100%;
      height: fn.percent-unit(50px);
      position: absolute;
      margin-top: fn.percent-unit(-24px);
      align-items: center;
      display: flex;
      justify-content: center;
      padding-left: fn.percent-unit(120px);
      margin-left: fn.percent-unit(-60px);
      .icon {
        margin-right: fn.percent-unit(15px);
      }
      span {
        font-family: Roboto;
        font-style: normal;
        font-weight: normal;
        font-size: fn.percent-unit(24px);
        line-height: 130%;
        color: #fff;
        display: flex;
        align-items: center;
      }
    }

    .ps-group-package {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      .ps-package-title {
        margin-top: fn.percent-unit(40px);
      }
      .ps-package-list {
        padding-top: 0;
        &--box {
          flex-direction: column;
          .item {
            width: fn.percent-unit(970px);
            height: fn.percent-unit(194px);
            overflow: hidden;
            border: fn.percent-unit(3px) solid transparent;
            &:not(:last-child) {
              margin-right: 0px;
              margin-bottom: fn.percent-unit(20px);
            }
          }
        }
      }
    }
    .ps-package {
      .thumbnail {
        width: 100%;
        height: fn.percent-unit(507px);
      }
      .row {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: fn.percent-unit(60px);
        margin-bottom: fn.percent-unit(13px);
        .col-3 {
          width: 25%;
          padding-left: fn.percent-unit(12px);
          padding-right: fn.percent-unit(12px);
        }
      }
      .package-note {
        text-align: center;
        color: pales.$gray-64;
        font-size: fn.percent-unit(24px);
      }
      .item {
        padding: fn.percent-unit(32px) fn.percent-unit(24px)
          fn.percent-unit(24px);
        border: 1px solid pales.$gray-cc;
        border-radius: fn.percent-unit(3px);
        position: relative;
        height: fn.percent-unit(237px);
        .item-h {
          margin-bottom: fn.percent-unit(8px);
          position: relative;
          display: flex;
          flex-direction: column;
          flex-grow: 1;
          .package-title {
            font-size: fn.percent-unit(36px);
            line-height: 1.16;
            font-weight: 500;
            color: pales.$black-08;
            margin-bottom: fn.percent-unit(16px);
          }
          .package-type {
            font-size: fn.percent-unit(24px);
            color: pales.$gray-64;
          }
          .package-price-box {
            padding-top: fn.percent-unit(24px);
            margin-top: fn.percent-unit(24px);
            border-top: 1px solid pales.$gray-cc;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            .price {
              color: pales.$black;
              font-size: fn.percent-unit(32px);
            }
            .old-price {
              color: pales.$gray-64;
              text-decoration: line-through;
              font-size: fn.percent-unit(24px);
            }
          }
        }
        & > div {
          z-index: 2;
        }
        .package-sub-name {
          position: absolute;
          top: 0;
          right: fn.percent-unit(-1px);
          transform: translateY(-65%);
          border-radius: 0 fn.percent-unit(2px) 0 fn.percent-unit(4px);
          font-size: fn.percent-unit(20px);
          line-height: 1.5;
          padding: fn.percent-unit(8px) fn.percent-unit(16px);
          background-color: #da9e1c;
          color: #fff;
        }
        &.active {
          .item-h {
            .package-type {
              color: pales.$black-08;
            }
            .package-price-box {
              border-top-color: #fff;
              .price,
              .old-price {
                color: #fff;
              }
            }
          }
          &::after {
            display: block;
            background: linear-gradient(90deg, #3ac882, #97e98a);
            content: "";
            position: absolute;
            top: fn.percent-unit(-5px);
            left: 0;
            right: 0;
            bottom: fn.percent-unit(-5px);
            z-index: 1;
            border-radius: fn.percent-unit(3px);
          }
        }
      }
    }
    .ps-info {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      .package-info {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-top: fn.percent-unit(30px);
        &--wrapper {
          width: fn.percent-unit(832px);
          & > div {
            margin-bottom: fn.percent-unit(24px);
            line-height: 1.5;
            & > div:first-child {
              color: pales.$gray-64;
              width: fn.percent-unit(336px);
              font-size: fn.percent-unit(28px);
            }
            & > div:nth-child(2) {
              text-align: right;
              width: fn.percent-unit(448px);
              font-size: fn.percent-unit(28px);
              &.package-info--total {
                color: pales.$green-3a;
                font-size: fn.percent-unit(36px);
              }
            }
          }
          .line-break {
            width: 100%;
            height: 1px;
            background-color: #9b9b9b;
            margin: fn.percent-unit(32px) 0;
          }
        }
      }
      .confirm-button {
        display: flex;
        justify-content: center;
        align-items: flex-end;
        flex-grow: 1;
        margin-bottom: fn.percent-unit(92px);
        .btn {
          width: fn.percent-unit(330px);
          &:not(:last-child) {
            margin-right: fn.percent-unit(24px);
          }
        }
      }
      .coupon-box {
        width: fn.percent-unit(688px);
        &--wrapper {
          display: flex;
        }
        .coupon-input {
          border: fn.percent-unit(3px) solid pales.$gray-64;
          border-radius: fn.percent-unit(2px);
          display: flex;
          align-items: center;
          padding: fn.percent-unit(14px);
          margin-right: fn.percent-unit(28px);
          line-height: 1.4;
          color: pales.$black;
          font-size: fn.percent-unit(28px);
          height: fn.percent-unit(66px);
          width: fn.percent-unit(430px);
          &.error {
            border-color: pales.$error;
          }
          &.disable {
            border-color: pales.$gray-cc;
          }
          &.focus {
            border-color: pales.$green-3a;
          }
        }
      }
      .coupon-input-error {
        font-size: fn.percent-unit(24px);
        margin-top: fn.percent-unit(12px);
        width: fn.percent-unit(456px);
        color: pales.$error;
      }
    }
    .ps-payment-methods {
      .list-methods {
        &--wrapper {
          display: flex;
          flex-wrap: wrap;
          margin-left: fn.percent-unit(-12px);
          margin-right: fn.percent-unit(-12px);
        }
        .method-item {
          height: fn.percent-unit(237px);
          width: 100%;
          border: fn.percent-unit(3px) solid pales.$gray-cc;
          padding: fn.percent-unit(40px) fn.percent-unit(27px);
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;
          .wallet-img {
            height: fn.percent-unit(64px);
            margin-bottom: fn.percent-unit(32px);
            & > img {
              height: 100%;
              width: auto;
            }
          }
          .wallet-title {
            font-size: fn.percent-unit(24px);
            text-align: center;
          }
          .wallet-promotion {
            font-size: fn.percent-unit(24px);
            line-height: 1.2;
            text-align: center;
            position: absolute;
            bottom: fn.percent-unit(8px);
            left: 0;
            width: 100%;
            color: pales.$green-0a;
          }
          & > div {
            z-index: 2;
          }
          &--box {
            width: 25%;
            padding: fn.percent-unit(16px) fn.percent-unit(12px);
            &.active {
              .method-item {
                .wallet-title {
                  color: pales.$white;
                }
                .wallet-promotion {
                  color: pales.$white;
                }
                &::after {
                  display: block;
                  content: "";
                  position: absolute;
                  top: fn.percent-unit(-3px);
                  left: fn.percent-unit(-3px);
                  right: fn.percent-unit(-3px);
                  bottom: fn.percent-unit(-3px);
                  z-index: 1;
                }
                &.momo::after {
                  background: $c-momo;
                }
                &.vnpay::after {
                  background: $c-vnpay;
                }
                &.credit-card::after {
                  background: $c-credit-card;
                }
                &.mobifone::after {
                  background: $c-mobifone;
                }
                &.moca::after {
                  background: $c-moca;
                }
                &.zalopay::after {
                  background: $c-zalopay;
                }
                &.vinaphone::after {
                  background: $c-vinaphone;
                }
                &.viettelsms::after {
                  background: $c-viettelsms;
                }
                &.payoo::after {
                  background: $c-payoo;
                }
              }
            }
            &.disabled {
              .method-item {
                border-color: hsla(0, 0%, 80%, 0.5);
                & > div {
                  opacity: 0.5;
                }
                .wallet-warning {
                  opacity: 1;
                  position: absolute;
                  bottom: fn.percent-unit(28px);
                  left: 0;
                  font-size: fn.percent-unit(24px);
                  color: red;
                  text-align: center;
                  width: 100%;
                }
              }
            }
          }
        }
      }
    }
  }
  .ps-payment-qr {
    .qr-price {
      position: absolute;
      width: fn.percent-unit(600px);
      font-size: fn.percent-unit(36px);
      line-height: 1.4;
      text-align: center;
      top: fn.percent-unit(67px);
      right: fn.percent-unit(156px);
      color: pales.$white;
    }
    .qr-img {
      text-align: center;
      height: fn.percent-unit(380px);
      width: fn.percent-unit(380px);
      position: absolute;
      top: fn.percent-unit(205px);
      right: fn.percent-unit(265px);
      & > img {
        width: 100%;
        height: auto;
      }
    }
    .qr-time-remain {
      color: pales.$white;
      text-align: center;
      position: absolute;
      top: fn.percent-unit(690px);
      right: fn.percent-unit(160px);
      width: fn.percent-unit(600px);
      font-size: fn.percent-unit(30px);
      .time {
        font-weight: 500;
      }
    }
  }
  .ps-zalopay-confirm {
    .confirm-box {
      display: flex;
      flex-flow: column;
      align-items: center;
      justify-content: center;
      .logo {
        margin-top: fn.percent-unit(120px);
        margin-bottom: fn.percent-unit(40px);
        height: fn.percent-unit(165px);
        width: 100%;
        text-align: center;
        justify-content: center;
        & > img {
          height: 100%;
          width: auto;
        }
      }
      .text {
        font-size: fn.percent-unit(28px);
        text-align: center;
        line-height: 1.5;
        color: pales.$gray-33;
        margin-bottom: fn.percent-unit(140px);
      }
      .btn {
        width: fn.percent-unit(400px);
      }
    }
  }
  .ps-payment-payoo {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;
    width: 100%;
    &.bg--payoo-confirm {
      background-color: $c-payoo;
    }
    &.bg--payoo-voucher {
      background-color: #111;
    }
    & > div {
      margin-bottom: fn.percent-unit(30px);
    }
    .step-direction {
      font-size: fn.percent-unit(28px);
      line-height: 1.5;
      color: #fff;
    }
    .btn.btn--confirm-payoo {
      width: fn.percent-unit(321px);
    }
    .store-list {
      max-width: 60%;
      max-height: 60%;
      overflow: hidden;
      & > img {
        width: 100%;
        height: auto;
      }
    }
    .page-voucher {
      align-self: flex-start;
      width: 100%;
      &--left {
        margin-left: fn.percent-unit(0px);
      }
    }
  }
  .ps-buy-success {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: row;
    &--left {
      width: 60%;
      position: relative;
      padding-right: fn.percent-unit(96px);
      border-right: 1px solid pales.$gray-cc;
      margin-top: fn.percent-unit(-24px);
      display: flex;
      flex-direction: column;
      align-items: center;
      .img {
        margin-top: fn.percent-unit(84px);
        margin-bottom: fn.percent-unit(60px);
        height: fn.percent-unit(224px);
        width: 100%;
        text-align: center;
        & > img {
          height: 100%;
          width: auto;
        }
      }
      .text {
        .title {
          font-weight: 500;
          font-size: fn.percent-unit(36px);
          color: pales.$black;
          text-align: center;
        }
        .package-info {
          width: fn.percent-unit(672px);
          &--wrapper {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-top: fn.percent-unit(30px);

            & > div {
              margin-bottom: fn.percent-unit(16px);
              line-height: 1.5;
              & > div:first-child {
                color: pales.$gray-64;
                width: fn.percent-unit(336px);
                font-size: fn.percent-unit(28px);
              }
              & > div:nth-child(2) {
                text-align: right;
                width: fn.percent-unit(448px);
                font-size: fn.percent-unit(28px);
              }
            }
          }
        }
      }
      .button-box {
        position: absolute;
        bottom: fn.percent-unit(92px);
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        justify-content: center;
        padding-right: fn.percent-unit(96px);
        .btn + .btn {
          margin-left: fn.percent-unit(47px);
        }
      }
      .btn {
        width: fn.percent-unit(400px);
      }
    }
    &--right {
      width: 40%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: fn.percent-unit(264px);
      padding-left: fn.percent-unit(64px);
      .title {
        font-size: fn.percent-unit(36px);
        font-weight: 500;
        color: pales.$black;
        margin-bottom: fn.percent-unit(24px);
        &.title--success {
          text-align: center;
          line-height: 1.2;
          margin-top: fn.percent-unit(50px);
          margin-bottom: fn.percent-unit(24px);
        }
      }
      .label {
        align-self: flex-start;
        margin-bottom: fn.percent-unit(14px);
        font-size: fn.percent-unit(28px);
      }
      .error-msg {
        color: pales.$error;
        line-height: 1.5;
        font-size: fn.percent-unit(24px);
        margin-top: fn.percent-unit(12px);
        align-self: flex-start;
      }
      .update-email-box {
        display: flex;
        width: 100%;
      }
      .input-box {
        .email-input {
          overflow: hidden;
          border: fn.percent-unit(3px) solid pales.$gray-64;
          border-radius: fn.percent-unit(2px);
          height: fn.percent-unit(65px);
          display: flex;
          align-items: center;
          padding: fn.percent-unit(14px);
          margin-right: fn.percent-unit(28px);
          line-height: 1.4;
          color: pales.$black;
          font-size: fn.percent-unit(28px);
          width: fn.percent-unit(414px);
          & > .placeholder {
            color: pales.$gray-9b;
          }
          &.focus {
            border-color: pales.$green-3a;
          }
        }
      }
    }
  }
  .ps-buy-pending,
  .ps-buy-fail {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .img {
      margin-top: fn.percent-unit(140px);
      margin-bottom: fn.percent-unit(60px);
      height: fn.percent-unit(222px);
      width: 100%;
      text-align: center;
      & > img {
        height: 100%;
        width: auto;
      }
    }
    .text {
      .title {
        font-weight: 500;
        font-size: fn.percent-unit(36px);
        color: pales.$black;
        text-align: center;
        margin-bottom: fn.percent-unit(16px);
      }
      .description {
        font-size: fn.percent-unit(28px);
        line-height: 2;
        text-align: center;
        .text-green {
          color: pales.$green-3a;
        }
      }
    }
    .button-box {
      position: absolute;
      bottom: fn.percent-unit(92px);
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      justify-content: center;
      .btn + .btn {
        margin-left: fn.percent-unit(47px);
      }
    }
    .btn {
      width: fn.percent-unit(400px);
    }
  }
  .ps-pay-credit {
    &--wrapper {
      display: flex;
      flex-direction: column;
      .row {
        display: flex;
        &.align-items-center {
          align-items: center;
        }
        &.justify-content-center {
          justify-content: center;
        }
      }
      .col-5 {
        width: 41.65%;
      }
      .col-7 {
        width: 58.33%;
      }
      .col-12 {
        width: 100%;
      }
      .mb-32 {
        margin-bottom: fn.percent-unit(32px);
      }
      .mb-24 {
        margin-bottom: fn.percent-unit(24px);
      }
      .mt-32 {
        margin-top: fn.percent-unit(32px);
      }
      .pl-15 {
        padding-left: fn.percent-unit(15px);
      }
      .pr-15 {
        padding-right: fn.percent-unit(15px);
      }
      .pl-30 {
        padding-left: fn.percent-unit(30px);
      }
      .pr-30 {
        padding-right: fn.percent-unit(30px);
      }
    }
    .button-box {
      display: flex;
      justify-content: center;
      & > .btn {
        width: fn.percent-unit(400px);
      }
    }
    .accept-policy {
      text-align: center;
      font-size: fn.percent-unit(24px);
      & > .text-green {
        color: pales.$green-3a;
      }
    }
    .input-group {
      & > label {
        font-size: fn.percent-unit(28px);
        color: pales.$black;
        display: block;
        margin-bottom: fn.percent-unit(16px);
      }
      & > input {
        font-size: fn.percent-unit(28px);
        padding: fn.percent-unit(16px) fn.percent-unit(24px);
        height: fn.percent-unit(70px);
        width: 100%;
        font-weight: 400;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        border: 1px solid #ced4da;
        outline: fn.percent-unit(2px) solid pales.$gray-64;
        &.focused {
          outline: fn.percent-unit(4px) solid pales.$green-0a;
        }
      }
    }
    .custom-checkbox {
      position: relative;
      display: block;
      height: fn.percent-unit(33px);
      padding-left: fn.percent-unit(48px);
      & > input {
        position: absolute;
        left: fn.percent-unit(110px);
        z-index: -1;
        width: fn.percent-unit(16px);
        height: fn.percent-unit(20px);
        opacity: 0;
        & + label::before {
          position: absolute;
          top: fn.percent-unit(2px);
          left: fn.percent-unit(-50px);
          pointer-events: none;
          content: "";
          display: block;
          width: fn.percent-unit(30px);
          height: fn.percent-unit(30px);
          border: fn.percent-unit(4px) solid pales.$gray-9b;
          z-index: 1;
        }

        & + label::after {
          content: "";
          display: block;
          position: absolute;
          top: fn.percent-unit(2px);
          left: fn.percent-unit(-50px);
          background: no-repeat 50%/50% 50%;
          width: fn.percent-unit(30px);
          height: fn.percent-unit(30px);
          z-index: 2;
        }
        &.focused {
          & + label::before {
            border-color: pales.$green-0a;
          }
        }
        &:checked {
          & + label::before {
            border-color: pales.$green-0a;
          }
          & + label::after {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%230ad418' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
          }
          &.focused + label::after {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
          }
          &.focused + label::before {
            border-color: pales.$green-0a;
            background-color: pales.$green-0a;
          }
        }
      }
      & > label {
        font-size: fn.percent-unit(28px);
        color: pales.$black;
        display: block;
        margin-bottom: fn.percent-unit(16px);
        position: relative;
      }
    }
  }
  .card-img {
    margin-left: fn.percent-unit(-10px);
    height: fn.percent-unit(385px);
    .card--template-credit {
      border-radius: fn.percent-unit(10px);
      padding: fn.percent-unit(20px);
      background: -webkit-linear-gradient(45deg, #d27a12, #ffd873);
      background: linear-gradient(45deg, #d27a12, #ffd873);
      height: 100%;
      width: 90%;
      margin: fn.percent-unit(20px) auto 0;
      display: flex;
      flex-direction: column;
      .credit-circuit {
        display: flex;
        flex-wrap: wrap-reverse;
        justify-content: space-between;
        padding: fn.percent-unit(16px);
        &-left {
          width: fn.percent-unit(93px);
          height: auto;
          & > img {
            width: 100%;
            height: auto;
          }
        }
        &-right {
          font-size: fn.percent-unit(44px);
          color: pales.$white;
          display: block;
          margin-bottom: fn.percent-unit(25px);
          line-height: 1;
        }
      }
      .credit-number {
        height: fn.percent-unit(96px);
        .credit-number-item {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
          .number-item {
            width: fn.percent-unit(28px);
            display: flex;
            align-items: center;
            justify-content: center;
            & > .icon {
              width: fn.percent-unit(16px);
              height: fn.percent-unit(16px);
            }
            &:nth-child(4n) {
              margin-right: fn.percent-unit(19px);
            }
            .text {
              color: pales.$white;
              font-size: fn.percent-unit(40px);
            }
          }
        }
        &.credit-number--valid-date {
          height: fn.percent-unit(47px);
          display: flex;
        }
      }
      .bottom {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        padding: fn.percent-unit(16px);
        padding-bottom: fn.percent-unit(24px);
        flex-grow: 1;
        .card-holder {
          font-size: fn.percent-unit(28px);
          color: pales.$gray-c4;
        }
        .card-valid-thru {
          .text {
            font-size: fn.percent-unit(16px);
            color: pales.$gray-c4;
          }
          .slash {
            font-size: fn.percent-unit(40px);
            padding: 0 fn.percent-unit(4px);
            color: pales.$white;
            line-height: fn.percent-unit(47px);
          }
        }
      }
    }
  }
  .ps-pay-sms {
    .sms-box {
      margin-top: fn.percent-unit(136px);
      .logo {
        height: fn.percent-unit(166px);
        width: 100%;
        text-align: center;
        & > img {
          height: 100%;
          width: auto;
          margin-left: auto;
          margin-right: auto;
        }
      }
      .sms-text-box {
        margin-top: fn.percent-unit(40px);
        text-align: center;
        .sms-row {
          font-size: fn.percent-unit(28px);
          margin-bottom: fn.percent-unit(16px);
          display: inline-block;
          color: pales.$black;
          line-height: 1.4;
          .sms-impression {
            margin-left: fn.percent-unit(8px);
            color: pales.$green-3a;
            font-size: fn.percent-unit(40px);
            font-weight: 500;
          }
        }
        .sms-note {
          max-width: 60%;
          font-size: fn.percent-unit(28px);
          margin: 0 auto fn.percent-unit(16px) auto;
          color: pales.$black;
          line-height: 1.4;
        }
      }
    }
  }
  .keyboard {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: pales.$black-08;
    .keyboard__item {
      box-sizing: content-box;
    }
    &.keyboard--full {
      .keyboard-word {
        width: fn.percent-unit(810px);
        margin: auto;
        padding-top: fn.percent-unit(20px);
        padding-bottom: fn.percent-unit(20px);
      }
      .keyboard-row {
        width: fn.percent-unit(1009px);
      }
    }
    &.keyboard--email {
      .keyboard-word {
        width: fn.percent-unit(1060px);
        margin: auto;
        padding-top: fn.percent-unit(20px);
        padding-bottom: fn.percent-unit(20px);
      }
    }
    &.keyboard--number {
      .keyboard-number {
        width: fn.percent-unit(482px);
        margin: auto;
        padding-top: fn.percent-unit(41px);
        padding-bottom: fn.percent-unit(41px);
      }
    }
  }
}
