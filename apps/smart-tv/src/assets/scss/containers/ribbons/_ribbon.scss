///
/// Global style for Ribbon
///

/// @group Settings
@use "../../settings/variables" as var;
@use "../../settings/function" as fn;
@use "../../settings/palettes" as pales;

/// @group mixin
@use "mixin/position" as position;
@use "mixin/pseudo" as pseudo;

.ribbon {
  display: block;
  align-self: flex-start;
  margin-left: fn.percent-unit(var.$sidebar-max-width);
  margin-bottom: fn.percent-unit(40);
  transition:
    visibility 0.2s linear,
    opacity 0.2s linear;
  position: relative;

  &__title {
    font-size: fn.percent-unit(32px);
    font-weight: 500;
    line-height: fn.percent-unit(38px);
    color: pales.$white;
    margin-bottom: fn.percent-unit(16px);
    transition: font-size 0.2s;
    height: fn.percent-unit(38px);
  }

  &__body {
    transition: width 0.2s;
  }

  &__card {
    position: relative;

    &:not(:last-child) {
      margin-right: fn.percent-unit(16px);
    }

    &.focus {
      // width: fn.percent-unit(1468);
      position: relative;
      outline: fn.percent-unit(6) solid pales.$white;

      .ribbon__visual {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        overflow: hidden;
        width: 100%;
      }

      .card--accordion {
        // .card__thumbnail {
        //   flex: 0 0 auto;
        //   width: fn.percent-unit(892);
        // }
        .card__section {
          flex: 0 0 auto;
          width: fn.percent-unit(526);
        }

        & + .card,
        & ~ .card {
          opacity: 0;
          transition: opacity 0.2s;
        }
      }
    }
  }

  &:not(.vertical) {
    .ribbon__body {
      display: flex;
      flex-direction: row;
      align-items: center;
      position: relative;
    }
  }

  &.ribbon--new-master-banner {
    margin-bottom: fn.percent-unit(40);
  }

  &.ribbon--category {
    margin-bottom: fn.percent-unit(60);
  }

  // &.focus &__title {
  //   font-size: fn.percent-unit(48px);
  // }
  %ribbonGrid {
    display: flex;

    &* > {
      margin-right: fn.percent-unit(8);
      display: flex;
    }
  }

  [data-type="slide"] {
    @extend %ribbonGrid;
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 1;
    flex-flow: row nowrap;
    // transition-property: transform;
    box-sizing: content-box;

    &[data-item="4"] {
      .ribbon__card {
        width: fn.percent-unit(429.5);
      }
    }

    &[data-item="5.1"] {
      .ribbon__card {
        width: fn.percent-unit(361);
      }
    }

    &[data-item="5.2"] {
      .ribbon__card {
        width: fn.percent-unit(348);
      }
    }

    &[data-item="7"] {
      .ribbon__card {
        width: fn.percent-unit(238);
      }
    }
  }

  [data-type="grid"] {
    @extend %ribbonGrid;

    &[data-item="4"] {
      .ribbon__card {
        width: 25%;
      }
    }

    &[data-item="7"] {
      .ribbon__card {
        width: 14.28571%;
      }
    }
  }

  &.ribbon--original {
    .card {
      width: fn.percent-unit(348);
      height: fn.percent-unit(695px);

      &:not(:last-child) {
        margin-right: fn.percent-unit(16px);
      }
    }
  }

  &.ribbon--master-banner,
  &.ribbon--new-master-banner,
  &.ribbon--poster,
  &.ribbon--tvod,
  &.ribbon--watch-later,
  &.ribbon--watch-more,
  &.ribbon--collections,
  &.ribbon--banner {
    // .ribbon__body {
    //   align-items: center;
    // }
    .card {
      &:not(.card--candidate) {
        width: fn.percent-unit(230px);
        height: fn.percent-unit(328.57px);

        .card__thumbnail {
          width: fn.percent-unit(230px);
          height: fn.percent-unit(328.57px);
        }
      }

      .card__ads-text {
        position: absolute;
        width: fn.percent-unit(100);
        bottom: fn.percent-unit(6);
        left: fn.percent-unit(9);
      }

      .card__thumbnail {
        & > .card__img {
          width: 100%;
          height: 100%;

          & > img {
            width: 100%;
            height: 100%;
          }
        }
      }

      &:not(:last-child) {
        margin-right: fn.percent-unit(16px);
      }

      &.card--candidate {
        &:not(:last-child) {
          margin-right: fn.percent-unit(24);
        }
      }
    }
  }

  &.ribbon--new-master-banner {
    .card {
      &:not(.card--candidate) {
        width: fn.percent-unit(230px);
        height: fn.percent-unit(129.34px);

        .card__thumbnail {
          width: fn.percent-unit(230px);
          height: fn.percent-unit(129.34px);
        }
      }

      .card__thumbnail {
        & > .card__img {
          width: 100%;
          height: 100%;

          & > img {
            width: 100%;
            height: 100%;
          }
        }
      }

      &:not(:last-child) {
        margin-right: fn.percent-unit(16px);
      }
    }
  }

  &.ribbon--top-views {
    .ribbon__body {
      align-items: center;
    }
  }

  &.ribbon--coming-soon {
    // width: fn.percent-unit(1714);
    // height: fn.percent-unit(441);

    &:not(:last-child) {
      padding-bottom: fn.percent-unit(112px);
    }
  }

  &__wrapper {
    &.ribbon:not(:last-child) {
      padding-bottom: fn.percent-unit(30);
    }

    .ribbon:first-child {
      padding-top: 0 !important;
    }

    .ribbon {
      padding-left: 0;
    }

    .ribbon:not(:last-child) {
      padding-bottom: fn.percent-unit(20);
    }
  }

  &.ribbon--rap-viet-banner {
    width: fn.percent-unit(1714);
    height: fn.percent-unit(260);
  }

  &.ribbon--promotion-banner,
  &.ribbon--outstream-ads {
    .ribbon__indicator {
      position: absolute;
      top: fn.percent-unit(310);
      right: 0;
      left: 0;
      width: fn.percent-unit(1716px);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10;

      .indicator {
        width: fn.percent-unit(16);
        height: fn.percent-unit(6);
        background: #646464;
        margin-right: fn.percent-unit(10);

        &:last-child {
          margin-right: 0;
        }

        &.focus {
          background: #fff;
        }
      }
    }
  }
}

.ribbon-list {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  z-index: 1;
  position: relative;

  &::after {
    @include pseudo.pseudo(
      $width: fn.percent-unit(var.$sidebar-max-width),
      $height: 100%,
      $display: block
    );
    @include position.absolute(left 0 top 0);
    z-index: 1;
    //background: rgba(0, 0, 0, 0.5);
  }

  .ribbon {
    opacity: 0.5;
    pointer-events: none;
    visibility: visible;
  }

  .ribbon.hidden {
    opacity: 0;
    visibility: hidden;
  }

  .ribbon.focus {
    opacity: 1;
    pointer-events: all;
  }
}
