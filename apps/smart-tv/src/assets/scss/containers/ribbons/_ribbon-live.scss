///
/// Global style for Ribbon
///

/// @group Settings
@use "../../settings/variables" as var;
@use "../../settings/function" as fn;
@use "../../settings/palettes" as pales;

/// @ribbon-live
.ribbon--live {
  @at-root .ribbon#{&} {
    &:not(:last-child) {
      padding-bottom: fn.percent-unit(28);
    }
    .ribbon__title {
      line-height: fn.percent-unit(36);
      height: fit-content;
      margin-bottom: fn.percent-unit(16);
    }
  }
}
