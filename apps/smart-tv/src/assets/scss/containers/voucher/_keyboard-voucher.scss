@use "../../settings/variables" as var;
@use "../../settings/function" as fn;
@use "../../settings/palettes" as pales;
.grp-key-top {
  //width: 34em;
  color: #9b9b9b;
  position: relative;
  text-align: left;
  .lst {
    display: block;
    clear: both;
    .chr {
      font-size: fn.percent-unit(28px);
      line-height: 140%;
      font-weight: 500;
      cursor: pointer;
      display: inline-flex;
      //padding: 0.25em;
      width: fn.percent-unit(72px);
      height: fn.percent-unit(72px);
      align-items: center;
      justify-content: center;
      background: #333333;
      margin: fn.percent-unit(5px);
      &.focused {
        background: #ffffff;
        color: rgba(0, 0, 0, 0.85);
        box-shadow: 0 0.5em 2em black;
      }
      &:hover {
        color: #e3e3e3;
        background: rgba(255, 255, 255, 0.3);
      }
    }
    .chr-last {
      color: #9b9b9b;
      justify-content: center;
      height: fn.percent-unit(72px);
      width: fn.percent-unit(72px);
      &.remove {
        font-size: 4em;
        font-weight: normal;
      }
    }
    &.lst-rt {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-direction: column;
      height: fit-content;
      position: absolute;
      right: fn.percent-unit(31px);
      top: 0;
    }
  }
  .vie {
    width: fn.percent-unit(32px);
    height: fn.percent-unit(24px);
    stroke-width: 0;
    stroke: currentColor;
    fill: currentColor;
  }
}
