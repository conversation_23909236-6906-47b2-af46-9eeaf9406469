@use "../../settings/variables" as var;
@use "../../settings/function" as fn;
@use "../../settings/palettes" as pales;
$screen-hd: 1280px !default;
$c-9b: #9b9b9b;
$c-green-3a: #3ac882;
$c-active: #3ac88a;
$c-97: #979797;
$c-white: #ffffff;

.page-voucher {
  display: flex;
  padding-top: fn.percent-unit(120px);
  color: white;
  line-height: 1.4;
  height: 100vh;
  &--left {
    margin: 0 fn.percent-unit(60px) 0 fn.percent-unit(144px);
    width: fn.percent-unit(896px);
  }
  &--right {
    margin: 0 fn.percent-unit(108px) fn.percent-unit(60px);
    flex: 1;
  }
  .title {
    font-size: fn.percent-unit(36px);
    font-weight: 500;
    margin-bottom: fn.percent-unit(24px);
  }
  &--input {
    margin-bottom: fn.percent-unit(60px);
  }
  &--submit {
    width: 100%;
    text-align: center;
    padding-top: fn.percent-unit(30px);

    .btn {
      font-size: fn.percent-unit(28px);
      width: fn.percent-unit(400px);
      height: 100%;
      display: inline-block;
      padding: 0.8em 1.2em;
      background: none;
      color: $c-97;
      border: none;
      line-height: 1;
      &-bd {
        border: 0.12em solid #3ac88a;
        color: #3ac88a;

        &.focused {
          border-color: $c-active;
          color: $c-white;
        }
      }

      &:hover,
      &.focused {
        background: $c-active;
        color: $c-white;
      }

      [class^="icon-"] {
        margin-right: fn.percent-unit(5px);
      }
    }
  }
  &--empty-info {
    width: 100%;
    text-align: center;
    padding-top: fn.percent-unit(60px);
    height: fn.percent-unit(500px);
    box-sizing: border-box;
    & > img {
      margin-bottom: 1.125em;
      height: fn.percent-unit(300px);
    }
    & > div {
      font-size: fn.percent-unit(28px);
      color: $c-9b;
    }
  }
}

.voucher-input-box {
  display: flex;
  position: relative;
  .voucher-input {
    width: fn.percent-unit(896px);
    border-radius: fn.percent-unit(2px);
    border: fn.percent-unit(2px) solid #4a4a4a;
    font-size: fn.percent-unit(28px);
    text-transform: uppercase;
    background: #4a4a4a00;
    color: #e3e3e3;
    padding: fn.percent-unit(20px);
    &.focused {
      border-color: #ffffff;
    }
    &.error {
      border-color: #e02020;
    }
  }
  .voucher-input--error {
    font-size: fn.percent-unit(28px);
    color: #e02020;
    position: absolute;
    left: 0;
    top: 110%;
    width: fn.percent-unit(910px);
    &::before {
      display: inline;
      content: "* ";
    }
  }
}
.btn--voucher-accept,
.btn--voucher-submit {
  font-size: fn.percent-unit(28px);
  background: transparent !important;
  color: #ffffff !important;
  border: fn.percent-unit(1.5px) solid #646464 !important;
  &.focused {
    background: #ffffff !important;
    color: #000000 !important;
  }
  &:disabled,
  &.disabled {
    border-color: $c-9b !important;
    color: #8a8a8a !important;
    background: #434343 !important;
  }
}
.btn--voucher-accept {
  width: 9.3em;
}
.btn--voucher-submit {
  width: 14.3em;
}
