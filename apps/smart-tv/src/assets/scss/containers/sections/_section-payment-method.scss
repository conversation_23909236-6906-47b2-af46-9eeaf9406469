// @group Section
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as pales;

/// @group mixin
@use "mixin/box" as box;
@use "mixin/fonts" as fonts;
@use "mixin/position" as posit;

/// @group Section
.section--payment-method {
  @at-root .section#{&} {
    .section__header {
      padding: fn.percent-unit(10) fn.percent-unit(104) fn.percent-unit(15);

      .section__title {
        // @include fonts.font-family("roboto-medium");
        @include fonts.font-size(32);
        color: pales.$black;
        margin: 0;
        font-weight: 500;

        &-sub {
          @include posit.relative;
          margin-top: fn.percent-unit(4);
          font-weight: normal;
          display: flex;
          align-items: center;

          .icon {
            @include box.box(
              $width: fn.percent-unit(24),
              $height: fn.percent-unit(24)
            );
            @include posit.absolute(top 50% left 0);
            transform: translate(0, -50%);

            .vie {
              color: pales.$green-3a;
            }

            & > img {
              height: 100%;
              width: 100%;
            }

            & + .text,
            & ~ .text {
              padding-left: fn.percent-unit(28);
            }
          }

          .text {
            @include fonts.font-size(20);
            line-height: 1.5;
          }
        }
      }
    }
  }

  .keyboard__wrapper {
    position: absolute;
    z-index: 99;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #111111;

    &.keyboard__wrapper--code {
      height: fn.percent-unit(436px);

      .keyboard-container {
        background: #181818;
        padding: fn.percent-unit(32px);
      }
    }

    &.keyboard__wrapper--number {
      height: fn.percent-unit(340px);

      .keyboard-container {
        background: #181818;
        padding: fn.percent-unit(32px);
      }
    }
  }

  .note {
    width: 100%;
    padding: fn.percent-unit(8);
    border-radius: fn.percent-unit(4);
    background-color: #e0e0e0;
    color: #646464;
    font-size: fn.percent-unit(20);
    text-align: center;
  }

  .form--ipc .grid.grid-x.global {
    margin-bottom: fn.percent-unit(32);
  }

  .note-open-card {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: fn.percent-unit(16) 0;
    width: fn.percent-unit(992);
    background: #f5f5f5;
    border-radius: fn.percent-unit(4) fn.percent-unit(4) 0 0;
    border-bottom: fn.percent-unit(1) solid #dedede;

    &--icon {
      width: fn.percent-unit(24);
      margin-right: fn.percent-unit(5);
      svg {
        width: fn.percent-unit(24);
        height: fn.percent-unit(24);
      }
    }
    &--text {
      color: pales.$gray-22;
      font-size: fn.percent-unit(20);
      line-height: 1.4;
      font-weight: 500;
    }
  }

  .form--ipc--card-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: fn.percent-unit(32);

    &--center {
      justify-content: center;
    }

    .card.card--ipc {
      margin: 0;
    }
  }

  .card-divider {
    width: fn.percent-unit(2);
    height: fn.percent-unit(120);
    background: #dedede;
  }

  .card-qr-img {
    width: fn.percent-unit(212);
    canvas {
      width: 100% !important;
      height: 100% !important;
      display: block;
    }
  }
}
