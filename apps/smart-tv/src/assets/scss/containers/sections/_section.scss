/// @group Section
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as pales;

// .section {
//   .section__wrap {
//   }
// }

// .section--login {
//   @at-root .section#{&} {
//   }
// }
.section--login-app {
  @at-root .section#{&} {
    height: 100%;
    display: flex;
    .section__wrap {
      height: 100%;
      width: 100%;
      // display: flex;
      // align-items: center;
      // justify-content: center;
      &--auth {
        height: 100%;
        width: 100%;
        display: flex;
      }
    }
  }
}

.section--login-auth {
  padding-left: fn.percent-unit(110);
}

.section--profile {
  @at-root .section#{&} {
    .profile-empty {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 80vh !important;
      flex-direction: column;

      .text {
        font-size: fn.percent-unit(28px);
        line-height: 1.5;
        color: #fff;
        text-align: center;
        margin-top: 0;
        margin-bottom: 0;

        & ~ .btn,
        & + .btn {
          margin-top: 1.5em;
        }
      }

      .btn {
        min-width: fn.percent-unit(380px);
        margin-top: fn.percent-unit(30px);
      }

      .profile-empty-img {
        height: fn.percent-unit(400px);
        width: auto;
        text-align: center;
        margin-bottom: 2.5em;
        .icon {
          &.icon-profile-empty {
            width: 18.625em;
            height: 18.625em;
            display: inline-block;
            .vie {
              color: #5f5f5f;
              font-size: 18.625em;
            }
          }
        }
      }
    }
  }
}
