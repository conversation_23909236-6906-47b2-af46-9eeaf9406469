// @group Section
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as pales;

/// @group mixin
@use "mixin/fonts" as fonts;

/// @group Section
.section--payment {
  @at-root .section#{&} {
    background-color: fn.el-color($color: v-white, $shade: base);

    .container {
      padding-left: fn.percent-unit(144);
    }
    .section__footer {
      position: absolute;
      width: 100%;
      bottom: 0;
      z-index: 2;
      display: flex;
      align-items: center;
      background-color: pales.$white;
      p {
        font-size: fn.percent-unit(20);
        margin-left: fn.percent-unit(16);
        color: pales.$gray-33;
      }
      border-top: 1px solid pales.$gray-de;
      padding: fn.percent-unit(12) fn.percent-unit(104);

      .button.button--hollow {
        @include fonts.font-family("roboto-medium");
        background: rgba(0, 0, 0, 0.5);
        color: pales.$white;
        border: none;
        &.focus {
          background: fn.el-color($color: v-medium-sea-green, $shade: 149);
        }
      }
    }

    .section__body {
      padding: 0 fn.percent-unit(104);
      background-color: pales.$white;
      .offer {
        // padding: 0 fn.percent-unit(6);
        &__title {
          font-weight: 400;
          line-height: 1.2;
          margin-bottom: fn.percent-unit(15);
          p {
            display: inline-block;
            margin: 0;
          }
          .text--blue {
            display: inline-block;
            margin-right: 10px;
            color: #003a6b;
            font-family: "UTM-Banque", sans-serif;
            font-size: fn.percent-unit(24);
            line-height: 1.5;
            & ~ span {
              font-size: fn.percent-unit(20);
            }
          }
          .text--green {
            margin-left: fn.percent-unit(5);
            font-weight: 500;
            font-size: fn.percent-unit(28);
            text-decoration: none;
            color: pales.$green-3a;
          }
        }
        &__list {
          display: flex;
          flex-wrap: wrap;
          width: fn.percent-unit(1740);
          flex: 0 0 auto;
          &.col-1 {
            width: fn.percent-unit(580);
          }
          &.col-2 {
            width: fn.percent-unit(1160);
          }
        }
        &__item {
          animation: fadeIn 0.1s linear;
          flex: 0 0 auto;
          width: fn.percent-unit(556);
          margin: 0 fn.percent-unit(24) fn.percent-unit(15) 0;
          box-sizing: border-box;
          padding: 0 fn.percent-unit(25) 0 fn.percent-unit(32);
          display: flex;
          align-items: center;
          -webkit-border-radius: 70px;
          -moz-border-radius: 70px;
          border-radius: 70px;
          border: 1px solid pales.$gray-64;
          height: fn.percent-unit(80);
          line-height: 1.5;
          .thumb {
            flex: 0 0 auto;
            vertical-align: middle;
            max-width: fn.percent-unit(87);
            max-height: fn.percent-unit(40);
            margin-right: fn.percent-unit(32);
          }
          .text {
            @include fonts.font-size(20);
            color: pales.$gray-22;
            display: inline-block;
            margin: 0;
          }
        }
      }
      .offer__container {
        display: flex;
        align-items: flex-start;
      }

      .offer--big-container {
        display: flex;
        margin-right: fn.percent-unit(18);
      }

      .offer--big {
        animation: fadeIn 0.1s linear;
        font-size: fn.percent-unit(19);
        color: #fff;
        font-weight: 700;
        line-height: 1.4;
        width: fn.percent-unit(556);
        height: fn.percent-unit(178);
        position: relative;
        &:not(:last-child) {
          margin-right: fn.percent-unit(18);
        }
      }

      .offer--big-img {
        display: block;
        width: 100%;

        img {
          display: block;
          width: 100%;
        }
      }

      .offer--big-content {
        box-sizing: border-box;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        padding: fn.percent-unit(15) fn.percent-unit(16) 0 fn.percent-unit(20);
        display: flex;
        justify-content: space-between;
      }

      .offer--big-text {
        width: fn.percent-unit(349);
        padding-top: fn.percent-unit(31.25);
      }

      .offer--big-qr {
        width: fn.percent-unit(148);
        height: fn.percent-unit(148);

        canvas {
          width: 100% !important;
          height: 100% !important;
          display: block;
        }
      }
    }

    &:not(.section--payment-method) {
      min-height: fn.percent-unit(883px);
    }
    &.section--payment-method {
      height: fn.percent-unit(978px);
    }
  }
}
