// @group Section
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as pales;

/// @group mixin
@use "mixin/fonts" as fonts;

/// @group Section
.section--payment-result {
  & + .keyboard__wrapper {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: fn.percent-unit(512px);
    display: flex;
    justify-content: center;
    align-items: center;
    background: #111111;

    .keyboard-container {
      background: #181818;
      padding: fn.percent-unit(32px);
    }
  }

  .payment-paid-note__box {
    position: absolute;
    top: 0 !important;
    left: 0;
    width: 100%;
    // height: fn.percent-unit(94px);
    padding: fn.percent-unit(19px);

    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.2);

    .payment-paid-note__text {
      font-size: fn.percent-unit(28px);
      margin-right: fn.percent-unit(20);
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .payment-paid-note__button-box {
      margin-right: fn.percent-unit(40);
      min-width: fn.percent-unit(300);
      display: flex;
      justify-content: right;
      align-items: center;

      .button {
        min-width: fn.percent-unit(300);
      }
    }

    &.payment-paid-note--extend {
      background: rgba(241, 194, 27, 0.1);
      border-bottom: none;

      .payment-paid-note__text {
        .icon {
          width: fn.percent-unit(40px);
          height: fn.percent-unit(40px);
          margin-right: fn.percent-unit(20px);

          & > img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }

  .payment-paid-note__box + .block--payment-result {
    .block__header {
      padding-top: fn.percent-unit(112);
    }

    .block__footer {
      margin-top: fn.percent-unit(0);
    }
  }
}
