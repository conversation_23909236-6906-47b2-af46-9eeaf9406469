// @group Section
///

/// @group setting
@use "settings/variables" as var;
@use "settings/function" as fn;
@use "settings/palettes" as pales;

/// @group mixin

/// @group Section
.section--payment-pack {
  @at-root .section#{&} {
    .section__header {
      border-bottom: 1px solid rgba(pales.$black, 0.2);
    }
    .section__body {
      padding-top: fn.percent-unit(10);
    }
    .text.text--note {
      font-weight: 500;
      font-size: fn.percent-unit(20);
    }
  }
}
