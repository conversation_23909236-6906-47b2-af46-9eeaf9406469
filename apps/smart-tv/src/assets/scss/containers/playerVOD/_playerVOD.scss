/////
/// @group billboard
////

/// @param palettes
@use "settings/palettes" as pales;

/// @group function
@use "settings/function" as fn;

/// @group mixin
@use "mixin/position" as position;

/// @group variables
@use "settings/variables" as var;

/// @group components
@use "components/icons/icon" as icons;

$screen-hd: 1280px !default;
$c-white: #fff;

.page-content {
  &.player {
    padding-left: 0;
    background-color: pales.$gray-11;
    .player-wrapper {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 0;
      #player::cue {
        font-size: fn.percent-unit(56px);
        // bottom: 56px;
        padding: fn.percent-unit(56px) fn.percent-unit(112px);
        // @media screen and (max-width: $screen-hd) {
        //   font-size: 38px;
        //   // bottom: 38px;
        //   padding: 38px 76px;
        // }
        width: 100%;
        text-align: center;
        position: absolute;
        left: 0;
        right: 0;
        color: pales.$white;
      }
      .poster {
        img {
          height: auto;
          width: 100%;
        }
      }
      .subs-player {
        font-size: fn.percent-unit(56px);
        // bottom: fn.percent-unit(56px);
        width: 100%;
        text-align: center;
        position: absolute;
        left: 0;
        right: 0;
        // padding: 0 fn.percent-unit(112px);
        box-sizing: border-box;
        color: $c-white;
        z-index: 1;
        overflow: hidden;
        // white-space: pre;

        &.large {
          font-size: fn.percent-unit(72px);
        }
        &.medium {
          font-size: fn.percent-unit(54px);
        }
        &.small {
          font-size: fn.percent-unit(44px);
          bottom: fn.percent-unit(76px);
        }

        > span {
          display: inline-block;
          padding: fn.percent-unit(6px) fn.percent-unit(0px);
          color: $c-white;
          text-shadow: 2px 2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000,
            -2px -2px 0 #000, 2px 0px 0 #000, 0px 2px 0 #000, -2px 0px 0 #000,
            0px -2px 0 #000;
        }
      }
    }
    .player-controller {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
      .filter-box {
        height: 100%;
        width: 100%;
        background-color: rgba(0, 0, 0, 0.5);
      }
      .ads-overlay {
        background: linear-gradient(
          272deg,
          #111 69.56%,
          rgba(17, 17, 17, 0) 98.24%
        );
        width: fn.percent-unit(915px);
        height: fn.percent-unit(1080px);
        position: absolute;
        opacity: 0.8;
        top: 0;
        right: 0;
        z-index: 1;
        .native-ads {
          pointer-events: none;
          // max-width: fn.percent-unit(915px);
          // max-height: fn.percent-unit(686px);
          width: 100%;
          height: auto;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translate(0, -50%);
        }
      }
      .top-controller,
      .bottom-controller {
        position: absolute;
        height: fn.percent-unit(150px);
        width: 100%;
        padding-top: fn.percent-unit(60px);
      }
      .top-controller {
        top: 0;
        left: 0;
        padding-left: fn.percent-unit(var.$sidebar-max-width);
        z-index: 2;
        .control-box {
          display: flex;
          align-items: flex-start;
          .control-btn {
            font-size: fn.percent-unit(28px);
            position: relative;
            line-height: 1.5;
            padding: fn.percent-unit(11px) fn.percent-unit(15px);
            color: #fff;
            height: 100%;
            margin-right: fn.percent-unit(10px);
            display: flex;
            align-items: center;
            border-radius: fn.percent-unit(4px);
            background-color: transparent;
            transition: background-color 200ms, color 200ms;
            &:first-child {
              margin-left: fn.percent-unit(-11px);
            }
            .icon {
              width: auto;
              margin-right: fn.percent-unit(8px);
            }
            svg {
              // margin-right: 0.5em;
              font-size: fn.percent-unit(33px);
              &.vie-arrow-pre-light {
                margin-right: fn.percent-unit(11px);
              }
              &.vie-circle-solid {
                font-size: fn.percent-unit(20px);
              }
            }
            .tags--quality {
              border-radius: fn.percent-unit(2px);
              background-color: pales.$green-3a;
              color: pales.$white;
              font-size: fn.percent-unit(18px);
              font-weight: 700;
              position: absolute;
              top: fn.percent-unit(2px);
              right: 70%;
              // transform: translateX(50%);
              // margin-right: -0.5rem;
              // width: fit-content;
              white-space: nowrap;
            }
            &.focused {
              background-color: pales.$white;
              color: pales.$gray-33;
              .icon .vie {
                color: pales.$gray-33;
              }
            }
            &.live {
              svg.vie-circle-solid {
                color: pales.$red-live;
              }
            }
            &.control-btn--quality {
              padding-left: fn.percent-unit(64px);
            }
          }
        }
      }
      .bottom-controller {
        left: 0;
        bottom: 0;
        padding-left: fn.percent-unit(290px);
        z-index: 2;
        .control-box {
          display: flex;
          align-items: center;
          .play-btn {
            color: pales.$white;
            font-size: fn.percent-unit(33px);
            margin-right: fn.percent-unit(9px);
            position: relative;
            width: fn.percent-unit(79px);
            height: fn.percent-unit(79px);
            &.focused {
              &::after {
                display: block;
                content: "";
                width: fn.percent-unit(79px);
                height: fn.percent-unit(79px);
                border-radius: 50%;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: pales.$white;
                z-index: -1;
              }
              .icon .vie {
                color: pales.$gray-33;
              }
            }
            &.hidden {
              opacity: 0;
            }
            .icon {
              width: fn.percent-unit(79px);
              height: fn.percent-unit(79px);
              display: flex;
              justify-content: center;
              .vie.vie-play-h-rc {
                margin-left: fn.percent-unit(3px);
              }
            }
          }
          .seek-bar {
            display: flex;
            align-items: center;
            .time {
              color: pales.$white;
              font-size: fn.percent-unit(28px);
              font-weight: bold;
              &.current-time {
                width: fn.percent-unit(100px);
                text-align: right;
              }
            }
            .progress-bar {
              width: fn.percent-unit(985px);
              background-color: rgba(248, 248, 248, 0.5);
              margin: 0 fn.percent-unit(27px);
              .bar {
                height: fn.percent-unit(12px);
                opacity: 1;
                background: linear-gradient(
                  90deg,
                  #63d681 0%,
                  #52d085 53.33%,
                  #3ac88a 100%
                );
                position: relative;
                &.point::after {
                  display: block;
                  content: "";
                  width: fn.percent-unit(30px);
                  height: fn.percent-unit(30px);
                  border-radius: 50%;
                  position: absolute;
                  top: 50%;
                  right: 0;
                  transform: translate(50%, -50%);
                  background-color: pales.$green-3a;
                }
              }
            }
          }
        }
      }
      .info {
        position: absolute;
        width: fn.percent-unit(700px);
        top: 25%;
        margin-left: fn.percent-unit(var.$sidebar-max-width);
        .info-box {
          color: #fff;
          .title {
            // font-size: 3em;
            // font-weight: 500;
            // line-height: 1.5;
            height: fn.percent-unit(146px);
            margin-bottom: fn.percent-unit(10px);
            img {
              height: 100%;
              max-width: 100%;
            }
            pointer-events: none;
          }
          .sub-title {
            font-size: fn.percent-unit(32px);
            font-weight: 500;
            line-height: 1.5;
            pointer-events: none;
          }
          .description {
            font-size: fn.percent-unit(28px);
            line-height: 1.5;
            display: -webkit-box;
            height: fn.percent-unit(126px);
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            pointer-events: none;
          }
          & > div:not(:last-child) {
            margin-bottom: fn.percent-unit(10px);
          }

          .recommend-login-controller {
            display: flex;
            margin-top: fn.percent-unit(48px);
            position: relative;
            > div:not(:last-child) {
              margin-right: fn.percent-unit(16);
            }
            .btn.btn--ghost {
              border-color: pales.$white;
              border-radius: fn.percent-unit(8);
            }
          }
        }
      }
      .film-strip {
        position: absolute;
        top: 60%;
        left: 50%;
        display: flex;
        &--item {
          padding: fn.percent-unit(4px);
          &:not(:last-child) {
            margin-right: fn.percent-unit(16px);
          }
        }
        &.focused {
          .film-strip--item {
            &.focused {
              outline: fn.percent-unit(6px) solid pales.$white;
            }
          }
        }
      }
      .controller-btn-group {
        position: static;
        &.controller-btn-group-outtro {
          position: absolute;
          top: 70%;
          right: 11%;
          display: flex;
        }
      }
      .cta-button {
        position: absolute;
        top: 70%;
        left: fn.percent-unit(var.$sidebar-max-width);
        font-size: fn.percent-unit(28px);
        padding: fn.percent-unit(16.8px) fn.percent-unit(28px);
        background: rgba(0, 0, 0, 0.6);
        color: pales.$white;
        border: fn.percent-unit(2px) solid pales.$white;
        border-radius: fn.percent-unit(2px);
        &.flex {
          display: flex;
          .icon {
            width: auto;
            padding-right: fn.percent-unit(9.3px);
          }
        }
        &.focused {
          background-color: pales.$white;
          color: pales.$gray-33;
          .icon .vie {
            color: pales.$gray-33;
          }
        }
        &--film-strip {
          top: 50%;
          left: 50%;
          transform: translate(-50%);
        }
        &--outtro {
          margin-left: fn.percent-unit(12);
        }
        &--outtro,
        &--credit {
          position: relative;
          top: 0;
          left: 0;
        }
        &--premieme {
          display: flex;
          svg {
            margin-right: fn.percent-unit(16.5px);
            font-size: fn.percent-unit(33px);
            &.vie-arrow-pre-light {
              margin-right: fn.percent-unit(11px);
            }
            &.vie-circle-solid {
              font-size: fn.percent-unit(20px);
            }
          }
          &.focused {
            svg.vie-circle-solid {
              color: pales.$gray-33;
            }
          }
          &.live {
            svg.vie-circle-solid {
              color: pales.$red-live;
            }
          }
        }
      }
      .side-popup {
        position: absolute;
        height: 100%;
        width: 28%;
        background-color: rgba(0, 0, 0, 0.7);
        top: 0;
        right: 0;
        padding: fn.percent-unit(60px);
        &--title {
          font-size: fn.percent-unit(32px);
          line-height: 1.5;
          margin-bottom: fn.percent-unit(26px);
          color: #fff;
        }
        &--item {
          font-size: fn.percent-unit(28px);
          line-height: 1.5;
          margin-bottom: fn.percent-unit(11.2px);
          width: fn.percent-unit(524px);
          padding-left: fn.percent-unit(70px);
          padding-top: fn.percent-unit(12px);
          padding-bottom: fn.percent-unit(12px);
          color: pales.$gray-9b;
          position: relative;
          border-radius: fn.percent-unit(5px);
          box-sizing: border-box;

          &.active::before {
            display: block;
            content: "";
            background-size: cover;
            position: absolute;
            top: 50%;
            // background-image: url(/src/assets/images/icon/tick-rounded-light.svg);
            background-image: url(../../../images/icon/tick-rounded-light.svg);
            left: fn.percent-unit(22.4px);
            transform: translateY(-50%);
            width: fn.percent-unit(28px);
            height: fn.percent-unit(28px);
            color: red;
          }

          &.focused::before {
            // background-image: url(/src/assets/images/icon/tick-rounded-dark.svg);
            background-image: url(../../../images/icon/tick-rounded-dark.svg);
          }

          .nav__icon {
            @include icons.icon($size: "basic");
            $icon-size: 32;
            visibility: visible;
            color: white;

            @include position.absolute(top 50% left fn.percent-unit(16));
            height: fn.percent-unit($icon-size);
            margin: 0;
            transform: translateY(-50%);
            width: fn.percent-unit($icon-size);

            .vie {
              $vie-size: fn.percent-unit($icon-size - 4);
              width: $vie-size;
              height: $vie-size;
              font-size: $vie-size;
            }
          }

          .nav__text {
            font-size: fn.percent-unit(28);
            color: #9b9b9b;
          }

          .v-tag {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: fn.percent-unit(12px);
          }
          &.focused {
            background-color: pales.$white;
            color: #222222;
            border-color: pales.$white;

            .nav__icon .vie,
            .nav__text {
              color: #222222;
              z-index: 10;
              position: relative;
            }
          }
        }
      }

      // Animation fade-in-out-up
      .fade-in-down {
        opacity: 0;
        transform: translate3d(0, -100%, 0);
      }

      .fade-in-down-enter {
        transition: all 0.5s ease-in-out !important;
      }
      .fade-in-down-enter-active {
        opacity: 1;
        transform: translate3d(0, 0, 0);
      }
      .fade-in-down-enter-done {
        opacity: 1;
        transform: translate3d(0, 0, 0);
      }
      .fade-in-down-exit {
        transition: all 0.5s ease-in-out !important;
      }
      .fade-in-down-exit-active {
        opacity: 0;
        transform: translate3d(0, -100%, 0);
      }
      .fade-in-down-exit-done {
        opacity: 0;
        transform: translate3d(0, -100%, 0);
      }

      // Animation fade-in-out-down
      .fade-in-up {
        opacity: 0;
        transform: translate3d(0, 100%, 0);
        &.film-strip {
          transform: translate3d(-50%, 100%, 0);
          &.hd {
            transform: translate3d(-50%, 100%, 0) scale(0.66);
          }
        }
      }
      .fade-in-up-enter {
        transition: all 0.5s ease-in-out !important;
      }
      .fade-in-up-enter-active {
        opacity: 1;
        transform: translate3d(0, 0, 0);
        &.film-strip {
          transform: translate3d(-50%, 0, 0);
          &.hd {
            transform: translate3d(-50%, 0, 0) scale(0.66);
          }
        }
      }
      .fade-in-up-enter-done {
        opacity: 1;
        transform: translate3d(0, 0, 0);
        &.film-strip {
          transform: translate3d(-50%, 0, 0);
          &.hd {
            transform: translate3d(-50%, -15%, 0) scale(0.66);
          }
        }
      }
      .fade-in-up-exit {
        transition: all 0.5s ease-in-out !important;
      }
      .fade-in-up-exit-active {
        opacity: 0;
        transform: translate3d(0, 100%, 0);
        &.film-strip {
          transform: translate3d(-50%, 100%, 0);
          &.hd {
            transform: translate3d(-50%, 100%, 0) scale(0.66);
          }
        }
      }
      .fade-in-up-exit-done {
        opacity: 0;
        transform: translate3d(0, 100%, 0);
        &.film-strip {
          transform: translate3d(-50%, 100%, 0);
          &.hd {
            transform: translate3d(-50%, 100%, 0) scale(0.66);
          }
        }
      }
      // Animation fade-in
      .fade-in {
        opacity: 0;
      }
      .fade-in-enter {
        transition: all 0.5s ease-in-out !important;
      }
      .fade-in-enter-active {
        opacity: 1;
      }
      .fade-in-enter-done {
        opacity: 1;
      }
      .fade-in-exit {
        transition: all 0.5s ease-in-out !important;
      }
      .fade-in-exit-active {
        opacity: 0;
      }
      .fade-in-exit-done {
        opacity: 0;
      }

      // Animation fade-in-right
      .fade-in-right {
        opacity: 0;
        transform: translate3d(100%, 0, 0);
      }
      .fade-in-right-enter {
        transition: all 0.5s ease-in-out !important;
      }
      .fade-in-right-enter-active {
        opacity: 1;
        transform: translate3d(0%, 0, 0);
      }
      .fade-in-right-enter-done {
        opacity: 1;
        transform: translate3d(0%, 0, 0);
      }
      .fade-in-right-exit {
        transition: all 0.5s ease-in-out !important;
      }
      .fade-in-right-exit-active {
        opacity: 0;
        transform: translate3d(100%, 0, 0);
      }
      .fade-in-right-exit-done {
        opacity: 0;
        transform: translate3d(100%, 0, 0);
      }
    }

    .box-toast {
      position: absolute;
      width: auto;
      left: fn.percent-unit(144px);
      top: fn.percent-unit(140px);
      background: #111111;
      border: 1px solid #646464;
      border-radius: fn.percent-unit(4px);
      pointer-events: none;

      .frame {
        display: flex;
        align-items: center;
        padding: fn.percent-unit(11) fn.percent-unit(16);
        .icon {
          width: auto;
          .vie {
            color: #3ac882;
          }
        }
        p {
          color: white;
          font-family: Roboto;
          font-style: normal;
          font-weight: normal;
          font-size: fn.percent-unit(20px);
          line-height: 150%;
          padding-left: fn.percent-unit(10px);
          margin: 0;
          span {
            font-weight: bold;
            &.text-warning {
              color: #edc42d;
              font-weight: normal;
            }
          }
        }
      }
      &.warning-message {
        top: fn.percent-unit(63px);
        left: fn.percent-unit(148px);
      }
    }
  }
}

// video::cue {
//   background-color: red;
//   color: white;
// }
// video::cue-region {
//   // background: transparent;
//   background-color: red;
// }
// video::-webkit-media-text-track-display {
//   overflow: visible !important;
//   -webkit-box-sizing: border-box;
//   background: black;
//   padding: 8px;
//   border-radius: 16px;
// }
