///
/// Global style for Intro
///

/// @group setting
@use "settings/palettes" as palettes;
@use "settings/variables" as var;
@use "settings/function" as fn;

/// @group mixin
@use "mixin/background" as bg;
@use "mixin/box" as box;
@use "mixin/pseudo" as pseudo;
@use "mixin/position" as position;

// @mixin intro
@mixin intro() {
  display: block;
  position: relative;
  height: 100vh;
  // background: #080e17;
  background: transparent;
  padding-top: fn.percent-unit(60);
  padding-right: fn.percent-unit(60);
  padding-left: fn.percent-unit(var.$sidebar-max-width);

  .intro {
    &__backdrop {
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      bottom: 0;
      img {
        width: 100%;
        height: auto;
      }

      &::after {
        @include pseudo.pseudo($width: 100%, $height: 100%, $display: block);
        @include position.absolute(top 0 left 0);
        background: rgba(0, 0, 0, 0.7);
      }
    }

    &__thumbnail {
      position: absolute;
      width: fn.percent-unit(1106);
      height: fn.percent-unit(622);
      left: fn.percent-unit(814);
      top: 0;

      &::after {
        @include pseudo.pseudo(
          $width: fn.percent-unit(204),
          $height: fn.percent-unit(622),
          $display: block
        );
        @include position.absolute(top 0 left -2px);
        background: linear-gradient(
          90deg,
          #111111 0%,
          rgba(#111111, 0.15) 70%,
          rgba(#111111, 0) 100%
        );
      }
      &::before {
        @include pseudo.pseudo(
          $width: fn.percent-unit(1106),
          $height: fn.percent-unit(143),
          $display: block
        );
        @include position.absolute(bottom -2px left 0);
        background: linear-gradient(
          0deg,
          palettes.$gray-11 0%,
          rgba(palettes.$gray-11, 0) 100%
        );
      }
    }

    &__img {
      overflow: hidden;
      @include box.box($width: 100%, $height: 100%);
      img {
        width: 100%;
        height: 100%;
      }
    }

    &__wrap {
      display: flex;
      flex-flow: row wrap;
      position: relative;
      z-index: fn.z-index(layer-1);

      & > * {
        flex-grow: 1;
        flex-shrink: 1;
        flex-basis: 0px;
        width: auto;
      }
    }

    &__info {
      flex: 0 0 auto;
      @include box.box(
        $width: fn.percent-unit(700),
        $height: fn.percent-unit(945)
      );
      padding-right: fn.percent-unit(1017);
      margin-right: fn.percent-unit(27);
      position: relative;
      & > * {
        margin-bottom: fn.percent-unit(17);
      }
      .card-intro {
        @include box.box($width: fn.percent-unit(990), $height: null);
        @include position.absolute(top 0 right 0);
        &::after {
          @include pseudo.pseudo(
            $height: fn.percent-unit(1920),
            $width: fn.percent-unit(175),
            $display: block
          );
          @include position.absolute(top 0 left 0);
          background: linear-gradient(
            90deg,
            rgba(8, 14, 23, 0.76) 0%,
            rgba(8, 14, 23, 0) 100%
          );
          transform: matrix(0, -1, -1, 0, 0, 0);
        }
      }
      .card-groups {
        @include box.box($width: fn.percent-unit(990), $height: null);
        @include position.absolute(top 0 right 0);
      }
    }
  }

  .info {
    &__title {
      height: fn.percent-unit(220);
      margin-bottom: fn.percent-unit(32);
      img {
        height: 100%;
        max-width: 100%;
      }
    }
    &__desc {
      color: #cccccc;
      width: fn.percent-unit(701);
      max-height: fn.percent-unit(126);
      margin-bottom: fn.percent-unit(16);
      font-size: fn.percent-unit(28);
      line-height: 1.5;
      font-weight: normal;
      font-style: normal;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    &__list {
      list-style-type: none;
      margin: 0;
      padding: 0;
      margin-bottom: fn.percent-unit(32);
      .item {
        font-size: fn.percent-unit(24);
        line-height: 1.5;
        font-weight: normal;
        font-style: normal;
        color: #cccccc;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        height: fn.percent-unit(36);
      }
    }

    &__view {
      display: flex;
      margin-bottom: fn.percent-unit(17);

      & > * {
        &:not(:last-child) {
          margin-right: fn.percent-unit(28);
        }
      }
      .views {
        font-size: fn.percent-unit(28);
        color: #cccccc;
      }
    }
    &__expire_date {
      font-family: Roboto;
      font-style: normal;
      font-weight: bold;
      font-size: fn.percent-unit(28);
      line-height: 150%;
      color: #ffffff;
    }
  }

  .intro-button-group {
    // width: fn.percent-unit(556);
    width: fn.percent-unit(700);
    // height: fn.percent-unit(322);
    margin-bottom: fn.percent-unit(32);
    box-sizing: border-box;
    overflow: hidden auto;
    max-height: fn.percent-unit(330);
    &.episodes {
      max-height: fn.percent-unit(660);
    }
    &.away-trigger {
      max-height: fn.percent-unit(198);
    }
    .intro-button {
      position: relative;
      height: fn.percent-unit(66);
      width: fn.percent-unit(556);
      font-style: normal;
      font-weight: normal;
      font-size: fn.percent-unit(28);
      line-height: 1.5;
      display: flex;
      flex: 0 0 auto;
      justify-content: flex-start;
      align-items: center;
      color: palettes.$gray-cc;
      padding: fn.percent-unit(12px) fn.percent-unit(16px);
      transition: color 0.2s;
      box-sizing: border-box;
      .icon {
        &:not(:last-child) {
          margin-right: fn.percent-unit(16);
        }
        .vie {
          color: palettes.$gray-cc;
        }
      }
      span {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        color: palettes.$gray-de;
        z-index: 10;
        position: relative;
      }

      p {
        min-width: fn.percent-unit(168);
        text-align: end;
        color: palettes.$gray-de;
        z-index: 10;
      }

      .duration {
        background-color: palettes.$gray-cc;
        width: fn.percent-unit(145);
        height: fn.percent-unit(9);
        border-radius: fn.percent-unit(2);
        margin-left: fn.percent-unit(18);
        margin-right: fn.percent-unit(18);
        .progress {
          background-color: palettes.$green-3a;
          height: 100%;
          display: block;
        }
      }

      &.eps {
        padding-right: fn.percent-unit(19);
        justify-content: space-between;
      }

      // &.active {
      //   // border-radius: fn.percent-unit(1);
      //   // box-shadow: 0 0 0 fn.percent-unit(2.5) palettes.$gray-9b;

      //   // border-color: palettes.$gray-9b;
      //   // border-width: fn.percent-unit(3);
      //   // border-style: solid;
      //   // border-radius: fn.percent-unit(4);
      // }

      &.focus {
        color: palettes.$black;

        &.eps {
          p {
            color: #222222;
          }
        }

        span {
          color: #222222;
          z-index: 10;
        }
        // border-color: palettes.$white;
        // border-width: fn.percent-unit(3);
        // border-style: solid;
        // border-radius: fn.percent-unit(1);
        // box-shadow: 0 0 0 fn.percent-unit(2.5) palettes.$white;
        .icon {
          .vie {
            color: palettes.$black;
            z-index: 10;
          }
        }
      }
      &.disabled,
      &:disabled {
        color: palettes.$gray-33;
        .icon {
          .vie {
            color: palettes.$gray-33;
          }
        }
      }
    }

    .fast-track-btn {
      position: relative;
      color: #fff;
      width: fn.percent-unit(556);
      height: fn.percent-unit(199);
      margin-bottom: fn.percent-unit(16);
      .fast-track-btn-bg {
        position: absolute;
        width: 100%;
        top: 0;
        left: 0;
        img {
          display: block;
          width: 100%;
        }
      }
      .fast-track-btn-desc {
        position: absolute;
        width: fn.percent-unit(365);
        top: fn.percent-unit(26);
        right: fn.percent-unit(16);
        line-height: 1.5;
      }
      .fast-track-btn-title {
        font-size: fn.percent-unit(24);
      }
      .fast-track-btn-text {
        font-size: fn.percent-unit(18);
        letter-spacing: fn.percent-unit(0.18);
      }
      .intro-button {
        position: absolute;
        width: fn.percent-unit(524);
        height: fn.percent-unit(48);
        left: 50%;
        bottom: fn.percent-unit(12);
        transform: translate(-50%, 0);
        display: flex;
        justify-content: center;
        color: #fff;
        border-radius: fn.percent-unit(2);
        border: solid fn.percent-unit(2) #fff;
        font-weight: 600;
        font-size: fn.percent-unit(24);
        &.focus {
          color: #111;
          background-color: #fff;
          .focus-box {
            background-color: #fff !important;
            opacity: 1;
          }
        }
        .focus-box {
          border-radius: fn.percent-unit(2) !important;
          border: solid fn.percent-unit(2) #fff;
          box-sizing: border-box;
          background-color: transparent !important;
          opacity: 0;
        }
        .span {
          color: #fff;
        }
      }
    }
  }

  .list-content {
    list-style-type: none;
    margin: 0;
    padding: 0;
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    margin-right: fn.percent-unit(120);
    padding-top: fn.percent-unit(60);
    width: fn.percent-unit(858);

    .item {
      width: auto;
      height: fn.percent-unit(233);
      margin-bottom: fn.percent-unit(32);
      display: flex;
      flex-flow: row nowrap;
      &__thumbnail {
        height: 100%;
        width: fn.percent-unit(417);
        padding-right: fn.percent-unit(32);
        img {
          overflow: hidden;
          width: 100%;
          height: 100%;
        }
      }
      &__info {
        background-color: red;
        flex: 1 1 0px;
      }
      &__title {
        display: inline-block;
        position: relative;
        height: fn.percent-unit(38);
        font-weight: 500;
        font-size: fn.percent-unit(32);
        line-height: fn.percent-unit(37);
        color: palettes.$white;
      }
      // &__desc {
      // }
    }
  }
}

.focus-intro {
  position: fixed;
  transition: top 0.25s;
  z-index: 1;
  border-radius: fn.percent-unit(1);
  box-shadow: 0 0 0 fn.percent-unit(2.5) palettes.$white;
  &.active {
    box-shadow: 0 0 0 fn.percent-unit(2.5) palettes.$gray-9b;
  }
}

.focus-intro-card {
  position: fixed;
  z-index: 1;
  box-shadow: 0 0 0 fn.percent-unit(4) palettes.$white;
  pointer-events: none;

  &.ribbon-stream {
    left: fn.percent-unit(var.$sidebar-max-width);
  }
}
