@use "../../settings/variables" as var;
@use "../../settings/function" as fn;
@use "../../settings/palettes" as pales;
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(fn.percent-unit(30px));
  }
  100% {
    opacity: 1;
    transform: translateY(fn.percent-unit(0px));
  }
}
@keyframes fadeOut {
  0% {
    opacity: 1;
    transform: translateY(fn.percent-unit(0px));
  }
  100% {
    opacity: 1;
    transform: translateY(fn.percent-unit(30px));
  }
}
.main--livestream {
  .title-meta {
    color: pales.$white;
    // &--livestream {
    // }
    &__wrap {
      flex-direction: column;
    }
  }

  .ribbon-group {
    position: absolute;
    top: fn.percent-unit(670px);
  }

  .show-content {
    opacity: 0;
    animation: fadeIn 0.1s linear;
  }
  .hide-content {
    opacity: 0;
    animation: fadeOut 0.1s linear;
  }
}

.livestream-controller {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  // &__content {
  // }
  &--control {
    display: flex;
    flex-direction: row;
    padding-left: fn.percent-unit(144px);
    padding-top: fn.percent-unit(67px);

    .focus {
      background-color: pales.$white;
      color: #000000;
      .icon .vie {
        fill: #000000;
      }
    }
    .btn-back {
      display: flex;
      flex-direction: row;
      font-weight: normal;
      font-size: fn.percent-unit(28px);
      line-height: 150%;
      align-items: center;

      padding: fn.percent-unit(12px) fn.percent-unit(19px);

      .icon {
        padding-top: fn.percent-unit(4px);
      }
    }
  }
}
