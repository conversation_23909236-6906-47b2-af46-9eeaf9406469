/// @group variables
@use "../../../settings/variables" as var;
@use "../../../settings/function" as fn;
@use "../../../settings/palettes" as color;

/// @group mixin
@use "../../../mixin/box" as displaybox;
@use "../../../mixin/position" as position;

.game-result {
  .game-container & {
    @include position.absolute(top fn.percent-unit(50) left 0 right 0 bottom 0);
    height: auto !important;
    max-height: fn.percent-unit(400);
    min-height: fn.percent-unit(350);
    display: flex;
    align-items: center;
    justify-content: center;

    &__content {
      font-weight: 500;
      font-size: fn.percent-unit(40);
      line-height: fn.percent-unit(52);
      color: color.$white;
      text-align: center;
      text-shadow: 0 0 fn.percent-unit(3) rgba(0, 0, 0, 0.5);

      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      &--icon {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        img {
          height: fn.percent-unit(48);
          width: fn.percent-unit(48);
          margin-left: fn.percent-unit(10);
        }
      }
    }
    &__pause {
      position: absolute;
      left: 0;
      right: 0;
      width: 100vw;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      top: fn.percent-unit(-86);
      .btn {
        font-size: fn.percent-unit(28);
        font-weight: 500;
        line-height: 140%;
        color: color.$white;
      }
    }

    &__winning {
      display: flex;
      flex-direction: row;
      justify-content: center;
      width: 100%;
      max-height: fn.percent-unit(400);

      .game-result__content {
        flex: 1;
      }
    }

    &__title {
      font-weight: 700;
      font-size: fn.percent-unit(32);
      line-height: fn.percent-unit(37.5);
      text-transform: uppercase;
      margin-bottom: fn.percent-unit(61);
    }
    &__price {
      color: color.$warning;
      font-size: fn.percent-unit(72);
      line-height: fn.percent-unit(84);
      font-weight: 500;
      letter-spacing: fn.percent-unit(0.3);

      &--vnd {
        text-decoration: underline;
      }
    }

    &__qr {
      max-height: fn.percent-unit(400);
      padding-left: fn.percent-unit(69);
      padding-right: fn.percent-unit(117);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      font-weight: 400;
      font-size: fn.percent-unit(32);
      line-height: fn.percent-unit(37.5);
      color: color.$white;
      border-left: 2px solid color.$gray-cc;
      p {
        margin: 0;
      }
      .text-strong {
        font-weight: 700;
      }
      div:first-child {
        display: flex;
        flex-direction: row;
        align-items: flex-start;

        svg {
          overflow: unset;
          width: fn.percent-unit(34);
          height: auto;
          margin-right: fn.percent-unit(11);
          margin-top: 0;
        }
      }
      .qr-code {
        margin-top: fn.percent-unit(25);
      }
    }
  }
}
