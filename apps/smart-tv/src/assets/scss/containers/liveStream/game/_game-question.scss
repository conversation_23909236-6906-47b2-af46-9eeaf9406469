/// @group variables
@use "../../../settings/variables" as var;
@use "../../../settings/function" as fn;
@use "../../../settings/palettes" as color;

/// @group mixin
@use "../../../mixin/box" as displaybox;
@use "../../../mixin/position" as position;

.game-question {
  .game-container & {
    @include position.absolute(top fn.percent-unit(50) left 0 right 0);
    &__pause {
      position: absolute;
      left: 0;
      right: 0;
      width: 100vw;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      margin-left: auto;
      margin-right: auto;
      top: fn.percent-unit(-86);
      .btn {
        font-size: fn.percent-unit(28);
        font-weight: 500;
        line-height: 140%;
        color: color.$white;
        width: fn.percent-unit(258);
        white-space: nowrap;
        &.focus path {
          fill: color.$gray-22;
        }
        span {
          padding-left: fn.percent-unit(12);
        }

        .btn__icon {
          width: fn.percent-unit(39px);
          padding-top: fn.percent-unit(8px);
          fill: color.$white;
        }
      }
    }
    &__group {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      &:first-child {
        margin-bottom: fn.percent-unit(30);
        margin-top: fn.percent-unit(20);
      }
    }
    &__question {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      height: fn.percent-unit(112);
      margin-left: fn.percent-unit(20);
      .label {
        font-weight: 500;
        font-size: fn.percent-unit(40);
        line-height: fn.percent-unit(56);
        letter-spacing: fn.percent-unit(0.3);
        color: color.$white;
        width: 100%;
        text-align: left;
      }

      .label-option {
        font-size: fn.percent-unit(32);
        line-height: fn.percent-unit(37.5);
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        color: color.$white;

        img {
          width: fn.percent-unit(48);
          height: fn.percent-unit(48);
          margin-right: fn.percent-unit(10);
        }

        .correct {
          color: color.$green-3a;
          font-size: fn.percent-unit(48);
          line-height: fn.percent-unit(56.25);
          margin-right: fn.percent-unit(12);
        }
        .incorrect {
          color: color.$red-e7;
          font-size: fn.percent-unit(48);
          line-height: fn.percent-unit(56.25);
          margin-right: fn.percent-unit(12);
        }
      }
    }

    &__item {
      width: fn.percent-unit(700);
      border: 2px solid color.$gray-de;
      box-sizing: border-box;
      border-radius: 999px;
      padding: fn.percent-unit(5) fn.percent-unit(30) fn.percent-unit(5)
        fn.percent-unit(59);
      @include position.relative();
      margin: fn.percent-unit(8) fn.percent-unit(10);
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .label {
        font-weight: 500;
        font-size: fn.percent-unit(36);
        line-height: 140%;
        color: color.$white;
        margin: 0;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        max-width: fn.percent-unit(556);
      }

      &.focus {
        background: color.$white;
        .label {
          color: color.$gray-22;
        }
      }

      &.selected,
      &.selected.focus {
        .label {
          color: color.$green-3a;
        }
      }

      &.correct {
        position: relative;
        background: color.$green-3a;
        border: 4px solid color.$transparent !important;
        .label {
          color: color.$green-13 !important;
        }

        &--noicon {
          border: none !important;
          &:before {
            display: none;
          }
        }
        &:before {
          content: "";
          position: absolute;
          left: fn.percent-unit(11);
          top: 0;
          bottom: 0;
          margin-top: auto;
          margin-bottom: auto;
          width: fn.percent-unit(40);
          height: fn.percent-unit(40);
          // background: url("/src/assets/images/correct-answer.svg") center
          //   no-repeat;
          background: url("../../../../images/correct-answer.svg") center
            no-repeat;
          background-size: cover;
        }
      }
      &.incorrect {
        position: relative;
        background: color.$error;
        border: 4px solid color.$transparent !important;
        .label {
          color: color.$red-79 !important;
        }
        &:before {
          content: "";
          position: absolute;
          left: fn.percent-unit(11);
          top: 0;
          bottom: 0;
          margin-top: auto;
          margin-bottom: auto;
          width: fn.percent-unit(40);
          height: fn.percent-unit(40);
          background: url("../../../../images/incorrect-answer.svg") center
            no-repeat;
          // background: url("/src/assets/images/incorrect-answer.svg") center
          //   no-repeat;
          background-size: cover;
        }
      }
      &.focusSelect {
        border: 4px solid color.$white !important;
      }

      &.timeout {
        border-color: color.$gray-64;
        .label {
          color: color.$gray-64;
        }
      }
    }
  }
}
