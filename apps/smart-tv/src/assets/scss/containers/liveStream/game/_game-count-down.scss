/// @group variables
@use "../../../settings/function" as fn;
@use "../../../settings/palettes" as color;

.count-down {
  position: relative;
  height: fn.percent-unit(90);
  width: fn.percent-unit(90);

  &__circle {
    fill: none;
    stroke: none;
  }

  &__path-elapsed {
    stroke-width: fn.percent-unit(4px);
    stroke: rgba(color.$gray-cc, 0.5);
  }

  &__label {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: fn.percent-unit(48);
    line-height: fn.percent-unit(56);
    color: rgba(color.$white, 0.5);
    font-weight: bold;
    &.active {
      color: color.$warning;
    }
  }

  &__path-remaining {
    /* Just as thick as the original ring */
    stroke-width: fn.percent-unit(4px);
    /* Rounds the line endings to create a seamless circle */
    stroke-linecap: round;
    /* Makes sure the animation starts at the top of the circle */
    transform: rotate(90deg);
    transform-origin: center;
    /* One second aligns with the speed of the countdown timer */
    transition: 1s linear all;
    /* Allows the ring to change color when the color value updates */
    stroke: color.$warning;
  }
}
