/// @group variables
@use "../../../settings/variables" as var;
@use "../../../settings/function" as fn;
@use "../../../settings/palettes" as color;

/// @group mixin
@use "../../../mixin/box" as displaybox;
@use "../../../mixin/position" as position;

.game-background {
  .game-container & {
    background: rgba(color.$gray-11, 0.9);
    border-top-left-radius: fn.percent-unit(20);
    border-top-right-radius: fn.percent-unit(20);
    border: fn.percent-unit(3) solid #ecbd57;
    border-bottom: none;
    height: 100%;
    width: 100%;
    box-sizing: content-box;
    transform: translateX(-(fn.percent-unit(3)));
  }
}
