/// @group variables
@use "../../../settings/variables" as var;
@use "../../../settings/function" as fn;
@use "../../../settings/palettes" as color;

/// @group mixin
@use "../../../mixin/box" as displaybox;
@use "../../../mixin/position" as position;

.join-game-container {
  .game-container & {
    position: absolute;
    top: fn.percent-unit(70);
    right: fn.percent-unit(84);
    display: flex;
    flex-direction: row;

    .text-yellow {
      color: color.$warning;
    }
    .box {
      background: color.$black;
      border: fn.percent-unit(4) solid color.$white;
      border-radius: fn.percent-unit(8);
      padding: fn.percent-unit(12) fn.percent-unit(28);
      //font
      font-size: fn.percent-unit(32);
      line-height: fn.percent-unit(37);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      p {
        width: fn.percent-unit(498);
        color: color.$white;
        text-align: left;
        font-weight: 500;
        margin: 0;

        span {
          color: color.$warning;
        }
      }

      &__button-group {
        margin-left: fn.percent-unit(74);

        .btn {
          font-size: fn.percent-unit(28);
          font-weight: 500;
          max-width: fn.percent-unit(230);
          white-space: nowrap;
        }

        .btn:first-child {
          margin-right: fn.percent-unit(20);
        }
      }
    }

    .copy-right {
      font-size: fn.percent-unit(28);
      font-weight: 700;
      line-height: fn.percent-unit(39.2);
      color: color.$white;

      display: flex;
      flex-direction: row;
      align-items: flex-start;
      margin-top: fn.percent-unit(22);

      p {
        margin: 0;
      }

      svg {
        overflow: unset;
        width: fn.percent-unit(34);
        height: auto;
        margin-right: fn.percent-unit(11);
        margin-top: 0;
      }
    }

    .qr-code {
      margin-left: fn.percent-unit(22);
      width: fn.percent-unit(210);
    }
  }
}
