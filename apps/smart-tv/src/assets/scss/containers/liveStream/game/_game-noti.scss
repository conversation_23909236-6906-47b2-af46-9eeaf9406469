/// @group variables
@use "../../../settings/variables" as var;
@use "../../../settings/function" as fn;
@use "../../../settings/palettes" as color;

/// @group mixin
@use "../../../mixin/box" as displaybox;
@use "../../../mixin/position" as position;

.game-noti {
  .game-container & {
    background: color.$gray-11;
    opacity: 0.95;
    border-radius: fn.percent-unit(8);
    border: fn.percent-unit(4px) solid color.$warning;
    padding: fn.percent-unit(30);
    @include position.absolute(
      right fn.percent-unit(80) bottom fn.percent-unit(70)
    );

    &--white {
      border-color: color.$white;
    }

    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: center;
    width: fit-content;

    p {
      color: color.$white;
      text-shadow: 0 0 fn.percent-unit(3) rgba(0, 0, 0, 0.5);
      font-size: fn.percent-unit(28);
      line-height: 150%;
      margin: 0;
    }

    .text-yellow {
      color: color.$warning;
    }

    .btn {
      min-width: fn.percent-unit(238);
      font-size: fn.percent-unit(28);
      font-weight: 500;
      margin-left: fn.percent-unit(74);
    }

    .count-down {
      width: fn.percent-unit(60);
      height: fn.percent-unit(60);
      margin-left: fn.percent-unit(14);

      &__label {
        font-size: fn.percent-unit(24px);
      }
    }
  }
}
