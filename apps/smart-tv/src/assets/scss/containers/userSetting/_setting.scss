@use "../../settings/variables" as var;
@use "../../settings/function" as fn;
@use "../../settings/palettes" as pales;
@use "../../mixin/truncated" as truncate;
$EasingLinear: linear;

@mixin flexPosition($justify-content, $align-item) {
  -webkit-justify-content: $justify-content;
  justify-content: $justify-content;
  -webkit-align-items: $align-item;
  align-items: $align-item;
}

@mixin displayFlex() {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

@mixin flexItem() {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}

@mixin TransitionWithDurationAndEase($duration, $easing) {
  -webkit-transition: all $duration $easing;
  -moz-transition: all $duration $easing;
  -ms-transition: all $duration $easing;
  -o-transition: all $duration $easing;
  transition: all $duration $easing;
}

@mixin box-shadow($args) {
  -webkit-box-shadow: $args;
  -moz-box-shadow: $args;
  box-shadow: $args;
}

@mixin radius($radius) {
  -webkit-border-radius: $radius;
  -moz-border-radius: $radius;
  -ms-border-radius: $radius;
  -o-border-radius: $radius;
  border-radius: $radius;
}

@mixin focused-item() {
  @include box-shadow(0 0.5em 2em rgba(0, 0, 0, 1));
}

.user-setting-page {
  padding-left: fn.percent-unit(144px);
  height: 100vh;

  .page-wrap,
  .content-right {
    height: 100%;
  }

  .nav.nav--tabs {
    margin: fn.percent-unit(20px) 0;
  }

  .content-right {
    display: flex;
    flex-direction: column;
  }

  .sect {
    &.sect-channel {
      .sect-cate-title {
        padding-top: 1.3em;
        margin-bottom: 2em;
      }

      .sect-lst {
        clear: both;
        overflow: hidden;

        &-channel {
          color: #ffffff;
          width: 23.9em;
          float: left;
          height: auto;
          min-height: 5em;
          margin-bottom: 3.2em;

          &:nth-child(3n + 1) {
            clear: both;
          }

          &-inner {
            position: relative;
            padding-left: 6.5em;

            [class^="icon-"] {
              display: block;
              position: absolute;
              left: 0;
              top: 0;
              font-size: 3em;
              padding: 0.2em;
              border: 0.08em solid #ffffff;
              width: 1.6em;
              height: 1.6em;
              text-align: center;
              //@include radius(0.1em);
            }

            .logo-channel {
              @include displayFlex;
              @include flexPosition(flex-start, center);
              max-width: 11em;
              //padding-top: 0.8em;
              height: 4.6em;

              > img {
                max-width: 100%;
                //max-height: 3.8em;
                max-height: 100%;
              }
            }
          }

          &.active {
            .sect-lst-channel-inner {
              [class^="icon-"] {
                border-color: #9b9b9b;
                background: #9b9b9b;
              }
            }
          }

          &.focused {
            .sect-lst-channel-inner {
              [class^="icon-"] {
                border-color: #3ac88a;
                background: #3ac88a;
              }
            }
          }

          /*&.active {
                        .sect-lst-channel-inner {
                            [class^="icon-"] {
                                border-color:  #3ac88a;
                                background:  #3ac88a;
                            }
                        }
                    }*/
        }
      }
    }

    &-signup {
      &.sect-setting.sect-single {
        .setting-inner {
          .icon-setting-page {
            &.icon-setting-user {
              padding-bottom: 1.325em;
              color: #6c6c6c;
              background-color: #2f2f2f;
              border-color: #6c6c6c;

              .vie {
                font-size: 10em;
              }
            }
          }
        }
      }
    }
  }

  .sect-single {
    display: flex;
    justify-content: center;
    flex: 1;
    height: 100%;
    flex-direction: column;
    -webkit-flex-direction: column;

    &.sect-sign-out,
    &.sect-setting,
    &.sect-login {
      flex-direction: column;
      -webkit-flex-direction: column;

      .sign-out-inner,
      .setting-inner,
      .login-inner,
      .profile-empty {
        @include flexItem;
        height: 100%;
        flex-direction: column;
        @include TransitionWithDurationAndEase(0.27s, $EasingLinear);

        .avatar-circle {
          margin: 0 0 fn.percent-unit(40px);
          width: 15em;
          height: 15em;
          border: 0.3em solid #cecece;
        }

        .icon-setting-page {
          margin: 0 0 fn.percent-unit(30px);
          color: #6c6c6c;
          width: fn.percent-unit(175px);
          @include flexItem;

          img {
            width: 100%;
          }

          &.icon-setting-user {
            width: 15em;
            height: 15em;
            border: 0.3em solid #9b9b9b;
            padding: 0.5em;
            @include radius(50%);
            overflow: hidden;
            background-color: #6c6c6c;
          }

          [class^="icon-"],
          .vie {
            height: fn.percent-unit(283px);
            width: fn.percent-unit(283px);
            stroke-width: 0;
            stroke: currentColor;
            fill: currentColor;
          }
        }

        .qr-code {
          display: block;
          margin-top: fn.percent-unit(30px);
          height: fn.percent-unit(200px);
          width: fn.percent-unit(200px);

          &-inner {
            width: 100%;
          }
        }

        .setting__icon {
          margin: 0 0 fn.percent-unit(30px);
          width: fn.percent-unit(175px);
          @include flexItem;

          img {
            width: 100%;
          }
        }

        .setting__title {
          color: pales.$gray-de;
          font-size: fn.percent-unit(36px);
          font-weight: 400;
          letter-spacing: 0.3px;
          line-height: fn.percent-unit(50px);
          margin-top: 0;
          margin-bottom: fn.percent-unit(53px);
        }

        .setting__description {
          color: pales.$gray-9b;
          font-size: fn.percent-unit(28px);
          font-weight: 400;
          letter-spacing: 0.3px;
          line-height: fn.percent-unit(40px);
          margin: 0;
        }

        .setting__link {
          color: #fff;
          font-size: fn.percent-unit(36px);
          font-weight: 400;
          letter-spacing: 0.3px;
          line-height: fn.percent-unit(50px);
          margin: 0.3vw 0 0;
        }

        .setting__text {
          color: pales.$gray-9b;
          font-size: fn.percent-unit(28px);
          font-weight: 400;
          letter-spacing: 0.3px;
          line-height: fn.percent-unit(40px);
          margin: fn.percent-unit(50px) 0 0;
        }

        .sign-out-name,
        .title-inner {
          font-size: fn.percent-unit(64px);
          font-weight: 400;
          color: #e3e3e3;
          margin-bottom: 0.3em;
          text-align: center;
          // text-transform: uppercase;
        }

        .title-code {
          font-size: 2.9em;
          font-weight: 300;
          margin-bottom: 1em;

          img {
            max-width: 1.3em;
          }
        }

        .sign-out-mail,
        .link {
          font-size: fn.percent-unit(40px);
          margin: 0 !important;
          margin-bottom: fn.percent-unit(20px) !important;
          color: #6c6c6c;
          max-width: 90%;
          text-align: center;
        }

        .title-inner {
          font-size: fn.percent-unit(40px);
          margin-bottom: fn.percent-unit(4px);
          color: #6c6c6c;
        }

        .link {
          color: #e3e3e3;
        }

        .btn {
          min-width: fn.percent-unit(380px);
          margin-top: fn.percent-unit(30px);

          &.focus {
            background: #ffffff;
          }
        }

        .profile-empty-img {
          display: block;
          width: 26em;
          height: 21em;
          margin-bottom: 3em;

          > img {
            display: block;
            height: 100%;
            margin: auto;
          }
        }
      }

      .login-inner {
        max-width: 100em;
        text-align: center;

        .sign-out-mail {
          margin-bottom: 2em;
          max-width: 100%;
        }

        .title-inner {
          color: #e3e3e3;
          margin-bottom: 0.3em;
        }

        .form-control {
          &--ip {
            min-width: 17.5em;
            @include radius(2px);
            border: 2px solid #4a4a4a;
            margin-bottom: 1.6em;
            font-size: 2.5em;
            background: #4a4a4a00;
            text-align: center;
            color: #e3e3e3;
            letter-spacing: 0.25em;

            & ~ .icon,
            & + .icon {
              font-size: 3em;
              color: #ffffff;

              &.middle {
                width: 1.375em;
                height: 1.375em;
                top: 50%;
                bottom: initial;
                margin-top: -0.6875em;
                right: 0.25em;
                display: flex;
                align-items: center;
                justify-content: center;
              }

              &.focus {
                color: #000000;
                box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.5);
              }
            }
          }

          .btn {
            margin-bottom: 0;
          }

          .group-btn {
            max-width: 45em;

            .btn {
              margin-bottom: 0;
              min-width: 12.5em;

              &:first-child {
                margin-bottom: 1em;
              }
            }
          }
        }

        .keyboard {
          font-size: 2.5em;
          color: #9b9b9b;
          position: relative;
          padding-right: 4.25em;
          margin-bottom: 1em;

          .lst {
            .chr {
              display: inline-flex;
              padding: 0.25em;
              width: 1.625em;
              height: 1.625em;
              @include radius(5em);
              font-weight: 500;
              font-size: 1.125em;
              justify-content: center;
              align-items: center;
              line-height: normal;
              margin: 0.12em 0.125em 0.25em;

              &.focused,
              &.hover {
                // color: #9b9b9b;
                // background:  #3ac88a;
                color: #000000;
                background: rgba(#ffffff, 1);
                @include focused-item;
              }

              &:hover {
                background: rgba(#ffffff, 0.1);
              }

              &.login {
                &.focused {
                  color: #000000;
                  background: #ffffff;
                  box-shadow: 0.125em 0.125em 0.5em rgba(#000000, 0.5);
                  // @include focused-item;
                }
              }
            }

            &.lst-rt {
              position: absolute;
              right: 0;
              top: 0;

              .chr {
                display: block;
                width: auto;
                padding: 0.125em 0.25em;

                [class^="icon-"] {
                  display: block;
                  font-size: 1.1em;
                }

                &-last {
                  &.remove {
                    height: 1.625em;
                    width: auto;
                    margin-bottom: 0.5em;
                    padding: 0;

                    // font-size: ;
                    .vie {
                      font-size: 1.5em;
                      line-height: normal;
                    }
                  }

                  &.clear {
                    display: flex;
                    height: 1.5em;
                  }
                }
              }
            }
          }
        }
      }
    }

    &.sect-setting {
      height: fn.percent-unit(936px);

      .setting-inner {
        .icon-setting-page {
          &.icon-setting-user {
            // padding-top: 3.5em;
            border-color: #9b9b9b;
            background-color: #6c6c6c;
            color: #9b9b9b;

            .vie {
              font-size: 12em;
            }

            &.pt-3 {
              padding-top: 3.5em;
            }
          }
        }

        .sign-out-name {
          // text-transform: uppercase;
          margin: 0 !important;
          margin-bottom: fn.percent-unit(20px) !important;
        }

        .sign-out-mail {
          max-width: 100%;
          font-size: fn.percent-unit(32px);
          margin-bottom: fn.percent-unit(20px);
        }
      }
    }

    &.sect-empty-page {
      .profile-empty-img {
        display: block;
        width: 26em;
        height: 21em;
        margin: 0 auto 1.5em;

        > img {
          display: block;
          height: 100%;
          margin: auto;
        }
      }

      .sign-out-name {
        font-size: 2.05em;
        font-weight: 500;
        color: #e3e3e3;
        text-align: center;
      }
    }

    .profile-inner {
      align-items: center;
      display: -webkit-flex;
      display: flex;
      justify-content: center;
      height: 100%;
      flex-direction: column;
      transition: all 0.27s linear;

      .icon-profile-page {
        margin: 0 0 2.5em;
        color: #6c6c6c;
        @include flexItem;

        &.icon-profile-user {
          width: 15em;
          height: 15em;
          border: 0.3em solid #9b9b9b;
          padding: 0.5em;
          @include radius(50%);
          overflow: hidden;
          background-color: #6c6c6c;
        }

        [class^="icon-"],
        .vie {
          height: fn.percent-unit(283px);
          width: fn.percent-unit(283px);
          stroke-width: 0;
          stroke: currentColor;
          fill: currentColor;
        }
      }
    }
  }

  .grid-list {
    &--container {
      overflow-y: auto;
      position: relative;
    }

    .grid {
      // transition-duration: 0.2s;
      transform: translateY(0);
    }
  }

  .card--poster {
    &.card {
      width: fn.percent-unit(230px);
      height: fn.percent-unit(329px);

      .card__thumbnail {
        width: fn.percent-unit(230px);
        height: fn.percent-unit(329px);

        & > .card__img {
          width: 100%;
          height: 100%;

          .bg-box {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-repeat: no-repeat;
            background-position: center top;
            background-size: cover;
            z-index: -1;
          }

          & > img {
            width: 100%;
            height: 100%;
          }
        }
      }

      &:not(:nth-child(7)) {
        margin-right: fn.percent-unit(20px);
        margin-bottom: fn.percent-unit(16px);
      }
    }
  }

  // Profile subs
  .wrapper-section.wrapper-section--subs {
    height: fn.percent-unit(1080-128-30);
    overflow-y: hidden;
  }

  .sub-list {
    position: relative;
    &__title-box {
      display: flex;
      align-items: center;
      color: #fff;
      padding-bottom: fn.percent-unit(12px);
    }

    &__title {
      font-size: fn.percent-unit(24px);
      font-weight: 500;
    }

    &__title-note {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: fn.percent-unit(24px);
      font-size: fn.percent-unit(20px);
      font-weight: 400;

      & > .icon {
        color: #3ac882;
        width: fn.percent-unit(32px);
        height: fn.percent-unit(32px);
        margin-right: fn.percent-unit(10px);

        & > img {
          width: 100%;
          height: 100%;
        }
      }
    }

    &__list {
      display: flex;
      flex-flow: row wrap;
    }
  }

  .sub-item {
    box-sizing: border-box;
    width: fn.percent-unit(450px);
    height: fn.percent-unit(246px);
    padding: fn.percent-unit(32px) fn.percent-unit(16px) fn.percent-unit(16px)
      fn.percent-unit(32px);
    margin-right: fn.percent-unit(24px);
    margin-bottom: fn.percent-unit(24px);
    border: fn.percent-unit(4px) solid;
    border-radius: fn.percent-unit(14px);
    color: #fff;
    display: flex;
    flex-direction: column;

    &__title {
      font-size: fn.percent-unit(32px);
      line-height: fn.percent-unit(37.5px);
      font-weight: 500;
      margin-bottom: fn.percent-unit(12px);
    }

    &__info {
      font-size: fn.percent-unit(23px);
      line-height: 1.5;
    }

    &__note {
      margin-top: auto;
      font-size: fn.percent-unit(20px);
      font-weight: 500;
    }

    &--active {
      border-color: rgba(58, 200, 130, 0.5);
    }

    &--remain {
      border-color: #dedede;

      .sub-item__info {
        color: #ccc;
      }
    }

    &--expired {
      border-color: #333333;

      .sub-item__title {
        color: #ccc;
      }

      .sub-item__info {
        color: #9b9b9b;
      }
    }

    &.focus {
      background: #fff;
      border-color: #fff;

      .sub-item__title {
        color: #111;
      }

      .sub-item__info {
        color: #222;
      }

      .sub-item__note {
        color: #111;
      }
    }
  }

  .sect-profile {
    .profile-inner {
      .icon-profile-page {
        &.icon-profile-user {
          // padding-top: 3.5em;
          border-color: #9b9b9b;
          background-color: #6c6c6c;
          color: #9b9b9b;

          .vie {
            font-size: 12em;
          }

          &.pt-3 {
            padding-top: 3.5em;
          }
        }
      }

      .sign-out-name {
        font-size: fn.percent-unit(48px);
        font-weight: 400;
        color: #e3e3e3;
        text-align: center;
        margin: 0 0 fn.percent-unit(20px);
        // text-transform: uppercase;
      }

      .sign-out-mail {
        max-width: 100%;
        font-size: fn.percent-unit(32px);
        margin: 0 0 1.0416666667vw !important;
        color: #6c6c6c;
        text-align: center;
      }
    }
  }

  .sect-devices {
    color: pales.$white;
    width: fn.percent-unit(1396px);
    margin-left: fn.percent-unit(26px);
    justify-content: flex-start;
    background-color: pales.$gray-11;
    height: fn.percent-unit(951);
    box-sizing: border-box;

    &__group-subtitle {
      padding-left: fn.percent-unit(15);
    }

    &__group-title {
      flex: 0 0 auto;
      margin-top: fn.percent-unit(38);
      background-color: pales.$gray-11;
      position: relative;
      z-index: 1;
      padding-bottom: fn.percent-unit(10);
      height: fn.percent-unit(72);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &__group-btns {
      display: flex;
    }

    &__group-btn {
      display: flex;
      flex: 0 0 auto;
      align-items: center;
      padding: fn.percent-unit(10) fn.percent-unit(30);
      font-size: fn.percent-unit(28);
      line-height: 140%;
      font-weight: 500;
      color: white;
      border-radius: 2px;
      border: solid 2px pales.$gray-9b;
      background: transparent;
      fill: none;
      outline: none;

      &--disabled {
        opacity: 0.5;
      }

      &--focused {
        background-color: white;
        border-color: white;
        color: pales.$gray-22;
      }

      &:not(:last-child) {
        margin-right: fn.percent-unit(15);
      }
    }

    &__group-btn__ic {
      width: fn.percent-unit(32);
      height: fn.percent-unit(32);
      margin-right: fn.percent-unit(14);
    }

    &__title {
      flex: 0 0 auto;
      font-weight: 500;
      font-size: fn.percent-unit(36px);
      line-height: 140%;
      letter-spacing: fn.percent-unit(0.3);

      &--current {
        padding-bottom: fn.percent-unit(10);
      }
    }

    &__subtitle {
      font-weight: 500;
      font-size: fn.percent-unit(28px);
      line-height: 140%;
      letter-spacing: fn.percent-unit(0.3);
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      word-break: break-all;
      padding-right: fn.percent-unit(32px);
      color: #ffffff;

      &--empty {
        font-weight: 400;
      }
    }

    &__description {
      font-weight: 400;
      font-size: fn.percent-unit(24px);
      line-height: 150%;
      letter-spacing: fn.percent-unit(0.3);
      // opacity: 0.7;
      color: pales.$gray-9b;
      display: flex;
      flex-direction: column;
      flex: 0 0 auto;
      width: fn.percent-unit(600);
    }

    &__description-avatar__container {
      position: relative;
      width: fn.percent-unit(32);
      height: fn.percent-unit(32);
      margin-right: fn.percent-unit(8);
      flex: 0 0 auto;
    }

    &__description-avatar {
      display: flex;
      flex: 0 0 auto;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      border-radius: 50%;
      width: 100%;
      height: 100%;

      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    &__description-avatar__kid {
      position: absolute;
      top: 0;
      right: 0;
      width: fn.percent-unit(10);
      height: fn.percent-unit(10);
      z-index: 11;

      img {
        display: block;
        width: 100%;
      }
    }

    &__checkbox {
      width: fn.percent-unit(25);
      height: fn.percent-unit(25);
      border-radius: 1px;
      border: solid 2px pales.$gray-9b;
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;

      &--active {
        border-color: pales.$green-3a;
        background-color: pales.$green-3a;
      }

      &-ic {
        fill: white;
        opacity: 0;

        svg {
          display: block;
        }

        &--active {
          opacity: 1;
        }
      }
    }

    &__description-1 {
      display: flex;
      align-items: center;
      margin-bottom: fn.percent-unit(10);
    }

    &__description-2 {
      display: flex;
    }

    &__description-ic-clock {
      margin-right: fn.percent-unit(9);
    }

    .device-list {
      overflow: hidden;
      position: relative;
      padding-top: 0;
      box-sizing: border-box;
      transition: padding-top 0.25s linear;

      .grid {
        display: block !important;
      }

      &--current {
        flex: 0 0 auto;
      }

      &--scrolled {
        padding-top: fn.percent-unit(60);

        &::before {
          content: "";
          position: absolute;
          width: 100%;
          height: fn.percent-unit(60);
          top: 0;
          left: 0;
          background: linear-gradient(to bottom, #111111 0%, transparent 100%);
          z-index: 10;
        }
      }

      &--scroll {
        &::after {
          content: "";
          position: absolute;
          width: 100%;
          height: fn.percent-unit(79);
          bottom: 0;
          left: 0;
          background: linear-gradient(0deg, #111111 0%, transparent 100%);
          z-index: 1;
        }

        &.scrolling::before {
          content: "";
          position: absolute;
          width: 100%;
          height: fn.percent-unit(79);
          top: 0;
          left: 0;
          background: linear-gradient(180deg, #111111 0%, transparent 100%);
          z-index: 1;
        }
      }

      &--empty {
        .sect-devices__subtitle {
          color: #9b9b9b;
        }
      }
    }

    .device-focus-box {
      width: fn.percent-unit(1124px);
      position: absolute;
      top: 0;
      left: fn.percent-unit(4);
      height: fn.percent-unit(158px);
      outline: fn.percent-unit(5px) solid pales.$white;
      pointer-events: none;
      border-radius: fn.percent-unit(4);
    }

    .device-focus-box-motion {
      border-radius: fn.percent-unit(4);
    }

    .device-wrapper {
      margin: 0 0 fn.percent-unit(12) 0;
    }

    .device-item {
      padding: fn.percent-unit(20) fn.percent-unit(46);
      display: flex;
      box-sizing: border-box;
      border: fn.percent-unit(2px) solid
        rgba($color: pales.$gray-de, $alpha: 0.5);
      border-radius: fn.percent-unit(4);
      display: flex;
      flex: 0 0 100%;
      align-items: center;
      position: relative;

      &--empty {
        border-color: transparent;
        padding: 0 0;
      }

      & * {
        box-sizing: border-box;
      }

      &.focus {
        outline: fn.percent-unit(5) solid pales.$white;
      }

      &__left {
        display: flex;
        align-items: center;
        flex: 0 0 auto;
        width: 47%;
        z-index: 10;

        &--empty {
          width: 100%;
        }
      }

      &__right {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 0 0 auto;
        width: 53%;
        border-left: solid 2px pales.$gray-33;
        padding-left: fn.percent-unit(32);
        z-index: 10;
      }

      &__icon {
        // width: fn.percent-unit(60px);
        // height: fn.percent-unit(50.32px);

        svg {
          overflow: overlay;

          path {
            fill: pales.$gray-9b;
            stroke: pales.$gray-9b;
          }
        }

        &.active {
          svg {
            overflow: overlay;

            path {
              fill: pales.$green-3a;
              stroke: pales.$green-3a;
            }
          }
        }
      }

      &.focused {
        border-color: pales.$white;
        .device-item__right {
          border-color: pales.$gray-de;
        }
        .sect-devices__subtitle {
          color: #111111;
        }
        .sect-devices__description {
          color: #454545;
        }
      }
    }

    .btn {
      font-weight: 500;
      font-size: fn.percent-unit(28px);
      line-height: 140%;
      letter-spacing: fn.percent-unit(0.3);
      white-space: nowrap;
      width: fit-content;
      color: pales.$white;
    }
  }

  // .nav-show {
  //   .sect-setting {
  //     .setting-inner {
  //       /*padding-left: 7.5em;
  //               @include translate(-19em, 0);*/
  //     }
  //   }
  // }

  .profile-empty {
    display: flex;
    justify-content: center;
    flex-direction: column;
    transition: all 0.27s linear;
    align-items: center;
    height: 80vh !important;

    .text {
      font-size: fn.percent-unit(28px);
      line-height: 1.5;
      color: #fff;
      text-align: center;
      margin-top: 0;
      margin-bottom: 0;

      & ~ .btn,
      & + .btn {
        margin-top: 1.5em;
      }
    }

    .profile-empty-img {
      display: block;
      height: fn.percent-unit(400px);
      width: auto;
      margin-bottom: fn.percent-unit(40px);

      & img {
        display: block;
        height: 100%;
        margin: auto;
      }

      .icon {
        &.icon-profile-empty {
          width: 18.625em;
          height: 18.625em;
          display: inline-block;

          .vie {
            color: #5f5f5f;
            font-size: 18.625em;
          }
        }
      }
    }
  }

  .sect-kids {
    display: flex;

    .profile-selector-list {
      display: flex;
      flex-direction: column;
      gap: fn.percent-unit(10px);
      width: fn.percent-unit(392px);
      color: white;

      .profile-item {
        height: fn.percent-unit(89px);
        display: flex;
        box-sizing: border-box;

        &.focus {
          border-radius: 4px;
          background: white;
          color: #333333 !important;
        }

        &.active {
          &::before {
            content: "";
            width: fn.percent-unit(8px);
            border-top-right-radius: fn.percent-unit(8px);
            border-bottom-right-radius: fn.percent-unit(8px);
            background-color: #3ac882;
          }
          border-radius: 4px;
          color: #dedede;
          box-sizing: border-box;
        }

        &--container {
          padding: 0px fn.percent-unit(20px);
          position: relative;
          display: flex;
          align-items: center;

          .icon {
            height: fn.percent-unit(56px);
            width: fn.percent-unit(56px);
            position: relative;

            .avatar {
              width: 100%;
              height: 100%;
              border-radius: fn.percent-unit(50%);
            }

            .kid-icon {
              position: absolute;
              top: 0;
              left: auto;
              right: 0;
              width: fn.percent-unit(15px) !important;
              height: fn.percent-unit(15px) !important;
              transform: none;
              z-index: 1;
            }
          }

          .title {
            display: -webkit-box;
            margin-left: fn.percent-unit(10px);
            font-weight: 400;
            font-size: fn.percent-unit(28px);
            line-height: 150%;
            text-overflow: ellipsis;
            overflow: hidden;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            word-break: break-all;
          }
        }
      }
    }

    .profile-report-title {
      font-size: fn.percent-unit(36px);
      font-weight: 500;
      text-align: center;
      color: #dedede;
      margin-top: fn.percent-unit(24px);
      margin-bottom: fn.percent-unit(16px);
    }

    .kid-profile-empty {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: fn.percent-unit(36px);
      color: #6c6c6c;
      line-height: fn.percent-unit(48px);
    }

    .tab-title-group {
      display: flex;
      justify-content: center;

      .tab-title {
        font-size: fn.percent-unit(28px);
        font-weight: 700;
        line-height: fn.percent-unit(42px);
        color: #9b9b9b;
        padding: fn.percent-unit(12px) fn.percent-unit(20px);

        &:nth-child(1) {
          margin-right: 20px;
        }

        &.focus {
          background: white;
          color: #222222;
        }

        &.active {
          color: #dedede;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            bottom: 0;
            left: fn.percent-unit(20px);
            height: fn.percent-unit(8px);
            width: calc(100% - #{fn.percent-unit(40px)});
            border-top-left-radius: fn.percent-unit(8px);
            border-top-right-radius: fn.percent-unit(8px);
            background-color: #3ac882;
            box-sizing: content-box;
          }
        }

        &.disable {
          color: #454545;
        }
      }
    }

    .tab-content-group {
      display: flex;
      margin-top: fn.percent-unit(16px);
      width: 100%;
      height: fn.percent-unit(655px);
      position: relative;

      .kid-report-empty {
        .empty__image {
          height: fn.percent-unit(356px);

          img {
            height: 100% !important;
          }
        }
      }

      .tab-time-watched {
        margin: fn.percent-unit(24px) auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: fn.percent-unit(808px);
        height: fn.percent-unit(608px);

        .row-custom {
          display: flex;
          font-size: fn.percent-unit(28px);
          line-height: fn.percent-unit(42px);
          font-weight: 400;
          padding: fn.percent-unit(12px);
          box-sizing: border-box;
          width: 100%;
        }

        &--first-row {
          @extend .row-custom;
        }

        &--row {
          @extend .row-custom;
          margin-bottom: fn.percent-unit(10px);
        }

        &--row {
          &:nth-child(even) {
            background: #222222;
          }
        }

        &--title {
          width: fn.percent-unit(444px);
        }

        &--description {
          margin-left: fn.percent-unit(48px);

          &.not-active {
            color: #9b9b9b;
          }
        }

        &--divider {
          margin: fn.percent-unit(12px) 0px;
          border: 1px solid #646464;
          width: 100%;
        }
      }

      .kids-manager-scroll {
        width: 100%;
        position: relative;

        //&::before {
        //  content: "";
        //  position: absolute;
        //  width: 100%;
        //  height: fn.percent-unit(7px);
        //  top: fn.percent-unit(-5px);
        //  left: 0;
        //  background: #000;
        //  z-index: 10;
        //}
        .tab-content-watched {
          margin: fn.percent-unit(24px) auto;
          display: flex;
          flex-direction: column;
          align-items: center;
          width: fn.percent-unit(1172px);
          height: fn.percent-unit(600px);
          overflow: hidden;

          &--scrolled {
            padding-top: fn.percent-unit(65px);
            height: fn.percent-unit(550px) !important;

            &::before {
              content: "";
              position: absolute;
              width: 100%;
              height: fn.percent-unit(65px);
              top: 0;
              left: 0;
              background: linear-gradient(
                to bottom,
                #111111 0%,
                transparent 100%
              );
              z-index: 10;
            }
          }

          &--scroll {
            &::after {
              content: "";
              position: absolute;
              width: 100%;
              height: fn.percent-unit(80px);
              bottom: -1px;
              left: 0;
              background: linear-gradient(0deg, #111111 0%, transparent 100%);
              z-index: 1;
            }

            &.scrolling::before {
              content: "";
              position: absolute;
              width: 100%;
              height: fn.percent-unit(80px);
              top: 0;
              left: 0;
              background: linear-gradient(180deg, #111111 0%, transparent 100%);
              z-index: 1;
            }
          }

          &--row {
            display: flex;
            font-size: fn.percent-unit(24px);
            line-height: fn.percent-unit(36px);
            font-weight: 400;
            box-sizing: border-box;
            align-items: center;
            width: 100%;
            height: fn.percent-unit(80px);
            margin-bottom: fn.percent-unit(12px);
            color: #dedede;
            border-radius: fn.percent-unit(4px);
          }

          &--row {
            &:nth-child(even) {
              background: #222222;
            }

            &.active {
              background: white;
              color: black;
            }
          }

          &--title {
            width: fn.percent-unit(760px);
            padding: 0px fn.percent-unit(12px);
            gap: fn.percent-unit(10px);
          }

          &--description {
            display: flex;
            justify-content: center;
            flex-direction: column;
            padding-right: fn.percent-unit(16px);
            box-sizing: border-box;
          }

          &--divider {
            margin: 0 fn.percent-unit(20px);
            height: fn.percent-unit(60px);
            border-right: 1px solid #646464;
            width: 1px;
          }
        }
      }
    }

    .profile-report {
      color: white;
      margin-left: fn.percent-unit(19px);
      width: fn.percent-unit(1228px);
      height: fn.percent-unit(824px);
      border-radius: 4px;
      border: 2px solid #333333;
      box-sizing: border-box;
    }
  }
}

.popup-setting {
  &__desc {
    font-size: fn.percent-unit(45px);
    text-align: center;
  }

  .popup__content {
    width: 100%;
    height: 100%;
    display: flex;
    align-content: center;
    justify-content: center;
    flex-direction: column;
    background: #000000;
    color: #ffffff;
  }

  .popup__button {
    display: flex;
    flex-direction: row;
    justify-content: center;

    .btn {
      margin-right: fn.percent-unit(15px);
      min-width: fn.percent-unit(150px);
    }
  }
}
