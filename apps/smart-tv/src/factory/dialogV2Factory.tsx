import React, { useEffect } from "react";
import ReactDOM from "react-dom";
import { Router } from "react-router-dom";
import { Provider } from "react-redux";
import styled from "styled-components";
import store from "app/store/store";
import history from "services/history";
import {
  FocusContext,
  FocusTypeContext,
  useFocusable,
  VieOnNavigation,
  VieOnNavigationDialog,
} from "core/KeyHandle";
import keyService from "services/keyService";

const Dialog = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  pointer-events: none;
`;

export interface DialogFactoryReturnType {
  close: () => void;
  update: any;
  destroy: () => void;
}

const FOCUS_KEY_DIALOG = "VN:DIALOG_WRAPPER";
const wrapperId = "dialog-wrapper";

type PopupBaseProps = {
  visible: boolean;
  close: () => void;
  afterClose: () => void;
  onOpen?: () => void;
  dialogParent?: HTMLElement;
};

type DialogConfig<T extends PopupBaseProps> = Omit<
  T,
  "visible" | "close" | "afterClose"
>;

type PopupUpdate<T extends PopupBaseProps> = T | ((prevConfig: T) => T);

function keyHandler() {
  // Block old keyhandle
}

function _dialogFactory<
  T extends {
    visible: boolean;
    close: () => void;
    afterClose: () => void;
    onOpen?: () => void;
    keyName?: string;
    dialogParent?: HTMLElement;
  }
>(Popup: React.FC<T>) {
  return (config: DialogConfig<T>) => {
    const div = document.createElement("div");
    const dialogWrapper =
      config?.dialogParent || document.getElementById(wrapperId);
    const container = dialogWrapper || document.body;
    if (container) {
      container.appendChild(div);
    }
    let currentConfig: T = {
      ...config,
      close,
      visible: true,
      afterClose: destroy,
    } as any;

    function destroy() {
      const unmountResult = ReactDOM.unmountComponentAtNode(div);
      if (unmountResult && div.parentNode) {
        div.parentNode.removeChild(div);
      }
    }

    function render({ ...props }: T) {
      div.setAttribute("data-dialog-keyName", props.keyName || "");
      ReactDOM.render(
        <FocusTypeContext.Provider value="dialog">
          <FocusContext.Provider value={FOCUS_KEY_DIALOG}>
            <Provider store={store}>
              <Router history={history}>
                <Popup {...props} />
              </Router>
            </Provider>
          </FocusContext.Provider>
        </FocusTypeContext.Provider>,
        div
      );
    }

    function close() {
      currentConfig = {
        ...currentConfig,
        visible: false,
      };
      render(currentConfig);
    }

    function update(configUpdate: PopupUpdate<T>) {
      if (typeof configUpdate === "function") {
        currentConfig = configUpdate(currentConfig);
      } else {
        currentConfig = {
          ...currentConfig,
          ...configUpdate,
        };
      }
      render(currentConfig);
    }

    render(currentConfig);
    return {
      close,
      update,
      destroy,
    };
  };
}

const DialogWrapper: React.FC = () => {
  const { ref, focusSelf, hasFocusedChild } = useFocusable({
    focusKey: FOCUS_KEY_DIALOG,
    trackChildren: true,
  });

  useEffect(() => {
    focusSelf();
  }, [focusSelf]);

  useEffect(() => {
    // Switch navigation service
    if (hasFocusedChild) {
      // Block old key handle
      keyService.getInstance().pushHandle(0, keyHandler);

      VieOnNavigation.block();
      VieOnNavigationDialog.unblock();
    } else {
      // Release old key handle
      keyService.getInstance().popHandle(0, keyHandler);

      VieOnNavigation.unblock();
      VieOnNavigationDialog.block();
    }
  }, [hasFocusedChild]);

  return (
    <Dialog
      ref={ref}
      id={wrapperId}
      style={{ pointerEvents: hasFocusedChild ? "auto" : "none" }}
    />
  );
};

export const DialogContainer: React.FC = () => {
  return (
    <FocusTypeContext.Provider value="dialog">
      <DialogWrapper />
    </FocusTypeContext.Provider>
  );
};

export default _dialogFactory;
