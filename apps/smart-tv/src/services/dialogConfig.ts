import { merge } from "lodash";
import get from "lodash/get";

class DialogConfig {
  private static instance: DialogConfig;

  config: { [key: string]: any } = {};

  private constructor() {
    this.config = {};
  }

  public static getInstance(): DialogConfig {
    if (!DialogConfig.instance) {
      DialogConfig.instance = new DialogConfig();
    }
    return DialogConfig.instance;
  }

  public add = (configObj: { [key: string]: string } = {}) => {
    return merge(this.config, configObj);
  };

  public get = (key: string) => {
    return get(this.config, key, "");
  };
}

export default DialogConfig.getInstance();
