import axios, { AxiosError, Axios<PERSON>romise, AxiosRequestConfig, AxiosResponse } from "axios";
import { cacheAdapterEnhancer, ICacheLike } from "axios-extensions";
import LRUCache from "lru-cache";
import get from "lodash/get";
import dayjs from "dayjs";
import store, { RootState } from "app/store/store";
import * as api from "services/endpoint";
import {
  API_DOMAIN,
  API_STATUS_CODE,
  BILLING_SERVICES_API_DOMAIN,
  CACHE_DATA_API,
  GAME_QUIZ_DOMAIN,
  NOTIFICATOR_DOMAINS,
  LIVE_COMMENT_DOMAIN,
  PROMOTION_SERVICES_API_DOMAIN,
  RECOMMEND_SERVICES_API_DOMAIN,
  ROUTES,
  USER_SERVICES_API_DOMAIN,
  VOTING_SERVICES_API_DOMAIN,
  WHITE_LIST_MULTI_PROFILE,
  POLICY_SERVICES_API_DOMAIN
} from "app/utils/constants";
import {
  appUnderConstruction,
  bannedAccount,
  loginAnonymousSuccess,
  logout,
  refreshTokenSuccess,
  removeCurrentProfile,
  toggleReadyCallDialog
} from "app/store/actions";
import platform from "services/platform";
import history from "services/history";
import { FlowDelAccountType } from "app/containers/FlowDelAccount/types";
import { authenticationRefreshToken } from "app/endpoint";
import { AccountModel } from "app/models";
import { showDialogGlobalCountryStopService } from "app/utils/showDialogCommon";
import { EnumSentryErrorType, EnumSentryLevelType, SentryTagAPIType } from "../types/common";
import SentryManager, { getSentryAPIMessage } from "../app/utils/SentryManager";
import { FormatYYYYMMDD } from "../constants/dateformat";
import dialogRoutines from "../app/routines/dialog";

type Env = "product" | "staging" | "testing" | "dev";

export const cacheMemory = new LRUCache({
  maxAge: CACHE_DATA_API * 60 * 1000,
  max: 50
}) as ICacheLike<AxiosPromise<any>>;

export const clearCache = () => {
  const cache: any = cacheMemory;
  if (cache && cache.reset && typeof cache.reset === "function") {
    cache.reset();
  }
};

// UPDATE Cache paramater: time -> minutes
export const updateCache = (time: number) => {
  const cache: any = cacheMemory;
  if (cache) {
    cache.maxAge = time * 60 * 1000;
  }

  if (cache && cache.forEach && typeof cache.forEach === "function") {
    cache.forEach((value: any, key: any, cache: any) => {
      cache.set(key, value, time * 60 * 1000);
    });
  }
};

export const removeCache = (keyRemove: string) => {
  const cache: any = cacheMemory;
  if (cache && cache.forEach && typeof cache.forEach === "function") {
    cache.forEach((value: any, key: any, cache: any) => {
      if (key && key.indexOf(keyRemove) !== -1) {
        cache.del(key);
      }
    });
  }
};

export const ENV = (localStorage.getItem("api_env") ||
  (process.env.REACT_APP_DEFAULT_API_ENV && process.env.REACT_APP_DEFAULT_API_ENV === "production"
    ? "product"
    : process.env.REACT_APP_DEFAULT_API_ENV) ||
  "product") as Env;
const API_ROOT = API_DOMAIN || "https://api.vieon.vn/backend/";
const API_SERVICES_USER = USER_SERVICES_API_DOMAIN || "https://api.vieon.vn/backend/user/";
const API_SERVICES_BILLING = BILLING_SERVICES_API_DOMAIN || "https://api.vieon.vn/backend/billing/";
const API_PROMOTION_SERVICES = PROMOTION_SERVICES_API_DOMAIN;
const API_SERVICES_LIVE_COMMENT = LIVE_COMMENT_DOMAIN || "https://live-comment-api.vieon.vn/";
const VOTING_SERVICES_API = VOTING_SERVICES_API_DOMAIN || "https://dev-game.vie-api.com/voting/";
const RECOMMEND_SERVICES_API = RECOMMEND_SERVICES_API_DOMAIN || "https://dev-recommend-engine.vieon.vn/";
const axiosInstance = axios.create({
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json"
  },
  adapter: cacheAdapterEnhancer(axios.defaults.adapter!, {
    enabledByDefault: false,
    defaultCache: cacheMemory
  }),
  timeout: 10000
});

axiosInstance.interceptors.request.use((config) => {
  // Add token to header
  const configHeaders = { ...(config.headers || {}) };
  const urlNotAddTokenList = [{ url: "cm/v5/geo-check", method: "GET" }];
  const gotAuthenHeader = !!get(config, "headers.Authorization", "");
  const isAddToken =
    urlNotAddTokenList.filter((request) => {
      return (
        config?.url?.includes?.(request.url) && config?.method?.toLocaleLowerCase() === request.method.toLowerCase()
      );
    }).length === 0;
  if (!gotAuthenHeader && isAddToken) {
    const state = store.getState();
    const token = get(state, "app.token", "");
    configHeaders.Authorization = `${token || ""}`;
    config.headers = { ...configHeaders };
  }

  // Add Profile-Token
  const state = store.getState();
  const currentProfile = get(state, "app.currentProfile", {});
  if (currentProfile) {
    const profileToken = get(currentProfile, "accessToken", "");
    (configHeaders.common as any)["Profile-Token"] = profileToken;
    config.headers = { ...configHeaders };
  }

  // Add platform params
  const params = {
    platform: platform.platformName,
    ui: "012021",
    app_version: platform.appVersion,
    ...config.params
  };
  config.params = params;
  return config;
});

axiosInstance.interceptors.response.use(
  (v) => v,
  (error) => {
    if (error.config && error.response && error.response.status === API_STATUS_CODE.TOKEN_INVALID) {
      const state = store.getState();
      const isAuthen = get(state, "app.isAuthen", false);
      if (!isAuthen) {
        const retryCounter = get(error, ["config", "retryCounter"], 0);
        if (!retryCounter) {
          return updateToken().then((token) => {
            store.dispatch(loginAnonymousSuccess({ token }));
            error.config.headers.Authorization = token;
            error.config.retryCounter = 1;
            return axiosInstance.request(error.config);
          });
        }
      } else {
        const token = state.app.token;
        const profile = state.app.profile;
        trackingSentry(token, profile, error.response);
        store.dispatch(logout());
        setTimeout(() => {
          const location = {
            pathname: "/"
          };
          history.replace(location);
        });
      }
    }
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (v) => v,
  async (error) => {
    if (error.config && error.response && error.response.status === API_STATUS_CODE.DEVICE_INVALID) {
      const state = store.getState();
      const token = state.app.token;
      const profile = state.app.profile;
      trackingSentry(token, profile, error.response);
      store.dispatch(logout());

      setTimeout(() => {
        const location = {
          pathname: "/"
        };
        history.replace(location);

        setTimeout(async () => {
          const state = store.getState();
          const readyCallDialog = get(state, "app.readyCallDialog", true);

          if (readyCallDialog) {
            const toggleCallDialog = () => {
              store.dispatch(toggleReadyCallDialog());
            };
            await toggleCallDialog();
            dialogRoutines.showDeviceWasLogoutDialog(toggleCallDialog);
          }
        }, 1000);
      });
    }
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (v) => v,
  (error) => {
    if (error.config && error.response && error.response.status === API_STATUS_CODE.PROFILE_TOKEN_INVALID) {
      store.dispatch(
        removeCurrentProfile({
          apiStatusCode: API_STATUS_CODE.PROFILE_TOKEN_INVALID
        })
      );
    }
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (v) => v,
  (error) => {
    if (error.config && error.response && error.response.status === API_STATUS_CODE.PROFILE_IS_DELETED) {
      store.dispatch(
        removeCurrentProfile({
          apiStatusCode: API_STATUS_CODE.PROFILE_IS_DELETED
        })
      );
    }
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (v) => v,
  (error) => {
    if (error.config && error.response && error.response.status === API_STATUS_CODE.TOKEN_EXPIRED) {
      const state = store.getState() as RootState;
      const isAuthen = state?.app?.isAuthen; // get(state, "app.isAuthen", false);
      const token = state?.app?.token;
      let refreshToken = state?.app?.refreshToken || "";
      const profileToken = state?.app?.currentProfile?.accessToken || "";
      if (isAuthen) {
        const { vieONDeviceID, model } = platform;
        const retryCounter = get(error, ["config", "retryCounter"], 0);
        if (!retryCounter) {
          // if (refreshToken === "") {
          //   return api
          //     .refreshToken({
          //       device_id: vieONDeviceID,
          //       model,
          //       device_name: platform.manufacturer,
          //     })
          //     .then((resp) => {
          //       const token = get(resp, "access_token");
          //       const refreshToken = get(resp, "refreshToken");
          //       const profileToken = get(resp, "profileToken");
          //       store.dispatch(
          //         refreshTokenSuccess({ token, refreshToken, profileToken })
          //       );
          //       error.config.headers.Authorization = token;
          //       error.config.headers["Profile-Token"] = profileToken;
          //       error.config.retryCounter = 1;
          //       return axiosInstance.request(error.config);
          //     });
          // }
          if (refreshToken === "") refreshToken = token;
          return authenticationRefreshToken(refreshToken, token, profileToken).then((resp) => {
            const token = resp.result.accessToken;
            const refreshToken = resp?.result?.refreshToken;
            const profileToken = resp?.result?.profileToken;
            localStorage.setItem("token", `user-${token}`);
            localStorage.setItem("refreshToken", `${refreshToken}`);
            store.dispatch(refreshTokenSuccess({ token, refreshToken, profileToken }));
            error.config.headers.Authorization = token;
            error.config.headers["Profile-Token"] = profileToken;
            error.config.retryCounter = 1;
            return axiosInstance.request(error.config);
          });
        }
      }
    }
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (v) => v,
  (error) => {
    if (error.config && error.response && error.response.status === API_STATUS_CODE.CONFLICT) {
      const state = store.getState();
      const token = state.app.token;
      const profile = state.app.profile;
      trackingSentry(token, profile, error.response);
      store.dispatch(logout());
      setTimeout(() => {
        const location = {
          pathname: "/"
        };
        history.push(location);
      });
      setTimeout(() => {
        const location = {
          pathname: `${ROUTES.AUTH_DELETE_ACCOUNT}/${FlowDelAccountType.RequestRemoveAccount}`
        };
        history.replace(location);
      }, 200);
    }
    return Promise.reject(error);
  }
);

const SENTRY_LOG_NUMBER = 0;
const StorageKey = "apisSentryLogInfo";
function handleSentryLogs(ROOT: string, error: AxiosError) {
  const { config, response } = error;
  if (config && response) {
    const apisSentryLogInfo = JSON.parse(sessionStorage.getItem(StorageKey) as string) || {};

    const {
      status,
      data,
      config: { method, url = "" }
    } = response;
    const effectTrackingServices = [API_SERVICES_USER, API_SERVICES_BILLING];

    const effectTrackingApis = [`${API_DOMAIN}${api.apiEndpoint.rootApi.getMenu}`];

    const getTitleByReplacePrefixAPI = () => {
      let newTitle = config?.url?.replace(API_DOMAIN, "") ?? "";
      effectTrackingServices.forEach((ser) => {
        newTitle = newTitle.replace(ser, "");
      });
      return newTitle;
    };

    if (effectTrackingApis.indexOf(url as string) > -1 || effectTrackingServices.includes(ROOT)) {
      const apiEndpoint = url.replace(/^.*\/\/[^\/]+/, "");

      const prevStorageInfo = apisSentryLogInfo[apiEndpoint];
      let canSendSentryLog = true;
      let canStorageInfo = true;
      const newStorageInfo = {
        date: dayjs().format(FormatYYYYMMDD),
        count: 1
      };

      if (prevStorageInfo) {
        if (prevStorageInfo.date === dayjs().format(FormatYYYYMMDD)) {
          if (prevStorageInfo.count <= SENTRY_LOG_NUMBER) {
            newStorageInfo.count = prevStorageInfo.count + 1;
          } else {
            canSendSentryLog = false;
            canStorageInfo = false;
          }
        }
      }

      // Executed Store Info by URL
      if (canStorageInfo) {
        apisSentryLogInfo[apiEndpoint] = newStorageInfo;
        sessionStorage.setItem(StorageKey, JSON.stringify(apisSentryLogInfo));
      }

      // Executed Sentry Log
      if (canSendSentryLog) {
        const titleApi = getTitleByReplacePrefixAPI();
        const message = getSentryAPIMessage({
          errorType: ROOT === API_SERVICES_BILLING ? EnumSentryErrorType.Payment : EnumSentryErrorType.API,
          method,
          title: titleApi
        });
        const tag: SentryTagAPIType = {
          environment: process.env.REACT_APP_SENTRY_ENV as string,
          level: EnumSentryLevelType.Error,
          errorType: EnumSentryErrorType.API
        };
        const extra = {
          method,
          errorCode: status,
          errorMessage: data
        };
        SentryManager.captureEvent(message, null, tag, extra);
      }
    }
  }
}

const updateToken = async () => {
  const response = await api.loginAnonymous();
  const token = get(response, "access_token", "") as string;
  return token;
};

const trackingSentry = (token: string, userProfile: AccountModel | null, error?: any) => {
  const user = {
    token,
    ...userProfile
  };
  const tag: SentryTagAPIType = {
    environment: process.env.REACT_APP_SENTRY_ENV as string,
    level: EnumSentryLevelType.Error,
    errorType: EnumSentryErrorType.API,
    phone: userProfile?.mobile ?? "",
    email: userProfile?.email ?? ""
  };
  const extra = {
    error: JSON.stringify(error)
  };
  SentryManager.captureEvent("ACCOUNT_AUTO_LOGOUT", user, tag, extra);
};

const _callApiFactory = (ROOT: string, bypassErrorCodeHandler: boolean = false) => {
  return <T>(options: AxiosRequestConfig, getErrorMsg?: any, returnFullResponseError: boolean = false) => {
    if (options.url) {
      options.url = options.url.indexOf("https://") === -1 ? ROOT + options.url : options.url;
    }

    return axiosInstance.request(options).then(
      (response: AxiosResponse<T>) => {
        return response.data;
      },
      (error) => {
        const url = get(error, "config.url", "");
        const status = get(error, ["response", "status"]);
        // Sentry Logs
        handleSentryLogs(ROOT, error);

        if (!bypassErrorCodeHandler) {
          if (status === API_STATUS_CODE.BANNED_ACCOUNT) {
            store.dispatch(bannedAccount());
          }
          if (status === API_STATUS_CODE.UNDER_CONSTRUCTION) {
            const errorMsg = get(error, ["response", "data", "message"], "") || "";
            store.dispatch(appUnderConstruction({ errorMsg }));
          }
        }
        // if (status === 401 && unauthenHandler) {
        //   const path = window.location.pathname;
        //   const searchParams = new URLSearchParams();
        //   searchParams.append("redirect", path);
        //   const location = {
        //     pathname: '/login/step-0',
        //     search: searchParams.toString(),
        //   }
        //   history.push(location);
        // }
        if (returnFullResponseError || url.indexOf("promotion-codes") !== -1) {
          throw error.response;
        } else if (status === API_STATUS_CODE.DEVICE_INVALID) {
          throw error.response;
        } else if (status === API_STATUS_CODE.GLOBAL_COUNTRY_LIMIT) {
          // TODO for Global Country Stop Serivce
          // API_STATUS_CODE.GLOBAL_COUNTRY_LIMIT === 555 with any api as equa Restrict Stop servive at Country
          showDialogGlobalCountryStopService();
          throw error.response;
        } else if (
          [API_STATUS_CODE.COUNT_EXPIRED, API_STATUS_CODE.NOT_FOUND, API_STATUS_CODE.BAD_REQUEST].includes(status) &&
          url.indexOf(WHITE_LIST_MULTI_PROFILE) >= 0
        ) {
          throw error.response;
        } else if (getErrorMsg && typeof getErrorMsg === "function") {
          throw new Error(getErrorMsg(error));
        } else {
          throw new Error(get(error, ["response", "data", "message"]) || error.message || "Unexpected");
        }
      }
    );
  };
};

export default _callApiFactory(API_ROOT);
export const callApiServiceUser = _callApiFactory(API_SERVICES_USER);
export const callApiServiceBilling = _callApiFactory(API_SERVICES_BILLING, true);
export const callApiPromotionService = _callApiFactory(API_PROMOTION_SERVICES, true);
export const callApiServiceLiveComment = _callApiFactory(API_SERVICES_LIVE_COMMENT, true);
export const callApiGameQuiz = _callApiFactory(GAME_QUIZ_DOMAIN, true);
export const callApiNotificator = _callApiFactory(NOTIFICATOR_DOMAINS, true);
export const callApiServicePolicy = _callApiFactory(POLICY_SERVICES_API_DOMAIN, true);
export const callApiServiceVoting = _callApiFactory(VOTING_SERVICES_API, true);

export const callApiServiceRecommend = _callApiFactory(RECOMMEND_SERVICES_API, true);

export function callApiNotDomain(options: AxiosRequestConfig) {
  const axiosInstance = axios.create({
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json"
    },
    adapter: cacheAdapterEnhancer(axios.defaults.adapter!, {
      enabledByDefault: false
    }),
    timeout: 10000
  });

  return axiosInstance.request(options).then(
    (response: AxiosResponse<any>) => {
      return response;
    },
    (error) => {
      return error.response;
    }
  );
}
