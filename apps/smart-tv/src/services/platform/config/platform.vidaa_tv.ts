import SentryManager from "app/utils/SentryManager";
import { ENV, PLATFORM } from "app/utils/constants";
import icFullsizePng from "assets/images/logo.png";
import icFullsize from "assets/images/logo.svg";
import { callApiServiceBilling } from "services/api";

declare global {
  interface Window {
    vidaatv?: any;
    Hisense_GetSupportForDolbyAtmos?: any;
    SigmaPacker?: any;
    sigmaPacker?: any;
  }
}
function init() {
  let trackingVieONPath: string;
  switch (ENV) {
    case "product":
      trackingVieONPath = "/topics/vieon-tracking-vidaa";
      break;
    case "staging":
      trackingVieONPath = "/topics/vieon-tracking-vidaa-staging";
      break;
    case "testing":
      trackingVieONPath = "/topics/vieon-tracking-vidaa-testing";
      break;
    default:
      trackingVieONPath = "/topics/vieon-tracking-vidaa-dev";
      break;
  }

  const getSideLogo = () => {
    const browserRegex =
      /(MSIE|Trident|(?!Gecko.+)Firefox|(?!AppleWebKit.+Chrome.+)Safari(?!.+Edge)|(?!AppleWebKit.+)Chrome(?!.+Edge)|(?!AppleWebKit.+Chrome.+Safari.+)Edge|AppleWebKit(?!.+Chrome|.+Safari)|Gecko(?!.+Firefox))(?: |\/)([\d.apre]+)/.exec(
        window.navigator.userAgent,
      );
    let sideLogo = icFullsize;
    if (browserRegex) {
      const browserMainVersion = parseInt(browserRegex[2].split(".")[0], 10);
      if (browserMainVersion < 41) {
        sideLogo = icFullsizePng;
      }
    }
    return sideLogo;
  };

  function loadScripts(
    scripts: string | any[],
    callback: { (): void; (): void },
  ) {
    let loadedScripts = 0;

    function scriptLoaded() {
      loadedScripts += 1;
      if (loadedScripts === scripts.length) {
        callback();
      }
    }

    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < scripts.length; i++) {
      const script = document.createElement("script");
      script.type = "text/javascript";
      script.async = true;
      script.src = scripts[i];
      script.onload = scriptLoaded;
      document.getElementsByTagName("head")[0].appendChild(script);
    }
  }
  const initSigmaPacker = () => {
    if (typeof window.SigmaPacker === "function" && !window.sigmaPacker) {
      window.sigmaPacker = new window.SigmaPacker();
      window.sigmaPacker.onload = () => {
        console.log("SigmaPacker: Initialized");
      };
      console.log("SigmaPacker: Initializing");
      window.sigmaPacker.init();

      SentryManager.init();
    }
  };
  const nonGameQR = `https://static.vieon.vn/vieon-images/rapviet/qrcode_download-app_lg.png?v=${new Date().getTime()}`;

  // @ts-ignore
  return {
    appVersion: process.env.REACT_APP_VERSION || "master",
    limitItemRibbon: 15,
    platformName: PLATFORM.VIDAA,
    fullPlatformName: "Vidaa",
    brand: "hisense",
    onPromotion: "",
    onAutoPromotion: "",
    model: "",
    vieONDeviceID: "",
    sideLogo: getSideLogo(),
    version: "",
    firmwareVersion: "",
    yearOfManufacture: 2030,
    disableTransition: false,
    disableGame: process.env.REACT_APP_DISABLE_GAME || false,
    nonGameQR,
    trackingGAID: "G-3B4G9CEQZ7", // id for vidaa
    trackingSegmentID: "Q2VWT0twYnBDY0E1am8xd0hVQ09VNlZtdVpMODJEa0I", // id for vidaa
    trackingVieONPath,
    trackingSegmentUrl: "https://vieon-tracking.vieon.vn",
    support4K: false, // check docs vidaa
    supportH265: false, // check docs vidaa
    supportAudio51: false, // check docs vidaa
    supportDolbyAtmos: false, // check docs vidaa
    manufacturer: "Vidaa",
    drmVersion: "UNKNOW",
    secLevel: 1,
    ip: "",
    forceKplus720: false,
    keys: {
      enter: 13,
      left: 37,
      up: 38,
      right: 39,
      down: 40,
      red: 403, // back-tick key on Mac
      green: 404,
      yellow: 405,
      blue: 406,
      // red: 33 // Page Up key on Windows,
      return: 8,
      rewind: 412,
      fastforward: 417,
      nexttrack: 425,
      prevtrack: 424,
      play: 415,
      pause: 19,
      stop: 413,
      channelUp: 427,
      channelDown: 428,
      exit: 10182,
      "0": 48,
      "1": 49,
      "2": 50,
      "3": 51,
      "4": 52,
      "5": 53,
      "6": 54,
      "7": 55,
      "8": 56,
      "9": 57,
    },
    exit() {
      window.close();
    },
    init(callback: any) {
      const me = this;
      loadScripts(
        [
          "https://tvmodules-vidaa.vidaahub.com/deviceapi/vidaatv.js",
          "https://static2.vieon.vn/production-vieon-vidaa/vidaa/static/js/sigma_packer-1.0.4.js",
        ],
        function () {
          initSigmaPacker();

          window.vidaatv
            .config({
              debug: false, // If debugging mode is enabled, necessary logs are printed
              client_id: "VIDAAOS", // your client_id in VIDAAOS
              apiList: [], // Plugins list that need to be used
            })
            .then(() => {
              // Plugin loaded success
            })
            .catch(() => {
              // Plugin loaded fail
            });

          // device_id
          try {
            if (typeof window.vidaatv.getDeviceID === "function") {
              const deviceId = window.vidaatv.getDeviceID(32);
              me.vieONDeviceID = deviceId;
            }
          } catch (e) {
            // error
          }

          // Model
          try {
            if (typeof window.vidaatv.getModelName === "function") {
              const modelName = window.vidaatv.getModelName();
              me.model = modelName;
            }
          } catch (e) {
            // error
          }

          // FirmWareVersion
          try {
            if (typeof window.vidaatv.getFirmWareVersion === "function") {
              const version = window.vidaatv.getFirmWareVersion();
              me.firmwareVersion = version;
            }
          } catch (e) {
            // error
          }

          // OS version
          try {
            if (typeof window.vidaatv.getOSVersion === "function") {
              const version = window.vidaatv.getOSVersion();
              me.version = version;
            }
          } catch (e) {
            // error
          }

          // 4K Suport
          try {
            if (typeof window.vidaatv.get4KState === "function") {
              const isSupported4K = window.vidaatv.get4KState();
              me.support4K = isSupported4K;
            }
          } catch (e) {
            // error
          }

          // H265 Support
          try {
            if (typeof window.vidaatv.getHDRStatus === "function") {
              const supportedHdr = window.vidaatv.getHDRStatus();
              if (supportedHdr.includes("HDR10")) {
                me.supportH265 = true;
              }
            }
          } catch (e) {
            // error
          }

          // DolbyAtmos Support
          try {
            if (typeof window.Hisense_GetSupportForDolbyAtmos === "function") {
              const isSupportedAtmos = window.Hisense_GetSupportForDolbyAtmos();
              me.supportDolbyAtmos = isSupportedAtmos;
            }
          } catch (e) {
            // error
          }
          const strListDevice =
            process.env.REACT_APP_LIST_DEVICE_FORCE_KPLUS_720;
          if (strListDevice) {
            const listDeviceForceKplus720: string[] = strListDevice.split(",");
            if (listDeviceForceKplus720.indexOf(me.vieONDeviceID) >= 0) {
              me.forceKplus720 = true;
            }
          }
          const countShowPromotion = parseInt(
            localStorage.getItem("countShowPromotion") || "0",
            10,
          );
          if (countShowPromotion >= 3) {
            callback(me);
          } else {
            callApiServiceBilling<{
              error_code: number;
              error_message: string;
              result?: {
                event_name: string;
                banner_type: string;
              };
            }>({
              url: `prom/tv/auto-active/permission`,
              method: "GET",
              headers: {
                "content-type": "application/json",
              },
              params: {
                device_model: me.model,
                device_uuid: me.vieONDeviceID,
                brand: me.brand,
              },
            })
              .then((res) => {
                const onAutoPromotion: string = res.result?.banner_type || "";
                if (onAutoPromotion) {
                  /*
                  Tivi thuộc dòng được khuyến mãi ?
                    -> Kiểm tra lần đầu mở app
                    -> Lưu localStorage
                */
                  const appVersion = localStorage.getItem("appVersion");
                  if (!appVersion) {
                    localStorage.setItem("onPromotion102020", "1");
                  }
                  const isFirstOpenApp =
                    localStorage.getItem("onPromotion102020");
                  if (isFirstOpenApp) {
                    me.onAutoPromotion = onAutoPromotion;
                  } else {
                    localStorage.setItem("countShowPromotion", "3");
                  }
                }
              })
              .catch(() => {
                // Do nothing
              });
            callback(me);
          }
        },
      );
    },
  };
}

export default init;
