import history from "services/history";
// import DisconnectNetworkPopup from "app/components/Common/DisconnectNetworkPopup";
import icFullsize from "assets/images/logo.svg";
import icFullsizePng from "assets/images/logo.png";
import { ENV, PLATFORM, ROUTES } from "app/utils/constants";
import { isSupportAudio51, isSupportCodecsH265 } from "app/utils/index";
import { makePathByDeepLink } from "app/utils/makePath";
import { callApiServiceBilling } from "../../api";

declare const webapis: any;
declare const tizen: any;
export default function init() {
  const model = webapis.productinfo.getRealModel();

  // Disable Transition for Samsung 2016
  let modelCode = "";
  let yearOfManufacture = 2030;
  let disableTransition = false;
  let support4K = false;
  const limitItemRibbon = 15;
  let manufacturer = "Tizen OS";
  try {
    modelCode = webapis.productinfo.getModelCode();
  } catch (error) {
    // error
  }
  try {
    // force to type any to access an undefined property called tizen
    const _window = window as any;
    const deviceInfo = _window?.tizen?.systeminfo;
    if (deviceInfo && typeof deviceInfo?.getCapability === "function") {
      manufacturer =
        deviceInfo?.getCapability("http://tizen.org/system/manufacturer") ||
        "Tizen OS";
    }
  } catch (error) {
    // error
  }
  const modelCodeRegex = /(^\d\d)_\w+/.exec(modelCode);
  if (modelCodeRegex) {
    yearOfManufacture = parseInt(modelCodeRegex[1], 10) + 2000;
  }
  if (yearOfManufacture && yearOfManufacture < 2017) {
    disableTransition = true;
  }
  try {
    if (webapis.productinfo.isUdPanelSupported()) {
      support4K = true;
    }
  } catch (error) {
    // error
  }

  // Svg logo use mix-blend-mode not work on Samsung 2016)
  let sideLogo = icFullsize;
  let disableLottie = false;
  if (yearOfManufacture && yearOfManufacture < 2017) {
    sideLogo = icFullsizePng;
    // Lottie animation not working properly on Samsung 2016
    disableLottie = true;
  }

  // Get DUID
  let duid = "";
  try {
    duid = webapis.productinfo.getDuid();
  } catch (error) {
    console.log("cannot get DUID");
  }

  if (typeof tizen.tvinputdevice.registerKey === "function") {
    [
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "MediaTrackNext",
      "MediaTrackPrevious",
      "MediaStop",
      "MediaPause",
      "MediaPlay",
      "MediaFastForward",
      "MediaRewind",
      "MediaPlayPause",
      "ColorF1Green",
      "ColorF0Red",
      "ColorF2Yellow",
      "ColorF3Blue",
      "ChannelUp",
      "ChannelDown",
    ].forEach(function (t) {
      tizen.tvinputdevice.registerKey(t);
    });
  }

  const deepLink = () => {
    // if (typeof tizen.application?.getCurrentApplication === "function") {
    //   return;
    // }

    const requestedAppControl = tizen.application
      .getCurrentApplication()
      .getRequestedAppControl();
    if (requestedAppControl) {
      const appControlData = requestedAppControl.appControl.data;
      for (const data of appControlData) {
        if (data.key === "PAYLOAD") {
          const { id, type, deep_link } = JSON.parse(
            JSON.parse(data.value[0]).values
          );
          if (deep_link && deep_link !== "") {
            if (history.length === 1) {
              window.history.pushState(null, "", "#/");
            }
            const location = makePathByDeepLink(deep_link);
            if (location) {
              history.push(location);
            }
          } else if (id) {
            if (history.length === 1) {
              window.history.pushState(null, "", "#/");
            }
            switch (type) {
              case "channel": {
                history.push({
                  pathname: `${ROUTES.LIVE_TV}/${id}`,
                  state: {
                    fromHomePreview: true,
                  },
                });
                break;
              }
              case "livestream": {
                history.push({
                  pathname: `${ROUTES.VIEW_STREAM}/${id}`,
                  state: {
                    fromHomePreview: true,
                  },
                });
                break;
              }
              default:
                history.push({
                  pathname: `${ROUTES.VIDEO_INTRO}/${id}`,
                  state: {
                    fromHomePreview: true,
                  },
                });
            }
          }
          break;
        }
      }
    }
  };
  window.addEventListener("appcontrol", deepLink);
  deepLink();

  const onNetworkStateChange = function (statusCode: number) {
    if (statusCode === 4) {
      // DisconnectNetworkPopup.close();
    } else if (statusCode === 5) {
      // DisconnectNetworkPopup.open();
    }
  };
  webapis.network.addNetworkStateChangeListener(onNetworkStateChange);
  const nonGameQR = `https://static.vieon.vn/vieon-images/rapviet/qrcode_download-app_samsung.png?v=${new Date().getTime()}`;
  let trackingVieONPath: string;
  switch (ENV) {
    case "product":
      trackingVieONPath = "/topics/vieon-tracking-tizen";
      break;
    case "staging":
      trackingVieONPath = "/topics/vieon-tracking-tizen-staging";
      break;
    case "testing":
      trackingVieONPath = "/topics/vieon-tracking-tizen-testing";
      break;
    default:
      trackingVieONPath = "/topics/vieon-tracking-tizen-dev";
      break;
  }

  const platformObject = {
    wifiMacAddress: "",
    lanMacAddress: "",
    appVersion: process.env.REACT_APP_VERSION || "master", // Lấy từ env: CICD lấy ra=tên branch release (v8.11.0), dev version là tên branch master
    limitItemRibbon,
    platformName: PLATFORM.TIZEN,
    fullPlatformName: "SamSung",
    yearOfManufacture,
    model,
    vieONDeviceID: duid,
    sideLogo,
    disableTransition,
    disableLottie,
    disableGame: process.env.REACT_APP_DISABLE_GAME || false,
    nonGameQR,
    trackingGAID: "UA-117114757-2",
    trackingSegmentID: "g3VBAAbOM2Z0SaO2mmhO0sLxU76mnVAk",
    trackingVieONPath,
    trackingSegmentUrl: "https://vieon-tracking.vieon.vn",
    support4K,
    supportH265: isSupportCodecsH265(),
    supportAudio51: isSupportAudio51(),
    version: "Default",
    manufacturer,
    drmVersion: "UNKNOW",
    secLevel: 1,
    ip: "",
    forceKplus720: false,
    keys: {
      enter: 13,
      left: 37,
      up: 38,
      right: 39,
      down: 40,
      red: 403, // back-tick key on Mac
      green: 404,
      yellow: 405,
      blue: 406,
      // red: 33 // Page Up key on Windows,
      return: 10009,
      exit: 10182,
      extra: 10253,
      playpause: 10252,
      fastforward: 417,
      rewind: 412,
      nexttrack: 425,
      prevtrack: 424,
      channelUp: 427,
      channelDown: 428,
      play: 415,
      pause: 19,
      stop: 413,
      "0": 48,
      "1": 49,
      "2": 50,
      "3": 51,
      "4": 52,
      "5": 53,
      "6": 54,
      "7": 55,
      "8": 56,
      "9": 57,
    },
    onPromotion: "",
    onAutoPromotion: "",
    autoPromotionExcludePkg: 0,
    exit() {
      if (typeof tizen.application?.getCurrentApplication === "function") {
        tizen.application.getCurrentApplication().exit();
      }
    },
    init(callback: any) {
      const countShowPromotion = parseInt(
        localStorage.getItem("countShowPromotion") || "0",
        10
      );
      if (countShowPromotion >= 3) {
        callback(this);
      } else {
        callApiServiceBilling<{
          error_code: number;
          error_message: string;
          result?: {
            event_name: string;
            banner_type: string;
          };
        }>({
          url: `prom/tv/auto-active/permission`,
          method: "GET",
          headers: {
            "content-type": "application/json",
          },
          params: {
            device_model: this.model,
            device_uuid: this.vieONDeviceID,
            brand: this.fullPlatformName,
          },
        })
          .then((jsonRes: any) => {
            const onAutoPromotion: string = jsonRes.result?.banner_type || "";
            const autoPromotionExcludePkg = 0;
            if (onAutoPromotion) {
              /*
              Tivi thuộc dòng được khuyến mãi ?
                -> Kiểm tra lần đầu mở app
                -> Lưu localStorage
            */
              const appVersion = localStorage.getItem("appVersion");
              if (!appVersion) {
                localStorage.setItem("onPromotion102020", "1");
              }
              const isFirstOpenApp = localStorage.getItem("onPromotion102020");
              if (isFirstOpenApp) {
                this.onAutoPromotion = onAutoPromotion;
                this.autoPromotionExcludePkg = autoPromotionExcludePkg;
              } else {
                localStorage.setItem("countShowPromotion", "3");
              }
            }
          })
          .catch(() => {})
          .finally(() => {
            const strListDevice =
              process.env.REACT_APP_LIST_DEVICE_FORCE_KPLUS_720;
            if (strListDevice) {
              const listDeviceForceKplus720: string[] =
                strListDevice.split(",");
              if (listDeviceForceKplus720.indexOf(this.vieONDeviceID) >= 0) {
                this.forceKplus720 = true;
              }
            }
            callback(this);
          });
      }
    },
  };

  // Get MacAddress
  const systemInfo = tizen.systeminfo;

  systemInfo.getPropertyValue("WIFI_NETWORK", (network: any) => {
    platformObject.wifiMacAddress = network.macAddress;
  });

  systemInfo.getPropertyValue("ETHERNET_NETWORK", (network: any) => {
    platformObject.lanMacAddress = network.macAddress;
  });

  return platformObject;
}
