import "@procot/webostv/webOSTV";
import { ENV, PLATFORM, ROUTES } from "app/utils/constants";
import {
  extractUrlValue,
  isSupportAudio51,
  isSupportCodecsH265,
} from "app/utils/index";
import icFullsizePng from "assets/images/logo.png";
import icFullsize from "assets/images/logo.svg";
import { callApiServiceBilling } from "services/api";
import history from "services/history";

declare const webOS: any;
declare global {
  interface Window {
    launchParams: string;
    PalmSystem: {
      launchParams: string;
    };
  }
}

function init() {
  const versionToYearMap: { [key: number]: number } = {
    1: 2014,
    2: 2015,
    3: 2017,
    4: 2019,
    5: 2020,
    6: 2021,
    7: 2022,
    8: 2023,
    9: 2024,
    10: 2025,
    11: 2026,
    12: 2027,
    13: 2028,
    14: 2029,
    15: 2030,
  };

  // Get side bar logo (svg logo use mix-blend-mode not work on chrome < 41)
  const browserRegex =
    /(MSIE|Trident|(?!Gecko.+)Firefox|(?!AppleWebKit.+Chrome.+)Safari(?!.+Edge)|(?!AppleWebKit.+)Chrome(?!.+Edge)|(?!AppleWebKit.+Chrome.+Safari.+)Edge|AppleWebKit(?!.+Chrome|.+Safari)|Gecko(?!.+Firefox))(?: |\/)([\d.apre]+)/.exec(
      window.navigator.userAgent
    );
  let sideLogo = icFullsize;
  if (browserRegex) {
    const browserMainVersion = parseInt(browserRegex[2].split(".")[0], 10);
    if (browserMainVersion < 41) {
      sideLogo = icFullsizePng;
    }
  }

  const deepLink = () => {
    const launchParams = window.launchParams
      ? window.launchParams
      : window.PalmSystem && window.PalmSystem.launchParams
      ? window.PalmSystem.launchParams
      : "";
    if (launchParams) {
      try {
        const { contentTarget = "" } = JSON.parse(launchParams);
        if (contentTarget) {
          const targets = contentTarget.split("?");
          const idTarget = targets[0];
          const type = extractUrlValue("type", `?${targets[1] || ""}`);
          if (history.length === 1) {
            window.history.pushState(null, "", "#/");
          }
          switch (type) {
            case "channel": {
              history.push({
                pathname: `${ROUTES.LIVE_TV}/${idTarget}`,
                state: {
                  fromHomePreview: true,
                },
              });
              break;
            }
            case "livestream": {
              history.push({
                pathname: `${ROUTES.VIEW_STREAM}/${idTarget}`,
                state: {
                  fromHomePreview: true,
                },
              });
              break;
            }
            default:
              history.push({
                pathname: `${ROUTES.VIDEO_INTRO}/${idTarget}`,
                state: {
                  fromHomePreview: true,
                },
              });
          }
        }
      } catch (e) {
        // error
      }
    }
  };
  document.addEventListener("webOSRelaunch", deepLink); // Trường hợp app đang mở, user nhấn chọn mở content trong Home Preview
  deepLink();

  const nonGameQR = `https://static.vieon.vn/vieon-images/rapviet/qrcode_download-app_lg.png?v=${new Date().getTime()}`;

  let trackingVieONPath: string;
  switch (ENV) {
    case "product":
      trackingVieONPath = "/topics/vieon-tracking-webos";
      break;
    case "staging":
      trackingVieONPath = "/topics/vieon-tracking-webos-staging";
      break;
    case "testing":
      trackingVieONPath = "/topics/vieon-tracking-webos-testing";
      break;
    default:
      trackingVieONPath = "/topics/vieon-tracking-webos-dev";
      break;
  }
  return {
    appVersion: process.env.REACT_APP_VERSION || "master", // Lấy từ env: CICD lấy ra=tên branch release (v8.11.0), dev version là tên branch master
    limitItemRibbon: 15,
    platformName: PLATFORM.LG,
    fullPlatformName: "LG",
    onPromotion: "",
    onAutoPromotion: "",
    model: "",
    vieONDeviceID: "",
    sideLogo,
    version: "",
    firmwareVersion: "",
    disableTransition: false,
    yearOfManufacture: 2030,
    disableGame: process.env.REACT_APP_DISABLE_GAME || false,
    nonGameQR,
    trackingGAID: "UA-117114757-7",
    trackingSegmentID: "CeVOKpbpCcA5jo1wHUCOU6VmuZL82DkB",
    trackingVieONPath,
    trackingSegmentUrl: "https://vieon-tracking.vieon.vn",
    support4K: false,
    supportH265: isSupportCodecsH265(),
    supportAudio51: isSupportAudio51(),
    webOSVersion: "",
    manufacturer: "LG",
    drmVersion: "UNKNOW",
    secLevel: 1,
    ip: "",
    forceKplus720: false,
    keys: {
      enter: 13,
      left: 37,
      up: 38,
      right: 39,
      down: 40,
      red: 403, // back-tick key on Mac
      green: 404,
      yellow: 405,
      blue: 406,
      // red: 33 // Page Up key on Windows,
      return: 461,
      rewind: 412,
      fastforward: 417,
      nexttrack: 425,
      prevtrack: 424,
      play: 415,
      pause: 19,
      stop: 413,
      channelUp: 33,
      channelDown: 34,
      exit: 10182,
      "0": 48,
      "1": 49,
      "2": 50,
      "3": 51,
      "4": 52,
      "5": 53,
      "6": 54,
      "7": 55,
      "8": 56,
      "9": 57,
    },
    exit() {
      window.close();
    },

    getPromotion() {
      const countShowPromotion = parseInt(
        localStorage.getItem("countShowPromotion") || "0",
        10
      );
      // Only show promotion three times
      if (countShowPromotion >= 3) {
        return "Exceed maximum promotion popup show";
      }

      callApiServiceBilling<{
        error_code: number;
        error_message: string;
        result?: {
          event_name: string;
          banner_type: string;
        };
      }>({
        url: `prom/tv/auto-active/permission`,
        method: "GET",
        headers: {
          "content-type": "application/json",
        },
        params: {
          device_model: this.model,
          device_uuid: this.vieONDeviceID,
          brand: this.fullPlatformName,
        },
      })
        .then((res) => {
          const onAutoPromotion: string = res.result?.banner_type || "";
          if (onAutoPromotion) {
            /*
                Tivi thuộc dòng được khuyến mãi ?
                  -> Kiểm tra lần đầu mở app
                  -> Lưu localStorage
              */
            const appVersion = localStorage.getItem("appVersion");
            if (!appVersion) {
              localStorage.setItem("onPromotion102020", "1");
            }
            const isFirstOpenApp = localStorage.getItem("onPromotion102020");
            if (isFirstOpenApp) {
              this.onAutoPromotion = onAutoPromotion;
            } else {
              localStorage.setItem("countShowPromotion", "3");
            }
          }
        })
        .catch(() => {
          // Do nothing
        });
    },
    init(callback: any) {
      const me = this;
      webOS.service.request("luna://com.webos.service.tv.systemproperty", {
        method: "getSystemInfo",
        parameters: {
          keys: ["modelName", "sdkVersion", "firmwareVersion", "UHD"],
        },
        onSuccess(inResponse: any) {
          me.model = inResponse.modelName;
          me.version = inResponse.sdkVersion;
          me.webOSVersion = `WebOS${inResponse.sdkVersion}`;
          me.firmwareVersion = inResponse.firmwareVersion;
          if (inResponse.UHD && inResponse.UHD === "true") {
            me.support4K = true;
          }
        },
        onFailure() {
          console.log("Failed to get TV device information");
        },
        onComplete() {
          const mainVersion = parseInt(me.version.split(".")[0], 10);
          if (mainVersion && mainVersion < 4) {
            me.disableTransition = true;
          }
          if (mainVersion) {
            me.yearOfManufacture = versionToYearMap[mainVersion];
          }
          webOS.service.request("luna://com.webos.service.sm", {
            method: "deviceid/getIDs",
            parameters: {
              idType: ["LGUDID"],
            },
            onSuccess(inResponse: any) {
              me.vieONDeviceID = inResponse.idList[0].idValue;
              // Surely has device_id and model
              me.getPromotion();
            },
            onFailure() {
              console.log("Failed to get TV device information");
            },
            onComplete() {
              const strListDevice =
                process.env.REACT_APP_LIST_DEVICE_FORCE_KPLUS_720;
              if (strListDevice) {
                const listDeviceForceKplus720: string[] =
                  strListDevice.split(",");
                if (listDeviceForceKplus720.indexOf(me.vieONDeviceID) >= 0) {
                  me.forceKplus720 = true;
                }
              }
              callback(me);
            },
          });
        },
      });
    },
  };
}

export default init;
