import { get } from "lodash";
import SentryManager from "app/utils/SentryManager";
import { ENV, PLATFORM } from "app/utils/constants";
import icFullsizePng from "assets/images/logo.png";
import icFullsize from "assets/images/logo.svg";
import { callApiServiceBilling } from "services/api";

declare global {
  interface Window {
    SigmaPacker?: any;
    sigmaPacker?: any;
    qjyBrowser: any;
  }
}
function init() {
  let trackingVieONPath: string;
  switch (ENV) {
    case "product":
      trackingVieONPath = "/topics/vieon-tracking-coolita";
      break;
    case "staging":
      trackingVieONPath = "/topics/vieon-tracking-coolita-staging";
      break;
    case "testing":
      trackingVieONPath = "/topics/vieon-tracking-coolita-testing";
      break;
    default:
      trackingVieONPath = "/topics/vieon-tracking-vcoolita-dev";
      break;
  }

  const getSideLogo = () => {
    const browserRegex =
      /(MSIE|Trident|(?!Gecko.+)Firefox|(?!AppleWebKit.+Chrome.+)Safari(?!.+Edge)|(?!AppleWebKit.+)Chrome(?!.+Edge)|(?!AppleWebKit.+Chrome.+Safari.+)Edge|AppleWebKit(?!.+Chrome|.+Safari)|Gecko(?!.+Firefox))(?: |\/)([\d.apre]+)/.exec(
        window.navigator.userAgent
      );
    let sideLogo = icFullsize;
    if (browserRegex) {
      const browserMainVersion = parseInt(browserRegex[2].split(".")[0], 10);
      if (browserMainVersion < 41) {
        sideLogo = icFullsizePng;
      }
    }
    return sideLogo;
  };

  function loadScripts(
    scripts: string | any[],
    callback: { (): void; (): void }
  ) {
    let loadedScripts = 0;

    function scriptLoaded() {
      loadedScripts += 1;
      if (loadedScripts === scripts.length) {
        callback();
      }
    }

    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < scripts.length; i++) {
      const script = document.createElement("script");
      script.type = "text/javascript";
      script.async = true;
      script.src = scripts[i];
      script.onload = scriptLoaded;
      document.getElementsByTagName("head")[0].appendChild(script);
    }
  }
  const initSigmaPacker = () => {
    if (typeof window.SigmaPacker === "function" && !window.sigmaPacker) {
      window.sigmaPacker = new window.SigmaPacker();
      window.sigmaPacker.onload = () => {
        console.log("SigmaPacker: Initialized");
      };
      console.log("SigmaPacker: Initializing");
      window.sigmaPacker.init();

      SentryManager.init();
    }
  };
  const nonGameQR = `https://static.vieon.vn/vieon-images/rapviet/qrcode_download-app_lg.png?v=${new Date().getTime()}`;

  // @ts-ignore
  return {
    appVersion: process.env.REACT_APP_VERSION || "master",
    limitItemRibbon: 15,
    platformName: PLATFORM.COOLITA,
    fullPlatformName: "Coocaa",
    brand: "coocaa",
    onPromotion: "",
    onAutoPromotion: "",
    model: "",
    vieONDeviceID: "",
    sideLogo: getSideLogo(),
    version: "",
    firmwareVersion: "",
    yearOfManufacture: 2030,
    disableTransition: true,
    disableGame: process.env.REACT_APP_DISABLE_GAME || false,
    nonGameQR,
    trackingGAID: "G-3B4G9CEQZ7", // id for vidaa
    trackingSegmentID: "ZzNWQkFBYk9NMlowU2FPMm1taE8wc0x4VTc2bW5WQWs", // id for vidaa
    trackingVieONPath,
    trackingSegmentUrl: "https://vieon-tracking.vieon.vn",
    support4K: false, // check docs vidaa
    supportH265: false, // check docs vidaa
    supportAudio51: false, // check docs vidaa
    supportDolbyAtmos: false, // check docs vidaa
    manufacturer: "Coocaa",
    drmVersion: "UNKNOW",
    secLevel: 1,
    ip: "",
    forceKplus720: false,
    lanMacAddress: "",
    keys: {
      enter: 13,
      left: 37,
      up: 38,
      right: 39,
      down: 40,
      red: 403, // back-tick key on Mac
      green: 404,
      yellow: 405,
      blue: 406,
      // red: 33 // Page Up key on Windows,
      return: 461,
      rewind: 412,
      fastforward: 417,
      nexttrack: 425,
      prevtrack: 424,
      play: 415,
      pause: 19,
      stop: 413,
      channelUp: 427,
      channelDown: 428,
      exit: 10182,
      "0": 48,
      "1": 49,
      "2": 50,
      "3": 51,
      "4": 52,
      "5": 53,
      "6": 54,
      "7": 55,
      "8": 56,
      "9": 57,
    },
    exit() {
      window.close();
    },
    init(callback: any) {
      const me = this;
      loadScripts(
        [
          "https://static2.vieon.vn/production-vieon-coolita/coolita/static/js/sigma_packer-1.0.4.js",
        ],
        function () {
          initSigmaPacker();
          if (typeof window.qjyBrowser === "object") {
            const data = JSON.parse(window.qjyBrowser.deviceInfo);
            me.vieONDeviceID = get(data, "deviceInfo.did", "");
            localStorage.setItem("VieONDeviceID", me.vieONDeviceID);
            me.model = get(data, "deviceInfo.chassis", "");
            me.firmwareVersion = get(data, "deviceInfo.sysVersion", "");
            me.version = get(data, "deviceInfo.osType", "");
            me.support4K = get(data, "deviceInfo.model", "") === "4K";
            me.supportH265 = get(data, "deviceInfo.model", "") === "4K";
            me.lanMacAddress = get(data, "deviceInfo.mac", "");
          }
          const strListDevice = process.env.REACT_APP_LIST_DEVICE_FORCE_KPLUS_720;
          if (strListDevice) {
            const listDeviceForceKplus720: string[] = strListDevice.split(",");
            if (listDeviceForceKplus720.indexOf(me.vieONDeviceID) >= 0) {
              me.forceKplus720 = true;
            }
          }
          const countShowPromotion = parseInt(
            localStorage.getItem("countShowPromotion") || "0",
            10
          );
          if (countShowPromotion >= 3) {
            callback(me);
          } else {
            callApiServiceBilling<{
              error_code: number;
              error_message: string;
              result?: {
                event_name: string;
                banner_type: string;
              };
            }>({
              url: `prom/tv/auto-active/permission`,
              method: "GET",
              headers: {
                "content-type": "application/json",
              },
              params: {
                device_model: me.model,
                device_uuid: me.vieONDeviceID,
                brand: me.brand,
              },
            })
              .then((res) => {
                const onAutoPromotion: string = res.result?.banner_type || "";
                if (onAutoPromotion) {
                  /*
                      Tivi thuộc dòng được khuyến mãi ?
                        -> Kiểm tra lần đầu mở app
                        -> Lưu localStorage
                    */
                  const appVersion = localStorage.getItem("appVersion");
                  if (!appVersion) {
                    localStorage.setItem("onPromotion102020", "1");
                  }
                  const isFirstOpenApp = localStorage.getItem("onPromotion102020");
                  if (isFirstOpenApp) {
                    me.onAutoPromotion = onAutoPromotion;
                  } else {
                    localStorage.setItem("countShowPromotion", "3");
                  }
                }
              })
              .catch(() => {
                // Do nothing
              });
            callback(me);
          }
        }
      );
    },
  };
}

export default init;
