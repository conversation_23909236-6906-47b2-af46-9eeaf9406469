import get from "lodash/get";
import merge from "lodash/merge";
import { stringify } from "querystring-es3";
import callApi, {
  callApiNotDomain,
  callApiServiceUser,
  callApiServiceBilling,
  callApiServiceVoting,
  callApiServiceRecommend,
  callApiPromotionService
} from "services/api";
import * as Types from "types/endpoint";
import platform from "services/platform";
import {
  QNET_API_DOMAIN,
  WHITE_LIST_ID_RIBBONS_NOT_CACHE
} from "app/utils/constants";
import { IDevice } from "types/endpoint/Device";
import { AccountModel } from "app/models";
import { LanguageCode } from "app/containers/Authentication/type";
import { FetchResult, FetchResultStatus } from "types/common";
import { OfferSegmentedUserResponse } from "types/endpoint/OfferSegmentedUser";
import { FetchedResultAlwaysOnCampaignRaw } from "app/models/Payment/Cake";
import { PvodDetailRes } from "app/models/ModelPvod";
import {
  ShopeePayBindingQRCodeResponse,
  ShopeePayChargePayResponse,
  ShopeePayCreateTransactionResponse,
  ShopeePayTransactionStatusResponse
} from "../types/endpoint/ShopeePay";
import {
  MomoChargePayResponse,
  MomoCreateTransactionResponse,
  MomoTransactionStatusResponse
} from "../types/endpoint/Momo";
import { removeParamRequest } from "../app/utils";

const API_VERSION = 1;

const SUD_BASE = "cm";
const VERSION = "v5";

const PREFIX = `${SUD_BASE}/${VERSION}`;

export const apiEndpoint = {
  rootApi: {
    getMenu: `${PREFIX}/menu`,
    getPaymentBanner: "billing/payment-conversion/repay-notification"
  },
  userApi: {
    loginMobile: "login/mobile",
    loginMobileOtp: "login/mobile_otp",
    loginMobileConfirmOtp: "login/mobile_confirm_otp"
  }
};

export const getConfig = (data: { key: string }) =>
  callApi({
    url: `cm/v5/get-config`,
    method: "GET",
    params: { ...data }
  });

export const getUserProfile = (token: string = "") =>
  callApiServiceUser<AccountModel>({
    url: "profile",
    method: "GET",
    params: {
      model: platform.model,
      device_name: platform.manufacturer,
      device_type: "smarttv"
    },
    headers: {
      Authorization: token
    }
  });

export const restoreUserProfile = (
  token: string,
  getErrorMsg: (error: string) => void
) =>
  callApiServiceUser<Types.RestoreUserProfile>(
    {
      url: "profile/restore",
      method: "POST",
      headers: {
        Authorization: token
      }
    },
    getErrorMsg
  );

export const restoreUserConfirmOTPProfile = (
  token: string = "",
  session_id: string = "",
  otp_code: string = "",
  getErrorMsg: (error: string) => void = () => {}
) =>
  callApiServiceUser<Types.RestoreUserConfirmOTPProfile>(
    {
      url: "profile/restore/confirm_otp",
      method: "POST",
      data: stringify({
        session_id,
        otp_code
      }),
      // data: new URLSearchParams({
      //   session_id,
      //   otp_code
      // }).toString(),
      headers: {
        Authorization: token,
        "Content-Type": "application/x-www-form-urlencoded"
      }
    },
    getErrorMsg
  );

export const loginAnonymous = () => {
  const data = {
    platform: platform.platformName,
    model: platform.model,
    device_id: platform.vieONDeviceID
  };
  return callApiServiceUser<Types.Anonymous>({
    url: "login/anonymous",
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data).toString(),
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  });
};

export const getUserType = (token: string = "") =>
  callApi<Types.BannerType>({
    url: `cm/v5/kplus/bannertype`,
    method: "GET",
    headers: {
      Authorization: token
    }
  });

export const getKplusUserValidate = (
  token: string = "",
  userId: string = "",
  liveTVId: string = ""
) =>
  callApi<{
    code: number;
    error: any;
    result: { validation: number; must_revalidate_before: number };
  }>({
    url: `cm/v5/kplus/validate/${userId}`,
    method: "GET",
    params: {
      user_id: userId,
      livetv_id: liveTVId,
      device_id: platform?.vieONDeviceID || "",
      device_brand: platform?.fullPlatformName || ""
    },
    headers: {
      Authorization: token,
      "Content-Type": "application/x-www-form-urlencoded"
    }
  });

export const getGeoCheck = () =>
  callApi(
    {
      url: "cm/v5/geo-check",
      method: "GET"
    },
    (error: any) => {
      const status = get(error, ["response", "status"], 0);
      if (status === 511) {
        return "status_511";
      }
      return (
        get(error, ["response", "data", "message"]) ||
        error.message ||
        "Unexpected"
      );
    }
  );

export const getMenu = () =>
  callApi<Types.Menu>({
    url: apiEndpoint.rootApi.getMenu,
    method: "GET",
    cache: false
  });

export const getLiveTVPage = () =>
  callApi<Types.LiveTVPage>({ url: "cm/v5/livetv/page", method: "GET" });

export const getPageBanners = (id: string, limit: number = 15) =>
  callApi<Types.PageBanners>({
    url: `cm/v5/page_banners/${id}?limit=${limit}`,
    method: "GET",
    cache: false
  });

export const getPageRibbons = (id: string) =>
  callApi<Types.PageRibbons>({
    url: `cm/v5/page_ribbons/${id}`,
    method: "GET"
  });

export const getRibbon = (
  id: string,
  limit: number = 15,
  page: number = 0,
  forceCacheValue?: boolean
) => {
  return callApi<Types.Ribbon>({
    url: `cm/v5/ribbon/${id}?limit=${limit}&page=${page}`,
    method: "GET",
    cache:
      forceCacheValue ?? !(WHITE_LIST_ID_RIBBONS_NOT_CACHE.indexOf(id) >= 0)
  });
};

export const getRentingContents = (params?: {
  page?: number;
  limit?: number;
  sort?: string;
  close_to_expiry?: number;
  pre_order?: number;
}) =>
  callApi<Types.Ribbon>({
    url: `cm/v5/tvod`,
    method: "GET",
    params: {
      page: 0,
      limit: 15,
      sort: "expiry",
      close_to_expiry: 0,
      pre_order: 0,
      ...params
    }
  });

export const getReminders = (params?: {
  time_length_past?: number;
  time_length_future?: number;
}) =>
  callApiServiceUser<Types.TvodLiveEventReminder[]>({
    url: `tvod/reminders`,
    method: "GET",
    params: {
      time_length_past: 24,
      time_length_future: 72,
      ...params
    }
  });

export const getWatchMore = (page: number, limit: number = 15) =>
  callApi<Types.WatchMore>({
    url: `cm/activity/watchmore`,
    method: "GET",
    params: {
      page,
      limit
    }
  });

export const getWatchLater = (page: number, limit: number = 15) =>
  callApi<Types.WatchLater>({
    url: `cm/activity/watchlater`,
    method: "GET",
    params: {
      page,
      limit
    }
  });

export const getPaymentBanner = () => {
  return callApi<Types.SubscriptionResponse>({
    url: `billing/payment-conversion/repay-notification`,
    method: "GET",
    cache: true
  });
};

export const getTvodInfoOfMovie = (
  id: string,
  params?: { [key: string]: any }
) =>
  callApiServiceBilling({
    url: `tvod/movie/info/${id}`,
    method: "GET",
    params: {
      api_version: API_VERSION,
      ...params
    },
    cache: false
  });

export const getTvodInfoOfShow = (
  id: string,
  params?: { [key: string]: any }
) =>
  callApiServiceBilling({
    url: `tvod/show/info/${id}`,
    method: "GET",
    params: {
      api_version: API_VERSION,
      ...params
    },
    cache: false
  });

export const getTvodInfoOfSimulcast = (
  id: string,
  params?: { [key: string]: any }
) =>
  callApiServiceBilling({
    url: `tvod/simulcast/info/${id}`,
    method: "GET",
    params: {
      api_version: API_VERSION,
      ...params
    },
    cache: false
  });

export const getGlobalPlanInfo = ({
  contentId,
  languageCode
}: {
  contentId: string;
  languageCode: LanguageCode;
}) => {
  return callApi({
    url: `billing/global/package/info/${contentId}`,
    method: "GET",
    cache: false,
    headers: {
      "Accept-Language": languageCode
    }
  });
};

export const getContent = (id: string, params?: { [key: string]: string }) =>
  callApi({
    url: `cm/v5/content/${id}`,
    method: "GET",
    params,
    cache: false
  });
export const getContentDetail = (
  id: string,
  params?: { [key: string]: string }
) =>
  callApi<Types.DataVideoPlayer>(
    {
      url: `cm/v5/content_detail/${id}`,
      method: "GET",
      params,
      cache: false
    },
    undefined,
    true
  );
export const getContentTips = (
  id: string,
  timeout: number = 10000,
  params: any = {}
) =>
  callApi({
    url: `cm/v5/content/${id}/tips`,
    method: "GET",
    cache: false,
    timeout,
    params
  });

export const getLiveStreamDetail = (id: string) =>
  callApi({
    url: `cm/v5/events/${id}`,
    method: "GET",
    params: {}
  });

export const getDeviceWatching = () =>
  callApi({
    url: `cm/activity/device/watching`,
    method: "GET",
    cache: false
  });

export const getEpisode = (id: string, params?: { [key: string]: any }) =>
  callApi({
    url: `cm/v5/episode/${id}`,
    method: "GET",
    params
    // cache: true,
  });

export const getRelated = (id: string, params?: { [key: string]: any }) =>
  callApi({
    url: `cm/v5/related_videos/${id}`,
    method: "GET",
    params,
    cache: true
  });

export const getRecommend = (id: string, page: number) =>
  callApi({
    url: `cm/v5/related/${id}`,
    method: "GET",
    params: {
      page,
      limit: 30
    },
    cache: true
  });

export const getRecommendLivestream = (id: string, page: number) =>
  callApi({
    url: `cm/v5/events/related/${id}`,
    method: "GET",
    params: {
      page,
      limit: 15
    },
    cache: true
  });

export const postWatchLater = (data: { content_id: string }) =>
  callApi({
    url: "cm/activity/watchlater",
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });

export const postAddAudioSubtitle = (data: {
  audio_code_name?: string;
  subtitle_code_name?: string;
}) =>
  callApi({
    url: "cm/v5/personal/audio-subtitle",
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });

export const getVTT = (url: string) =>
  callApiNotDomain({
    url
  });

export const trackingProgress = (data: {
  usi?: string;
  content_id: string;
  content_type: number;
  content_name: string;
  data: any[];
  video_codec: string;
}) =>
  callApi({
    url: "cm/activity/tracking/watch",
    method: "POST",
    data
  });

export const getSubList = (data: { list_id: string[] }) =>
  callApiServiceUser<Record<string, boolean>>({
    url: "get-subscribe-content",
    method: "POST",
    data
  });

export const subscribeContent = (
  data: {
    content_id: string;
    device_id: string;
    model: string;
  },
  params?: { start_time?: number; content_type?: number }
) =>
  callApiServiceUser({
    url: "subscribe-content",
    method: "POST",
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    },
    data: stringify(data),
    // data: new URLSearchParams(data).toString(),
    params
  });

export const unsubscribeContent = (data: { content_id: string }) =>
  callApiServiceUser({
    url: "unsubscribe-content",
    method: "POST",
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    },
    data: stringify(data)
    // data: new URLSearchParams(data).toString()
  });

export const getSubsInfo = () =>
  callApiServiceBilling<Package.PurchasedServices>({
    url: `purchased-services`,
    method: "GET"
  });

export const getBuyHistory = () =>
  callApiServiceBilling({
    url: `transactions`,
    method: "GET",
    params: {
      page_index: 0,
      page_size: 50,
      api_version: API_VERSION
    }
  });

// #region Login
export const requestLoginOTP = (phoneNumber: string) => {
  const data = {
    phone_number: phoneNumber,
    platform: platform.platformName,
    // model: platform.model,
    device_id: platform.vieONDeviceID,
    device_name: platform.fullPlatformName
  };
  return callApiServiceUser({
    url: "login/mobile_otp",
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data).toString(),
    cache: false,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  });
};

export const requestResendLoginOTP = (phoneNumber: string) => {
  const data = {
    phone_number: phoneNumber,
    platform: platform.platformName,
    // model: platform.model,
    device_id: platform.vieONDeviceID,
    device_name: platform.fullPlatformName
  };
  return callApiServiceUser({
    url: "login/resend_mobile_otp",
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data).toString(),
    cache: false,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  });
};

export const loginByOtp = (otp: string, session: string) => {
  const data = {
    otp_code: otp,
    register_session_id: session
  };
  return callApiServiceUser({
    url: "login/mobile_confirm_otp",
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data).toString(),
    cache: false,
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });
};

export const loginByPassword = (
  phoneNumber: string,
  password: string,
  getErrorMsg?: Function
) => {
  const data = {
    phone_number: phoneNumber,
    password,
    platform: platform.platformName,
    model: platform.model,
    device_id: platform.vieONDeviceID,
    device_name: platform.manufacturer,
    device_type: "smarttv"
  };
  return callApiServiceUser(
    {
      url: "login/mobile",
      method: "POST",
      data: stringify(data),
      // data: new URLSearchParams(data).toString(),
      cache: false,
      headers: {
        "content-type": "application/x-www-form-urlencoded"
      }
    },
    getErrorMsg
  );
};

export const loginStringCodeRequest = () => {
  const data = {
    platform: platform.platformName,
    model: platform.model,
    device_id: platform.vieONDeviceID,
    device_name: platform.manufacturer,
    device_type: "smarttv"
  };

  return callApiServiceUser({
    url: "login/string-code",
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });
};

export const loginCheckCode = (code: string) =>
  callApiServiceUser({
    url: `login/info-code?code=${code}`,
    method: "GET",
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });
/** Login: End */

// #region Logout
export const logout = () => {
  const data = {
    device_id: platform.vieONDeviceID
  };
  return callApiServiceUser({
    url: "logout",
    method: "POST",
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    },
    data: stringify(data),
    // data: new URLSearchParams(data).toString(),
    validateStatus(status) {
      return status >= 200 && status < 500;
    }
  });
};
/** Logout: End */

// #region refreshToken
// export const refreshToken = (data: {
//   device_id: string;
//   model: string;
//   device_name: string;
// }) =>
//   callApiServiceUser({
//     url: "refresh_token",
//     method: "POST",
//     headers: {
//       "content-type": "application/x-www-form-urlencoded",
//     },
//     data: stringify(data),
//   });

/** LiveTV: Start */
export const liveTVGetAllChannel = () =>
  callApi({
    url: "cm/v5/livetv/list",
    method: "GET",
    params: {
      page: 0,
      limit: 0
    }
  });

export const liveTVGetPage = () =>
  callApi({
    url: "cm/v5/livetv/page",
    method: "GET"
  });

export const liveTVGetRibbonList = () =>
  callApi({
    url: "cm/v5/livetv/category",
    method: "GET"
  });
export const liveTVGetHighlight = () =>
  callApi({
    url: "cm/v5/livetv/highlight",
    method: "GET"
  });

export const liveTVGetRibbonDetail = (listId: string) =>
  callApi({
    url: `cm/v5/livetv/list/${listId}`,
    method: "GET"
  });

export const liveTVGetFavoriteList = () =>
  callApi({
    url: `cm/activity/livetv/favorite/list`,
    method: "GET"
  });
export const liveTVGetRecentWatchList = () =>
  callApi({
    url: `cm/activity/livetv/watched/list`,
    method: "GET"
  });

export const liveTVGetOnAirList = () =>
  callApi({
    url: `cm/v5/livetv/epg/broadcasting`,
    method: "GET"
  });

export const liveTVGetEPGHighlight = () =>
  callApi({
    url: `cm/v5/livetv/epg/highlight`,
    method: "GET"
  });

export const liveTVGetOnAirDetail = (id: String) =>
  callApi({
    url: `cm/v5/ribbon/${id}`,
    method: "GET"
  });

export const liveTVGetEPGList = (livetv_id: string, str_date: string) =>
  callApi({
    url: `cm/v5/livetv/epg`,
    method: "GET",
    params: {
      livetv_id,
      str_date
    }
  });

export const liveTVGetChannelDetail = (id: string) =>
  callApi({
    url: `cm/v5/livetv/detail/${id}`,
    method: "GET"
  });

export const liveTVGetEPGDetail = (livetv_slug: string) =>
  callApi({
    url: `cm/v5/slug/livetv/detail`,
    method: "POST",
    data: stringify({
      livetv_slug
    }),
    // data: new URLSearchParams({
    //   livetv_slug
    // }).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });

export const liveTVAddFavoriteChannel = (data: { livetv_ids: string }) =>
  callApi({
    url: `cm/activity/livetv/favorite/add`,
    method: "POST",
    data: stringify(data),
    // data: JSON.stringify(data),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });
export const liveTVAddWatchedChannel = (data: { livetv_ids: string }) =>
  callApi({
    url: `cm/activity/livetv/watched/add`,
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });
/** LiveTV: End */

/** Qnet: Start */
export const qnetGetUserIDQnet = (id: string, type: string) =>
  callApi({
    url: `cm/v5/qnet-token`,
    method: "GET",
    params: {
      id,
      type
    }
  });

export const qnetPing = (
  operatorId: string,
  session: string,
  token: string
) => {
  const params =
    token !== ""
      ? {
          token
        }
      : {
          operatorId,
          session
        };

  return callApiNotDomain({
    url: `${QNET_API_DOMAIN}csl/ping`,
    method: "GET",
    params
  });
};

export const qnetRefreshToken = (operatorId: string, session: string) =>
  callApiNotDomain({
    url: `${QNET_API_DOMAIN}csl/refresh`,
    method: "GET",
    params: {
      operatorId,
      session
    }
  });

export const qnetEndStream = (token: string) =>
  callApiNotDomain({
    url: `${QNET_API_DOMAIN}csl/end`,
    method: "GET",
    params: {
      token
    }
  });
/** Qnet: End */

// #region Signup
export const registerUser = (
  phoneNumber: string,
  password: string,
  getErrorMsg?: Function
) => {
  const data = {
    phone_number: phoneNumber,
    given_name: phoneNumber,
    password,
    platform: platform.platformName,
    model: platform.model,
    device_id: platform.vieONDeviceID,
    device_name: platform.manufacturer,
    device_type: "smarttv"
    // user_agent: "",
  };
  return callApiServiceUser(
    {
      url: "register/mobile",
      method: "POST",
      data: stringify(data),
      // data: new URLSearchParams(data).toString(),
      headers: {
        "content-type": "application/x-www-form-urlencoded"
      }
    },
    getErrorMsg
  );
};

export const confirmOtpRegister = (otp: string, registerSessionId: string) => {
  const data = {
    otp_code: otp,
    register_session_id: registerSessionId
  };
  return callApiServiceUser({
    url: "register/confirm_otp",
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });
};

export const requestResendSigupOTP = (phoneNumber: string) => {
  const data = {
    phone_number: phoneNumber,
    platform: platform.platformName,
    // model: platform.model,
    device_id: platform.vieONDeviceID,
    device_name: platform.fullPlatformName
  };
  return callApiServiceUser({
    url: "register/resend_otp",
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data).toString(),
    cache: false,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  });
};
// #endregion Signup

// #region Reset Password
export const requestResetPassword = (
  phoneNumber: string,
  getErrorMsg?: Function
) => {
  const data = {
    phone_number: phoneNumber,
    platform: platform.platformName,
    // model: platform.model,
    device_id: platform.vieONDeviceID,
    device_name: platform.fullPlatformName
  };
  return callApiServiceUser(
    {
      url: "forget/forget_password",
      method: "POST",
      data: stringify(data),
      // data: new URLSearchParams(data).toString(),
      cache: false,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      }
    },
    getErrorMsg
  );
};

export const checkOtpResetPassword = (
  otp: string,
  registerSessionId: string
) => {
  const data = {
    otp_code: otp,
    register_session_id: registerSessionId
  };
  return callApiServiceUser({
    url: "otp/check-valid",
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });
};

export const confirmOtpResetPassword = (
  otp: string,
  registerSessionId: string,
  password: string,
  password_old?: string
) => {
  const data = merge(
    {
      otp_code: otp,
      register_session_id: registerSessionId,
      password
    },
    password_old ? { password_old } : {}
  );
  return callApiServiceUser({
    url: "forget/confirm_otp",
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data as any).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });
};

/** Payment: Start */

export const getDailyPermission = (token: string = "") =>
  callApiServiceBilling<any>({
    url: "payment/check-permission-daily",
    method: "POST",
    headers: {
      "content-type": "application/x-www-form-urlencoded",
      Authorization: token
    }
  });

export const getPackageList = () =>
  callApiServiceBilling<any[]>({ url: "packages", method: "GET" });

export const getInfoPaymentTvod = (pkgId: string) =>
  callApiServiceBilling<any>({
    url: `tvod/offer/${pkgId}`,
    method: "GET"
  });

export const getPackagePermission = (data: {
  next_package_id: number;
  platform: string;
}) =>
  callApiServiceBilling<any>({
    url: `payment/permission`,
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data as any).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });

export const getPackageProm = (data: {
  platform: string;
  device_id: string;
  check_frequency?: boolean;
}) =>
  callApiServiceBilling<any>({
    url: `prom/package-discount`,
    method: "GET",
    params: data,
    cache: false
  });

// export const getNapasTokenList = () =>
//   callApiServiceBilling<any[]>({ url: "v2/tokens", method: "GET" });
//
export const getNapasTokenList = (data: {
  platform: string;
  payment_method: string;
}) =>
  callApiServiceBilling<any>({
    url: "v2/tokens",
    method: "POST",
    data: JSON.stringify(data),
    headers: {
      "content-type": "application/json"
    }
  });

export const createPaymentTransaction = (data: {
  payment_method: string;
  payment_service: string;
  package_id: string;
  promotion_code: string;
  token_id?: string;
  platform: string;
}) =>
  callApiServiceBilling<any>(
    {
      url: "v2/transaction",
      method: "POST",
      data: JSON.stringify(data),
      headers: {
        "content-type": "application/json"
      }
    },
    (error: any) => get(error, ["response", "data", "error_message"])
  );

export const getPaymentCheckOrder = (orderId: string) =>
  callApiServiceBilling<any>({
    url: `v2/transaction/status/${orderId}`,
    method: "GET"
  });

export const getPackageExtend = (data: {
  next_package_id: number;
  platform: string;
}) =>
  callApiServiceBilling<any>({
    url: `payment/permission2`,
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data as any).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });

export const getAlwaysOnCampaignList: () => Promise<FetchedResultAlwaysOnCampaignRaw> =
  () =>
    callApiPromotionService<FetchedResultAlwaysOnCampaignRaw>({
      url: `always-on-campaigns`,
      method: "GET"
    });

export const getMomoQRCode = (data: {
  package_id: number;
  promotion_code: string;
}) =>
  callApiServiceBilling<{
    errorCode: number;
    orderId: string;
    qrCodeImg: string;
  }>({
    url: `transaction-momo`,
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data as any).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });

export const getMomoCheckOrder = (data: { txn_ref: string }) =>
  callApiServiceBilling<any>({
    url: `statustransaction-momo`,
    method: "GET",
    params: { ...data }
  });

export const getMocaQRCode = (data: {
  package_id: string | number;
  promotion_code: string;
  platform: string;
}) =>
  callApiServiceBilling<{
    errorCode: number;
    orderId: string;
    qrCodeImg: string;
  }>({
    url: `transaction-moca`,
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data as any).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });

export const getMocaCheckOrder = (orderId: string, data: { tnxId: string }) =>
  callApiServiceBilling<any>({
    url: `statustransaction-moca/${orderId}`,
    method: "GET",
    params: { ...data }
  });

export const getZaloPayQRCode = (data: {
  package_id: string | number;
  promotion_code: string;
  platform: string;
}) =>
  callApiServiceBilling<{
    errorCode: number;
    orderId: string;
    qrCodeImg: string;
  }>({
    url: `transaction-zalopay`,
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data as any).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });

export const getZaloPayCheckOrder = (orderId: string) =>
  callApiServiceBilling<any>({
    url: `statustransaction-zalopay/${orderId}`,
    method: "GET"
  });

export const getZaloPayBindingQRCode = () =>
  callApiServiceBilling<{
    orderId: string;
    qrCodeImg: string;
    qrDisplayTime?: number;
  }>({
    url: `agreement-pay-binding-zalopay`,
    method: "POST"
  });

export const getZaloPayBindingStatus = (userId: string) =>
  callApiServiceBilling<any>({
    url: `agreement-pay-binding-status-zalopay/${userId}`,
    method: "GET"
  });

export const chargeZaloPayByToken = (data: {
  package_id: number;
  promotion_code: string;
  platform: string;
}) =>
  callApiServiceBilling<any>({
    url: `charge-by-token-zalopay`,
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data as any).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });

export const getShopeePayBindingQRCode =
  (): Promise<ShopeePayBindingQRCodeResponse> => {
    const { platformName } = platform;
    return callApiServiceBilling<ShopeePayBindingQRCodeResponse>(
      {
        url: `v2/tokens/link`,
        method: "POST",
        data: JSON.stringify({
          platform: platformName,
          payment_method: "WALLET",
          payment_service: "SHOPEEPAY",
          return_url: "http://www.vieon.vn"
        }),
        headers: {
          "content-type": "application/json"
        }
      },
      (error: any) => get(error, ["response", "data", "error_message"])
    );
  };

export const getShopeePayTokens = (): Promise<{
  result: { tokens: string[] };
}> => {
  const { platformName } = platform;
  return callApiServiceBilling<{
    result: { tokens: string[] };
  }>({
    url: `v2/tokens`,
    method: "POST",
    data: JSON.stringify({
      payment_method: "WALLET",
      payment_service: "SHOPEEPAY",
      platform: platformName
    }),
    headers: {
      "content-type": "application/json"
    }
  });
};

export const createShopeePayTransaction = (requestData: {
  packageId: string;
  promotionCode: string;
  tokenId?: string;
}): Promise<ShopeePayCreateTransactionResponse> => {
  const { platformName } = platform;
  return callApiServiceBilling<ShopeePayCreateTransactionResponse>(
    {
      url: `v2/transaction`,
      method: "POST",
      data: JSON.stringify(
        removeParamRequest({
          package_id: requestData.packageId,
          promotion_code: requestData.promotionCode,
          token_id: requestData?.tokenId,
          payment_method: "WALLET",
          payment_service: "SHOPEEPAY",
          platform: platformName
        })
      ),
      headers: {
        "content-type": "application/json"
      }
    },
    (error: any) => get(error, ["response", "data", "error_message"])
  );
};

export const getShopeePayTransactionStatus = (
  orderId: string
): Promise<ShopeePayTransactionStatusResponse> =>
  callApiServiceBilling<ShopeePayTransactionStatusResponse>({
    url: `v2/transaction/status/${orderId}`,
    method: "GET",
    params: { order_id: orderId }
  });

export const takeChargePageShopeePay = (
  orderId: string
): Promise<ShopeePayChargePayResponse> => {
  const { platformName } = platform;
  return callApiServiceBilling<ShopeePayChargePayResponse>({
    url: `v2/transaction/pay`,
    method: "POST",
    headers: {
      "content-type": "application/json"
    },
    data: JSON.stringify({
      order_id: orderId,
      platform: platformName,
      return_url: "http://www.vieon.vn"
    })
  });
};

export const createMomoTransaction = (requestData: {
  packageId: string;
  promotionCode: string;
  tokenId?: string;
}): Promise<MomoCreateTransactionResponse> => {
  const { platformName } = platform;
  return callApiServiceBilling<MomoCreateTransactionResponse>(
    {
      url: `v2/transaction`,
      method: "POST",
      data: JSON.stringify(
        removeParamRequest({
          package_id: requestData.packageId,
          promotion_code: requestData.promotionCode,
          token_id: requestData?.tokenId,
          payment_method: "WALLET",
          payment_service: "MOMO",
          platform: platformName
        })
      ),
      headers: {
        "content-type": "application/json"
      }
    },
    (error: any) => get(error, ["response", "data", "error_message"])
  );
};

export const getMomoTransactionStatus = (
  orderId: string
): Promise<MomoTransactionStatusResponse> =>
  callApiServiceBilling<MomoTransactionStatusResponse>({
    url: `v2/transaction/status/${orderId}`,
    method: "GET",
    params: { order_id: orderId }
  });

export const takeChargePageMomo = (
  orderId: string
): Promise<MomoChargePayResponse> => {
  const { platformName } = platform;
  return callApiServiceBilling<MomoChargePayResponse>({
    url: `v2/transaction/pay`,
    method: "POST",
    headers: {
      "content-type": "application/json"
    },
    data: JSON.stringify({
      order_id: orderId,
      platform: platformName,
      return_url: "http://www.vieon.vn"
    })
  });
};

export const getVNPayQRCode = (data: {
  package_id: number;
  promotion_code: string;
}) =>
  callApiServiceBilling<{
    errorCode: number;
    orderId: string;
    qrCodeImg: string;
  }>({
    url: `transaction-vnpay-qr`,
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data as any).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });

export const getVNPayCheckOrder = (data: { txn_ref: string }) =>
  callApiServiceBilling({
    url: `statustransaction-vnpay`,
    method: "GET",
    params: { ...data }
  });

export const getCreditCardOrder = (data: {
  package_id: number;
  promotion_code: string;
}) =>
  callApiServiceBilling<any>({
    url: `transaction-asiapay`,
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data as any).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });

export const getCreditCardConfigInfo = (data: { platform: string }) =>
  callApiServiceBilling<any>({
    url: `payment/on-channel/${data.platform}`,
    method: "GET"
  });

export const getAsiaPay = (paylink: string, params: any) =>
  callApiNotDomain({
    url: paylink,
    method: "POST",
    data: stringify(params),
    // data: new URLSearchParams(params as any).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });

export const getTrackingResponseTime = (url: string) =>
  callApiNotDomain({
    url,
    method: "OPTIONS"
  });

export const getCreditCardCheckOrder = (data: { txn_ref: string }) =>
  callApiServiceBilling({
    url: `statustransaction-asiapay`,
    method: "GET",
    params: { ...data }
  });

export const checkOrderStatusByProvider = async (
  paymentMethod: string,
  orderId: string
) => {
  let res;
  try {
    switch (paymentMethod) {
      case "zalopay": {
        res = await getZaloPayCheckOrder(orderId);
        break;
      }
      case "shopeepay": {
        res = await getShopeePayTransactionStatus(orderId);
        break;
      }
      case "asiapay": {
        res = await getCreditCardCheckOrder({ txn_ref: orderId });
        break;
      }
      case "vnpay": {
        res = await getVNPayCheckOrder({ txn_ref: orderId });
        break;
      }
      case "momo": {
        res = await getMomoCheckOrder({ txn_ref: orderId });
        break;
      }
      case "moca": {
        res = await getMocaCheckOrder(orderId, { tnxId: orderId });
        break;
      }
      default:
        res = await getPaymentCheckOrder(orderId);
        break;
    }
  } catch (error) {
    res = await getPaymentCheckOrder(orderId);
  }

  return res;
};

export const getCouponInfo = (
  userId: string,
  coupon: string,
  params: {
    package_id: string | number;
    platform: string;
  }
) =>
  callApiServiceBilling({
    url: `users/${userId}/promotion-codes/${coupon}`,
    method: "POST",
    data: JSON.stringify(params),
    headers: {
      "content-type": "application/json"
    }
  });

export const getInfoOfferForSegmentedUser: () => Promise<FetchResult> =
  async () => {
    try {
      const info: any = await callApiServiceBilling({
        url: "/prom/offer-segmented-user",
        method: "GET"
      });
      if (info?.result?.data?.promotion_code) {
        return {
          status: FetchResultStatus.SUCCESS,
          data: new OfferSegmentedUserResponse(info?.result?.data)
        };
      }
      return {
        status: FetchResultStatus.SUCCESS,
        data: null
      };
    } catch {
      return {
        status: FetchResultStatus.FAIL,
        data: null
      };
    }
  };

export const sendPaymentEmail = (data: { email: string; txn_ref: string }) =>
  callApiServiceUser({
    url: `result-transaction`,
    method: "POST",
    data: stringify(data),
    // data: new URLSearchParams(data as any).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });

export const getLDPCampaign = (data: { mobile: string }) =>
  callApiServiceBilling<any>({
    url: `ldp-campaign/valid`,
    method: "POST",
    data,
    headers: {
      "content-type": "application/json"
    },
    cache: false
  });
/** Payment: End */

// #region Payemnt TVOD
export const getTVODMovieInfo = (
  id: string,
  data: { id_type: string; api_version: string }
) =>
  callApiServiceBilling({
    url: `tvod/movie/info/${id}`,
    method: "GET",
    params: { ...data }
  });

export const getTVODShowInfo = (
  id: string,
  data: { id_type: string; api_version: string }
) =>
  callApiServiceBilling({
    url: `tvod/show/info/${id}`,
    method: "GET",
    params: { ...data }
  });

export const getTVODLiveeventInfo = (
  id: string,
  getErrorMsg: (error: string) => void
) =>
  callApiServiceBilling(
    {
      url: `tvod/live-event/info/${id}`,
      method: "GET",
      params: {
        api_version: 1
      },
      cache: false
    },
    getErrorMsg
  );

export const getTVODOffer = (id: string, data: { api_version: string }) =>
  callApiServiceBilling({
    url: `tvod/offer/${id}`,
    method: "GET",
    params: { ...data }
  });

export const tvodTransactionFintech = (
  data: {
    tvod_product_id: string;
    payment_service: string;
    token_id?: string;
    redirect_params?: string;
  },
  getErrorMsg: (error: string) => void
) =>
  callApiServiceBilling(
    {
      url: `tvod/transaction/fintech`,
      method: "POST",
      params: {
        api_version: "1"
      },
      data: JSON.stringify(data),
      headers: {
        "content-type": "application/json"
      }
    },
    getErrorMsg
  );
export const tvodTransactionFintechPay = (
  data: { order_id: string },
  params: { api_version: string }
) =>
  callApiServiceBilling({
    url: `tvod/transaction/fintech/pay`,
    method: "POST",
    params: { ...params },
    data: JSON.stringify(data),
    headers: {
      "content-type": "application/json"
    }
  });
export const getTVODStatusTransaction = (
  id: string,
  params: { api_version: string }
) =>
  callApiServiceBilling({
    url: `tvod/transaction/${id}`,
    method: "GET",
    params: { ...params }
  });

/** Payment TVOD: End */

// #region Payemnt PVOD
export const getPVODMovieInfo = (
  id: string,
  type: string,
  data: { api_version: string } = { api_version: "1" }
) =>
  callApiServiceBilling({
    url: `pvod/${id}/${type}`,
    method: "GET",
    params: { ...data }
  });

export const getPVODOffer = (id: string, data: { api_version: string }) =>
  callApiServiceBilling({
    url: `pvod/offer/${id}`,
    method: "GET",
    params: { ...data }
  });

export const pvodTransactionFintech = (
  data: {
    product_id: string;
    payment_service: string;
    token_id?: string;
    redirect_params?: string;
  },
  getErrorMsg: (error: string) => void
) =>
  callApiServiceBilling(
    {
      url: `pvod/transaction/fintech`,
      method: "POST",
      params: {
        api_version: "1"
      },
      data: JSON.stringify(data),
      headers: {
        "content-type": "application/json"
      }
    },
    getErrorMsg
  );
export const pvodTransactionFintechPay = (
  data: { order_id: string },
  params: { api_version: string }
) =>
  callApiServiceBilling({
    url: `pvod/transaction/fintech/pay`,
    method: "POST",
    params: { ...params },
    data: JSON.stringify(data),
    headers: {
      "content-type": "application/json"
    }
  });
export const getPVODStatusTransaction = (
  id: string,
  params: { api_version: string }
) =>
  callApiServiceBilling({
    url: `pvod/transaction/${id}`,
    method: "GET",
    params: { ...params }
  });
/** Payment PVOD: End */

// #region Personalization
export const getConfigPersonal = () =>
  callApi({
    url: `cm/v5/get-config`,
    method: "GET",
    params: {
      key: "personal_genre"
    }
  });

export const submitPersonal = (list: any[]) => {
  const items: any[] = list
    .filter((item) => item.id)
    .map((item) => {
      return {
        id: get(item, "id", ""),
        name: get(item, "name", ""),
        odr: 1
      };
    });
  return callApi({
    url: `cm/v5/collection`,
    method: "POST",
    data: stringify({ items: JSON.stringify(items) }),
    // data: new URLSearchParams({ items: JSON.stringify(items) }).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded"
    }
  });
};
// #endregion Personalization

// #region Screensaver
export const getScreensaver = () =>
  callApi({
    url: `cm/v5/screen-saver`,
    method: "GET"
  });
// #endregion Screensaver

// #region Report
export const getReportList = () =>
  callApi({
    url: `user-report/v1/report-type/list`,
    method: "GET"
  });
export interface IReportParam {
  report_ids?: string[];
  message?: string;
  video_profile?: string;
  audio_profile?: string;
  subtitle?: string;
  os_version?: string;
  time_seeker?: number;
  entity_type?: number;
  version?: string;
  model?: string;
  year?: string;
  platform?: string;
}
export const sendReport = (contentId: string, data: IReportParam) => {
  return callApi({
    url: `user-report/v1/report-content/${contentId}`,
    method: "POST",
    data,
    headers: {
      "Content-Type": "application/json"
    }
  });
};
// #endregion Report
// #region Brand Promotion
export interface ActiveSamsungAutoPromotionReq {
  [key: string]: string;
  brand: string;
  device_uuid: string;
  device_model: string;
  mac_address: string;
}

export const activeSamsungAutoPromotion = (
  data: ActiveSamsungAutoPromotionReq
) =>
  callApiServiceBilling<any>({
    url: "prom/tv/auto-active/apply",
    method: "POST",
    data,
    headers: {
      "content-type": "application/json"
    }
  });

export interface ActiveAutoPromotionReq {
  // [key: string]: string;
  brand: string;
  device_uuid: string;
  device_model: string;
  mac_address?: string;
}

export const activeAutoPromotion = (data: ActiveAutoPromotionReq) =>
  callApiServiceBilling<any>({
    url: "prom/tv/auto-active/apply",
    method: "POST",
    data,
    headers: {
      "content-type": "application/json"
    }
  });
// #end region Brand Promotion

// #region Rap Viet
// Campaign: Vòng thi đấu
// Question: Tập gameshow (1 vòng gồm nhiều tập)
// Option: Thí sinh
// Chart: Bảng xếp hạng
export const getCampaigns = () =>
  callApiServiceVoting<any>({
    url: "campaign",
    method: "GET"
  });
export const getQuestionByCampaign = (campaignId: string) =>
  callApiServiceVoting<any>({
    url: `${campaignId}/question`,
    method: "GET"
  });
export const getOptionByCampaign = (campaignId: string) =>
  callApiServiceVoting<any>({
    url: `campaign/${campaignId}/option`,
    method: "GET"
  });
export const getOptionChartByQuestion = (questionId: string) =>
  callApiServiceVoting<any>({
    url: `${questionId}/option_chart`,
    method: "GET"
  });
export const getOptionFinalChartByQuestion = (questionId: string) =>
  callApiServiceVoting<any>({
    url: `${questionId}/final_chart`,
    method: "GET"
  });

// #end region Rap Viet

// #start Device manager
export const getDevices = () =>
  callApi({
    url: `cm/activity/device/list`,
    method: "GET"
  });

export const userDisabledDevices = (deviceIds: IDevice[]) => {
  const data = deviceIds.map((device) => {
    return {
      device_id: device.deviceId,
      platform: device.platform,
      model: device.model
    };
  });
  return callApi({
    url: `user/profile/device/disable`,
    method: "POST",
    data: {
      devices: [...data]
    },
    headers: {
      "Content-Type": "application/json"
    }
  });
};

// #end Device Manager

// #start Kids Report
export const getUserSummaryWatch = (profileId: string, params: any) =>
  callApiServiceRecommend({
    url: `api/v1/user/activity-report/summary-watch/${profileId}`,
    method: "GET",
    params: { ...params }
  });

export const getUserDetailWatch = (profileId: string, params: any) =>
  callApiServiceRecommend({
    url: `api/v1/user/activity-report/detail-watch/${profileId}`,
    method: "GET",
    params: { ...params }
  });
// #end Kids Report
