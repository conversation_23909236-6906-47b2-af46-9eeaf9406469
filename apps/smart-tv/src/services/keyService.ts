type KeyHandler = (e: KeyboardEvent) => void;

export default class KeyServices {
  defaultHandle: KeyHandler = () => {};

  registers: { [key: string]: KeyHandler } = {};

  priorities: { currentHandle: KeyHandler; handles: KeyHandler[] }[] = [];

  private static instance: KeyServices;

  private constructor() {
    window.addEventListener("keydown", this.keyHandle);
  }

  public static getInstance(): KeyServices {
    if (!KeyServices.instance) {
      KeyServices.instance = new KeyServices();
    }
    return KeyServices.instance;
  }

  private keyHandle = (e: KeyboardEvent) => {
    const { registers, priorities } = this;
    // Object.keys(registers).forEach((k) => {
    //   registers[k](e);
    // });
    for (const k in registers) {
      registers[k](e);
    }
    for (let i = 0; i < priorities.length; i += 1) {
      if (priorities[i] && typeof priorities[i].currentHandle === "function") {
        priorities[i].currentHandle(e);
        break;
      }
    }
    if (!(e.keyCode > 111 && e.keyCode < 124)) {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  public regHandler(id: string, keyHandle: KeyHandler) {
    this.registers[id] = keyHandle;
  }

  public unregHandler(id: string) {
    delete this.registers[id];
  }

  public clearRegisters() {
    this.registers = {};
  }

  public pushHandle = (priority: number, keyHandle: KeyHandler) => {
    const { priorities } = this;
    if (priorities[priority]) {
      priorities[priority].currentHandle = keyHandle;
      priorities[priority].handles.push(keyHandle);
    } else {
      priorities[priority] = {
        currentHandle: keyHandle,
        handles: [keyHandle],
      };
    }
  };

  public popHandle = (priority: number, keyHandle: KeyHandler) => {
    const { priorities } = this;
    const p = priorities[priority];
    if (p) {
      if (keyHandle) {
        const keyHandlerIndex = p.handles.indexOf(keyHandle);
        if (keyHandlerIndex >= 0) {
          p.handles.splice(keyHandlerIndex, 1);
        }
      }
      p.currentHandle = p.handles.slice(-1)[0];
    }
  };

  public switchHandle = (priority: number, keyHandle: KeyHandler) => {
    const { priorities } = this;
    if (priorities[priority]) {
      priorities[priority].currentHandle = keyHandle;
    } else {
      priorities[priority] = {
        currentHandle: keyHandle,
        handles: [],
      };
    }
  };

  public stopHandle = (priority: number) => {
    this.switchHandle(priority, this.defaultHandle);
  };
}
