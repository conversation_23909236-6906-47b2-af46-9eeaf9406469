import merge from "lodash/merge";
import get from "lodash/get";
import { Many } from "lodash";

class Languages {
  private static instance: Languages;

  languages: { [key: string]: any } = {};

  private constructor(langObj: { [key: string]: any }) {
    this.languages = {};
  }

  public static getInstance(): Languages {
    if (!Languages.instance) {
      Languages.instance = new Languages({});
    }
    return Languages.instance;
  }

  public add = (langObj: { [key: string]: string }) => {
    return merge(this.languages, langObj);
  };

  public get = (path: Many<string | number | symbol>) => {
    return get(this.languages, path, "");
  };
}

export default Languages.getInstance();
