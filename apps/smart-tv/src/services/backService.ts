type Handler = () => void;

class BackServices {
  private static instance: BackServices;

  handlers: Handler[] = [];

  defaultHandle: Handler = () => {
    window.history.back();
  };

  public static getInstance(): BackServices {
    if (!BackServices.instance) {
      BackServices.instance = new BackServices();
    }
    return BackServices.instance;
  }

  public back = () => {
    const index = this.handlers.length - 1;
    if (
      index < 0 ||
      !this.handlers[index] ||
      typeof this.handlers[index] !== "function"
    ) {
      this.defaultHandle();
    } else {
      this.handlers[index]();
    }
  };

  public register = (handler: Handler) => {
    this.handlers.push(handler);
  };

  public unregister = (handler: <PERSON><PERSON>) => {
    this.handlers.splice(this.handlers.indexOf(handler), 1);
  };
}

export default BackServices.getInstance();
