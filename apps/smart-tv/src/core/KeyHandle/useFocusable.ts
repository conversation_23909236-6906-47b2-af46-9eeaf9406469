import {
  RefObject,
  useCallback,
  useMemo,
  useRef,
  useEffect,
  useState,
} from "react";
import noop from "lodash/noop";
import uniqueId from "lodash/uniqueId";
import {
  VieOnNavigation,
  VieOnNavigationDialog,
  FocusableComponentLayout,
  FocusDetails,
  KeyPressDetails,
} from "./VieOnNavigation";
import { useFocusContext, useFocusTypeContext } from "./useFocusedContext";

let hoverTimer: TimerHandle = 0;

function clearHoverTimer() {
  hoverTimer && clearTimeout(hoverTimer);
  hoverTimer = 0;
}

export type EnterPressHandler<P = object> = (
  props: P,
  details?: KeyPressDetails
) => void;

export type EnterReturnHandler<P = object> = (
  props: P,
  details?: KeyPressDetails
) => void;

export type EnterReleaseHandler<P = object> = (props: P) => void;

export type ArrowPressHandler<P = object> = (
  direction: string,
  props: P,
  details: KeyPressDetails
) => boolean;

export type FocusHandler<P = object> = (
  layout: FocusableComponentLayout,
  props: P,
  details: FocusDetails
) => void;

export type BlurHandler<P = object> = (
  layout: FocusableComponentLayout,
  props: P,
  details: FocusDetails
) => void;

export type FocusHandlerType<P = { [key: string]: any }> = (
  layout: FocusableComponentLayout,
  props: P,
  details: FocusDetails,
) => void;
export interface UseFocusableConfig<P = object> {
  focusable?: boolean;
  saveLastFocusedChild?: boolean;
  trackChildren?: boolean;
  autoRestoreFocus?: boolean;
  isFocusBoundary?: boolean;
  focusKey?: string;
  preferredChildFocusKey?: string;
  onEnterPress?: EnterPressHandler<P>;
  onReturnPress?: EnterReturnHandler<P>;
  onEnterRelease?: EnterReleaseHandler<P>;
  onArrowPress?: ArrowPressHandler<P>;
  onFocus?: FocusHandler<P>;
  onBlur?: BlurHandler<P>;
  enterOnClick?: boolean;
  focusOnClick?: boolean;
  focusImplicitOnHover?: boolean;
  extraProps?: P;
}

export interface UseFocusableResult {
  ref: RefObject<any>; // <any> since we don't know which HTML tag is passed here
  focusSelf: (focusDetails?: FocusDetails) => void;
  focused: boolean;
  hasFocusedChild: boolean;
  focusKey: string;
  setFocus: (focusKey: string, focusDetails?: FocusDetails) => void;
  navigateByDirection: (direction: string, focusDetails: FocusDetails) => void;
  pause: () => void;
  resume: () => void;
  updateAllLayouts: () => void;
  getNodeLayoutByFocusKey: (
    focusKey: string
  ) => FocusableComponentLayout | null | undefined;
}

const useFocusableHook = <P>({
  focusable = true,
  saveLastFocusedChild = true,
  trackChildren = false,
  autoRestoreFocus = true,
  isFocusBoundary = false,
  focusKey: propFocusKey,
  preferredChildFocusKey,
  onEnterPress = noop,
  onReturnPress = noop,
  onEnterRelease = noop,
  onArrowPress = () => true,
  onFocus = noop,
  onBlur = noop,
  enterOnClick = false,
  focusOnClick = false,
  focusImplicitOnHover = false,
  extraProps = {} as P,
}: UseFocusableConfig<P> = {}): UseFocusableResult => {
  const ref = useRef<HTMLElement>(null);

  const [focused, setFocused] = useState(false);
  const [hasFocusedChild, setHasFocusedChild] = useState(false);

  const parentFocusKey = useFocusContext();

  const typeContext = useFocusTypeContext();

  const KeyHandler = useMemo(
    () => (typeContext === "normal" ? VieOnNavigation : VieOnNavigationDialog),
    [typeContext]
  );

  /**
   * Either using the propFocusKey passed in, or generating a random one
   */
  const focusKey = useMemo(
    () => propFocusKey || uniqueId("vn:focusable-item-"),
    [propFocusKey]
  );

  const focusSelf = useCallback(
    (focusDetails: FocusDetails = {}) => {
      if (!focusable) return;
      KeyHandler.setFocus(focusKey, focusDetails);
    },
    [KeyHandler, focusKey, focusable]
  );

  const focusSelfOnClick = useCallback(
    (e: Event) => {
      e.stopPropagation();
      if (!focusable) return;
      KeyHandler.setFocus(focusKey);
    },
    [KeyHandler, focusKey, focusable]
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const focusSelfImplicit = useCallback(() => {
    if (!focusable) return;

    hoverTimer = setTimeout(() => {
      KeyHandler.setFocus(focusKey, { implicit: true });
    }, 150);
  }, [KeyHandler, focusKey, focusable]);

  const onEnterPressHandler = useCallback(
    (details?: KeyPressDetails) => {
      if (details) onEnterPress(extraProps, details);
    },
    [onEnterPress, extraProps]
  );

  const onReturnPressHandler = useCallback(
    (details?: KeyPressDetails) => {
      if (details) onReturnPress(extraProps, details);
    },
    [onReturnPress, extraProps]
  );

  const onClickHandler = useCallback(
    (e: Event) => {
      e.stopPropagation();
      if (focusable) {
        focusSelf();
        onEnterPress(extraProps, { pressedKeys: { click: 1 } });
      }
    },
    [focusable, focusSelf, onEnterPress, extraProps]
  );

  const onEnterReleaseHandler = useCallback(() => {
    onEnterRelease(extraProps);
  }, [onEnterRelease, extraProps]);

  const onArrowPressHandler = useCallback(
    (direction: string, details: KeyPressDetails) =>
      onArrowPress(direction, extraProps, details),
    [extraProps, onArrowPress]
  );

  const onFocusHandler = useCallback(
    (layout: FocusableComponentLayout, details: FocusDetails) => {
      layout.node.setAttribute("data-focus", "true");
      onFocus(layout, extraProps, details);
    },
    [extraProps, onFocus]
  );

  const onBlurHandler = useCallback(
    (layout: FocusableComponentLayout, details: FocusDetails) => {
      layout.node.removeAttribute("data-focus");
      onBlur(layout, extraProps, details);
    },
    [extraProps, onBlur]
  );

  useEffect(() => {
    const node = ref.current;

    if (node) {
      KeyHandler.addFocusable({
        focusKey,
        node,
        parentFocusKey,
        preferredChildFocusKey,
        onEnterPress: onEnterPressHandler,
        onReturnPress: onReturnPressHandler,
        onEnterRelease: onEnterReleaseHandler,
        onArrowPress: onArrowPressHandler,
        onFocus: onFocusHandler,
        onBlur: onBlurHandler,
        onUpdateFocus: (isFocused = false) => setFocused(isFocused),
        onUpdateHasFocusedChild: (isFocused = false) =>
          setHasFocusedChild(isFocused),
        saveLastFocusedChild,
        trackChildren,
        isFocusBoundary,
        autoRestoreFocus,
        focusable,
      });
    }

    return () => {
      KeyHandler.removeFocusable({
        focusKey,
      });
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    const node = ref.current;

    if (node)
      KeyHandler.updateFocusable(focusKey, {
        node,
        preferredChildFocusKey,
        focusable,
        isFocusBoundary,
        onEnterPress: onEnterPressHandler,
        onReturnPress: onReturnPressHandler,
        onEnterRelease: onEnterReleaseHandler,
        onArrowPress: onArrowPressHandler,
        onFocus: onFocusHandler,
        onBlur: onBlurHandler,
      });
  }, [
    focusKey,
    preferredChildFocusKey,
    focusable,
    isFocusBoundary,
    onEnterPressHandler,
    onEnterReleaseHandler,
    onArrowPressHandler,
    onFocusHandler,
    onBlurHandler,
    onReturnPressHandler,
    KeyHandler,
  ]);

  useEffect(() => {
    const node = ref.current;
    // Handle click
    if (focusOnClick) node?.addEventListener("click", focusSelfOnClick);
    else if (enterOnClick) node?.addEventListener("click", onClickHandler);

    return () => {
      // Handle click
      if (focusOnClick) node?.removeEventListener("click", focusSelfOnClick);
      else if (enterOnClick) node?.removeEventListener("click", onClickHandler);
    };
  }, [enterOnClick, focusOnClick, focusSelfOnClick, onClickHandler]);

  useEffect(() => {
    const node = ref.current;

    // Handle hover
    if (focusImplicitOnHover) {
      node?.addEventListener("mouseenter", focusSelfImplicit);
      node?.addEventListener("mouseleave", clearHoverTimer);
    }

    return () => {
      // Handle hover
      if (focusImplicitOnHover) {
        node?.removeEventListener("mouseenter", focusSelfImplicit);
        node?.removeEventListener("mouseleave", clearHoverTimer);
      }
    };
  }, [focusImplicitOnHover, focusSelfImplicit]);

  return {
    ref,
    focusSelf,
    focused,
    hasFocusedChild,
    focusKey, // returns either the same focusKey as passed in, or generated one
    setFocus: KeyHandler.isNativeMode() ? noop : KeyHandler.setFocus,
    navigateByDirection: KeyHandler.navigateByDirection,
    pause: KeyHandler.pause,
    resume: KeyHandler.resume,
    updateAllLayouts: KeyHandler.updateAllLayouts,
    getNodeLayoutByFocusKey: KeyHandler.getNodeLayoutByFocusKey,
  };
};

export const useFocusable = useFocusableHook;
