import { useContext, createContext } from "react";
import { ROOT_FOCUS_KEY } from "./VieOnNavigation";

type ContextType = "normal" | "dialog";

export const FocusContext = createContext(ROOT_FOCUS_KEY);

/** @internal */
export const useFocusContext = () => useContext(FocusContext);

export const FocusTypeContext = createContext<ContextType>("normal");

/** @internal */
export const useFocusTypeContext = () => useContext(FocusTypeContext);
