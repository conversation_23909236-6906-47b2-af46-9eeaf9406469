import KeepAlive, { KeepAliveData } from "app/utils/KeepAlive";
import {
  ADD_KEYWORD_SEARCH,
  DELETE_ALL_KEYWORD_SEARCH,
  DELETE_KEYWORD_SEARCH,
  RESET_SEARCH_STORE,
  UPDATE_ACTIVE_SUGGEST_ITEM_SEARCH,
  UPDATE_ERROR_SEARCH,
  UPDATE_FOCUS_RESULT_ITEM_SEARCH,
  UPDATE_FOCUS_SUGGEST_ITEM_SEARCH,
  UPDATE_FOCUS_SUGGEST_SEARCH,
  UPDATE_KEEP_ALIVE_DATA_SEARCH,
  UPDATE_NEXT_PAGE_SEARCH,
  UPDATE_PAGE_SEARCH,
  UPDATE_RESULT_BL<PERSON>_SEARCH,
  UPDATE_RESULT_LIST_SEARCH,
  UPDATE_RESULT_LOADING_SEARCH,
  UPDATE_SUGGEST_BLUR_SEARCH,
  UPDATE_SUGGEST_LIST_SEARCH,
  UPDATE_TIP_SEARCH,
  UPDATE_TOTAL_SEARCH,
} from "./actionTypes";

export type SearchInitialStateType = {
  tip: string;
  keyword: string;
  resultList: any[];
  resultLoading: boolean;
  resultBlur: boolean;
  focusResultItem: any | null;
  suggestList: any[];
  error: any;
  page: number;
  nextPage: any;
  total: number;
  suggestBlur: boolean;
  focusSuggest: boolean;
  focusSuggestItem: any | null;
  activeSuggestItem: number;
  keepAliveData: KeepAliveData | null;
};

export const initialSearchState: SearchInitialStateType = {
  tip: "để quay lại",
  keyword: "",
  page: 0,
  nextPage: null,
  total: 0,
  error: null,
  resultList: [],
  resultLoading: false,
  resultBlur: true,
  focusResultItem: null,
  suggestList: [],
  suggestBlur: false,
  focusSuggestItem: null,
  activeSuggestItem: -1,
  focusSuggest: false,
  keepAliveData: null,
};

const searchReducer = (
  state: SearchInitialStateType = initialSearchState,
  action: { type: string; [key: string]: any }
) => {
  switch (action?.type) {
    case ADD_KEYWORD_SEARCH:
      return {
        ...state,
        keyword: action?.value
          ? state.keyword + action?.value
          : initialSearchState?.keyword,
      };
    case DELETE_KEYWORD_SEARCH:
      return { ...state, keyword: state.keyword?.slice(0, -1).trim() };
    case DELETE_ALL_KEYWORD_SEARCH:
      return { ...state, keyword: "" };
    case UPDATE_TIP_SEARCH:
      return { ...state, tip: action?.value || initialSearchState.tip };
    case UPDATE_KEEP_ALIVE_DATA_SEARCH:
      return {
        ...state,
        keepAliveData: action?.value || initialSearchState.keepAliveData,
      };
    case UPDATE_PAGE_SEARCH:
      return { ...state, page: action?.value ?? initialSearchState.page };
    case UPDATE_NEXT_PAGE_SEARCH:
      return {
        ...state,
        nextPage: action?.value || initialSearchState.nextPage,
      };
    case UPDATE_TOTAL_SEARCH:
      return { ...state, total: action?.value ?? initialSearchState.total };
    case UPDATE_ERROR_SEARCH:
      return {
        ...state,
        error: action?.value || initialSearchState.error,
      };
    case UPDATE_RESULT_LIST_SEARCH:
      return {
        ...state,
        resultList: action?.value || initialSearchState.resultList,
      };
    case UPDATE_FOCUS_RESULT_ITEM_SEARCH:
      return {
        ...state,
        focusResultItem: action?.value || initialSearchState.focusResultItem,
      };
    case UPDATE_SUGGEST_LIST_SEARCH:
      return {
        ...state,
        suggestList: action?.value || initialSearchState.suggestBlur,
      };
    case UPDATE_SUGGEST_BLUR_SEARCH:
      return {
        ...state,
        suggestBlur: action?.value || initialSearchState.suggestBlur,
      };
    case UPDATE_FOCUS_SUGGEST_ITEM_SEARCH:
      return {
        ...state,
        focusSuggestItem: action?.value || initialSearchState.focusSuggestItem,
      };
    case UPDATE_ACTIVE_SUGGEST_ITEM_SEARCH:
      return {
        ...state,
        activeSuggestItem:
          action?.value ?? initialSearchState.activeSuggestItem,
      };
    case UPDATE_RESULT_LOADING_SEARCH:
      return {
        ...state,
        resultLoading: action?.value || initialSearchState.resultLoading,
      };
    case UPDATE_FOCUS_SUGGEST_SEARCH:
      return {
        ...state,
        focusSuggest: action?.value || initialSearchState.focusSuggest,
      };
    case UPDATE_RESULT_BLUR_SEARCH:
      return {
        ...state,
        resultBlur: action?.value,
      };
    case RESET_SEARCH_STORE:
      return { ...initialSearchState };
    default:
      return state;
  }
};

export default searchReducer;
