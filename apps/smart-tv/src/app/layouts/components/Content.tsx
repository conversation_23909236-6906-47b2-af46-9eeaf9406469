import React, { useEffect } from "react";
import { FocusContext, useFocusable } from "core/KeyHandle";
import backService from "services/backService";

function Content({
  children,
  focusKey: focusKeyProp,
  preferredChildFocusKey = "",
}: {
  children: React.ReactNode;
  focusKey: string;
  preferredChildFocusKey?: string;
}) {
  const { ref, focusKey, focusSelf } = useFocusable({
    focusKey: focusKeyProp,
    preferredChildFocusKey,
    onReturnPress: () => backService.back(),
  });

  useEffect(() => {
    focusSelf();
  }, [focusSelf]);

  return (
    <FocusContext.Provider value={focusKey}>
      <div className="content__control" ref={ref} data-label="focus control" />
      {children}
    </FocusContext.Provider>
  );
}

export default Content;
