import React, { memo, useCallback, useMemo } from "react";
import { useSelector } from "react-redux";
import {
  Redirect,
  Route,
  RouteComponentProps,
  Switch,
  useHistory,
  useLocation,
} from "react-router-dom";
import LogoWithCate from "app/components/Common/LogoWithCate";
import ErrorPage from "app/containers/Error";
import LiveStream from "app/containers/LiveStream/LiveStream";
import LiveTVNewUI from "app/containers/LiveTVNewUI/LiveTV";
import Sport from "app/containers/Sport";
import AuthenticationMenu from "app/containers/Authentication/AuthenticationMenu";
import Video from "app/containers/Video";
import { RootState } from "app/store/store";
import { makeRoute } from "app/utils/Route";
import useKeyService from "hooks/useKeyService";
import { FOCUS_KEY_CONTENT, FOCUS_KEY_SIDEBAR } from "app/App";
import { ContextAuthenVerifyProvider } from "app/containers/Authentication/Context/ContextAuthenVerify";
import ContextAuthenLoginProvider from "app/containers/Authentication/Context/ContextAuthenLogin";
import Notification from "app/containers/Notification";
import Schedule from "app/containers/Schedule";
import SearchV2 from "app/containers/SearchV2";
import LiveTVV2 from "app/containers/LiveTVV2";
import useNetwork from "app/containers/Network/hooks";
import SideBar from "./components/SideBar";
import Content from "./components/Content";
import { ROUTES } from "../utils/constants";
import { getFirstMenuMustHomePage } from "../routines/common";

// const SearchV2 = React.lazy(() => import("app/containers/SearchV2"));

// const LiveTVV2 = React.lazy(() => import("app/containers/LiveTVV2"));

interface Props {}

const LayoutSideMenu: React.FC<Props> = React.memo(function LayoutSideMenu() {
  const menus = useSelector((state: RootState) => state.app.menus);
  const isShowMastheadAds = useSelector(
    (state: RootState) => state.app.mastheadAds.status !== "NONE"
  );
  const location = useLocation();
  const history = useHistory();
  /* usePersonal(true); */
  const keyHandler = (e: KeyboardEvent) => {
    switch (focusIndexRef.current) {
      case 0:
        if (keyHandlerFn.page && typeof keyHandlerFn.page === "function") {
          keyHandlerFn.page(e);
        }
        break;
      case 1:
        if (
          keyHandlerFn.sidebar &&
          typeof keyHandlerFn.sidebar === "function"
        ) {
          keyHandlerFn.sidebar(e);
        }
        break;
      default:
        break;
    }
  };
  const isAuthen = useSelector((state: RootState) => state.app.isAuthen);
  const [
    focusIndex,
    _setFocusIndex,
    focusIndexRef,
    registerKeyHandler,
    unregisterKeyHandler,
    keyHandlerFn,
    onPopoutFactory,
  ] = useKeyService({
    keyHandler,
  });

  const onPagePopout = onPopoutFactory({ left: 1, up: 1 });

  function renderLoginPage({ location }: RouteComponentProps<{ id: string }>) {
    if (isAuthen) {
      history.replace(ROUTES.MULTI_PROFILE_LOBBY);
      return null;
    }
    return (
      <ContextAuthenVerifyProvider>
        <ContextAuthenLoginProvider>
          <AuthenticationMenu key={location.pathname} />
        </ContextAuthenLoginProvider>
      </ContextAuthenVerifyProvider>
    );
  }

  const renderSearchPage = useMemo(() => {
    return <SearchV2 key={location.pathname} />;
  }, [location.pathname]);

  const renderLiveTVV2Page = useMemo(() => {
    return <LiveTVV2 />;
  }, [location.pathname]);

  function renderNotificationPage() {
    if (!isAuthen) {
      history.replace(ROUTES.LOGIN);
      return null;
    }
    return <Notification />;
  }

  function renderHomePage() {
    const menuHomepage = getFirstMenuMustHomePage(menus);
    return (
      <Redirect to={menuHomepage ? makeRoute(menuHomepage) : ROUTES.ROOT} />
    );
  }

  function renderLiveStreamPage({
    location,
  }: RouteComponentProps<{ id: string }>) {
    return (
      <LiveStream
        key={location.pathname}
        focus={focusIndex === 0}
        registerKey={[
          registerKeyHandler.bind(null, "page"),
          unregisterKeyHandler.bind(null, "page"),
        ]}
        onPopout={onPagePopout}
      />
    );
  }

  return (
    <div className="wrap">
      <LogoWithCate
        focusSideBar={focusIndex === 1}
        className="logo logo--top"
        showMastheadAds={isShowMastheadAds}
      />
      <SideBar focusKey={FOCUS_KEY_SIDEBAR} />
      <Content focusKey={FOCUS_KEY_CONTENT}>
        <Switch>
          <Route path={ROUTES.LOGIN}>{renderLoginPage}</Route>
          <Route path={ROUTES.SEARCH}>{renderSearchPage}</Route>
          <Route path={ROUTES.NOTIFICATION}>{renderNotificationPage}</Route>
          <Route path={`${ROUTES.SCHEDULE}/:subMenuId`} component={Schedule} />
          <Route path={ROUTES.SCHEDULE} component={Schedule} />
          <Route path={`${ROUTES.VOD}/:id`}>
            <Video key={location.pathname} />
          </Route>
          {/* <Route
            path={`${ROUTES.LIVE_TV_NEW_UI}/:subMenuId`}
            component={LiveTVNewUI}
          /> */}
          <Route path={ROUTES.LIVE_TV_NEW_UI}>{renderLiveTVV2Page}</Route>
          <Route path={`${ROUTES.LIVE_STREAM}/:id`}>
            {renderLiveStreamPage}
          </Route>
          <Route path={ROUTES.ERROR}>
            <ErrorPage key={location.pathname} />
          </Route>
          <Route path={`${ROUTES.SPORT_FOOTBALL}/:id`}>
            <Sport key={location.pathname} />{" "}
          </Route>
          <Route path="/">{renderHomePage}</Route>
        </Switch>
      </Content>
    </div>
  );
});

export default memo(LayoutSideMenu);
