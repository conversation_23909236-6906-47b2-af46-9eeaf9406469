import React, { useEffect, useRef, useState, useCallback } from "react";
import { useHistory, useLocation } from "react-router-dom";
import get from "lodash/get";
import classNames from "classnames";
import useStateRef from "hooks/useStateRef";
import * as api from "services/endpoint";
import Input from "app/components/Common/Input/Input";
import NumberQWERTYKeyboard from "app/components/Common/KeyBoard/NumberQWERTYKeyboard";
import KeepAlive, { KeepAliveData } from "app/utils/KeepAlive";
import Icon from "app/components/Common/Icon";
import SegmentManager from "app/utils/SegmentManager";
import { ROUTES } from "app/utils/constants";
import DialogCommon from "app/components/DialogCommon";
import Button from "app/containers/Error/components/Button";
import FocusableWrapper from "app/components/FocusableWrapper";
import { <PERSON>ter<PERSON><PERSON><PERSON><PERSON><PERSON>, FocusContext } from "core/KeyHandle";

interface Props {
  phoneNumber: string;
  keepAliveData?: KeepAliveData;
  onSuccess: (data: any) => void;
  onBack: () => void;
}

const MIN_PASSWORD_LENGTH = 6;

const Step2: React.FC<Props> = ({
  phoneNumber,
  keepAliveData,
  onSuccess,
  onBack,
}) => {
  const history = useHistory();
  const location = useLocation<any>();
  const keepAliveDataRef = useRef(keepAliveData);
  const [capslock, setCapslock] = useState(false);
  const [error, setError, errorRef] = useStateRef("");
  const [password, setPassword, passwordRef] = useStateRef("");
  const [passwordVisible, setPasswordVisible, passwordVisibleRef] =
    useStateRef(false);

  useEffect(() => {
    if (keepAliveData && keepAliveData.extra.step === 2) {
      setCapslock(keepAliveData.extra.capslock);
      setError(keepAliveData.extra.error);
      setPassword(keepAliveData.extra.password);
      setPasswordVisible(keepAliveData.extra.passwordVisible);
    }
  }, [keepAliveData]);

  const onKeyboardEnter = (currentKey: string) => {
    const currentKeyLower = currentKey.toLowerCase();
    switch (currentKeyLower) {
      case "capslock": {
        setCapslock(!capslock);
        break;
      }
      case "clear": {
        setPassword("");
        break;
      }
      case "backspace": {
        setPassword(passwordRef.current.slice(0, -1));
        break;
      }
      default: {
        if (passwordRef.current.length === 20) return;
        setPassword(passwordRef.current + currentKey);
        break;
      }
    }
  };

  const onButtonEnter = useCallback<
    EnterPressHandler<{
      type?: string;
    }>
  >(async ({ type }) => {
    switch (type) {
      case "BACK":
        onBack();
        break;
      case "SIGN_IN":
        setError("");
        await api
          .loginByPassword(phoneNumber, passwordRef.current, (error: any) => {
            const errorData = get(error, ["response", "data", "data"]);
            const errorMesssage = get(error, ["response", "data", "message"]);
            if (errorData === "No_exist") {
              DialogCommon.commonMinimalDialog({
                keyName: "dialog_minimal_signinphone_step2_sign_up",
                type: "success",
                title: "",
                // className: "popup__phone-used",
                description:
                  "Số điện thoại này hiện chưa đăng ký tài khoản VieON. Bạn có muốn đăng ký ngay không?",
                hideMask: true,
                actions: [
                  {
                    title: "Đăng ký",
                    onClick: () => {
                      history.replace({
                        ...location,
                        pathname: ROUTES.SIGNUP_PAGE,
                        state: {
                          ...location.state,
                          phoneNumber,
                        },
                      });
                      SegmentManager.segmentAction(
                        "registration button selected",
                        {
                          current_page: "Đăng nhập",
                          popup_name: "Gợi ý đăng ký từ đăng nhập",
                        }
                      );
                    },
                  },
                  {
                    title: "Bỏ qua",
                    onClick: () => {},
                  },
                ],
              });
            } else setError(errorMesssage);
          })
          .then((data: any) => {
            onSuccess(data);
          })
          .catch((error: any) => {});
        break;
      case "FORGOT_PASSWORD":
        if (keepAliveDataRef.current) {
          keepAliveDataRef.current.extra.step = 2;
          keepAliveDataRef.current.extra.phoneNumber = phoneNumber;
          keepAliveDataRef.current.extra.capslock = capslock;
          keepAliveDataRef.current.extra.error = errorRef.current;
          keepAliveDataRef.current.extra.password = passwordRef.current;
          keepAliveDataRef.current.extra.passwordVisible =
            passwordVisibleRef.current;
          KeepAlive.saveData(keepAliveDataRef.current);
        }
        history.replace({ ...location, pathname: ROUTES.RESET_PASSWORD });
        break;
      case "SHOW_PASSWORD":
        setPasswordVisible(!passwordVisibleRef.current);
        break;
      default:
        break;
    }
  }, []);

  return (
    <div className="block block--login block--login-otp">
      <div className="grid">
        <div className="col col-6 panel-left triangle-right-2">
          <h3 className="block__title">Nhập mật khẩu</h3>
          <NumberQWERTYKeyboard
            loop="all"
            capslock={capslock}
            onEnter={onKeyboardEnter}
            onReturn={onBack}
          />
          <FocusableWrapper
            enterOnClick
            focusImplicitOnHover
            saveLastFocusedChild={false}
            preferredChildFocusKey="VN:STEP2_SIGN_IN"
            focusKey="VN:STEP2_BUTTON"
            onClick={() => {}}
          >
            {({ ref, focused }) => {
              return (
                <FocusContext.Provider value="VN:STEP2_BUTTON">
                  <div className="button-group" ref={ref}>
                    <Button
                      onEnter={onButtonEnter}
                      onReturn={onBack}
                      className="button"
                      focusKey="VN:STEP2_BACK"
                      type="BACK"
                    >
                      Quay lại
                    </Button>
                    <Button
                      onReturn={onBack}
                      onEnter={onButtonEnter}
                      disabled={password.length < MIN_PASSWORD_LENGTH}
                      className="button"
                      focusKey="VN:STEP2_SIGN_IN"
                      type="SIGN_IN"
                    >
                      Đăng nhập
                    </Button>
                  </div>
                </FocusContext.Provider>
              );
            }}
          </FocusableWrapper>
        </div>
        <div className="col col-6 panel-right">
          <div className="muted">
            <span className="icon">
              <Icon name="vie-hand-phone-o-rc" />
            </span>
            <p className="hint">{phoneNumber}</p>
          </div>
          <div className="muted">
            <span className="icon">
              <Icon name="vie-key-skeleton-o" />
            </span>
            <Input
              className={classNames("input", {
                password: !passwordVisible,
                warning: capslock && error === "",
              })}
              placeholder="Mật khẩu (6-20 ký tự)"
              value={password}
            />
          </div>
          <p
            className={classNames("error", {
              hide: error === "",
            })}
          >
            {error}
          </p>
          <p
            className={classNames("warning", {
              hide: !capslock || error !== "",
            })}
          >
            Bạn đang mở CAPSLOCK
          </p>
        </div>
        <div className="col col-12 panel-bottom">
          <div className="button-group">
            <Button
              onReturn={onBack}
              onEnter={onButtonEnter}
              className="button small"
              focusKey="VN:STEP2_FORGOT_PASSWORD"
              type="FORGOT_PASSWORD"
            >
              Quên mật khẩu
            </Button>
            <Button
              onEnter={onButtonEnter}
              disabled={phoneNumber.length !== 10}
              className="button small"
              focusKey="VN:STEP2_SHOW_PASSWORD"
              type="SHOW_PASSWORD"
            >
              {passwordVisible ? "Ẩn mật khẩu" : "Hiện mật khẩu"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Step2;
