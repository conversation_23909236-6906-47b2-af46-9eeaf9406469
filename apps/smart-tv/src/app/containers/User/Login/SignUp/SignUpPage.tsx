import React, { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useHistory, useLocation } from "react-router-dom";
import get from "lodash/get";
import * as multiProfile<PERSON>pi from "app/endpoint/MultiProfile/endpoint";
import useStateRef from "hooks/useStateRef";
import { getUserType, getDailyPermission } from "services/endpoint";
import Logo from "app/components/Common/Logo";
import { loginSuccess } from "app/store/actions";
import TipBox from "app/components/Common/Portal/TipBox";
import GAManager from "app/utils/GAManager";
import SegmentManager from "app/utils/SegmentManager";
import Message from "app/components/Common/Message";
import KeepAlive, { KeepAliveData } from "app/utils/KeepAlive";
import backService from "services/backService";
import { useGAPageViewByLocation } from "hooks/useGAPageViewByLocation";
import { RootState } from "app/store/store";
import { ROUTES } from "app/utils/constants";
import { FocusContext, useFocusable } from "core/KeyHandle";
import { MESSAGES } from "types/common/Message";
import { useContextTrackLocationState } from "context/ContextTrackLocation";
import { AuthenticationMethod } from "types/common";
import { AccountModel } from "app/models";
import Step1 from "./Step1";
import Step2 from "./Step2";
import Step3 from "./Step3";

const SignUpPage: React.FC = ({}) => {
  const [keepAliveData, setKeepAliveData, keepAliveDataRef] = useStateRef({
    path: window.location.hash,
    focus: {
      x: 0,
      y: 0,
    },
    extra: {
      phoneNumber: "",
      password: "",
      focusStep1: { x: 0, y: 0 },
      focusStep2: { x: 0, y: 0 },
      focusStep3: { x: 0, y: 0 },
    },
  } as KeepAliveData);

  const guestFlow = useSelector((state: RootState) => state.app.guestFlow);
  const userFlow = useSelector((state: RootState) => state.app.userFlow);
  const history = useHistory();
  const location = useLocation<any>();
  const dispatch = useDispatch();
  const [step, setStep, stepRef] = useStateRef(1);
  const [phoneNumber, setPhoneNumber] = useState("");
  const [registerSessionId, setRegisterSessionId] = useState("");
  const [password, setPassword] = useState("");
  const isFetching = useRef<boolean>(false);

  const trackLocation = useContextTrackLocationState();

  const { ref, focusKey, setFocus } = useFocusable({
    focusKey: "VN:SIGN_UP_PAGE",
    preferredChildFocusKey:
      step === 2 ? `VN:STEP${step}_SIGN_UP` : `VN:STEP${step}_CONTINUE`,
  });

  useEffect(() => {
    (async function init() {
      const keepAliveDt = KeepAlive.getData(window.location.hash);
      if (keepAliveDt) {
        setKeepAliveData(keepAliveDt);
        setStep(keepAliveDt.extra.step);
        setPhoneNumber(keepAliveDt.extra.phoneNumber);
      }
    })();
  }, []);

  useGAPageViewByLocation();
  const locationRef = useRef<any>(location);
  useEffect(() => {
    locationRef.current = location;
    const phoneNumber = get(location.state, "phoneNumber");
    if (phoneNumber && phoneNumber !== "") {
      setPhoneNumber(phoneNumber);
    }
  }, [location]);

  const backwardStep = (s: number) => {
    keepAliveDataRef.current.extra.step = s;
    setStep(s);
    if (s === 2) {
      setFocus(`VN:STEP${s}_SIGN_UP`);
    } else {
      setFocus(`VN:STEP${s}_CONTINUE`);
    }
  };

  const onStep1Success = (phoneNumber: string, keepAliveDt: KeepAliveData) => {
    setKeepAliveData(keepAliveDt);
    delete keepAliveDataRef.current.extra.step;

    setPhoneNumber(phoneNumber);
    setStep(2);
  };

  const onStep2Success = (
    data: any,
    password: string,
    keepAliveDt: KeepAliveData
  ) => {
    if (data.access_token) {
      onStep3Success(data);
      return;
    }
    setKeepAliveData(keepAliveDt);
    delete keepAliveDataRef.current.extra.step;

    setRegisterSessionId(data.register_session_id);
    setPassword(password);
    setStep(3);
  };

  const onStep3Success = async (data: any) => {
    if (isFetching.current) return;
    isFetching.current = true;
    const token = get(data, "access_token") as string;
    const profile = get(data, "profile") as AccountModel;
    const resUserType: {
      [key: string]: any;
    } = await getUserType(token).catch(() => ({}));
    const resPackagePermission: {
      [key: string]: any;
    } = await getDailyPermission(token).catch(() => ({}));
    isFetching.current = false;

    const userType = get(resUserType, "type", 0);
    const packageGroupId = get(resUserType, "package_group_id", 0);
    const livetvGroupId = get(resUserType, "livetv_group_id", "");
    const hideBuyPackage = get(resUserType, "hide_button_buy_package", false);
    const needShowOverlap = get(
      resPackagePermission,
      "paid_message_need",
      false
    );
    const overlapInfo = {
      msg: "",
      btnText: "",
    };
    overlapInfo.msg = get(resPackagePermission, "paid_message", "");
    overlapInfo.btnText = get(
      resPackagePermission,
      "primary_button_message",
      ""
    );

    await dispatch(
      loginSuccess({
        token,
        refreshToken: "",
        profile,
        userType,
        packageGroupId,
        livetvGroupId,
        hideBuyPackage,
        needShowOverlap,
        overlapInfo,
      })
    );

    multiProfileApi.updateAgreement(true);
    trackingData(profile);
    // TODO: Optimize redirect flow to pervious page
    // Redirect
    const urlParams = new URLSearchParams(location.search);
    const redirectTo = urlParams.get("redirect");
    const state = urlParams.get("state");
    let search = "";
    if (state) {
      const nextUrlParams = new URLSearchParams();
      nextUrlParams.append("state", state);
      search = nextUrlParams.toString();
    }
    // Tracking Start
    let locationState;
    if (redirectTo && redirectTo.indexOf("payment") >= 0) {
      locationState = {
        referal: "Đăng ký",
      };
    }

    // Tracking End
    let nextLocation: any = {
      pathname: redirectTo || "/",
      search,
      state: locationState,
    };
    if (profile.showGuestidFlow && userFlow) {
      if (!redirectTo || redirectTo.indexOf("promotion") < 0) {
        // uu tien flow promotion
        nextLocation = {
          pathname: ROUTES.PERSONALIZATION,
        };
      }
    }
    // else {
    //   const successMsg = get(location, ["state", "successMsg"]);
    //   Message.open(successMsg || "Chào mừng bạn đến với VieON");
    // }
    if (redirectTo && redirectTo.indexOf(ROUTES.LIVE_TV) >= 0) {
      // check Livetv login add yêu thích
      const addFavorite = urlParams.get("state") === "addFavorite";
      history.replace({
        pathname: ROUTES.MULTI_PROFILE_LOBBY,
        state: {
          ...location.state,
          redirect: nextLocation,
          authenticationMethod: AuthenticationMethod.SignUp,
          isBack: addFavorite,
        },
      });
      return;
    }
    const successLocation = get(location, ["state", "successLocation"]);

    if (
      successLocation &&
      successLocation?.pathname &&
      !successLocation.pathname.includes("payment")
    ) {
      setTimeout(() => {
        history.replace({
          pathname: ROUTES.MULTI_PROFILE_LOBBY,
          state: {
            ...location.state,
            redirect: successLocation,
            authenticationMethod: AuthenticationMethod.SignUp,
            isBack: true,
          },
        });
      }, 100);
      return;
    }

    if (
      successLocation &&
      successLocation?.pathname &&
      successLocation.pathname.includes("payment")
    ) {
      history.goBack();
      if (successLocation?.state) {
        successLocation.state.authenticationMethod =
          AuthenticationMethod.SignUp;
      }
      setTimeout(() => {
        history.replace(successLocation);
        const successMsg = get(location, ["state", "successMsg"]);
        Message.open(successMsg || MESSAGES.welcomeBack);
      }, 100);
      return;
    }

    setTimeout(() => {
      history.replace({
        pathname: ROUTES.MULTI_PROFILE_LOBBY,
        state: {
          ...location.state,
          redirect: trackLocation || nextLocation,
          authenticationMethod: !redirectTo
            ? AuthenticationMethod.SignUpShowLobbby
            : AuthenticationMethod.SignUp,
          isBack: true,
        },
      });
    }, 100);
  };

  const trackingData = (profile: AccountModel) => {
    GAManager.gaSetUser(profile.id);
    GAManager.gaEvent("signup_successfully", "sign_up", "signup_successfully");
    GAManager.gaEvent("login_successfully", "log_in", "login_successfully");
    const search = get(locationRef, ["current", "search"], "");
    const urlParams = new URLSearchParams(search);
    const trigger = urlParams.get("trigger") || "";
    const flow = urlParams.get("flow");
    if (trigger === "registration-banner") {
      SegmentManager.segmentAction("sign_up_successfully", {
        flow_name:
          "Từ luồng bấm bannner kêu gọi Đăng ký trên ribbon bạn Có thể bạn sẽ thích",
      });
    } else {
      SegmentManager.segmentAction("sign up successfully", {
        flow_name: flow || "",
      });
    }
    SegmentManager.segmentIdentify(profile);
    SegmentManager.segmentAction("login successfully", {
      log_in_method: "phone",
      is_auto: false,
      flow_name: flow || "",
    });
  };

  useEffect(() => {
    SegmentManager.segmentAction("sign up", {
      current_page: "Đăng ký",
    });
  }, []);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const from = searchParams.get("from");
    const backHandler = () => {
      if (from) {
        searchParams.delete("from");
        history.replace({
          pathname: from,
          search: searchParams.toString(),
        });
      } else {
        history.goBack();
        const successLocation = get(location, ["state", "successLocation"]);
        if (successLocation) {
          setTimeout(() => {
            history.replace(successLocation);
          }, 100);
        }
      }
    };
    backService.register(backHandler);
    return () => {
      backService.unregister(backHandler);
    };
  }, []);

  return (
    <FocusContext.Provider value={focusKey}>
      <div className="main main--user main--user-login non-bg" ref={ref}>
        <Logo className="logo logo--top" />
        <div className="main__header" />
        <div className="main__body">
          <section className="section section--login section--login-app">
            <div className="section__wrap">
              {step === 1 && (
                <Step1
                  keepAliveData={keepAliveData}
                  phoneNumber={phoneNumber}
                  onSuccess={onStep1Success}
                />
              )}
              {step === 2 && (
                <Step2
                  keepAliveData={keepAliveData}
                  password={password}
                  onSuccess={onStep2Success}
                  onSignupSuccess={onStep3Success}
                  phoneNumber={phoneNumber}
                  onBack={() => backwardStep(1)}
                />
              )}
              {step === 3 && (
                <Step3
                  onSuccess={onStep3Success}
                  onBack={() => backwardStep(2)}
                  phoneNumber={phoneNumber}
                  registerSessionId={registerSessionId}
                />
              )}
            </div>
          </section>
        </div>
        <TipBox tip="để quay lại" />
      </div>
    </FocusContext.Provider>
  );
};
export default SignUpPage;
