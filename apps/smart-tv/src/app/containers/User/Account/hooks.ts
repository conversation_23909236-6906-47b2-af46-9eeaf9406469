import produce from "immer";
import get from "lodash/get";
import merge from "lodash/merge";
import { useEffect, useReducer, useRef, useCallback } from "react";
import { useSelector } from "react-redux";
import { RootState } from "app/store/store";
import * as api from "services/endpoint";
import { IDevice } from "types/endpoint/Device";

export enum DeviceError {
  NoError,
  InitFailed,
  DisabledFailed,
}
type State = {
  isLoading: boolean;
  isLoadingDisabled: boolean;
  error: number;
  devices: IDevice[];
  currentDevice: IDevice;
};

type Action =
  | { type: "initStart" }
  | {
      type: "initSuccess";
      payload: {
        devices: IDevice[];
        currentDevice: IDevice;
      };
    }
  | { type: "initFail" }
  | { type: "disableStart" }
  | {
      type: "disableSuccess";
      payload: {
        devices: IDevice[];
      };
    }
  | { type: "disableFail" }
  | {
      type: "toggleSelection";
      payload: {
        deviceId: string;
        isSelected: boolean;
      };
    }
  | {
      type: "toggleSelectionAll";
      payload: {
        isSelected: boolean;
      };
    };
type Reducer<S, A> = (prevState: S, action: A) => S;
const initialState = {
  isLoading: true,
  error: DeviceError.NoError,
  currentDevice: {} as IDevice,
  devices: [] as IDevice[],
  isLoadingDisabled: false,
};

export type toggleSelectionAllType = ({
  isSelected,
}: {
  isSelected: boolean;
}) => void;

export type toggleSelectionType = ({
  deviceId,
  isSelected,
}: {
  deviceId: string;
  isSelected: boolean;
}) => void;

export type handleRemoveDevicesType = (
  devices: IDevice[],
  callback?: (status: boolean) => void
) => void;

/* ====== REDUCER ====== */
const reducer: Reducer<State, Action> = (state, action) => {
  switch (action.type) {
    case "initStart":
      return produce(state, (draft) => {
        merge(draft, { isLoading: true, error: 0 });
      });
    case "initSuccess":
      return produce(state, (draft) => {
        merge(draft, {
          ...action.payload,
          error: DeviceError.NoError,
          isLoading: false,
          isLoadingDisabled: false,
        });
      });
    case "initFail":
      return produce(state, (draft) => {
        merge(draft, {
          error: DeviceError.InitFailed,
          isLoading: false,
          isLoadingDisabled: false,
        });
      });
    case "disableStart":
      return produce(state, (draft) => {
        merge(draft, {
          error: DeviceError.NoError,
          isLoading: false,
          isLoadingDisabled: true,
        });
      });
    case "disableSuccess": {
      const ids = action.payload.devices.map((i) => i.deviceId);
      return {
        ...state,
        devices: state.devices.filter((i) => !ids.includes(i.deviceId)),
        error: DeviceError.DisabledFailed,
        isLoading: false,
        isLoadingDisabled: false,
      };
    }
    case "disableFail":
      return produce(state, (draft) => {
        merge(draft, {
          error: DeviceError.DisabledFailed,
          isLoading: false,
          isLoadingDisabled: false,
        });
      });
    case "toggleSelection": {
      return {
        ...state,
        devices: state.devices.map((item) => {
          if (item.deviceId === action.payload.deviceId) {
            return {
              ...item,
              profile: {
                ...item.profile,
              },
              isSelected: action.payload.isSelected,
            };
          }
          return {
            ...item,
            profile: {
              ...item.profile,
            },
          };
        }),
      };
    }
    case "toggleSelectionAll": {
      return {
        ...state,
        devices: state.devices.map((item) => {
          return {
            ...item,
            profile: {
              ...item.profile,
            },
            isSelected: action.payload.isSelected,
          };
        }),
      };
    }
    default:
      return state;
  }
};

/* ====== END REDUCER ====== */
const useDeviceData = (): {
  state: State;
  handleRemoveDevices: handleRemoveDevicesType;
  stateRef: React.MutableRefObject<State>;
  toggleSelection: toggleSelectionType;
  toggleSelectionAll: toggleSelectionAllType;
} => {
  const isAuthen = useSelector((state: RootState) => state.app.isAuthen);
  const [state, dispatch] = useReducer(
    reducer,
    merge(initialState, { isAuthen })
  );
  const stateRef = useRef<State>(state);
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  useEffect(() => {
    (async () => {
      try {
        dispatch({ type: "initStart" });
        const res = await api.getDevices();
        const currentDevice = get(res, "current_device", {});
        const otherDevice = get(res, "other_devices", []);
        dispatch({
          type: "initSuccess",
          payload: {
            currentDevice: new IDevice(
              { ...currentDevice, is_active: 1 },
              "smarttv"
            ),
            devices: otherDevice.map((i: any) => new IDevice(i)),
          },
        });
      } catch (err) {
        dispatch({
          type: "initFail",
        });
      }
    })();
  }, []);

  const handleRemoveDevices = useCallback(
    (devices: IDevice[], callback?: (status: boolean) => void) => {
      (async () => {
        try {
          dispatch({ type: "disableStart" });
          const res = await api.userDisabledDevices(devices);
          const isSuccess = get(res, "success", false);
          if (callback && typeof callback === "function") {
            callback?.(isSuccess);
          }
          if (isSuccess) {
            dispatch({
              type: "disableSuccess",
              payload: {
                devices,
              },
            });
          } else {
            dispatch({ type: "disableFail" });
          }
        } catch (err) {
          if (callback && typeof callback === "function") {
            callback(false);
          }
          dispatch({ type: "disableFail" });
        }
      })();
    },
    []
  );

  const toggleSelection = useCallback(
    ({ deviceId, isSelected }: { deviceId: string; isSelected: boolean }) => {
      dispatch({
        type: "toggleSelection",
        payload: {
          deviceId,
          isSelected,
        },
      });
    },
    []
  );

  const toggleSelectionAll: toggleSelectionAllType = useCallback(
    ({ isSelected }) => {
      dispatch({
        type: "toggleSelectionAll",
        payload: {
          isSelected,
        },
      });
    },
    []
  );

  return {
    state,
    handleRemoveDevices,
    stateRef,
    toggleSelection,
    toggleSelectionAll,
  };
};

export default useDeviceData;
