@use "src/assets/scss/settings/function" as fn;
@use "src/assets/scss/settings/palettes" as pales;
@use "src/assets/scss/mixin/pseudo" as pseudo;
@use "src/assets/scss/mixin/layer" as layer;
@use "src/assets/scss/mixin/position" as position;

.user-restriction-content {
  margin-left: fn.percent-unit(50);
  height: fn.percent-unit(850);
  display: flex;
  box-sizing: border-box;
  padding-top: fn.percent-unit(60);
  position: relative;
  //&::before {
  //  display: block;
  //  content: "";
  //  width: fn.percent-unit(100%);
  //  height: fn.percent-unit(84);
  //  background: -webkit-linear-gradient(bottom, #111, rgba(17, 17, 17, 0));
  //  background: linear-gradient(180deg, #111, rgba(17, 17, 17, 0));
  //  top: fn.percent-unit(-8);
  //  position: absolute;
  //  left: 0;
  //  z-index: 1;
  //}
  //&::after {
  //  z-index: 1;
  //  display: block;
  //  content: "";
  //  width: fn.percent-unit(100%);
  //  height: fn.percent-unit(84);
  //  bottom: 0;
  //  position: absolute;
  //  left: 0;
  //  background: -webkit-linear-gradient(bottom, #111, rgba(17, 17, 17, 0));
  //  background: linear-gradient(0deg, #111, rgba(17, 17, 17, 0));
  //}
  &__menu {
    width: fn.percent-unit(432);
    .profiles {
      display: flex;
      flex-direction: column;
      .profile-item {
        display: flex;
        box-sizing: border-box;
        padding: fn.percent-unit(16) fn.percent-unit(20);
        width: fn.percent-unit(392);
        height: fn.percent-unit(89);
        margin-bottom: fn.percent-unit(24);
        border-radius: fn.percent-unit(4);
        color: pales.$gray-de;
        position: relative;
        &.focused {
          background: pales.$white;
          color: pales.$black;
        }
        &.active {
          //border: fn.percent-unit(2) solid pales.$gray-9b;
          &:after {
            @include pseudo.pseudo(
              $width: fn.percent-unit(8),
              $height: fn.percent-unit(100%),
              $display: block
            );
            border-top-right-radius: fn.percent-unit(8);
            border-bottom-right-radius: fn.percent-unit(8);
            @include position.absolute(left fn.percent-unit(0) top 50%);
            @include layer.layers(layer-min);
            background-color: pales.$green-3a;
            transform: translateY(-50%);
          }
        }
        &__avatar {
          align-self: center;
          margin-right: fn.percent-unit(16);
          position: relative;
          img {
            width: fn.percent-unit(56);
            height: fn.percent-unit(56);
            border-radius: fn.percent-unit(56);
          }
          img.kid-icon {
            position: absolute;
            right: fn.percent-unit(0);
            top: fn.percent-unit(0);
            width: fn.percent-unit(16);
            height: fn.percent-unit(16);
          }
        }
        &__name {
          align-self: center;
          font-weight: 400;
          font-size: fn.percent-unit(28);
          line-height: fn.percent-unit(42);
        }
      }
    }
  }
  &__content-panel {
    width: fn.percent-unit(1228);
    display: flex;
    .empty-focused {
      color: #6c6c6c;
      font-size: fn.percent-unit(28);
      font-weight: 400;
      line-height: fn.percent-unit(48);
      width: fn.percent-unit(100%);
      height: fn.percent-unit(100%);
      display: flex;
      border-left: fn.percent-unit(1) solid pales.$gray-33;
      padding-top: fn.percent-unit(22%);
      padding-left: fn.percent-unit(305);
    }
    .content-container {
      width: fn.percent-unit(818);
      height: fn.percent-unit(850);
      border-left: fn.percent-unit(1) solid pales.$gray-33;
      border-right: fn.percent-unit(1) solid pales.$gray-33;
      overflow: hidden;
      position: relative;
      .scroll-box {
        width: fn.percent-unit(818);
        height: fn.percent-unit(850);
        position: absolute;
        top: fn.percent-unit(-25);
        left: 0;
      }
      .content-list {
        padding: fn.percent-unit(0) fn.percent-unit(38);
        height: fn.percent-unit(100%);
        &__item {
          box-sizing: border-box;
          padding: fn.percent-unit(20);
          width: fn.percent-unit(738);
          height: fn.percent-unit(147);
          color: pales.$white;
          background: #22222280;
          border-radius: fn.percent-unit(8);
          border: fn.percent-unit(3) solid rgba(255, 255, 255, 0.2);
          margin-bottom: fn.percent-unit(12);
          display: flex;
          &.focused {
            border: fn.percent-unit(4) solid pales.$white;
            background-color: pales.$white;
            color: pales.$black;
          }
          .title {
            width: fn.percent-unit(568);
            height: fn.percent-unit(84);
            font-size: fn.percent-unit(28);
            font-weight: 400;
            line-height: fn.percent-unit(42);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          .action {
            border-left: fn.percent-unit(1) dashed pales.$white;
            flex: auto 1 1;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            .btn-remove {
              width: fn.percent-unit(82);
              padding: 0;
              &.focus {
                background-color: pales.$black;
                color: pales.$white;
              }
            }
          }
        }
      }

      .not-restriction-content {
        height: fn.percent-unit(100%);
        display: flex;
        padding-top: fn.percent-unit(15%);
        justify-content: center;
        img {
          width: fn.percent-unit(524);
          height: fn.percent-unit(346);
        }
      }
    }

    .actions {
      padding: fn.percent-unit(0) fn.percent-unit(40);
      .btn {
        width: fn.percent-unit(320);
      }
      .btn:first-child {
        margin-bottom: fn.percent-unit(24);
      }
    }
  }
  .non-profile-kids-default {
    width: fn.percent-unit(657);
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    .hint {
      font-size: fn.percent-unit(28);
      line-height: fn.percent-unit(42);
      color: pales.$white;
      font-weight: 400;
    }
  }
}
