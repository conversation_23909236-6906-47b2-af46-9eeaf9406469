import React, { useCallback, useMemo, useRef, useEffect } from "react";
import classnames from "classnames";
import { FocusHandler, useFocusable } from "core/KeyHandle";

interface Props {
  type: Package.SubscriptionTypes;
  data: Partial<
    Package.SubscriptionUseItem &
      Package.SubscriptionRemainItem &
      Package.SubscriptionExpiredItem
  >;
  listNodeRef: React.RefObject<HTMLDivElement>;
  subListNodeRef: React.RefObject<HTMLDivElement>;
  titleRef: React.RefObject<HTMLDivElement>;
  index: number;
  onPopout?: VoidFunction;
}

const FOCUS_KEY_TEMPLATE = "VN:SUB_ITEM";

const SubItem: React.FC<Props> = ({
  type,
  data: {
    index,
    name,
    expired_date,
    next_recurring_date,
    user_status_recurring = 0,
    description,
  },
  listNodeRef,
  subListNodeRef,
  titleRef,
  index: indexItem,
  onPopout,
}) => {
  const focusKey = useMemo(() => `${FOCUS_KEY_TEMPLATE}_${index}`, [index]);
  const itemNodeRef = useRef<any>(null);

  const onFocus: FocusHandler = useCallback(
    ({}, {}, { implicit }) => {
      if (implicit) return;
      const listNode = listNodeRef.current;
      const subListNode = subListNodeRef.current;
      const itemNode = itemNodeRef.current;
      const titleNode = titleRef.current;
      if (listNode && subListNode && itemNode && titleNode) {
        const offsetTopSubListNode = subListNode.offsetTop;
        const offsetTopItemNode = itemNode.offsetTop;
        const offsetTopTitleNode = titleNode.offsetTop;
        let offsetTop = 0;
        if (indexItem < 3) {
          offsetTop = offsetTopSubListNode + offsetTopTitleNode;
        } else {
          offsetTop = offsetTopSubListNode + offsetTopItemNode;
        }
        listNode.style.transition = "0.2s";
        listNode.style.transform = `translate(0, ${-offsetTop}px)`;
      }
    },
    [indexItem]
  );

  const { ref, focused } = useFocusable({
    enterOnClick: true,
    focusImplicitOnHover: true,
    focusKey,
    onReturnPress: () => {
      if (typeof onPopout === "function") {
        onPopout();
      }
    },
    onFocus,
  });

  useEffect(() => {
    itemNodeRef.current = ref.current;
  }, []);

  return (
    <div
      ref={ref}
      className={classnames("sub-item", `sub-item--${type}`, {
        focus: focused,
      })}
    >
      <div className="sub-item__title">{name}</div>
      <div className="sub-item__info">Hạn sử dụng: {expired_date}</div>
      {next_recurring_date && user_status_recurring > 0 && (
        <div className="sub-item__info">
          Kỳ thành toán tiếp theo: {next_recurring_date}
        </div>
      )}
      {description && <div className="sub-item__note">{description}</div>}
    </div>
  );
};

export default SubItem;
