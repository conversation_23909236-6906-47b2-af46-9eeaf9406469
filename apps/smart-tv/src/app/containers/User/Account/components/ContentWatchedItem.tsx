import React, { useMemo } from "react";
import classNames from "classnames";
import { convertTimeddddDDMMYYHHmm } from "app/utils/formatTime";
import { formatVietmeseTime } from "app/components/Common/CommonTagsLuna";
import {
  FocusableComponentLayout,
  FocusDetails,
  useFocusable,
} from "core/KeyHandle";

interface ContentWatchedItemProps {
  content: any;
  index: number;
  onFocus?: (
    layout: FocusableComponentLayout,
    props: object,
    details: FocusDetails
  ) => void;
  onPopout?: (props: { pressedKey: string; isBackToProfile?: boolean }) => void;
}

const FOCUS_KEY_TEMPLATE = `VN:CONTENT_WATCHED_ITEM`;

const ContentWatchedItem: React.FC<ContentWatchedItemProps> = ({
  content,
  index,
  onFocus,
  onPopout,
}) => {
  const {
    content_title: contentTitle,
    time_started: timeStarted,
    duration,
  } = content;
  const focusKey = useMemo(() => `${FOCUS_KEY_TEMPLATE}_${index}`, [index]);

  const { ref, focused, setFocus } = useFocusable({
    focusKey,
    focusImplicitOnHover: true,
    enterOnClick: true,
    onFocus,
    onArrowPress: (direction) => {
      if (direction === "up" && index === 0) {
        setFocus("VN:PROFILE_TAB_ITEM_1");
        return false;
      } else if (direction === "right") {
        return false;
      } else if (direction === "left") {
        if (typeof onPopout === "function") {
          onPopout({ pressedKey: "left", isBackToProfile: true });
        }
        return false;
      }
      return true;
    },
    onReturnPress: () => {
      if (typeof onPopout === "function") {
        onPopout({ pressedKey: "return" });
      }
    },
    extraProps: { index },
  });

  return (
    <div
      className={classNames("tab-content-watched--row", {
        active: focused,
      })}
      ref={ref}
    >
      <div className="tab-content-watched--title">{contentTitle}</div>
      <div className="tab-content-watched--divider" />
      <div className="tab-content-watched--description">
        <span>{convertTimeddddDDMMYYHHmm(timeStarted)}</span>
        <span>{`Xem trong ${formatVietmeseTime(
          duration > 60 ? duration : 60
        )}`}</span>
      </div>
    </div>
  );
};
export default ContentWatchedItem;
