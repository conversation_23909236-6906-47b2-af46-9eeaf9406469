import { useState, useEffect, useCallback, useRef, useReducer } from "react";
import get from "lodash/get";
import { concat } from "lodash";
import * as api from "services/endpoint";
import KeepAlive, { KeepAliveData } from "app/utils/KeepAlive";
import { ErrorType } from "app/utils/AppError";
import useUnmountRef from "hooks/useUnmountRef";

export type Item = Record<string, any> & {
  index: number;
};

type State = {
  items: Item[];
  isLoading: boolean;
  isLoadMore: boolean;
  error: number;
  page: number;
  pageTitle: string;
};

type Action =
  | { type: "request" }
  | { type: "success"; items: any[]; page: number; pageTitle: string }
  | { type: "failure"; error: number }
  | { type: "loadmore" }
  | { type: "loadmoresuccess"; items: any[] }
  | { type: "loadmorefailure"; error?: number };

type Reducer<S, A> = (prevState: S, action: A) => S;

const initialState: State = {
  items: [],
  isLoading: true,
  isLoadMore: false,
  error: 0,
  page: 0,
  pageTitle: "",
};
const reducer: Reducer<State, Action> = (state, action) => {
  switch (action.type) {
    case "request":
      return {
        ...state,
        isLoading: true,
        error: 0,
      };
    case "success":
      return {
        ...state,
        isLoading: false,
        error: 0,
        page: action.page,
        pageTitle: action.pageTitle,
        items: action.items.map((item, index) => ({
          ...item,
          index,
        })),
      };
    case "failure":
      return {
        ...state,
        isLoading: false,
        error: action.error || ErrorType.Unexpected,
      };
    case "loadmore":
      return {
        ...state,
        error: 0,
        isLoadmore: true,
      };
    case "loadmoresuccess": {
      return {
        ...state,
        isLoadmore: false,
        error: 0,
        page: state.page + 1,
        items: [...state.items, ...action.items].map((item, index) => ({
          ...item,
          index,
        })),
      };
    }
    case "loadmorefailure":
      return {
        ...state,
        isLoadmore: false,
        error: action.error || ErrorType.Unexpected,
      };
    default:
      return state;
  }
};

export const useUserProfileApi = (
  type: string
): [
  Item[],
  boolean,
  number,
  (I: number) => void,
  boolean,
  string,
  KeepAliveData
] => {
  const listPageRef = useRef<Array<number>>([]);

  const [{ items, isLoading, isLoadMore, error, page, pageTitle }, dispatch] =
    useReducer(reducer, initialState);
  const [keepAliveData, setKeepAliveData] = useState({
    path: window.location.hash,
    focus: {},
    extra: {},
  } as KeepAliveData);
  const isUnmounted = useUnmountRef();
  const maxPageRef = useRef<number>(0);

  useEffect(() => {
    const keepAliveDt = KeepAlive.getData(window.location.hash);
    const fetchData = async () => {
      const makePageRef = (data: any) => {
        const meta = get(data, "metadata", {});
        const items = get(data, "items", []) || [];
        const total = get(meta, "total", 0);
        const current = items.length;
        const maxPage = Math.ceil(total / 7);
        const page = Math.ceil(current / 7);
        return {
          page,
          maxPage,
        };
      };

      try {
        dispatch({ type: "request" });
        let keepPage = 0;
        let res;

        switch (type) {
          case "history": {
            res = await api.getWatchMore(page, 35);
            break;
          }
          case "renting": {
            res = await api.getRentingContents({ page, limit: 35 });
            break;
          }
          default: {
            res = await api.getWatchLater(page, 35);
          }
        }

        if (keepAliveDt) {
          setKeepAliveData(keepAliveDt);
          keepPage = get(keepAliveDt, "focus.y", 0);
        }

        let items = get(res, "items", []) || [];
        const pageInfo = makePageRef(res);
        if (keepPage > 1) {
          for (let i = 5; i < keepPage + 3; i++) {
            listPageRef.current.push(i);
            let resPageRibbon;

            switch (type) {
              case "history": {
                resPageRibbon = await api.getWatchMore(i, 7);
                break;
              }
              case "renting": {
                resPageRibbon = await api.getRentingContents({
                  page: i,
                  limit: 7,
                });
                break;
              }
              default: {
                resPageRibbon = await api.getWatchLater(i, 7);
              }
            }
            const itemsRes = get(resPageRibbon, "items", []) || [];
            // @ts-ignore
            items = concat(items, itemsRes);
            pageInfo.page += 1;
            if (pageInfo.page === pageInfo.maxPage) {
              break;
            }
          }
        }

        if (isUnmounted.current) return;
        const pageTitle = get(res, "name", "");
        maxPageRef.current = pageInfo.maxPage;
        if (items.length === 0) {
          dispatch({ type: "failure", error: 1 });
        } else {
          dispatch({ type: "success", page: pageInfo.page, items, pageTitle });
        }
      } catch (error: any) {
        if (error.name === "App Error") {
          dispatch({ type: "failure", error: error.code });
        } else {
          dispatch({ type: "failure", error: ErrorType.Unexpected });
        }
      }
    };
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const loadMore = useCallback(
    async (focusIndex: number) => {
      const maxPage = maxPageRef.current;
      try {
        // prevent recall same page
        if (listPageRef.current.includes(page)) return;

        if (isLoadMore) return;
        if (maxPage === page) return;
        if (focusIndex < page - 3) return;

        // prevent recall same page
        listPageRef.current.push(page);

        dispatch({ type: "loadmore" });
        let res;

        switch (type) {
          case "history": {
            res = await api.getWatchMore(page, 7);
            break;
          }
          case "renting": {
            res = await api.getRentingContents({ page, limit: 7 });
            break;
          }
          default: {
            res = await api.getWatchLater(page, 7);
          }
        }

        if (isUnmounted.current) return;
        const items = get(res, "items", []) || [];
        dispatch({ type: "loadmoresuccess", items });
      } catch (error: any) {
        if (error.name === "App Error") {
          dispatch({ type: "loadmorefailure", error: error.code });
        } else {
          dispatch({ type: "loadmorefailure", error: ErrorType.Unexpected });
        }
      }
    },
    [page, isLoadMore, isUnmounted]
  );
  return [
    items,
    isLoading,
    error,
    loadMore,
    isLoadMore,
    pageTitle,
    keepAliveData,
  ];
};
