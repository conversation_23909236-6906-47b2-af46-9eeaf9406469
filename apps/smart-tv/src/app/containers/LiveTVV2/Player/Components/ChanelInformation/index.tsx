import React, { Fragment, useEffect, useMemo } from "react";
import { useSelector } from "react-redux";
import classNames from "classnames";
import { useParams } from "react-router-dom";
import get from "lodash/get";
import Icon from "app/components/Common/Icon";
import Tag, { TagType } from "app/components/Common/Tag/Tag";
import { getListEPGNow } from "app/endpoint/LiveTV/service";
import { ChannelDetail, ChannelEPG } from "app/models/LiveTV";
import { convertEpochToHHMM } from "app/utils/formatNumber";
import ICPlay from "assets/images/icon/ic-play.gif";
import useStateRef from "hooks/useStateRef";
import { useLiveTVV2Actions, useLiveTVV2Selector } from "app/redux/LiveTVV2";
import { makeRemainingTimeStr } from "app/utils/formatTime";
import { RootState } from "app/store/store";
import { getSubList } from "services/endpoint";

type ChanelInformationProps = {
  channelDetail?: ChannelDetail;
  isEPG: boolean;
};

const ChannelEPGDetail = ({ channelDetail }: Pick<ChanelInformationProps, "channelDetail">) => {
  const { title, time_start, time_end } = channelDetail?.epg ?? {};
  return (
    <div className="channel-info-current">
      <p className="title two-line-text">{title}</p>
      <div className="tag-channel">
        <div className="left">
          <Icon name="live-tv-v2-new-icon-replay" className="replay-icon icon" viewBox="0 0 24 24" />
          <span className="title--live">PHÁT LẠI</span>
          <span className="time">
            {time_start && time_end && `${convertEpochToHHMM(time_start)} - ${convertEpochToHHMM(time_end)}`}
          </span>
        </div>
      </div>
    </div>
  );
};
const ChanelInformation = React.memo(function ChanelInformation({ isEPG, channelDetail }: ChanelInformationProps) {
  const params = useParams();
  const idChannel = get(params, "id", "");
  const isAuthen = useSelector((state: RootState) => state.app.isAuthen);
  const [channelInfoList, setChannelInfoList, channelInfoListRef] = useStateRef<ChannelEPG[]>([]);

  const { updateEpgSubscribeItemLiveTVV2Store } = useLiveTVV2Actions();

  const epgSubscribeList = useLiveTVV2Selector((state) => state.epgSubscribeList);

  useEffect(() => {
    if (idChannel) {
      getListEPGNow(idChannel, async (epgList) => {
        const channelInfoList = epgList.slice(0, 2);
        channelInfoListRef.current = channelInfoList;
        setChannelInfoList(channelInfoList);
        const [_, epgNext] = channelInfoList;
        if (isAuthen && epgNext && epgSubscribeList[epgNext.id] === undefined) {
          try {
            const subscribeList: any = await getSubList({
              list_id: [epgNext.id]
            });
            Object.keys(subscribeList)?.forEach((channelId) => {
              updateEpgSubscribeItemLiveTVV2Store({
                channelId,
                isSubscribed: subscribeList[channelId]
              });
            });
          } catch (error) {
            console.error("getSubList: ", error);
          }
        }
      });
    }
    return () => {
      setChannelInfoList([]);
    };
  }, [idChannel, isAuthen]);

  const renderChannelInfo = useMemo(() => {
    if (!channelInfoListRef.current.length) {
      return (
        <div
          className={classNames("channel-info-current", {
            error: !channelInfoListRef.current.length
          })}
        >
          <p className="title two-line-text">
            Tên chương trình & lịch phát sóng đang chưa hỗ trợ hiển thị lúc này. Vui lòng thử lại sau
          </p>
        </div>
      );
    }
    return channelInfoList.map((item: ChannelEPG) => {
      const [_, epgNext] = channelInfoList;
      const minutesUntil = makeRemainingTimeStr(item?.time_end, false);
      return (
        <Fragment key={item.id}>
          <div className={item.is_coming_soon ? "channel-info-next" : "channel-info-current"} key={item.id}>
            <p className="title two-line-text">{item.title}</p>
            <div className="tag-channel">
              <div className="left">
                {item.is_coming_soon ? (
                  <Icon
                    viewBox="0 0 44 44"
                    fill="none"
                    className={classNames("bell", {
                      subscribed: epgSubscribeList[epgNext.id]
                    })}
                    name="live-tv-v2-player-bell"
                  />
                ) : null}
                {!item.is_coming_soon ? (
                  <>
                    <span>
                      <Tag type={TagType.Live} key={TagType.Live} />
                    </span>
                    <div className="tag-channel-active">
                      <img src={ICPlay} alt="" />
                    </div>
                  </>
                ) : null}

                <span className="title--live">{item.is_coming_soon ? "TIẾP THEO" : "ĐANG PHÁT"}</span>
                <span className="time">
                  {item?.time_start &&
                    item?.time_end &&
                    `${convertEpochToHHMM(item?.time_start)} - ${convertEpochToHHMM(item?.time_end)}`}
                </span>
              </div>

              {!item.is_coming_soon ? <div className="right">Còn lại {minutesUntil}</div> : null}
            </div>
          </div>
          {!item.is_coming_soon ? <div className="break-line" /> : null}
        </Fragment>
      );
    });
  }, [channelInfoList]);

  return (
    <div className="channel-info">{isEPG ? <ChannelEPGDetail channelDetail={channelDetail} /> : renderChannelInfo}</div>
  );
});

export default ChanelInformation;
