import { useCallback } from "react";
import { useSelector } from "react-redux";
import {
  fetchLiveTVData,
  getLiveTVEPGList,
  getSubscribeEPGList,
} from "app/endpoint/LiveTV/originalService";
import { useLiveTVPlayerActions } from "app/redux/Player/LiveTVPlayer";
import { LiveTVType } from "types/page";
import { RibbonDataItemModel } from "app/models/RibbonData/RibbonDataItem";
import { RootState } from "app/store/store";

const useLiveTVPlayer = () => {
  const {
    updateChannelList,
    updateEPGListLoading,
    updateActiveEPGItem,
    resetStore,
  } = useLiveTVPlayerActions();
  const isLogged = useSelector((state: RootState) => state.app.isAuthen);

  const fetchAllChannel = useCallback(async () => {
    const res = await fetchLiveTVData(null, LiveTVType.AllChannel);
    if (res?.length > 0) {
      const modelRes = res?.map((item: any) => new RibbonDataItemModel(item));

      updateChannelList(modelRes);
    }
  }, [updateChannelList]);

  const fetchLiveTVEPGList = useCallback(
    async (id, date) => {
      updateEPGListLoading(true);
      const res: any = isLogged
        ? await getSubscribeEPGList(id, date)
        : await getLiveTVEPGList(id, date);

      setTimeout(() => {
        updateEPGListLoading(false);
      }, 300);
      if (res) {
        return res;
      }
      return [];
    },
    [getSubscribeEPGList, getLiveTVEPGList, updateEPGListLoading, isLogged]
  );

  const navigateNextEPG = useCallback(
    (epg) => {
      const index = epg?.epgList?.findIndex(
        (item: any, id: number) =>
          item?.indexLive < 0 &&
          item?.slug?.length > 0 &&
          epg?.nextEPG?.index < id
      );
      updateActiveEPGItem({
        id: epg?.nextEPG?.id,
        idChannel: epg?.idChannel,
        idDate: epg?.idDate,
        nextEPG: epg?.epgList?.[index]
          ? {
              id: epg?.epgList?.[index]?.id,
              slug: epg?.epgList?.[index]?.slug,
              index,
            }
          : null,
        epgList: epg?.epgList,
        slug: epg?.nextEPG?.slug,
        index: epg?.nextEPG?.index,
        lastEPG: epg?.nextEPG?.index === epg?.epgList?.length - 1,
        nextDateId: null,
      });
      updateEPGListLoading(true);
    },
    [updateActiveEPGItem, updateEPGListLoading]
  );

  const navigateEPGNextDay = useCallback(
    async (epg) => {
      const res = await fetchLiveTVEPGList(epg?.idChannel, epg?.nextDateId);
      const index = res?.findIndex(
        (item: any) => item?.indexLive < 0 && item?.slug?.length > 0
      );
      if (res?.[index]?.slug) {
        const nextSlugIndex = res?.findIndex(
          (item: any, id: number) =>
            item?.indexLive < 0 && item?.slug?.length > 0 && index < id
        );
        updateActiveEPGItem({
          id: res?.[index]?.id,
          idChannel: epg?.idChannel,
          idDate: epg?.nextDateId,
          nextEPG: {
            id: res?.[nextSlugIndex]?.id || "",
            slug: res?.[nextSlugIndex]?.slug || "",
            index: nextSlugIndex,
          },
          epgList: res,
          slug: res?.[index]?.slug,
          index,
          lastEPG: index === res?.length - 1,
          nextDateId: null,
        });
        return res?.[index]?.slug;
      }
      updateActiveEPGItem(null);
      return null;
    },
    [updateActiveEPGItem, fetchLiveTVEPGList]
  );

  return { resetStore, fetchAllChannel, navigateEPGNextDay, navigateNextEPG };
};

export default useLiveTVPlayer;
