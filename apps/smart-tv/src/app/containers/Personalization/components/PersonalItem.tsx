import React, { useMemo } from "react";
import classNames from "classnames";
import useDebounce from "hooks/useDebounce";
import useStateRef from "hooks/useStateRef";

interface Props {
  focus?: boolean;
  selected?: boolean;
  thumbnail?: string;
  title: string;
  onClick: () => void;
}

const PersonalItem: React.FC<Props> = ({
  focus = false,
  selected = false,
  title = "",
  thumbnail = "",
  onClick,
}) => {
  const [selectStr, setSelectStr] = useStateRef<string | null>(null);
  const handleStartDebounce = (value: boolean) => {
    if (focus) {
      if (selectStr === null) {
        setSelectStr("");
      } else {
        if (!value) {
          setSelectStr("selecting");
        } else {
          setSelectStr("unselecting");
        }
      }
    } else {
      setSelectStr("");
    }
  };

  const handleEndDebounce = () => {
    setSelectStr("");
  };
  const debouncedSelected = useDebounce(
    selected,
    300,
    handleStartDebounce,
    handleEndDebounce
  );
  return (
    <div
      className={classNames("personal-item-container", selectStr, {
        focus,
        selected: debouncedSelected,
      })}
      onClick={onClick}
    >
      <div className={classNames("personal-item")}>
        <div className="personal-item__image">
          <img alt="hình thumb" src={thumbnail} />
        </div>
        <div className="personal-item__desc">{title}</div>
      </div>
    </div>
  );
};

export default PersonalItem;
