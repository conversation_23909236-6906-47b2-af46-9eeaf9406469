import React, { useEffect, useState, useRef, useCallback } from "react";
import { useHistory } from "react-router-dom";
import classNames from "classnames";
import { useDispatch, useSelector } from "react-redux";
import { get } from "lodash";
import platform from "services/platform";
import * as api from "services/endpoint";
import useStateRef from "hooks/useStateRef";
import useKeyService from "hooks/useKeyService";
import { useGAPageViewByLocation } from "hooks/useGAPageViewByLocation";
import Logo from "app/components/Common/Logo";
import Button from "app/components/Button";
import PersonalPopup from "app/components/Common/Portal/PersonalPopup";
import TipBox from "app/components/Common/Portal/TipBox";
import Message from "app/components/Common/Message";
import usePersonal from "hooks/usePersonal";
import { getProfileData, updateProfile } from "app/store/actions";
import SegmentManager from "app/utils/SegmentManager";
import { RootState } from "app/store/store";
import { clearCache } from "services/api";
import PersonalItem from "./components/PersonalItem";

interface Personalization {
  id: string;
  name: string;
  image: string;
}

interface Props {}
const Personalization: React.FC<Props> = () => {
  const history = useHistory();
  const isAuthen = useSelector((state: RootState) => state.app.isAuthen);
  const reduxDispatcher = useDispatch();
  const [isShow, setIsShow] = useState(false);
  const timer = useRef<TimerHandle>(null);
  const [listPersonal, setListPersonal, listPersonalRef] = useStateRef<
    Personalization[]
  >([]);
  const [selected, setSelected, selectedRef] = useStateRef<Personalization[]>(
    []
  );

  /* Tracking Start */
  useGAPageViewByLocation();
  /* Tracking End */

  const [, , , setShownGuestFlow] = usePersonal(false);
  const keyHandler = (e: KeyboardEvent) => {
    const { keys } = platform;
    const { up, down, next, prev } = calNav(
      focusIndexRef.current,
      listPersonalRef.current,
      selectedRef.current
    );
    switch (e.keyCode) {
      case keys.right:
        if (next >= 0) setFocusIndex(next);
        break;
      case keys.left:
        if (prev >= 0) setFocusIndex(prev);
        break;
      case keys.down:
        if (down >= 0) setFocusIndex(down);
        break;
      case keys.up:
        if (up >= 0) setFocusIndex(up);
        break;
      case keys.enter:
        if (focusIndexRef.current === listPersonalRef.current.length) {
          submitHandle(selectedRef.current);
        } else {
          toggleSelection(selectedRef.current, focusIndexRef.current);
        }
        break;
      case keys.return: {
        history.replace("/");
        break;
      }
      default:
        break;
    }
  };
  const [focusIndex, setFocusIndex, focusIndexRef] = useKeyService({
    keyHandler,
    defaultFocusIndex: 0,
  });

  useEffect(() => {
    setFocusIndex(0);
    // get data
    (async function () {
      const {
        data: { value },
      } = (await api.getConfigPersonal()) as { data: { value: string } };
      try {
        if (value && value.length) setListPersonal(JSON.parse(value));
      } catch (err) {
        // error
      }
    })();

    return () => {
      if (timer.current) {
        clearTimeout(timer.current);
        timer.current = null;
      }
    };
  }, []);

  const toggleSelection = useCallback((selected: any[], index: number) => {
    if (
      selected.length < 3 &&
      selected.findIndex((c) => c.id === listPersonalRef.current[index].id) ===
        -1
    ) {
      setSelected((s: any[]) => [...s, listPersonalRef.current[index]]);
    } else {
      setSelected(
        selectedRef.current.filter(
          (c) => c.id !== listPersonalRef.current[index].id
        )
      );
    }
  }, []);

  const submitHandle = useCallback((selected: any[]) => {
    if (selected?.length > 0) {
      setIsShow(true);
    }
    SegmentManager.segmentAction("start watching button selected", {
      genre_name_submitted: selected
        .filter((i) => i.name)
        .map((i) => i.name)
        .join(", "),
    });
    api
      .submitPersonal(selected)
      .then(() => {
        if (isAuthen) {
          reduxDispatcher(
            getProfileData({
              callback: ({ status, data, errorMsg }) => {
                if (status === "success") {
                  const payload = {
                    profile: get(data, "profile", null),
                    userType: get(data, "type", 0),
                    packageGroupId: get(data, "package_group_id", 0),
                    livetvGroupId: get(data, "livetv_group_id", ""),
                    hideBuyPackage: get(data, "hide_button_buy_package", false),
                  };
                  reduxDispatcher(updateProfile(payload));
                }
                setShownGuestFlow(true);
                timer.current = setTimeout(
                  () => {
                    setIsShow(false);
                    const location = {
                      pathname: "/",
                    };
                    history.replace(location);
                    Message.open("Chào mừng bạn đến với VieON");
                  },
                  selected?.length > 0 ? 3000 : 0
                );
              },
            })
          );
        } else {
          clearCache();
          setShownGuestFlow(true);
          timer.current = setTimeout(
            () => {
              setIsShow(false);
              const location = {
                pathname: "/",
              };
              history.replace(location);
              Message.open("Chào mừng bạn đến với VieON");
            },
            selected?.length > 0 ? 3000 : 0
          );
        }
      })
      .catch((err) => {
        setIsShow(false);
        setShownGuestFlow(true, true);
        const location = {
          pathname: "/",
        };
        history.replace(location);
        Message.open("Chào mừng bạn đến với VieON");
      });
  }, []);
  const calNav = (
    currentIndex: number,
    list: Personalization[],
    selected: Personalization[]
  ): { up: number; down: number; next: number; prev: number } => {
    let up = -1;
    let down = -1;
    let next = -1;
    let prev = -1;
    if (currentIndex < list.length) {
      // up
      if (currentIndex % 5 !== 0 && currentIndex - 1 >= 0) {
        up = currentIndex - 1;
      }
      // down
      if ((currentIndex + 1) % 5 !== 0 && currentIndex + 1 < list.length) {
        down = currentIndex + 1;
      } else {
        // Nút bắt đầu xem
        down = list.length;
      }
      // next
      if (currentIndex + 5 < list.length) {
        next = currentIndex + 5;
      } else if (currentIndex % 5 > (list.length - 1) % 5) {
        next = list.length - 1;
      }
      if (currentIndex - 5 >= 0) {
        prev = currentIndex - 5;
      }
    } else {
      if (list.length > 0 && list.length < 5) {
        up = list.length - 1;
      } else {
        up = 4;
      }
    }
    return { up, down, next, prev };
  };
  const onClose = () => {
    setIsShow(false);
  };
  const handleSubmitButtonClick = useCallback((selected: any[]) => {
    submitHandle(selected);
  }, []);
  const handlePersonalItemClick = useCallback(
    (selected: any[], index: number) => {
    setFocusIndex(index);
    toggleSelection(selected, index);
    },
    []
  );
  return (
    <div className="personal">
      <div className="personal-header">
        <p className="personal-title">Bạn thích xem gì </p>
        <Logo className="personal-logo" />
      </div>
      <p className="personal-description">
        Cho chúng tôi biết 3 thể loại bạn thích nhất
      </p>
      <div className="personal-list">
        {listPersonal.map((item, index) => (
          <PersonalItem
            key={index}
            focus={focusIndex === index}
            title={item.name}
            thumbnail={item.image}
            selected={selected.map((c) => c.id).includes(item.id)}
            onClick={handlePersonalItemClick.bind(null, selected, index)}
          />
        ))}
      </div>
      <Button
        focus={focusIndex === listPersonal.length}
        className={classNames("personal-button")}
        onClick={handleSubmitButtonClick.bind(null, selected)}
        style={{ width: "8em" }}
      >
        Bắt đầu xem
      </Button>
      <PersonalPopup onClose={onClose} isShow={isShow} />
      <TipBox tip="để quay lại" />
    </div>
  );
};

export default Personalization;
