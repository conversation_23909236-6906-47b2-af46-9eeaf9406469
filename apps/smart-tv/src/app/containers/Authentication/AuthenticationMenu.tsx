import React, { useCallback, useEffect, useMemo, useState, useRef } from "react";
import { useHistory, useLocation } from "react-router-dom";
import isEmpty from "lodash/isEmpty";
import { QRCode } from "react-qrcode-logo";
import { useDispatch, useSelector } from "react-redux";
import classNames from "classnames";
import Message from "app/components/Common/Message";
import { MESSAGES } from "types/common/Message";
import {
  EnterPressHandler,
  FocusableComponentLayout,
  FocusContext,
  FocusHandlerType,
  useFocusable,
  VieOnNavigation,
} from "core/KeyHandle";
import Button from "app/containers/Error/components/Button";
import { RootState } from "app/store/store";
import TipBox from "app/components/Common/Portal/TipBox";
import FocusableWrapper from "app/components/FocusableWrapper";
import backService from "services/backService";
import { ROUTES } from "app/utils/constants";
import { authenticationDeviceLogin, authenticationDeviceLoginStatus } from "app/endpoint/Authentication/service";
import KeepAlive, { KeepAliveData } from "app/utils/KeepAlive";
import { AuthenticationMethod } from "types/common";
import SegmentManager from "app/utils/SegmentManager";
import qrRules from "assets/images/qr-rules.png";
import OpenErrorImg from "assets/images/empty_state/open-error.png";
import logoVieonQrCode from "assets/images/logo-vieon-qr-code.png";
import { useContextTrackLocationState } from "context/ContextTrackLocation";
import { GlobalFeatureEventNames, GlobalFeaturePropertyKeys } from "app/components/Tracking/GlobalFeatureTracking";
import Loader from "app/components/Common/Spinner";
import useIsGlobal from "hooks/useIsGlobal";
import useStateRef from "hooks/useStateRef";
import { useContextAuthenRoutingState } from "./Context/ContextAuthenRouting";
import { CodeAPI } from "./type";
import { handleSuccessfulAuthentication } from "./utils";

import useAuthMenu from "./hooks/useAuthMenu";

enum LoginType {
  PHONE = "PHONE",
  EMAIL = "EMAIL"
  // APP = "APP",
  // WEB = "WEB",
  // OTHER = "OTHER",
}

const buttons = [
  {
    type: LoginType.PHONE,
    label: "Bắt đầu với Số điện thoại",
    focusKey: "VN:AUTH_BY_PHONE"
  },
  {
    type: LoginType.EMAIL,
    label: "Bắt đầu với Email",
    focusKey: "VN:AUTH_BY_EMAIL"
  }
  // {
  //   type: LoginType.OTHER,
  //   label: "Bắt đầu với mã QR",
  //   focusKey: "VN:AUTH_BY_OTHER",
  // },
];

const Authentication: React.FC = () => {
  const history = useHistory();
  const location = useLocation<{
    [key: string]: any;
  }>();
  const dispatch = useDispatch();
  const userFlow = useSelector((state: RootState) => state.app.userFlow);
  const authMenu = useAuthMenu();
  const { terms, shortLink, qrCode } = authMenu || {};
  const [logoVieonQrCodeWidth, setLogoVieonQrCodeWidth] = useState<number>(0);
  const [qrImgWidth, setQrImgWidth] = useState<number>(396);

  const trackLocation = useContextTrackLocationState();

  const [buttonType, setButtonType, buttonTypeRef] = useStateRef<string>("PHONE");
  const [userCode, setUserCode] = useState<string>("");
  const [codeToCheckStatus, setCodeToCheckStatus, codeToCheckStatusRef] = useStateRef<string>("");
  const [timeIntervalToCheckStatus, setTimeIntervalToCheckStatus, timeIntervalToCheckStatusRef] =
    useStateRef<number>(0);
  const isGlobal = useIsGlobal();
  const { resetContextAuthenRoutingState } = useContextAuthenRoutingState();
  // Solve case: sometimes onFocus event is trigger twice at the same time on the same button
  const isFetchingCodeRef = useRef<boolean>(false);
  const isCodeExpiredRef = useRef<boolean>(true);
  const timerDebounceFetchCodeRef = useRef<TimerHandle>(null);
  const timerFetchStatusRef = useRef<TimerHandle>(null);
  const timeToRefetchCodeRef = useRef<number>(0);
  const [countdown, setCountdown, countdownRef] = useStateRef<number>(0);
  const timerCountdownRef = useRef<TimerHandle>(null);
  const [isLoadingCode, setIsLoadingCode] = useState<boolean>(true);
  const { authenRouting, authenRoutingRef } = useContextAuthenRoutingState();

  const { titleAuthenticationMenu } = authenRouting;

  const { ref, focusKey, focusSelf } = useFocusable({
    focusKey: "VN:LOGIN_PAGE"
  });

  useEffect(() => {
    (async function init() {
      const keepAliveDt = KeepAlive.getData(window.location.hash);
      if (keepAliveDt) {
        VieOnNavigation.setFocus(keepAliveDt.extra?.focusKey);
        focusSelf();
      }
    })();
  }, []);

  // TODO: Calculate size of QR Code Img
  useEffect(() => {
    const bodyWidth = document?.body?.clientWidth || 0;
    // 96, 360 and 1920 are from figma design
    const _logoVieonQrCodeWidth = (bodyWidth * 96) / 1920;
    setLogoVieonQrCodeWidth(_logoVieonQrCodeWidth);
    const _qrImgWidth = (bodyWidth * 396) / 1920;
    setQrImgWidth(_qrImgWidth);
  }, []);

  const clearTimer = () => {
    if (timerFetchStatusRef.current) {
      clearInterval(timerFetchStatusRef.current);
      timerFetchStatusRef.current = null;
    }
    if (timerDebounceFetchCodeRef.current) {
      clearTimeout(timerDebounceFetchCodeRef.current);
      timerDebounceFetchCodeRef.current = null;
    }
  };

  // TODO: countdown
  const startCountdown = useCallback(() => {
    if (timerCountdownRef.current) {
      clearInterval(timerCountdownRef.current);
    }
    timerCountdownRef.current = setInterval(() => {
      if (countdownRef.current > 0) {
        setCountdown((countdown) => countdown - 1);
      } else if (timerCountdownRef.current) {
        clearInterval(timerCountdownRef.current);
      }
    }, 1000);
  }, []);

  const fetchCode = useCallback(async () => {
    clearTimer();
    if (isFetchingCodeRef.current) {
      return;
    }
    isFetchingCodeRef.current = true;
    setIsLoadingCode(true);
    const response = await authenticationDeviceLogin();

    if (response.code === CodeAPI.Success) {
      isCodeExpiredRef.current = false;
      const { userCode, expiresIn, interval, code } = response.result;
      setUserCode(userCode);
      setCodeToCheckStatus(code);
      setTimeIntervalToCheckStatus(interval * 1000);
      timeToRefetchCodeRef.current = Date.now() + expiresIn * 1000;
      setCountdown(expiresIn);
      startCountdown();
    } else {
      setUserCode("");
      setCodeToCheckStatus("");
    }
    isFetchingCodeRef.current = false;
    setIsLoadingCode(false);
  }, []);

  const fetchStatus = useCallback(async () => {
    const isCodeExpired = Date.now() >= timeToRefetchCodeRef.current;
    if (isCodeExpired) {
      Message.open(MESSAGES.ExpiredLoginCode);
      await fetchCode();
    } else {
      const response = await authenticationDeviceLoginStatus(codeToCheckStatusRef.current);
      if (response.code === CodeAPI.Success) {
        if (timerFetchStatusRef.current) {
          clearInterval(timerFetchStatusRef.current);
          timerFetchStatusRef.current = null;
        }
        const { result, account } = response;
        const { state, searchParams } = authenRoutingRef.current;
        await handleSuccessfulAuthentication({
          dispatch,
          history,
          trackLocation,
          state,
          searchParams,
          authenticationMethod: AuthenticationMethod.SignIn,
          result: {
            ...result,
            profile: account
          },
          isLocal: !isGlobal,
          userFlow,
          hotelAccount: account?.hotelAccount
        });
      }
    }
  }, [authenRoutingRef, codeToCheckStatusRef, dispatch, fetchCode, history, isGlobal, trackLocation, userFlow]);

  // TODO: fetch login status intervally
  useEffect(() => {
    if (codeToCheckStatus && timeIntervalToCheckStatus) {
      timerFetchStatusRef.current = setInterval(async () => {
        const other = ["PHONE", "EMAIL"].includes(buttonTypeRef.current);
        // if (!["OTHER"].includes(buttonTypeRef.current)) {
        if (!other) {
          clearTimer();
          return;
        }
        await fetchStatus();
      }, timeIntervalToCheckStatus);
    }
  }, [codeToCheckStatus, timeIntervalToCheckStatus]);

  // TODO: Clear all timers on unmount
  useEffect(() => {
    return () => {
      clearTimer();
      if (timerCountdownRef.current) {
        clearInterval(timerCountdownRef.current);
        timerCountdownRef.current = null;
      }
    };
  }, []);

  const onReturn = useCallback(() => {
    if (location.pathname === ROUTES.LOGIN) {
      VieOnNavigation.navigateByDirection("left", {});
    } else {
      resetContextAuthenRoutingState();
      backService.back();
    }
  }, [location.pathname, resetContextAuthenRoutingState]);

  const onButtonEnter = useCallback<
    EnterPressHandler<{
      type?: string | undefined;
    }>
  >(({ type }) => {
    switch (type) {
      case LoginType.PHONE: {
        KeepAlive.saveData({
          path: window.location.hash,
          extra: {
            focusKey: "VN:AUTH_BY_PHONE"
          }
        } as KeepAliveData);
        history.replace({
          pathname: ROUTES.AUTH_VERIFY_PHONE,
          search: `?from=${location.pathname}`
        });
        // Tracking
        const flowName = location?.state?.flowName || null;
        SegmentManager.segmentAction(GlobalFeatureEventNames.MobileAuthenButtonSelected, {
          [GlobalFeaturePropertyKeys.flowName]: flowName
        });
        break;
      }
      case LoginType.EMAIL: {
        KeepAlive.saveData({
          path: window.location.hash,
          extra: {
            focusKey: "VN:AUTH_BY_EMAIL"
          }
        } as KeepAliveData);
        history.replace({
          pathname: ROUTES.AUTH_VERIFY_EMAIL,
          search: `?from=${location.pathname}`
        });
        // Tracking
        const flowName = location?.state?.flowName || null;
        SegmentManager.segmentAction(GlobalFeatureEventNames.EmailAuthenButtonSelected, {
          [GlobalFeaturePropertyKeys.flowName]: flowName
        });
        break;
      }
      default: {
        break;
      }
    }
  }, []);

  const onButtonFocus = useCallback<FocusHandlerType>((_, { type }, __) => {
    setButtonType(type);
    const other = ["PHONE", "EMAIL"].includes(type);
    // if (!["OTHER"].includes(type)) {
    if (!other) {
      clearTimer();
    }
    // if (["OTHER"].includes(type)) {
    if (other) {
      // Tracking
      // const eventType =
      //   type === "APP"
      //     ? GlobalFeatureEventNames.AppAuthenButtonSelected
      //     : GlobalFeatureEventNames.WebAuthenButtonSelected;
      // const flowName = location?.state?.flowName || null;
      // SegmentManager.segmentAction(eventType, {
      //   [GlobalFeaturePropertyKeys.flowName]: flowName,
      // });

      const isCodeExpired = Date.now() >= timeToRefetchCodeRef.current;
      if (isCodeExpired) {
        if (codeToCheckStatusRef.current) {
          Message.open(MESSAGES.ExpiredLoginCode);
        }
        fetchCode();
      } else if (timeIntervalToCheckStatusRef.current && !timerFetchStatusRef.current) {
        timerFetchStatusRef.current = setInterval(async () => {
          await fetchStatus();
        }, timeIntervalToCheckStatusRef.current);
      }
    }
  }, []);

  const focusTermBtn = useCallback(
    () => onButtonFocus({} as FocusableComponentLayout, { type: "TERMS" }, {}),
    [onButtonFocus],
  );

  // !Close temporarily
  // TODO: clear all timers excepting coundown timer on blurring of btns
  // const onBlur = useCallback(() => {
  //   clearTimer();
  // }, []);

  const renderLoginByOtherMethods = useMemo(
    () => (
      <div className="content__center--app content__center--other">
        <p className="title">
          Sử dụng máy ảnh trên điện thoại hoặc máy tính bảng
          <br />
          để quét mã bên dưới
        </p>
        <div className="qr-code">
          <QRCode
            logoImage={logoVieonQrCode}
            logoWidth={logoVieonQrCodeWidth}
            value={`${shortLink}?code=${userCode}`}
            size={qrImgWidth}
          />
        </div>
        <div className="text-line">CÁCH KHÁC</div>
        <p className="text">
          Truy cập vào liên kết <span className="highlight">{shortLink}</span>
          <br />
          Hoặc ở phần Đăng nhập Smart TV trên ứng dụng VieON
          <br />
          và nhập mã <span className="highlight highlight-1">{userCode}</span>
        </p>
        <p className="text-time">Mã sẽ hết hạn sau {new Date(countdown * 1000).toISOString().slice(14, 19)} phút nữa</p>
      </div>
    ),
    [countdown, logoVieonQrCodeWidth, qrImgWidth, shortLink, userCode]
  );

  const renderErrorSection = useMemo(
    () => (
      <div className="content__center--app content__center--other content__center--error">
        <div className="error-img">
          <img src={OpenErrorImg} alt="" />
        </div>
        <h3 className="error-title">Không tải được mã QR</h3>
        <p className="error-text">
          Dường như đã có lỗi khi tải mã QR. Vui lòng kiểm tra kết nối mạng và
          <br />
          thử lại.
        </p>
      </div>
    ),
    []
  );

  const renderLoadingSection = useMemo(
    () => (
      <div className="content__center--app content__center--other content__center--error">
        <Loader />
      </div>
    ),
    []
  );

  // const renderLoginByPhone = useMemo(
  //   () => (
  //     <div className="content__center">
  //       <img className="img--center" src={TVEnter} alt="TVEnter" />
  //       <div className="short-description">
  //         Bấm “OK” trên điều khiển để bắt đầu
  //       </div>
  //     </div>
  //   ),
  //   []
  // );

  const renderTermsOfUse = useMemo(
    () => (
      <div className="content__center--term">
        <div className="term-detail">Trên điện thoại hoặc máy tính truy cập {terms}</div>
        <div className="term-cta-qr">Hoặc quét mã QR bên dưới</div>
        <img className="term-img" src={!isEmpty(qrCode) ? qrCode : qrRules} alt="qr-code" />
      </div>
    ),
    [qrCode, terms]
  );

  const computedContentLogin = useMemo(() => {
    // if (["PHONE", "EMAIL"].includes(buttonType)) {
    //   return renderLoginByPhone;
    // } else if (buttonType === "OTHER" && userCode && !isLoadingCode) {
    //   return renderLoginByOtherMethods;
    // } else if (buttonType === "OTHER" && !userCode && !isLoadingCode) {
    //   return renderErrorSection;
    // } else if (buttonType === "OTHER") {
    //   return renderLoadingSection;
    // }
    const other = ["PHONE", "EMAIL"].includes(buttonType);
    if (other && userCode && !isLoadingCode) {
      return renderLoginByOtherMethods;
    } else if (other && !userCode && !isLoadingCode) {
      return renderErrorSection;
    } else if (other) {
      return renderLoadingSection;
    }
    return renderTermsOfUse;
  }, [
    buttonType,
    isLoadingCode,
    renderErrorSection,
    renderLoadingSection,
    renderLoginByOtherMethods,
    // renderLoginByPhone,
    renderTermsOfUse,
    userCode
  ]);

  const computedBtns = useMemo(() => {
    let computedBtns = buttons;
    if (!isGlobal) {
      computedBtns = buttons.filter((btn) => btn.type !== LoginType.EMAIL);
    }
    return computedBtns.map((button) => {
      const { label, focusKey, type } = button;
      return (
        <Button
          type={type as any}
          onEnter={onButtonEnter}
          onFocus={onButtonFocus}
          className="auth-button"
          focusKey={focusKey}
          key={focusKey}
          onReturn={onReturn}
        >
          {label}
        </Button>
      );
    });
  }, [isGlobal, onButtonEnter, onButtonFocus, onReturn]);

  return (
    <FocusContext.Provider value={focusKey}>
      <div className="main main--user main--user-login main--user-auth-menu" ref={ref}>
        <div className="main__body__login">
          <section className="section section--login section--login-app section--login-auth">
            <div className="section__wrap">
              <div className="section__wrap--auth">
                <div className="block block--login block--login-auth block--login-required">
                  <h3 className="block__title block__title--auth">{titleAuthenticationMenu}</h3>
                  <div className="block__title--desc">Vui lòng chọn phương thức để bắt đầu</div>
                  <FocusableWrapper enterOnClick focusImplicitOnHover focusKey="VN:AUTH_BUTTON_GROUP">
                    {({ ref }) => {
                      return (
                        <FocusContext.Provider value="VN:AUTH_BUTTON_GROUP">
                          <div className="auth-button-group" ref={ref}>
                            {computedBtns}
                          </div>
                        </FocusContext.Provider>
                      );
                    }}
                  </FocusableWrapper>
                </div>
                <div className="block--divider" />
                <div className="block__info">{computedContentLogin}</div>
              </div>
              <div className="block__desc block__desc--auth">
                Bằng cách đăng ký, bạn đã đồng ý với
                <FocusableWrapper
                  enterOnClick
                  focusImplicitOnHover
                  saveLastFocusedChild={false}
                  focusKey="VN:TERMS"
                  onFocus={focusTermBtn}
                  onReturnPress={onReturn}
                >
                  {({ ref, focused }) => {
                    return (
                      <div ref={ref}>
                        <span
                          className={classNames("block__desc--btn", {
                            focus: focused
                          })}
                        >
                          Điều Khoản Sử Dụng
                        </span>
                      </div>
                    );
                  }}
                </FocusableWrapper>
                của VieON và xác nhận rằng bạn trên 18 tuổi
              </div>
            </div>
          </section>
          <TipBox tip="để quay lại" />
        </div>
      </div>
    </FocusContext.Provider>
  );
};

export default Authentication;
