import React, {
  ReactNode,
  createContext,
  useContext,
  useState,
  useCallback,
  useMemo,
} from "react";
import { OTPType } from "types/page";

interface ContextAuthenLoginProviderType {
  confirmationNo: string;
  expiresIn: number; // second
  retryAfterTime: number; // Epoch Time
  codeOTP: string;
  flowName: string; // For tracking
  type: OTPType.ZALO | OTPType.SMS;
}

const ContextAuthenLoginState = createContext<
  ContextAuthenLoginProviderType | undefined
>(undefined);
ContextAuthenLoginState.displayName = "ContextAuthenLoginState";

const ContextAuthenLoginAction = createContext<
  | {
      setAuthenData: React.Dispatch<
        React.SetStateAction<ContextAuthenLoginProviderType>
      >;
      resetContextAuthenLoginState: VoidFunction;
    }
  | undefined
>(undefined);
ContextAuthenLoginAction.displayName = "ContextAuthenLoginAction";

const ContextAuthenLoginProvider = ({ children }: { children: ReactNode }) => {
  const [authenData, setAuthenData] = useState<ContextAuthenLoginProviderType>({
    confirmationNo: "",
    expiresIn: 0,
    retryAfterTime: 0,
    codeOTP: "",
    flowName: "",
    type: OTPType.SMS,
  });
  const resetContextAuthenLoginState = useCallback(() => {
    setAuthenData({
      confirmationNo: "",
      expiresIn: 0,
      retryAfterTime: 0,
      codeOTP: "",
      flowName: "",
      type: OTPType.SMS,
    });
  }, []);
  const actions = useMemo(() => {
    return {
      setAuthenData,
      resetContextAuthenLoginState,
    };
  }, [resetContextAuthenLoginState]);
  return (
    <ContextAuthenLoginAction.Provider value={actions}>
      <ContextAuthenLoginState.Provider value={authenData}>
        {children}
      </ContextAuthenLoginState.Provider>
    </ContextAuthenLoginAction.Provider>
  );
};

export const useContextAuthenLoginState = () => {
  const contextAuthenLoginState = useContext(ContextAuthenLoginState);
  if (contextAuthenLoginState) {
    return contextAuthenLoginState;
  }
  throw new Error("ContextAuthenLoginState is undefined");
};

export const useContextAuthenLoginAction = () => {
  const contextAuthenLoginAction = useContext(ContextAuthenLoginAction);
  if (contextAuthenLoginAction) {
    return contextAuthenLoginAction;
  }
  throw new Error("ContextAuthenLoginAction is undefined");
};

export default ContextAuthenLoginProvider;
