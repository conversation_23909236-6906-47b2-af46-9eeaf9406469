import React, { useCallback, useEffect, useRef, useState } from "react";
import { useHistory, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import isEmpty from "lodash/isEmpty";
import classNames from "classnames";
import useStateRef from "hooks/useStateRef";
import Input from "app/components/Common/Input/Input";
import KeepAlive, { KeepAliveData } from "app/utils/KeepAlive";
import Icon from "app/components/Common/Icon";
import { API_STATUS_CODE, ROUTES } from "app/utils/constants";
import Button from "app/containers/Error/components/Button";
import FocusableWrapper from "app/components/FocusableWrapper";
import { EnterPressHandler, FocusContext, VieOnNavigation } from "core/KeyHandle";
import { AuthenticationMethod } from "types/common";
import { useContextTrackLocationState } from "context/ContextTrackLocation";
import { MESSAGES } from "types/common/Message";
import Message from "app/components/Common/Message";
import {
  authenticationLogin,
  authenticationPasswordForgot,
} from "app/endpoint/Authentication/service";
import SegmentManager from "app/utils/SegmentManager";
import {
  GlobalFeatureCurrentPage,
  GlobalFeatureEventNames,
  GlobalFeatureFlowAuthen,
  GlobalFeaturePropertyKeys,
  GlobalPropertyValues,
} from "app/components/Tracking/GlobalFeatureTracking";
import { RootState } from "app/store/store";
import Keyboard from "app/components/Common/KeyBoard";
import { EnumKeyboardType } from "app/components/Common/KeyBoard/types";
import { EnumKeyMap } from "app/components/Common/KeyBoard/constant";
import useIsGlobal from "hooks/useIsGlobal";
import { handleSuccessfulAuthentication } from "../utils";
import { useContextAuthenVerifyState } from "../Context/ContextAuthenVerify";
import { useContextAuthenRoutingState } from "../Context/ContextAuthenRouting";
import { CodeAPI } from "../type";
import { useContextAuthenLoginAction } from "../Context/ContextAuthenLogin";

interface Props {
  phoneCode: string;
  email: string;
  keepAliveData?: KeepAliveData;
  onBack: () => void;
}

const MIN_PASSWORD_LENGTH = 6;

const Step2: React.FC<Props> = ({ email, keepAliveData, onBack }) => {
  const history = useHistory();
  const location = useLocation<any>();
  const dispatch = useDispatch();
  const userFlow = useSelector((state: RootState) => state.app.userFlow);
  const keepAliveDataRef = useRef(keepAliveData);
  const [capslock, setCapslock] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [error, setError, errorRef] = useStateRef<string>("");
  const [password, setPassword, passwordRef] = useStateRef("");
  const { countryCode, phoneNumberServer, phoneNumber, phoneNumberDisplay } =
    useContextAuthenVerifyState();
  const [isLoginByEmail, setIsLoginByEmail, isLoginByEmailRef] =
    useStateRef<boolean>(false);
  const [passwordVisible, setPasswordVisible, passwordVisibleRef] =
    useStateRef<boolean>(false);

  const trackLocation = useContextTrackLocationState();
  const isGlobal = useIsGlobal();
  const { authenRouting } = useContextAuthenRoutingState();
  const { state, searchParams } = authenRouting;

  const { setAuthenData } = useContextAuthenLoginAction();

  useEffect(() => {
    setIsLoginByEmail(!isEmpty(email));
  }, [email]);

  useEffect(() => {
    if (keepAliveData && keepAliveData.extra.step === 2) {
      setCapslock(keepAliveData.extra.capslock);
      setError(keepAliveData.extra.error);
      setPassword(keepAliveData.extra.password);
      setPasswordVisible(keepAliveData.extra.passwordVisible);
    }
  }, [keepAliveData]);

  const onKeyboardEnter = (currentKey: string) => {
    const currentKeyLower = currentKey.toLowerCase();
    switch (currentKeyLower) {
      case "capslock": {
        setCapslock(!capslock);
        break;
      }
      case "clear": {
        setPassword("");
        setError("");
        break;
      }
      case "backspace": {
        setPassword(passwordRef.current.slice(0, -1));
        setError("");
        break;
      }
      default: {
        if (passwordRef.current.length === 20) return;
        setPassword(passwordRef.current + currentKey);
        break;
      }
    }
  };

  const onButtonEnter = useCallback<
    EnterPressHandler<{
      focusKey?: string;
      type?: string | undefined;
    }>
  >(async ({ type }) => {
    switch (type) {
      case "BACK":
        onBack();
        break;
      case "SIGN_IN": {
        if (isFetching) {
          return;
        }
        // Tracking
        SegmentManager.segmentAction(
          GlobalFeatureEventNames.LoginButtonSelected,
          {
            [GlobalFeaturePropertyKeys.flowAuthen]: email
              ? GlobalFeatureFlowAuthen.Email
              : GlobalFeatureFlowAuthen.Mobile,
          }
        );
        setError("");
        const userName = !isEmpty(email) ? email : phoneNumberServer;
        setIsFetching(true);
        const response = await authenticationLogin(
          userName,
          email ? "" : countryCode,
          passwordRef.current
        );

        const { code, message, result, statusCode, account } = response || {};

        switch (code) {
          case CodeAPI.Success: {
            await handleSuccessfulAuthentication({
              dispatch,
              history,
              trackLocation,
              state,
              searchParams,
              authenticationMethod: AuthenticationMethod.SignIn,
              userIsPremium: account?.isPremium,
              result: {
                ...result,
                profile: account,
              },
              isLocal: !isGlobal,
              flowAuthen: email
                ? GlobalFeatureFlowAuthen.Email
                : GlobalFeatureFlowAuthen.Mobile,
              userFlow,
              hotelAccount: account?.hotelAccount,
            });
            break;
          }
          case CodeAPI.RequestInvalid: {
            setError(message);
            break;
          }
          case CodeAPI.RequestLimitReached: {
            if (statusCode === API_STATUS_CODE.LIMIT_REQUEST) {
              setError(message);
            } else {
              setError(MESSAGES.SomethingWrong);
            }
            break;
          }
          default: {
            setError(MESSAGES.SomethingWrong);
            break;
          }
        }

        setIsFetching(false);
        break;
      }
      case "FORGOT_PASSWORD": {
        if (keepAliveDataRef.current) {
          keepAliveDataRef.current.extra.step = 2;
          keepAliveDataRef.current.extra.phoneNumber = phoneNumber;
          keepAliveDataRef.current.extra.capslock = capslock;
          keepAliveDataRef.current.extra.error = errorRef.current;
          keepAliveDataRef.current.extra.password = passwordRef.current;
          keepAliveDataRef.current.extra.passwordVisible =
            passwordVisibleRef.current;
          KeepAlive.saveData(keepAliveDataRef.current);
        }

        const response = await authenticationPasswordForgot({
          userName: email || phoneNumberServer,
          countryCode: email ? "" : countryCode,
        });

        if (response.code === CodeAPI.Success) {
          SegmentManager.segmentAction(GlobalFeatureEventNames.SendOtpSms, {
            [GlobalFeaturePropertyKeys.currentPage]:
              GlobalFeatureCurrentPage.ResetPassword,
            [GlobalFeaturePropertyKeys.flowName]:
              GlobalPropertyValues.ForgotPassword,
            ...(phoneNumberServer
              ? {
                  [GlobalFeaturePropertyKeys.flowAuthen]:
                    GlobalPropertyValues.Mobile,
                }
              : {}),
            ...(type
              ? { [GlobalFeaturePropertyKeys.Method]: response.result.type }
              : {}),
          });
          setAuthenData((prev) => {
            return {
              ...prev,
              confirmationNo: response.result.confirmationNo,
              expiresIn: response.result.expiresIn,
              type: response.result.type,
            };
          });
          history.replace({
            pathname: ROUTES.AUTH_RESET_PASSWORD,
            search: `?from=${location.pathname}`,
          });
        } else if (response.code === CodeAPI.OTPLimitReached) {
          setAuthenData((prev) => {
            return {
              ...prev,
              retryAfterTime: response.result.retryAfterTime,
            };
          });
          history.replace({
            pathname: ROUTES.AUTH_RESET_PASSWORD,
            search: `?from=${location.pathname}`,
          });
        } else if (
          response.code === CodeAPI.RequestInvalid ||
          (response.code === CodeAPI.RequestLimitReached &&
            response.statusCode === API_STATUS_CODE.LIMIT_REQUEST)
        ) {
          setError(response.message);
        } else if (response.code === CodeAPI.InvalidCaptcha) {
          Message.open(MESSAGES.CaptchaNotSupportedForgotPassword);
        } else {
          setError(MESSAGES.SomethingWrong);
        }

        // Tracking
        SegmentManager.segmentAction(
          GlobalFeatureEventNames.ForgotPasswordButtonSelected,
          {
            [GlobalFeaturePropertyKeys.currentPage]:
              GlobalFeatureCurrentPage.ResetPassword,
            [GlobalFeaturePropertyKeys.flowAuthen]: email
              ? GlobalFeatureFlowAuthen.Email
              : GlobalFeatureFlowAuthen.Mobile,
            [GlobalFeaturePropertyKeys.flowName]:
              GlobalPropertyValues.ForgotPassword,
          }
        );
        break;
      }
      case "SHOW_PASSWORD":
        setPasswordVisible(!passwordVisibleRef.current);
        break;
      default: {
        break;
      }
    }
  }, []);

  const onKeyboardPopout = useCallback(
    (direction: Direction) => {
      if (direction === "down") {
        if (password.length < MIN_PASSWORD_LENGTH) {
          VieOnNavigation.setFocus("VN:STEP2_BACK");
        } else {
          VieOnNavigation.setFocus("VN:STEP2_SIGN_IN");
        }
      }
    },
    [password]
  );

  return (
    <div className="block block--login block--login-otp">
      <div className="grid">
        <div className="col col-6 panel-left triangle-right-2">
          <div className="keyboard-container">
            <h3 className="block__title">Nhập mật khẩu </h3>
            <Keyboard
              type={EnumKeyboardType.PASSWORD}
              focusKey={EnumKeyMap.KEY_O}
              capslock={capslock}
              onEnter={onKeyboardEnter}
              onPopout={onKeyboardPopout}
              onReturn={onBack}
            />
          </div>
          <div className="action-container">
            <FocusableWrapper
              enterOnClick
              focusImplicitOnHover
              saveLastFocusedChild={false}
              preferredChildFocusKey="VN:STEP2_SIGN_IN"
              focusKey="VN:STEP2_BUTTON"
              onClick={() => {}}
            >
              {({ ref }) => {
                return (
                  <FocusContext.Provider value="VN:STEP2_BUTTON">
                    <div className="button-group" ref={ref}>
                      <Button
                        onEnter={onButtonEnter}
                        onReturn={onBack}
                        className="button"
                        focusKey="VN:STEP2_BACK"
                        type="BACK"
                      >
                        Quay lại
                      </Button>
                      <Button
                        onReturn={onBack}
                        onEnter={onButtonEnter}
                        disabled={
                          password.length < MIN_PASSWORD_LENGTH || isFetching
                        }
                        className="button"
                        focusKey="VN:STEP2_SIGN_IN"
                        type="SIGN_IN"
                      >
                        Đăng nhập
                      </Button>
                    </div>
                  </FocusContext.Provider>
                );
              }}
            </FocusableWrapper>
          </div>
        </div>
        <div className="col col-6 panel-right">
          <div className="muted">
            {isLoginByEmail ? (
              <>
                <span className="icon">
                  <Icon viewBox="0 0 48 48" name="vie-email-light" />
                </span>
                <div className="hint">
                  <div className="block__title user-email">{email}</div>
                </div>
              </>
            ) : (
              <>
                <span className="icon">
                  <Icon viewBox="0 0 32 32" name="vie-hand-phone-o-rc" />
                </span>
                <p className="hint">{phoneNumberDisplay}</p>
              </>
            )}
          </div>
          <div className="muted">
            <span className="icon">
              <Icon name="vie-key-skeleton-o" />
            </span>
            <Input
              className={classNames("input", {
                password: !passwordVisible,
              })}
              placeholder="Mật khẩu (6-20 ký tự)"
              value={password}
            />
          </div>
          <p
            className={classNames("error", {
              hide: error === "",
            })}
          >
            {error}
          </p>
          {error === "" && (
            <p
              className={classNames("warning", {
                hide: !capslock,
              })}
            >
              Bạn đang mở CAPSLOCK
            </p>
          )}
        </div>
        <div className="col col-12 panel-bottom">
          <div className="button-group">
            <Button
              onReturn={onBack}
              onEnter={onButtonEnter}
              className="button small"
              focusKey="VN:STEP2_FORGOT_PASSWORD"
              type="FORGOT_PASSWORD"
            >
              Quên mật khẩu?
            </Button>
            <Button
              onReturn={onBack}
              onEnter={onButtonEnter}
              disabled={false}
              className="button small"
              focusKey="VN:STEP2_SHOW_PASSWORD"
              type="SHOW_PASSWORD"
            >
              {passwordVisible ? "Ẩn mật khẩu" : "Hiện mật khẩu"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Step2;
