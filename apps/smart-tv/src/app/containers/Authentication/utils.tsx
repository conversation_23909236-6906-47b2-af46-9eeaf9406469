import { Dispatch } from "redux";
import { History } from "history";
import { AuthenticationMethod } from "types/common";
import Message from "app/components/Common/Message";
import { MESSAGES } from "types/common/Message";
import { getDailyPermission, getUserType } from "services/endpoint";
import { resetMastheadAdsStatus, signInSuccess } from "app/store/actions";
import { ROUTES } from "app/utils/constants";
import { ProfileStatusType } from "types/endpoint";
import { FlowName } from "app/components/Tracking/DeleteAccount";
import GAManager from "app/utils/GAManager";
import { AccountModel } from "app/models";
import SegmentManager from "app/utils/SegmentManager";
import {
  GlobalFeatureEventNames,
  GlobalFeatureFlowAuthen,
  GlobalFeaturePropertyKeys,
} from "app/components/Tracking/GlobalFeatureTracking";
import platform from "services/platform";
import { EnumParamsVideoIntro } from "context/types";
import {
  ContextAuthenRoutingSearchParams,
  ContextAuthenRoutingStates,
} from "./Context/ContextAuthenRouting";
import { CountryCode } from "./type";
import { FlowDelAccountType } from "../FlowDelAccount/types";
import { MaxTimeShowAutoPromotion } from "../Promotion/types";

export function verifyEmailFormat(email: string) {
  const regex =
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/i;
  return regex.test(email);
}

export const LIMIT_OTP_PER_DAY =
  "Bạn đã hết lượt gửi mã OTP hôm nay,<br /> vui lòng thử lại vào ngày mai";

// time: Epoch Time: multiple with 1000 to get correct time
export function createReachLimitOtpPerDayMsg(time: number) {
  if (!time) return "";
  const d = new Date(time * 1000);
  const date = d.getDate();
  const month = d.getMonth() + 1;
  const year = d.getFullYear();
  const hour = d.getHours();
  const min = d.getMinutes();
  const timeStr = `${hour > 9 ? "" : "0"}${hour}:${min > 9 ? "" : "0"}${min}`;
  const dateStr = `${date > 9 ? "" : "0"}${date}/${
    month > 9 ? "" : "0"
  }${month}/${year}`;
  // return `Bạn đã hết số lượt gửi OTP trong ngày, vui lòng thử lại vào lúc ${timeStr} - ${dateStr}, để được hỗ trợ thêm hãy liên hệ với chúng tô<NAME_EMAIL>`;
  return LIMIT_OTP_PER_DAY;
}

export const formatPhoneNumber = ({
  phoneNumber,
  countryCode,
  phoneCode,
}: {
  phoneNumber: string;
  countryCode: CountryCode;
  phoneCode: string; // format +84
}) => {
  if (phoneNumber.length === 10 && countryCode === CountryCode.VN) {
    return `${phoneCode} ${phoneNumber.slice(1)}`;
  }
  return `${phoneCode} ${phoneNumber}`;
};

const computeMessage = (authenticationMethod: AuthenticationMethod) => {
  let message = "";
  if (
    authenticationMethod === AuthenticationMethod.SignIn ||
    authenticationMethod === AuthenticationMethod.SignUp
  ) {
    message = MESSAGES.welcomeBack;
  } else if (authenticationMethod === AuthenticationMethod.ResetPassword) {
    message = MESSAGES.resetPasswordAndWelcomeBack;
  } else if (authenticationMethod === AuthenticationMethod.RecoveryAccount) {
    message = MESSAGES.RecoveryAccountSuccess;
  }
  return message;
};

const trackingData = ({
  profile,
  flowAuthen,
  authenticationMethod,
  isLocal,
}: {
  profile: AccountModel;
  flowAuthen?: GlobalFeatureFlowAuthen;
  authenticationMethod: AuthenticationMethod;
  isLocal: boolean;
}) => {
  GAManager.gaSetUser(profile.id);
  if (authenticationMethod === AuthenticationMethod.SignUp) {
    GAManager.gaEvent("signup_successfully", "sign_up", "signup_successfully");
    SegmentManager.segmentAction(
      GlobalFeatureEventNames.SignUpSuccessfullySpace,
      {
        [GlobalFeaturePropertyKeys.flowAuthen]: flowAuthen,
      }
    );
  } else if (authenticationMethod === AuthenticationMethod.ResetPassword) {
    SegmentManager.segmentAction(GlobalFeatureEventNames.ResetPasswordSuccess);
  }
  GAManager.gaEvent(
    GlobalFeatureEventNames.LoginSuccessfully,
    "log_in",
    GlobalFeatureEventNames.LoginSuccessfully
  );
  SegmentManager.segmentIdentify(profile);
  SegmentManager.segmentAction(GlobalFeatureEventNames.LoginSuccessfullySpace, {
    [GlobalFeaturePropertyKeys.flowAuthen]: flowAuthen,
  });
};

export const handleSuccessfulAuthentication = async ({
  dispatch,
  history,
  trackLocation,
  state,
  searchParams,
  authenticationMethod,
  result,
  flowAuthen,
  isLocal,
  userFlow,
  hotelAccount = false,
  userIsPremium,
}: {
  dispatch: Dispatch<any>;
  history: History;
  trackLocation: any;
  state: ContextAuthenRoutingStates;
  searchParams: ContextAuthenRoutingSearchParams;
  authenticationMethod: AuthenticationMethod;
  result: any;
  flowAuthen?: GlobalFeatureFlowAuthen;
  isLocal: boolean;
  userFlow: boolean;
  hotelAccount?: boolean;
  userIsPremium?: number;
}) => {
  const { accessToken: token, refreshToken, profile } = result || {};

  const customState = {
    ...state,
    wasActionSignIn: true,
  } as ContextAuthenRoutingStates;
  // Start Flow: Account is in recovery account process
  if (
    authenticationMethod === AuthenticationMethod.ResetPassword ||
    authenticationMethod === AuthenticationMethod.SignIn
  ) {
    const status = profile?.status as ProfileStatusType;
    if (status === ProfileStatusType.DeleteInProgress) {
      history.replace({
        pathname: `${ROUTES.AUTH_DELETE_ACCOUNT}/${FlowDelAccountType.RecoveryAccount}`,
        state: {
          account: profile,
          token,
          flow:
            authenticationMethod === AuthenticationMethod.ResetPassword
              ? FlowName.RestoreAccountForgotPassword
              : FlowName.RestoreAccountLoginPhone,
        },
      });
      return;
    }
  }
  // End Flow: Account is in recover account process

  // Start get and handle info of profile
  const resUserType: {
    [key: string]: any;
  } = await getUserType(token).catch(() => ({}));
  const resPackagePermission: {
    [key: string]: any;
  } = await getDailyPermission(token).catch(() => ({}));

  const userType = resUserType?.type || 0;
  const packageGroupId = resUserType?.package_group_id || 0;
  const livetvGroupId = resUserType?.livetv_group_id || "";
  const hideBuyPackage = resUserType?.hide_button_buy_package || false;
  const needShowOverlap = resPackagePermission?.paid_message_need || false;
  const overlapInfo = {
    msg: "",
    btnText: "",
  };
  overlapInfo.msg = resPackagePermission?.paid_message || "";
  overlapInfo.btnText = resPackagePermission?.primary_button_message || "";
  // End get and handle info of profile

  // Reset Masthead Ads
  dispatch(resetMastheadAdsStatus());

  // Save profile data to store
  dispatch(
    signInSuccess({
      token,
      refreshToken,
      profile,
      userType,
      packageGroupId,
      livetvGroupId,
      hideBuyPackage,
      needShowOverlap,
      overlapInfo,
    })
  );

  if (authenticationMethod !== AuthenticationMethod.RecoveryAccount) {
    trackingData({
      profile,
      flowAuthen,
      authenticationMethod,
      isLocal,
    });
  }
  // Start handling next route
  const {
    searchParamState,
    searchParamRedirect,
    searchParamSlug,
    searchParamDialogAction,
    searchParamEpg,
  } = searchParams;
  let search = "";
  if (searchParamState) {
    const nextUrlParams = new URLSearchParams();
    nextUrlParams.append("state", searchParamState);
    search = nextUrlParams.toString();
  }

  if (authenticationMethod === AuthenticationMethod.RecoveryAccount) {
    history.replace(ROUTES.ROOT);
    return;
  }

  // Start Tracking
  const locationState = customState || {};
  if (searchParamRedirect && searchParamRedirect.indexOf("payment") >= 0) {
    locationState.referal =
      authenticationMethod === AuthenticationMethod.SignUp
        ? "Đăng ký"
        : "Đăng nhập";
  }
  // End  Tracking

  let nextLocation: {
    pathname: string;
    search?: string;
    state?: any;
  } = {
    pathname: searchParamRedirect || "/",
    search,
    state: locationState,
  };

  // For flow Signup
  if (
    profile.showGuestidFlow &&
    userFlow &&
    authenticationMethod === AuthenticationMethod.SignUp
  ) {
    if (!searchParamRedirect || !searchParamRedirect.includes("promotion")) {
      // uu tien flow promotion
      nextLocation = {
        pathname: ROUTES.PERSONALIZATION,
      };
    }
  }

  // For flow Signup
  if (searchParamRedirect === ROUTES.AUTO_PROMOTION) {
    setTimeout(() => {
      history.replace(nextLocation);
    }, 50);
    return;
  }

  // const { onAutoPromotion } = platform;
  // const countShowPromotion = localStorage.getItem("countShowPromotion");

  if (searchParamRedirect && searchParamRedirect.includes(ROUTES.LIVE_TV)) {
    // check Livetv login add yêu thích
    const addFavorite = searchParamState === "addFavorite";

    const newSearchParams = new URLSearchParams(search ?? "");

    if (searchParamEpg) {
      newSearchParams.append("epg", searchParamEpg);
    }
    history.replace({
      pathname: ROUTES.MULTI_PROFILE_LOBBY,
      search: newSearchParams.toString(),
      state: {
        ...customState,
        redirect: nextLocation,
        isBack: addFavorite,
        authenticationMethod,
      },
    });

    const message = computeMessage(authenticationMethod);
    if (message) {
      Message.open(message);
    }

    return;
  }

  const successLocation = customState?.successLocation;

  // TODO: flow lobby in voucher page
  if (
    successLocation &&
    successLocation?.pathname &&
    successLocation.pathname.includes("voucher")
  ) {
    history.goBack();
    if (successLocation.state) {
      successLocation.state.authenticationMethod = authenticationMethod;
    }
    setTimeout(() => {
      history.replace(successLocation);
      const successMsg = customState?.successMsg;
      if (!hotelAccount && successMsg) {
        Message.open(successMsg);
      }
    }, 100);
    return;
  }

  if (
    successLocation &&
    successLocation?.pathname &&
    !successLocation.pathname.includes("payment")
  ) {
    const message = computeMessage(authenticationMethod);
    const redirect = {
      pathname: ROUTES.MULTI_PROFILE_LOBBY,
      state: {
        ...customState,
        redirect: successLocation,
        isBack: customState?.isBack !== false,
        authenticationMethod,
      },
    };
    // if (onAutoPromotion && countShowPromotion !== MaxTimeShowAutoPromotion) {
    //   history.replace({
    //     pathname: ROUTES.AUTO_PROMOTION,
    //     state: {
    //       redirect,
    //       successMsg: message,
    //     },
    //   });
    // } else {
    setTimeout(() => {
      history.replace(redirect);
      if (message) {
        Message.open(message);
      }
    }, 50);
    // }

    return;
  }

  if (
    successLocation &&
    successLocation?.pathname &&
    successLocation.pathname.includes("payment")
  ) {
    if (successLocation.state) {
      successLocation.state.authenticationMethod = authenticationMethod;
    }
    let successMsg = customState?.successMsg;
    const successMsgSignUp = customState?.successMsgSignUp;
    if (!successMsg && authenticationMethod === AuthenticationMethod.SignIn) {
      successMsg = MESSAGES.welcomeBack;
    } else if (!successMsg) {
      successMsg = MESSAGES.welcomeBack;
    }
    // if (onAutoPromotion && countShowPromotion !== MaxTimeShowAutoPromotion) {
    //   history.replace({
    //     pathname: ROUTES.AUTO_PROMOTION,
    //     state: {
    //       redirect: successLocation,
    //       successMsg,
    //     },
    //   });
    // } else {
    if (customState?.isBack !== false) {
      history.goBack();
    }
    setTimeout(() => {
      history.replace(successLocation);
      if (!hotelAccount) {
        if (
          authenticationMethod === AuthenticationMethod.SignIn &&
          (customState?.successTvodSvodSignInMsg || successMsg)
        ) {
          Message.open(
            customState?.successTvodSvodSignInMsg &&
              (result?.profile?.hadTvod || userIsPremium === 1)
              ? customState?.successTvodSvodSignInMsg
              : successMsg
          );
        } else if (
          authenticationMethod === AuthenticationMethod.SignUp &&
          successMsgSignUp
        ) {
          Message.open(successMsgSignUp);
        }
      }
    }, 200);
    // }
    return;
  }

  setTimeout(() => {
    let _authenticationMethod = authenticationMethod;
    if (
      (authenticationMethod === AuthenticationMethod.SignUp &&
        !searchParamRedirect) ||
      customState?.isShowLobby
    ) {
      _authenticationMethod = AuthenticationMethod.SignUpShowLobbby;
    }
    const isUseTrackLocation = customState?.isUseTrackLocation;
    const isFromVideoIntroSlugPage = !!searchParamSlug;
    if (isFromVideoIntroSlugPage) {
      if (nextLocation?.pathname) {
        const searchParams = new URLSearchParams(nextLocation.search);
        searchParams.append("slug", searchParamSlug || "");
        if (searchParamDialogAction !== "") {
          searchParams.append("dialog-action", `${searchParamDialogAction}`);
        }
        nextLocation.search = searchParams.toString();
      } else if (trackLocation?.pathname) {
        const searchParams = new URLSearchParams(trackLocation);
        searchParams.append("slug", searchParamSlug || "");
        trackLocation.search = searchParams.toString();
      }
    }
    let redirect = trackLocation || nextLocation;
    if (nextLocation.pathname?.includes?.(ROUTES.SETTING)) {
      redirect = {
        pathname: "/",
      };
    } else if (isUseTrackLocation === false) {
      redirect = nextLocation;
    } else if (nextLocation?.pathname?.includes(ROUTES.PERSONALIZATION)) {
      redirect = nextLocation;
    }
    // else if (
    //   onAutoPromotion &&
    //   countShowPromotion !== MaxTimeShowAutoPromotion &&
    //   typeof trackLocation?.pathname === "string" &&
    //   trackLocation?.pathname?.includes(ROUTES.AUTO_PROMOTION)
    // ) {
    //   redirect = nextLocation;
    //   if (redirect && state?.isBack) {
    //     redirect.state.isBack = false;
    //   } else {
    //     redirect.state.isBack = true;
    //   }
    // }
    let isBack = true;
    if (
      (!redirect?.state?.isBack &&
        authenticationMethod === AuthenticationMethod.SignIn) ||
      (typeof trackLocation?.search === "string" &&
        trackLocation?.search?.includes?.(EnumParamsVideoIntro.VideoEpisode)) ||
      trackLocation?.search?.includes?.(
        EnumParamsVideoIntro.VideoRecommended
      ) ||
      trackLocation?.search?.includes?.(EnumParamsVideoIntro.VideoRelated)
    ) {
      isBack = false;
    }
    const searchParams = new URLSearchParams(redirect?.search || "");
    if (searchParamState === "player") {
      searchParams.append("state", "player");
    }
    const finalLocation = {
      pathname: ROUTES.MULTI_PROFILE_LOBBY,
      state: {
        ...customState,
        redirect,
        isBack,
        authenticationMethod: _authenticationMethod,
      },
    };
    const message = computeMessage(authenticationMethod);
    // if (onAutoPromotion && countShowPromotion !== MaxTimeShowAutoPromotion) {
    //   sessionStorage.setItem("isShowPromotionOnHome", "1");
    //   history.replace({
    //     pathname: ROUTES.AUTO_PROMOTION,
    //     state: {
    //       redirect: finalLocation,
    //       successMsg: message,
    //     },
    //   });
    // } else {
    history.replace(finalLocation);
    if (message) {
      Message.open(message);
    }
    // }
  }, 100);
  // End handling next route
};

export const authenticationbyToken = async ({
  dispatch,
  result,
}: {
  dispatch: Dispatch<any>;
  result: any;
}) => {
  const { accessToken: token, refreshToken, profile } = result || {};
  // Start get and handle info of profile
  const resUserType: {
    [key: string]: any;
  } = await getUserType(token).catch(() => ({}));
  const resPackagePermission: {
    [key: string]: any;
  } = await getDailyPermission(token).catch(() => ({}));

  const userType = resUserType?.type || 0;
  const packageGroupId = resUserType?.package_group_id || 0;
  const livetvGroupId = resUserType?.livetv_group_id || "";
  const hideBuyPackage = resUserType?.hide_button_buy_package || false;
  const needShowOverlap = resPackagePermission?.paid_message_need || false;
  const overlapInfo = {
    msg: "",
    btnText: "",
  };
  overlapInfo.msg = resPackagePermission?.paid_message || "";
  overlapInfo.btnText = resPackagePermission?.primary_button_message || "";
  // End get and handle info of profile

  // Reset Masthead Ads
  dispatch(resetMastheadAdsStatus());
  dispatch(
    signInSuccess({
      token,
      refreshToken,
      profile,
      userType,
      packageGroupId,
      livetvGroupId,
      hideBuyPackage,
      needShowOverlap,
      overlapInfo,
    })
  );
};

export enum AuthenFlowName {
  RegisterForPayment = "register_for_payment",
  RegistrationTrigger = "registration_trigger",
  PaymentTrigger = "payment_trigger",
  ProfileIcon = "profile_icon",
  RegistrationFeature = "registration_feature",
  BannerRegister = "banner_register",
}

export enum AuthenFeatureName {
  AddToList = "add_to_list",
  RemindMe = "remind_me",
  ReportIssue = "report_issue",
  RegisterForRecommend = "register_for_recommend",
}
