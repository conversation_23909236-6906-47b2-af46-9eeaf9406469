import React, { useCallback, useState } from "react";
import isEmpty from "lodash/isEmpty";
import classNames from "classnames";
import { useHistory } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import Input from "app/components/Common/Input/Input";
import Keyboard from "app/components/Common/KeyBoard";
import Icon from "app/components/Common/Icon";
import Button from "app/containers/Error/components/Button";
import FocusableWrapper from "app/components/FocusableWrapper";
import { EnumKeyboardType } from "app/components/Common/KeyBoard/types";
import { EnumKeyMap } from "app/components/Common/KeyBoard/constant";
import {
  EnterPressHandler,
  FocusContext,
  VieOnNavigation,
} from "core/KeyHandle";
import { MESSAGES } from "types/common/Message";
import { AuthenticationMethod } from "types/common";
import { authenticationOTPConfirm } from "app/endpoint";
import { GlobalFeatureFlowAuthen } from "app/components/Tracking/GlobalFeatureTracking";
import { API_STATUS_CODE } from "app/utils/constants";
import useIsGlobal from "hooks/useIsGlobal";
import { RootState } from "app/store/store";
import { useContextTrackLocationState } from "context/ContextTrackLocation";
import { EnumSignUp, StepValue } from "./SignUpPage";
import { handleSuccessfulAuthentication } from "../utils";
import { useContextAuthenVerifyState } from "../Context/ContextAuthenVerify";
import { CodeAPI } from "../type";
import { useContextAuthenLoginState } from "../Context/ContextAuthenLogin";
import { useContextAuthenRoutingState } from "../Context/ContextAuthenRouting";

interface Props {
  error: string;
  setError: React.Dispatch<React.SetStateAction<string>>;
  step: number;
  capslock: boolean;
  setCapslock: React.Dispatch<React.SetStateAction<boolean>>;
  step2Value: StepValue;
  setStep2Value: React.Dispatch<React.SetStateAction<StepValue>>;
  step3Value: StepValue;
  setStep3Value: React.Dispatch<React.SetStateAction<StepValue>>;
  passwordVisible: boolean;
  setPasswordVisible: React.Dispatch<React.SetStateAction<boolean>>;
  onSuccess: () => void;
  onBack: () => void;
}

const MIN_PASSWORD_LENGTH = 6;
const MAX_PASSWORD_LENGTH = 20;

const Step2: React.FC<Props> = ({
  error,
  setError,
  capslock,
  setCapslock,
  step,
  step2Value,
  setStep2Value,
  step3Value,
  setStep3Value,
  onSuccess,
  onBack,
  passwordVisible,
  setPasswordVisible,
}) => {
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const { phoneNumberDisplay, email } = useContextAuthenVerifyState();
  const { value: password } = step2Value;
  const { value: confirm } = step3Value;
  const dispatch = useDispatch();
  const userFlow = useSelector((state: RootState) => state.app.userFlow);
  const history = useHistory();
  const isGlobal = useIsGlobal();
  const { confirmationNo, codeOTP } = useContextAuthenLoginState();

  const trackLocation = useContextTrackLocationState();
  const { authenRouting } = useContextAuthenRoutingState();
  const { state, searchParams } = authenRouting;

  const onKeyboardEnter = (currentKey: string) => {
    const currentKeyLower = currentKey.toLowerCase();
    switch (currentKeyLower) {
      case "capslock": {
        setCapslock(!capslock);
        break;
      }
      case "clear": {
        if (step === EnumSignUp.PASSWORD) {
          setStep2Value({ ...step2Value, value: "" });
        } else {
          setStep3Value({ ...step3Value, value: "" });
        }
        setError("");
        break;
      }
      case "backspace": {
        if (step === EnumSignUp.PASSWORD) {
          setStep2Value({ ...step2Value, value: password.slice(0, -1) });
        } else {
          setStep3Value({ ...step3Value, value: confirm.slice(0, -1) });
        }
        setError("");
        break;
      }
      default: {
        if (step === EnumSignUp.PASSWORD) {
          if (password?.length === MAX_PASSWORD_LENGTH) return;
          setStep2Value({ ...step2Value, value: password + currentKey });
        } else {
          if (confirm?.length === MAX_PASSWORD_LENGTH) return;
          setStep3Value({ ...step3Value, value: confirm + currentKey });
        }
        break;
      }
    }
  };

  const onKeyboardPopout = useCallback(
    (direction: Direction) => {
      if (direction === "down") {
        if (step === EnumSignUp.PASSWORD) {
          if (password.length < MIN_PASSWORD_LENGTH) {
            VieOnNavigation.setFocus("VN:STEP2_BACK");
          } else {
            VieOnNavigation.setFocus("VN:STEP2_CONTINUE");
          }
        } else if (step === EnumSignUp.CONFIRM) {
          if (confirm.length < MIN_PASSWORD_LENGTH) {
            VieOnNavigation.setFocus("VN:STEP2_BACK");
          } else {
            VieOnNavigation.setFocus("VN:STEP3_SIGN_UP");
          }
        }
      }
    },
    [step, password, confirm]
  );

  const onButtonEnter = useCallback<
    EnterPressHandler<{
      focusKey?: string;
      type?: string | undefined;
    }>
  >(
    async ({ type }) => {
      switch (type) {
        case "BACK": {
          if (onBack) {
            onBack();
          }
          break;
        }
        case "SIGN_UP": {
          if (isFetching) {
            return;
          }
          if (confirm !== password) {
            setError(MESSAGES.PasswordAndRepeatIsNotTheSame);
            return;
          }
          setIsFetching(true);
          const response = await authenticationOTPConfirm(
            confirmationNo,
            codeOTP,
            password
          );
          setIsFetching(false);
          const { code, message, result, account } = response || {};
          switch (code) {
            case CodeAPI.Success: {
              await handleSuccessfulAuthentication({
                dispatch,
                history,
                trackLocation,
                state,
                searchParams,
                authenticationMethod: AuthenticationMethod.SignUp,
                result: {
                  ...result,
                  profile: account,
                },
                isLocal: !isGlobal,
                flowAuthen: email
                  ? GlobalFeatureFlowAuthen.Email
                  : GlobalFeatureFlowAuthen.Mobile,
                userFlow,
                hotelAccount: account?.hotelAccount,
              });
              break;
            }
            case CodeAPI.RequestInvalid: {
              setError(message);
              break;
            }
            case CodeAPI.RequestLimitReached: {
              if (
                response.code === CodeAPI.RequestLimitReached &&
                response.statusCode === API_STATUS_CODE.LIMIT_REQUEST
              ) {
                setError(message);
              } else {
                setError(MESSAGES.SomethingWrong);
              }
              break;
            }
            default: {
              setError(MESSAGES.SomethingWrong);
              break;
            }
          }
          break;
        }
        case "CONTINUE":
          setError("");
          onSuccess();
          break;
        case "SHOW_PASSWORD":
          setPasswordVisible(!passwordVisible);
          break;
        default: {
          break;
        }
      }
    },
    [passwordVisible, step2Value, step3Value, codeOTP, confirmationNo]
  );

  let title = phoneNumberDisplay;
  if (!isEmpty(email)) {
    title = email;
  }

  return (
    <div className="block block--login">
      <div className="grid">
        <div className="col col-6 panel-left triangle-right-2">
          <div className="keyboard-container">
            <h3 className="block__title">Nhập mật khẩu </h3>
            {step === EnumSignUp.PASSWORD && (
              <Keyboard
                type={EnumKeyboardType.PASSWORD}
                focusKey={EnumKeyMap.KEY_O}
                capslock={capslock}
                onEnter={onKeyboardEnter}
                onPopout={onKeyboardPopout}
                onReturn={onBack}
              />
            )}
            {step === EnumSignUp.CONFIRM && (
              <Keyboard
                type={EnumKeyboardType.PASSWORD}
                focusKey={EnumKeyMap.KEY_O}
                capslock={capslock}
                onEnter={onKeyboardEnter}
                onPopout={onKeyboardPopout}
                onReturn={onBack}
              />
            )}
          </div>
          <div className="action-container">
            <FocusableWrapper
              enterOnClick
              focusImplicitOnHover
              saveLastFocusedChild={false}
              preferredChildFocusKey="VN:STEP2_CONTINUE"
              focusKey="VN:STEP2_BUTTON"
              onClick={() => {}}
            >
              {({ ref, focused }) => {
                return (
                  <FocusContext.Provider value="VN:STEP2_BUTTON">
                    <div className="button-group" ref={ref}>
                      <Button
                        onEnter={onButtonEnter}
                        onReturn={onBack}
                        className="button"
                        focusKey="VN:STEP2_BACK"
                        type="BACK"
                      >
                        Quay lại
                      </Button>
                      {step === EnumSignUp.PASSWORD && (
                        <Button
                          onEnter={onButtonEnter}
                          onReturn={onBack}
                          disabled={password?.length < MIN_PASSWORD_LENGTH}
                          className="button"
                          focusKey="VN:STEP2_CONTINUE"
                          type="CONTINUE"
                        >
                          Xác nhận
                        </Button>
                      )}
                      {step === EnumSignUp.CONFIRM && (
                        <Button
                          onEnter={onButtonEnter}
                          onReturn={onBack}
                          disabled={
                            confirm.length < MIN_PASSWORD_LENGTH || isFetching
                          }
                          className="button"
                          focusKey="VN:STEP3_SIGN_UP"
                          type="SIGN_UP"
                        >
                          Đăng ký tài khoản
                        </Button>
                      )}
                    </div>
                  </FocusContext.Provider>
                );
              }}
            </FocusableWrapper>
          </div>
        </div>
        <div className="col col-6 panel-right">
          <div className="muted">
            <span className="icon">
              <Icon name="vie-hand-phone-o-rc" />
            </span>
            <div className="hint">
              <div className="block__title">{title}</div>
            </div>
          </div>
          <div className="muted">
            <span className="icon" style={{ zIndex: 5 }}>
              <Icon name="vie-key-skeleton-o" />
            </span>
            <Input
              className={classNames("input--custom", {
                password: !passwordVisible,
                "input--focus": step === EnumSignUp.PASSWORD,
                "input--non-border": step === EnumSignUp.CONFIRM,
              })}
              placeholder="Mật khẩu (6-20 ký tự)"
              value={password}
            />
          </div>
          <div className="muted">
            <span className="icon">
              <Icon name="vie-key-skeleton-o" />
            </span>
            <Input
              className={classNames("input--custom", {
                password: !passwordVisible,
                "input--focus": step === EnumSignUp.CONFIRM,
                "input--non-border": step === EnumSignUp.PASSWORD,
              })}
              placeholder="Xác nhận mật khẩu mới (6-20 ký tự)"
              value={confirm}
            />
          </div>
          <p
            className={classNames("error", {
              hide: error === "",
            })}
          >
            {error}
          </p>
          {error === "" && (
            <p
              className={classNames("warning", {
                hide: !capslock,
              })}
            >
              Bạn đang mở CAPSLOCK
            </p>
          )}
        </div>
        <div className="col col-12 panel-bottom">
          <div className="button-group">
            <Button
              onEnter={onButtonEnter}
              onReturn={onBack}
              className="button small"
              focusKey="VN:STEP2_SHOW_PASSWORD"
              type="SHOW_PASSWORD"
            >
              {passwordVisible ? "Ẩn mật khẩu" : "Hiện mật khẩu"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Step2;
