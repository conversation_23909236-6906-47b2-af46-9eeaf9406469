import React, { memo, useEffect } from "react";
import { Route, Switch, useLocation } from "react-router-dom";
import isEmpty from "lodash/isEmpty";
import { ReturnTipBox } from "app/components/Common/Portal/TipBox";
import { ROUTES } from "app/utils/constants";
import Logo from "app/components/Common/Logo";
import useAuthMenu from "app/containers/Authentication/hooks/useAuthMenu";
import LayoutSingle from "app/layouts/LayoutSingle";
import LayoutSideMenu from "app/layouts/LayoutSideMenu";
import { AuthenticationMethod } from "types/common";
import bgDefault from "assets/images/auth_bg.png";
import SegmentManager from "app/utils/SegmentManager";
import {
  LocalFeatureEventNames,
  LocalFeaturePropertyKeys,
  LocalPropertyValues,
} from "app/components/Tracking/LocalFeatureTracking";
import Verify from "./Verify/Verify";
import { ContextAuthenVerifyProvider } from "./Context/ContextAuthenVerify";
import style from "./style.module.scss";
import SignInPage from "./SignIn/SignInPage";
import SignUpPage from "./SignUp/SignUpPage";
import "./style.scss";
import ResetPassword from "./ResetPassword/ResetPassword";
import ContextAuthenLoginProvider from "./Context/ContextAuthenLogin";
import { useContextAuthenRoutingAction } from "./Context/ContextAuthenRouting";
import { EnumAuthTitle } from "./type";
import FlowDelAccount from "../FlowDelAccount";

const StepRestoreAccount = React.lazy(() => import("./RestoreAccount"));
const Authentication = memo(function Authentication() {
  const location = useLocation<any>();

  const setAuthenRouting = useContextAuthenRoutingAction();
  const authMenu = useAuthMenu();
  const { background } = authMenu || {};

  // Save routing state
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const searchParamRedirect = urlParams.get("redirect") || "";
    const searchParamState = urlParams.get("state") || "";
    const searchParamSlug = urlParams.get("slug") || "";
    const searchParamEpg = urlParams.get("epg") || "";
    const searchParamDialogAction = urlParams.get("dialog-action") || "";
    const state = location?.state;
    setAuthenRouting({
      state: {
        ...state,
        redirect: state?.redirect ?? null,
        authenticationMethod:
          state?.authenticationMethod || AuthenticationMethod.SignIn,
        isBack: state?.isBack,
        isShowLobby: state?.isShowLobby || false,
      },
      searchParams: {
        searchParamRedirect,
        searchParamState,
        searchParamSlug,
        searchParamDialogAction,
        searchParamEpg,
      },
      titleAuthenticationMenu: state?.title || EnumAuthTitle.Content,
      steps: {
        stepMenu: {
          ...location,
        },
      },
    });
    SegmentManager.segmentAction(LocalFeatureEventNames.RegisterScreenLoaded, {
      [LocalFeaturePropertyKeys.CurrentPage]:
        LocalPropertyValues.RegisterScreen,
      [LocalFeaturePropertyKeys.FlowName]:
        location.state?.authenFlowName || location.state?.flowName || null,
      [LocalFeaturePropertyKeys.FeatureName]:
        location.state?.authenFeatureName || null,
    });
    return () => {
      setAuthenRouting({
        state: {
          redirect: null,
          authenticationMethod: AuthenticationMethod.NoFlow,
          isBack: false,
        },
        searchParams: {
          searchParamRedirect: "",
          searchParamState: "",
          searchParamSlug: "",
          searchParamDialogAction: "",
        },
        titleAuthenticationMenu: "",
        steps: {},
      });
    };
  }, []);

  return (
    <ContextAuthenVerifyProvider>
      <ContextAuthenLoginProvider>
        <div
          className={`authentication ${style.authentication} ${
            location.pathname.includes("menu") ? "logo--left" : "logo--right"
          }`}
        >
          <div
            className={`${style.authenticationBg}`}
            style={{
              background: `url(${
                !isEmpty(background) ? background : bgDefault
              }) no-repeat center center`,
              backgroundSize: "cover",
            }}
          />
          <Logo className="logo logo--top" />
          <Switch>
            <Route exact path={ROUTES.LOGIN_STEP}>
              <LayoutSingle />
            </Route>
            <Route exact path={ROUTES.LOGIN}>
              <LayoutSideMenu />
            </Route>
            <Route path={ROUTES.AUTH_VERIFY}>
              <Verify />
            </Route>
            <Route path={ROUTES.AUTH_RESET_PASSWORD}>
              <ResetPassword />
            </Route>
            <Route path={ROUTES.SIGN_IN}>
              <SignInPage />
            </Route>
            <Route path={ROUTES.SIGN_UP}>
              <SignUpPage />
            </Route>
            <Route path={`${ROUTES.AUTH_DELETE_ACCOUNT}/:flowtype`}>
              <FlowDelAccount />
            </Route>
            <Route path={ROUTES.AUTH_RESTORE_ACCOUNT}>
              <StepRestoreAccount />
            </Route>
          </Switch>
          <ReturnTipBox />
        </div>
      </ContextAuthenLoginProvider>
    </ContextAuthenVerifyProvider>
  );
});

export default Authentication;
