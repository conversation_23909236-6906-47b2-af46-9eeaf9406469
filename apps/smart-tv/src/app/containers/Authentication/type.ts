export enum CountryCode {
  US = "US",
  VN = "VN",
}

export enum PhoneCode {
  US = "+1",
  VN = "+84",
}

export enum LanguageCode {
  Vietnamese = "vi",
  English = "en",
}

export enum EnumPhoneLength {
  Min = 9,
  Max = 10,
}

// Define which method B<PERSON> uses to send OTP
// Provider = 1 means email method. The rest is phone method
export enum Provider {
  Mobile = 0,
  Email,
  Google,
  Facebook,
  Apple,
  Hotel,
}

export enum FOCUS_KEY_COMMON {
  // focus key for keys in keyboard
  // format: Prefix_KeyName
  KEY_FOCUS_ITEM_PREFIX = "VN:KEY_ITEM",
}

export enum CodeAPI {
  Success = 0, // Thành công
  RequestInvalid = 400, // Dữ liệu đầu vào không hợp lệ
  AccountAlreadyExists = 4009, // Tài khoản đã tồn tại
  OTPLimitReached = 4010, // Đã đạt giới hạn gửi OTP trong ngày
  RequestLimitReached = 13, // Đạt giới hạn số lần gọi API trong khoảng thời gian nhất định
  InvalidCaptcha = 4013 // Captcha không hợp lệ - nếu BE bật check captcha cho SmartTV, luôn trả về lỗi này
}

export enum EnumFlow {
  RecoveryAccount = "RecoveryAccount",
}

export enum EnumAuthTitle {
  Content = "Đăng ký tài khoản / Đăng nhập VieON để tận hưởng kho Phim, Show, Thể thao, Truyền hình cực đỉnh",
  RegisterByPayment = "Đăng ký tài khoản / Đăng nhập để tiếp tục thanh toán",
  RegisterByVoucherCode = "Đăng ký tài khoản / Đăng nhập để nhập mã VieON",
  RegisterByReport = "Đăng ký tài khoản / Đăng nhập để báo lỗi nội dung này",
  RegisterByAddToList = "Đăng ký tài khoản / Đăng nhập để thêm nội dung này vào danh sách của bạn",
  RegisterBySetting = "Đăng ký tài khoản / Đăng nhập để sử dụng chức năng Cài đặt",
  RegisterByRemind = "Đăng ký tài khoản / Đăng nhập để nhận thông báo ngay khi nội dung này ra mắt",
  RegisterByRegistrationTrial = "Nội dung bắt buộc đăng nhập. Vui lòng đăng ký/đăng nhập để trải nghiệm toàn bộ nội dung",
}
