/// @group setting
@use "../../../assets/scss/settings/function" as fn;

.authentication {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  // :global .btn.btn--ghost {
  //   background: rgba(34, 34, 34, 0.7);
  //   border: 1.5px solid #646464;
  // }

  :global .btn__dialog-RecoveryAccount.focus {
    background: #fff !important;
    border-color: #fff !important;
  }
  :global .btn__dialog-RequestRemoveAccount.focus {
    background: #fff !important;
    border-color: #fff !important;
    color: #222 !important;
  }

  :global .btn__dialog-RequestRemoveAccount {
    color: #fff !important;
  }
}

.authenticationBg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: -1;
}
