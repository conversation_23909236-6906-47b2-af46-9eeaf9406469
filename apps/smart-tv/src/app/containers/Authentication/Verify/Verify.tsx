import React, { memo, useCallback } from "react";
import { Redirect, Route, Switch, useHistory } from "react-router-dom";
import { ROUTES } from "app/utils/constants";
import VerifyPhone from "./VerifyPhone";
import VerifyEmail from "./VerifyEmail";
import "./style.scss";
import useRouterSearchFrom from "../hooks/useRouterSearchFrom";
import { useContextAuthenVerifyAction } from "../Context/ContextAuthenVerify";
import { useContextAuthenLoginAction } from "../Context/ContextAuthenLogin";
import { useContextAuthenRoutingState } from "../Context/ContextAuthenRouting";

export const FOCUS_KEY = {
  FocusKeyPhone: "VN:AuthenVerifyPhone",
  FocusKeyEmail: "VN:AuthenVerifyEmail",
  FocusKeyPhoneKeyboard: "VN:AuthenVerifyPhoneKeyboard",
  FocusKeyEmailKeyboard: "VN:AuthenVerifyEmailKeyboard"
};

const Verify = memo(function Verify() {
  const history = useHistory();

  const fromRoute = useRouterSearchFrom();

  const { resetContextState } = useContextAuthenVerifyAction();
  const { resetContextAuthenLoginState } = useContextAuthenLoginAction();
  const { authenRouting } = useContextAuthenRoutingState();
  // const { resetContextAuthenRoutingState } = useContextAuthenRoutingState();

  const onReturn = useCallback(() => {
    resetContextState();
    resetContextAuthenLoginState();
    // resetContextAuthenRoutingState();
    history.replace(fromRoute || authenRouting?.steps?.stepMenu || ROUTES.LOGIN);
  }, [
    history,
    fromRoute,
    resetContextState,
    resetContextAuthenLoginState
    // resetContextAuthenRoutingState,
  ]);

  return (
    <div className="auth authen-verify">
      <h2 className="auth__title">Đăng ký tài khoản hoặc đăng nhập</h2>
      <Switch>
        <Route exact path={ROUTES.AUTH_VERIFY_EMAIL}>
          <VerifyEmail onReturn={onReturn} />
        </Route>
        <Route exact path={ROUTES.AUTH_VERIFY_PHONE}>
          <VerifyPhone onReturn={onReturn} />
        </Route>
        <Route exact path={ROUTES.AUTH_VERIFY}>
          <Redirect to={ROUTES.AUTH_VERIFY_PHONE} />
        </Route>
      </Switch>
    </div>
  );
});

export default Verify;
