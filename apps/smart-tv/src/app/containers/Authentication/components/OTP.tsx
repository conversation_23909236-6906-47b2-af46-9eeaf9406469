import React, { useCallback, useEffect, useRef, useState, useMemo } from "react";
import { useHistory, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import classNames from "classnames";
import { EnumKeyMap } from "app/components/Common/KeyBoard/constant";
import { EnumKeyboardType } from "app/components/Common/KeyBoard/types";
import useStateRef from "hooks/useStateRef";
import PinInput from "app/components/PinInput";
import Keyboard from "app/components/Common/KeyBoard";
import Button from "app/containers/Error/components/Button";
import FocusableWrapper from "app/components/FocusableWrapper";
import { FocusContext, useFocusable } from "core/KeyHandle";
import "assets/scss/components/pin-input/_pin-input.scss";
import { authenticationOTPConfirm, authenticationOTPValidate } from "app/endpoint";
import { MESSAGES } from "types/common/Message";
import { AuthenticationLoginModel, AuthenticationValidateModel } from "app/models";
import { API_STATUS_CODE } from "app/utils/constants";
import "app/containers/MultiProfile/Lobby/animation.scss";
import SegmentManager from "app/utils/SegmentManager";
import {
  GlobalFeatureCurrentPage,
  GlobalFeatureEventNames,
  GlobalFeatureFlowAuthen,
  GlobalFeaturePropertyKeys,
  GlobalPropertyValues
} from "app/components/Tracking/GlobalFeatureTracking";
import SMSIcon from "assets/images/icon/sms.png";
import ZaloIcon from "assets/images/icon/zalo.png";
import { OTPType } from "types/page";
import { AuthenticationMethod } from "types/common";
import { useContextTrackLocationState } from "context/ContextTrackLocation";
import trackingDeleteAccount, { TrackingName } from "app/components/Tracking/DeleteAccount";
import useIsGlobal from "hooks/useIsGlobal";
import { RootState } from "app/store/store";
import { FOCUS_KEY } from "../ResetPassword/ResetPassword";
import { useContextAuthenVerifyState } from "../Context/ContextAuthenVerify";
import { LIMIT_OTP_PER_DAY, createReachLimitOtpPerDayMsg, handleSuccessfulAuthentication } from "../utils";
import { CodeAPI, CountryCode, EnumFlow, EnumPhoneLength, FOCUS_KEY_COMMON, Provider } from "../type";
import { useContextAuthenLoginAction, useContextAuthenLoginState } from "../Context/ContextAuthenLogin";
import "./style.scss";
import { useContextAuthenRoutingState } from "../Context/ContextAuthenRouting";

const DEFAULT_MESSAGE = "Mã xác nhận có thể đến chậm bạn vui lòng đợi chút nha!";

function OTP({
  onSuccess,
  onReturn,
  token,
  sendOtp,
  flow,
  historyState,
  currentPage
}: {
  onSuccess: VoidFunction;
  onReturn: VoidFunction;
  token?: string;
  sendOtp: (type?: string) => Promise<AuthenticationValidateModel>;
  flow?: EnumFlow;
  historyState?: any;
  currentPage?: string;
}) {
  const history = useHistory();
  const location = useLocation<{ [key: string]: any; account: any }>();
  const dispatch = useDispatch();
  const trackLocation = useContextTrackLocationState();
  const { authenRouting } = useContextAuthenRoutingState();
  const { state, searchParams } = authenRouting;
  const { email, phoneNumberDisplay, phoneNumber } = useContextAuthenVerifyState();
  const { confirmationNo, retryAfterTime, expiresIn, type } = useContextAuthenLoginState();
  const { setAuthenData } = useContextAuthenLoginAction();
  const [otp, setOtp, otpRef] = useStateRef("");
  const [otpCountDown, setOtpCountDown, otpCountDownRef] = useStateRef(expiresIn);
  const isFetchingRef = useRef<boolean>(false);
  const [isDisabledResendBtn, setIsDisabledResendBtn] = useState<boolean>(true);
  const [isContentBiggerContainer, setIsContentBiggerThanContainer] = useState<boolean>(false);

  const pinInputRef = useRef<any>();
  const titleContainerNodeRef = useRef<HTMLDivElement>(null);
  const titleNodeRef = useRef<HTMLDivElement>(null);
  const [firstType, setFirstType] = useState<string | undefined>();
  const [errorMsg, setErrorMsg] = useState<string>("");
  const isGlobal = useIsGlobal();
  const userFlow = useSelector((state: RootState) => state.app.userFlow);

  // Used to track whether or not user reach OTP per day
  const [infoMsg, setInfoMsg] = useState<string>(() => {
    return createReachLimitOtpPerDayMsg(retryAfterTime);
  });

  const { setFocus } = useFocusable({
    focusKey: FOCUS_KEY.FocusKeyOTP
  });

  const handleSendOtp = useCallback(
    async (otherType?: string) => {
      if (isFetchingRef.current) {
        return;
      }

      // Tracking
      SegmentManager.segmentAction(GlobalFeatureEventNames.ResendOtpSelected, {
        ...(currentPage ? { [GlobalFeaturePropertyKeys.currentPage]: currentPage } : {}),
        [GlobalFeaturePropertyKeys.flowName]: GlobalPropertyValues.ForgotPassword,
        [GlobalFeaturePropertyKeys.flowAuthen]: email ? GlobalFeatureFlowAuthen.Email : GlobalFeatureFlowAuthen.Mobile,
        [GlobalFeaturePropertyKeys.Method]: otherType || type
      });
      setErrorMsg("");
      isFetchingRef.current = true;
      const response = flow === EnumFlow.RecoveryAccount ? await sendOtp() : await sendOtp(otherType || type);
      if (response.code === CodeAPI.Success) {
        setAuthenData((prev) => {
          return {
            ...prev,
            confirmationNo: response.result.confirmationNo,
            type: response.result.type
          };
        });
        setOtpCountDown(response.result.expiresIn);
        setFocus(`${FOCUS_KEY_COMMON.KEY_FOCUS_ITEM_PREFIX}_0`);
        setInfoMsg(DEFAULT_MESSAGE);
      } else if (response.code === CodeAPI.OTPLimitReached) {
        setInfoMsg(LIMIT_OTP_PER_DAY);
        setErrorMsg("");
        setIsDisabledResendBtn(true);
        setFocus(`${FOCUS_KEY_COMMON.KEY_FOCUS_ITEM_PREFIX}_0`);
      } else if (
        response.code === CodeAPI.RequestInvalid ||
        (response.code === CodeAPI.RequestLimitReached && response.statusCode === API_STATUS_CODE.LIMIT_REQUEST)
      ) {
        setErrorMsg(response.message);
        setInfoMsg("");
      } else {
        setErrorMsg(MESSAGES.SomethingWrong);
        setInfoMsg("");
      }
      isFetchingRef.current = false;
      setOtp("");
      pinInputRef.current.clear();
    },
    [email, flow, type, sendOtp, setAuthenData, setFocus, setOtp, setOtpCountDown]
  );

  useEffect(() => {
    if (pinInputRef.current) {
      pinInputRef.current.clear();
    }
    if (currentPage === GlobalFeatureCurrentPage.ResetPassword) {
      SegmentManager.segmentAction(GlobalFeatureEventNames.OtpScreenLoaded, {
        [GlobalFeaturePropertyKeys.currentPage]: currentPage,
        [GlobalFeaturePropertyKeys.flowName]: GlobalPropertyValues.ForgotPassword,
        [GlobalFeaturePropertyKeys.flowAuthen]: email ? GlobalFeatureFlowAuthen.Email : GlobalFeatureFlowAuthen.Mobile
      });
    }
    if (flow === EnumFlow.RecoveryAccount) {
      handleSendOtp();
    }
    if (infoMsg === "") {
      setInfoMsg(DEFAULT_MESSAGE);
    }
  }, []);

  useEffect(() => {
    setOtpCountDown(expiresIn);
  }, [expiresIn]);

  useEffect(() => {
    if (otpCountDown === 0 && [LIMIT_OTP_PER_DAY].indexOf(infoMsg) === -1) {
      if (infoMsg !== LIMIT_OTP_PER_DAY) {
        setIsDisabledResendBtn(false);
        setInfoMsg("");
      }
    } else {
      setIsDisabledResendBtn(true);
    }
  }, [otpCountDown, infoMsg]);

  const confirmOTP = useCallback(async () => {
    if (isFetchingRef.current) {
      return;
    }
    const pinInput = pinInputRef.current;
    isFetchingRef.current = true;
    const response = await authenticationOTPValidate(confirmationNo, otpRef.current, token);
    if (response.code === CodeAPI.Success) {
      setAuthenData((prev) => {
        return {
          ...prev,
          codeOTP: otpRef.current
        };
      });
      if (flow === EnumFlow.RecoveryAccount) {
        const response = (await authenticationOTPConfirm(
          confirmationNo,
          otpRef.current,
          undefined,
          undefined,
          token
        )) as AuthenticationLoginModel;
        if (response.code === CodeAPI.Success) {
          const { result, account } = response || {};
          await handleSuccessfulAuthentication({
            dispatch,
            history,
            trackLocation,
            state,
            searchParams,
            authenticationMethod: AuthenticationMethod.RecoveryAccount,
            result: {
              ...result,
              profile: account
            },
            flowAuthen: GlobalFeatureFlowAuthen.Mobile,
            isLocal: !isGlobal,
            userFlow,
            hotelAccount: account?.hotelAccount
          });
          SegmentManager.segmentAction(GlobalFeatureEventNames.OtpInputted, {
            [GlobalFeaturePropertyKeys.currentPage]: GlobalFeatureCurrentPage.ResetPassword,
            [GlobalFeaturePropertyKeys.Result]: GlobalPropertyValues.Success,
            ...(currentPage === GlobalFeatureCurrentPage.ResetPassword
              ? {
                  [GlobalFeaturePropertyKeys.flowName]: GlobalPropertyValues.ForgotPassword
                }
              : {}),
            ...(phoneNumber
              ? {
                  [GlobalFeaturePropertyKeys.flowAuthen]: GlobalFeatureFlowAuthen.Mobile
                }
              : {})
          });
          onSuccess();
        } else if (
          response.code === CodeAPI.RequestInvalid ||
          (response.code === CodeAPI.RequestLimitReached && response.statusCode === API_STATUS_CODE.LIMIT_REQUEST)
        ) {
          SegmentManager.segmentAction(GlobalFeatureEventNames.OtpInputted, {
            [GlobalFeaturePropertyKeys.currentPage]: GlobalFeatureCurrentPage.ResetPassword,
            [GlobalFeaturePropertyKeys.Result]: GlobalPropertyValues.Failed,
            [GlobalFeaturePropertyKeys.TextError]: response.message,
            ...(currentPage === GlobalFeatureCurrentPage.ResetPassword
              ? {
                  [GlobalFeaturePropertyKeys.flowName]: GlobalPropertyValues.ForgotPassword
                }
              : {}),
            ...(phoneNumber
              ? {
                  [GlobalFeaturePropertyKeys.flowAuthen]: GlobalFeatureFlowAuthen.Mobile
                }
              : {})
          });
          setErrorMsg(response.message);
        } else {
          SegmentManager.segmentAction(GlobalFeatureEventNames.OtpInputted, {
            [GlobalFeaturePropertyKeys.currentPage]: GlobalFeatureCurrentPage.ResetPassword,
            [GlobalFeaturePropertyKeys.Result]: GlobalPropertyValues.Failed,
            [GlobalFeaturePropertyKeys.TextError]: MESSAGES.SomethingWrong,
            ...(currentPage === GlobalFeatureCurrentPage.ResetPassword
              ? {
                  [GlobalFeaturePropertyKeys.flowName]: GlobalPropertyValues.ForgotPassword
                }
              : {}),
            ...(phoneNumber
              ? {
                  [GlobalFeaturePropertyKeys.flowAuthen]: GlobalFeatureFlowAuthen.Mobile
                }
              : {})
          });
          setErrorMsg(MESSAGES.SomethingWrong);
        }
      } else {
        SegmentManager.segmentAction(GlobalFeatureEventNames.OtpInputted, {
          [GlobalFeaturePropertyKeys.currentPage]: GlobalFeatureCurrentPage.ResetPassword,
          [GlobalFeaturePropertyKeys.Result]: GlobalPropertyValues.Success,
          ...(currentPage === GlobalFeatureCurrentPage.ResetPassword
            ? {
                [GlobalFeaturePropertyKeys.flowName]: GlobalPropertyValues.ForgotPassword
              }
            : {}),
          ...(phoneNumber
            ? {
                [GlobalFeaturePropertyKeys.flowAuthen]: GlobalFeatureFlowAuthen.Mobile
              }
            : {})
        });
        onSuccess();
      }
    } else if (
      response.code === CodeAPI.RequestInvalid ||
      (response.code === CodeAPI.RequestLimitReached && response.statusCode === API_STATUS_CODE.LIMIT_REQUEST)
    ) {
      SegmentManager.segmentAction(GlobalFeatureEventNames.OtpInputted, {
        [GlobalFeaturePropertyKeys.currentPage]: GlobalFeatureCurrentPage.ResetPassword,
        [GlobalFeaturePropertyKeys.Result]: GlobalPropertyValues.Failed,
        [GlobalFeaturePropertyKeys.TextError]: response.message,
        ...(currentPage === GlobalFeatureCurrentPage.ResetPassword
          ? {
              [GlobalFeaturePropertyKeys.flowName]: GlobalPropertyValues.ForgotPassword
            }
          : {}),
        ...(phoneNumber
          ? {
              [GlobalFeaturePropertyKeys.flowAuthen]: GlobalFeatureFlowAuthen.Mobile
            }
          : {})
      });
      setErrorMsg(response.message);
      if (flow === EnumFlow.RecoveryAccount) {
        trackingDeleteAccount({
          trackingName: TrackingName.ConfirmationButtonSelected,
          trackingContent: {
            current_page: "Đăng ký OTP",
            cause_for_failure: response.message,
            flow_name: historyState.flow
          }
        });
      }
    } else {
      SegmentManager.segmentAction(GlobalFeatureEventNames.OtpInputted, {
        [GlobalFeaturePropertyKeys.currentPage]: GlobalFeatureCurrentPage.ResetPassword,
        [GlobalFeaturePropertyKeys.Result]: GlobalPropertyValues.Failed,
        [GlobalFeaturePropertyKeys.TextError]: MESSAGES.SomethingWrong,
        ...(currentPage === GlobalFeatureCurrentPage.ResetPassword
          ? {
              [GlobalFeaturePropertyKeys.flowName]: GlobalPropertyValues.ForgotPassword
            }
          : {}),
        ...(phoneNumber
          ? {
              [GlobalFeaturePropertyKeys.flowAuthen]: GlobalFeatureFlowAuthen.Mobile
            }
          : {})
      });
      setErrorMsg(MESSAGES.SomethingWrong);
    }
    setOtp("");
    pinInput?.clear();
    isFetchingRef.current = false;
  }, [confirmationNo, onSuccess, otpRef, phoneNumber, setAuthenData, setOtp]);

  useEffect(() => {
    if (otp.length === 4) {
      confirmOTP();
    }
  }, [otp, confirmOTP]);

  useEffect(() => {
    if (!firstType) {
      setFirstType(type);
    }
  }, [type]);

  const onKeyboardEnter = (currentKey: string) => {
    const currentKeyLower = currentKey.toLowerCase();
    const pinInput = pinInputRef.current;
    if (infoMsg !== LIMIT_OTP_PER_DAY && ["clear", "backspace"].indexOf(currentKeyLower) === -1) {
      setInfoMsg("");
    }

    switch (currentKeyLower) {
      case "clear":
        if (isFetchingRef.current) {
          return;
        }
        pinInput.clear();
        setOtp("");
        break;
      case "backspace": {
        if (isFetchingRef.current) {
          return;
        }
        if (pinInput.elements[3].state.value) {
          pinInput.elements[3].state.value = "";
          setOtp(otpRef.current.slice(0, -1));
          break;
        }
        if (pinInput.elements[2].state.value) {
          pinInput.elements[2].state.value = "";
          setOtp(otpRef.current.slice(0, -1));
          break;
        }
        if (pinInput.elements[1].state.value) {
          pinInput.elements[1].state.value = "";
          setOtp(otpRef.current.slice(0, -1));
          break;
        }
        if (pinInput.elements[0].state.value) {
          pinInput.elements[0].state.value = "";
          setOtp(otpRef.current.slice(0, -1));
          setErrorMsg("");
        }
        break;
      }
      default: {
        if (otpRef.current.length === 4) return;
        setErrorMsg("");
        if (pinInput.elements[2].state.value) {
          pinInput.elements[3].state.value = currentKeyLower;
          setOtp(otpRef.current + currentKeyLower);
          return;
        }
        if (pinInput.elements[1].state.value) {
          pinInput.elements[2].state.value = currentKeyLower;
          setOtp(otpRef.current + currentKeyLower);
          return;
        }
        if (pinInput.elements[0].state.value) {
          pinInput.elements[1].state.value = currentKeyLower;
          setOtp(otpRef.current + currentKeyLower);
          return;
        }
        pinInput.elements[0].state.value = currentKeyLower;
        setOtp(otpRef.current + currentKeyLower);
        break;
      }
    }
  };

  useEffect(() => {
    const otpCountDownTimer = setInterval(() => {
      if (otpCountDown > 0) {
        setOtpCountDown((prev) => prev - 1);
      } else clearInterval(otpCountDownTimer);
    }, 1000);

    return () => {
      clearInterval(otpCountDownTimer);
    };
  }, [otpCountDown, otpCountDownRef, setOtpCountDown]);

  useEffect(() => {
    if (titleNodeRef.current && titleContainerNodeRef.current && email) {
      const titleContainerNodeWidth = titleContainerNodeRef.current.clientWidth;
      const titleNodeWidth = titleNodeRef.current.clientWidth;
      if (titleContainerNodeWidth < titleNodeWidth) {
        setTimeout(() => {
          setIsContentBiggerThanContainer(true);
        }, 500);
      }
    }
  }, [email]);

  const title = useMemo(() => {
    let method: Provider | "" = "";
    let methodInfo = "";
    let methodPhoneCode = "";
    const accountProfile = location?.state?.account;
    const provider = accountProfile?.provider;
    if (provider === Provider.Email) {
      method = Provider.Email;
      methodInfo = accountProfile?.email;
    } else if (typeof provider === "number") {
      method = Provider.Mobile;
      methodInfo = accountProfile?.mobile;
      methodPhoneCode = `+${accountProfile?.callingNo}`;
      if (accountProfile?.country === CountryCode.VN && accountProfile?.mobile?.length === EnumPhoneLength.Max) {
        methodInfo = accountProfile?.mobile?.slice(1);
      }
    } else {
      method = email ? Provider.Email : Provider.Mobile;
      methodInfo = email || phoneNumberDisplay;
    }

    // let title = (
    //   <span>{`Nhập OTP được gửi tới số điện thoại${
    //     methodPhoneCode ? ` ${methodPhoneCode}` : ""
    //   } ${methodInfo}`}</span>
    // );
    let title = (
      <span>
        Vui lòng nhập mã OTP được gửi về SMS <br />
        {`qua số điện thoại${methodPhoneCode ? ` ${methodPhoneCode}` : ""} ${methodInfo}`}
      </span>
    );
    if (type === OTPType.ZALO) {
      title = (
        <span>
          Vui lòng nhập mã OTP được gửi về Zalo <br />
          {`qua số điện thoại${methodPhoneCode ? ` ${methodPhoneCode}` : ""} ${methodInfo}`}
        </span>
      );
    }
    if (method === Provider.Email) {
      title = (
        <span className="auth-otp-title-container">Vui lòng nhập mã OTP đã được gửi về địa chỉ email {email}</span>
      );
    }
    return title;
  }, [email, isContentBiggerContainer, location, phoneNumberDisplay, type]);

  const countdown = new Date(otpCountDown * 1000).toISOString().slice(14, 19);

  return (
    <div className="auth--mt-79 block block--login auth-otp-wp">
      <div className="grid">
        <div className="col col-6 panel-left triangle-right-1">
          <div className="keyboard-container">
            <h3 className="block__title block--sign-up">{title}</h3>
            <Keyboard
              focusKey={EnumKeyMap.KEY_5}
              loop="all"
              type={EnumKeyboardType.NUMBER}
              onEnter={onKeyboardEnter}
              onReturn={onReturn}
            />
          </div>
          <FocusableWrapper
            enterOnClick
            focusImplicitOnHover
            saveLastFocusedChild={false}
            preferredChildFocusKey={FOCUS_KEY.FocusKeyOTPConfirm}
            focusKey={FOCUS_KEY.FocusKeyOTPBtns}
          >
            {({ ref }) => {
              return (
                <FocusContext.Provider value={FOCUS_KEY.FocusKeyOTPBtns}>
                  <div ref={ref}>
                    <div className="send-buttons">
                      <Button
                        onEnter={() => handleSendOtp()}
                        onReturn={onReturn}
                        className={classNames("button", {
                          active: otpCountDown !== 0
                        })}
                        disabled={isDisabledResendBtn}
                        focusKey={FOCUS_KEY.FocusKeyOTPConfirm}
                        type="RESENT_OTP"
                      >
                        Gửi lại OTP
                      </Button>
                      {firstType === OTPType.ZALO && (
                        <Button
                          onEnter={() => handleSendOtp(type === OTPType.ZALO ? OTPType.SMS : OTPType.ZALO)}
                          onReturn={onReturn}
                          className="button"
                          disabled={isDisabledResendBtn}
                          focusKey={FOCUS_KEY.FocusKeyOtherOTPConfirm}
                          type="RESENT_OTP"
                        >
                          <div className="other-send">
                            <div>Gửi lại OTP qua</div>
                            <img className="send-icon" src={type === OTPType.ZALO ? SMSIcon : ZaloIcon} alt="" />
                          </div>
                        </Button>
                      )}
                    </div>
                    <div className="back-button">
                      <Button
                        onEnter={onReturn}
                        onReturn={onReturn}
                        className="button"
                        focusKey={FOCUS_KEY.FocusKeyOTPBack}
                        type="BACK"
                      >
                        Quay lại
                      </Button>
                    </div>
                  </div>
                </FocusContext.Provider>
              );
            }}
          </FocusableWrapper>
        </div>
        <div className="col col-6 panel-right">
          <div className="input-wp">
            <div className="panel-right__container">
              <form className="form form--dark">
                <div className="muted">
                  <PinInput ref={pinInputRef} length={4} type="numeric" inputMode="number" placeholder="" />
                </div>
              </form>
            </div>
            {errorMsg ? <p className="error">{errorMsg}</p> : null}
            {infoMsg === DEFAULT_MESSAGE && !errorMsg ? <p className="info-msg">{infoMsg}</p> : null}
          </div>
          {infoMsg === LIMIT_OTP_PER_DAY ? (
            <p className="info-msg" dangerouslySetInnerHTML={{ __html: infoMsg }} />
          ) : null}
          {otpCountDown !== 0 && infoMsg !== LIMIT_OTP_PER_DAY ? (
            <p className="info-msg">Nếu không nhận được OTP, chọn gửi lại sau ( {countdown} )</p>
          ) : null}
        </div>
      </div>
    </div>
  );
}
export default OTP;
