/// @group setting
@use "../../../../assets/scss/settings/function" as fn;

.auth-otp-wp {
  .auth-otp-title-container {
    display: flex;
    word-break: break-word;
  }
  .auth-otp-title {
    display: inline-block;
    width: fn.percent-unit(392);
    height: fn.percent-unit(48);
    overflow: hidden;
    position: relative;
    margin-left: fn.percent-unit(4);
  }
  .panel-right {
    font-weight: 400;
    p {
       margin: 0 !important;
       font-weight: 400 !important;
    }
    .input-wp {
      min-height: fn.percent-unit(132);
    }
    .info-msg {
      color: #9B9B9B !important;
    }
    .form {
      margin-bottom: 0;
    }
    .error {
      text-align: left !important;
    }
    .muted {
      margin-bottom: fn.percent-unit(24) !important;
    }
  }
  .send-buttons {
    display: flex;
    margin: fn.percent-unit(28) fn.percent-unit(-6) fn.percent-unit(32);
    .other-send {
      display: flex;
      align-items: center;
      .send-icon {
        width: fn.percent-unit(36);
        height: fn.percent-unit(36);
        object-fit: contain;
        margin-left: fn.percent-unit(32);
      }
    }
    .btn {
      flex: 1;
      min-width: 0;
      margin: 0 fn.percent-unit(6);
      display: flex;
      align-items: center;
      &.btn--ghost {
        &.focus {
          background: #ffffff !important;
          color: #222222;
        }
        &.disabled {
          &.active {
            background: #434343 !important;
          }
         
        }
      }
    }
  }
  .back-button {
    .btn {
      &.btn--ghost {
        &.focus {
          background: #ffffff;
          color: #222222;
        }
      }
    }
  }
}
