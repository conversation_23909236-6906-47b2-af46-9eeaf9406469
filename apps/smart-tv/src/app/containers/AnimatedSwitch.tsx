import React, { useEffect, useMemo, useRef } from "react";
import { Redirect, Route, Switch, withRouter } from "react-router-dom";
import { useSelector } from "react-redux";
import WelcomeDialog from "app/components/WelcomeDialog";
import { KEEP_ALIVE_KEY_LOBBY, ROUTES } from "app/utils/constants";
import WelcomeDialogContext from "context/WelcomeDialogContext";
import useMakeCurrentPage from "hooks/useMakeCurrentPage";
import ProtectedRouteWithLobby from "app/components/ProtectedRouteWithLobby";
import useWelcomeDialog from "hooks/useWelcomeDialog";
import LayoutSideMenu from "app/layouts/LayoutSideMenu";
import LayoutSingle from "app/layouts/LayoutSingle";
import { RootState } from "app/store/store";
import KeepAlive, { KeepAliveData } from "app/utils/KeepAlive";
import { ContextTrackLocationProvider } from "context/ContextTrackLocation";
import { useAppOnboarding } from "hooks/useAppOnboarding";
import { StateAppContextProvider } from "context/StateAppContext";
import Personalization from "./Personalization";
// import SignUpPage from "./Authentication/SignUp/SignUpPage";
// import SignInPage from "./Authentication/SignIn/SignInPage";
// import ResetPasswordPage from "./User/Login/ResetPassword/ResetPasswordPage";
import VideoIntro from "./VideoIntro/VideoIntro";
import Voucher from "./PaymentNewUI/Voucher";
import PaymentNewUI from "./PaymentNewUI";
import PaymentTVOD from "./PaymentNewUI/PaymentTVOD";
import PaymentPVOD from "./PaymentNewUI/PaymentPVOD";
import ViewStream from "./LiveStream/ViewStream";
import Promotion from "./Promotion/Promotion";
import AutoPromotion from "./Promotion/AutoPromotion";
import Lobby from "./MultiProfile/Lobby/Lobby";
import Authentication from "./Authentication/Authentication";
import PaymentGlobal from "./PaymentNewUI/PaymentGlobal/PaymentGlobal";
import ContextAuthenRoutingProvider from "./Authentication/Context/ContextAuthenRouting";
import { ContextPaymentGlobalProvider } from "./PaymentNewUI/PaymentGlobal/ContextPaymentGlobal";
import LiveTVPlayerV2 from "./LiveTVV2/Player";
import MultiProfile from "./MultiProfile";

const AnimatedSwitch = withRouter(function AnimateSwitch({ location, history, match }) {
  const {
    isDialogShowing,
    setIsDialogShowing,
    dialog,
    addDialog,
    removeDialog,
    handleCountTVOD,
    handleSaveListReminder,
    handleShowAds,
    handleShowDialogs,
    clearAllDialogs,
    allowRenderDialogs
  } = useWelcomeDialog();
  useAppOnboarding({
    allowRenderDialogs,
    handleShowDialogs
  });
  const isAuthen = useSelector((state: RootState) => state.app.isAuthen);
  // const currentProfile = useSelector(
  //   (state: RootState) => state.app.currentProfile
  // );

  // useEffect(() => {
  //   // clear all and prevent rendering dialogs when lobby is rendered
  //   // when profile is selected, allow rendering dialogs => Check logic in LobbyItem component
  //   if (isAuthen && !currentProfile) {
  //     clearAllDialogs();
  //   } else {
  //     // reset the flow when user logout
  //     allowRenderDialogs();
  //   }
  // }, [allowRenderDialogs, clearAllDialogs, currentProfile, isAuthen]);

  const welcomeDialogState = {
    isDialogShowing,
    setIsDialogShowing,
    dialog,
    addDialog,
    removeDialog,
    handleCountTVOD,
    handleSaveListReminder,
    handleShowAds,
    handleShowDialogs,
    clearAllDialogs,
    allowRenderDialogs
  };

  const keepAliveDataLobbyRef = useRef({
    path: window.location.hash,
    focus: {},
    extra: {}
  } as KeepAliveData);

  const renderMultiProfilePage = useMemo(() => {
    return <MultiProfile />;
  }, []);

  useEffect(() => {
    if (!isAuthen) {
      KeepAlive.clearData(KEEP_ALIVE_KEY_LOBBY);
      keepAliveDataLobbyRef.current = {
        path: window.location.hash,
        focus: {},
        extra: {}
      } as KeepAliveData;
    }
  }, [isAuthen]);

  return (
    // <TransitionGroup className="page-wrapper">
    //   <CSSTransition
    //     key={location.pathname}
    //     className="page-transition"
    //     classNames="fade"
    //     timeout={500}
    //   >
    //     <div>
    // eslint-disable-next-line react/jsx-no-bind

    <StateAppContextProvider>
      <WelcomeDialogContext.Provider value={welcomeDialogState}>
        <ContextTrackLocationProvider>
          <WelcomeDialog />
          <Switch location={location}>
            <Route path={ROUTES.MULTI_PROFILE_LOBBY}>
              {isAuthen ? <Lobby keepAliveDataRef={keepAliveDataLobbyRef} /> : <Redirect to={ROUTES.ROOT} />}
            </Route>
            <Route path={ROUTES.MULTI_PROFILE_MAIN}>
              {isAuthen ? renderMultiProfilePage : <Redirect to={ROUTES.LOGIN} />}
            </Route>
            <Route path={ROUTES.PERSONALIZATION}>
              <Personalization />
            </Route>
            {/* <Route path="/screensaver" component={Screensaver} /> */}
            {/* <Route path={ROUTES.SIGNUP_PAGE}>
                  <SignUpPage />
                </Route> */}
            {/* <Route path={ROUTES.RESET_PASSWORD}>
                  <ResetPasswordPage />
                </Route> */}
            <ProtectedRouteWithLobby path={`${ROUTES.SUB_PAGE}/:slug/:id`}>
              <LayoutSingle />
            </ProtectedRouteWithLobby>
            <ProtectedRouteWithLobby path={`${ROUTES.RIBBON}/:id`}>
              <LayoutSingle />
            </ProtectedRouteWithLobby>
            <ProtectedRouteWithLobby path={`${ROUTES.RAP_VIET_ARTIST}/:id`}>
              <LayoutSingle />
            </ProtectedRouteWithLobby>
            <ProtectedRouteWithLobby path={`${ROUTES.VIDEO_INTRO}/:id`}>
              <VideoIntro />
            </ProtectedRouteWithLobby>
            <Route path={ROUTES.VOUCHER}>
              <Voucher />
            </Route>
            {/* <Route path="/payment/:packageId" component={LayoutSingle} /> */}
            {/* <Route path="/payment" component={LayoutSingle} /> */}
            <Route path={`${ROUTES.PAYMENT}/:packageId`}>
              <PaymentNewUI />
            </Route>
            <Route path={ROUTES.PAYMENT}>
              <PaymentNewUI />
            </Route>
            <Route path={`${ROUTES.PAYMENT_TVOD}/:slug/:id`}>
              <PaymentTVOD />
            </Route>
            <Route path={`${ROUTES.PAYMENT_PVOD}/:slug/:id`}>
              <PaymentPVOD />
            </Route>
            <Route path={`${ROUTES.PAYMENT_GLOBAL}`}>
              <ContextPaymentGlobalProvider>
                <PaymentGlobal />{" "}
              </ContextPaymentGlobalProvider>
            </Route>
            <ProtectedRouteWithLobby path={ROUTES.SETTING} isBackToThisPageOnSuccess={false}>
              <LayoutSingle />
            </ProtectedRouteWithLobby>
            {/* <Route path={ROUTES.LOGIN_STEP}>
                <LayoutSingle />
              </Route>
              <Route exact path={ROUTES.LOGIN}>
                <LayoutSideMenu />
              </Route> */}
            <Route path={ROUTES.AUTH}>
              <ContextAuthenRoutingProvider>
                <Authentication />
              </ContextAuthenRoutingProvider>
            </Route>
            <Route path={ROUTES.ERROR}>
              <LayoutSideMenu />
            </Route>
            <Route path={ROUTES.ERROR_SINGLE}>
              <LayoutSingle />
            </Route>
            {/* <Route path={ROUTES.LOGIN_PAGE}>
                  <SignInPage />
                </Route> */}
            <ProtectedRouteWithLobby path={ROUTES.USER_PROFILE}>
              <LayoutSideMenu />
            </ProtectedRouteWithLobby>
            <ProtectedRouteWithLobby path={ROUTES.SEARCH}>
              <LayoutSideMenu />
            </ProtectedRouteWithLobby>
            <ProtectedRouteWithLobby path={ROUTES.NOTIFICATION}>
              <LayoutSideMenu />
            </ProtectedRouteWithLobby>
            <ProtectedRouteWithLobby path={ROUTES.SCHEDULE}>
              <LayoutSideMenu />
            </ProtectedRouteWithLobby>
            <ProtectedRouteWithLobby path={`${ROUTES.VOD}/:id`}>
              <LayoutSideMenu />
            </ProtectedRouteWithLobby>
            <ProtectedRouteWithLobby path={`${ROUTES.LIVE_TV}/:id`}>
              <LiveTVPlayerV2 />
            </ProtectedRouteWithLobby>
            <ProtectedRouteWithLobby path={`${ROUTES.SPORT_FOOTBALL}/:id`}>
              <LayoutSideMenu />
            </ProtectedRouteWithLobby>
            <ProtectedRouteWithLobby path={`${ROUTES.VIEW_STREAM}/:id`}>
              <ViewStream />
            </ProtectedRouteWithLobby>
            <ProtectedRouteWithLobby path={ROUTES.LIVE_TV_NEW_UI}>
              <LayoutSideMenu />
            </ProtectedRouteWithLobby>
            <ProtectedRouteWithLobby path={`${ROUTES.LIVE_STREAM}/:id`}>
              <LayoutSideMenu />
            </ProtectedRouteWithLobby>
            <Route
              path={ROUTES.PROMOTION}
              render={({ location, history, match }) => (
                <Promotion location={location} history={history} match={match} />
              )}
            />
            <Route
              path={ROUTES.AUTO_PROMOTION}
              render={({ location, history, match }) => (
                <AutoPromotion location={location} history={history} match={match} />
              )}
            />
            <ProtectedRouteWithLobby path="/">
              <LayoutSideMenu />
            </ProtectedRouteWithLobby>
          </Switch>
        </ContextTrackLocationProvider>
      </WelcomeDialogContext.Provider>
    </StateAppContextProvider>
    //      </div>
    //    </CSSTransition>
    // </TransitionGroup>
  );
});

export default AnimatedSwitch;
