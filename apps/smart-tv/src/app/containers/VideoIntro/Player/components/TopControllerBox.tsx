import React, { PureComponent } from "react";
import { CSSTransition } from "react-transition-group";
import classNames from "classnames";
import platform from "services/platform";

type KeyHandlerFn = (e: KeyboardEvent) => void;
interface Props {
  focus: boolean;
  show: boolean;

  onPopout: (direction: string) => void;
  registerKey: [(keyHandler: KeyHandlerFn) => void, () => void];
  onEnter: (action: string) => void;
  defaultIndex?: number;
}

interface State {
  focusIndex: number;
}

export default class TopControllerBox extends PureComponent<Props, State> {
  state = {
    focusIndex: 0,
  };

  buttonCount: number = 0;

  buttons: HTMLButtonElement[] = [];

  private createButtonsRefFn: (
    index: number,
    e: HTMLButtonElement | null
  ) => void = (index, element) => {
    if (element) {
      this.buttons[index] = element;
    }
  };

  keyHandler = (e: KeyboardEvent) => {
    const { keys } = platform;
    const { focusIndex } = this.state;
    const { onPopout, onEnter } = this.props;
    switch (e.keyCode) {
      case keys.right:
        if (focusIndex === this.buttonCount - 1) {
          onPopout("right");
          return;
        }
        this.setState({
          focusIndex: focusIndex + 1,
        });
        break;
      case keys.left:
        if (focusIndex === 0) {
          onPopout("left");
          return;
        }
        this.setState({
          focusIndex: focusIndex - 1,
        });
        break;
      case keys.up:
        onPopout("up");
        break;
      case keys.down:
        onPopout("down");
        break;
      case keys.enter:
        const action =
          this.buttons[focusIndex].getAttribute("data-action") || "";
        onEnter(action);
        break;
      default:
        break;
    }
  };

  onClick = (e: MouseEvent) => {
    const currentEl = e.currentTarget as HTMLElement;
    if (currentEl) {
      const action = currentEl.getAttribute("data-action");
      if (action) {
        const { onEnter } = this.props;
        onEnter(action);
      }
    }
  };

  componentDidUpdate(prevProps: Props) {
    if (!prevProps.focus && this.props.focus) {
      this.setState({
        focusIndex: this.props.defaultIndex || 0,
      });
    }
  }

  componentDidMount() {
    this.props.registerKey[0](this.keyHandler);
  }

  componentWillUnmount() {
    this.props.registerKey[1]();
  }

  render() {
    const { children, focus: isFocus, show } = this.props;
    const { focusIndex } = this.state;
    let buttonCount = 0;
    const renderChild = React.Children.map(children, (child, index) => {
      if (!React.isValidElement<any>(child)) {
        return child;
      }
      if (child.type === "div") {
        buttonCount++;
        return React.cloneElement(child, {
          ...child.props,
          className: classNames(
            {
              focused: isFocus && focusIndex === buttonCount - 1,
            },
            child.props.className
          ),
          ref: this.createButtonsRefFn.bind(null, buttonCount - 1),
          onClick: this.onClick,
        });
      }
    });
    this.buttonCount = buttonCount;
    return (
      <CSSTransition in={show} timeout={500} classNames="fade-in-down">
        <div className="top-controller fade-in-down">
          <div className="control-box">{renderChild}</div>
        </div>
      </CSSTransition>
    );
  }
}
