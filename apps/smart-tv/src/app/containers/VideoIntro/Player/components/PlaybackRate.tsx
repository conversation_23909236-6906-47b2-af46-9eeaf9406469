import React, { Component, MouseEvent } from "react";
import { CSSTransition } from "react-transition-group";
import classNames from "classnames";

import platform from "services/platform";

type KeyHandlerFn = (e: KeyboardEvent) => void;

interface Props {
  show: boolean;
  activePlaybackRate: number;

  registerKey: [(keyHandler: KeyHandlerFn) => void, () => void];
  onChangePlaybackRate: (playbackRate: number) => void;
}
interface State {
  focusIndex: number;
}

export default class PlaybackRate extends Component<Props, State> {
  state = {
    focusIndex: 3,
  };

  playbackRate = [
    { title: "0.25x", value: 0.25 },
    { title: "0.5x", value: 0.5 },
    { title: "0.75x", value: 0.75 },
    { title: "1x (<PERSON>ình thường)", value: 1 },
    { title: "1.25x", value: 1.25 },
    { title: "1.5x", value: 1.5 },
    { title: "1.75x", value: 1.75 },
    { title: "2x", value: 2 },
  ];

  keyHandler = (e: KeyboardEvent) => {
    const { keys } = platform;
    const { onChangePlaybackRate } = this.props;
    const { focusIndex } = this.state;
    switch (e.keyCode) {
      case keys.enter:
        onChangePlaybackRate(this.playbackRate[focusIndex].value);
        break;
      case keys.up:
        this.setState({
          focusIndex: Math.max(0, focusIndex - 1),
        });
        break;
      case keys.down:
        this.setState({
          focusIndex: Math.min(focusIndex + 1, this.playbackRate.length - 1),
        });
        break;
      default:
        break;
    }
  };

  onProfileClick = (event: MouseEvent) => {
    const { onChangePlaybackRate } = this.props;
    const currentEl = event.currentTarget as HTMLElement;
    if (currentEl) {
      const index = currentEl.getAttribute("data-index");
      if (index) {
        const indexInt = parseInt(index) || 3;
        onChangePlaybackRate(this.playbackRate[indexInt].value);
      }
    }
  };

  componentDidUpdate(prevProps: Props) {
    if (!prevProps.show && this.props.show) {
      this.setState({
        focusIndex: 0,
      });
    }
  }

  componentDidMount() {
    this.props.registerKey[0](this.keyHandler);
  }

  componentWillUnmount() {
    this.props.registerKey[1]();
  }

  render() {
    const { show, activePlaybackRate } = this.props;
    const { focusIndex } = this.state;
    return (
      <CSSTransition in={show} timeout={500} classNames="fade-in-right">
        <div className="side-popup fade-in-right">
          <div className="side-popup-box">
            {this.playbackRate.length > 0 ? (
              <>
                <div className="side-popup--title">Tốc độ phát</div>
                {this.playbackRate.map((item, index) => (
                  <div
                    key={`playback-rate-${index}`}
                    className={classNames("side-popup--item", {
                      active: activePlaybackRate === item.value,
                      focused: index === focusIndex,
                    })}
                    data-index={index}
                    onClick={this.onProfileClick}
                  >
                    {item.title}
                  </div>
                ))}
              </>
            ) : null}
          </div>
        </div>
      </CSSTransition>
    );
  }
}
