import get from "lodash/get";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";
import { useSelector } from "react-redux";
import { useHistory, useLocation, useParams } from "react-router-dom";
import Icon from "app/components/Common/Icon";
import Message from "app/components/Common/Message";
import Loader from "app/components/Common/Spinner";
import DialogCommon from "app/components/DialogCommon";
import { DialogType, DialogUserType } from "app/components/DialogCommon/types";
import DialogV2 from "app/components/DialogV2";
import ViePlayer, {
  ViePlayerConfigImage,
  ViePlayerConfigTrailer,
  ViePlayerEventType,
  ViePlayerStatus
} from "app/components/Player/ViePlayer";
import useEventPlayer from "app/components/Player/ViePlayer/useEventPlayer";
import { RootState } from "app/store/store";
import { ROUTES, VIDEO_TYPE } from "app/utils/constants";
import { DialogImg, DialogMessage, DialogTitle } from "app/utils/DialogInfo";
import GAManager from "app/utils/GAManager";
import { makeTypeOfUser } from "app/utils/make";
import SegmentManager from "app/utils/SegmentManager";
import { trackTvod, TVOD_EVENT_NAMES } from "app/utils/track";
import dialogBgCover from "assets/images/tvod-mbg.png";
import usePrevious from "hooks/usePrevious";
import useQuery from "hooks/useQuery";
import useStateRef from "hooks/useStateRef";
import dialogConfig from "services/dialogConfig";
import {
  Audio,
  DataVideoIntro,
  DataVideoPlayer,
  StreamProfile,
  Subtitle,
  TYPE_OF_CONTENT
} from "types/endpoint";
import { TvodDetails } from "types/endpoint/Tvod";
import { TYPE_OF_ID, VideoType } from "types/page";
import { PermissionPlayerType } from "types/page/player";
import {
  showDialogContentForKidNeedBuy,
  showDialogContentForKidNeedBuyTVOD,
  showDialogContentForKidNotAccess,
  showDialogFeatureForKidNeedBuy
} from "app/utils/player";
import {
  GlobalFeatureCurrentPage,
  GlobalFeatureEventNames,
  GlobalFeatureFlowName,
  GlobalFeaturePropertyKeys
} from "app/components/Tracking/GlobalFeatureTracking";
import { AccountModel, SLOT_NAME } from "app/models";
import { EnumParamsVideoIntro } from "context/types";
import useIsGlobal from "hooks/useIsGlobal";
import SentryManager, { renderRecordID } from "app/utils/SentryManager";
import { EnumSentryErrorType, EnumSentryLevelType } from "types/common";
import platform from "services/platform";
import { getDialogKey } from "app/components/DialogCommon/transform";
import { TrackingEngagementTriggerEventNames } from "app/components/Tracking/TrackingEngagementTrigger";
import {
  TrackingFastTrackNames,
  TrackingFastTrackProps,
  trackingFastTrack
} from "app/components/Tracking/TrackingFastTrack";
import { getPVODMovieInfoService } from "app/endpoint/ServicePvod";
import useCanTrackingWithHotelAccount from "hooks/useCanTrackingWithHotelAccount";
import {
  LocalFeatureEventNames,
  LocalFeaturePropertyKeys,
  LocalPropertyValues
} from "app/components/Tracking/LocalFeatureTracking";
import RecommendSVODDialog from "app/components/DialogCommon/RecommendSVODDialog";
import { SVODTrialContent } from "app/components/DialogCommon/SVODTrialDialog";
import { SVODTrialTriggerType } from "app/endpoint/Trigger/service";
import { includesValue } from "app/utils/array";
import ButtonFocusable from "app/components/ButtonFocusable";
import useCurrentPageRef from "hooks/useCurrentPageRef";
import { VieOnNavigation } from "core/KeyHandle";
import KeepAlive from "app/utils/KeepAlive";
import { forceSeasonVideoTypeToEpisode } from "app/utils/transform";
import * as apiVideoIntro from "./ApiVideoIntro";
import EndscreenSuggestion from "./Player/components/EndscreenSuggestion";
import ControllerPlayer, {
  ForceLoginActionParams
} from "./Player/ControllerPlayer";
import { showDialogFastTrack, showDialogFastTrackKid } from "./utils";
import VideoPlayer from "./Player/VideoPlayer";
import Intro from "./Intro/Intro";
import EndScreenForceLogin from "./Player/components/EndscreenForceLogin";
import trackingRoutines from "../../routines/tracking";
import { EnumAuthTitle } from "../Authentication/type";
import useNetwork from "../Network/hooks";
import {
  NoNetworkNotificationType,
  pushNoNetworkMessage
} from "../Network/context";
import { AuthenFeatureName, AuthenFlowName } from "../Authentication/utils";

export enum Type {
  Intro = 0,
  ControllerPlayer = 1,
  Null = 2
}

enum VODType {
  MOVIE = 1,
  SHOW = 2,
  SEASON = 3,
  EPISODE = 4,
  LIVETV = 5,
  TRAILER = 6,
  EPG = 7
}

enum PLAY_AUTO_STATUS {
  NULL = 0, // default
  PLAY_REVIEW = 1, // dang play reivew
  PLAY_REVIEW_SUCCESS = 2, // play reivew success
  PLAY_MOVIE = 3, // play movie  (chua show control)
  PLAY_MOVIE_SUCCESS = 4 // play movie  ( show control)
}

const triggerFromValueMap: Record<string, string> = {
  audio_sub: LocalPropertyValues.SubAudioDialog,
  vip_quality: LocalPropertyValues.QualityDialog,
  svod_trial: LocalPropertyValues.SvodTrialDialog
};
const TIME_AUTO_PLAY = 3000;
const TIME_AUTO_HIDE_INFO = 5000;

const isVipFastTrack = includesValue([
  DialogType.UserVipFastTrack,
  DialogType.AnonymousUserVipFastTrack
]);

interface Props {}

const VideoIntro: React.FC<Props> = () => {
  const currentPageRef = useCurrentPageRef();
  const isAuthen = useSelector((state: RootState) => {
    return get(state, "app.isAuthen", false);
  });
  const userType = useSelector((state: RootState) => {
    return get(state, "app.userType");
  });
  const { isKid } =
    useSelector((state: RootState) => state.app.currentProfile) || {};

  const parmas = useParams();
  const history = useHistory();
  const { isOnline, notificationType } = useNetwork();
  const location: any = useLocation();
  const idVOD = get(parmas, "id", "");
  const query = useQuery();
  const statePlayer = query.get("state") === "player";
  const slugParam = query.get("slug");
  const isMasterBanner = query.get("player-type") === "masterbanner";
  const statePlayerRef = useRef(false);
  const keepDialog = useRef<string | undefined>();

  const [lastIsPaused, setLastIsPaused] = useState<boolean>(false);
  const [isShowController, setIsShowController] = useState<boolean>(false);
  const [showInStreamAds, setShowInStreamAds] = useState<boolean>(false);
  const [recommendSVODDialogData, setRecommendSVODDialogData] = useState<{
    data?: DataVideoPlayer;
    tvodDetails?: TvodDetails;
    userType?: string;
    videoType?: Type;
    seasonData?: any;
    onLogin?: () => void;
    onBuyPackage?: () => void;
    onPaymentTVOD?: () => void;
  } | null>(null);

  useEffect(() => {
    statePlayerRef.current = statePlayer;
  }, [statePlayer]);
  if (idVOD === "") {
    history.goBack();
  }

  const [isShowAdsInfoBox, setIsShowAdsInfoBox] = useState(false);
  const [delayShowDialogTriggerLogin, setDelayShowDialogTriggerLogin] =
    useState<boolean | null>(null);

  const isUnmount = useRef(false);

  const [showPlayer, setShowPlayer, showPlayerRef] = useStateRef(false);

  const [videoIntroType, setVideoIntroType] = useState(Type.Intro);
  const [isShowLoading, setIsShowLoading] = useState(true);
  const [isLoaded, setIsLoaded] = useState(false);

  const [selectSubName, setSelectSubName] = useState("");
  const [selectAudioName, setSelectAudioName] = useState("");

  const [selectStreamProfileId, setSelectStreamProfileId] = useState("");
  const [selectStreamProfile, setSelectStreamProfile] = useState(0);

  const [selectPlaybackRate, setSelectPlaybackRate] = useState(1.0);

  const [isShowEndScreenSuggestion, setIsShowEndScreenSuggestion] =
    useState(false);

  const [dataVideoIntro, setDataVideoIntro, dataVideoIntroRef] =
    useStateRef<DataVideoIntro>({} as DataVideoIntro);
  const [episodesList, setEpisodesList, episodesListRef] = useStateRef<any[]>(
    []
  );
  const [dataVideoPlayer, setDataVideoPlayer, dataVideoPlayerRef] =
    useStateRef<DataVideoPlayer>({} as DataVideoPlayer);
  const [isWatchlater, setIsWatchlater, isWatchlaterRef] = useStateRef(false);

  const dataVideoSeasonRef = useRef<any>({});

  const [videoEl, setVideoEL] = useState<HTMLVideoElement | null>(null);
  const playerRef = useRef<any>(null);

  const isGlobal = useIsGlobal();

  const profile = useSelector(
    (state: RootState) => state.app.profile
  ) as AccountModel;
  const token = useSelector((state: RootState) => state.app.token);
  const isLogged = useSelector((state: RootState) => state.app.isAuthen);
  const currentProfile = useSelector(
    (state: RootState) => state.app.currentProfile
  );

  const playAutoStatus = useRef<PLAY_AUTO_STATUS>(PLAY_AUTO_STATUS.NULL);
  const timerAutoPlay = useRef<TimerHandle>(null);
  const timerAutoHideInfo = useRef<TimerHandle>(null);
  const videoIntroInInfo = useRef(true);
  const firstLoad = useRef(true);
  const playTrailer = useRef(false);
  const afterPlayToastTextRef = useRef("");
  const navigateLoginStepTimeout = useRef<TimerHandle>(null);
  const [isShowWarning, setIsShowWarning] = useState(false);

  const canProcessTracking = useCanTrackingWithHotelAccount();
  const typeOfUser = makeTypeOfUser(isAuthen, userType);

  function checkStreamProfile(profile: StreamProfile[]) {
    let index = 0;
    for (let i = 0; i < profile.length; i++) {
      if (profile[i].id === selectStreamProfileId) {
        index = i;
        break;
      }
    }
    setSelectStreamProfile(index);
    setSelectStreamProfileId(profile[index].id);
  }

  useEffect(() => {
    if (ViePlayer.getImageId() === idVOD) {
      if (ViePlayer.getStatusPlayer() !== ViePlayerStatus.PLAY) {
        ViePlayer.showViePlayerImage(true);
      }
    } else {
      ViePlayer.destroyViePlayerImage();
    }

    if (
      ViePlayer.getId() === idVOD &&
      ViePlayer.getStatusPlayer() === ViePlayerStatus.PLAY
    ) {
      ViePlayer.changeSizePlayer("full");
      playAutoStatus.current = PLAY_AUTO_STATUS.PLAY_REVIEW;
    } else {
      ViePlayer.destroy();
    }

    return () => {
      isUnmount.current = true;
      clearTimerAutoPlay();
      clearTimerAutoHideInfo();
    };
  }, []);

  useEffect(() => {
    if (isOnline && videoIntroType === Type.Null) {
      setVideoIntroType(Type.Intro);
    }
  }, [isOnline]);

  useEffect(() => {
    setIsLoaded(false);
    setIsShowLoading(true);
    if (firstLoad.current) {
      firstLoad.current = false;
    } else {
      onSelectRecomended(0);
    }

    let selectSubName: string = "";
    let selectAudioName: string = "";
    const isFromSlug =
      query.get("slug") === "video-recommended" ||
      query.get("slug") === "video-episode";

    apiVideoIntro.getDataVideoIntro(
      idVOD,
      isGlobal,
      (success, isWatchlater, content, episodesList) => {
        if (success) {
          selectAudioName = apiVideoIntro.checkDefaultAudio(content!.audios);
          selectSubName = apiVideoIntro.checkDefaultSub(content!.subtitles);
          setSelectSubName(selectSubName);
          setSelectAudioName(selectAudioName);
          setDataVideoIntro(content!);
          dataVideoSeasonRef.current = {
            id: content?.id,
            images: { thumbnail_v4: content?.images?.thumbnail_v4 },
            title: content?.title
          };
          setSelectPlaybackRate(1.0);

          const { fromHomePreview } = (location.state || {}) as any;
          if (fromHomePreview) {
            trackingRoutines.homePreview(idVOD, content?.title || "");
          }

          if (episodesList) {
            setEpisodesList(episodesList);
          }
          setIsWatchlater(isWatchlater);
          if (statePlayerRef.current) {
            // state = player
            ViePlayer.destroy();
            const { type } = content!;
            if (type === VODType.SEASON || type === VODType.SHOW) {
              const id = content!.idEps;
              const groupID = content!.id;
              playMovieWidthStatePlayer(id, groupID);
            } else {
              const { id } = content!;
              playMovieWidthStatePlayer(id);
            }
          } else {
            // Auto play
            if (
              playAutoStatus.current === PLAY_AUTO_STATUS.NULL &&
              !isFromSlug
            ) {
              checkPlayMovieAutoPlay(content!);
            }
            setIsShowLoading(false);
            setIsLoaded(true);
          }
        } else {
          if (isOnline) {
            Message.open("Có lỗi xảy ra vui lòng thử lại");
            history.goBack();
          }
        }
      }
    );

    return () => {
      ViePlayer.destroyViePlayerImage();
      ViePlayer.destroy();
    };
  }, [idVOD]);

  const prevVideoIntroType = usePrevious<Type>(videoIntroType);
  useEffect(() => {
    if (
      prevVideoIntroType === Type.ControllerPlayer &&
      videoIntroType === Type.Intro
    ) {
      const contentType = get(dataVideoIntroRef, ["current", "type"]);
      if (
        [VideoType.SHOW, VideoType.SEASON, VideoType.EPISODE].indexOf(
          contentType
        ) >= 0
      ) {
        const listRelated: any[] = get(
          dataVideoIntroRef,
          ["current", "related_season"],
          []
        );
        const relatedIdArr = listRelated.map((item: any) =>
          get(item, "id", "")
        );
        apiVideoIntro.getEpisodeListByArray(
          relatedIdArr,
          (isSuccess, episodesList) => {
            if (isSuccess && episodesList) {
              setEpisodesList(episodesList);
            }
          }
        );
      }
    }
  }, [videoIntroType]);

  /* Tracking Start */
  useEffect(() => {
    // Tracking page view video player
    if (videoIntroType === Type.ControllerPlayer) {
      const trackingURL = get(
        dataVideoPlayerRef,
        ["current", "trackingURL"],
        ""
      );
      if (trackingURL) {
        GAManager.gaPageView(trackingURL);
      }
    }
  }, [videoIntroType]);

  /* Tracking End */

  function clearTimerAutoPlay() {
    if (timerAutoPlay.current) {
      clearTimeout(timerAutoPlay.current);
      timerAutoPlay.current = null;
    }
  }

  function clearTimerAutoHideInfo() {
    if (timerAutoHideInfo.current) {
      clearTimeout(timerAutoHideInfo.current);
      timerAutoHideInfo.current = null;
    }
  }

  function initImage(url: string) {
    const config: ViePlayerConfigImage = {
      id: idVOD,
      url: [url],
      type: isMasterBanner ? "masterbanner" : "thumbnail",
      show: ViePlayer.getStatusPlayer() !== ViePlayerStatus.PLAY && !statePlayer
    };

    ViePlayer.requestViePlayerImage(config);
  }

  const clearParamDialogAction = useCallback(() => {
    const searchParams = new URLSearchParams(location.search);
    searchParams.delete("dialog-action");
    history.replace({ search: searchParams.toString() });
    keepDialog.current = undefined;
  }, [location.search, history]);

  useEffect(() => {
    if (Object.keys(dataVideoIntro).length === 0) return;
    const searchParams = new URLSearchParams(location.search);
    const paramDialog = searchParams?.get("dialog-action");
    if (paramDialog) {
      const [type, index] = paramDialog?.split("-");
      switch (type) {
        case "audio": {
          checkPermissionAudio(Number(index));
          break;
        }
        case "sub": {
          checkPermissonSubtitle(Number(index));
          break;
        }
        default:
      }
    }
    if (
      ViePlayer.getImageId() !== idVOD &&
      (idVOD === dataVideoIntro.id || idVOD === dataVideoIntro.idEps)
    ) {
      let url: string = isMasterBanner
        ? get(dataVideoIntro, "images.carousel_tv_v4_ntc", "")
        : get(dataVideoIntro, "images.thumbnail_big_v4_ntc", "");
      if (url === "") {
        url = get(dataVideoIntro, "images.thumbnail_v4_ntc", "");
      }
      initImage(url);
    }
  }, [dataVideoIntro, idVOD]);

  function playReview() {
    clearTimerAutoPlay();
    timerAutoPlay.current = setTimeout(() => {
      if (isUnmount.current) return;
      if (playAutoStatus.current === PLAY_AUTO_STATUS.NULL) {
        playAutoStatus.current = PLAY_AUTO_STATUS.PLAY_REVIEW;
        const linkPlay =
          dataVideoIntroRef.current?.trailer_link_play?.hls_link_play ?? "";
        const warningTag = dataVideoIntroRef.current?.warningTag || "";
        if (linkPlay !== "") {
          const config: ViePlayerConfigTrailer = {
            env: "product",
            id: idVOD,
            linkURL: linkPlay,
            type: "full",
            configMedia: {
              loop: false
            },
            dataWarningMessage:
              warningTag !== ""
                ? {
                    title: warningTag,
                    position: "top_right"
                  }
                : undefined
          };
          ViePlayer.requestPlayerTrailer(config);
        }
      }
      clearTimerAutoPlay();
    }, TIME_AUTO_PLAY);
  }

  const computeGlobalTrialDialogType = (type: number, isAuthen: boolean) => {
    if (isAuthen) {
      return type === VideoType.MOVIE
        ? DialogType.UserTrialMovieRequirePaymentGlobalDialog
        : DialogType.UserTrialEpisodeRequirePaymentGlobalDialog;
    }
    return type === VideoType.MOVIE
      ? DialogType.AnonymousUserTrialMovieRequirePaymentGlobalDialog
      : DialogType.AnonymousUserTrialEpisodeRequirePaymentGlobalDialog;
  };

  const computeLocalVipEarlyAccessDialogType = (isAuthen: boolean) => {
    if (isAuthen) {
      return DialogType.UserVipFastTrack;
    }
    return DialogType.AnonymousUserVipFastTrack;
  };

  function checkPermissionPlaylist(
    begin: boolean,
    content?: DataVideoPlayer,
    nextEpisode: boolean = false
  ) {
    const contentDT: DataVideoPlayer = content || dataVideoPlayer;
    const pkgId = get(contentDT, ["packages", 0, "id"], "");

    // [CHECK] user engagement
    // contentDT.permission = 208;
    if (contentDT.trial === true) {
      return true;
    }
    if (contentDT.triggerLoginDuration > 0) {
      return true;
    }
    if (contentDT.permission === PermissionPlayerType.LIMIT_DEVICE) {
      showDialogLimitDevice(begin, contentDT);
    } else if (
      contentDT.permission === PermissionPlayerType.CONTENT_RESTRICTED
    ) {
      DialogCommon.commonDialog({
        keyName: "dialog_restricted_content",
        type: DialogType.RestrictionLimitContent,
        layoutDirection: "vertical",
        buttonsEventClicks: [
          () => {
            history.replace(ROUTES.ROOT);
          }
        ]
      });
    } else if (contentDT.permission === PermissionPlayerType.REQUIRE_LOGIN) {
      const force_login = get(contentDT, "force_login", 0) || 0;
      const isTvodContent: boolean =
        contentDT.is_premium === TYPE_OF_CONTENT.TVOD;
      if (isTvodContent) {
        const id =
          contentDT.type === 6
            ? idVOD
            : contentDT.type === 4
            ? contentDT.id
            : contentDT.group_id;
        nextPaymentTVOD(id, contentDT, false, nextEpisode);
      } else if (force_login === 1 && contentDT.is_vip === 0) {
        nextPageLogin(contentDT, "registration_trial");
      } else if (contentDT.is_vip === 0) {
        nextPageLogin(contentDT);
      } else {
        const textId = contentDT.packages[0]?.text_id;
        const isEngagementTrigger = !!contentDT?.is_trigger_engagement;
        let dialogType = DialogType.AnonymousUserVipDialog;
        if (!isGlobal && isEngagementTrigger) {
          dialogType = DialogType.AnonymousUserVipEngagementDialog;
        } else if (!isGlobal && contentDT.is_premium === TYPE_OF_CONTENT.PVOD) {
          dialogType = DialogType.FastTrackDialog;
        } else if (
          !isGlobal &&
          contentDT.is_premium === TYPE_OF_CONTENT.PVOD_NOT_HAVE_SVOD
        ) {
          dialogType = computeLocalVipEarlyAccessDialogType(false);
        } else {
          dialogType = (() => {
            switch (textId) {
              case "family":
                return DialogType.AnonymousUserFamilyDialog;
              case "sport":
                return DialogType.AnonymousUserSportDialog;
              case "kplus":
                return DialogType.AnonymousUserVipKPlusDialog;
              case "hbo":
                return DialogType.AnonymousUserVipHboDialog;
              default:
                if (contentDT.is_premium === TYPE_OF_CONTENT.TVOD_AND_SVOD) {
                  return DialogType.AnonymousVipSvodTvodDialog;
                }
                return isGlobal
                  ? computeGlobalTrialDialogType(contentDT.type, false)
                  : DialogType.AnonymousUserVipDialog;
            }
          })();
        }
        showDialogTriggerPayment(
          "svod",
          dialogType,
          contentDT,
          dataVideoIntro,
          false,
          true,
          "Bấm chọn VOD yêu cầu đăng nhập",
          pkgId
        );
      }
      return false;
    } else if (
      contentDT.permission === PermissionPlayerType.CONTENT_NOT_FOR_KID
    ) {
      const action = () => {
        history.replace(ROUTES.ROOT);
      };
      showDialogContentForKidNotAccess([action], action);
      return false;
    } else if (contentDT.permission === PermissionPlayerType.BUY_VIP) {
      // TODO ADULTS FLOW
      const isTvodContent: boolean =
        contentDT.is_premium === TYPE_OF_CONTENT.TVOD;
      if (isTvodContent) {
        if (isLogged && currentProfile?.isKid) {
          // TODO KID FLOW
          const action = () => {
            handleVisibilityChange();
          };
          showDialogContentForKidNeedBuyTVOD([action], action);
        } else {
          const id =
            contentDT.type === 6
              ? idVOD
              : contentDT.type === 4
              ? contentDT.id
              : contentDT.group_id;
          nextPaymentTVOD(id, contentDT, false, nextEpisode);
        }
      } else if (isLogged) {
        if (currentProfile?.isKid) {
          // TODO KID FLOW
          const action = () => {
            handleVisibilityChange();
          };
          if (dataVideoIntroRef.current?.hasPvod) {
            showDialogFastTrackKid({
              onOk: action,
              onReturn: action
            });
          } else {
            showDialogContentForKidNeedBuy([action], action);
          }
        } else if (!isGlobal) {
          const textId = contentDT.packages[0]?.text_id;
          // [CHECK] user engagement
          //  const isEngagementTrigger = true;
          const isEngagementTrigger = !!contentDT?.is_trigger_engagement;
          let dialogType = DialogType.UserVipDialog;
          if (isEngagementTrigger) {
            dialogType = DialogType.UserVipEngagementDialog;
          } else if (contentDT.is_premium === TYPE_OF_CONTENT.PVOD) {
            dialogType = DialogType.FastTrackDialog;
          } else if (
            contentDT.is_premium === TYPE_OF_CONTENT.PVOD_NOT_HAVE_SVOD
          ) {
            dialogType = computeLocalVipEarlyAccessDialogType(true);
          } else {
            dialogType = (() => {
              switch (textId) {
                case "family":
                  return DialogType.UserFamilyDialog;
                case "sport":
                  return DialogType.UserSportDialog;
                case "kplus":
                  return DialogType.UserVipDialog;
                case "hbo":
                  return DialogType.UserVipHboDialog;
                default:
                  if (contentDT.is_premium === TYPE_OF_CONTENT.TVOD_AND_SVOD) {
                    return DialogType.UserVipSvodTvodDialog;
                  }
                  return DialogType.UserVipDialog;
              }
            })();
          }
          showDialogTriggerPayment(
            "svod",
            dialogType,
            contentDT,
            dataVideoIntro,
            false,
            true,
            "Bấm chọn VOD VIP",
            pkgId
          );
        } else {
          showDialogTriggerPayment(
            "svod",
            computeGlobalTrialDialogType(contentDT.type, true),
            contentDT,
            dataVideoIntro,
            false,
            true,
            pkgId
          );
        }
      } else {
        const textId = contentDT.packages[0]?.text_id;
        // [CHECK] user engagement
        //  const isEngagementTrigger = true;
        const isEngagementTrigger = !!contentDT?.is_trigger_engagement;
        let dialogType = DialogType.AnonymousUserVipDialog;
        if (!isGlobal && isEngagementTrigger) {
          dialogType = DialogType.AnonymousUserVipEngagementDialog;
        } else if (!isGlobal && contentDT.is_premium === TYPE_OF_CONTENT.PVOD) {
          dialogType = DialogType.FastTrackDialog;
        } else if (
          !isGlobal &&
          contentDT.is_premium === TYPE_OF_CONTENT.PVOD_NOT_HAVE_SVOD
        ) {
          dialogType = computeLocalVipEarlyAccessDialogType(false);
        } else {
          dialogType = (() => {
            switch (textId) {
              case "family":
                return DialogType.AnonymousUserFamilyDialog;
              case "sport":
                return DialogType.AnonymousUserSportDialog;
              case "kplus":
                return DialogType.AnonymousUserVipKPlusDialog;
              case "hbo":
                return DialogType.AnonymousUserVipHboDialog;
              default:
                if (contentDT.is_premium === TYPE_OF_CONTENT.TVOD_AND_SVOD) {
                  return DialogType.AnonymousVipSvodTvodDialog;
                }
                return isGlobal
                  ? computeGlobalTrialDialogType(contentDT.type, false)
                  : DialogType.AnonymousUserVipDialog;
            }
          })();
        }
        showDialogTriggerPayment(
          "svod",
          dialogType,
          contentDT,
          dataVideoIntro,
          false,
          true,
          "Bấm chọn VOD VIP",
          pkgId
        );
      }
      return false;
    } else {
      return true;
    }
  }

  function checkPermissionAudio(index: number, replacePlayer: boolean = false) {
    if (
      dataVideoIntro &&
      dataVideoIntro.audios &&
      index < dataVideoIntro.audios!.length &&
      dataVideoIntro.audios![index].permission === PermissionPlayerType.BUY_VIP
    ) {
      // Temporary disable this trigger tracking
      // SegmentManager.segmentAction(
      //   LocalFeatureEventNames.QualitySubSubscribePackageButtonSelected,
      //   {
      //     [LocalFeaturePropertyKeys.UserType]: typeOfUser,
      //     [LocalFeaturePropertyKeys.TriggerFrom]:
      //       LocalPropertyValues.SubAudioDialog,
      //     [LocalFeaturePropertyKeys.ContentID]: dataVideoIntro.id,
      //     [LocalFeaturePropertyKeys.ContentName]: dataVideoIntro.title,
      //   }
      // );
      const pckId = dataVideoIntro.audios![index].package_id;
      if (isLogged) {
        if (currentProfile?.isKid) {
          // TODO KIDS FLOW
          const action = () => {
            if (replacePlayer) {
              playerControllerProxy("play");
            }
          };
          showDialogFeatureForKidNeedBuy([action], action);
        } else {
          // TODO ADULTS FLOW
          showDialogTriggerPayment(
            "audio_sub",
            DialogType.UserSubAudioVipDialog,
            dataVideoPlayer,
            dataVideoIntro,
            replacePlayer,
            false,
            "Bấm chọn Audio VIP",
            pckId
          );
          // showDialogPayment(
          //   DialogType.NonVipUserSubAudioVipDialog,
          //   dataVideoIntro.type === 1 ? dataVideoIntro.id : dataVideoIntro.idEps,
          //   dataVideoIntro.type,
          //   replacePlayer,
          //   false,
          //   pkgId
          // );
        }
      } else {
        showDialogTriggerPayment(
          "audio_sub",
          DialogType.AnonymousUserSubAudioVipDialog,
          dataVideoPlayer,
          dataVideoIntro,
          replacePlayer,
          false,
          "Bấm chọn Audio VIP",
          pckId
        );
      }
      return false;
    }
    if (
      dataVideoIntro &&
      dataVideoIntro.audios &&
      index < dataVideoIntro.audios!.length &&
      dataVideoIntro.audios![index].permission ===
        PermissionPlayerType.REQUIRE_LOGIN
    ) {
      // Temporary disable this trigger tracking
      // SegmentManager.segmentAction(
      //   LocalFeatureEventNames.QualitySubSubscribePackageButtonSelected,
      //   {
      //     [LocalFeaturePropertyKeys.UserType]: typeOfUser,
      //     [LocalFeaturePropertyKeys.TriggerFrom]:
      //       LocalPropertyValues.SubAudioDialog,
      //     [LocalFeaturePropertyKeys.ContentID]: dataVideoIntro.id,
      //     [LocalFeaturePropertyKeys.ContentName]: dataVideoIntro.title,
      //   }
      // );
      const pckId = dataVideoIntro.audios![index].package_id;
      showDialogTriggerPayment(
        "audio_sub",
        DialogType.AnonymousUserSubAudioVipDialog,
        dataVideoPlayer,
        dataVideoIntro,
        replacePlayer,
        false,
        "Bấm chọn Audio yêu cầu đăng nhập",
        pckId
      );
    } else if (
      dataVideoIntro &&
      dataVideoIntro.audios &&
      index < dataVideoIntro.audios!.length
    ) {
      setSelectAudioName(dataVideoIntro.audios![index].code_name);
      if (isLogged) {
        apiVideoIntro.addAudioSubtitle(
          dataVideoIntro.audios![index].code_name,
          "",
          (success) => {}
        );
      }
      return true;
    }
    return true;
  }

  function checkPermissonSubtitle(
    index: number,
    replacePlayer: boolean = false
  ) {
    if (
      dataVideoIntro &&
      dataVideoIntro.subtitles &&
      index < dataVideoIntro.subtitles!.length &&
      dataVideoIntro.subtitles![index].permission ===
        PermissionPlayerType.BUY_VIP
    ) {
      // Temporary disable this trigger tracking
      // SegmentManager.segmentAction(
      //   LocalFeatureEventNames.QualitySubSubscribePackageButtonSelected,
      //   {
      //     [LocalFeaturePropertyKeys.UserType]: typeOfUser,
      //     [LocalFeaturePropertyKeys.TriggerFrom]:
      //       LocalPropertyValues.SubAudioDialog,
      //     [LocalFeaturePropertyKeys.ContentID]: dataVideoIntro.id,
      //     [LocalFeaturePropertyKeys.ContentName]: dataVideoIntro.title,
      //   }
      // );
      const pckId = dataVideoIntro.subtitles[index].package_id;
      if (isLogged) {
        if (currentProfile?.isKid) {
          // TODO KID FLOW
          const action = () => {
            if (replacePlayer) {
              playerControllerProxy("play");
            }
          };
          showDialogFeatureForKidNeedBuy([action], action);
        } else {
          // TODO ADULTS FLOW
          // showDialogPayment(
          //   DialogType.NonVipUserSubAudioVipDialog,
          //   dataVideoIntro.type === 1 ? dataVideoIntro.id : dataVideoIntro.idEps,
          //   dataVideoIntro.type,
          //   replacePlayer,
          //   false,
          //   pkgId
          // );
          showDialogTriggerPayment(
            "audio_sub",
            DialogType.UserSubAudioVipDialog,
            dataVideoPlayer,
            dataVideoIntro,
            replacePlayer,
            false,
            "Bấm chọn Sub VIP",
            pckId
          );
        }
      } else {
        showDialogTriggerPayment(
          "audio_sub",
          DialogType.AnonymousUserSubAudioVipDialog,
          dataVideoPlayer,
          dataVideoIntro,
          replacePlayer,
          false,
          "Bấm chọn Sub VIP",
          pckId
        );
      }
      return false;
    }
    if (
      dataVideoIntro &&
      dataVideoIntro.subtitles &&
      index < dataVideoIntro.subtitles!.length &&
      dataVideoIntro.subtitles![index].permission ===
        PermissionPlayerType.REQUIRE_LOGIN
    ) {
      // Temporary disable this trigger tracking
      // SegmentManager.segmentAction(
      //   LocalFeatureEventNames.QualitySubSubscribePackageButtonSelected,
      //   {
      //     [LocalFeaturePropertyKeys.UserType]: typeOfUser,
      //     [LocalFeaturePropertyKeys.TriggerFrom]:
      //       LocalPropertyValues.SubAudioDialog,
      //     [LocalFeaturePropertyKeys.ContentID]: dataVideoIntro.id,
      //     [LocalFeaturePropertyKeys.ContentName]: dataVideoIntro.title,
      //   }
      // );
      const pckId = dataVideoIntro.subtitles[index].package_id;
      showDialogTriggerPayment(
        "audio_sub",
        DialogType.AnonymousUserSubAudioVipDialog,
        dataVideoPlayer,
        dataVideoIntro,
        replacePlayer,
        false,
        "Bấm chọn Sub yêu cầu đăng nhập",
        pckId
      );
    } else if (
      dataVideoIntro &&
      dataVideoIntro.subtitles &&
      index < dataVideoIntro.subtitles!.length
    ) {
      setSelectSubName(dataVideoIntro.subtitles![index].code_name);
      if (isLogged) {
        apiVideoIntro.addAudioSubtitle(
          "",
          dataVideoIntro.subtitles![index].code_name,
          (success) => {}
        );
      }
      return true;
    }
    return true;
  }

  function checkPermissonStreamprofile(index: number) {
    if (
      dataVideoPlayer &&
      dataVideoPlayer.streamProfile &&
      index < dataVideoPlayer.streamProfile.length &&
      dataVideoPlayer.streamProfile[index].permission ===
        PermissionPlayerType.BUY_VIP
    ) {
      // Temporary disable this trigger tracking
      // SegmentManager.segmentAction(
      //   LocalFeatureEventNames.QualitySubSubscribePackageButtonSelected,
      //   {
      //     [LocalFeaturePropertyKeys.UserType]: typeOfUser,
      //     [LocalFeaturePropertyKeys.TriggerFrom]:
      //       LocalPropertyValues.QualityDialog,
      //     [LocalFeaturePropertyKeys.ContentID]: dataVideoIntroRef.current?.id,
      //     [LocalFeaturePropertyKeys.ContentName]:
      //       dataVideoIntroRef.current?.title,
      //   }
      // );
      const pckId = dataVideoPlayer?.link_play?.package_id;
      if (isLogged) {
        if (currentProfile?.isKid) {
          // TODO KID FLOW
          const action = () => {
            playerControllerProxy("play");
          };
          showDialogFeatureForKidNeedBuy([action], action);
        } else {
          // TODO ADULTS FLOW
          // showDialogPayment(
          //   DialogType.NonVipUserQualityVipDialog,
          //   id,
          //   dataVideoPlayer.type,
          //   true,
          //   false,
          //   pkgId
          // );

          showDialogTriggerPayment(
            "vip_quality",
            DialogType.UserQualityVipDialog,
            dataVideoPlayer,
            dataVideoIntro,
            true,
            false,
            "Bấm chọn Profile VIP",
            pckId
          );
        }
      } else {
        showDialogTriggerPayment(
          "vip_quality",
          DialogType.AnonymousUserQualityVipDialog,
          dataVideoPlayer,
          dataVideoIntro,
          true,
          false,
          "Bấm chọn Profile VIP",
          pckId
        );
      }
      return false;
    }
    if (
      dataVideoPlayer &&
      dataVideoPlayer.streamProfile &&
      index < dataVideoPlayer.streamProfile.length &&
      dataVideoPlayer.streamProfile[index].permission ===
        PermissionPlayerType.REQUIRE_LOGIN
    ) {
      // Temporary disable this trigger tracking
      // SegmentManager.segmentAction(
      //   LocalFeatureEventNames.QualitySubSubscribePackageButtonSelected,
      //   {
      //     [LocalFeaturePropertyKeys.UserType]: typeOfUser,
      //     [LocalFeaturePropertyKeys.TriggerFrom]:
      //       LocalPropertyValues.QualityDialog,
      //     [LocalFeaturePropertyKeys.ContentID]: dataVideoIntro.id,
      //     [LocalFeaturePropertyKeys.ContentName]: dataVideoIntro.title,
      //   }
      // );
      const pckId = dataVideoPlayer?.link_play?.package_id;
      showDialogTriggerPayment(
        "vip_quality",
        DialogType.AnonymousUserQualityVipDialog,
        dataVideoPlayer,
        dataVideoIntro,
        true,
        false,
        "Bấm chọn Profile yêu cầu đăng nhập",
        pckId
      );
    } else {
      setSelectStreamProfile(index);
      setSelectStreamProfileId(dataVideoPlayer.streamProfile[index].id);
      return true;
    }
  }

  function checkCurrentIndexEpisode() {
    let currentIndex = 0;
    const currentEpisodeList = get(episodesListRef, "current", []);
    currentIndex = currentEpisodeList.findIndex(
      (row: any) => row.id === dataVideoPlayer.id
    );
    if (currentIndex >= 0) {
      return currentIndex;
    }
    return 0;
  }

  function showDialogTriggerPayment(
    triggerType: SVODTrialTriggerType,
    dialogType: DialogType,
    contentDT: DataVideoPlayer,
    dataVideoIntro: DataVideoIntro,
    replacePlayer: boolean = false,
    destroyPlayer: boolean = false,
    popupName?: string,
    pckId?: number
  ) {
    const id = contentDT.id || dataVideoIntro.id;
    const showDialog = async () => {
      const authenticatedUserTriggerPaymentList = [
        DialogType.UserFamilyDialog,
        DialogType.UserSportDialog,
        DialogType.UserVipDialog,
        DialogType.UserVipHboDialog,
        DialogType.UserSubAudioVipDialog,
        DialogType.UserQualityVipDialog,
        DialogType.UserTrialEpisodeRequirePaymentGlobalDialog,
        DialogType.UserTrialMovieRequirePaymentGlobalDialog,
        DialogType.UserVipEngagementDialog,
        DialogType.UserVipFastTrack,
        DialogType.UserVipSvodTvodDialog
      ];
      const typeOfUser = makeTypeOfUser(isAuthen, userType);
      const packagesId =
        pckId || get(dataVideoIntro, ["packages", 0, "id"], "");
      const packageName =
        get(dataVideoIntro, ["packages", 0, "name"], "") ||
        get(contentDT, ["packages", 0, "name"], "");
      const isEngagementTrigger = [
        DialogType.UserVipEngagementDialog,
        DialogType.AnonymousUserVipEngagementDialog
      ].includes(dialogType);
      let pkgIdFromConfig = "";
      let durationIdFromConfig = "";
      let couponCodeFromConfig = "";
      if (isEngagementTrigger) {
        const responseDataDialog = dialogConfig.get(
          getDialogKey(dialogType, DialogUserType.VIP)
        );
        pkgIdFromConfig = responseDataDialog?.packageId || "";
        durationIdFromConfig = responseDataDialog?.durationId || "";
        couponCodeFromConfig = responseDataDialog?.couponCode || "";
      }
      if (statePlayer) {
        const params = new URLSearchParams(location.search);
        params.delete("state");
        history.replace({
          search: params.toString()
        });
      }
      const handleBuyPackage = () => {
        clearParamDialogAction();
        if (dialogType === DialogType.AnonymousUserRequireSignUp) {
          const searchParams = new URLSearchParams();

          // TODO: Start: handle navigation for video intro: back to previous page after done payment
          const urlParams = new URLSearchParams(location.search);
          const slug = urlParams.get("slug");
          if (slug === EnumParamsVideoIntro.VideoAudioSubtitle) {
            searchParams.append("slug", slug);
          }
          // TODO: End: handle navigation for video intro: back to previous page after done payment

          if (contentDT.id) {
            searchParams.append(
              "redirect",
              `${ROUTES.VIDEO_INTRO}/${
                contentDT.type === 6
                  ? dataVideoIntro.id
                  : contentDT.type === 4
                  ? contentDT.id
                  : contentDT.group_id
              }`
            );
          } else {
            searchParams.append(
              "redirect",
              `${ROUTES.VIDEO_INTRO}/${dataVideoIntro.id}`
            );
          }
          searchParams.append("focusIndex", "1");
          history.push({
            pathname: ROUTES.LOGIN_STEP,
            search: searchParams.toString(),
            state: {
              flowName: GlobalFeatureFlowName.RegistrationForContent,
              authenFlowName: AuthenFlowName.PaymentTrigger
            }
          });
          SegmentManager.segmentAction("sign_up_button_selected", {
            popup_name: "Dialog Đăng nhập để xem nội dung",
            [GlobalFeaturePropertyKeys.flowName]:
              GlobalFeatureFlowName.RegistrationForContent
          });
        } else {
          const pkgId = get(contentDT, ["packages", 0, "id"], "");
          const pkgIdState =
            pckId || pkgId || get(dataVideoIntro, ["packages", 0, "id"], "");
          if (contentDT.type === 4) {
            const searchParams = new URLSearchParams();
            if (replacePlayer) {
              searchParams.append("state", "player");
            }
            history.replace({
              pathname: `${ROUTES.VIDEO_INTRO}/${contentDT.id}`,
              search: searchParams.toString()
            });
          }
          const searchParams = new URLSearchParams();
          const urlParams = new URLSearchParams(location.search);

          // TODO: Start: handle navigation for video intro: back to previous page after done payment
          const slug = urlParams.get("slug");
          if (slug === EnumParamsVideoIntro.VideoAudioSubtitle) {
            searchParams.append("slug", slug);
          }
          // TODO: End: handle navigation for video intro: back to previous page after done payment

          if (contentDT.id) {
            searchParams.append(
              authenticatedUserTriggerPaymentList.includes(dialogType)
                ? "from"
                : "redirect",
              `${ROUTES.VIDEO_INTRO}/${
                contentDT.type === 6
                  ? dataVideoIntro.id
                  : contentDT.type === 4
                  ? contentDT.id
                  : contentDT.group_id
              }`
            );
          } else {
            searchParams.append(
              authenticatedUserTriggerPaymentList.includes(dialogType)
                ? "from"
                : "redirect",
              `${ROUTES.VIDEO_INTRO}/${dataVideoIntro.id}`
            );
          }
          if (contentDT.type !== VideoType.TRAILER) {
            if (slug !== EnumParamsVideoIntro.VideoAudioSubtitle) {
              searchParams.append("state", "player");
            }
            const recommended = urlParams.get("recommended");
            if (recommended) {
              searchParams.append("recommended", recommended);
            }
          }
          if (!isGlobal) {
            if (isEngagementTrigger) {
              searchParams.append("pkgId", pkgIdFromConfig || pkgId || "");
              if (isAuthen) {
                searchParams.append("durationId", durationIdFromConfig);
              }
              searchParams.append("pkgTitle", "VIP");
              searchParams.append("couponCode", couponCodeFromConfig);
            } else {
              if (
                !pkgId &&
                popupName &&
                [
                  "Bấm chọn Profile VIP",
                  "Bấm chọn Audio VIP",
                  "Bấm chọn Sub VIP"
                ].indexOf(popupName) !== -1
              ) {
                searchParams.append("pkgTitle", "VIP");
              } else {
                searchParams.append("pkgId", pkgId);
              }
            }
          }
          history.push({
            pathname: isGlobal ? ROUTES.PAYMENT_GLOBAL : ROUTES.PAYMENT,
            search: searchParams.toString(),
            state: authenticatedUserTriggerPaymentList.includes(dialogType)
              ? {
                  referal: "Video Intro",
                  contentId: contentDT.id,
                  pkgId: pkgIdFromConfig || pkgIdState,
                  durationId:
                    durationIdFromConfig?.length > 0
                      ? durationIdFromConfig
                      : null,
                  data:
                    Object.keys(contentDT).length > 0
                      ? contentDT
                      : dataVideoSeasonRef.current,
                  groupData: dataVideoSeasonRef.current,
                  [GlobalFeaturePropertyKeys.currentPage]:
                    GlobalFeatureCurrentPage.ViewVideoDetail
                }
              : {
                  contentId: contentDT.id,
                  data:
                    Object.keys(contentDT).length > 0
                      ? contentDT
                      : dataVideoSeasonRef.current,
                  pkgId: pkgIdFromConfig || pkgIdState,
                  durationId:
                    durationIdFromConfig?.length > 0
                      ? durationIdFromConfig
                      : null,
                  groupData: dataVideoSeasonRef.current,
                  [GlobalFeaturePropertyKeys.currentPage]:
                    GlobalFeatureCurrentPage.ViewVideoDetail
                }
          });
          if (isGlobal) {
            // Tracking
            SegmentManager.segmentAction(
              GlobalFeatureEventNames.PaymentButtonSelected,
              {
                [GlobalFeaturePropertyKeys.currentPage]:
                  GlobalFeatureCurrentPage.ViewVideoDetail
              }
            );
          } else if (isVipFastTrack(dialogType)) {
            trackingFastTrack({
              trackingName: TrackingFastTrackNames.RevisePaymentSelected,
              trackingContent: {
                [TrackingFastTrackProps.flowName]: LocalPropertyValues.FastTrack
              }
            });
          } else if (isEngagementTrigger) {
            SegmentManager.segmentAction(
              TrackingEngagementTriggerEventNames.EngagementTriggerSubscriptionButtonSelected
            );
          }
        }
      };
      const handleLogin = authenticatedUserTriggerPaymentList.includes(
        dialogType
      )
        ? () => {}
        : () => {
            if (contentDT.type === 4) {
              const searchParams = new URLSearchParams();
              if (replacePlayer) {
                searchParams.append("state", "player");
              }
              history.replace({
                pathname: `${ROUTES.VIDEO_INTRO}/${contentDT.id}`,
                search: searchParams.toString()
              });
            }
            const searchParams = new URLSearchParams();
            const urlParams = new URLSearchParams(location.search);

            // TODO: Start: handle navigation for video intro: back to previous page after done payment
            const slug = urlParams.get("slug");
            if (slug === EnumParamsVideoIntro.VideoAudioSubtitle) {
              searchParams.append("slug", slug);
              if (keepDialog.current) {
                searchParams.append("dialog-action", `${keepDialog.current}`);
              }
            }
            // TODO: End: handle navigation for video intro: back to previous page after done payment

            if (contentDT.id) {
              searchParams.append(
                "redirect",
                `${ROUTES.VIDEO_INTRO}/${
                  contentDT.type === 6
                    ? dataVideoIntro.id
                    : contentDT.type === 4
                    ? contentDT.id
                    : contentDT.group_id
                }`
              );
            } else {
              searchParams.append(
                "redirect",
                `${ROUTES.VIDEO_INTRO}/${dataVideoIntro.id}`
              );
            }
            if (contentDT.type !== VideoType.TRAILER) {
              if (slug !== EnumParamsVideoIntro.VideoAudioSubtitle) {
                searchParams.append("state", "player");
              }
              const recommended = urlParams.get("recommended");
              if (recommended) {
                searchParams.append("recommended", recommended);
              }
            }
            history.push({
              pathname: ROUTES.LOGIN_STEP,
              search: searchParams.toString(),
              state: {
                flowName: [GlobalFeatureFlowName.RegistrationForContent],
                isBack: true,
                isUseTrackLocation: false,
                authenFlowName: AuthenFlowName.PaymentTrigger
              }
            });
            if (isEngagementTrigger) {
              SegmentManager.segmentAction(
                TrackingEngagementTriggerEventNames.EngagementTriggerSignInButtonSelected
              );
            } else {
              SegmentManager.segmentAction("login button selected", {
                current_page: replacePlayer ? "VOD Player" : "VOD Intro",
                popup_name:
                  popupName ||
                  `Bấm chọn chức năng yêu cầu ${
                    contentDT.is_vip > 0 ? "login" : "VIP"
                  }`,
                [GlobalFeaturePropertyKeys.flowName]:
                  GlobalFeatureFlowName.RegistrationForPayment
              });
            }
          };
      const handleBackKey = () => {
        clearParamDialogAction();
        if (replacePlayer) {
          playerControllerProxy("play");
        } else if (destroyPlayer) {
          handleVisibilityChange();
        }
        if (!isGlobal && isVipFastTrack(dialogType)) {
          trackingFastTrack({
            trackingName: TrackingFastTrackNames.RevisePaymentClosed,
            trackingContent: {
              [TrackingFastTrackProps.flowName]: LocalPropertyValues.FastTrack
            }
          });
        } else if (dialogType === DialogType.AnonymousUserRequireSignUp) {
          SegmentManager.segmentAction("sign_up_cancel", {
            popup_name: "Dialog Đăng nhập để xem nội dung",
            cancel: "user bấm back ở Smart TV",
            flow_name: "registration_for_content"
          });
        }
      };
      if (isEngagementTrigger) {
        SegmentManager.segmentAction(
          TrackingEngagementTriggerEventNames.EngagementTriggerDialogLoaded
        );
      }
      if (!isGlobal && isVipFastTrack(dialogType)) {
        SegmentManager.segmentAction(
          TrackingFastTrackNames.DialogRevisePaymentLoaded,
          {
            [LocalFeaturePropertyKeys.FlowName]: LocalPropertyValues.FastTrack,
            [LocalFeaturePropertyKeys.UserType]: typeOfUser,
            [LocalFeaturePropertyKeys.ContentID]:
              dataVideoIntroRef.current?.idEps || id || "",
            [LocalFeaturePropertyKeys.ContentName]:
              dataVideoIntroRef.current?.titleEps ||
              dataVideoIntroRef?.current?.title ||
              "",
            [LocalFeaturePropertyKeys.PackageId]: packagesId,
            [LocalFeaturePropertyKeys.PackageName]: packageName,
            [LocalFeaturePropertyKeys.CampainID]: null,
            [LocalFeaturePropertyKeys.CampainName]: null
          }
        );
      }
      if (
        dialogType === DialogType.UserVipSvodTvodDialog ||
        dialogType === DialogType.AnonymousVipSvodTvodDialog
      ) {
        if (dataVideoSeasonRef.current?.images) {
          contentDT.images = dataVideoSeasonRef.current?.images;
        }
        setRecommendSVODDialogData({
          data: contentDT,
          seasonData: dataVideoIntroRef.current?.tvodSeasons,
          videoType: videoIntroType,
          userType: typeOfUser,
          tvodDetails: dataVideoIntroRef.current?.tvodDetails,
          onLogin: handleLogin,
          onBuyPackage: handleBuyPackage,
          onPaymentTVOD: () =>
            nextPaymentTVOD(
              contentDT?.type === 4 ? contentDT?.group_id : contentDT?.id,
              contentDT,
              false,
              false
            )
        });
      } else if (dialogType === DialogType.FastTrackDialog) {
        const onOk = () => {
          goToPaymentPvod({
            id: contentDT?.id,
            contentDT,
            replacePlayer
          });
        };
        const onReturn = () => {
          if (replacePlayer) {
            playerControllerProxy("play");
          } else if (destroyPlayer) {
            handleVisibilityChange();
          }
        };
        const pvodDetail = await getPVODMovieInfoService({
          contentId: contentDT?.id || "",
          contentType: contentDT?.type || 0
        });
        const contentImg =
          dataVideoIntroRef?.current?.images?.poster_v4_show ||
          dataVideoIntroRef?.current?.images?.poster_v4_ntc_show ||
          "";
        showDialogFastTrack({
          onOk,
          onReturn,
          contentName: dataVideoIntroRef?.current?.title || "",
          contentEpisode: pvodDetail?.bizInfo?.contentMsg || "",
          contentPrice: pvodDetail?.bizInfo?.priceMsg || "",
          contentImg,
          contentDate: pvodDetail?.bizInfo?.expirationMsg || "",
          contentId: contentDT?.id,
          contentTitle: contentDT?.title
        });
      } else {
        if (isGlobal || isVipFastTrack(dialogType)) {
          DialogCommon.commonDialog({
            keyName: "dialog_revise_end_trial_required_signup",
            type: dialogType,
            layoutDirection:
              DialogType.AnonymousUserRequireSignUp === dialogType
                ? "vertical"
                : "columnEnd",
            packageID: get(contentDT, ["packages", 0, "id"], ""),
            buttonsEventClicks: [handleBuyPackage, handleLogin],
            onBack: handleBackKey,
            showTipBoxGoback: true
          });
        } else {
          DialogCommon.fullScreenDialog({
            keyName: "dialog_revise_end_trial_required_signup",
            children: <SVODTrialContent />,
            onNavigateSVODPayment: handleBuyPackage,
            onNavigateLoginPayment: handleLogin,
            contentId: id,
            contentTitle: dataVideoIntroRef?.current.title,
            contentType: contentDT.type,
            onBack: handleBackKey,
            triggerType,
            posterImage: dataVideoIntroRef.current?.images?.poster_v4_show,
            trackingData: {
              packageId: packagesId,
              packageName,
              idEps: dataVideoIntroRef.current?.idEps,
              titleEps: dataVideoIntroRef.current?.titleEps
            }
          });
        }
      }
    };

    if (replacePlayer) {
      if (contentDT.type !== 6) {
        const curPath = get(location, "pathname", "");
        const stateParams = new URLSearchParams();
        stateParams.append("state", "player");
        const urlParams = new URLSearchParams(location.search);
        const recommended = urlParams.get("recommended");
        if (recommended) {
          stateParams.append("recommended", recommended);
        }
        history.replace({
          pathname: curPath,
          search: stateParams.toString()
        });
      }
      setTimeout(() => {
        showDialog();
      });
    } else {
      showDialog();
    }
  }

  function nextPageLogin(content?: DataVideoPlayer, flowName?: string) {
    const params = new URLSearchParams(location.search);
    if (statePlayer) {
      params.delete("state");
      history.replace({
        search: params.toString()
      });
    }

    const searchParams = new URLSearchParams();
    const contentDT = content ?? dataVideoPlayer;
    const contentId =
      contentDT.type === 6
        ? dataVideoIntro.id
        : contentDT.type === 4
        ? contentDT.id
        : contentDT.group_id;

    if (contentDT.id) {
      if (location?.state?.page !== "schedule") {
        history.replace({
          ...location,
          pathname: `${ROUTES.VIDEO_INTRO}/${contentId}`
        });
      }

      searchParams.append("redirect", `${ROUTES.VIDEO_INTRO}/${contentId}`);
    } else {
      searchParams.append(
        "redirect",
        `${ROUTES.VIDEO_INTRO}/${dataVideoIntro.id}`
      );
    }
    searchParams.append("focusIndex", "1");
    searchParams.append("state", "player");
    if (slugParam?.length) {
      searchParams.append("slug", slugParam);
    }

    if (flowName) {
      searchParams.append("flow_name", flowName);
    }

    if (flowName === "registration_trial" && videoEl?.currentTime) {
      if (videoEl?.currentTime) {
        KeepAlive.saveData({
          path: `${ROUTES.VIDEO_INTRO}/${contentId}`,
          focus: { x: 0, y: 0 },
          extra: {
            playerTime: Math.ceil(videoEl.currentTime) || 0,
            state: "player"
          }
        });
      }
    }
    if (navigateLoginStepTimeout.current) {
      clearTimeout(navigateLoginStepTimeout.current);
    }
    navigateLoginStepTimeout.current = setTimeout(() => {
      history.push(`${ROUTES.VIDEO_INTRO}/${contentId}`);
      history.replace({
        pathname: ROUTES.LOGIN_STEP,
        search: searchParams.toString(),
        state: {
          title:
            flowName === "registration_trial"
              ? EnumAuthTitle.RegisterByRegistrationTrial
              : EnumAuthTitle.Content,
          flowName,
          isUseTrackLocation: false,
          isBack: true,
          authenFlowName: AuthenFlowName.RegistrationTrigger
        }
      });
    }, 50);
  }

  function nextPayment(
    id: string,
    type: number,
    replacePlayer: boolean,
    pkgId: any
  ) {
    if (statePlayer) {
      const params = new URLSearchParams(location.search);
      params.delete("state");
      history.replace({
        search: params.toString()
      });
    }

    const showPayment = () => {
      const searchParams = new URLSearchParams();
      searchParams.append("from", `${ROUTES.VIDEO_INTRO}/${id}`);
      if (type !== 6) {
        searchParams.append("state", "player");
        const urlParams = new URLSearchParams(location.search);
        const recommended = urlParams.get("recommended");
        if (recommended) {
          searchParams.append("recommended", recommended);
        }
      }
      if (pkgId) {
        searchParams.append("pkgId", pkgId.toString());
      }
      history.push({
        pathname: ROUTES.PAYMENT,
        search: searchParams.toString(),
        state: {
          referal: "Video Intro",
          contentId: id
        }
      });
    };

    if (replacePlayer) {
      if (type !== 6) {
        const curPath = get(location, "pathname", "");
        const stateParams = new URLSearchParams();
        stateParams.append("state", "player");
        const urlParams = new URLSearchParams(location.search);
        const recommended = urlParams.get("recommended");
        if (recommended) {
          stateParams.append("recommended", recommended);
        }
        history.replace({
          pathname: curPath,
          search: stateParams.toString()
        });
      }
      setTimeout(() => {
        showPayment();
      });
    } else {
      showPayment();
    }
  }

  const goToPaymentPvod = ({
    id,
    contentDT,
    replacePlayer,
    slugPage = "",
    isRemoveReturnBtn = false
  }: {
    id: string;
    contentDT: DataVideoPlayer;
    replacePlayer: boolean;
    slugPage?: string;
    isRemoveReturnBtn?: boolean;
  }) => {
    if (statePlayerRef.current) {
      const params = new URLSearchParams(location.search);
      params.delete("state");
      history.replace({
        search: params.toString()
      });
    }

    const showPayment = () => {
      const searchParams = new URLSearchParams();
      searchParams.append("from", `${ROUTES.VIDEO_INTRO}/${id}`);
      if (contentDT.type !== 6) {
        searchParams.append("state", "player");
        const urlParams = new URLSearchParams(location.search);
        const recommended = urlParams.get("recommended");
        const slug = urlParams.get("slug") || slugPage;
        if (recommended) {
          searchParams.append("recommended", recommended);
        }
        if (slug) {
          searchParams.append("slug", slug);
        }
        if (isRemoveReturnBtn) {
          searchParams.append("isRemoveReturnBtn", "true");
        }
      }
      let slug;
      if (contentDT.type === VideoType.MOVIE) {
        slug = "movie";
      } else {
        slug = "show";
      }
      const idPayment =
        contentDT.type === 4 ? contentDT.group_id : contentDT.id;
      history.push({
        pathname: `${ROUTES.PAYMENT_PVOD}/${slug}/${idPayment}`,
        search: searchParams.toString(),
        state: {
          referal: "Video Intro",
          contentId: id,
          image:
            (dataVideoIntroRef.current.images.poster_v4_show ||
              dataVideoIntroRef.current.images.poster_v4_ntc_show) ??
            ""
        }
      });
    };

    if (replacePlayer) {
      if (contentDT.type !== 6) {
        const curPath = get(location, "pathname", "");
        const stateParams = new URLSearchParams();
        stateParams.append("state", "player");
        const urlParams = new URLSearchParams(location.search);
        const recommended = urlParams.get("recommended");
        if (recommended) {
          stateParams.append("recommended", recommended);
        }
        history.replace({
          pathname: curPath,
          search: stateParams.toString()
        });
      }
      setTimeout(() => {
        showPayment();
      });
    } else {
      showPayment();
    }
  };

  function nextPaymentTVOD(
    id: string,
    contentDT: DataVideoPlayer,
    replacePlayer: boolean,
    nextEpisode: boolean
  ) {
    if (nextEpisode) {
      const title = get(dataVideoIntroRef, "current.title", "");
      const image =
        get(dataVideoIntroRef, "current.images.poster_v4", "") ||
        get(dataVideoIntro, "current.images.poster_v4_ntc", "");

      const { waitingDurMsg, consumingDurMsg, tvodPrice, tvodPriceMsg } =
        dataVideoIntroRef.current.tvodDetails;
      const typeOfUser = makeTypeOfUser(isAuthen, userType);

      trackTvod(
        TVOD_EVENT_NAMES.dialogNotRentLoaded,
        dataVideoIntroRef.current,
        typeOfUser
      );

      const cancelDialog = () => {
        trackTvod(
          TVOD_EVENT_NAMES.dialogNotRentLoadedClose,
          dataVideoIntroRef.current,
          typeOfUser
        );
        handleVisibilityChange();
      };

      DialogCommon.commonDialog({
        keyName: "dialog_next_payment_tvod",
        type: DialogType.CommonCustomerDialog,
        direction: "vertical",
        layoutDirection: "vertical",
        dataDialog: {
          title: `Xem “${title}” với giá ${tvodPriceMsg}`,
          rightArea: {
            bgFrame: dialogBgCover,
            imageCenter: image
          },
          actions: [
            {
              key: "thue_ngay",
              title: "Thuê ngay",
              onClick: () => {
                trackTvod(
                  TVOD_EVENT_NAMES.dialogNotRentLoadedAccept,
                  dataVideoIntroRef.current,
                  typeOfUser
                );
                nextPaymentTVODConfirm(id, contentDT, replacePlayer);
              }
            },
            {
              key: "bo_qua",
              title: "Bỏ qua",
              onClick: cancelDialog
            }
          ]
        },
        onBack: cancelDialog,
        showTermsOfUse: false,
        description: (
          <>
            <div className="remain-time light">
              <Icon width={32} height={32} name="vie-time" />{" "}
              <span>
                <b>{waitingDurMsg}</b> để bắt đầu xem nội dung
              </span>
            </div>
            <div className="remain-time light">
              <Icon width={32} height={32} name="vie-time" />{" "}
              <span>
                <b>{consumingDurMsg}</b> để xem hết kể từ lúc bắt đầu xem
              </span>
            </div>
          </>
        ),
        showTipBoxGoback: true
      });
      return;
    }
    nextPaymentTVODConfirm(id, contentDT, replacePlayer);
  }

  function nextPaymentTVODConfirm(
    id: string,
    contentDT: DataVideoPlayer,
    replacePlayer: boolean
  ) {
    if (statePlayer) {
      const params = new URLSearchParams(location.search);
      params.delete("state");
      history.replace({
        search: params.toString()
      });
    }

    const showPayment = () => {
      const searchParams = new URLSearchParams();
      searchParams.append("from", `${ROUTES.VIDEO_INTRO}/${id}`);
      if (contentDT.type !== 6) {
        searchParams.append("state", "player");
        const urlParams = new URLSearchParams(location.search);
        const recommended = urlParams.get("recommended");
        if (recommended) {
          searchParams.append("recommended", recommended);
        }
      }
      if (contentDT.is_premium === TYPE_OF_CONTENT.TVOD_AND_SVOD) {
        searchParams.append("flow", "tvod-svod");
      }
      let slug;
      if (dataVideoIntroRef?.current?.isSimulcast) {
        slug = "simulcast";
        searchParams.append(
          "releasedTimeMsg",
          dataVideoIntroRef?.current?.releasedTimeMsg
        );
      } else if (contentDT.type === VideoType.MOVIE) {
        slug = "movie";
      } else {
        slug = "show";
      }
      const idPayment =
        contentDT.type === 4 ? contentDT.group_id : contentDT.id;
      history.push({
        pathname: `${ROUTES.PAYMENT_TVOD}/${slug}/${idPayment}`,
        search: searchParams.toString(),
        state: {
          referal: "Video Intro",
          contentId: id,
          image: dataVideoIntroRef.current.images.poster_v4 ?? ""
        }
      });
    };

    if (replacePlayer) {
      if (contentDT.type !== 6) {
        const curPath = get(location, "pathname", "");
        const stateParams = new URLSearchParams();
        stateParams.append("state", "player");
        const urlParams = new URLSearchParams(location.search);
        const recommended = urlParams.get("recommended");
        if (recommended) {
          stateParams.append("recommended", recommended);
        }
        history.replace({
          pathname: curPath,
          search: stateParams.toString()
        });
      }
      setTimeout(() => {
        showPayment();
      });
    } else {
      showPayment();
    }
  }

  // This function is only used for authenticated user
  // currently only use for SVOD trial dialog
  function showDialogPayment(
    triggerType: SVODTrialTriggerType,
    dialogType: DialogType,
    id: string,
    type: number,
    replacePlayer: boolean = false,
    destroyPlayer: boolean = false,
    pkgId?: number,
    pkgName?: string
  ) {
    const showDialog = () => {
      const isTrialGlobalDialog = includesValue([
        DialogType.UserTrialMovieRequirePaymentGlobalDialog,
        DialogType.UserTrialEpisodeRequirePaymentGlobalDialog
      ]);
      const handleBuyPackage = () => {
        const searchParams = new URLSearchParams();
        searchParams.append("from", `${ROUTES.VIDEO_INTRO}/${id}`);
        if (type !== 6) {
          searchParams.append("state", "player");
          const urlParams = new URLSearchParams(location.search);
          const recommended = urlParams.get("recommended");
          if (recommended) {
            searchParams.append("recommended", recommended);
          }
        }
        if (pkgId) {
          searchParams.append("pkgId", pkgId.toString());
        }

        // Tracking for trial dialog
        if (isTrialGlobalDialog(dialogType)) {
          SegmentManager.segmentAction(
            LocalFeatureEventNames.SvodTrialSubscribePackageButtonSelected,
            {
              [LocalFeaturePropertyKeys.TriggerFrom]:
                LocalPropertyValues.SvodTrialDialog,
              [LocalFeaturePropertyKeys.UserType]: typeOfUser,
              [LocalFeaturePropertyKeys.ContentID]:
                dataVideoIntroRef.current?.id ?? "",
              [LocalFeaturePropertyKeys.ContentName]:
                dataVideoIntroRef.current?.title ?? "",
              [LocalFeaturePropertyKeys.CampainID]: null,
              [LocalFeaturePropertyKeys.CampainName]: null
            }
          );
        }
        history.push({
          pathname: isTrialGlobalDialog(dialogType)
            ? ROUTES.PAYMENT_GLOBAL
            : ROUTES.PAYMENT,
          search: searchParams.toString(),
          state: {
            referal:
              (DialogType.NonVipUserFreeTrialDialog === dialogType && pkgId) ||
              isTrialGlobalDialog(dialogType)
                ? "svod_trial_subcribe_package"
                : "Video Intro",
            contentId: id,
            data:
              Object.keys(dataVideoPlayer).length > 0
                ? dataVideoPlayer
                : dataVideoSeasonRef.current,
            groupData: dataVideoSeasonRef.current,
            pkgId,
            [GlobalFeaturePropertyKeys.currentPage]:
              GlobalFeatureCurrentPage.TrialContent
          }
        });
        if (isTrialGlobalDialog(dialogType)) {
          // Tracking
          SegmentManager.segmentAction(
            GlobalFeatureEventNames.PaymentButtonSelected,
            {
              [GlobalFeaturePropertyKeys.currentPage]:
                GlobalFeatureCurrentPage.TrialContent
            }
          );
        }
      };

      const handleBackKey = () => {
        if (replacePlayer) {
          playerControllerProxy("play");
        } else if (destroyPlayer) {
          handleVisibilityChange();
        }
        // Tracking for trial dialog
        if (isTrialGlobalDialog(dialogType)) {
          SegmentManager.segmentAction(
            LocalFeatureEventNames.SvodTrialCancelButtonSelected,
            {
              [LocalFeaturePropertyKeys.TriggerFrom]:
                LocalPropertyValues.SvodTrialDialog,
              [LocalFeaturePropertyKeys.UserType]: typeOfUser || "",
              [LocalFeaturePropertyKeys.ContentID]:
                dataVideoIntroRef.current?.id ?? "",
              [LocalFeaturePropertyKeys.ContentName]:
                dataVideoIntroRef.current?.title ?? "",
              [LocalFeaturePropertyKeys.CampainID]: null,
              [LocalFeaturePropertyKeys.CampainName]: null
            }
          );
        }
      };
      if (isGlobal || isVipFastTrack(dialogType)) {
        DialogCommon.commonDialog({
          keyName: "dialog_revise_end_trial_required_payment",
          type: dialogType,
          layoutDirection: "columnEnd",
          packageID: `${pkgId}`,
          buttonsEventClicks: [handleBuyPackage, () => {}],
          onBack: handleBackKey,
          showTipBoxGoback: true
        });
      } else {
        DialogCommon.fullScreenDialog({
          keyName: "dialog_revise_end_trial_required_payment",
          children: <SVODTrialContent />,
          onNavigateSVODPayment: handleBuyPackage,
          onBack: handleBackKey,
          contentId: id,
          contentTitle: dataVideoIntroRef.current?.title,
          contentType: dataVideoIntroRef.current?.type,
          triggerType,
          posterImage: dataVideoIntroRef.current?.images?.poster_v4_show,
          trackingData: {
            idEps: dataVideoIntroRef.current?.idEps,
            titleEps: dataVideoIntroRef.current?.titleEps
          }
        });
      }
    };
    // Profile kid not push payment
    if (currentProfile?.isKid) {
      const action = () => {
        if (replacePlayer) {
          playerControllerProxy("play");
        } else if (destroyPlayer) {
          handleVisibilityChange();
        }
      };
      showDialogFeatureForKidNeedBuy([action], action);
      return;
    }
    if (replacePlayer) {
      if (type !== 6) {
        const curPath = get(location, "pathname", "");
        const stateParams = new URLSearchParams();
        stateParams.append("state", "player");
        const urlParams = new URLSearchParams(location.search);
        const recommended = urlParams.get("recommended");
        if (recommended) {
          stateParams.append("recommended", recommended);
        }
        history.replace({
          pathname: curPath,
          search: stateParams.toString()
        });
      }
      setTimeout(() => {
        showDialog();
      });
    } else {
      showDialog();
    }
  }

  function showDialogError(begin: boolean = false, error?: any) {
    if (!isOnline) {
      pushNoNetworkMessage();
      return;
    }
    // LOG SENTRY
    const titleVOD =
      dataVideoIntroRef.current.type === 3 ||
      dataVideoIntroRef.current.type === 2
        ? `${dataVideoIntroRef.current.title} - ${dataVideoIntroRef.current.titleEps}`
        : dataVideoIntroRef.current.title;
    const idVOD =
      dataVideoIntroRef.current.type === 3 ||
      dataVideoIntroRef.current.type === 2
        ? dataVideoIntroRef.current.idEps
        : dataVideoIntroRef.current.id;

    const message = "VOD_PLAYER";
    const extra = {
      errorDetail: error,
      errorDetailStr: JSON.stringify(error),
      contentId: idVOD,
      contentName: titleVOD,
      browserSupport: {},
      firmwareVersion: platform.firmwareVersion,
      platform: { ...platform }
    };
    const user = {
      ...profile
    };
    const tag = {
      environment: process.env.REACT_APP_SENTRY_ENV as string,
      errorType: EnumSentryErrorType.API_DETAIL,
      level: EnumSentryLevelType.Error,
      contentType: "VOD",
      contentId: idVOD,
      contentName: titleVOD,
      phone: profile?.mobile ?? "",
      email: profile?.email ?? "",
      errorCode: "API_DETAIL_ERROR",
      errorMessage: "API_DETAIL_ERROR",
      recordId: renderRecordID()
    };
    SentryManager.captureEvent(message, user, tag, extra);

    // LOG DATA
    // SegmentManager.segmentAction("player error", {
    //   content_id: dataVideoIntro.id,
    //   content_title:
    //     dataVideoIntro.type === 4
    //       ? dataVideoIntro.titleEps
    //       : dataVideoIntro.title,
    //   content_type:
    //     dataVideoIntro.type === 1
    //       ? "movie"
    //       : dataVideoIntro.type === 2
    //       ? "show"
    //       : dataVideoIntro.type === 3
    //       ? "season"
    //       : dataVideoIntro.type === 6
    //       ? "trailer"
    //       : "episode",
    //   season_name: dataVideoIntro.title,
    //   player_error_code: playerErrorCode,
    //   player_profile: null,
    //   player_subtitle: dataVideoPlayer,
    //   player_audio: null,
    //   player_codec: null,
    //   player_streaming_protocal: null,
    // });
    const obj =
      dataVideoPlayerRef.current && dataVideoPlayerRef.current.isDVR
        ? dialogConfig.get("cat-error-premiere")
        : dialogConfig.get("cat-error-vod");
    const titleDialog =
      get(obj, "title", DialogTitle.PLAYER_ERROR) || DialogTitle.PLAYER_ERROR;
    const messageDialog =
      get(obj, "message", DialogMessage.PLAYER_ERROR) ||
      DialogMessage.PLAYER_ERROR;
    const buttonRetry = get(obj, "button_retry", "Thử lại") || "Thử lại";
    const buttonClose = get(obj, "button_close", "Đóng") || "Đóng";
    DialogCommon.commonMinimalDialog({
      keyName: "dialog_minimal_show_dialog_error",
      type: "success",
      title: titleDialog,
      description: messageDialog,
      image: DialogImg.PLAYER_ERROR,
      imagePosition: "bottom",
      maskBackground: "#222222",
      actions: [
        {
          title: buttonRetry,
          onClick: () => {
            if (begin) {
              onPlayMovieBegin();
            } else {
              onPlayMovie();
            }
          }
        },
        {
          title: buttonClose,
          onClick: () => {
            handleVisibilityChange();
          }
        }
      ]
    });
  }

  function showDialogTrial() {
    if (isLogged) {
      const id =
        dataVideoPlayer.type === 6
          ? idVOD
          : dataVideoPlayer.type === 4
          ? dataVideoPlayer.id
          : dataVideoPlayer.group_id;
      const pkgId = get(dataVideoPlayer, ["packages", 0, "id"]);
      const userType = get(dataVideoPlayer, ["packages", 0, "name"], "");
      if (
        dataVideoPlayer?.is_premium === TYPE_OF_CONTENT.TVOD_AND_SVOD &&
        !!dataVideoPlayer?.is_premium_display &&
        !isKid
      ) {
        showDialogTriggerPayment(
          "svod_trial",
          dataVideoPlayer?.is_premium === TYPE_OF_CONTENT.TVOD_AND_SVOD
            ? DialogType.AnonymousVipSvodTvodDialog
            : isGlobal
            ? computeGlobalTrialDialogType(dataVideoPlayer.type, false)
            : DialogType.AnonymousUserFreeTrialDialog,
          dataVideoPlayer,
          dataVideoIntro,
          false,
          true,
          pkgId
        );
      } else {
        showDialogPayment(
          "svod_trial",
          dataVideoPlayer?.is_premium === TYPE_OF_CONTENT.TVOD_AND_SVOD &&
            !!dataVideoPlayer?.is_premium_display &&
            !isKid
            ? DialogType.UserVipSvodTvodDialog
            : isGlobal
            ? computeGlobalTrialDialogType(dataVideoPlayer.type, true)
            : DialogType.NonVipUserFreeTrialDialog,
          id,
          dataVideoPlayer.type,
          false,
          true,
          pkgId,
          userType
        );
      }
    } else {
      const pkgId = get(dataVideoPlayer, ["packages", 0, "id"]);
      showDialogTriggerPayment(
        "svod_trial",
        dataVideoPlayer?.is_premium === TYPE_OF_CONTENT.TVOD_AND_SVOD &&
          !!dataVideoPlayer?.is_premium_display &&
          !isKid
          ? DialogType.AnonymousVipSvodTvodDialog
          : isGlobal
          ? computeGlobalTrialDialogType(dataVideoPlayer.type, false)
          : DialogType.AnonymousUserFreeTrialDialog,
        dataVideoPlayer,
        dataVideoIntro,
        false,
        true,
        pkgId
      );
    }
  }

  // Limit Device
  function showDialogLimitDevice(begin: boolean, content: DataVideoPlayer) {
    DialogV2.limitDevices({
      onOptionOne: () => {
        history.push({
          pathname: ROUTES.SETTING_DEVICE_MANAGER
        });
      },
      onOptionTwo: () => {
        if (begin) {
          onPlayMovieBegin();
        } else {
          onPlayMovie();
        }
      },
      onOptionThree: () => {
        handleVisibilityChange();
        showDialogChangePassword();
      },
      onReturn: () => {
        handleVisibilityChange();
      },
      devices: content.devices
    });
  }

  function showDialogChangePassword() {
    if (profile && profile.mobile && profile.mobile !== "") {
      DialogV2.changePassword({
        onOk: () => {
          setTimeout(() => {
            showDialogChangePasswordSuccess();
          }, 1000);
        },
        onCancel: () => {}
      });
    } else {
      DialogV2.cannotResetPassword({
        onOk: () => {}
      });
    }
  }

  function showDialogChangePasswordSuccess() {
    DialogV2.resetPasswordSuccess({
      onOk: () => {
        history.push({
          pathname: ROUTES.SETTING_DEVICE_MANAGER
        });
      },
      onCancel: () => {}
    });
  }

  // Player
  function setPlayerVideoEl(el: HTMLVideoElement | null) {
    setVideoEL(el);
    if (playAutoStatus.current === PLAY_AUTO_STATUS.PLAY_MOVIE && el) {
      const setCanplay = (e: Event) => {
        el.removeEventListener("canplay", setCanplay);
      };
      el.addEventListener("canplay", setCanplay);
    }
  }

  function handleVisibilityChange() {
    if (
      (!playTrailer.current ||
        (playTrailer.current && idVOD === dataVideoIntro.id)) &&
      videoEl !== null
    ) {
      const progress = Math.round(videoEl.currentTime);
      const contentDT = dataVideoIntro;
      contentDT.progress = progress;
      if (dataVideoPlayerRef.current.type === VODType.EPISODE) {
        contentDT.idEps = dataVideoPlayerRef.current.id;
        contentDT.titleEps = dataVideoPlayerRef.current.titleEpi;
      }
      setDataVideoIntro(contentDT);
    }
    playerRef.current = null;
    setVideoEL(null);
    setShowPlayer(false);
    setVideoIntroType(Type.Intro);
    setIsShowLoading(false);
    setIsLoaded(true);
    ViePlayer.showViePlayerImage(true);
  }

  function endPlayer() {
    playerRef.current = null;
    setVideoEL(null);
    setShowPlayer(false);
    setVideoIntroType(Type.Intro);
    setIsShowLoading(false);
    setIsLoaded(true);
    ViePlayer.showViePlayerImage(true);
  }

  function refreshData() {
    apiVideoIntro.getDataVideoIntro(
      dataVideoIntro.id,
      isGlobal,
      (success, isWatchlater, content, episodesList) => {
        if (success && content) {
          const data = content;
          data.listRelated = dataVideoIntro.listRelated;
          data.listRecommended = dataVideoIntro.listRecommended;
          setDataVideoIntro(data);
          if (episodesList) {
            setEpisodesList(episodesList);
          }
          setIsWatchlater(isWatchlater);
        }
        // setVideoIntroType(Type.Null);
        // setVideoIntroType(Type.Intro);
      }
    );
  }

  function handleOnEnded(skipCheckTrial: boolean = true) {
    if (skipCheckTrial) {
      if (dataVideoPlayer.trial) {
        showDialogTrial();
        return;
      }
    }
    setShowPlayer(false);
    const videoPlayerType = get(dataVideoPlayerRef, ["current", "type"], -1);
    playTrailer.current = false;
    if (videoPlayerType === 6) {
      // setShowPlayer(false);
      // setVideoIntroType(Type.Intro);
      endPlayer();
      playTrailer.current = true;
      return;
    }
    const currentEpisodesList = get(episodesListRef, ["current"], []);
    if (currentEpisodesList && currentEpisodesList.length > 0) {
      const total = currentEpisodesList.length;
      const currentIndex = checkCurrentIndexEpisode();
      if (currentIndex + 1 === total) {
        showEndScreenSuggestion();
      } else {
        nextVideo(currentIndex + 1);
      }
    } else {
      showEndScreenSuggestion();
    }
  }

  function nextVideo(index: number) {
    setIsShowLoading(true);
    setShowPlayer(false);
    setVideoIntroType(Type.Null);
    setDelayShowDialogTriggerLogin(false);
    setIsShowAdsInfoBox(false);
    const currentEpisode = get(episodesListRef, ["current", index], []);
    const id = get(currentEpisode, ["id"], "");
    const groupID = get(currentEpisode, ["group_id"], "");
    apiVideoIntro.getDataVideoPlayer(
      groupID,
      id,
      (success, dataVideoPlayer, error) => {
        if (success) {
          const contentDT = { ...dataVideoIntro };
          if (contentDT && dataVideoPlayer.type !== 6) {
            checkStreamProfile(dataVideoPlayer.streamProfile);
            contentDT.id = dataVideoPlayer.group_id;
            contentDT.idEps = dataVideoPlayer.id;
            contentDT.title = dataVideoPlayer.title;
            contentDT.images = dataVideoPlayer.images;
            contentDT.titleEps = dataVideoPlayer.titleEpi;
            contentDT.progress = dataVideoPlayer.progress;
            contentDT.runtime = dataVideoPlayer.runtime;
            contentDT.audios = dataVideoPlayer.audios;
            contentDT.subtitles = dataVideoPlayer.subtitles;
            contentDT.desc = dataVideoPlayer.desc;
            setDataVideoIntro(contentDT);
          }
          setDataVideoPlayer(dataVideoPlayer);
          if (checkPermissionPlaylist(false, dataVideoPlayer, true)) {
            setShowPlayer(true);
            setVideoIntroType(Type.ControllerPlayer);
          }
        } else {
          showDialogError(false, error);
        }
        setIsShowLoading(false);
      }
    );
  }

  function showEndScreenSuggestion() {
    if (dataVideoIntro.listRecommended.length > 0) {
      setVideoIntroType(Type.Null);
      setIsShowEndScreenSuggestion(true);
    } else {
      endPlayer();
    }
    if (dataVideoPlayer.isDVR) {
      // some thing
    } else {
      setTimeout(() => {
        refreshData();
      }, 500);
    }
  }

  function onExitSuggestion() {
    setIsShowEndScreenSuggestion(false);
    endPlayer();
  }

  function onBackADS() {
    setShowPlayer(false);
    setVideoIntroType(Type.Intro);
  }

  function playAutoOnShowControl(playSuccess: boolean) {
    if (playSuccess) {
      if (playAutoStatus.current === PLAY_AUTO_STATUS.PLAY_MOVIE) {
        clearTimerAutoHideInfo();
        if (videoIntroInInfo.current) {
          timerAutoHideInfo.current = setTimeout(() => {
            playAutoStatus.current = PLAY_AUTO_STATUS.PLAY_MOVIE_SUCCESS;
            setVideoIntroType(Type.Null);
            setVideoIntroType(Type.ControllerPlayer);
            clearTimerAutoHideInfo();
          }, TIME_AUTO_HIDE_INFO);
        } else {
          setTimeout(() => {
            playerControllerProxy("pause");
          }, 500);
        }
      }
    } else {
      setShowPlayer(false);
      playAutoStatus.current = PLAY_AUTO_STATUS.PLAY_MOVIE_SUCCESS;
    }
  }

  // control player
  function playerControllerProxy(
    message: string,
    options?: { [key: string]: any }
  ) {
    if (message === "exitPlayer") {
      playTrailer.current = false;
      const isDisable = options?.isDisable;
      if (isDisable) {
        playTrailer.current = true;
        handleVisibilityChange();
        setVideoIntroType(Type.Intro);
      } else {
        const contentDT = dataVideoIntro;
        if (videoEl !== null) {
          // Remove state from URL when exit player, to prevent loop infinite when back from authentication page
          const params = new URLSearchParams(location.search);
          params.delete("state");
          history.replace({
            search: params.toString()
          });

          const progress = Math.round(videoEl.currentTime);
          contentDT.progress = progress;
          if (dataVideoPlayer.type === VODType.EPISODE) {
            contentDT.idEps = dataVideoPlayer.id;
            contentDT.titleEps = dataVideoPlayer.titleEpi;
            contentDT.desc = dataVideoPlayer.desc;
          }
          if (contentDT.isPremium === TYPE_OF_CONTENT.TVOD) {
            const typeOfId = TYPE_OF_ID[contentDT.type];
            apiVideoIntro.getDetailsOfTvodContent(
              contentDT.id,
              typeOfId,
              {},
              (success, result) => {
                if (success && result) {
                  contentDT.tvodDetails = new TvodDetails(result);
                }
                const isContentBought: boolean =
                  contentDT.tvodDetails.tvodBenefitType > 0;
                if (isContentBought) {
                  setDataVideoIntro(contentDT);
                } else {
                  handleVisibilityChange();
                }
                setVideoIntroType(Type.Intro);
              }
            );
          } else {
            setDataVideoIntro(contentDT);
            setVideoIntroType(Type.Intro);
          }
        } else {
          setVideoIntroType(Type.Intro);
        }
      }
    } else if (
      playerRef.current &&
      playerRef.current.controllerMessageHandler &&
      typeof playerRef.current.controllerMessageHandler === "function"
    ) {
      if (message === "changeAudio") {
        const index = options?.index;
        if (checkPermissionAudio(index, true)) {
          playerRef.current.controllerMessageHandler(message, options);
        }
      } else if (message === "changeSubtitle") {
        const index = options?.index;
        if (checkPermissonSubtitle(index, true)) {
          playerRef.current.controllerMessageHandler(message, options);
        }
      } else if (message === "changeProfile") {
        const index = options?.index;
        if (checkPermissonStreamprofile(index)) {
          playerRef.current.controllerMessageHandler(message, options);
        }
      } else if (message === "playbackRate") {
        const playbackRate = options?.playbackRate;
        setSelectPlaybackRate(playbackRate ?? 1.0);
        playerRef.current.controllerMessageHandler(message, options);
      } else if (message === "skipOuttro") {
        if (dataVideoPlayer.trial) {
          handleOnEnded(false);
        } else {
          playerRef.current.controllerMessageHandler(message, options);
        }
      } else if (message === "play") {
        // const ended = get(options, "ended", false);
        const ended = checkEndPlayerTrial();
        if (ended) {
          playerRef.current.controllerMessageHandler("seek", {
            seekTo: 1,
            seekToPoint: true
          });
          playerRef.current.controllerMessageHandler("play");
        } else {
          playerRef.current.controllerMessageHandler(message, options);
        }
      } else {
        playerRef.current.controllerMessageHandler(message, options);
      }
    }
  }

  function checkEndPlayerTrial() {
    if (videoEl) {
      const duration = Math.round(videoEl.duration);
      const time = Math.round(videoEl.currentTime);
      const ended = duration - time < 2;
      if (ended && dataVideoPlayer.trial) {
        return true;
      }
    }
    return false;
  }

  // Intro
  function onReturnIntro() {
    ViePlayer.destroy();
    ViePlayer.destroyViePlayerImage();
    history.goBack();
  }

  function actionIntroKeyEnter() {
    videoIntroInInfo.current = false;
    if (playAutoStatus.current === PLAY_AUTO_STATUS.PLAY_REVIEW) {
      ViePlayer.pause();
    } else if (playAutoStatus.current === PLAY_AUTO_STATUS.PLAY_MOVIE) {
      clearTimerAutoHideInfo();
      clearTimerAutoPlay();
      playerControllerProxy("pause");
    } else if (playAutoStatus.current === PLAY_AUTO_STATUS.NULL) {
      clearTimerAutoHideInfo();
      clearTimerAutoPlay();
    }
  }

  function actionIntroKeyUpDown() {
    videoIntroInInfo.current = true;
    if (playAutoStatus.current === PLAY_AUTO_STATUS.PLAY_REVIEW) {
      ViePlayer.play();
    } else if (playAutoStatus.current === PLAY_AUTO_STATUS.PLAY_MOVIE) {
      clearTimerAutoHideInfo();
      timerAutoHideInfo.current = setTimeout(() => {
        playAutoStatus.current = PLAY_AUTO_STATUS.PLAY_MOVIE_SUCCESS;
        setVideoIntroType(Type.Null);
        setVideoIntroType(Type.ControllerPlayer);
        clearTimerAutoHideInfo();
      }, TIME_AUTO_HIDE_INFO);
      playerControllerProxy("play");
    } else if (playAutoStatus.current === PLAY_AUTO_STATUS.NULL) {
      checkPlayMovieAutoPlay(dataVideoIntroRef.current!);
    }
  }

  function playMovieWidthStatePlayer(id: string, groupID?: string) {
    apiVideoIntro.getDataVideoPlayer(
      groupID || "",
      id,
      (success, dataVideoPlayer, error) => {
        if (success) {
          checkStreamProfile(dataVideoPlayer.streamProfile);
          const contentDT = dataVideoIntroRef.current;
          if (contentDT) {
            contentDT.idEps = dataVideoPlayer.id;
            contentDT.titleEps = dataVideoPlayer.titleEpi;
            contentDT.progress = dataVideoPlayer.progress;
            contentDT.runtime = dataVideoPlayer.runtime;
            contentDT.audios = dataVideoPlayer.audios;
            contentDT.subtitles = dataVideoPlayer.subtitles;
            contentDT.desc = dataVideoPlayer.desc;
            setDataVideoIntro(contentDT);
          }
          if (checkPermissionPlaylist(false, dataVideoPlayer)) {
            setDataVideoPlayer(dataVideoPlayer);
            setVideoIntroType(Type.ControllerPlayer);
            setShowPlayer(true);
            setIsShowLoading(false);
            setIsLoaded(true);
          }
        } else {
          // setVideoIntroType(Type.Intro);
          showDialogError(false, error);
        }
      }
    );
  }

  function checkPlayMovieAutoPlay(content: DataVideoIntro) {
    if (content) {
      const { type } = content;
      if (type === VODType.SEASON || type === VODType.SHOW) {
        const id = content.idEps;
        const groupID = content.id;
        playMovieAuto(id, groupID);
      } else {
        const { id } = content;
        playMovieAuto(id); // Get episode list logic
      }
    }
  }

  function playMovieAuto(id: string, groupID?: string) {
    const isTvodContent =
      dataVideoIntroRef.current.isPremium === TYPE_OF_CONTENT.TVOD;
    if (isTvodContent) {
      playReview();
      return;
    }
    apiVideoIntro.getDataVideoPlayer(
      groupID || "",
      id,
      (success, dataVideoPlayer, error) => {
        if (success) {
          checkStreamProfile(dataVideoPlayer.streamProfile);
          if (
            dataVideoPlayer.permission &&
            dataVideoPlayer.permission === PermissionPlayerType.CAN_PLAY
          ) {
            if (
              // dataVideoPlayer &&
              // dataVideoPlayer.ads &&
              // Object.keys(dataVideoPlayer.ads).length > 0

              dataVideoPlayer.adsPreroll ||
              dataVideoPlayer.adsMidroll
            ) {
              playReview();
              return;
            }
            if (
              dataVideoPlayer.age_restricted &&
              dataVideoPlayer.progress === 0
            ) {
              playReview();
              return;
            }
            setDataVideoPlayer(dataVideoPlayer);
            clearTimerAutoPlay();
            timerAutoPlay.current = setTimeout(() => {
              setShowPlayer(true);
              playAutoStatus.current = PLAY_AUTO_STATUS.PLAY_MOVIE;
              clearTimerAutoPlay();
            }, TIME_AUTO_PLAY);
          } else {
            playReview();
          }
        }
      }
    );
  }

  function playMovie(begin: boolean, id: string, groupID?: string) {
    if (!isOnline && !showPlayer) {
      pushNoNetworkMessage();
      return;
    }
    clearTimerAutoPlay();
    clearTimerAutoHideInfo();
    playAutoStatus.current = PLAY_AUTO_STATUS.PLAY_MOVIE_SUCCESS;
    if (
      showPlayerRef.current &&
      dataVideoPlayerRef.current &&
      dataVideoPlayerRef.current.id === id
    ) {
      if (
        playerRef.current &&
        playerRef.current.controllerMessageHandler &&
        typeof playerRef.current.controllerMessageHandler === "function"
      ) {
        if (begin) {
          playerRef.current.controllerMessageHandler("seek", {
            seekTo: 0,
            seekToPoint: true
          });
        }
        playerRef.current.controllerMessageHandler("play");
      } else {
        if (begin) {
          const data = dataVideoPlayerRef.current;
          data.progress = 0;
          setDataVideoPlayer(data);
        }
        setShowPlayer(true);
      }
      setVideoIntroType(Type.ControllerPlayer);
      /* Fix VFE-5769 */
      if (afterPlayToastTextRef.current) {
        Message.open(afterPlayToastTextRef.current);
        afterPlayToastTextRef.current = "";
      }
      /* End Fix VFE-5769 */
      return;
    }
    ViePlayer.destroy();
    setVideoIntroType(Type.Null);
    ViePlayer.showViePlayerImage(true);
    setIsShowLoading(true);
    setShowPlayer(false);
    apiVideoIntro.getDataVideoPlayer(
      groupID || "",
      id,
      (success, dataVideoPlayer, error) => {
        setIsShowLoading(false);
        if (success) {
          checkStreamProfile(dataVideoPlayer.streamProfile);
          const contentDT = dataVideoIntro;
          if (contentDT && dataVideoPlayer.type !== 6) {
            contentDT.idEps = dataVideoPlayer.id;
            contentDT.titleEps = dataVideoPlayer.titleEpi;
            contentDT.progress = dataVideoPlayer.progress;
            contentDT.runtime = dataVideoPlayer.runtime;
            contentDT.audios = dataVideoPlayer.audios;
            contentDT.subtitles = dataVideoPlayer.subtitles;
            contentDT.desc = dataVideoPlayer.desc;
            setDataVideoIntro(contentDT);
          }
          if (begin) {
            dataVideoPlayer.progress = 0;
          }
          setDataVideoPlayer(dataVideoPlayer);
          if (checkPermissionPlaylist(begin, dataVideoPlayer)) {
            setShowPlayer(false);
            setTimeout(() => {
              setVideoIntroType(Type.ControllerPlayer);
              setShowPlayer(true);
              /* Fix VFE-5769 */
              if (afterPlayToastTextRef.current) {
                Message.open(afterPlayToastTextRef.current);
                afterPlayToastTextRef.current = "";
              }
              /* End Fix VFE-5769 */
            }, 50);
          }
        } else {
          showDialogError(begin, error);
        }
      }
    );
  }

  function onPlayMovie(
    {
      isTriggerFastTrack,
      slug,
      isRemoveReturnBtn
    }: {
      isTriggerFastTrack: boolean;
      slug?: string;
      isRemoveReturnBtn?: boolean;
    } = { isTriggerFastTrack: false, slug: "", isRemoveReturnBtn: false }
  ) {
    if (dataVideoIntro) {
      if (isTriggerFastTrack) {
        if (currentProfile?.isKid) {
          // TODO KID FLOW
          const action = () => {
            handleVisibilityChange();
          };
          showDialogFastTrackKid({
            onOk: action,
            onReturn: action
          });
        } else {
          goToPaymentPvod({
            id: dataVideoIntroRef.current.id,
            contentDT: {
              id: dataVideoIntroRef.current.id,
              type: dataVideoIntroRef.current.type,
              group_id: ""
            } as DataVideoPlayer,
            replacePlayer: false,
            slugPage: slug,
            isRemoveReturnBtn
          });
        }
        return;
      }
      const { type } = dataVideoIntro;
      if (type === VODType.SEASON || type === VODType.SHOW) {
        const id = dataVideoIntro.idEps;
        const groupID = dataVideoIntro.id;
        playMovie(false, id, groupID);
      } else {
        const { id } = dataVideoIntro;
        playMovie(false, id);
      }
    }
  }

  function onPlayMovieBegin() {
    if (dataVideoIntro) {
      const { type } = dataVideoIntro;
      if (type === VODType.SEASON || type === VODType.SHOW) {
        const id = dataVideoIntro.idEps;
        const groupID = dataVideoIntro.id;
        playMovie(true, id, groupID);
      } else {
        const { id } = dataVideoIntro;
        playMovie(true, id);
      }
    }
  }

  function onSelectEpisode(index: number) {
    const currentEpisodesList: any[] = get(episodesListRef, "current", []);
    if (currentEpisodesList && index < currentEpisodesList.length) {
      // const episode = get(currentEpisodesList, [index], {});
      // const id = get(episode, ["id"], "");
      // const groupID = get(episode, ["group_id"], "");
      // playMovie(false, id, groupID);
      nextVideo(index);
    }
  }

  function onSelectRelated(index: number) {
    if (
      dataVideoIntro &&
      dataVideoIntro.listRelated &&
      index < dataVideoIntro.listRelated.length
    ) {
      const item = dataVideoIntro.listRelated[index];
      const id = get(item, "id", "") || "";
      playMovie(false, id);
    }
  }

  function onSelectRecomended(index: number) {
    setIsLoaded(false);
    setIsShowLoading(true);
    setVideoIntroType(Type.Intro);
    setIsShowEndScreenSuggestion(false);
    setIsWatchlater(false);
    playAutoStatus.current = PLAY_AUTO_STATUS.NULL;
    playerRef.current = null;
    setVideoEL(null);
    setShowPlayer(false);
    clearTimerAutoHideInfo();
    clearTimerAutoPlay();
    ViePlayer.destroy();
    videoIntroInInfo.current = true;
    setDataVideoIntro({} as DataVideoIntro);
    setDataVideoPlayer({} as DataVideoPlayer);
  }

  function onSelectSubName(name: string) {
    const index = dataVideoIntro.subtitles.findIndex(
      (sub: Subtitle) => sub.code_name === name
    );
    keepDialog.current = `sub-${index}`;
    checkPermissonSubtitle(index);
  }

  function onSelectAudioName(name: string) {
    const index = dataVideoIntro.audios.findIndex(
      (audio: Audio) => audio.code_name === name
    );
    keepDialog.current = `audio-${index}`;
    checkPermissionAudio(index);
  }

  function onSelectFavorite() {
    if (isLogged) {
      // TODO need confirm when task done
      // https://vieon.atlassian.net/browse/VBE-3612
      if (currentProfile?.isKid && !dataVideoIntroRef.current?.allows_kid) {
        const action = () => {
          history.replace(ROUTES.ROOT);
        };
        const showBackButton = true;
        showDialogContentForKidNotAccess([action], action, showBackButton);
      } else {
        apiVideoIntro.addFavorite(dataVideoIntro.id, (success) => {
          if (success) {
            setIsWatchlater(!isWatchlaterRef.current);
          }
        });
      }
    } else {
      actionIntroKeyEnter();
      // const genAction = (pathname: string) => () => {
      const pathname = ROUTES.LOGIN_STEP;
      const searchParams = new URLSearchParams();
      searchParams.append("redirect", `${ROUTES.VIDEO_INTRO}/${idVOD}`);
      const urlParams = new URLSearchParams(location.search);
      const recommended = urlParams.get("recommended");
      if (recommended) {
        searchParams.append("recommended", recommended);
      }
      history.push({
        pathname,
        search: searchParams.toString(),
        state: {
          title: EnumAuthTitle.RegisterByAddToList,
          authenFlowName: AuthenFlowName.RegistrationFeature,
          authenFeatureName: AuthenFeatureName.AddToList
        }
      });

      // SegmentManager.segmentAction(
      //   pathname === ROUTES.LOGIN_PAGE_BY_PHONE
      //     ? "login_button_selected"
      //     : "registration_button_selected",
      //   {
      //     current_page: "VOD Player",
      //     popup_name: "Thêm VOD vào danh sách của tôi",
      //   }
      // );
      // };
      // const onBack = () => {
      //   actionIntroKeyUpDown();
      // };
      // dialogRoutines.showDialogAnonymousRequestLoginByType(
      //   DialogType.AnonymousUserRequireLoginAddFavorite,
      //   [genAction(ROUTES.LOGIN_PAGE_BY_PHONE), genAction(ROUTES.SIGNUP_PAGE)],
      //   onBack
      // );
    }
  }

  const changeRoute = (url: string, isRedirect?: boolean) => {
    if (isRedirect) {
      const searchParams = new URLSearchParams();
      searchParams.append("redirect", `${ROUTES.VIDEO_INTRO}/${idVOD}`);
      history.push({
        pathname: url,
        search: searchParams.toString()
      });
    } else {
      history.push(url);
    }
  };

  function viePlayerUpdateTime() {
    if (videoIntroInInfo.current) {
      ViePlayer.play();
    } else {
      ViePlayer.pause();
    }
  }

  function viePlayerStart() {
    if (videoIntroInInfo.current) {
      ViePlayer.play();
    } else {
      ViePlayer.pause();
    }
  }

  function viePlayerEnd() {
    ViePlayer.destroy();
    playAutoStatus.current = PLAY_AUTO_STATUS.PLAY_REVIEW_SUCCESS;
    checkPlayMovieAutoPlay(dataVideoIntroRef.current);
  }

  function viePlayerError() {
    ViePlayer.destroy();
    playAutoStatus.current = PLAY_AUTO_STATUS.PLAY_REVIEW_SUCCESS;
    checkPlayMovieAutoPlay(dataVideoIntroRef.current);
  }

  useEventPlayer({
    player: ViePlayer,
    events: {
      [ViePlayerEventType.PLAYER_START]: viePlayerStart,
      [ViePlayerEventType.PLAYER_END]: viePlayerEnd,
      [ViePlayerEventType.PLAYER_ERROR]: viePlayerError,
      [ViePlayerEventType.PLAYER_TIME_UPDATE]: viePlayerUpdateTime
    }
  });

  const [keepAliveData, setKeepAliveData] = useState(() => {
    return KeepAlive.getData(`${ROUTES.VIDEO_INTRO}/${idVOD}`);
  });

  const forceLoginActions = useCallback(
    ({ isEndScreen }: ForceLoginActionParams) => {
      const contentId =
        dataVideoPlayer.type === 6
          ? dataVideoIntro.id
          : dataVideoPlayer.type === 4
          ? dataVideoPlayer.id
          : dataVideoPlayer.group_id;
      const playerTime = keepAliveData?.extra?.playerTime;
      const searchParams = new URLSearchParams({
        state: "player",
        redirect: `${ROUTES.VIDEO_INTRO}/${contentId}`
      });

      const _keepAliveSavedData = {
        path: `${ROUTES.VIDEO_INTRO}/${contentId}`,
        focus: { x: 0, y: 0 },
        extra: {
          playerTime:
            isEndScreen || !videoEl?.currentTime
              ? playerTime
              : Math.floor(videoEl?.currentTime),
          state: "player"
        }
      };

      const videoType =
        VIDEO_TYPE[forceSeasonVideoTypeToEpisode(dataVideoIntro.type)] || "";

      return {
        gotoPayment: () => {
          SegmentManager.segmentAction(
            LocalFeatureEventNames.PaymentButtonSelected,
            {
              [LocalFeaturePropertyKeys.CurrentPage]:
                currentPageRef.current?.name,
              [LocalFeaturePropertyKeys.FlowName]:
                LocalPropertyValues.ForceLogin,
              [LocalFeaturePropertyKeys.ContentID]:
                dataVideoIntro.idEps || dataVideoIntro.id || "",
              [LocalFeaturePropertyKeys.ContentType]: videoType
            }
          );
          KeepAlive.saveData(_keepAliveSavedData);
          history.push({
            pathname: ROUTES.PAYMENT,
            search: `${searchParams.toString()}`
          });
        },
        gotoLogin: () => {
          SegmentManager.segmentAction(
            LocalFeatureEventNames.LoginButtonSelected,
            {
              [LocalFeaturePropertyKeys.FlowName]: isEndScreen
                ? LocalPropertyValues.RegistrationForVOD
                : LocalPropertyValues.RegistrationTrial,
              [LocalFeaturePropertyKeys.TriggerFrom]: isEndScreen
                ? LocalPropertyValues.ForceLoginNotification
                : LocalPropertyValues.ForceLoginButton,
              [LocalFeaturePropertyKeys.ContentID]:
                dataVideoIntro.idEps || dataVideoIntro.id || "",
              [LocalFeaturePropertyKeys.ContentType]: videoType
            }
          );
          KeepAlive.saveData(_keepAliveSavedData);
          history.replace({
            pathname: `${ROUTES.VIDEO_INTRO}/${contentId}`,
            search: `?state=player`
          });
          history.push({
            pathname: ROUTES.LOGIN_STEP,
            search: searchParams.toString(),
            state: {
              title: EnumAuthTitle.RegisterByRegistrationTrial,
              isUseTrackLocation: false,
              isBack: true,
              authenFlowName: AuthenFlowName.RegistrationTrigger
            }
          });
        }
      };
    },
    [dataVideoIntro, dataVideoPlayer, query]
  );

  const closeForceLoginEndScreen = useCallback(() => {
    // clear params
    history.replace({
      pathname: `${ROUTES.VIDEO_INTRO}/${idVOD}`
    });
    setKeepAliveData(null);
    endPlayer();
  }, []);

  const isShowForceLoginEndScreen = useMemo(() => {
    const playerTime = keepAliveData?.extra?.playerTime || -1;
    const isForceLogin =
      playerTime >= dataVideoPlayer.triggerLoginDuration && !isAuthen;
    if (isForceLogin && playerRef.current) {
      // pause player when show force login screen
      playerRef.current.controllerMessageHandler("pause");
      VieOnNavigation.setFocus("LoginButton");
    }
    return isForceLogin;
  }, [dataVideoPlayer, isAuthen, keepAliveData]);

  useEffect(() => {
    // for flow force login only to keep player state when back from authen (success / turn back from authen page)
    if (keepAliveData?.extra?.state === "player") {
      history.replace({
        pathname: `${ROUTES.VIDEO_INTRO}/${idVOD}`,
        search: `?state=player`
      });
    }
  }, [keepAliveData]);

  return (
    <>
      <RecommendSVODDialog
        dialogData={recommendSVODDialogData}
        visible={!!recommendSVODDialogData}
        onClose={(visible: boolean) => {
          if (!visible) {
            setRecommendSVODDialogData(null);
            endPlayer();
          }
        }}
      />
      {isLoaded ? (
        <>
          <div
            className="page-content player"
            style={{ display: showPlayer ? "block" : "none" }}
          >
            <div className="player-wrapper">
              {showPlayer ? (
                <VideoPlayer
                  canProcessTracking={canProcessTracking}
                  isKid={isKid}
                  isAuthen={isAuthen}
                  isNetwork={{ isOnline, notificationType }}
                  setVideoEl={setPlayerVideoEl}
                  lastIsPaused={lastIsPaused}
                  dataVideoIntro={dataVideoIntro}
                  episodeList={episodesList}
                  dataVideoPlayer={dataVideoPlayer}
                  tvodDetails={dataVideoIntroRef.current.tvodDetails}
                  activedSubtitle={selectSubName}
                  activedAudio={selectAudioName}
                  activeStreamProfile={selectStreamProfile}
                  selectPlaybackRate={selectPlaybackRate}
                  profile={profile}
                  token={token}
                  isLogged={isLogged}
                  playBackground={videoIntroType !== Type.ControllerPlayer}
                  idVOD={idVOD}
                  genre={dataVideoIntro.genre}
                  isTVOD={
                    dataVideoIntroRef.current.isPremium === TYPE_OF_CONTENT.TVOD
                  }
                  ref={(el) => (playerRef.current = el)}
                  handleVisibilityChange={handleVisibilityChange}
                  handleOnEnded={handleOnEnded}
                  onBackADS={onBackADS}
                  playAutoOnShowControl={playAutoOnShowControl}
                  changeRoute={changeRoute}
                  showWarning={(isShowWarning: boolean) => {
                    setIsShowWarning(isShowWarning);
                  }}
                  handleShowDialogTriggerLogin={() => {
                    nextPageLogin(undefined, "registration_trial");
                  }}
                  setShowInStreamAds={setShowInStreamAds}
                  setIsShowAdsInfoBox={setIsShowAdsInfoBox}
                  isShowController={isShowController}
                  isShowForceLoginScreen={isShowForceLoginEndScreen}
                  delayShowDialogTriggerLogin={delayShowDialogTriggerLogin}
                  setDelayShowDialogTriggerLogin={
                    setDelayShowDialogTriggerLogin
                  }
                  keepAliveData={keepAliveData}
                />
              ) : null}
              {videoIntroType === Type.ControllerPlayer &&
              isShowWarning === false ? (
                <ControllerPlayer
                  isLogged
                  lastIsPaused={lastIsPaused}
                  setLastIsPaused={setLastIsPaused}
                  isDVR={dataVideoPlayer.isDVR}
                  profile={profile}
                  dataVideoPlayer={dataVideoPlayer}
                  dataVideoIntro={dataVideoIntro}
                  activedSubtitle={selectSubName}
                  activedAudio={selectAudioName}
                  activeStreamProfile={selectStreamProfile}
                  episodeList={episodesList}
                  videoEl={videoEl}
                  isShowWarning={isShowWarning}
                  dispatchMessage={playerControllerProxy}
                  setIsShowController={setIsShowController}
                  showInStreamAds={showInStreamAds}
                  showEndScreenSuggestion={isShowEndScreenSuggestion}
                  forceLoginActions={forceLoginActions}
                  isShowAdsToast={isShowAdsInfoBox}
                  delayShowDialogTriggerLogin={delayShowDialogTriggerLogin}
                  isShowForceLoginScreen={isShowForceLoginEndScreen}
                />
              ) : null}
            </div>
          </div>
          {videoIntroType === Type.Intro ? (
            <Intro
              selectSubName={selectSubName}
              selectAudioName={selectAudioName}
              dataVideoIntro={dataVideoIntro}
              episodesList={episodesList}
              isWatchlater={isWatchlater}
              dataVideoPlayer={dataVideoPlayer}
              actionIntro={actionIntroKeyEnter}
              actionInfor={actionIntroKeyUpDown}
              onReturnIntro={onReturnIntro}
              onPlayMovie={onPlayMovie}
              onPlayMovieBegin={onPlayMovieBegin}
              onSelectEpisode={onSelectEpisode}
              onSelectRelated={onSelectRelated}
              onSelectRecommended={onSelectRecomended}
              onSelectSubName={onSelectSubName}
              onSelectAudioName={onSelectAudioName}
              onSelectFavorite={onSelectFavorite}
              updateDataVideoIntro={setDataVideoIntro}
            />
          ) : null}
          {isShowForceLoginEndScreen && (
            <EndScreenForceLogin
              onExitSuggestion={closeForceLoginEndScreen}
              forceLoginActions={forceLoginActions}
              vodData={{
                type: dataVideoIntro.type,
                id: dataVideoIntro.idEps || dataVideoIntro.id,
                title: dataVideoIntro.titleEps || dataVideoIntro.title,
                backdropImage: dataVideoIntro?.images?.carousel_tv_v4
              }}
            />
          )}
          {isShowEndScreenSuggestion && !isShowForceLoginEndScreen ? (
            <EndscreenSuggestion
              listRecommended={dataVideoIntro.listRecommended}
              onExitSuggestion={onExitSuggestion}
              onSelectRecommended={onSelectRecomended}
              slotId={
                dataVideoPlayer.adsEndscreen?.listUrl?.[`${SLOT_NAME}1`]?.[0]
                  ?.url || ""
              }
            />
          ) : null}
        </>
      ) : null}
      {isShowLoading ? (
        <div
          style={{
            position: "absolute",
            left: 0,
            top: 0,
            width: "100%",
            height: "100%",
            zIndex: 2
          }}
        >
          <Loader />
        </div>
      ) : null}
    </>
  );
};

export default VideoIntro;
