import React, { useC<PERSON>back, useMemo, ReactElement } from "react";
import classNames from "classnames";
import { get, join } from "lodash";
import Tags from "app/components/Common/Tags";
import ProgressBar from "app/components/Common/ProgressBar";
import Img from "app/components/Common/Img";
import { convertMinutesToHHMM } from "app/utils/formatNumber";
import { makeMetadata } from "app/components/BillBoardLuna/functions";
import { TYPE_OF_CONTENT } from "types/endpoint";
import TvodTag, {
  makeTag,
  transformTvodTagItemStructure,
} from "app/components/Common/TvodTagLuna";
import {
  LiveTag,
  StartTimeTag,
  genTagPvodYellow,
  genTagPvodGreen,
  transformResponseDataStructure,
} from "app/components/Common/CommonTagsLuna";
import {
  FocusableComponentLayout,
  FocusDetails,
  useFocusable,
} from "core/KeyHandle";
import MotionFocusBox from "app/components/Motions/MotionFocusBox";
import useIsGlobal from "hooks/useIsGlobal";
import { RibbonType, VideoType } from "types/page";
import Tag, { TagType } from "app/components/Common/Tag/Tag";
import GroupTagBottom from "app/components/Common/Tag/GroupTagBottom";
import { makeRemainingTimeStr } from "app/utils/formatTime";
import TvodVipTag from "app/components/Common/Tag/TvodVipTag";
import { listId } from "./Recommended";

interface Props {
  hidden: boolean;
  index: number;
  item: any;
  focusKey: string;
  onEnter?: (props: object) => void;
  onFocus?: (
    layout: FocusableComponentLayout,
    props: object,
    details: FocusDetails
  ) => void;
  onReturn?: () => void;
  displayTVODTags?: boolean;
  displayFreeTag?: boolean;
  displayEarlyAccessTag?: boolean;
  isPremium?: number;
}

const CardIntro: React.FC<Props> = ({
  hidden,
  index,
  item,
  focusKey,
  onEnter,
  onFocus,
  onReturn,
  displayTVODTags = false,
  displayFreeTag = false,
  displayEarlyAccessTag = false,
  isPremium = 0,
}) => {
  const isGlobal = useIsGlobal();
  const makeTagTime = useCallback(() => {
    const typeContent = get(item, "type", 0) || 0;
    if (typeContent > 0) {
      if (typeContent === 2 || typeContent === 3) {
        const seasonNumber = (get(item, "related_season", []) || []).length;
        const category = get(item, "category", 1);
        let time = "";
        if (seasonNumber > 0) {
          if (category === 2) {
            time = `${seasonNumber} Mùa`;
          } else {
            time = `${seasonNumber} Phần`;
          }
        }
        return time;
      }
    }
    return "";
  }, [item]);

  const makeProgress = (progress: number, runtime: number) => {
    if (progress === 0) return 0;
    const runtimeSec = runtime * 60;
    return Math.ceil((progress * 100) / runtimeSec);
  };

  const makeCast = (casts: any[]) => {
    const cast = casts.map((item: any) => {
      return get(item, "name", "");
    });
    const strCast = join(cast, ", ");
    return strCast;
  };

  const type = get(item, "type", 0);
  const image = get(item, "images.thumbnail_v4", "");
  let progress = 0;
  if (type === 4) {
    progress = get(item, "progress", 0);
  }
  const runtime = get(item, "runtime", 1);
  const title = get(item, "title", "");
  const desc = get(item, "short_description", "");
  const groupId = get(item, "group_id", "");
  const premium = get(item, "is_premium", 0);
  const isPremiumDisplay = get(item, "is_premium_display", "");
  const isFree = get(item, "is_free", 0);
  const duration = convertMinutesToHHMM(runtime);
  progress = makeProgress(progress, runtime);
  const cast = makeCast(get(item, "people.cast", []) || []);

  const isTvodContent: boolean =
    get(item, "is_premium", -10) === TYPE_OF_CONTENT.TVOD;
  const liveTag = LiveTag({
    item: transformResponseDataStructure(item),
    isBorderRadius: true,
  });

  const { ref, focused } = useFocusable({
    focusKey,
    enterOnClick: true,
    onEnterPress: onEnter,
    onReturnPress: onReturn,
    focusImplicitOnHover: true,
    onFocus,
    extraProps: {
      index,
      item,
      focusKey,
    },
  });

  const renderTags = useCallback(
    (item: any) => {
      const runtime = get(item, "runtime", 0);
      const premium = get(item, "is_premium", 0);
      const isPremiumDisplay = get(item, "is_premium_display", "");
      const releaseYear = get(item, "release_year", 0);
      const ageRange = item?.age_range || "";
      const resolution = item?.resolution || 0;
      const type = item?.type || 0;
      const tagsInfo = get(item, "tags", []);
      let country = "";
      for (let i = 0; i < tagsInfo.length; i += 1) {
        if (get(tagsInfo[i], "type", "") === "country") {
          const name = get(tagsInfo[i], "name", "");
          country = name;
          break;
        }
      }
      const duration = convertMinutesToHHMM(runtime);
      const tagTime = makeTagTime();
      const tagLabel = get(item, "label_subtitle_audio", "") || "";
      const isTvodContent: boolean = item?.is_premium === TYPE_OF_CONTENT.TVOD;
      const isTvodSvodContent: boolean =
        item?.is_premium === TYPE_OF_CONTENT.TVOD_AND_SVOD &&
        !!isPremiumDisplay &&
        isFree === 0;

      const tags: ReactElement[] = [];

      let numberOfTag = 0;
      const tagPvodYellow = genTagPvodYellow({
        item: transformResponseDataStructure(item) as any,
        isGlobal,
        typeOfRibbon: RibbonType.SettingPage,
        style: { marginLeft: 0, marginRight: "0.5rem" },
      });
      const tagPvodGreen = genTagPvodGreen({
        item: transformResponseDataStructure(item) as any,
        isGlobal,
        typeOfRibbon: RibbonType.SettingPage,
        style: { marginLeft: 0, marginRight: "0.5rem" },
      });
      // Add premium tag
      if (premium === 1 && isPremiumDisplay && !isTvodContent) {
        tags.push(
          <Tag
            type={TagType.Vip}
            key={TagType.Vip}
            premiumDisplay={isPremiumDisplay}
            style={{ marginLeft: 0, marginRight: "0.5rem" }}
          />
        );
        numberOfTag += 1;
      }

      if (tagPvodYellow) {
        tags.push(tagPvodYellow);
        numberOfTag += 1;
      } else if (tagPvodGreen) {
        tags.push(tagPvodGreen);
        numberOfTag += 1;
      }
      // Add TVOD tag
      if (isTvodContent) {
        const tvodTag = makeTag({
          item: transformResponseDataStructure(item) as any,
          isTimeRemainingShown: false,
          isForRibbon: false,
          style: { marginLeft: 0, marginRight: "0.5rem" },
        });
        if (tvodTag) {
          tags.push(tvodTag);
        } else {
          numberOfTag += 1;
        }
      }
      if (isTvodSvodContent) {
        tags.push(
          <TvodVipTag
            item={transformResponseDataStructure(item) as any}
            isTimeRemainingShown={false}
            isForRibbon={false}
            key="tvodsvodtag"
          />
        );
        numberOfTag += 1;
      }
      const metadata = makeMetadata(
        {
          releaseYear,
          country,
          season: tagTime,
          duration,
          type,
          subtitleAudio: tagLabel,
          resolution: resolution || 0,
          age: ageRange,
        },
        numberOfTag
      );
      metadata.forEach((data, index) => {
        tags.push(
          <Tags
            className="tags tags--line tags--line-right tags--for-dark"
            label={`${data}`}
            direction="vertical"
            key={index}
          />
        );
      });
      return {
        metadataTags: tags,
        // Vip or TVOD or PVOD tags
        hasTags: numberOfTag > 0,
      };
    },
    [makeTagTime]
  );

  const { metadataTags, hasTags } = useMemo(() => {
    return renderTags(item);
  }, [item, renderTags]);

  const isRenderPvodTagForEpisode = useMemo(() => {
    const isPremium = item?.is_premium || 0;
    const type = item?.type || 0;
    if (
      type === VideoType.EPISODE &&
      (isPremium === TYPE_OF_CONTENT.PVOD ||
        isPremium === TYPE_OF_CONTENT.PVOD_NOT_HAVE_SVOD)
    ) {
      return true;
    }
    return false;
  }, [item]);

  const remainingTimeStr = React.useMemo(() => {
    const remainingTime: number = item?.tvod?.benefit_ended_at ?? -1;
    return remainingTime > 0
      ? makeRemainingTimeStr(remainingTime, false)
      : null;
  }, [item]);

  return (
    <div
      className={classNames("card card--intro", {
        focus: focused,
      })}
      ref={ref}
      data-layout="horizontal"
      data-index={index}
    >
      {!hidden ? (
        <>
          <div className="card__thumbnail">
            <div className="card__img ratio-16-9">
              <Img src={image} type="thumbnail" />
            </div>
            {/* TVOD Tags */}
            {displayTVODTags && (
              <div className="card__tag-box">
                <TvodTag
                  item={transformTvodTagItemStructure(item)}
                  isPriceShown={false}
                  isBorderRadius
                  isForRibbon
                  isUpperCase={false}
                />
                {liveTag || (
                  <StartTimeTag
                    item={transformResponseDataStructure(item)}
                    key={TagType.Live}
                  />
                )}
              </div>
            )}

            {progress > 0 ? (
              <ProgressBar
                ariaValueNow={progress}
                styleProgress={{
                  marginLeft: "1em",
                  width: "90%",
                  transform: "translateY(-1.0208333333vw)",
                }}
              />
            ) : null}
            {premium === TYPE_OF_CONTENT.TVOD_AND_SVOD &&
              remainingTimeStr &&
              !isPremiumDisplay &&
              item?.is_free === 1 && (
                <div className="card__tag-box card__tag-box__remaining-time">
                  <Tag
                    type={TagType.TvodRemainingTime}
                    key={TagType.TvodRemainingTime}
                    isPosition
                    isBorderRadius
                  >
                    Hết hạn sau {remainingTimeStr}
                  </Tag>
                </div>
              )}
            {premium === TYPE_OF_CONTENT.TVOD_AND_SVOD &&
            !!isPremiumDisplay &&
            isFree === 0 &&
            groupId !== listId ? (
              <div className="card__tag-box">
                <TvodVipTag
                  item={{ ...transformTvodTagItemStructure(item) }}
                  isTimeRemainingShown={false}
                  isForRibbon={false}
                  isBorderRadius
                  key="tvodsvodtag"
                />
              </div>
            ) : isRenderPvodTagForEpisode && displayEarlyAccessTag ? (
              <div className="card__tag-box">
                <Tag
                  type={TagType.EarlyAccessYellow}
                  key={TagType.EarlyAccessYellow}
                  isPosition
                  isBorderRadius
                />
              </div>
            ) : type === 4 ? (
              <div className="card__tag-box">
                {premium === 1 && !isTvodContent ? (
                  <Tag
                    type={TagType.Vip}
                    key={TagType.Vip}
                    premiumDisplay={isPremiumDisplay}
                    isPosition
                    isBorderRadius
                  />
                ) : null}
              </div>
            ) : null}
            {isGlobal &&
            displayFreeTag &&
            isPremium > 0 &&
            isPremiumDisplay?.toUpperCase?.() === "FREE" ? (
              <div className="card__tag-box">
                <Tag
                  type={TagType.Free}
                  key={TagType.Free}
                  isPosition
                  isBorderRadius
                />
              </div>
            ) : null}
            {focused && <MotionFocusBox />}
          </div>
          <div className="card__section">
            <div className="card__title card__title-truncate">{title}</div>
            {type !== 4 && type !== 6 ? (
              <div
                className="tags-group recommended"
                style={{
                  marginLeft: 0,
                  marginBottom: "0.5vw",
                }}
              >
                {metadataTags}
              </div>
            ) : null}
            <div className="card__desc">{desc}</div>
            {cast !== "" ? (
              <div className="card__cast">{`Khách mời: ${cast}`}</div>
            ) : null}
            {(type === 4 || type === 6) && duration !== "" ? (
              <div className="card__duration">{`(${duration})`}</div>
            ) : null}
          </div>
        </>
      ) : null}
    </div>
  );
};
export default CardIntro;
