import React, { useEffect, useMemo, useRef, useState } from "react";
import { useHistory, useLocation } from "react-router-dom";
import { DialogType } from "app/components/DialogCommon/types";
import DialogCommon from "app/components/DialogCommon";
import Spinner from "app/components/Common/Spinner";
import TipBox from "app/components/Common/Portal/TipBox";
import Billboard from "app/components/BillBoardLuna";
import Sport from "app/components/Sport";
import { ROUTES } from "app/utils/constants";
import SegmentManager from "app/utils/SegmentManager";
import BillboardContext from "context/BillboardLunaContext";
import useBillboardContainer from "hooks/useBillboardLunaContainer";
import useCurrentPageRef from "hooks/useCurrentPageRef";
import { useGAPageViewByLocation } from "hooks/useGAPageViewByLocation";
import useGetUserTypeName from "hooks/useGetUserTypeName";
import useLiveEventTVODSchedule from "hooks/useLiveEventTVODSchedule";
import RibbonListPerformance from "app/components/RibbonLuna/RibbonContentList";
import { RibbonType } from "types/page";
import useRibbonData from "hooks/useRibbonDataLuna";
import backService from "services/backService";
import useNavigate from "app/components/RibbonLuna/Shared/useNavigate";
import Screensaver, {
  useScreensaver,
} from "app/components/Screensaver/Screensaver";
import { RibbonVisibleProvider } from "context/RibbonVisibleContext";
import EmptyBox from "../Error/components/EmptyBox";

interface Props {
  onPopout: PopoutHandler;
}

const Category: React.FC<Props> = ({ onPopout }) => {
  const [isShowScreensaver, exitScreensaver, resetTimer] = useScreensaver();

  const {
    billboardItem,
    billboardStatus,
    setBillboard,
    setBillboardStatus,
    prevAutoItem,
    setPrevAutoItem,
    removedMasterBannerItem,
    setRemovedMasterBannerItem,
  } = useBillboardContainer();

  const { navigate } = useNavigate();

  const location = useLocation();
  const locationRef = useRef<any>(location);
  const ribbonContentEventFnc = React.useRef<any>();
  const history = useHistory();
  useEffect(() => {
    locationRef.current = location;
  }, [location]);

  const currentPageRef = useCurrentPageRef();
  const { display_type: displayType } = currentPageRef.current || {};
  const isSportPage = displayType === "sport-football";

  //
  const typeOfUser = useGetUserTypeName();
  useLiveEventTVODSchedule({
    onEventCallback: (item) => {
      const title = item.content_title;
      const images = item.poster_image;
      const id = item.content_id;

      SegmentManager.segmentAction("toast_event_happening_browsing_loaded", {
        use_type: typeOfUser,
        content_name: title,
        content_id: id,
        flow_name: "event_happening_reminder",
      });

      DialogCommon.commonDialog({
        keyName: "dialog_subpage_reminder_live_event",
        onBack: () => {},
        type: DialogType.CommonCustomerDialog,
        layoutDirection: "vertical",
        dataDialog: {
          title: `Nội dung đã đặt trước "${title}" đang phát sóng`,
          rightArea: {
            bgFrame: images,
          },
          actions: [
            {
              key: "xem_ngay",
              title: "Xem ngay",
              onClick: () => {
                history.replace("/");
                history.push(`${ROUTES.VIEW_STREAM}/${id}`);
                SegmentManager.segmentAction(
                  "toast_event_happening_browsing_select",
                  {
                    user_type: typeOfUser,
                    content_name: title,
                    content_id: id,
                    flow_name: "event_happening_reminder",
                  }
                );
              },
            },
            {
              key: "de_sau",
              title: "Để sau",
              onClick: () => {
                SegmentManager.segmentAction(
                  "toast_event_happening_browsing_close",
                  {
                    user_type: typeOfUser,
                    content_name: title,
                    content_id: id,
                    flow_name: "event_happening_reminder",
                  }
                );
              },
            },
          ],
        },
      });
    },
  });

  /* Tracking Start */
  useGAPageViewByLocation();

  /* Tracking End */

  const [tip, setTip] = useState<string>("để quay lại");

  const [ribbons, isLoading, isError, pageTitle, , keepAliveData] =
    useRibbonData(false);

  const onCloseScreenSave = React.useCallback(() => {
    exitScreensaver();
    if (ribbonContentEventFnc.current) {
      const { forceReloadSomeRibbonSpecial = () => {} } =
        ribbonContentEventFnc.current;
      forceReloadSomeRibbonSpecial();
    }
  }, [exitScreensaver]);

  const onArrowPress = React.useCallback(() => {
    resetTimer();
  }, [resetTimer]);

  const ribbonsData = React.useMemo(() => {
    let newRibbons = [];
    if (isSportPage) {
      newRibbons = ribbons.filter(
        (ribbon) => ribbon.type !== RibbonType.NewMasterBanner
      );
    } else if (displayType === "livestream") {
      newRibbons = ribbons.map((ribbon) => {
        if (ribbon.type === RibbonType.NewMasterBanner) {
          return ribbon;
        }

        return {
          ...ribbon,
          type: RibbonType.LivestreamPage,
        };
      });
    } else {
      newRibbons = [...ribbons];
    }
    return newRibbons;
  }, [ribbons, isSportPage, displayType]);

  const Content = useMemo(() => {
    if (isSportPage) {
      return (
        <Sport
          keepAliveData={keepAliveData}
          onPopout={onPopout}
          ribbons={ribbonsData}
        />
      );
    }

    return ribbonsData.length > 0 ? (
      <div className="main__wrapper">
        <RibbonVisibleProvider>
          <RibbonListPerformance
            data={ribbonsData}
            setTip={setTip}
            onPopOut={onPopout}
            keepAliveData={keepAliveData}
            onContentFocus={setBillboard}
            onContentSelect={navigate}
            onArrowPress={onArrowPress}
            listener={(fn) => {
              ribbonContentEventFnc.current = fn;
            }}
          />
        </RibbonVisibleProvider>
      </div>
    ) : null;
  }, [
    isSportPage,
    keepAliveData,
    navigate,
    onPopout,
    ribbonsData,
    setBillboard,
  ]);

  return isLoading ? (
    <Spinner style={{ height: "100vh" }} />
  ) : (
    <>
      <div className="sub-page__title">{pageTitle}</div>

      {ribbonsData.length === 0 || isError > 0 ? (
        <div style={{ width: "100vw", height: "100vh" }}>
          <EmptyBox
            onEnter={() => {
              history.replace("/");
            }}
            onReturn={() => backService.back()}
            type="SUB_CATEGORY"
          />
        </div>
      ) : (
        <BillboardContext.Provider
          value={{
            prevAutoItem,
            setPrevAutoItem,
            billboardItem,
            billboardStatus,
            setBillboard,
            setBillboardStatus,
            removedMasterBannerItem,
            setRemovedMasterBannerItem,
          }}
        >
          {!!ribbons.length && !isSportPage && <Billboard />}
          <div id="Home" className="main main--sub-page">
            {Content}
          </div>
          {isShowScreensaver &&
          billboardItem?.billboardType !== "masterbanner" ? (
            <Screensaver onClose={onCloseScreenSave} />
          ) : null}
          <TipBox tip={tip} />
        </BillboardContext.Provider>
      )}
    </>
  );
};

export default Category;
