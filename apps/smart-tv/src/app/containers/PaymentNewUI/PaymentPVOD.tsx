import React, { useCallback, useEffect, useReducer, useRef } from "react";
import get from "lodash/get";
import merge from "lodash/merge";
import { useHistory, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import Message from "app/components/Common/Message";
import {
  getProfileData as getProfileDataAction,
  updateProfile as updateProfileAction,
} from "app/store/actions";
import { GetProfilePayload, UpdateProfilePayload } from "app/store/types";
import { RootState } from "app/store/store";
import KeepAlive from "app/utils/KeepAlive";
import usePrevious from "hooks/usePrevious";
import backService from "services/backService";
import Spinner from "app/components/Common/Spinner";
import { FocusContext, useFocusable } from "core/KeyHandle";
import { ROUTES } from "app/utils/constants";
import { ProfileModel } from "app/models/MultiProfile/ProfileModel";
import {
  showDialogFastTrackAlreadyBought,
  showDialogFastTrackExpired,
} from "app/containers/VideoIntro/utils";
import withPaymentCountryRestrict from "app/components/HOC/withPaymentCountryRestrict";
import { usePaymentDataPVOD } from "./hook";
import StepThree, {
  PVODPaymentResultFail,
  PVODPaymentResultSuccess,
} from "./StepThree";
import { EnumPaymentNewUIFocusKey, EnumPaymentStep } from "./types";
import styles from "./Components/PaymentStyles.module.scss";
import StepTwoPVOD from "./StepTwo/StepTwoPVOD";

interface Props {}

export interface State {
  step: number;
  retryMethod: string;
  location: any;
  history: any;
  paymentResult?: PVODPaymentResultSuccess | PVODPaymentResultFail | null;
}

type Action =
  | {
      type: "stepTwoSubmit";
      payload: PVODPaymentResultSuccess | PVODPaymentResultFail;
    }
  | {
      type: "backStep";
      payload: {
        isAuthen: any;
        currentProfile: ProfileModel | null;
      };
    }
  | {
      type: "backHistory";
      payload: {
        isAuthen: any;
        currentProfile: ProfileModel | null;
      };
    }
  | { type: "retry"; payload: { retryMethod: string } }
  | { type: "cleanRetry" };

type Reducer<S, A> = (prevState: S, action: A) => S;
type InitialState = Omit<State, "historyPoint" | "location" | "history">;
const initialState: InitialState = {
  step: EnumPaymentStep.TWO,
  paymentResult: null,
  retryMethod: "",
};

const reducer: Reducer<State, Action> = (state, action) => {
  const { location, history } = state;
  switch (action.type) {
    case "stepTwoSubmit":
      return {
        ...state,
        step: EnumPaymentStep.THREE,
        paymentResult: action.payload,
      };
    case "backStep":
      switch (state.step) {
        case EnumPaymentStep.TWO:
          if (action.payload.isAuthen && !action.payload.currentProfile) {
            const searchParams = new URLSearchParams(location.search);
            const redirect = searchParams.get("redirect");
            const from = searchParams.get("from");
            const authenticationMethod =
              location.state?.authenticationMethod || "";
            // history.goBack();
            setTimeout(() => {
              history.replace({
                pathname: ROUTES.MULTI_PROFILE_LOBBY,
                state: {
                  redirect: from || redirect || "/",
                  isBack: true,
                  authenticationMethod,
                },
              });
            }, 100);
          } else {
            history.goBack();
          }
          break;
        case EnumPaymentStep.THREE:
          return {
            ...state,
            step: EnumPaymentStep.TWO,
          };
        default:
          return state;
      }
      return state;
    case "backHistory":
      const searchParams = new URLSearchParams(location.search);
      const from = searchParams.get("from");
      const trigger = searchParams.get("trigger");
      const redirect = searchParams.get("redirect");
      const authenticationMethod = location.state?.authenticationMethod || "";
      const statePlayer = searchParams.get("state");
      const root = searchParams.get("root");
      let search = "";
      if (statePlayer || root) {
        const nextUrlParams = new URLSearchParams();
        if (statePlayer) {
          nextUrlParams.append("state", statePlayer);
        }
        if (root) {
          nextUrlParams.append("root", root);
        }
        search = nextUrlParams.toString();
      }
      if (from) {
        setTimeout(() => {
          if (action.payload.isAuthen && !action.payload.currentProfile) {
            history.replace({
              pathname: ROUTES.MULTI_PROFILE_LOBBY,
              state: {
                redirect: {
                  pathname: from,
                  search,
                },
                isBack: trigger !== "dialog",
                authenticationMethod,
              },
            });
          } else {
            history.goBack();
            setTimeout(() => {
              history.replace({
                pathname: from,
                search,
                isBack: trigger !== "dialog",
              });
            }, 50);
          }
        }, 100);
      } else if (redirect) {
        if (action.payload.isAuthen && !action.payload.currentProfile) {
          setTimeout(() => {
            history.replace({
              pathname: ROUTES.MULTI_PROFILE_LOBBY,
              state: {
                redirect: {
                  pathname: redirect,
                  search,
                },
                isBack: trigger !== "dialog",
                authenticationMethod,
              },
            });
          }, 100);
        } else {
          history.replace({
            pathname: redirect,
            search,
          });
        }
      } else if (action.payload.isAuthen && !action.payload.currentProfile) {
        setTimeout(() => {
          history.replace({
            pathname: ROUTES.MULTI_PROFILE_LOBBY,
            state: {
              redirect: "/",
              isBack: trigger !== "dialog",
              authenticationMethod,
            },
          });
        }, 100);
      } else {
        history.goBack();
      }
      return state;
    case "retry":
      return {
        ...state,
        step: 2,
        retryMethod: action.payload.retryMethod,
      };
    case "cleanRetry":
      return {
        ...state,
        retryMethod: "",
      };
    default:
      return state;
  }
};

const PaymentPVOD: React.FC<Props> = withPaymentCountryRestrict(() => {
  const location = useLocation<any>();
  const locationRef = useRef(location);
  useEffect(() => {
    locationRef.current = location;
  }, [location]);
  const userType = useSelector((state: RootState) => {
    return get(state, "app.userType");
  });
  const history = useHistory();

  const dispatchProfile = useDispatch();
  const getProfile = (payload: GetProfilePayload) =>
    dispatchProfile(getProfileDataAction(payload));
  const updateProfile = (payload: UpdateProfilePayload) =>
    dispatchProfile(updateProfileAction(payload));

  const [state, dispatch] = useReducer(
    reducer,
    merge(initialState, {
      historyPoint: window.history.length,
      location,
      history,
    })
  );

  const stateRef = useRef<State>(state);
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  const [paymentState] = usePaymentDataPVOD();
  const {
    config,
    napasToken,
    shopeePayTokens,
    isLoading,
    error,
    pvodInfo,
    pvodOffer,
  } = paymentState;
  useEffect(() => {
    if (error > 0) {
      backService.back();
      Message.open("Có lỗi xảy ra vui lòng thử lại");
    }
  }, [error]);
  useEffect(() => {
    if (currentProfile?.isKid) {
      const action = () => {
        backService.back();
      };
      // showDialogFeatureForKidNeedBuy([action], action);
      // show dialog fast-track cho profile kid
    }
    backService.register(onBackHandler);
    return () => {
      backService.unregister(onBackHandler);
    };
  }, []);

  useEffect(() => {
    if (pvodInfo) {
      const isExpire = get(pvodInfo, "isExpire", false);
      const status = get(pvodInfo, "benefit_info.type", 0) || 0;
      if (isExpire) {
        // show dialog expire
        funcShowDialogFastTrackExpired();
      } else if (status === 1) {
        // show dialog da co goi
        funcShowDialogFastTrackAlreadyBought();
      }
    }
  }, [pvodInfo]);

  const funcShowDialogFastTrackExpired = () => {
    const onOk = () => {
      onStepThreeContinue();
    };
    const onReturn = () => {
      onBackHandler();
    };
    const contentName = get(pvodInfo, "contentInfo.title", "");
    showDialogFastTrackExpired({ onOk, onReturn, contentName });
  };
  const funcShowDialogFastTrackAlreadyBought = () => {
    const onOk = () => {
      onStepThreeContinue();
    };
    const onReturn = () => {
      onBackHandler();
    };
    const contentEpisode = "";
    const contentName = get(pvodInfo, "contentInfo.title", "");
    showDialogFastTrackAlreadyBought({
      onOk,
      onReturn,
      contentEpisode,
      contentName,
    });
  };

  const stepTwoMethods = useRef<any>(null);
  const isAuthen = useSelector((state: RootState) => {
    return get(state, "app.isAuthen", "");
  });
  const currentProfile = useSelector((state: RootState) => {
    return state?.app.currentProfile;
  });

  const onBackHandler = useCallback(() => {
    dispatch({
      type: "backStep",
      payload: {
        isAuthen,
        currentProfile,
      },
    });
  }, []);
  const onEnterCode = () => {};
  const cleanRetry = () => {
    dispatch({
      type: "cleanRetry",
    });
  };
  const onStepThreeBackHome = () => {
    KeepAlive.clearData();
    const searchParams = new URLSearchParams(location.search);
    const from = searchParams.get("from");
    const trigger = searchParams.get("trigger");
    const redirect = searchParams.get("redirect");
    const authenticationMethod = location.state?.authenticationMethod || "";
    const slug = searchParams.get("slug");
    let search = "";
    if (slug) {
      const nextUrlParams = new URLSearchParams();
      nextUrlParams.append("slug", slug);
      search = nextUrlParams.toString();
      if (from) {
        setTimeout(() => {
          if (isAuthen && !currentProfile) {
            history.replace({
              pathname: ROUTES.MULTI_PROFILE_LOBBY,
              state: {
                redirect: {
                  pathname: from,
                  search,
                },
                isBack: trigger !== "dialog",
                authenticationMethod,
              },
            });
          } else {
            history.goBack();
            setTimeout(() => {
              history.replace({
                pathname: from,
                search,
                state: { isBack: trigger !== "dialog" },
              });
            }, 50);
          }
        }, 100);
      } else if (redirect) {
        if (isAuthen && !currentProfile) {
          setTimeout(() => {
            history.replace({
              pathname: ROUTES.MULTI_PROFILE_LOBBY,
              state: {
                redirect: {
                  pathname: redirect,
                  search,
                },
                isBack: trigger !== "dialog",
                authenticationMethod,
              },
            });
          }, 100);
        } else {
          history.replace({
            pathname: redirect,
            search,
          });
        }
      } else if (isAuthen && !currentProfile) {
        setTimeout(() => {
          history.replace({
            pathname: ROUTES.MULTI_PROFILE_LOBBY,
            state: {
              redirect: "/",
              isBack: trigger !== "dialog",
              authenticationMethod,
            },
          });
        }, 100);
      } else if (history.length > 1) {
        history.goBack();
      } else {
        history.replace(ROUTES.ROOT);
      }
    } else if (history.length > 1) {
      history.goBack();
    } else {
      history.replace(ROUTES.ROOT);
    }
  };
  const onStepThreeContinue = () => {
    dispatch({
      type: "backHistory",
      payload: {
        isAuthen,
        currentProfile,
      },
    });
  };
  const onStepThreeRetry = (method: string) => {
    dispatch({
      type: "retry",
      payload: {
        retryMethod: method,
      },
    });
  };

  const onStepTwoSubmit = (
    payload: PVODPaymentResultSuccess | PVODPaymentResultFail
  ) => {
    // if (payload.status === "pvod_success") {
    //   // Update profile
    //   getProfile({
    //     callback: ({ status, data }) => {
    //       if (status === "success" && data) {
    //         const profile = get(data, "profile") as AccountModel;
    //         const userType = get(data, "type") as number;
    //         const packageGroupId = get(data, "package_group_id", 0) as number;
    //         const livetvGroupId = get(data, "livetv_group_id", "") as string;
    //         const hideBuyPackage = get(
    //           data,
    //           "hide_button_buy_package",
    //           false
    //         ) as boolean;
    //         updateProfile({
    //           profile,
    //           userType,
    //           packageGroupId,
    //           livetvGroupId,
    //           hideBuyPackage,
    //         });
    //       }
    //     },
    //   });
    // }

    dispatch({
      type: "stepTwoSubmit",
      payload,
    });
  };

  useEffect(() => {
    if (!isLoading) {
      // Got keep alive dt
      const stepTwoListener = stepTwoMethods.current;
      const keepAliveDt = KeepAlive.getData("#/paymentPVOD");
      if (keepAliveDt) {
        isHaveKeepAlive.current = true;
        const extraData = keepAliveDt.extra;
        const chosenMethod = get(extraData, "chosenMethod");
        const chosenMethodText = get(extraData, "chosenMethodText");
        if (chosenMethod !== undefined) {
          const couponCode = get(extraData, "couponCode");
          const activeAtSubmitCode = get(extraData, "activeAtSubmitCode");
          setTimeout(() => {
            stepTwoListener("setInitialState", {
              focusIndex: chosenMethod,
              chosenMethodText,
              couponCode,
              activeAtSubmitCode,
            });
          }, 200);
        }
      }
    }
  }, [isLoading]);

  const previousStep = usePrevious<number>(state.step);
  useEffect(() => {
    if (
      previousStep === EnumPaymentStep.TWO &&
      state.step === EnumPaymentStep.THREE
    ) {
      //
    }
  }, [state.step]);
  const isHaveKeepAlive = useRef(false);
  const { ref, focusKey, focusSelf } = useFocusable({
    focusKey: EnumPaymentNewUIFocusKey.PaymentRootTVOD,
  });

  React.useEffect(() => {
    focusSelf();
  }, []);
  return isLoading || get(pvodInfo, "isExpire", false) ? (
    <Spinner style={{ height: "100vh" }} />
  ) : (
    <FocusContext.Provider value={focusKey}>
      <div className={styles["payment-wp"]} ref={ref}>
        <StepTwoPVOD
          show={state.step === EnumPaymentStep.TWO}
          onStepSubmit={onStepTwoSubmit}
          onStepReturn={onBackHandler}
          onEnterCode={onEnterCode}
          onContinue={onStepThreeContinue} // Go to previous place (home/content)
          pvodInfo={pvodInfo}
          pvodOfferData={pvodOffer}
          config={config}
          napasToken={napasToken}
          shopeePayTokens={shopeePayTokens}
          onPopout={() => {}}
          retryMethod={state.retryMethod}
          cleanRetry={cleanRetry}
          // listener={(fn: any) => (stepTwoMethods.current = fn)}
          // onStepThreeContinue={onStepThreeContinue}
        />
        {state.paymentResult && (
          <StepThree
            show={state.step === EnumPaymentStep.THREE}
            data={state.paymentResult}
            config={config}
            onPopout={() => {}}
            onBackHome={onStepThreeBackHome} // Go to home
            onContinue={onStepThreeContinue} // Go to previous place (home/content)
            onRetry={onStepThreeRetry} // Retry
            pvodInfo={pvodInfo}
          />
        )}
      </div>
    </FocusContext.Provider>
  );
});
export default PaymentPVOD;
