import React, { useEffect, useRef, useState, useCallback } from "react";
import { useHistory, useLocation, useRouteMatch } from "react-router-dom";
import get from "lodash/get";
import { useSelector } from "react-redux";
import DialogCommon from "app/components/DialogCommon";
import { FocusContext, useFocusable } from "core/KeyHandle";
import { StepName, StepType } from "types/page/payment";
import withRenderByShowProp from "app/components/HOC/withFocusByRender";
import { RootState } from "app/store/store";
import { ROUTES } from "app/utils/constants";
import { EnumAuthTitle } from "app/containers/Authentication/type";
import { GlobalFeatureFlowName } from "app/components/Tracking/GlobalFeatureTracking";
import SegmentManager from "app/utils/SegmentManager";
import { LocalFeatureEventNames, LocalFeaturePropertyKeys } from "app/components/Tracking/LocalFeatureTracking";
import KeepAlive, { KeepAliveData } from "app/utils/KeepAlive";
import TriggerFooter from "./TriggerFooter";
import styles from "./styles.module.scss";
import DurationList from "./components/DurationList";
import PackageList from "./components/PackageList";
import SpecialOfferList from "./components/SpecialOfferList";
import PromotionCodePanel from "./components/PromotionCodePanel";
import useData from "./hook";
import PaymentHeader from "../Components/PaymentHeader";
import { EnumPaymentNewUIFocusKey } from "../types";
import { PaymentBuyWithoutLogin } from "..";

interface Props {
  packages: any[];
  specialOfferConfig: any;
  promotionConfig: any;
  onSubmit?: (value: { chosenDuration: any; chosenPackage: any }) => void;
  onStepReturn: () => void;
  onEnterCode?: () => void;
  isShowAds: boolean;
  setIsShowAds?: (data: boolean) => void;
  listener?: (I: any) => void;
  getFocusMethod?: () => void;
  getCouponCode?: () => void;
  isNotiOEM?: boolean;
  config: any;
  pkg: any;
  duration: any;
  paymentBuyWithoutLogin?: PaymentBuyWithoutLogin;
}

const StepOne = withRenderByShowProp(
  ({
    packages,
    onSubmit,
    onStepReturn,
    onEnterCode,
    specialOfferConfig,
    promotionConfig,
    isShowAds,
    setIsShowAds,
    listener,
    getFocusMethod,
    getCouponCode,
    isNotiOEM,
    pkg,
    duration,
    config,
    paymentBuyWithoutLogin
  }: Props) => {
    // mini step instep 1
    const [step, setStep] = useState<number>(StepType.PackageListType);
    const [directionEnterCode, setDirectionEnterCode] = useState<boolean>(false);
    const location: any = useLocation<{
      referal: string;
      contentId?: string;
    }>();
    const match = useRouteMatch();
    const locationRef = useRef<any>();
    const [isInitialized, setIsInitialized] = useState<boolean>(false);
    const pkgRef = useRef<any>(null);
    const durationRef = useRef<any>(null);

    const [state, dispatch] = useData(packages);
    const { activePackage, durations, activeDuration } = state;
    const activePackageRef = useRef(activePackage);
    const isAuthen = useSelector((state: RootState) => {
      return get(state, "app.isAuthen", "");
    });
    const history = useHistory();

    const { ref, focusKey, focusSelf, setFocus } = useFocusable({
      focusKey: EnumPaymentNewUIFocusKey.PaymentStepOne
    });

    useEffect(() => {
      activePackageRef.current = activePackage;
    }, [activePackage]);

    useEffect(() => {
      locationRef.current = location;
    }, [location]);

    useEffect(() => {
      focusSelf();
      const canShowPromotions = get(promotionConfig, "is_show", false);
      const image = get(promotionConfig, "image", "");
      const okText = get(promotionConfig, "button_name", "Tiếp tục");
      const showDuration = +get(promotionConfig, "show_duration", 0);
      const now = new Date().getTime();
      const old_time = sessionStorage.getItem("ads_show_time");
      // set lần đầu chưa có old_time luôn hienẹ popup
      const time = !old_time ? showDuration * 60000 : now - +old_time;
      if (isShowAds && canShowPromotions && image.length && time / 60000 >= showDuration) {
        sessionStorage.setItem("ads_show_time", `${new Date().getTime()}`);
        setTimeout(() => {
          DialogCommon.DialogPromotionAds({
            keyName: "dialog_promotion_show_time",
            image,
            okText,
            onOk: () => {
              if (setIsShowAds && typeof setIsShowAds === "function") {
                setIsShowAds(false);
              }
            }
          });
        }, 1000);
      }
    }, []);

    const onSelectPackage = (value?: any, backFromStepTwo: boolean = false) => {
      // next to duration list
      if (paymentBuyWithoutLogin) {
        setStep(StepType.DurationListType);
      } else if (isAuthen) {
        setStep(StepType.DurationListType);
        if (!backFromStepTwo) {
          SegmentManager.segmentAction(LocalFeatureEventNames.PaymentPageLoaded, {
            [LocalFeaturePropertyKeys.CurrentPage]: StepName.DurationListName
          });
          // const item = value?.items?.[0] ?? {};
          // SegmentManager.segmentAction(
          //   LocalFeatureEventNames.PackageDurationSelected,
          //   {
          //     [LocalFeaturePropertyKeys.PackageId]: item?.id,
          //     [LocalFeaturePropertyKeys.PackageName]: item?.name,
          //     [LocalFeaturePropertyKeys.PaidPrice]: item?.price,
          //     [LocalFeaturePropertyKeys.PackageDuration]: `${item?.duration} ${item?.duration_type}`,
          //   }
          // );
        }
      } else {
        const keepAliveDt = KeepAlive.getData(window.location.hash);
        const currentState = {
          ...(locationRef.current?.state ?? {}),
          ...(keepAliveDt?.extra?.state ?? {})
        };

        const keepAliveData: KeepAliveData = {
          path: window.location.hash,
          focus: { x: 0, y: 0 },
          extra: {
            state: {
              search: locationRef.current?.search || "",
              ...currentState,
              ...(value?.id ? { pkgId: value?.id } : {}),
              check_out_started: true
            }
          },
          focusKey
        };

        KeepAlive.saveData(keepAliveData);

        const searchParams = new URLSearchParams(locationRef.current?.search);
        if (currentState?.durationId) {
          searchParams.append("durationId", currentState?.durationId);
        }
        history.push({
          pathname: ROUTES.LOGIN_STEP,
          state: {
            successLocation: {
              ...locationRef.current,
              state: {
                ...currentState,
                referal: "Đăng nhập",
                activePackage: state?.activePackage,
                ...(value?.id ? { pkgId: value?.id } : {})
              },
              search: searchParams.toString()
            },
            successMsg: "Đăng nhập tài khoản thành công.<br/>Vui lòng tiếp tục thanh toán",
            successMsgSignUp: "Tạo tài khoản thành công.<br/>Vui lòng tiếp tục thanh toán",
            title: EnumAuthTitle.RegisterByPayment,
            flowName: GlobalFeatureFlowName.RegistrationForPayment
          }
        });
      }
      SegmentManager.segmentAction("package_selected", {
        [LocalFeaturePropertyKeys.PackageName]: value?.name,
        [LocalFeaturePropertyKeys.PackageId]: value?.id
      });
    };

    const onChangePackage = (value: any) => {
      if (value && Object.keys(value)?.length > 0) {
        dispatch({
          type: "changeActivePackage",
          payload: {
            activePackage: value
          }
        });
      }
    };
    const onChangeDuration = (value: any) => {
      if (value) {
        dispatch({
          type: "changeActiveDuration",
          payload: {
            activeDuration: value
          }
        });
      }
    };

    const onSelectDuration = (value: any) => {
      if (onSubmit && typeof onSubmit === "function") {
        onSubmit({
          chosenDuration: value,
          chosenPackage: activePackageRef.current
        });
      }
      // SegmentManager.segmentAction(
      //   LocalFeatureEventNames.PackageDurationSelected,
      //   {
      //     [LocalFeaturePropertyKeys.PackageId]: value?.id,
      //     [LocalFeaturePropertyKeys.PackageName]: value?.name,
      //     [LocalFeaturePropertyKeys.PaidPrice]: value?.price,
      //     [LocalFeaturePropertyKeys.PackageDuration]: `${value?.duration} ${value?.duration_type}`,
      //     [LocalFeaturePropertyKeys.Recurring]: !!value.recurring,
      //   }
      // );
    };

    const onDurationReturn = () => {
      setStep(StepType.PackageListType);
    };

    const handleEnterCodeClick = () => {
      history.replace({
        pathname: ROUTES.PAYMENT,
        state: {
          successLocation: {
            ...locationRef.current,
            state: {
              step: StepType.PackageListType,
              activePackage: state?.activePackage
            }
            /* search: searchParams.toString(), */
          }
        }
      });
      if (onEnterCode && typeof onEnterCode === "function") {
        onEnterCode();
      }
      SegmentManager.segmentAction(LocalFeatureEventNames.PromotionButtonSelected, {
        [LocalFeaturePropertyKeys.CurrentPage]: "code_input_screen"
      });
    };
    // Scenario access directly to package or duration or step two
    // #/payment?pkgId=29 => select package 29 on step 1
    // #/payment?pkgId=29&duration=58 => select package 29, duration 59 and submit step 1
    /* useEffect(() => { */
    /*   const searchParams = new URLSearchParams(location.search); */
    /*   const pkgId = searchParams.get("pkgId"); */
    /*   const durationId = searchParams.get("durationId"); */
    /*   if (pkgId) { */
    /*     const pkg = packages.find((p) => pkgId === p.id.toString()); */
    /*     onChangePackage(pkg); */
    /*     if (durationId) { */
    /*       const duration = pkg.items.find( */
    /*         (d: any) => durationId === d.id.toString() */
    /*       ); */
    /*       setTimeout(() => { */
    /*         onSelectDuration(duration); */
    /*         setIsInitialized(true); */
    /*       }); */
    /*       return; */
    /*     } */
    /*   } */
    /*   setIsInitialized(true); */
    /* }, []); */

    const messageHandler = useCallback((msg: string, data?: any) => {
      let pkgId: string;
      let pkg: any;
      let durationId: string;
      let duration: any;
      switch (msg) {
        case "initial":
          setIsInitialized(true);
          break;
        case "firstSetPkgId":
          pkgId = get(data, "pkgId");
          if (pkgId) {
            pkg = packages.find((p) => pkgId === p.id);
            onChangePackage(pkg);
            onSelectPackage(pkg);
          }
          setIsInitialized(true);
          break;
        case "setPkgId":
          // move to pkg id
          pkgId = get(data, "pkgId");
          pkg = packages.find((p) => pkgId === p.id);
          onChangePackage(pkg);

          // auto select package
          // onSelectPackage();
          setIsInitialized(true);
          break;
        case "submitDuration":
          pkgId = get(data, "pkgId");
          pkg = packages.find((p) => pkgId === p.id);
          durationId = get(data, "durationId");
          duration = pkg?.items?.find((d: any) => durationId === d.id) ?? null;
          onChangePackage(pkg);
          activePackageRef.current = pkg;
          onSelectPackage();
          setTimeout(() => {
            onSelectDuration(duration);
            setIsInitialized(true);
            const cb = get(data, "cb");
            if (cb && typeof cb === "function") {
              cb();
            }
          });
          break;
        case "backFromStepTwo":
          pkgId = get(data, "pkgId");
          pkg = packages.find((p) => pkgId === p.id);
          durationId = get(data, "durationId");
          duration = pkg?.items?.find((d: any) => durationId === d.id) ?? null;
          onChangePackage(pkg);
          activePackageRef.current = pkg;
          onSelectPackage(null, true);
          onChangeDuration(duration);
          setTimeout(() => {
            setIsInitialized(true);
          });
          break;
        default:
          break;
      }
    }, []);

    useEffect(() => {
      pkgRef.current = pkg;
    }, [pkg]);

    useEffect(() => {
      durationRef.current = duration;
    }, [duration]);

    useEffect(() => {
      if (step === StepType.DurationListType) {
        // SegmentManager.segmentAction(LocalFeatureEventNames.PaymentPageLoaded, {
        //   [LocalFeaturePropertyKeys.CurrentPage]: StepName.DurationListName,
        // });
      }
    }, [step]);

    useEffect(() => {
      if (listener && typeof listener === "function") {
        listener(messageHandler);
      }
      if (location?.state?.packageId) {
        const activeFromPath = packages?.filter((value) => value?.id === location?.state?.packageId);
        if (activeFromPath?.length > 0) {
          dispatch({
            type: "changeActivePackage",
            payload: {
              activePackage: activeFromPath[0]
            }
          });
        }
      }
    }, []);

    return (
      // New layout Wrap, this Master container all page
      <FocusContext.Provider value={focusKey}>
        <PaymentHeader benefits={config?.vip_privilege || []} hotline={get(config, "hotline_vieon", "")} step={step} />
        <div className={styles["payment-step-one"]} ref={ref}>
          {isInitialized && (
            <div className={styles.main}>
              <div className={styles.section}>
                <div className={styles.content}>
                  {step === StepType.PackageListType && (
                    <PackageList
                      packages={packages}
                      activePackage={activePackage}
                      onChange={onChangePackage}
                      onSelect={onSelectPackage}
                      onReturn={onStepReturn}
                      config={{
                        packages: get(config, "packages", []),
                        benefits: get(config, "benefits", []),
                        vipPrivilege: get(config, "vip_privilege", []),
                        packagesNameMapping: config.package_group_name_mapping || {}
                      }}
                    />
                  )}
                  {step === StepType.DurationListType && (
                    <DurationList
                      durations={durations ?? []}
                      activePackage={activePackage}
                      activeDuration={activeDuration}
                      config={get(config, "package_methods", []) as any[]}
                      onChange={onChangeDuration}
                      onSelect={onSelectDuration}
                      onReturn={onDurationReturn}
                    />
                  )}
                  <SpecialOfferList
                    pkgId={activePackage?.id}
                    durId={activeDuration?.id}
                    specialList={specialOfferConfig}
                    currentStep={step}
                  />
                </div>
                {step === StepType.DurationListType && (
                  <TriggerFooter
                    data={location?.state?.data || locationRef.current?.state?.data}
                    id={location?.state?.contentId || locationRef.current?.state?.contentId}
                    activePackage={activePackage}
                    config={{
                      packages: config?.packages || [],
                      benefits: config?.benefits || []
                    }}
                    onReturn={onDurationReturn}
                  />
                )}
                {step === StepType.PackageListType && (
                  <PromotionCodePanel onClick={handleEnterCodeClick} onReturn={onStepReturn} />
                )}
              </div>
            </div>
          )}
        </div>
      </FocusContext.Provider>
    );
  }
);

export default StepOne;
