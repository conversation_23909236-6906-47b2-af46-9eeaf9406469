import React, { useCallback, useEffect, useReducer, useRef } from "react";
import get from "lodash/get";
import merge from "lodash/merge";
import { useHistory, useLocation } from "react-router-dom";
/* import DialogPromotionAds from "app/components/Common/Portal/Dialog/DialogPromotionAds"; */
/* import PaymentResultPopup, { ButtonType } from "./Component/PaymentResultPopup"; */
/* import { getZaloPayBindingStatus } from "services/endpoint"; */
import { useDispatch, useSelector } from "react-redux";
import Message from "app/components/Common/Message";
/* import useQuery from "hooks/useQuery"; */
import {
  getProfileData as getProfileDataAction,
  updateProfile as updateProfileAction,
} from "app/store/actions";
import { GetProfilePayload, UpdateProfilePayload } from "app/store/types";
/* import Tracking from "./Tracking"; */
import { RootState } from "app/store/store";
import KeepAlive from "app/utils/KeepAlive";
import { makeTypeOfUser } from "app/utils/make";
import SegmentManager from "app/utils/SegmentManager";
import { trackTvod, TVOD_EVENT_NAMES } from "app/utils/track";
import usePrevious from "hooks/usePrevious";
import backService from "services/backService";
import Spinner from "app/components/Common/Spinner";
import { MESSAGES } from "types/common/Message";
import useGetUserTypeName from "hooks/useGetUserTypeName";
import { FocusContext, useFocusable } from "core/KeyHandle";
import {
  DialogType as DialogTypeCommon,
  DialogType as NewDialogType,
} from "app/components/DialogCommon/types";
import DialogCommon from "app/components/DialogCommon";
import tvodMBg from "assets/images/tvod-mbg.png";
import Icon from "app/components/Common/Icon";
import emptyCamera from "assets/images/empty_state/Empty_Camera.png";
import { ROUTES } from "app/utils/constants";
import { ProfileModel } from "app/models/MultiProfile/ProfileModel";
import { showDialogFeatureForKidNeedBuy } from "app/utils/player";
import { AccountModel } from "app/models";
import withPaymentCountryRestrict from "app/components/HOC/withPaymentCountryRestrict";
import { usePaymentDataTVOD } from "./hook";
import StepThree, {
  TVODPaymentResultFail,
  TVODPaymentResultSuccess,
} from "./StepThree";
import StepTwoTVOD from "./StepTwo/StepTwoTVOD";
import { EnumPaymentNewUIFocusKey, EnumPaymentStep } from "./types";
import styles from "./Components/PaymentStyles.module.scss";

interface Props {}

export interface State {
  step: number;
  retryMethod: string;
  location: any;
  history: any;
  paymentResult?: TVODPaymentResultSuccess | TVODPaymentResultFail | null;
}

type Action =
  | {
      type: "stepTwoSubmit";
      payload: TVODPaymentResultSuccess | TVODPaymentResultFail;
    }
  | {
      type: "backStep";
      payload: {
        isAuthen: any;
        currentProfile: ProfileModel | null;
        isOwner: boolean;
      };
    }
  | {
      type: "backHistory";
      payload: {
        isAuthen: any;
        currentProfile: ProfileModel | null;
      };
    }
  | { type: "retry"; payload: { retryMethod: string } }
  | { type: "cleanRetry" };

type Reducer<S, A> = (prevState: S, action: A) => S;
type InitialState = Omit<State, "historyPoint" | "location" | "history">;
const initialState: InitialState = {
  step: EnumPaymentStep.TWO,
  paymentResult: null,
  retryMethod: "",
};

const reducer: Reducer<State, Action> = (state, action) => {
  const { location, history } = state;
  switch (action.type) {
    case "stepTwoSubmit":
      return {
        ...state,
        step: EnumPaymentStep.THREE,
        paymentResult: action.payload,
      };
    case "backStep":
      switch (state.step) {
        case EnumPaymentStep.TWO:
          let search = "";
          if (action.payload.isAuthen && !action.payload.currentProfile) {
            const searchParams = new URLSearchParams(location.search);
            const redirect = searchParams.get("redirect");
            const state = searchParams?.get("state");
            const from = searchParams.get("from");
            const flow = searchParams?.get("flow");
            const authenticationMethod =
              location.state?.authenticationMethod || "";
            if (state && (action.payload?.isOwner || flow === "tvod-svod")) {
              const nextUrlParams = new URLSearchParams();
              nextUrlParams.append("state", state);
              search = nextUrlParams.toString();
            }
            // history.goBack();
            setTimeout(() => {
              history.replace({
                pathname: ROUTES.MULTI_PROFILE_LOBBY,
                state: {
                  redirect: {
                    pathname: from || redirect || "/",
                    search,
                  },
                  authenticationMethod,
                },
              });
            }, 100);
          } else {
            history.goBack();
          }
          break;
        case EnumPaymentStep.THREE:
          return {
            ...state,
            step: EnumPaymentStep.TWO,
          };
        default:
          return state;
      }
      return state;
    case "backHistory":
      const searchParams = new URLSearchParams(location.search);
      const from = searchParams.get("from");
      const trigger = searchParams.get("trigger");
      const redirect = searchParams.get("redirect");
      const authenticationMethod = location.state?.authenticationMethod || "";
      const statePlayer = searchParams.get("state");
      const root = searchParams.get("root");
      let search = "";
      if (statePlayer || root) {
        const nextUrlParams = new URLSearchParams();
        if (statePlayer) {
          nextUrlParams.append("state", statePlayer);
        }
        if (root) {
          nextUrlParams.append("root", root);
        }
        search = nextUrlParams.toString();
      }
      if (from) {
        setTimeout(() => {
          if (action.payload.isAuthen && !action.payload.currentProfile) {
            history.replace({
              pathname: ROUTES.MULTI_PROFILE_LOBBY,
              state: {
                redirect: {
                  pathname: from,
                  search,
                },
                isBack: trigger !== "dialog",
                authenticationMethod,
              },
            });
          } else {
            history.goBack();
            setTimeout(() => {
              history.replace({
                pathname: from,
                search,
                isBack: trigger !== "dialog",
              });
            }, 50);
          }
        }, 100);
      } else if (redirect) {
        if (action.payload.isAuthen && !action.payload.currentProfile) {
          setTimeout(() => {
            history.replace({
              pathname: ROUTES.MULTI_PROFILE_LOBBY,
              state: {
                redirect: {
                  pathname: redirect,
                  search,
                },
                isBack: trigger !== "dialog",
                authenticationMethod,
              },
            });
          }, 100);
        } else {
          history.replace({
            pathname: redirect,
            search,
          });
        }
      } else if (action.payload.isAuthen && !action.payload.currentProfile) {
        setTimeout(() => {
          history.replace({
            pathname: ROUTES.MULTI_PROFILE_LOBBY,
            state: {
              redirect: "/",
              isBack: trigger !== "dialog",
              authenticationMethod,
            },
          });
        }, 100);
      } else {
        history.goBack();
      }
      return state;
    case "retry":
      return {
        ...state,
        step: 2,
        retryMethod: action.payload.retryMethod,
      };
    case "cleanRetry":
      return {
        ...state,
        retryMethod: "",
      };
    default:
      return state;
  }
};

const PaymentTVOD: React.FC<Props> = withPaymentCountryRestrict(() => {
  const location = useLocation<any>();
  const locationRef = useRef(location);
  useEffect(() => {
    locationRef.current = location;
  }, [location]);
  const userType = useSelector((state: RootState) => {
    return get(state, "app.userType");
  });
  const hotelAccount = useSelector((state: RootState) => {
    return state.app.profile?.hotelAccount ?? false;
  });

  const profile: any = useSelector((state: RootState) => {
    return state.app.profile ?? false;
  });

  const history = useHistory();

  const dispatchProfile = useDispatch();
  const getProfile = (payload: GetProfilePayload) =>
    dispatchProfile(getProfileDataAction(payload));
  const updateProfile = (payload: UpdateProfilePayload) =>
    dispatchProfile(updateProfileAction(payload));

  const [state, dispatch] = useReducer(
    reducer,
    merge(initialState, {
      historyPoint: window.history.length,
      location,
      history,
    })
  );

  const stateRef = useRef<State>(state);
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  const [paymentState] = usePaymentDataTVOD();
  const {
    config,
    napasToken,
    shopeePayTokens,
    isLoading,
    error,
    tvodInfo,
    tvodOffer,
  } = paymentState;

  React.useEffect(() => {
    if (hotelAccount) {
      history.replace(ROUTES.ROOT);
    }
  }, [hotelAccount]);

  useEffect(() => {
    if (error > 0) {
      backService.back();
      Message.open("Có lỗi xảy ra vui lòng thử lại");
    }
  }, [error]);
  useEffect(() => {
    if (currentProfile?.isKid) {
      const action = () => {
        backService.back();
      };
      showDialogFeatureForKidNeedBuy([action], action);
    }
    backService.register(onBackHandler);
    return () => {
      backService.unregister(onBackHandler);
    };
  }, []);

  const stepTwoMethods = useRef<any>(null);
  const isAuthen = useSelector((state: RootState) => {
    return get(state, "app.isAuthen", "");
  });
  const currentProfile = useSelector((state: RootState) => {
    return state?.app.currentProfile;
  });

  const onBackHandler = useCallback(() => {
    const status = get(tvodInfo, "benefit_info.type", 0) || 0;
    const isOwner = status > 0;
    dispatch({
      type: "backStep",
      payload: {
        isAuthen,
        currentProfile,
        isOwner,
      },
    });
  }, [isAuthen, currentProfile, tvodInfo]);
  const onEnterCode = () => {};
  const cleanRetry = () => {
    dispatch({
      type: "cleanRetry",
    });
  };
  const showMessageReminderSimulcast = () => {
    const { isSimulcast } = tvodInfo;
    const preOrderEndedAt = tvodInfo?.biz_info?.pre_order?.until || 0;
    const preOrderStartedAt = tvodInfo?.biz_info?.pre_order?.started_at || 0;
    const now = new Date().getTime() / 1000;
    const isPreOrderAvailable =
      now >= preOrderStartedAt && now <= preOrderEndedAt;
    if (isSimulcast && isPreOrderAvailable) {
      Message.open(MESSAGES.tvodRemindOnAir);
    }
  };
  const showMessageReminderLiveEvent = () => {
    const { isLiveEvent } = tvodInfo;
    const isLive = tvodInfo?.contentInfo?.is_live;
    if (isLiveEvent && !isLive) {
      Message.open(MESSAGES.liveEventOnAir);
    }
  };
  const onStepThreeBackHome = () => {
    trackTvod(TVOD_EVENT_NAMES.paymentSuccessSelectHomepage);
    KeepAlive.clearData();
    if (isAuthen && !currentProfile) {
      const authenticationMethod = location.state?.authenticationMethod || "";
      history.replace({
        pathname: ROUTES.MULTI_PROFILE_LOBBY,
        state: {
          redirect: "/",
          authenticationMethod,
        },
      });
    } else {
      history.push({
        pathname: "/",
      });
    }
    showMessageReminderSimulcast();
    showMessageReminderLiveEvent();
  };
  const onStepThreeContinue = () => {
    trackTvod(TVOD_EVENT_NAMES.paymentSuccessSelectWatch);
    dispatch({
      type: "backHistory",
      payload: {
        isAuthen,
        currentProfile,
      },
    });
    showMessageReminderSimulcast();
    showMessageReminderLiveEvent();
  };
  const onStepThreeRetry = (method: string) => {
    dispatch({
      type: "retry",
      payload: {
        retryMethod: method,
      },
    });
  };

  const onStepTwoSubmit = (
    payload: TVODPaymentResultSuccess | TVODPaymentResultFail
  ) => {
    if (payload.status === "tvod_success") {
      // Update profile
      getProfile({
        callback: ({ status, data }) => {
          if (status === "success" && data) {
            const profile = get(data, "profile") as AccountModel;
            const userType = get(data, "type") as number;
            const packageGroupId = get(data, "package_group_id", 0) as number;
            const livetvGroupId = get(data, "livetv_group_id", "") as string;
            const hideBuyPackage = get(
              data,
              "hide_button_buy_package",
              false
            ) as boolean;
            updateProfile({
              profile,
              userType,
              packageGroupId,
              livetvGroupId,
              hideBuyPackage,
            });
          }
        },
      });
    }

    dispatch({
      type: "stepTwoSubmit",
      payload,
    });
  };

  useEffect(() => {
    if (!isLoading) {
      // Got keep alive dt
      const stepTwoListener = stepTwoMethods.current;
      const keepAliveDt = KeepAlive.getData("#/paymentTVOD");
      if (keepAliveDt) {
        isHaveKeepAlive.current = true;
        const extraData = keepAliveDt.extra;
        const chosenMethod = get(extraData, "chosenMethod");
        const chosenMethodText = get(extraData, "chosenMethodText");
        if (chosenMethod !== undefined) {
          const couponCode = get(extraData, "couponCode");
          const activeAtSubmitCode = get(extraData, "activeAtSubmitCode");
          setTimeout(() => {
            stepTwoListener("setInitialState", {
              focusIndex: chosenMethod,
              chosenMethodText,
              couponCode,
              activeAtSubmitCode,
            });
          }, 200);
        }
      }
    }
  }, [isLoading]);

  const previousStep = usePrevious<number>(state.step);
  useEffect(() => {
    if (
      previousStep === EnumPaymentStep.TWO &&
      state.step === EnumPaymentStep.THREE
    ) {
      const result = get(state, "paymentResult.status", "");
      const transaction_id = get(state, "paymentResult.data.orderId", "");
      const cause_for_failure = get(state, "paymentResult.data.errorText", "");
      const CHECKOUT_RESULT = {
        tvod_success: "succeed",
        tvod_fail: "failed",
        tvod_pending: "pending",
      };
      const data = {
        transaction_id,
        checkout_result: get(CHECKOUT_RESULT, result, ""),
        cause_for_failure,
        user_type: makeTypeOfUser(isAuthen, userType),
        referal: "tvod",
      };
      SegmentManager.segmentAction("checkout_result_page_loaded", data);
    }
  }, [state.step]);

  const typeOfUser = useGetUserTypeName();
  const isHaveKeepAlive = useRef(false);
  useEffect(() => {
    if (isHaveKeepAlive.current) return;
    const isLiveEvent = get(tvodInfo, "isLiveEvent", false);
    if (isLiveEvent) {
      const isFinish = get(tvodInfo, "contentInfo.is_finish", false);
      if (isFinish) {
        showDialogTVODEndStream();
        return;
      }
      const isExpire = get(tvodInfo, "isExpire", false);
      if (isExpire) {
        showDialogLiveEventEndOrder();
      } else {
        const price = parseInt(get(locationRef.current, "state.price", 0) || 0);
        if (price > 0) {
          const totalPrice = (get(tvodOffer, "total_price", 0) || 0) / 100;
          if (totalPrice > 0 && totalPrice !== price) {
            showDialogTVODPreOrderToOrder(`${price}`);
          }
        }
      }
    }
  }, [tvodInfo]);

  function showDialogLiveEventEndOrder() {
    const isVOD = !!(get(tvodInfo, "contentInfo.content_id", "") || "");
    const image = get(tvodInfo, "contentInfo.images.poster_v4", "");
    const title = get(tvodInfo, "contentInfo.title", "") || "";
    const id = get(tvodInfo, "contentInfo.id", "") || "";

    SegmentManager.segmentAction("dialog_missed_event_loaded", {
      user_type: typeOfUser,
      content_name: title,
      content_id: id,
      flow_name: isVOD ? "missed_event_have_vod" : "missed_event",
    });
    DialogCommon.commonDialog({
      type: DialogTypeCommon.CommonCustomerDialog,
      layoutDirection: "vertical",
      showTermsOfUse: false,
      dataDialog: {
        title: `Hết hạn thuê ${title}`,
        rightArea: {
          bgFrame: tvodMBg,
          imageCenter: image,
        },
        actions: [
          {
            addonBefore: (
              <span>
                Trở về trang chủ để khám phá thêm những nội <br /> dung hấp dẫn
                khác
              </span>
            ),
            key: "tro_ve_trang_chu",
            title: "Trở về trang chủ",
            onClick: () => {
              KeepAlive.clearData();
              history.push("/");

              SegmentManager.segmentAction("dialog_missed_event_homepage", {
                user_type: typeOfUser,
                content_name: title,
                content_id: id,
                flow_name: isVOD ? "missed_event_have_vod" : "missed_event",
              });
            },
          },
        ],
      },
      onBack: () => {
        SegmentManager.segmentAction("dialog_missed_event_close", {
          user_type: typeOfUser,
          content_name: title,
          content_id: id,
          flow_name: isVOD ? "missed_event_have_vod" : "missed_event",
        });
        history.goBack();
      },
    });
  }

  function showDialogTVODPreOrderToOrder(pricePreOrder: string) {
    const title = get(tvodOffer, "product_name_msg", "");
    const poster = get(tvodInfo, "contentInfo.images.poster_v4", "");
    const price = get(tvodOffer, "total_price_msg", "");
    const waitingDurMsg = get(tvodOffer, "waiting_dur_msg", "");
    const consumingDurMsg = get(tvodOffer, "consuming_dur_msg", "");
    const isVOD = !!(get(tvodInfo, "contentInfo.content_id", "") || "");

    SegmentManager.segmentAction("dialog_time_out_sale_loaded", {
      user_type: typeOfUser,
      content_name: get(tvodInfo, "contentInfo.title", "") || "",
      content_id: get(tvodInfo, "contentInfo.id", "") || "",
      flow_name: isVOD
        ? "time_out_sale_pre_order_have_vod"
        : "time_out_sale_pre_order",
    });

    DialogCommon.commonDialog({
      type: DialogTypeCommon.CommonCustomerDialog,
      layoutDirection: "vertical",
      showTermsOfUse: false,
      description: (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
          }}
        >
          {isVOD ? (
            <>
              <span>Sau khi nội dung phát sóng, bạn có</span>
              <br />
              <div style={{ marginBottom: "10px" }}>
                <Icon width={27} height={27} name="vie-time" />{" "}
                <span>
                  Nội dung chưa xem được lưu trong <b>{waitingDurMsg}</b>
                </span>
              </div>
              <div>
                <Icon width={27} height={27} name="vie-time" />{" "}
                <span>
                  Sau khi bấm xem, bạn có <b>{consumingDurMsg}</b> để xem hết
                </span>
              </div>
            </>
          ) : null}
        </div>
      ),
      dataDialog: {
        title: `Hết hạn đặt trước “${title}\" với giá ${pricePreOrder}`,
        rightArea: {
          bgFrame: tvodMBg,
          imageCenter: poster,
        },
        actions: [
          {
            key: "xem_ngay",
            title: `Tiếp tục với ${price}`,
            onClick: () => {
              SegmentManager.segmentAction("dialog_time_out_sale_select", {
                user_type: typeOfUser,
                content_name: get(tvodInfo, "contentInfo.title", "") || "",
                content_id: get(tvodInfo, "contentInfo.id", "") || "",
                flow_name: isVOD
                  ? "time_out_sale_pre_order_have_vod"
                  : "time_out_sale_pre_order",
              });
            },
          },

          {
            key: "de_sau",
            title: "Bỏ qua",
            onClick: () => {
              SegmentManager.segmentAction("dialog_time_out_sale_close", {
                user_type: typeOfUser,
                content_name: get(tvodInfo, "contentInfo.title", "") || "",
                content_id: get(tvodInfo, "contentInfo.id", "") || "",
                flow_name: isVOD
                  ? "time_out_sale_pre_order_have_vod"
                  : "time_out_sale_pre_order",
              });
              history.goBack();
            },
          },
        ],
      },
      onBack: () => {
        SegmentManager.segmentAction("dialog_time_out_sale_close", {
          user_type: typeOfUser,
          content_name: get(tvodInfo, "contentInfo.title", "") || "",
          content_id: get(tvodInfo, "contentInfo.id", "") || "",
          flow_name: isVOD
            ? "time_out_sale_pre_order_have_vod"
            : "time_out_sale_pre_order",
        });
        history.goBack();
      },
    });
  }

  function showDialogTVODEndStream() {
    SegmentManager.segmentAction("dialog_livestream_end_loaded", {
      flow_name: "livestream_end",
    });
    DialogCommon.commonDialog({
      type: DialogTypeCommon.CommonCustomerDialog,
      layoutDirection: "vertical",
      showTermsOfUse: false,
      dataDialog: {
        title: "Chương trình đã kết thúc",
        rightArea: {
          imageCenter: emptyCamera,
        },
        actions: [
          {
            addonBefore: (
              <span>
                Bạn có thể xem lại chương trình ngay khi chúng <br /> tôi có
                thông báo. Vui lòng quay lại trang chủ để <br /> khám phá thêm
                những nội dung hấp dẫn khác
              </span>
            ),
            key: "tro_ve_trang_chu",
            title: "Trở về trang chủ",
            onClick: () => {
              KeepAlive.clearData();
              SegmentManager.segmentAction("dialog_livestream_end_homepage", {
                flow_name: "livestream_end",
              });
              history.push("/");
            },
          },
        ],
      },
      onBack: () => {
        history.goBack();
      },
    });
  }

  const { ref, focusKey, focusSelf } = useFocusable({
    focusKey: EnumPaymentNewUIFocusKey.PaymentRootTVOD,
  });

  React.useEffect(() => {
    focusSelf();
  }, []);

  return isLoading || get(tvodInfo, "isExpire", false) ? (
    <Spinner style={{ height: "100vh" }} />
  ) : (
    <FocusContext.Provider value={focusKey}>
      <div className={styles["payment-wp"]} ref={ref}>
        <StepTwoTVOD
          hotelAccount={hotelAccount}
          show={state.step === EnumPaymentStep.TWO}
          onStepSubmit={onStepTwoSubmit}
          onStepReturn={onBackHandler}
          onEnterCode={onEnterCode}
          tvodInfo={tvodInfo}
          tvodOfferData={tvodOffer}
          config={config}
          napasToken={napasToken}
          shopeePayTokens={shopeePayTokens}
          onPopout={() => {}}
          retryMethod={state.retryMethod}
          cleanRetry={cleanRetry}
          listener={(fn: any) => (stepTwoMethods.current = fn)}
          onStepThreeContinue={onStepThreeContinue}
        />
        {state.paymentResult && (
          <StepThree
            show={state.step === EnumPaymentStep.THREE}
            data={state.paymentResult}
            config={config}
            onPopout={() => {}}
            onBackHome={onStepThreeBackHome} // Go to home
            onContinue={onStepThreeContinue} // Go to previous place (home/content)
            onRetry={onStepThreeRetry} // Retry
            tvodInfo={tvodInfo}
          />
        )}
      </div>
    </FocusContext.Provider>
  );
});

export default PaymentTVOD;
