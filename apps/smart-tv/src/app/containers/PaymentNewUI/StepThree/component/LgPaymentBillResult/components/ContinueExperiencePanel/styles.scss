@use "src/assets/scss/settings/function" as fn;
@use "src/assets/scss/mixin/box" as box;
@use "src/assets/scss/mixin/position" as position;
// @use "src/assets/scss/mixin/fonts" as fonts;

.continue-experience-panel {
  &__container {
    margin: 0 auto;
    margin-top: fn.percent-unit(332);
    width: fn.percent-unit(620);
    display: flex;
    flex-direction: column;

    .row {
      &.title {
        color: #fff;
        font-weight: 500;
        line-height: fn.percent-unit(50);
        font-size: fn.percent-unit(36);
        letter-spacing: fn.percent-unit(0.3);
        margin-bottom: fn.percent-unit(64);
        text-align: center;
      }

      &.auto-text {
        color: #fff;
        font-weight: 500;
        line-height: fn.percent-unit(42);
        font-size: fn.percent-unit(28);
        letter-spacing: fn.percent-unit(0.3);
        margin-top: fn.percent-unit(36);
        text-align: center;
      }

      &.btn-group {
        display: flex;
        justify-content: space-between;
        margin-top: fn.percent-unit(36);
        .btn {
          width: fn.percent-unit(100%);
        }
      }
    }
  }
}
