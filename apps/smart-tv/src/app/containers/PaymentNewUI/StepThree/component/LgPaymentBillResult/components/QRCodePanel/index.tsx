import React, { useCallback, useEffect, useState } from "react";
import QRCodeGenerate from "app/components/Common/QRCodeGenerate";
import { useFocusable } from "core/KeyHandle";
import TipBox from "app/components/Common/Portal/TipBox";
import OpenErrorImg from "assets/images/empty_state/open-error.png";
import { AuthenticationLoginModel } from "app/models";
import SegmentManager from "app/utils/SegmentManager";
import {
  LocalFeatureEventNames,
  LocalFeaturePropertyKeys,
  LocalPropertyValues,
} from "app/components/Tracking/LocalFeatureTracking";
import useDebounce from "hooks/useDebounce";
import { LOGIN_QR_LABEL } from "../AuthPanel/ActionGroup";
import "./styles.scss";

interface Props {
  onReturnPress?: VoidFunction;
  onAuthSuccess: (response: AuthenticationLoginModel) => void;
}
export const QRCODE_KEY = "VN:PAYMENT_QRCODE";
const TIME_OUT = 1000;

const QRCodePanel = React.memo(({ onReturnPress, onAuthSuccess }: Props) => {
  const [userCode, setUserCode] = useState<string>("");
  const userCodeDebounce = useDebounce(userCode, TIME_OUT);
  const [countText, setCountText] = useState<string>("");
  const [errorElement, setErrorElement] =
    useState<React.FunctionComponent | null>(null);

  const { ref } = useFocusable({
    focusKey: QRCODE_KEY,
    onReturnPress,
  });
  const countdownCalback = React.useCallback((_countdown: number, text: string) => {
    setCountText(text);
  }, []);

  const onQRCodeErrorCallback = (element: React.FunctionComponent) => {
    setErrorElement(element);
  };

  const onTracking = useCallback((code: string) => {
    SegmentManager.segmentAction(
      LocalFeatureEventNames.ScanLoginQRCodeButtonSelected,
      {
        [LocalFeaturePropertyKeys.CurrentPage]:
          LocalPropertyValues.TransactionResultScreen,
        [LocalFeaturePropertyKeys.FlowName]:
          LocalPropertyValues.RegisterForPayment,
      [LocalFeaturePropertyKeys.ButtonName]: LOGIN_QR_LABEL,
        [LocalFeaturePropertyKeys.Code]: code,
      }
    );
  }, []);

  useEffect(() => {
    if (userCodeDebounce) {
      onTracking(userCodeDebounce);
    }
  }, [userCodeDebounce]);

  if (errorElement) {
    return (
      <>
        <div className="content__center--app content__center--other content__center--error">
          <div className="error-img">
            <img src={OpenErrorImg} alt="" />
          </div>
          <h3 className="error-title">Không tải được mã QR</h3>
          <p className="error-text">
            Dường như đã có lỗi khi tải mã QR. Vui lòng kiểm tra kết nối mạng và
            thử lại.
          </p>
        </div>
        {onReturnPress && <TipBox zIndex={9999} tip="để quay lại" />}
      </>
    );
  }

  return (
    <div className="qr-code-login-panel" ref={ref}>
      <div className="qr-code-login-panel__container">
        <div className="row title">
          Sử dụng máy ảnh trên điện thoại hoặc máy tính bảng quét mã bên dưới để
          đăng nhập
        </div>
        <div className="row qrcode">
          <QRCodeGenerate
            width={410}
            countdownCalback={countdownCalback}
            onAuthSuccess={onAuthSuccess}
            errorCallback={onQRCodeErrorCallback}
            codeCallback={setUserCode}
          />
        </div>
        <div className="row title">Mã sẽ hết hạn sau {countText}</div>
      </div>
      {onReturnPress && <TipBox zIndex={9999} tip="để quay lại" />}
    </div>
  );
});

export default QRCodePanel;
