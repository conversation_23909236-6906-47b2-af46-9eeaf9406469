import React, { useMemo } from "react";
import get from "lodash/get";
import classNames from "classnames";
import { useFocusable } from "core/KeyHandle";
import { PAYMENT_METHOD } from "app/utils/constants";
import SegmentManager from "app/utils/SegmentManager";
import {
  LocalFeatureEventNames,
  LocalFeaturePropertyKeys,
} from "app/components/Tracking/LocalFeatureTracking";
import AlertCat from "assets/images/empty_state/alert_cat.png";
import { EnumPaymentNewUIFocusKey } from "app/containers/PaymentNewUI/types";
import styles from "./style.module.scss";

interface Props {
  orderId: string;
  paymentMethod: string;
  config: any;
  onPopout?: PopoutHandler;
  onBackHome?: () => void;
}

const PaymentBillResult: React.FC<Props> = React.memo(
  ({ orderId, paymentMethod, config, onBackHome }) => {
    const method = useMemo(() => {
      const mt = (get(config, "methods", []) as any[]).find(
        (element) => element.id === paymentMethod
      );
      return mt;
    }, [paymentMethod, config]);

    const { focusSelf, ref } = useFocusable({
      focusKey: EnumPaymentNewUIFocusKey.PaymentBillResultPending,
      focusImplicitOnHover: true,
      enterOnClick: true,
      onEnterRelease: () => {
        if (onBackHome && typeof onBackHome === "function") {
          SegmentManager.segmentAction(
            LocalFeatureEventNames.ContinueWatchingButtonSelected,
            {
              [LocalFeaturePropertyKeys.CurrentPage]:
                "Giao dịch đang chờ xử lý",
              [LocalFeaturePropertyKeys.TransactionID]: orderId,
            }
          );
          onBackHome();
        }
      },
      onReturnPress: () => {
        if (onBackHome && typeof onBackHome === "function") {
          onBackHome();
        }
      },
    });

    React.useEffect(() => {
      focusSelf();
    }, []);

    return (
      // Block bill
      <div
        className={classNames(
          "block block--payment block--payment-bill block--payment-result block--payment-result--pending",
          styles["payment-bill-result-pending"]
        )}
        ref={ref}
      >
        <div className="block__header">
          <div className="stage stage--payment">
            <div className="stage__wrap">
              <img src={AlertCat} alt="" />
            </div>
          </div>
          <h1 className="block__title font-size-36">
            Giao dịch đang chờ xử lý
          </h1>
        </div>
        <div className="block__body">
          {orderId && (
            <div className="block__text">
              Mã giao dịch: <span>#{orderId}</span>
            </div>
          )}
          <div className="block__text">
            Giao dịch này mất nhiều thời gian để xử lý.
            <br />
            Kết quả giao dịch sẽ được cập nhật sau 24 giờ làm việc tại Tài khoản
            / Lịch sử giao dịch.
          </div>
        </div>
        <div className="block__footer">
          <div className={classNames("block__text", styles["text-footer"])}>
            Nếu cần hỗ trợ vui lòng liên hệ hotline VieON (miễn phí){" "}
            <strong>1800 599920</strong> <br />
            hoặc hotline {method?.name || PAYMENT_METHOD[paymentMethod]}
            <strong>
              {" "}
              {method?.tel || get(config, ["hotline", paymentMethod])}
            </strong>{" "}
            để được hỗ trợ.
          </div>
          <button className="button button--for-light active">
            Xem tiếp VieON
          </button>
        </div>
      </div>
    );
  }
);

export default PaymentBillResult;
