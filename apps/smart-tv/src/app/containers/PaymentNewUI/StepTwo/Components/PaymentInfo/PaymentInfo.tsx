import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import { get } from "lodash";
import classNames from "classnames";
import { animate, linear } from "popmotion";
import Input from "app/components/Common/Input/Input";
import {
  formatEndDate,
  formatMoney,
  formatTvodDevidedPrice,
} from "app/utils/formatNumber";
import { getElStyle, getTranslateY } from "app/utils/Dom";
import useStateRef from "hooks/useStateRef";
import platform from "services/platform";
import { FocusContext, FocusHandlerType, useFocusable, UseFocusableConfig } from "core/KeyHandle";
import FocusableWrapper from "app/components/FocusableWrapper";
import Keyboard from "app/components/Common/KeyBoard";
import { EnumKeyboardType } from "app/components/Common/KeyBoard/types";
import { EnumKeyMap } from "app/components/Common/KeyBoard/constant";
import { EnumPaymentNewUIFocusKey } from "app/containers/PaymentNewUI/types";
import { EnumStepMethod } from "../../types";

type InfoType = "recurring" | "normal" | "tvod";

interface Props {
  step: EnumStepMethod;
  type?: InfoType;
  userAccount?: string;
  pkgName?: string;
  durationName?: string;
  startDate?: string;
  expiredDate?: string;
  price?: number;
  oldPrice?: number;
  totalPrice?: string;
  discountPrice?: number;
  isDisablePromotionInput?: boolean;
  waitingDurMsg?: string;
  consumingDurMsg?: string;
  sectionRef?: React.RefObject<HTMLDivElement>;
  onApplyCoupon: (
    couponCode: string,
    cb: (I: { status?: boolean; data?: any; errorMsg?: string }) => void
  ) => void;
  setApplyCodeFuncRef?: (I: any) => void;
  onCleanCoupon: () => void;
  onClick?: () => void;
  onPopout: PopoutHandler;
  onReturn: () => void;
  onFocus?: VoidFunction;
  onMethodViewReturn: () => void;
  setCouponCodeRef?: (I: any) => void;
  setFocusRef?: (I: any) => void;
  getCouponCodeSub?: (I: any) => void;
  tvodInfo?: any;
  promo?: boolean;
  isShowKeyboard?: boolean;
  setIsShowKeyboard?: React.Dispatch<React.SetStateAction<boolean>>;
}

type RefType = {
  hideKeyBoard: () => void;
};

const OldPaymentInfo = forwardRef<RefType, Props>(
  (
    {
      step,
      type = "normal",
      userAccount,
      pkgName,
      durationName,
      startDate,
      expiredDate,
      price = 0,
      oldPrice = 0,
      totalPrice = "",
      discountPrice = 0,
      isDisablePromotionInput = false,
      waitingDurMsg = "",
      consumingDurMsg = "",
      sectionRef,
      onPopout,
      onApplyCoupon,
      onFocus,
      onCleanCoupon,
      onReturn,
      onMethodViewReturn = () => {},
      setCouponCodeRef,
      setFocusRef,
      setApplyCodeFuncRef,
      onClick,
      getCouponCodeSub,
      tvodInfo,
      promo,
      isShowKeyboard = false,
      setIsShowKeyboard = () => {},
    },
    ref
  ) => {
    const keyboardBoxRef = useRef<HTMLDivElement | null>(null);
    const hasMountKeyboardRef = useRef<boolean>(false);
    const [couponCode, setCouponCode, couponCodeRef] = useStateRef<string>("");
    const [isApplied, setIsApplied] = useStateRef<boolean>(false);
    const [errorMsg, setErrorMsg] = useState<string>("");
    const lastCheckCouponRef = useRef<string>("");
    const isOnApplying = useRef<boolean>(false);
    const infoBoxRef = useRef<HTMLDivElement>(null);

    const isInputCodeRef = useRef<boolean>(false);
    const isInputCode = React.useMemo(() => {
      return step === EnumStepMethod.MethodList && type !== "tvod" && !promo;
    }, [step, promo, type]);

    React.useEffect(() => {
      isInputCodeRef.current = isInputCode;
    }, [isInputCode]);

    const onFocusHandler = useCallback<FocusHandlerType>(
      (_, __, { implicit }) => {
        if (!implicit && isInputCodeRef.current) {
          setIsShowKeyboard(true);
          onFocus?.();
        }
      },
      [onFocus]
    );

    React.useEffect(() => {
      return () => {
        if (step === EnumStepMethod.MethodView && !isApplied) {
          setErrorMsg("");
          setCouponCode("");
        }
      };
    }, [step]);

    const {
      ref: focusRef,
      focusKey,
      focusSelf,
    } = useFocusable({
      focusable: isInputCode,
      focusKey: EnumPaymentNewUIFocusKey.PaymentInfo,
      trackChildren: true,
      onFocus: onFocusHandler,
      onReturnPress: onMethodViewReturn,
    });

    const cleanCoupon = useCallback(() => {
      setIsApplied(false);
      onCleanCoupon();
    }, [onCleanCoupon]);

    const applyCoupon = useCallback(
      (couponCode: string) => {
        if (
          !lastCheckCouponRef.current ||
          lastCheckCouponRef.current !== couponCode
        ) {
          if (isOnApplying.current) return;
          isOnApplying.current = true;
          onApplyCoupon(couponCode, ({ status, errorMsg, data }) => {
            isOnApplying.current = false;
            if (status === true) {
              setIsApplied(true);
            } else if (errorMsg === "SKIP_LOGIN") {
              isOnApplying.current = false;
            } else {
              errorMsg =
                errorMsg || "Mã đã sử dụng. Vui lòng kiểm tra và thử lại.";
              lastCheckCouponRef.current = couponCode;
              setErrorMsg(errorMsg);
              setIsShowKeyboard(true);
            }
          });
        }
      },
      [onApplyCoupon]
    );

    const onKeyboardPopout: PopoutHandler = (direction) => {
      switch (direction) {
        case "up":
          setIsShowKeyboard(false);
          break;
        default:
          break;
      }
    };

    const onKeyboardEnter = (character: string) => {
      const lowerCharacter = character.toLowerCase();
      if (lowerCharacter !== "confirm") {
        setErrorMsg("");
        lastCheckCouponRef.current = "";
      }
      if (lowerCharacter === "confirm") {
        if (
          couponCodeRef.current
          // && !isAppliedRef.current
        ) {
          applyCoupon(couponCodeRef.current);
        } else {
          setErrorMsg("Vui lòng nhập mã ưu đãi.");
        }
        return;
      }

      setCouponCode((value) => {
        if (lowerCharacter !== "submit") {
          setErrorMsg("");
          lastCheckCouponRef.current = "";
        }
        switch (lowerCharacter) {
          case "backspace":
            return value.slice(0, -1);
          case "clear":
            return "";
          default:
            if (value) {
              if (value.length > 20) return value;
              return value + character;
            }
            return character;
        }
      });
    };

    const onKeyboardReturn = () => {
      setIsShowKeyboard(false);
    };

    useLayoutEffect(() => {
      if (isShowKeyboard) {
        const sectionEl = sectionRef && sectionRef.current;
        const infoBoxEl = infoBoxRef && infoBoxRef.current;
        const keyboardBox = keyboardBoxRef && keyboardBoxRef.current;
        if (!sectionEl || !infoBoxEl || !keyboardBox) return;
        const offset = platform.screenType === "FullHD" ? 86 : 57;
        const moveTo =
          infoBoxEl.offsetTop +
          infoBoxEl.offsetHeight -
          keyboardBox.offsetHeight -
          offset;
        animate({
          to: [0, moveTo],
          ease: linear,
          duration: 100,
          onUpdate: (v) => {
            const sectionEl = sectionRef && sectionRef.current;
            if (!sectionEl) return;
            sectionEl.style.transform = `translateY(-${v}px)`;
            keyboardBox.style.bottom = `-${v}px`;
          },
        });
      } else {
        const sectionEl = sectionRef && sectionRef.current;
        if (!sectionEl) return;
        const translateY = getTranslateY(getElStyle(sectionEl));
        animate({
          to: [translateY, 0],
          ease: linear,
          duration: 100,
          onUpdate: (v) => {
            const sectionEl = sectionRef && sectionRef.current;
            if (!sectionEl) return;
            sectionEl.style.transform = `translateY(${v}px)`;
          },
        });
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isShowKeyboard]);

    /* useEffect(() => { */
    /*   const searchParams = new URLSearchParams(location.search); */
    /*   const pkgId = searchParams.get("pkgId"); */
    /*   const durationId = searchParams.get("durationId"); */
    /*   const couponCode = searchParams.get("couponCode"); */
    /*   if (pkgId && durationId && couponCode) { */
    /*     setCouponCode(couponCode); */
    /*     searchParams.delete("couponCode"); */
    /*     history.replace({ */
    /*       ...location, */
    /*       search: searchParams.toString(), */
    /*     }); */
    /*     setTimeout(() => { */
    /*       applyCoupon(couponCode); */
    /*     }); */
    /*   } */
    /* }, []); */

    useEffect(() => {
      if (getCouponCodeSub && typeof getCouponCodeSub === "function") {
        getCouponCodeSub(() => {
          return couponCodeRef.current;
        });
      }
      if (setCouponCodeRef && typeof setCouponCodeRef === "function") {
        setCouponCodeRef(setCouponCode);
      }
      if (setFocusRef && typeof setFocusRef === "function") {
        setFocusRef(() => {
          setIsShowKeyboard(false);
          focusSelf();
        });
      }
      if (setApplyCodeFuncRef && typeof setApplyCodeFuncRef === "function") {
        setApplyCodeFuncRef(applyCoupon);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [focusSelf]);

    const handleClick = (type: number) => () => {
      if (onClick && typeof onClick === "function") {
        onClick();
      }
      switch (type) {
        case 0:
          onFocus?.();
          setIsShowKeyboard(true);
          break;
        case 1:
          setIsShowKeyboard(false);
          if (isApplied) {
            cleanCoupon();
          } else if (couponCode) {
            applyCoupon(couponCode);
          }
          break;
        default:
          break;
      }
    };

    useImperativeHandle(ref, () => ({
      hideKeyBoard() {
        setIsShowKeyboard(false);
      },
    }));

    function makeNote() {
      const preOrderStartedAt = get(tvodInfo, "biz_info.pre_order.started_at");
      const preOrderEndedAt = get(tvodInfo, "biz_info.pre_order.until");
      const now = new Date().getTime() / 1000;
      const isPreOrderAvailable =
        now >= preOrderStartedAt && now <= preOrderEndedAt;
      const isSimulcast = get(tvodInfo, "isSimulcast", false);
      const isLiveEvent = get(tvodInfo, "isLiveEvent", false);
      const isVOD = get(tvodInfo, "contentInfo.content_id", "") !== "";

      if (step) {
        if (type === "tvod") {
          return (
            <>
              {isSimulcast ? (
                <>
                  <div className="badge">
                    Bạn có thể xem ngay khi nội dung phát hành{" "}
                  </div>
                  <div className="badge">
                    *Thời hạn của bạn tính từ lúc tập cuối ra mắt, bạn có{" "}
                    <strong>{waitingDurMsg}</strong> để bắt đầu xem và{" "}
                    <strong>{consumingDurMsg}</strong> để xem hết kể từ lúc bắt
                    đầu xem
                  </div>
                </>
              ) : isLiveEvent ? (
                <>
                  {isVOD ? (
                    <div className="badge">
                      *Sau khi nội dung phát sóng , bạn có{" "}
                      <strong>{waitingDurMsg}</strong> để bắt đầu xem lại và{" "}
                      <strong>{consumingDurMsg}</strong> để xem hết kể từ lúc
                      bạn bắt đầu xem
                    </div>
                  ) : null}
                </>
              ) : (
                <div className="badge">
                  *Nội dung chưa xem được lưu trong{" "}
                  <strong>{waitingDurMsg}</strong>. Sau khi bấm xem, bạn có{" "}
                  <strong>{consumingDurMsg}</strong> để xem hết.
                </div>
              )}
              <div className="badge">
                Bằng việc thanh toán, bạn đồng ý với{" "}
                <span>Điều Khoản Sử Dụng</span>,{" "}
                <span>Chính Sách Và Quy Định Chung</span> của VieON
              </div>
            </>
          );
        }
        if (type === "recurring") {
          return (
            <div className="badge">
              Bằng việc thanh toán, bạn xác nhận đã đọc và đồng ý với{" "}
              <span>Hợp đồng và Chính sách</span> của VieON{" "}
              <span> (https://vieon.vn/quy-dinh) </span> và chấp nhận cho VieON
              tự động gia hạn gói dịch vụ khi hết hạn. Bạn có thể hủy gia hạn
              bất kỳ lúc nào.
            </div>
          );
        }
        return (
          <div className="badge">
            Bằng việc thanh toán, bạn xác nhận đã đọc và đồng ý với{" "}
            <span>Hợp đồng và Chính sách</span> của VieON{" "}
            <span>(https://vieon.vn/quy-dinh)</span>
          </div>
        );
      }
      if (type === "tvod") {
        if (isSimulcast) {
          return (
            <>
              {isPreOrderAvailable ? (
                <div className="badge">
                  Bạn có thể xem ngay khi nội dung phát hành
                </div>
              ) : null}
              <div className="badge">
                *Thời hạn của bạn tính từ lúc tập cuối ra mắt, bạn có{" "}
                <strong>{waitingDurMsg}</strong> để bắt đầu xem và{" "}
                <strong>{consumingDurMsg}</strong> để xem hết kể từ lúc bắt đầu
                xem
              </div>
            </>
          );
        }
        if (isLiveEvent) {
          return (
            <>
              {isVOD ? (
                <div className="badge">
                  *Sau khi nội dung phát sóng , bạn có{" "}
                  <strong>{waitingDurMsg}</strong> để bắt đầu xem lại và{" "}
                  <strong>{consumingDurMsg}</strong> để xem hết kể từ lúc bạn
                  bắt đầu xem
                </div>
              ) : null}
            </>
          );
        }
        return (
          <>
            <div className="badge">
              *Nội dung chưa xem được lưu trong <strong>{waitingDurMsg}</strong>
              . Sau khi bấm xem, bạn có <strong>{consumingDurMsg}</strong> để
              xem hết.
            </div>
          </>
        );
      }

      return null;
    }

    const isVOD = get(tvodInfo, "contentInfo.content_id", "") !== "";

    const propUseFocusBtn = {
      enterOnClick: true,
      focusImplicitOnHover: true,
    } as UseFocusableConfig;

    return (
      <FocusContext.Provider value={focusKey}>
        <div className="block__body" ref={infoBoxRef}>
          <div className="grid">
            <div className="text text--muted col-6">Tài khoản VieON</div>
            <div className="text col-6">{userAccount || "-"}</div>
          </div>
          {pkgName && (
            <div className="grid">
              <div className="text text--muted col-6">
                {type === "tvod" ? "Tên nội dung" : "Tên gói"}
              </div>
              <div className="text col-6">{pkgName}</div>
            </div>
          )}
          {durationName && (
            <div className="grid">
              <div className="text text--muted col-6">
                {type === "tvod" ? "Thời hạn*" : "Thời hạn gói"}
              </div>
              <div className="text col-6">
                {!isVOD && tvodInfo?.isLiveEvent
                  ? "Khi chương trình kết thúc"
                  : durationName}
              </div>
            </div>
          )}
          {startDate && (
            <div className="grid">
              <div className="text text--muted col-6">
                {type === "tvod" ? "Ngày giao dịch" : "Ngày hiệu lực"}
              </div>
              <div className="text col-6">{formatEndDate(startDate)}</div>
            </div>
          )}
          {type === "recurring" ? (
            <>
              <div className="grid">
                <div className="text text--muted col-6">Sử dụng đến</div>
                <div className="text col-6">Khi bạn huỷ</div>
              </div>
              {expiredDate && (
                <div className="grid">
                  <div className="text text--muted col-6">
                    Kì thanh toán tiếp theo
                  </div>
                  <div className="text col-6">{formatEndDate(expiredDate)}</div>
                </div>
              )}
            </>
          ) : (
            expiredDate && (
              <div className="grid">
                <div className="text text--muted col-6">Sử dụng đến</div>
                <div className="text col-6">{formatEndDate(expiredDate)}</div>
              </div>
            )
          )}
          {(price > 0 || oldPrice > 0) && (
            <div className="grid">
              <div className="text text--muted col-6">Trị giá</div>
              <div className="text col-6">
                {type === "tvod" ? (
                  <>{formatTvodDevidedPrice(price)} ₫</>
                ) : (
                  <>{formatMoney(price || oldPrice)} ₫</>
                )}
              </div>
            </div>
          )}
          {oldPrice > 0 ? (
            oldPrice - price + discountPrice > 0 ? (
              <div className="grid">
                <div className="text text--muted col-6">Giảm giá</div>
                <div className="text col-6">
                  -{formatMoney(oldPrice - price + discountPrice)} ₫
                </div>
              </div>
            ) : (
              <div className="grid">
                <div className="text text--muted col-6">Giảm giá</div>
                <div className="text col-6">0 ₫</div>
              </div>
            )
          ) : discountPrice > 0 ? (
            <div className="grid">
              <div className="text text--muted col-6">Giảm giá</div>
              <div className="text col-6">
                -{" "}
                {type === "tvod" ? (
                  <>{formatTvodDevidedPrice(discountPrice)} ₫</>
                ) : (
                  <>{formatMoney(discountPrice)} ₫</>
                )}
              </div>
            </div>
          ) : (
            <div className="grid">
              <div className="text text--muted col-6">Giảm giá</div>
              <div className="text col-6">0 ₫</div>
            </div>
          )}
          {isInputCode && (
            <div className="input-group" ref={focusRef}>
              <FocusableWrapper
                focusKey="VN:PAYMENT_INFO_INPUT"
                onClick={handleClick(0)}
                focusImplicitOnHover
                enterOnClick
                onArrowPress={(direction) => {
                  if (direction === "left") {
                    onPopout(direction);
                    hasMountKeyboardRef.current = false;
                    return false;
                  }
                  return true;
                }}
                focusable
                onReturnPress={onReturn}
              >
                {({ focused, ref }) => (
                  <Input
                    className={classNames("input--payment-discount", {
                      focus: focused || isShowKeyboard,
                      error: !!errorMsg,
                      disabled: isDisablePromotionInput,
                    })}
                    placeholder="Mã khuyến mãi"
                    value={couponCode}
                    notifyMsg={errorMsg}
                    rootRef={ref}
                  />
                )}
              </FocusableWrapper>
              <FocusableWrapper
                focusKey="VN:PAYMENT_INFO_BUTTON"
                onClick={handleClick(1)}
                {...propUseFocusBtn}
                focusable={!!couponCode || isApplied}
                onReturnPress={onReturn}
              >
                {({ focused, ref }) => {
                  return (
                    <button
                      className={classNames("button button--for-light", {
                        focus: focused,
                      })}
                      disabled={!couponCode && !isApplied}
                      ref={ref}
                    >
                      {isApplied ? "Hủy" : "Xác nhận"}
                    </button>
                  );
                }}
              </FocusableWrapper>
            </div>
          )}
          <div className="grid price price--total">
            <div className="col-6 price__item price__item-label">
              Thành tiền
            </div>
            <div className="col-6 price__item">
              {type === "tvod" ? (
                totalPrice
              ) : (
                <>
                  {formatMoney(price - discountPrice)}
                  <span className="price__unit"> ₫</span>
                </>
              )}
            </div>
          </div>
        </div>
        <div className="block__footer">{makeNote()}</div>
        {isShowKeyboard && isInputCode && (
          <div
            className="keyboard__wrapper keyboard__wrapper--code"
            ref={keyboardBoxRef}
          >
            <div className="keyboard-container">
              <Keyboard
                type={EnumKeyboardType.PAYMENT}
                focusKey={EnumKeyMap.KEY_O}
                onPopout={onKeyboardPopout}
                onEnter={onKeyboardEnter}
                onReturn={onKeyboardReturn}
                capslock
              />
            </div>
          </div>
        )}
      </FocusContext.Provider>
    );
  }
);

export default OldPaymentInfo;
