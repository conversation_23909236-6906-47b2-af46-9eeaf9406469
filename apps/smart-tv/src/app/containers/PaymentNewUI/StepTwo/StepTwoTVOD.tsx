import classNames from "classnames";
import get from "lodash/get";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useHistory, useLocation } from "react-router-dom";
import useStateRef from "hooks/useStateRef";
import Logo from "app/components/Common/Logo";
import Message from "app/components/Common/Message";
import { RootState } from "app/store/store";
import { FocusContext, useFocusable } from "core/KeyHandle";
import { ReleasedTimeMsg } from "app/components/Common/CommonTagsLuna";
import Icon from "app/components/Common/Icon";
import { RemainingTimeMsg } from "app/components/Common/TvodTagLuna";
import DialogCommon from "app/components/DialogCommon";
import { DialogType as DialogTypeCommon, DialogType as NewDialogType } from "app/components/DialogCommon/types";
import { ROUTES } from "app/utils/constants";
import { setUrlSearchKey } from "app/utils/Dom";
import { convertTimeToDDMM, convertTimeToHHMM, makeRemainingTimeStr } from "app/utils/formatTime";
import KeepAlive from "app/utils/KeepAlive";
import SegmentManager from "app/utils/SegmentManager";
import bgRightFarm from "assets/images/tvod-mbg.png";
import useGetUserTypeName from "hooks/useGetUserTypeName";
import { MESSAGES } from "types/common/Message";
import withFocusByRender from "app/components/HOC/withFocusByRender";
import { ProductOwnerType } from "types/endpoint";
import { GlobalFeatureFlowName } from "app/components/Tracking/GlobalFeatureTracking";
import MethodView from "./Components/MethodView";
import { generateObjectClassPayment } from "./transform";
import { EnumStepMethod } from "./types";
import OldPaymentInfo from "./Components/PaymentInfo/PaymentInfo";
import OldPaymentMethodList from "./Components/PaymentMethodList/PaymentMethodList";
import OldMethodTitle from "./Components/MethodTitle/MethodTitle";
import styles from "./styles.module.scss";
import MethodTitle from "./Components/MethodTitle";
import PaymentMethodList from "./Components/PaymentMethodList";
import PaymentInfo from "./Components/PaymentInfo";
import { getTVODOffer } from "../hook";
import PaymentStepView from "../Components/PaymentStepView";
import { EnumPaymentNewUIFocusKey } from "../types";
import { EnumAuthTitle } from "../../Authentication/type";
import PaymentHeader from "../Components/PaymentHeader";

interface Props {
  hotelAccount: boolean;
  onStepSubmit: any;
  onStepReturn: () => void;
  onEnterCode: () => void;
  tvodInfo: any;
  tvodOfferData: any;
  config: any;
  napasToken: any[];
  shopeePayTokens: any[];
  onPopout: PopoutHandler;
  cleanRetry: () => void;
  retryMethod: string;
  listener?: (I: any) => void;
  onStepThreeContinue: () => void;
}

const TVOD_PAYMENT_METHODS = ["napas", "zalopay", "viettelpay", "vnpay", "moca", "shopeepay", "momo"];

const StepTwoTVOD = withFocusByRender(
  ({
    hotelAccount,
    onStepSubmit,
    onStepReturn: onStepReturnProps,
    onEnterCode,
    tvodInfo,
    tvodOfferData,
    config,
    napasToken,
    shopeePayTokens,
    onPopout,
    retryMethod,
    cleanRetry,
    listener,
    onStepThreeContinue
  }: Props) => {
    const [stepMethod, setStepMethod] = useState<EnumStepMethod>(EnumStepMethod.MethodList);
    const sectionRef = useRef<HTMLDivElement>(null);
    const location = useLocation();
    const locationRef = useRef<any>();
    const imageDialog = useRef("");
    const priceCheckPreorder = useRef("");
    const tvodInfoRef = useRef<{
      isSimulcast: boolean;
      releasedTimeMsg: string;
    }>();
    const [tvodOffer, setTVODOffer] = useState(tvodOfferData);

    const { ref, focusKey, focusSelf, setFocus } = useFocusable({
      focusKey: EnumPaymentNewUIFocusKey.PaymentStepTwoTVOD,
      autoRestoreFocus: false,
      onReturnPress: onStepReturnProps
    });

    React.useEffect(() => {
      focusSelf();
    }, []);

    useEffect(() => {
      locationRef.current = location;
      if (imageDialog.current === "") {
        imageDialog.current = get(locationRef.current, "state.image", "");
      }
      if (priceCheckPreorder.current !== "") {
        priceCheckPreorder.current = get(locationRef.current, "state.price", "") || "";
      }
      if (priceCheckPreorder.current !== "") {
        priceCheckPreorder.current = get(tvodOffer, "total_price_msg", "") || "";
      }
    }, [location]);
    const history = useHistory();
    const [isDisablePromotionInput, setDisablePromotionInput, isDisablePromotionInputRef] = useStateRef(true);
    const [methods, setMethods] = useState<string[]>([]);
    const [discountInfo, setDiscountInfo] = useStateRef<{
      couponCode: string;
      discountPrice: number;
      availableMethods: string[];
      newExpiredDate: string;
    }>({
      couponCode: "",
      discountPrice: 0,
      availableMethods: [],
      newExpiredDate: ""
    });
    const [activeMethods, setActiveMethods] = useState<string[]>([]);
    const [promotionList, setPromotionList] = useState<string[]>([]);
    const [chosenMethod, setChosenMethod, chosenMethodRef] = useStateRef<string>("");
    const [isLoading, setIsLoading, isLoadingRef] = useStateRef<boolean>(false);
    const [paidData, setPaidData, paidDataRef] = useStateRef<{
      paidMessageType: number;
      paidMessage: string;
      buttonMsg: string;
    }>({
      paidMessageType: 0,
      paidMessage: "",
      buttonMsg: ""
    });
    const getFocusMethodRef = useRef<any>(null);
    const getCouponCodeRef = useRef<any>(null);
    const payemntInfoRef = useRef<any>(null);
    const userAccount = useSelector((state: RootState) => {
      const profile = get(state, "app.profile", {});
      return get(profile, "mobile") || get(profile, "email") || get(profile, "givenName") || "";
    });
    const isAuthen = useSelector((state: RootState) => {
      return get(state, "app.isAuthen", "");
    });
    const userId = useSelector((state: RootState) => {
      const profile = get(state, "app.profile", {});
      return get(profile, "id");
    });

    const userIsPremium = useSelector((state: RootState) => {
      const profile = get(state, "app.profile", {});
      return get(profile, "isPremium");
    });

    const typeOfUser = useGetUserTypeName();

    const changeStepMethodView = useCallback((method: string) => {
      setChosenMethod(method);
      setStepMethod(EnumStepMethod.MethodView);
    }, []);

    useEffect(() => {
      const searchParams = new URLSearchParams(location.search);
      setUrlSearchKey(searchParams, "couponCode", get(discountInfo, ["couponCode"], ""));
      history.replace({
        ...location,
        search: searchParams.toString()
      });
    }, [discountInfo.couponCode]);

    useEffect(() => {
      if (retryMethod) {
        setChosenMethod(retryMethod);
        setStepMethod(EnumStepMethod.MethodView);
        cleanRetry();
      }
    }, [retryMethod]);

    useEffect(() => {
      // let activeMethods = [...methods];
      // activeMethods = activeMethods.filter(
      //   (method: string) => TVOD_PAYMENT_METHODS.indexOf(method) >= 0
      // );
      const packageTVOD = get(config, "tvod_methods", []);
      const methods = get(config, "tvod_methods.methods", []) as string[];
      setMethods(methods);
      let activeMethods = get(packageTVOD, "methods_active", []) as string[];
      activeMethods = activeMethods.filter((method: string) => TVOD_PAYMENT_METHODS.indexOf(method) >= 0);
      setActiveMethods(activeMethods);
      // Make promotion list
      const methodsPromotion = get(packageTVOD, "methods_promotion");
      const promotionList = methods.map((method) => {
        if (!methodsPromotion) {
          return "";
        }
        return get(methodsPromotion, [method], "");
      });
      setPromotionList(promotionList);
    }, [config]);

    const onStepReturn = (keepFocus?: boolean) => {
      if (!keepFocus) {
        setFocus(EnumPaymentNewUIFocusKey.PaymentMethodList);
      }
      setDiscountInfo({
        couponCode: "",
        discountPrice: 0,
        availableMethods: [],
        newExpiredDate: ""
      });
      onStepReturnProps();
    };
    const onPaymentMethodListPopout: PopoutHandler = useCallback((direction) => {
      if (!isDisablePromotionInputRef.current && direction === "right") {
        setFocus(EnumPaymentNewUIFocusKey.PaymentInfo);
      }
    }, []);
    const onPaymentInfoPopout: PopoutHandler = useCallback((direction) => {
      if (direction === "left") {
        // setFocusIndex(0);
        setFocus(EnumPaymentNewUIFocusKey.PaymentMethodList);
      }
    }, []);

    const onMethodChange = useCallback((method: string) => {}, []);

    const onCleanCoupon = useCallback(() => {
      setDiscountInfo({
        couponCode: "",
        discountPrice: 0,
        availableMethods: [],
        newExpiredDate: ""
      });
    }, []);

    const onApplyCoupon = useCallback((couponCode, cb) => {}, []);

    const onMethodSubmit = useCallback(
      (methodC: string, id?: number) => {
        let method = methodC;
        if (method === "napas_create") {
          method = "napas";
        }
        if (isAuthen) {
          const isLiveEvent = get(tvodInfo, "isLiveEvent", false);
          if (isLiveEvent) {
            const tvod_product_id = get(tvodInfo, "biz_info.tvod_product_id", "");
            getTVODOffer(tvod_product_id, (data) => {
              if (data) {
                setTVODOffer(data);
              }
              const totalPrice = get(data, "total_price_msg", "") || "";
              if (totalPrice !== "" && priceCheckPreorder.current !== "" && totalPrice !== priceCheckPreorder.current) {
                showDialogTVODPreOrderToOrder(method);
              } else {
                changeStepMethodView(method);
              }
            });
          } else {
            changeStepMethodView(method);
          }
        } else {
          saveState(method);
          const searchParams = new URLSearchParams(location.search);
          history.replace({
            pathname: ROUTES.LOGIN_STEP,
            state: {
              successLocation: {
                ...locationRef.current,
                state: {
                  ...locationRef.current?.state,
                  referal: "Đăng nhập",
                  image: imageDialog.current,
                  price: priceCheckPreorder.current
                }
                /* search: searchParams.toString(), */
              },
              successMsg: "Đăng nhập tài khoản thành công.<br/>Vui lòng tiếp tục thanh toán",
              successTvodSvodSignInMsg:
                searchParams.get("flow") === "tvod-svod" ? "Chào mừng bạn trở lại với VieON" : null,
              successMsgSignUp: "Tạo tài khoản thành công.<br/>Vui lòng tiếp tục thanh toán",
              title: EnumAuthTitle.RegisterByPayment,
              flowName: GlobalFeatureFlowName.RegistrationForPaymentTvod,
              authenFlowName: GlobalFeatureFlowName.RegistrationForPaymentTvod
            },
            search: `?flow=${searchParams.get("flow") === "tvod-svod" ? searchParams.get("flow") : "tvod"}`
          });
          /*
        DialogCommon.commonDialog({
          keyName: "dialog_revise_required_login",
          showTipBoxGoback: true,
          type: DialogType.AnonymousUserPaymentRequireLogin,
          buttonsEventClicks: [
            () => {
              saveState(method);
              history.push({
                pathname: ROUTES.LOGIN_PAGE,
                state: {
                  successLocation: {
                    ...locationRef.current,
                    state: {
                      ...locationRef.current?.state,
                      referal: "Đăng nhập",
                      image: imageDialog.current,
                      price: priceCheckPreorder.current,
                    },
                  },
                  successMsg:
                    "Đăng nhập thành công.<br/>Vui lòng tiếp tục thanh toán.",
                  flowName: GlobalFeatureFlowName.RegistrationForPaymentTvod,
                },
                search: "?flow=tvod",
              });
            },
            () => {
              saveState(method);
              history.push({
                pathname: ROUTES.SIGNUP_PAGE,
                state: {
                  successLocation: {
                    ...locationRef.current,
                    state: {
                      ...locationRef.current?.state,
                      referal: "Đăng ký",
                      image: imageDialog.current,
                      price: priceCheckPreorder.current,
                    },
                  },
                  successMsg:
                    "Tạo tài khoản thành công.<br/>Vui lòng tiếp tục thanh toán.",
                },
                search: "?flow=tvod",
              });
            },
          ],
          onBack: () => {},
        });
        */
        }
      },
      [userIsPremium, location]
    );

    function showDialogTVODPreOrderToOrder(method: string) {
      const title = get(tvodOffer, "product_name_msg", "");
      const poster = imageDialog.current;
      const price = get(tvodOffer, "total_price_msg", "");
      const pricePreOrder = priceCheckPreorder.current;
      const waitingDurMsg = get(tvodOffer, "waiting_dur_msg", "");
      const consumingDurMsg = get(tvodOffer, "consuming_dur_msg", "");
      const isVOD = !!(get(tvodInfo, "contentInfo.content_id", "") || "");

      SegmentManager.segmentAction("dialog_time_out_sale_loaded", {
        user_type: typeOfUser,
        content_name: get(tvodInfo, "contentInfo.title", "") || "",
        content_id: get(tvodInfo, "contentInfo.id", "") || "",
        flow_name: isVOD ? "time_out_sale_pre_order_have_vod" : "time_out_sale_pre_order"
      });

      DialogCommon.commonDialog({
        keyName: "dialog_step2_tvod_pre_order_to_order",
        type: DialogTypeCommon.CommonCustomerDialog,
        layoutDirection: "vertical",
        showTermsOfUse: false,
        description: (
          <div
            style={{
              display: "flex",
              flexDirection: "column"
            }}
          >
            {isVOD ? (
              <>
                <span>Có thể xem lại sau khi chương trình kết thúc</span>
                <br />
                <div style={{ marginBottom: "10px" }}>
                  <Icon width={27} height={27} name="vie-time" />{" "}
                  <span>
                    <b>{waitingDurMsg}</b> để bắt đầu xem nội dung
                  </span>
                </div>
                <div>
                  <Icon width={27} height={27} name="vie-time" />{" "}
                  <span>
                    <b>{consumingDurMsg}</b> để xem hết kể từ lúc bắt đầu xem
                  </span>
                </div>
              </>
            ) : null}
          </div>
        ),
        dataDialog: {
          title: `Hết hạn đặt trước “${title}\" với giá ${pricePreOrder}`,
          rightArea: {
            bgFrame: bgRightFarm,
            imageCenter: poster
          },
          actions: [
            {
              key: "xem_ngay",
              title: `Tiếp tục với ${price}`,
              onClick: () => {
                changeStepMethodView(method);
                SegmentManager.segmentAction("dialog_time_out_sale_select", {
                  user_type: typeOfUser,
                  content_name: get(tvodInfo, "contentInfo.title", "") || "",
                  content_id: get(tvodInfo, "contentInfo.id", "") || "",
                  flow_name: isVOD ? "time_out_sale_pre_order_have_vod" : "time_out_sale_pre_order"
                });
              }
            },

            {
              key: "de_sau",
              title: "Bỏ qua",
              onClick: () => {
                SegmentManager.segmentAction("dialog_time_out_sale_close", {
                  user_type: typeOfUser,
                  content_name: get(tvodInfo, "contentInfo.title", "") || "",
                  content_id: get(tvodInfo, "contentInfo.id", "") || "",
                  flow_name: isVOD ? "time_out_sale_pre_order_have_vod" : "time_out_sale_pre_order"
                });
              }
            }
          ]
        },
        onBack: () => {
          SegmentManager.segmentAction("dialog_time_out_sale_close", {
            user_type: typeOfUser,
            content_name: get(tvodInfo, "contentInfo.title", "") || "",
            content_id: get(tvodInfo, "contentInfo.id", "") || "",
            flow_name: isVOD ? "time_out_sale_pre_order_have_vod" : "time_out_sale_pre_order"
          });
        }
      });
    }

    const onMethodViewFail = useCallback(() => {
      setStepMethod(EnumStepMethod.MethodList);
    }, []);

    const onMethodViewSubmit = useCallback((data) => {
      setChosenMethod("");
      if (onStepSubmit && typeof onStepSubmit === "function") {
        onStepSubmit({
          ...data,
          paidData: paidDataRef.current
        });
      }
    }, []);

    const onMethodViewReturn = useCallback(() => {
      setStepMethod(EnumStepMethod.MethodList);
    }, []);

    // Keep last position
    const jumpMethodRef = useRef<any>(null);
    const setCouponCodeRef = useRef<any>(null);
    const setFocusInfoRef = useRef<any>(null);
    const saveState = (method: string) => {
      KeepAlive.saveData({
        path: "#/paymentTVOD",
        focus: { x: 0, y: 0 },
        extra: {
          chosenMethod: getFocusMethodRef.current && getFocusMethodRef.current(),
          chosenMethodText: method,
          couponCode: getCouponCodeRef.current && getCouponCodeRef.current(),
          activeAtSubmitCode: false
        }
      });
    };

    const checkAutoSubmitMethod = (method: string) => {
      const status = get(tvodInfo, "benefit_info.type", 0) || 0;
      const isLiveEvent = get(tvodInfo, "isLiveEvent", false);
      if (isLiveEvent) {
        const isExpire = get(tvodInfo, "isExpire", false);
        if (isExpire) {
          showDialogLiveEventEndOrder();
        } else if (status === 0 || status === -1) {
          const totalPrice = get(tvodOffer, "total_price_msg", "") || "";
          if (totalPrice !== "" && priceCheckPreorder.current !== "" && totalPrice !== priceCheckPreorder.current) {
            showDialogTVODPreOrderToOrder(method);
          } else {
            changeStepMethodView(method);
          }
        } else {
          showDialogLiveEventOrder();
        }
        return;
      }

      if (status === 0 || status === -1) {
        changeStepMethodView(method);
      } else {
        // const isSimulcast = tvodInfo.isSimulcast;

        const preOrderEndedAt = tvodInfo?.biz_info?.pre_order?.until || 0;
        const preOrderStartedAt = tvodInfo?.biz_info?.pre_order?.started_at || 0;
        const now = new Date().getTime() / 1000;
        const isPreOrderAvailable = now >= preOrderStartedAt && now <= preOrderEndedAt;
        const productOwnerType = tvodInfo?.benefit_info?.product_owner_type;
        const isPreOrderOwner =
          productOwnerType === ProductOwnerType.preOrder || productOwnerType === ProductOwnerType.preOrderNormal;
        const searchParams = new URLSearchParams(location.search);
        if (searchParams.get("flow") === "tvod-svod" && userIsPremium === 1) {
          return;
        }

        if (isPreOrderOwner) {
          const stepReturn = () => {
            SegmentManager.segmentAction("dialog_rent_loaded_watch", {
              user_type: typeOfUser,
              content_name: get(tvodInfo, "contentInfo.title", "") || "",
              content_id: get(tvodInfo, "contentInfo.id", "") || ""
            });
            onStepThreeContinue();
            Message.open(MESSAGES.tvodRemindOnAir);
          };

          SegmentManager.segmentAction("dialog_rent_loaded", {
            user_type: typeOfUser,
            content_name: get(tvodInfo, "contentInfo.title", "") || "",
            content_id: get(tvodInfo, "contentInfo.id", "") || ""
          });
          DialogCommon.commonDialog({
            keyName: "dialog_pre_order_owner",
            type: NewDialogType.CommonCustomerDialog,
            direction: "vertical",
            layoutDirection: "vertical",
            dataDialog: {
              title: `Bạn đã đặt trước "${tvodOffer?.product_name_msg}"`,
              rightArea: {
                bgFrame: imageDialog.current
              },
              actions: [
                {
                  key: "tiep_tuc",
                  title: "Tiếp tục",
                  onClick: stepReturn
                }
              ]
            },
            onBack: stepReturn,
            showTermsOfUse: false,
            description: <ReleasedTimeMsg releasedTimeMsg={tvodInfo.releasedTimeMsg} />,
            showTipBoxGoback: false,
            executeActionBeforeClose: true
          });
        } else {
          const end_at = get(tvodInfo, "benefit_info.end_at", 0) || 0;
          const title = `Bạn đã thuê "${get(tvodOffer, "product_name_msg", "")}"`;
          const time = makeRemainingTimeStr(end_at, false);
          const image = imageDialog.current;
          DialogCommon.commonDialog({
            keyName: "dialog_have_order",
            type: NewDialogType.CommonCustomerDialog,
            direction: "vertical",
            layoutDirection: "vertical",
            dataDialog: {
              title,
              rightArea: {
                bgFrame: bgRightFarm,
                imageCenter: image
              },
              actions: [
                {
                  key: "xem_ngay",
                  title: "Xem ngay",
                  onClick: () => {
                    onStepReturn(true);
                  }
                }
              ]
            },
            onBack: () => {
              onStepReturn(true);
            },
            showTermsOfUse: false,
            description: <RemainingTimeMsg remainingTimeMsg={time} sameTextColor={false} />,
            showTipBoxGoback: true,
            executeActionBeforeClose: true
          });
        }
      }
    };

    function showDialogLiveEventEndOrder() {
      const isVOD = !!(get(tvodInfo, "contentInfo.content_id", "") || "");

      SegmentManager.segmentAction("dialog_missed_event_loaded", {
        user_type: typeOfUser,
        content_name: get(tvodInfo, "contentInfo.title", "") || "",
        content_id: get(tvodInfo, "contentInfo.id", "") || "",
        flow_name: isVOD ? "missed_event_have_vod" : "missed_event"
      });
      DialogCommon.commonDialog({
        keyName: "dialog_live_event_end_order",
        type: DialogTypeCommon.CommonCustomerDialog,
        layoutDirection: "vertical",
        showTermsOfUse: false,
        dataDialog: {
          title: `Hết hạn thuê ${tvodOffer.product_name_msg || ""}`,
          rightArea: {
            bgFrame: bgRightFarm,
            imageCenter: imageDialog.current
          },
          actions: [
            {
              addonBefore: (
                <span>
                  Trở về trang chủ để khám phá thêm những nội <br /> dung hấp dẫn khác
                </span>
              ),
              key: "tro_ve_trang_chu",
              title: "Trở về trang chủ",
              onClick: () => {
                KeepAlive.clearData();
                history.push("/");

                SegmentManager.segmentAction("dialog_missed_event_homepage", {
                  user_type: typeOfUser,
                  content_name: get(tvodInfo, "contentInfo.title", "") || "",
                  content_id: get(tvodInfo, "contentInfo.id", "") || "",
                  flow_name: isVOD ? "missed_event_have_vod" : "missed_event"
                });
              }
            }
          ]
        },
        onBack: () => {
          SegmentManager.segmentAction("dialog_missed_event_close", {
            user_type: typeOfUser,
            content_name: get(tvodInfo, "contentInfo.title", "") || "",
            content_id: get(tvodInfo, "contentInfo.id", "") || "",
            flow_name: isVOD ? "missed_event_have_vod" : "missed_event"
          });
        }
      });
    }
    function showDialogLiveEventOrder() {
      const product_owner_type = get(tvodInfo, "benefit_info.product_owner_type", 0) || 0; // 1: Thue, 2 đặt trước giá giảm, 3 đặt trước giá gốc
      const title = `Bạn ${
        product_owner_type <= 1 ? "đã thuê" : "đã đặt trước"
      } "${get(tvodOffer, "product_name_msg", "")}"`;
      const strTime = get(tvodInfo, "contentInfo.str_to_time", "") || "";
      const time = convertTimeToHHMM(strTime);
      const day = convertTimeToDDMM(strTime);

      SegmentManager.segmentAction("dialog_rent_loaded", {
        user_type: typeOfUser,
        content_name: get(tvodInfo, "contentInfo.title", "") || "",
        content_id: get(tvodInfo, "contentInfo.id", "") || ""
      });

      DialogCommon.commonDialog({
        keyName: "dialog_live_event_order",
        type: NewDialogType.CommonCustomerDialog,
        direction: "vertical",
        layoutDirection: "vertical",
        dataDialog: {
          title,
          rightArea: {
            bgFrame: bgRightFarm,
            imageCenter: imageDialog.current
          },
          actions: [
            {
              key: "xem_ngay",
              title: "Xem ngay",
              onClick: () => {
                SegmentManager.segmentAction("dialog_rent_loaded_watch", {
                  user_type: typeOfUser,
                  content_name: get(tvodInfo, "contentInfo.title", "") || "",
                  content_id: get(tvodInfo, "contentInfo.id", "") || ""
                });
                onStepThreeContinue();
              }
            },
            {
              key: "tro_ve_trang_chu",
              title: "Trở về trang chủ",
              onClick: () => {
                SegmentManager.segmentAction("dialog_rent_loaded_homepage", {
                  user_type: typeOfUser,
                  content_name: get(tvodInfo, "contentInfo.title", "") || "",
                  content_id: get(tvodInfo, "contentInfo.id", "") || ""
                });
                KeepAlive.clearData();
                history.push("/");
              }
            }
          ]
        },
        onBack: () => {
          onStepReturn();
        },
        showTermsOfUse: false,
        description: (
          <div className="remain-time">
            <Icon width={32} height={32} name="vie-clock-o-rc-medium" />{" "}
            <span>
              Trực tiếp lúc {time}, ngày {day}
            </span>
          </div>
        ),
        showTipBoxGoback: true,
        executeActionBeforeClose: true
      });
    }
    const messageHandler = useCallback(
      (msg: string, data?: any) => {
        switch (msg) {
          case "setInitialState":
            if (jumpMethodRef.current && typeof jumpMethodRef.current === "function") {
              jumpMethodRef.current(data.focusIndex);
              const { chosenMethodText } = data;
              if (isAuthen && chosenMethodText) {
                checkAutoSubmitMethod(chosenMethodText);
              }
            }
            if (setCouponCodeRef.current && typeof setCouponCodeRef.current === "function") {
              setCouponCodeRef.current(data.couponCode);
            }
            const isActiveAtSubmitCode = get(data, "activeAtSubmitCode");
            if (isActiveAtSubmitCode) {
              if (setFocusInfoRef.current && typeof setFocusInfoRef.current === "function") {
                setFocus(EnumPaymentNewUIFocusKey.PaymentInfo);
                setTimeout(() => {
                  setFocusInfoRef.current();
                });
              }
            }
            const searchParams = new URLSearchParams(location.search);
            if (searchParams.get("flow") === "tvod-svod" && userIsPremium === 1) {
              onStepReturn(true);
            }
            break;
          default:
            break;
        }
      },
      [location]
    );

    useEffect(() => {
      if (listener && typeof listener === "function") {
        listener(messageHandler);
      }
    }, []);

    useEffect(() => {
      const [chosenMethodType] = chosenMethod.split("_");
      SegmentManager.segmentAction("select payment method", {
        payment_method: chosenMethodType
      });
      SegmentManager.segmentAction("payment method selected", {
        payment_method: chosenMethodType
      });
    }, [chosenMethod]);

    const [chosenMethodType] = chosenMethod.split("_");
    const pkgName = get(tvodOffer, "product_name_msg", "");
    const durationName = get(tvodOffer, "duration_msg", "");
    const startDate = get(tvodOffer, "start_date", "");
    const expiredDate = ""; // get(duration, "expired_date", "");
    const { newExpiredDate } = discountInfo;
    // get(config, ["sms_price", `${chosenMethodType}_${durationId}`], 0) ||
    // get(duration, "price", 0);
    const oldPrice = 0; // get(duration, "old_price", 0);
    const waitingDurMsg = get(tvodOffer, "waiting_dur_msg", "");
    const consumingDurMsg = get(tvodOffer, "consuming_dur_msg", "");
    const discountPrice = get(tvodOffer, "discount_amount", "");
    const price = get(tvodOffer, "price", 0);
    const totalPrice = get(tvodOffer, "total_price_msg");
    return (
      // <FocusContext.Provider value={focusKey}>
      //   <div className="wrap wrap--bg-white wrap--payment" ref={ref}>
      //     <header className="header header--payment">
      //       <Logo className="logo logo--payment" />
      //       <PaymentStepView
      //         activeStep={2}
      //         hideStepThree={
      //           stepMethod === EnumStepMethod.MethodView &&
      //           ["payoo", "mbfsms", "vinaphone", "viettelsms"].indexOf(
      //             chosenMethodType
      //           ) >= 0
      //         }
      //         tvod
      //       />
      //     </header>
      //     <main className="main">
      //       <section
      //         className="section section--payment section--payment-method"
      //         ref={sectionRef}
      //       >
      //         <div className="section__header">
      //           <div className="grid">
      //             <div className="col-7">
      //               {stepMethod === EnumStepMethod.MethodList && (
      //                 <div className="section__title">
      //                   Chọn phương thức thanh toán
      //                 </div>
      //               )}
      //               {stepMethod === EnumStepMethod.MethodView && (
      //                 <OldMethodTitle
      //                   type={chosenMethodType}
      //                   methodObj={(get(config, "methods", []) as any[]).find(
      //                     (element) => element.id === chosenMethodType
      //                   )}
      //                 />
      //               )}
      //             </div>
      //           </div>
      //         </div>
      //         <div className="section__body">
      //           <div
      //             className={classNames("grid", {
      //               "grid-margin-x": chosenMethodType,
      //             })}
      //           >
      //             <div
      //               className={classNames(
      //                 "col-7",
      //                 stepMethod === EnumStepMethod.MethodView &&
      //                   chosenMethodType
      //                   ? "col block block--payment"
      //                   : "",
      //                 stepMethod === EnumStepMethod.MethodView &&
      //                   chosenMethodType
      //                   ? generateObjectClassPayment(chosenMethodType)
      //                   : null
      //               )}
      //             >
      //               <OldPaymentMethodList
      //                 show={stepMethod === EnumStepMethod.MethodList}
      //                 chosenMethodType={chosenMethodType}
      //                 methods={methods}
      //                 activeMethods={activeMethods}
      //                 promotionList={promotionList}
      //                 methodObjs={get(config, "methods", []) as any[]}
      //                 napasToken={napasToken}
      //                 shopeePayTokens={shopeePayTokens}
      //                 onChange={onMethodChange}
      //                 onPopout={onPaymentMethodListPopout}
      //                 onMethodSubmit={onMethodSubmit}
      //                 onReturn={onStepReturn}
      //                 sectionRef={sectionRef}
      //                 // onClick={() => setFocusIndex(0)}
      //                 getFocusSub={(fn: any) =>
      //                   (getFocusMethodRef.current = fn)
      //                 }
      //                 jumpMethodSub={(fn: any) => (jumpMethodRef.current = fn)}
      //                 type="tvod"
      //               />
      //               {stepMethod === EnumStepMethod.MethodView &&
      //               chosenMethodType &&
      //               isAuthen ? (
      //                 <MethodView
      //                   onSubmit={onMethodViewSubmit}
      //                   onFail={onMethodViewFail}
      //                   onReturn={onMethodViewReturn}
      //                   onGoToVoucher={onEnterCode}
      //                   type={chosenMethod}
      //                   duration={{}}
      //                   tvodInfo={tvodInfo}
      //                   discount={discountInfo}
      //                   userAccount={userAccount}
      //                   config={config}
      //                   shopeePayTokens={shopeePayTokens}
      //                 />
      //               ) : null}
      //             </div>
      //             <div
      //               className="col col-5 block block--payment block--payment-bill"
      //               style={{
      //                 paddingLeft: chosenMethodType ? "0" : "0.625vw",
      //                 paddingRight: "0.625vw",
      //               }}
      //             >
      //               <OldPaymentInfo
      //                 ref={payemntInfoRef}
      //                 step={stepMethod}
      //                 type="tvod"
      //                 userAccount={userAccount}
      //                 pkgName={pkgName}
      //                 durationName={durationName}
      //                 startDate={startDate}
      //                 expiredDate={
      //                   newExpiredDate !== "" ? newExpiredDate : expiredDate
      //                 }
      //                 tvodInfo={tvodInfo}
      //                 price={price}
      //                 totalPrice={totalPrice}
      //                 oldPrice={oldPrice}
      //                 discountPrice={discountPrice}
      //                 isDisablePromotionInput={isDisablePromotionInput}
      //                 waitingDurMsg={waitingDurMsg}
      //                 consumingDurMsg={consumingDurMsg}
      //                 onPopout={onPaymentInfoPopout}
      //                 // keyboardBoxRef={keyboardBoxRef}
      //                 sectionRef={sectionRef}
      //                 onApplyCoupon={onApplyCoupon}
      //                 onCleanCoupon={onCleanCoupon}
      //                 onReturn={onStepReturn}
      //                 onMethodViewReturn={onMethodViewReturn}
      //                 setCouponCodeRef={(fn: any) =>
      //                   (setCouponCodeRef.current = fn)
      //                 }
      //                 setFocusRef={(fn: any) => (setFocusInfoRef.current = fn)}
      //                 // onClick={() => setFocusIndex(1)}
      //                 getCouponCodeSub={(fn: any) =>
      //                   (getCouponCodeRef.current = fn)
      //                 }
      //               />
      //             </div>
      //           </div>
      //         </div>
      //       </section>
      //     </main>
      //   </div>
      // </FocusContext.Provider>

      <FocusContext.Provider value={focusKey}>
        <PaymentHeader
          benefits={config?.vip_privilege || []}
          hotline={get(config, "hotline_vieon", "")}
          hasBenefits={false}
          step={2}
          steps={[{ title: "Chọn nội dung" }, { title: "Chọn phương thức thanh toán" }, { title: "Kết quả" }]}
        />
        <div className={styles["payment-step-two"]} ref={ref}>
          <div className={styles.main}>
            <div className={classNames("section--payment-method", styles.section)} ref={sectionRef}>
              <div className={styles.content}>
                {stepMethod === EnumStepMethod.MethodView && (
                  <div className={styles["step-title"]}>
                    <MethodTitle
                      type={chosenMethodType}
                      methodObj={(get(config, "methods", []) as any[]).find(
                        (element) => element.id === chosenMethodType
                      )}
                    />
                  </div>
                )}
              </div>
              <div className="section__body">
                <div className={classNames("grid")}>
                  <div
                    className={classNames(
                      "col-7",
                      stepMethod === EnumStepMethod.MethodView && chosenMethodType ? "col block block--payment" : "",
                      stepMethod === EnumStepMethod.MethodView && chosenMethodType
                        ? generateObjectClassPayment(chosenMethodType)
                        : null
                    )}
                  >
                    <PaymentMethodList
                      useFor="TVODE"
                      show={stepMethod === EnumStepMethod.MethodList}
                      chosenMethodType={chosenMethodType}
                      methods={methods}
                      activeMethods={activeMethods}
                      promotionList={promotionList}
                      methodObjs={get(config, "methods", []) as any[]}
                      napasToken={napasToken}
                      shopeePayTokens={shopeePayTokens}
                      onChange={onMethodChange}
                      onPopout={onPaymentMethodListPopout}
                      onMethodSubmit={onMethodSubmit}
                      onReturn={onStepReturn}
                      sectionRef={sectionRef}
                      // onClick={() => setFocusIndex(0)}
                      getFocusSub={(fn: any) => (getFocusMethodRef.current = fn)}
                      jumpMethodSub={(fn: any) => (jumpMethodRef.current = fn)}
                      type="tvod"
                    />
                    {stepMethod === EnumStepMethod.MethodView && chosenMethodType && isAuthen ? (
                      <MethodView
                        onSubmit={onMethodViewSubmit}
                        onFail={onMethodViewFail}
                        onReturn={onMethodViewReturn}
                        onGoToVoucher={onEnterCode}
                        type={chosenMethod}
                        duration={{}}
                        tvodInfo={tvodInfo}
                        discount={discountInfo}
                        userAccount={userAccount}
                        config={config}
                        shopeePayTokens={shopeePayTokens}
                      />
                    ) : null}
                  </div>
                  <div
                    className="col col-5 block block--payment block--payment-bill"
                    style={{
                      paddingLeft: chosenMethodType ? "1vw" : "0.625vw",
                      paddingRight: "0.625vw"
                    }}
                  >
                    <PaymentInfo
                      ref={payemntInfoRef}
                      step={stepMethod}
                      type="tvod"
                      userAccount={userAccount}
                      pkgName={pkgName}
                      durationName={durationName}
                      startDate={startDate}
                      expiredDate={newExpiredDate !== "" ? newExpiredDate : expiredDate}
                      tvodInfo={tvodInfo}
                      price={price}
                      totalPrice={totalPrice}
                      oldPrice={oldPrice}
                      discountPrice={discountPrice}
                      isDisablePromotionInput={isDisablePromotionInput}
                      waitingDurMsg={waitingDurMsg}
                      consumingDurMsg={consumingDurMsg}
                      onPopout={onPaymentInfoPopout}
                      // keyboardBoxRef={keyboardBoxRef}
                      sectionRef={sectionRef}
                      onApplyCoupon={onApplyCoupon}
                      onCleanCoupon={onCleanCoupon}
                      onReturn={onStepReturn}
                      onMethodViewReturn={onMethodViewReturn}
                      setCouponCodeRef={(fn: any) => (setCouponCodeRef.current = fn)}
                      setFocusRef={(fn: any) => (setFocusInfoRef.current = fn)}
                      // onClick={() => setFocusIndex(1)}
                      getCouponCodeSub={(fn: any) => (getCouponCodeRef.current = fn)}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </FocusContext.Provider>
    );
  }
);

export default StepTwoTVOD;
