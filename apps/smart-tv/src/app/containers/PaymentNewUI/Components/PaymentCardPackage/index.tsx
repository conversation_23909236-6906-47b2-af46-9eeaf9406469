import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useState,
} from "react";
import { get } from "lodash";
import classNames from "classnames";
import defaultImg from "assets/images/video-default.png";
import { useFocusable } from "core/KeyHandle";
import Icon from "app/components/Common/Icon";
import ImageWithFallback from "app/components/Common/ImageWithFallback";
import { ConfigBenefitType, PackageBenefitType } from "types/common/config";
import styles from "./styles.module.scss";

const getPackageColor = (
  packageId: string,
  packagesNameMapping: Record<string, string>
) => {
  const name = packagesNameMapping[packageId];
  switch (name) {
    case "hbo":
      return "blue";
    case "sport":
      return "red";
    case "all_access":
      return "yellow";
    default:
      return "green";
  }
};

interface Props {
  prefixKey: string;
  index: number;
  packageItem: any;
  length: number;
  config: any;
  onClick: (item: any) => void;
  onChange?: (item: any) => void;
  onReturn: () => void;
}
const PaymentCardPackage: React.FC<Props> = React.memo(
  ({
    index,
    prefixKey,
    packageItem,
    length,
    config,
    onClick,
    onChange = () => {},
    onReturn,
  }) => {
    const [screen, setScreen]: any = useState<string>("FullHD");
    const banner = get(packageItem, "banner", null);
    const bannerInfoGroup = get(packageItem, "banner_info_group", null);
    const userIsBuy = get(packageItem, "user_is_buy", false);
    const remainingDays = get(packageItem, "remaining_days", null);
    const backgroundPromo = packageItem?.background_promo || null;

    const { ref, focused } = useFocusable({
      focusKey: `${prefixKey}_${packageItem.id}`,
      onArrowPress: (direction) => {
        if (index === 0 && direction === "left") {
          onReturn();
          return false;
        }
        return true;
      },
      onReturnPress: () => {
        onReturn();
      },
      focusImplicitOnHover: true,
      enterOnClick: true,
      onEnterPress: () => {
        onClick(packageItem);
      },
    });

    useEffect(() => {
      if (focused) {
        onChange(packageItem);
      }
    }, [focused, packageItem]);

    const currentPackage = useMemo(() => {
      const pkg: any = config?.packages.filter(
        (p: any) => p.id === packageItem?.id
      );
      if (pkg?.length > 0) {
        return pkg[0] || [];
      }
      return [];
    }, [config?.packages, packageItem?.id]);

    const packagesNameMapping = useMemo(
      () => config?.packagesNameMapping,
      [config?.packagesNameMapping]
    );

    const onError = useCallback((e: any) => {
      e.target.src = defaultImg;
    }, []);

    useLayoutEffect(() => {
      if (window?.screen?.width === 1280) {
        setScreen("HD");
      }
    }, []);

    const packageUsingBadge = useMemo(() => {
      if (!userIsBuy) return null;

      if (remainingDays) {
        return (
          <div className={classNames(styles["user-buy"], styles.expiration)}>
            <span>Gói sẽ hết hạn sau: {remainingDays} ngày</span>
            <Icon name="vie-clock-countdown" viewBox="0 0 16 16" />
          </div>
        );
      }

      return (
        <div className={styles["user-buy"]}>
          <span>Đang sử dụng</span>
          <Icon name="vie-payment-star" viewBox="0 0 22 22" />
        </div>
      );
    }, [remainingDays, userIsBuy]);

    const packageColor = getPackageColor(packageItem.id, packagesNameMapping);

    const benefitsElement = useMemo(
      () =>
        config?.benefits?.map(
          ({ id, title, icon_benefit }: ConfigBenefitType, key: number) => {
            const benefit = currentPackage?.benefits?.find(
              (bnf: PackageBenefitType) => bnf.id === id
            );
            if (!benefit) return null;

            return (
              <div
                className={classNames(styles.item, {
                  [styles["item--has-text"]]: length === 1,
                  [styles.disable]: benefit.visibility === "inactive",
                })}
                key={key + id}
              >
                <div className={classNames(styles.benefit, styles["tx-400"])}>
                  <span>
                    <ImageWithFallback
                      imageProps={{
                        src: icon_benefit || "",
                        alt: title,
                        className: styles.icon,
                      }}
                      fallback={
                        <Icon
                          name="vie-star-sparkle"
                          width={30}
                          height={30}
                          viewBox="0 0 30 30"
                          className={styles.icon}
                        />
                      }
                    />
                  </span>

                  <div className={styles.text}>{title}</div>
                </div>
              </div>
            );
          }
        ),
      [config?.benefits, currentPackage?.benefits, length]
    );

    return (
      // New layout Wrap, this Master container all page
      <div
        className={classNames(styles.wrapper, {
          [styles["highlight-tag"]]: !!currentPackage.tag,
          [styles.focused]: focused,
        })}
      >
        {currentPackage?.tag && (
          <>
            <div className={styles[`background-${packageColor}`]} />

            <div className={classNames(styles.tag, "a-line-text")}>
              {currentPackage.tag}
            </div>
          </>
        )}
        <div
          ref={ref}
          data-title={packageItem.name}
          className={classNames(styles["payment-package-card"], {
            [styles["is-focus"]]: focused,
          })}
        >
          <div className={styles.thumbnail}>
            {packageUsingBadge}
            <img
              src={backgroundPromo || banner}
              onError={onError}
              alt="banner payment"
            />
            {bannerInfoGroup && (
              <img
                className={styles.info}
                src={bannerInfoGroup}
                alt="banner payment "
                onError={onError}
              />
            )}
          </div>
          <div
            className={classNames(styles.list, {
              [styles["list--focus"]]: !!focused,
            })}
          >
            {benefitsElement}
          </div>
        </div>
      </div>
    );
  }
);

export default PaymentCardPackage;
