import React, { useMemo } from "react";

import InputCodeStep1Img from "assets/images/payment/tutorials/input_code_step_1.png";
import InputCodeStep2Img from "assets/images/payment/tutorials/input_code_step_2.png";
import InputCodeStep3Img from "assets/images/payment/tutorials/input_code_step_3.png";

import styles from "./style.module.scss";
import TutorialCard from "../TutorialCard";

const InputCode = ({ code = "" }: { code: string }) => {
  const tutorialData = useMemo(
    () => [
      {
        title: "Bước 01:",
        description: "Mở ứng dụng VieON ",
        thumbnail: InputCodeStep1Img,
      },
      {
        title: "Bước 02:",
        description:
          "Vào trang cá nhân trên VieON bằng cách nhấn vào avatar ở góc phải trên cùng và chọn “<PERSON><PERSON><PERSON> nhập SmartTV“",
        thumbnail: InputCodeStep2Img,
      },
      {
        title: "Bước 03:",
        description: (
          <div>
            <PERSON><PERSON><PERSON><PERSON> mã sau<div className={styles.code}>{code}</div>
          </div>
        ),
        thumbnail: InputCodeStep3Img,
      },
    ],
    [code]
  );
  return (
    <div className={styles["input-code"]}>
      <div className={styles["tutorial-list"]}>
        {tutorialData.map(({ ...props }, index) => (
          <TutorialCard key={index} {...props} />
        ))}
      </div>
    </div>
  );
};

InputCode.displayName = "InputCode";

export default InputCode;
