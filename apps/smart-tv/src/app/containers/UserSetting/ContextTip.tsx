import React, { useState, createContext, ReactNode, useContext } from "react";

const ContextTipAction = createContext<
  React.Dispatch<React.SetStateAction<string>> | undefined
>(undefined);

const ContextTipState = createContext<string>("để quay lại");

export const ContextTipProvider = ({ children }: { children: ReactNode }) => {
  const [tip, setTip] = useState<string>("để quay lại");
  return (
    <ContextTipAction.Provider value={setTip}>
      <ContextTipState.Provider value={tip}>
        {children}
      </ContextTipState.Provider>
    </ContextTipAction.Provider>
  );
};

export const useContextTipAction = () => {
  const contextTipAction = useContext(ContextTipAction);
  if (contextTipAction !== undefined) {
    return contextTipAction;
  }
  throw new Error("ContextTipAction is undefined");
};

export const useContextTipState = () => {
  const contextTipState = useContext(ContextTipState);
  if (contextTipState !== undefined) {
    return contextTipState;
  }
  throw new Error("ContextTipState is undefined");
};
