import React, { useCallback, memo } from "react";
import Layout from "./Layout";
import { useContextAgeAndGender } from "../context/contextAgeAndGender";
import { useContextMainDashboard } from "../context/contextMainDashboard";

export interface ListType {
  id: string | number;
  content: string;
  isSelected: boolean;
}

const Gender = memo(function Gender({
  onPopoutGenderList,
}: {
  onPopoutGenderList: VoidFunction;
}) {
  const {
    isDataChanged: { setValue: setIsDataChanged },
  } = useContextMainDashboard();

  const { genderList, selectedGender, setSelectedGender } =
    useContextAgeAndGender();

  const onClickItem = useCallback(
    (index: number) => {
      if (genderList) {
        const focusItem = genderList[index];
        setSelectedGender(focusItem);
        setIsDataChanged(true);
        onPopoutGenderList();
      }
    },
    [setSelectedGender, genderList, onPopoutGenderList, setIsDataChanged]
  );

  const onClickUnderlay = useCallback(() => {
    onPopoutGenderList();
  }, [onPopoutGenderList]);

  const onPopout = (direction: Direction) => {
    if (direction === "left") {
      onPopoutGenderList();
    }
  };

  return (
    <Layout
      focusKey="MULTI_PROFILE_GENDER_BOX"
      list={genderList}
      onPopout={onPopout}
      onReturn={onPopoutGenderList}
      selectedItem={selectedGender}
      onClickItem={onClickItem}
      onClickUnderlay={onClickUnderlay}
      title="Giới tính"
    />
  );
});

export default Gender;
