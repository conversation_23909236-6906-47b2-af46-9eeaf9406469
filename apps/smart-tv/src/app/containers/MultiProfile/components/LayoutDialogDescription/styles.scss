@use "src/assets/scss/settings/function" as fn;

.layout-dialog-description {
  position: relative;

  &__description {
    font-weight: 400;
    font-size: fn.percent-unit(28);
    line-height: fn.percent-unit(150%);
    margin-bottom: fn.percent-unit(40);
    color: #dedede;

    &__p {
      &__title {
        font-size: fn.percent-unit(36);
        margin: 0 0 fn.percent-unit(10);
        font-weight: 500;
      }

      &__desc {
        margin: 0;
        font-weight: 400;
      }

      &:not(:last-child) {
        margin-bottom: fn.percent-unit(40);
      }
    }
  }

  &__note-container {
    color: #dedede;
    margin-bottom: fn.percent-unit(18);
    margin-top: 0;
    padding-left: fn.percent-unit(25);

    &.dot {
      margin-bottom: fn.percent-unit(100);
    }

    .note-item {
      display: flex;
      align-items: center;
      font-size: fn.percent-unit(28);

      &:not(:last-child) {
        margin-bottom: fn.percent-unit(18);
      }

      &.number {
        .decoration {
          flex: 0 0 fn.percent-unit(50);
          text-align: center;
          justify-content: center;
          margin-right: fn.percent-unit(20);

          span {
            width: fn.percent-unit(32);
            height: fn.percent-unit(32);
            color: #3ac882;
            border: fn.percent-unit(3) solid #3ac882;
            border-radius: fn.percent-unit(50%);
            padding: fn.percent-unit(5);
            justify-content: center;
            display: flex;
          }
        }
      }

      &.dot {
        align-items: flex-start;

        .decoration {
          flex: 0 0 auto;
          text-align: center;
          justify-content: center;
          margin-right: fn.percent-unit(25);
          height: 100%;
          display: flex;
          align-items: flex-start;

          span {
            display: block;
            width: fn.percent-unit(8);
            height: fn.percent-unit(8);
            background-color: #fff;
            border-radius: 50%;
            margin-top: fn.percent-unit(16px);
          }
        }
      }

      .label {
        font-weight: 500;
        line-height: fn.percent-unit(40);
        font-size: fn.percent-unit(32);
      }
    }
  }
}
