@use "src/assets/scss/settings/function" as fn;
@use "src/assets/scss/settings/palettes" as palettes;
@use "src/assets/scss/settings/variables" as var;

.main-profile {
  color: white !important;
  display: flex;
  flex-direction: column;
  width: fn.percent-unit(100%);
  height: fn.percent-unit(100vh);
  position: relative;

  &__header {
    width: fn.percent-unit(100%);
    position: relative;

    .title {
      font-size: fn.percent-unit(36px);
      font-weight: 500;
      line-height: 1;
      color: palettes.$white;
      margin-top: fn.percent-unit(45);
      // margin-left: fn.percent-unit(var.$sidebar-max-width);
      margin-left: fn.percent-unit(144);
      padding-left: fn.percent-unit(16px);
      border-left: solid 4px palettes.$green-3a;
      z-index: 3;
      width: fn.percent-unit(100%);
    }
  }

  &__body {
    flex: 1;
    display: flex;
    width: fn.percent-unit(100%);
    flex-direction: row;

    //background: blue;
    .panel {
      flex: 1;
      display: flex;

      //background: #0ad418;
      //border: 1px solid white;
      &.left {
        padding-left: fn.percent-unit(144);
      }

      &.flex-column {
        flex-direction: column;
      }

      &.flex-row {
        flex-direction: row;
      }

      &.contentCenter {
        justify-content: center;
      }
    }
    .profile-container {
      width: fn.percent-unit(556);
      display: flex;
      flex-direction: column;
      //justify-content: center;
      justify-content: space-around;
    }
  }
}
