import React, { useEffect, useMemo, useState } from "react";
import useStateRef from "hooks/useStateRef";
import TipBox from "app/components/Common/Portal/TipBox";
import { useFocusable } from "core/KeyHandle/useFocusable";
import { FocusContext } from "core/KeyHandle/useFocusedContext";
import NewButton from "app/components/NewButton";
import * as api from "services/endpoint";
import { RibbonDataItemModel } from "app/models/RibbonData/RibbonDataItem";
import "./styles.scss";
import EndScreenSuggestionList from "./components/EndScreenSuggestionList";

export enum EndScreenSuggestionKeys {
  EndScreenSuggestion = "EndScreenSuggestionLivestream",
  BtnReturn = "EndScreenSuggestionLivestreamBtnReturn",
  List = "EndScreenSuggestionLivestreamList",
  ListItem = "EndScreenSuggestionLivestreamListItem",
}

const MaxItems = 5;

const EndScreenSuggestion = ({
  contentDetailId,
  onBack,
}: {
  contentDetailId: string;
  onBack: VoidFunction;
}) => {
  const [list, setList, listRef] = useStateRef<RibbonDataItemModel[]>([]);
  const [isFetch, setIsFetch] = useState<boolean>(false);
  const preferredChildFocusKey = useMemo(() => {
    if (list.length) {
      return EndScreenSuggestionKeys.List;
    }
    return EndScreenSuggestionKeys.BtnReturn;
  }, [list]);

  const { ref, focusKey, focusSelf } = useFocusable({
    focusKey: EndScreenSuggestionKeys.EndScreenSuggestion,
    preferredChildFocusKey,
    onReturnPress: onBack,
  });

  useEffect(() => {
    if (isFetch) {
      focusSelf();
    }
  }, [focusSelf, isFetch]);

  useEffect(() => {
    (async () => {
      try {
        if (contentDetailId && !list.length) {
          const suggestionListRes: any = await api.getRecommendLivestream(
            contentDetailId,
            0
          );
          const suggestionList = [...suggestionListRes?.items];
          if (suggestionList?.length && Array.isArray(suggestionList)) {
            setList(
              suggestionList.slice(0, MaxItems).map((item) => {
                return new RibbonDataItemModel(item);
              })
            );
          }
        }
      } catch (error) {
        // error
      } finally {
        setIsFetch(true);
      }
    })();
  }, [contentDetailId, list]);

  const textDescription = useMemo(() => {
    if (list.length) {
      return "Hãy khám phá ngay nội dung đề xuất ở dưới đây nhé";
    }
    return "Nội dung không khả dụng.Thoát khỏi chương trình để khám phá</br>nhiều nội dung hấp dẫn khác nhé";
  }, [list]);

  return (
    <FocusContext.Provider value={focusKey}>
      <div ref={ref} className="end-screen">
        {isFetch ? (
          <>
            <div className="end-screen__button">
              <NewButton
                className="btn-exit"
                focusKey={EndScreenSuggestionKeys.BtnReturn}
                icon="vie-arrow-left"
                onClick={onBack}
                onReturn={onBack}
              >
                Thoát
              </NewButton>
            </div>
            <div
              className={`end-screen__info ${
                list.length ? "end-screen__info-has-list" : ""
              }`}
            >
              <div className="title">Sự kiện đã kết thúc!</div>
              <div
                className="description"
                dangerouslySetInnerHTML={{
                  __html: textDescription,
                }}
              />
            </div>
            <EndScreenSuggestionList list={list} onBack={onBack} />
            <TipBox zIndex={9999} tip="để quay lại" />
          </>
        ) : null}
      </div>
    </FocusContext.Provider>
  );
};
export default EndScreenSuggestion;
