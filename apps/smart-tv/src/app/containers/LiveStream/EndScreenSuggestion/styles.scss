@use "src/assets/scss/settings/function" as fn;
@use "src/assets/scss/mixin/position" as position;
@use "src/assets/scss/mixin/pseudo" as pseudo;

.end-screen {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background-image: url("../../../../assets/images/livestream-endscreen-bg.png");
  background-size: cover;
  background-position: center top;

  &__button {
    position: absolute;
    left: fn.percent-unit(100);
    top: fn.percent-unit(60);
    z-index: 1;
  }

  .btn {
    &-exit {
      font-size: fn.percent-unit(28);
      line-height: 1.5;
      padding: fn.percent-unit(8px) fn.percent-unit(24px);
      border-color: transparent;
      background: transparent;

      .btn__icon {
        margin-right: fn.percent-unit(10);
        width: fn.percent-unit(32);
        height: fn.percent-unit(32);
        .vie {
          color: #fff;
        }
      }

      &.focus {
        background-color: white;
        color: #222222;
        border-color: white;
        .btn__icon {
          .vie {
            color: #222222;
          }
        }
      }
    }
  }

  &__info {
    position: absolute;
    top: fn.percent-unit(465);
    left: 0;
    width: 100%;
    text-align: center;
    z-index: 2;
    &-has-list {
      top: fn.percent-unit(282);
    }
    .title {
      font-weight: 500;
      font-size: fn.percent-unit(48);
      line-height: fn.percent-unit(56);
      color: #fff;
      margin-bottom: fn.percent-unit(12);
      letter-spacing: fn.percent-unit(0.297);
      font-feature-settings: "clig" off, "liga" off;
    }
    .description {
      font-size: fn.percent-unit(28);
      color: #adadad;
      line-height: fn.percent-unit(42);
    }
  }

  &__ribbon {
    position: absolute;
    right: fn.percent-unit(44);
    bottom: fn.percent-unit(153);
    text-align: center;
    z-index: 3;
    &-title {
      font-weight: 500;
      font-size: fn.percent-unit(28);
      line-height: fn.percent-unit(42);
      color: #fff;
      margin: 0 0 fn.percent-unit(12);
      text-align: left;
    }

    &-list {
      display: flex;
      text-align: left;
      width: fit-content;

      &-item {
        display: flex;
        align-items: center;
        justify-content: center;
        padding-bottom: fn.percent-unit(16);
        &:not(:last-child) {
          margin-right: fn.percent-unit(24);
        }
        .card {
          background: white;
          height: fn.percent-unit(180);
          width: fn.percent-unit(320);
          position: relative;
          overflow: hidden;
          border-width: fn.percent-unit(4);
          img {
            width: 100%;
            height: 100%;
            display: block;
          }
        }
        .focus {
          outline: fn.percent-unit(5) solid #ffffff;
          outline-offset: fn.percent-unit(4);
        }
      }
    }
  }
}
