import { stringify } from "querystring-es3";
import { callApiGameQuiz } from "services/api";
import platform from "services/platform";

const pushAnser = (
  question_id: string,
  data: {
    option_id: string;
    user_id: string;
    platform: string;
    model: string;
  }
) =>
  callApiGameQuiz({
    url: `quiz/option/${question_id}`,
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    data: stringify(data)
    // data: new URLSearchParams(data as Record<string, string>).toString()
  });

const getAward = (content_id: string) =>
  callApiGameQuiz({
    url: `quiz/award/${content_id}`,
    method: "GET"
  });

const saveTerms = (
  content_id: string,
  data: {
    status: number;
    model: string;
  }
) =>
  callApiGameQuiz({
    url: `quiz/terms/${content_id}`,
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    data: stringify(data)
    // data: new URLSearchParams(data as any).toString()
  });

const getTerms = (content_id: string) =>
  callApiGameQuiz({
    url: `quiz/terms/${content_id}`,
    method: "GET"
  });

// /
export async function gamePushAnser(
  question_id: string,
  option_id: string,
  user_id: string,
  callback: (success: boolean, response: any) => void
) {
  const data = {
    option_id,
    user_id,
    platform: platform.platformName,
    model: platform.fullPlatformName
  };
  const res = await pushAnser(question_id, data)
    .then((response) => {
      return response;
    })
    .catch(() => {
      return undefined;
    });
  if (res) {
    callback(true, res);
  } else {
    callback(false, res);
  }
}

export async function gameGetAward(
  content_id: string,
  callback: (success: boolean, response: any) => void
) {
  const res = await getAward(content_id)
    .then((response) => {
      return response;
    })
    .catch(() => {
      return undefined;
    });
  if (res) {
    callback(true, res);
  } else {
    callback(false, res);
  }
}

export async function gameSaveTerms(
  content_id: string,
  status: number,
  callback: (success: boolean, response: any) => void
) {
  const res = await saveTerms(content_id, {
    status,
    model: platform.fullPlatformName
  })
    .then((response) => {
      return response;
    })
    .catch(() => {
      return undefined;
    });
  if (res) {
    callback(true, res);
  } else {
    callback(false, res);
  }
}

export async function gameGetTerms(
  content_id: string,
  callback: (success: boolean, response: any) => void
) {
  const res = await getTerms(content_id)
    .then((response) => {
      return response;
    })
    .catch(() => {
      return undefined;
    });
  if (res) {
    callback(true, res);
  } else {
    callback(false, res);
  }
}
