import React from "react";
import classNames from "classnames";
import { formatNumber } from "app/utils";

interface Props {
  className?: string;
  focus: boolean;
  selected: boolean;
  correctId: string;
  isTimeout: boolean;
  content: string;
  id: string;
  optionCount?: number;
  onClick?: (event: React.MouseEvent<HTMLElement>) => void;
}

const QuestionItem: React.FC<Props> = ({
  className,
  focus: isFocus,
  selected: isSelected,
  correctId,
  id,
  isTimeout,
  content,
  optionCount,
  onClick,
}) => {
  return (
    <div
      className={classNames(className, "game-question__item", {
        focus: isFocus,
        selected: isSelected,
        correct: isTimeout && id === correctId,
        "correct--noicon": isTimeout && id === correctId && !isSelected,
        incorrect:
          isTimeout && isSelected && correctId !== "" && id !== correctId,
        timeout: !isSelected && isTimeout,
        focusSelect:
          isFocus &&
          ((isTimeout && id === correctId) ||
            (isTimeout && isSelected && correctId !== "" && id !== correctId)),
      })}
      onClick={onClick}
    >
      <div className="label">{content}</div>
      {optionCount !== undefined && optionCount >= 0 && (
        <div className="label">{formatNumber(optionCount)}</div>
      )}
    </div>
  );
};
export default QuestionItem;
