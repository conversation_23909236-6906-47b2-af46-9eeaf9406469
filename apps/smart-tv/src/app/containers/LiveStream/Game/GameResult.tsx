import React, { useEffect, useRef } from "react";
import classNames from "classnames";
import QRCode from "qrcode";
import FaceIcon from "assets/images/face-icon-game.svg";
import platform from "services/platform";
import useKeyProps from "hooks/useKeyProps";
import Button from "app/components/Button";
import Icon from "app/components/Common/Icon";
import { formatMoney } from "app/utils/formatNumber";
import countDownHook from "./countDownHook";
import gameConfig from "./gameConfig";

interface Props {
  className?: string;
  registerKey: RegisterKey;
  onPopout?: PopoutHandler;
  onReturn?: () => void;
  onClose?: () => void;
  data?: ResultData;
}

interface ResultData {
  type: "waiting" | "winning" | "lose" | "answer_error";
  data?: {
    title?: string;
    content: string;
    correctAnswers?: number;
    totalQuestion?: number;
    totalPersons?: number;
    winPrice?: number;
    url?: string;
    campaignAward?: string;
    messageError?: string;
  };
}

const GameResult: React.FC<Props> = ({
  className,
  registerKey: register,
  onPopout,
  onReturn,
  onClose,
  data,
}) => {
  const keyHandler = (e: KeyboardEvent) => {
    const { keys } = platform;
    switch (e.keyCode) {
      case keys.up:
        if (onPopout && typeof onPopout === "function") {
          onPopout("up");
        }
        break;
      case keys.down:
        if (onPopout && typeof onPopout === "function") {
          onPopout("down");
        }
        break;
      case keys.left:
        if (onPopout && typeof onPopout === "function") {
          onPopout("left");
        }
        break;
      case keys.right:
        if (onPopout && typeof onPopout === "function") {
          onPopout("right");
        }
        break;
      case keys.enter:
        if (onClose && typeof onClose === "function") {
          onClose();
        }
        break;
      case keys.return:
        if (onReturn && typeof onReturn === "function") {
          onReturn();
        }
        break;
      default:
        break;
    }
  };
  const [focusIndex] = useKeyProps({
    keyHandler,
    defaultFocusIndex: 0,
    register,
  });
  const qrRef = useRef<HTMLCanvasElement>(null);
  useEffect(() => {
    if (data?.data?.url?.length) {
      QRCode.toCanvas(qrRef.current, data?.data?.url ?? "", {
        width: 0.1041 * (window?.innerWidth ?? 1920),
        margin: 1,
      }).then(() => {});
    } else if (qrRef.current) {
      qrRef.current.style.display = "none";
    }
  }, [data?.data?.url]);
  // if(focusIndex === 1){
  //     return  <GameNotification>
  //         <p>Bạn có muốn quay trở lại game?</p>
  //         <Button focus={focusIndex === 1}>Quay lại game</Button>
  //     </GameNotification>
  // }
  const onCountDownCompleted = () => {
    if (onClose) {
      onClose();
    }
  };
  const [time, startTimer, clearTimer] = countDownHook({
    onCompleted: onCountDownCompleted,
  });
  useEffect(() => {
    if (data !== undefined && data.type === "lose") {
      clearTimer();
      startTimer(15);
    }
    return () => {
      clearTimer();
    };
  }, [data?.type]);
  return (
    <div className={classNames(className, "game-result game-background")}>
      {data && data.type === "answer_error" ? (
        <div
          className="game-result__content"
          onClick={onClose}
          style={{ marginLeft: "6em", marginRight: "6em" }}
        >
          {data.data?.messageError}
          <br />
          <br />
          {/* Bạn đang sử dụng nhiều hơn 1 thiết bị để tham gia Game Rap Việt <br/>
                    Vui lòng chơi tiếp game trên thiết bị đã được ghi nhận câu trả lời.<br/> */}
          <Button focus={focusIndex === 0}>Tôi đã hiểu</Button>
        </div>
      ) : null}
      {data !== undefined && data.type === "waiting" && (
        <div
          className="game-result__content"
          dangerouslySetInnerHTML={{
            __html: gameConfig.get("waitResultMsg"),
          }}
        />
      )}
      {data !== undefined && data.type === "winning" && (
        <div className="game-result__winning">
          <div className="game-result__content">
            <div className="game-result__title">{data.data?.title}</div>
            <div
              dangerouslySetInnerHTML={{
                __html: gameConfig.get("winResultMsg", [
                  data.data?.correctAnswers,
                  data.data?.totalQuestion,
                  data.data?.totalPersons,
                ]),
              }}
            />
            <div className="game-result__price">
              {formatMoney(data.data?.winPrice || 0)}{" "}
              <span className="game-result__price--vnd">đ</span>
            </div>
          </div>
          <div className="game-result__qr" style={{ maxHeight: "20.833vw" }}>
            <div className="qr-content">
              <Icon name="vie-copy-right-yellow" />
              <p
                dangerouslySetInnerHTML={{
                  __html: gameConfig.get("tutorialQrCode"),
                }}
              />
            </div>
            <div className="qr-code">
              <canvas className="qr-code-inner" ref={qrRef} />
            </div>
          </div>
        </div>
      )}
      {data !== undefined && data.type === "lose" && (
        <div className="game-result__content">
          <div className="game-result__content--icon">
            <div>
              {gameConfig.get("loseGameMsg", [
                data.data?.correctAnswers,
                data.data?.totalQuestion,
              ])}
            </div>
            <img src={FaceIcon} alt="face" />
          </div>
          <div>
            {gameConfig.get("loseGameMsg2", [data.data?.campaignAward])}
          </div>
        </div>
      )}
      {!data || data.type !== "answer_error" ? (
        <div className="game-result__pause" onClick={onClose}>
          <Button focus={focusIndex === 0}>
            Đóng {data !== undefined && data.type === "lose" ? `(${time})` : ""}
          </Button>
        </div>
      ) : null}
    </div>
  );
};
export default GameResult;
