import React, { useEffect, useMemo, useState } from "react";
import classNames from "classnames";
import { concat, findIndex, isArray, merge, set } from "lodash";
import get from "lodash/get";
import { useSelector } from "react-redux";
import { useHistory } from "react-router-dom";
import useKeyProps from "hooks/useKeyProps";
import Message from "app/components/Common/Message";
import {
  FavoriteTipBox,
  ReturnTipBox,
} from "app/components/Common/Portal/TipBox";
import SubMenu, { SubMenuType } from "app/components/SubMenu/SubMenu";
import ChannelGrid from "app/containers/LiveTVNewUI/Components/ChannelGridPlayer";
import {
  liveTVAddFavoriteChannel,
  liveTVGetFavoriteList,
} from "app/endpoint/LiveTV/endpoint";
import NavBack from "app/layouts/components/NavBack";
import { Channel, ChannelDetail } from "app/models/LiveTV";
import { RootState } from "app/store/store";
import { GlobalFeatureFlowName } from "app/components/Tracking/GlobalFeatureTracking";
import { ROUTES } from "app/utils/constants";
import SegmentManager from "app/utils/SegmentManager";
import useCurrentPageRef from "hooks/useCurrentPageRef";
import { getPageRibbons } from "services/endpoint";
import KeepAlive from "app/utils/KeepAlive";
import dialogRoutines from "app/routines/dialog";
import { DialogType } from "app/components/DialogCommon/types";
import { EnumAuthTitle } from "app/containers/Authentication/type";
import { convertPxToVW } from "app/utils/Dom";
import ViePlayer from "app/components/Player/ViePlayer";
import useListData from "./hook";
import { useLiveTVPlayerActions } from "app/redux/Player/LiveTVPlayer";

interface Props {
  focus: boolean;
  registerKey: RegisterKey;
  index: number;
  id: string;
  onSelect: (channel: Channel) => void;
  onReturn: () => void;
  forPlayer?: boolean;
  channelDetail: ChannelDetail;
  panelFocus?: boolean;
  onSelfFocus?: () => void;
  onSetScrollDisplay: React.Dispatch<React.SetStateAction<boolean>>;
  onPopout: (direction: string) => void;
}

const PlayerChannel: React.FC<Props> = React.memo(
  ({
    focus: isFocus,
    registerKey: register,
    forPlayer = false,
    id,
    onSelect,
    onReturn,
    panelFocus,
    onSelfFocus,
    onSetScrollDisplay,
    onPopout,
  }) => {
    const [keepAliveDt, setKeepAliveDt] = useState(
      KeepAlive.getData(window.location.hash)
    );
    const history = useHistory();
    const isAuthen = useSelector((state: RootState) => state.app.isAuthen);
    const profile = useSelector((state: RootState) => state.app.profile);
    const currentPageRef = useCurrentPageRef();
    const [state, dispatch, stateRef] = useListData(keepAliveDt);
    const [canFavorite, setCanFavorite] = useState<boolean>(true);
    const [text, setText] = useState<string>("để thêm vào danh sách của bạn");
    const {
      isLoading,
      isLoadingRibbonData,
      error,
      highlightRibbon,
      ribbonList,
      ribbonData,
      activeRibbon,
    } = state;

    const keyHandler = (e: KeyboardEvent) => {
      keyHandlerFn[focusIndexRef.current]?.(e);
    };

    const [
      focusIndex,
      setFocusIndex,
      focusIndexRef,
      registerKey,
      unregisterKey,
      keyHandlerFn,
      onPopoutFactory,
    ] = useKeyProps({
      keyHandler,
      defaultFocusIndex: 0,
      register,
    });

    function onselect(index: number) {
      onReturn();
    }

    const subMenuOnPopout = (direction: Direction) => {
      switch (direction) {
        case "up":
          if (forPlayer && typeof onPopout === "function") {
            onPopout("up");
          }
          break;
        case "down":
          setFocusIndex(1);
          break;
        case "left":
          if (typeof onPopout === "function") {
            onPopout("up");
          }
          break;
        default:
          break;
      }
    };

    const subMenuOnFocus = () => {
      setFocusIndex(0);
    };

    const subMenuOnEnter = (item: SubMenuType) => {
      dispatch({
        type: "changeActiveRibbon",
        payload: {
          activeRibbon: { ...item },
        },
      });
    };

    const onGridPopout = (direction: Direction) => {
      switch (direction) {
        case "up":
          setFocusIndex(0);
          break;
        default:
          break;
      }
    };

    const onSubMenuReturn = ({
      focusIndex: subMenuFocusIndex,
      setFocusIndex: setSubMenuFocusIndex,
    }: {
      focusIndex: number;
      setFocusIndex: any;
    }) => {
      if (typeof onReturn === "function") {
        onReturn();
      }
    };

    const channelIndex = useMemo(() => {
      const focusIndex = get(keepAliveDt, "extra.focusIndex", -1);
      if (focusIndex < 1) return 0;
      const focusData = get(keepAliveDt, "extra.focusData");
      const listItems = get(ribbonData, [activeRibbon.id], []);
      return listItems.findIndex((item) => item.id === focusData);
    }, [activeRibbon.id, keepAliveDt, ribbonData]);

    useEffect(() => {
      if (!isLoadingRibbonData && !isLoading) {
        const focusIndex = get(keepAliveDt, "extra.focusIndex", -1);
        // Clear keep alive
        setTimeout(() => {
          setKeepAliveDt(null);
        }, 200);

        if (focusIndex < 0) return;

        onSelfFocus?.();
        setFocusIndex(focusIndex);
      }
    }, [isLoadingRibbonData, isLoading]);

    const saveKeepAlive = (item: any) => {
      const activeRibbonId = get(stateRef, ["current", "activeRibbon", "id"]);
      const data = {
        focusIndex: focusIndexRef.current,
        focusData: item.id,
        activeRibbon: activeRibbonId,
      };
      KeepAlive.saveData({
        path: window.location.hash,
        focus: {
          x: 0,
          y: 0,
        },
        extra: data,
      });
    };

    const onChannelEnter = (item: Channel) => {
      onSelect?.(item);
      saveKeepAlive(item);
    };

    const onChannelClick = (item: any) => {
      onSelect?.(item);
      saveKeepAlive(item);
    };

    useEffect(() => {
      if (
        focusIndex === 0 ||
        !isFocus ||
        !get(ribbonData, [activeRibbon.id], []).length
      ) {
        setCanFavorite(false);
      } else {
        setCanFavorite(true);
      }
    }, [focusIndex, isFocus, ribbonData, activeRibbon.id]);

    const toggleGridFavorite = (item: Channel) => {
      if (profile?.hotelAccount || !item) return;

      toggleFavorite(item, {
        type: "addFavoriteChannelOnList",
        payload: { channelId: get(item, "id", "") },
      });
    };

    const toggleFavorite = (item: any, action: any) => {
      const fetchAddFavorite = async (data: any, cb: any = () => {}) => {
        try {
          const id = get(data, "id");
          const res = await liveTVAddFavoriteChannel({
            livetv_ids: `["${id}"]`,
          }).catch(() => {});
          if (res) {
            cb(true);
          } else {
            cb(false);
          }
        } catch (err) {
          cb(false);
        }
      };
      const fetchRibbonList = async (pageId: string, cb: any = () => {}) => {
        try {
          let ribbonList: any[] = await getPageRibbons(pageId).catch(() => []);
          ribbonList = ribbonList.map(({ id, name, type }) => ({
            name,
            id,
            type,
          }));
          cb(ribbonList);
        } catch (err) {
          cb([]);
        }
      };
      const fetchFavoriteRibbon = async (cb: any = () => {}) => {
        try {
          const favoriteList = (await liveTVGetFavoriteList()) as any[];
          const computedData = favoriteList?.map(
            (item: any) => new Channel(item)
          );
          cb(computedData);
        } catch (err) {
          cb([]);
        }
      };
      if (isAuthen) {
        fetchAddFavorite(item, (isSuccess: boolean) => {
          if (isSuccess) {
            const isFavorite = get(item, "is_favorite");
            if (isFavorite) {
              Message.open("Đã gỡ kênh khỏi danh sách của bạn");
              const currentActive = get(stateRef, ["current", "activeRibbon"]);
              const currentActiveType = currentActive.type;
              // Khi grid dang active o favorite
              if (currentActiveType === 102) {
                // Nếu gỡ trên favorite tab cần refresh lại grid
                setTimeout(() => {
                  fetchFavoriteRibbon((favoriteList: any) => {
                    const meta = {};
                    // Refresh data
                    if (isArray(favoriteList) && favoriteList.length > 0) {
                      // Con favoriteList thi update lai favoriteList
                      setText("để gỡ khỏi danh sách của bạn");
                      set(meta, ["ribbonData", currentActive.id], favoriteList);
                    } else {
                      // Remove het se da ve tab 0
                      let ribbonList = get(stateRef, ["current", "ribbonList"]);
                      ribbonList = ribbonList.filter(
                        (i: any) => i.type !== 102
                      );
                      set(meta, ["ribbonList"], ribbonList);
                      set(meta, ["cb"], () => {
                        dispatch({
                          type: "changeActiveRibbon",
                          payload: {
                            activeRibbon: { ...ribbonList[0] },
                          },
                        });
                      });
                      merge(action, {
                        meta,
                      });
                      dispatch(action);
                      return;
                    }
                    merge(action, {
                      meta,
                    });
                    dispatch(action);
                  });
                }, 200);
                return;
              }
              setText("để thêm vào danh sách của bạn");
              // Khi grid dang active !== favorite
              const pageId = get(stateRef, ["current", "pageId"], "");
              setTimeout(() => {
                fetchRibbonList(pageId, (list: any) => {
                  const indexTemp = list.findIndex((item: any) =>
                    [102, 103].includes(item.type)
                  );
                  let ribbonList: any[] = [];
                  if (indexTemp === -1 || indexTemp > 0) {
                    ribbonList = concat(
                      [
                        {
                          id: "all",
                          name: "Tất cả",
                          type: 99,
                        },
                      ],
                      list
                    );
                  } else {
                    const insertIndex =
                      list.length > 1 && [102, 103].includes(list[1].type)
                        ? 2
                        : 1;
                    ribbonList = [...list];
                    ribbonList.splice(insertIndex, 0, {
                      id: "all",
                      name: "Tất cả",
                      type: 99,
                    });
                  }
                  merge(action, {
                    meta: {
                      ribbonList,
                    },
                  });
                  dispatch(action);
                });
              }, 200);
              return;
            }
            // Nếu là add thì kiểm tra đã có ribbon favorite trong ribbon list?
            const ribbonList = get(stateRef, ["current", "ribbonList"], []);
            const gotFavoriteList = findIndex(ribbonList, ["type", 102]) >= 0;
            if (!gotFavoriteList) {
              const pageId = get(stateRef, ["current", "pageId"], "");
              fetchRibbonList(pageId, (list: any) => {
                Message.open("Đã thêm kênh vào danh sách của bạn");
                setText("để gỡ khỏi danh sách của bạn");
                const indexTemp = list.findIndex((item: any) =>
                  [102, 103].includes(item.type)
                );
                let ribbonList: any[] = [];
                if (indexTemp === -1 || indexTemp > 0) {
                  ribbonList = concat(
                    [
                      {
                        id: "all",
                        name: "Tất cả",
                        type: 99,
                      },
                    ],
                    list
                  );
                } else {
                  const insertIndex =
                    list.length > 1 && [102, 103].includes(list[1].type)
                      ? 2
                      : 1;
                  ribbonList = [...list];
                  ribbonList.splice(insertIndex, 0, {
                    id: "all",
                    name: "Tất cả",
                    type: 99,
                  });
                }
                merge(action, {
                  meta: {
                    ribbonList,
                  },
                });
                dispatch(action);
              });
              return;
            }
            Message.open("Đã thêm kênh vào danh sách của bạn");
            setText("để gỡ khỏi danh sách của bạn");

            dispatch(action);
          }
        });
      } else {
        // const genAction = (pathname: string) => () => {
        const pathname = ROUTES.LOGIN_STEP;
        const searchParams = new URLSearchParams();
        searchParams.append("redirect", `${ROUTES.LIVE_TV}/${id}`);
        searchParams.append("state", "addFavorite");
        history.push({
          pathname,
          search: searchParams.toString(),
          state: {
            title: EnumAuthTitle.RegisterByAddToList,
            flowName: GlobalFeatureFlowName.RegistrationForAddToList,
          },
        });
        // SegmentManager.segmentAction("login button selected", {
        //   current_page: currentPageRef.current.name,ký
        //   popup_name: "Thêm kênh vào Danh sách của tôi",
        // });
        // };
        // const actionBack = () => {
        //   ViePlayer.play();
        // }
        // ViePlayer.pause();
        // dialogRoutines.showDialogAnonymousRequestLoginByType(
        //   DialogType.AnonymousUserRequireLoginAddFavorite,
        //   [genAction(ROUTES.LOGIN_PAGE_BY_PHONE), genAction(ROUTES.SIGNUP_PAGE)],
        //   actionBack
        // );
      }
    };

    return (
      <div
        className={classNames("player-channels-new-ui", {
          forPlayer,
        })}
      >
        {isLoading ? null : (
          <>
            {!forPlayer && (
              <NavBack focus={focusIndex === 2} navigateBack={() => {}} />
            )}
            <SubMenu
              forPlayer
              active={activeRibbon.id}
              subMenus={ribbonList}
              focus={isFocus && focusIndex === 0}
              popoutDirection={
                forPlayer ? null : forPlayer ? null : { left: true }
              }
              onPopout={subMenuOnPopout}
              onEnter={subMenuOnEnter}
              onFocus={subMenuOnFocus}
              registerKey={[
                registerKey.bind(null, "0"),
                unregisterKey.bind(null, "0"),
              ]}
              className="livetv__submenu--level-2"
              returnHandler={onSubMenuReturn}
              activeUnderLine
            />
            <div
              style={{
                overflow: "hidden",
                position: "relative",
                paddingLeft: `${convertPxToVW(11, "FullHD")}vw`,
                paddingTop: `${convertPxToVW(11, "FullHD")}vw`,
              }}
            >
              {isLoadingRibbonData ? null : (
                <ChannelGrid
                  idChannel={id}
                  forPlayer={forPlayer}
                  col={forPlayer ? 2 : 4}
                  focus={isFocus && focusIndex === 1}
                  onPopout={onGridPopout}
                  registerKey={[
                    registerKey.bind(null, "1"),
                    unregisterKey.bind(null, "1"),
                  ]}
                  items={get(ribbonData, [activeRibbon.id], [])}
                  returnHandler={() => {
                    onReturn?.();
                  }}
                  defaultIndex={channelIndex}
                  onEnter={onChannelEnter}
                  onCardClick={onChannelClick}
                  onKeyRed={toggleGridFavorite}
                  setFavoriteTip={setText}
                  itemDisplayNum={22}
                  onSetScrollDisplay={onSetScrollDisplay}
                />
              )}
            </div>
          </>
        )}
        {panelFocus && canFavorite && (
          <FavoriteTipBox
            tip={text}
            zIndex={20}
            hasBg={false}
            showRedButton={!profile || !profile?.hotelAccount}
          />
        )}
        {panelFocus && !canFavorite && <ReturnTipBox zIndex={20} />}
      </div>
    );
  }
);

export default PlayerChannel;
