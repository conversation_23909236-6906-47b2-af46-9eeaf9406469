import { useEffect } from "react";
import {
  useHistory,
  useLocation,
  useParams,
  useRouteMatch,
} from "react-router-dom";
import { ROUTES } from "app/utils/constants";
import { checkHomePage } from "app/utils/Route";
import platform from "services/platform";

export function useBrandPromotion() {
  const { id: menuId } = useParams<{ id: string }>();
  const history = useHistory();
  const location = useLocation();
  useEffect(() => {
    if (checkHomePage(location.pathname, menuId)) {
      const { onPromotion, onAutoPromotion } = platform;
      const isShowPromotion = sessionStorage.getItem("isShowPromotionOnHome");
      if ((onPromotion || onAutoPromotion) && !isShowPromotion) {
        sessionStorage.setItem("isShowPromotionOnHome", "1");
        setTimeout(() => {
          history.push({
            pathname: onPromotion ? ROUTES.PROMOTION : ROUTES.AUTO_PROMOTION,
          });
        }, 500);
        // history.push({
        //   pathname: onPromotion ? "/promotion" : "/auto-promotion",
        // });
      }
    }
  }, []);
}

export function useBrandPromotionForPaymentPage() {
  const history = useHistory();
  const isPaymentPage = !!useRouteMatch([
    ROUTES.PAYMENT,
    `${ROUTES.PAYMENT}/:id`,
  ]);
  const isVoucherPage = !!useRouteMatch(ROUTES.VOUCHER);
  const { packageId } = useParams<{ packageId: string }>();
  useEffect(() => {
    if (isPaymentPage || isVoucherPage) {
      const { onPromotion, onAutoPromotion, autoPromotionExcludePkg } =
        platform;
      const isShowPromotion = parseInt(
        sessionStorage.getItem("isShowPromotionInPayment") || "0",
        10
      );
      if (onPromotion && !isShowPromotion) {
        sessionStorage.setItem("isShowPromotionInPayment", "1");
        history.push({
          pathname: ROUTES.PROMOTION,
        });
        return;
      }
      if (
        onAutoPromotion &&
        !isShowPromotion &&
        parseInt(packageId, 10) !== autoPromotionExcludePkg
      ) {
        sessionStorage.setItem("isShowPromotionInPayment", "1");
        history.push({
          pathname: ROUTES.AUTO_PROMOTION,
        });
        return;
      }
    }
    return () => {
      if (sessionStorage.getItem("isShowPromotionInPayment")) {
        sessionStorage.setItem("isShowPromotionInPayment", "0");
      }
    };
  }, []);
}
