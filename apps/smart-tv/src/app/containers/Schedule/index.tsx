import React, { use<PERSON>allback, useEffect, useMemo, useRef } from "react";
import classNames from "classnames";

import {
  Redirect,
  Route,
  Switch,
  useHistory,
  useParams,
} from "react-router-dom";
import { useSelector } from "react-redux";
import { FocusContext, VieOnNavigation, useFocusable } from "core/KeyHandle";
import { FOCUS_KEY_SIDEBAR } from "app/App";
import SubMenuNew, { SubMenuType } from "app/components/SubMenu/SubMenuNew";
import { ROUTES } from "app/utils/constants";
import { convertPX } from "app/utils/Dom";
import KeepAlive, { KeepAliveData } from "app/utils/KeepAlive";
import platform from "services/platform";
import { scrollToElement } from "app/utils/animation";
import Spinner from "app/components/Common/Spinner";
import MastheadAds from "app/components/Ads/MastheadAds/MastheadAdsElement";
import { RootState } from "app/store/store";
import useMastheadAdsLogic from "app/components/Ads/MastheadAds/useMastbeadAdsLogic";
import { VideoType } from "types/page";
import ScheduleFocusBox from "./components/ScheduleFocusBox";
import ScheduleEmpty from "./components/ScheduleEmpty";
import { useSchedule } from "./hook";
import styles from "./style.module.scss";
import ScheduleList from "./components/ScheduleList";
import {
  KEEP_FOCUS_KEY,
  SCHEDULE_CARD_FOCUS_KEY,
} from "./components/ScheduleCard";

export const SCHEDULE_FOCUS_KEY = "VN:SCHEDULE_PAGE";

const SCHEDULE_TABS_FOCUS_KEY = "VN:SCHEDULE_TABS";

const MARGIN_ITEM = 36;

export const TOP_ITEM = 50;

const Schedule: React.FC = () => {
  const isShowMastheadAds = useSelector(
    (state: RootState) => state.app.mastheadAds.status === "COLLAPSE"
  );

  const { toggleVisibleAdsInViewport } = useMastheadAdsLogic();

  const history = useHistory();
  const {
    schedule,
    isLoading,
    isItemLoading,
    page,
    tabs,
    fetchMore,
    fetchData,
  } = useSchedule();
  const { subMenuId } = useParams<{ subMenuId: string }>();
  const scheduleRef: any = useRef();
  const currentIndex = useRef(0);
  const focusByArrow = useRef(false);
  const focusByWheel = useRef(false);
  const keepAliveDataRef: any = useRef(null);

  const config = useMemo(() => {
    const offsetWidth =
      document?.getElementsByTagName("body")?.[0]?.offsetWidth;
    const screen: any = {
      1920: {
        width: 1516,
        height: 1004 - (isShowMastheadAds ? convertPX(226, 1920) : 0),
        screenWidth: 1920,
        display: "FullHD",
      },
      1280: {
        width: 1013.666,
        height: 673.6767 - (isShowMastheadAds ? convertPX(226, 1280) : 0),
        screenWidth: 1280,
        display: "HD",
      },
    };
    return screen?.[offsetWidth] || screen[1920];
  }, [isShowMastheadAds]);

  const tabDatas: any = useMemo(() => {
    return tabs;
  }, [tabs]);

  const paramId = useMemo(
    () =>
      subMenuId && subMenuId !== "undefined" ? subMenuId : tabDatas[0]?.id,
    [tabDatas, subMenuId]
  );

  const scrollTop: any = useRef(
    convertPX(TOP_ITEM - (isShowMastheadAds ? 226 : 0), config?.screenWidth)
  );

  const initialScrollOffset = useMemo(() => {
    const data = keepAliveDataRef.current;
    return data?.extra?.top
      ? data?.extra?.top - convertPX(TOP_ITEM, config?.screenWidth)
      : scrollTop.current
        ? scrollTop.current - convertPX(TOP_ITEM, config?.screenWidth)
        : 0;
  }, [keepAliveDataRef.current, scrollTop.current, isShowMastheadAds]);

  const onFocusFirst = useCallback(() => {
    if (!focusByArrow.current && !focusByWheel.current) {
      const data = keepAliveDataRef.current;
      if (data?.focusKey) {
        VieOnNavigation.setCurrentFocusedKey(data?.focusKey, {});
      }
    }
  }, [
    keepAliveDataRef.current,
    !focusByWheel.current,
    keepAliveDataRef.current,
  ]);

  const { setFocus, getNodeLayoutByFocusKey, focusKey, ref } = useFocusable({
    focusKey: SCHEDULE_FOCUS_KEY,
    preferredChildFocusKey: `VN:SUB_MENU/${paramId?.toUpperCase()}`,
    autoRestoreFocus: false,
    onBlur: () => {
      ScheduleFocusBox.onShow(false);
    },
    onFocus: onFocusFirst,
  });

  const getItemSize = (_: any, screenWidth: number) => {
    return convertPX(410, screenWidth) + convertPX(32, screenWidth);
  };

  const smoothScrollToElement = useCallback(scrollToElement, []);

  const handleSmoothScroll = useCallback(
    (elm: HTMLElement, targetY: number, behavior?: any) => {
      if (elm) {
        const duration =
          platform.disableTransition || behavior === "auto" ? 0 : 350;
        smoothScrollToElement(elm, targetY, duration);
      }
    },
    [smoothScrollToElement]
  );

  const handleScroll = useCallback(
    (scroll: number, behavior?: any) => {
      handleSmoothScroll(
        scheduleRef.current?._outerRef,
        scroll - convertPX(TOP_ITEM, config?.screenWidth),
        behavior
      );
    },
    [scheduleRef?.current, handleSmoothScroll]
  );

  const onEnter = useCallback(
    (item: SubMenuType) => {
      if (subMenuId !== item.id) {
        const pathname = `${ROUTES.SCHEDULE}/${item.id}`;
        if (pathname) {
          const location = {
            pathname,
          };
          history.replace(location);
        }
      }
    },
    [history.replace, subMenuId, VieOnNavigation.setCurrentFocusedKey]
  );

  const onFocus = useCallback(() => {
    ScheduleFocusBox.onShow(false);
  }, [ScheduleFocusBox]);

  const onFocusItem = useCallback<(_: any, top: any, _detail: any, index: any) => void>(
    (_, top, _detail, index) => {
      if (!_detail?.implicit) {
        handleScroll(top);
        ScheduleFocusBox.onShow(true);
        scrollTop.current = top;
      } else {
        ScheduleFocusBox.onShow(false);
      }
      currentIndex.current = index;
    },
    [ScheduleFocusBox.onShow, handleScroll]
  );

  const onScroll = useCallback(
    (e: any) => {
      const top = scrollTop.current - convertPX(TOP_ITEM, config?.screenWidth);
      if (
        e.scrollOffset >= top - 1 &&
        currentIndex.current === schedule?.length - 1 &&
        subMenuId
      ) {
        fetchMore(subMenuId);
      }
    },
    [
      scrollTop.current,
      currentIndex.current,
      schedule?.length,
      subMenuId,
      config?.screenWidth,
    ]
  );

  const onWheel = useCallback(() => {
    keepAliveDataRef.current = null;
    if (focusByArrow.current) {
      focusByArrow.current = false;
    }
    if (!focusByWheel.current) {
      focusByWheel.current = true;
    }
  }, [focusByWheel?.current, focusByArrow?.current]);

  const onScrollbyWheel = useCallback(
    (e: any) => {
      keepAliveDataRef.current = null;
      if (!focusByWheel.current) {
        focusByWheel.current = true;
        focusByArrow.current = false;
      }
      if (!!focusByWheel.current && !focusByArrow.current) {
        const nextKey = getNodeLayoutByFocusKey(
          `${SCHEDULE_CARD_FOCUS_KEY}_${currentIndex.current + 1}`
        );
        const prevKey = getNodeLayoutByFocusKey(
          `${SCHEDULE_CARD_FOCUS_KEY}_${currentIndex.current - 1}`
        );
        if (e.deltaY < 0 && prevKey) {
          setFocus(
            `${SCHEDULE_CARD_FOCUS_KEY}_${currentIndex.current - 1}`,
            {}
          );
        } else if (e.deltaY > 0 && nextKey) {
          setFocus(
            `${SCHEDULE_CARD_FOCUS_KEY}_${currentIndex.current + 1}`,
            {}
          );
        }
      }
    },
    [
      focusByWheel.current,
      focusByArrow.current,
      currentIndex.current,
      getNodeLayoutByFocusKey,
      setFocus,
    ]
  );

  const onEnterPress = useCallback<(id: any, top: any, focusKey: any, type: any, slug: any) => void>(
    (id, top, focusKey, type, slug) => {
      const keepAliveData: KeepAliveData = {
        path: window.location.hash,
        focus: {
          x: 0,
          y: top,
        },
        extra: {
          top,
          page,
        },
        focusKey,
      };
      KeepAlive.saveData(keepAliveData);
      const paths: any = {
        [VideoType.LIVETV]: ROUTES.LIVE_TV,
        [VideoType.EPG]: ROUTES.LIVE_TV,
        [VideoType.LIVESTREAM]: ROUTES.VIEW_STREAM,
        [VideoType.MOVIE]: ROUTES.VIDEO_INTRO,
        [VideoType.SEASON]: ROUTES.VIDEO_INTRO,
        [VideoType.SHOW]: ROUTES.VIDEO_INTRO,
        [VideoType.EPISODE]: ROUTES.VIDEO_INTRO,
        [VideoType.TRAILER]: ROUTES.VIDEO_INTRO,
      };
      if (id && paths?.[type]) {
        const searchParams = new URLSearchParams();
        switch (type) {
          case VideoType.EPG:
            searchParams.append("epg", slug);
            break;
          case VideoType.MOVIE:
          case VideoType.SEASON:
          case VideoType.SHOW:
          case VideoType.EPISODE:
          case VideoType.TRAILER:
            if (subMenuId === tabDatas?.[0]?.id) {
              searchParams.append("state", "player");
            }
            break;
          default:
            break;
        }
        history.push({
          pathname: `${paths?.[type]}/${id}`,
          search: searchParams.toString(),
          state: {
            page: "schedule",
          },
        });
      }
    },
    [KeepAlive.saveData, history.push, page, subMenuId, tabDatas]
  );

  const onArrowPress = useCallback(() => {
    keepAliveDataRef.current = null;
    if (!focusByArrow.current) {
      focusByArrow.current = true;
      focusByWheel.current = false;
    }
    return true;
  }, [focusByArrow?.current, currentIndex.current]);

  const onReturnPress = useCallback(() => {
    setFocus(`VN:SIDEBAR_ITEM_/schedule`);
  }, [setFocus]);

  const scheduleData = useMemo(() => {
    // eslint-disable-next-line array-callback-return
    schedule?.map((value: any) => {
      value.isNowShowing = subMenuId === tabDatas?.[0]?.id;
      value.onFocus = onFocusItem;
      value.onArrowPress = onArrowPress;
      value.onEnterPress = onEnterPress;
      value.onFocusFirst = onFocusFirst;
      value.onReturnPress = onReturnPress;
      value.toggleVisibleAdsInViewport = toggleVisibleAdsInViewport;
    });

    return schedule || [];
  }, [
    schedule,
    subMenuId,
    tabDatas,
    onFocusItem,
    onEnterPress,
    onArrowPress,
    onReturnPress,
    toggleVisibleAdsInViewport,
  ]);

  useEffect(() => {
    keepAliveDataRef.current = null;
    if (subMenuId) {
      setFocus(`VN:SUB_MENU/${subMenuId?.toUpperCase()}`);
      localStorage?.setItem(KEEP_FOCUS_KEY, `${SCHEDULE_CARD_FOCUS_KEY}_0`);
      fetchData(subMenuId);
    }
    scrollTop.current = convertPX(0, config?.screenWidth);
  }, [subMenuId]);

  useEffect(() => {
    if (paramId) {
      history.replace(`${ROUTES.SCHEDULE}/${paramId}`);
      localStorage?.setItem(KEEP_FOCUS_KEY, `${SCHEDULE_CARD_FOCUS_KEY}_0`);
      if (!focusByArrow.current) {
        setFocus(`VN:SUB_MENU/${paramId?.toUpperCase()}`);
      }
    }
  }, [tabs]);

  useEffect(() => {
    ref?.current?.addEventListener("wheel", onScrollbyWheel);
    return () => {
      ref?.current?.removeEventListener("wheel", onScrollbyWheel);
    };
  }, [ref?.current]);

  useEffect(() => {
    const data = KeepAlive.getData(window.location.hash);
    if (data) {
      keepAliveDataRef.current = data;
      if (subMenuId) {
        fetchData(subMenuId, data?.extra?.page);
      }
    }
  }, []);

  const renderTabs = useMemo(() => {
    return (
      <SubMenuNew
        active={paramId}
        subMenus={tabDatas}
        onEnter={onEnter}
        className={styles.submenu}
        itemClassName={classNames(styles["submenu-item"], "thin-line")}
        focusClassName={styles.focus}
        focusKey={SCHEDULE_TABS_FOCUS_KEY}
        onFocus={onFocus}
        onPopout={() => setFocus(FOCUS_KEY_SIDEBAR)}
        activeUnderLine
      />
    );
  }, [paramId, tabDatas, onEnter, onFocus, setFocus]);

  const renderSchedule: any = useMemo(() => {
    if (scheduleData.length <= 0) {
      return (
        <ScheduleEmpty
          onHide={() => ScheduleFocusBox.onShow(false)}
          isShowMastheadAds={isShowMastheadAds}
        />
      );
    }
    return (
      <ScheduleList
        ref={scheduleRef}
        itemData={scheduleData}
        config={config}
        overScrollItem={scheduleData?.length * 10}
        initialScrollOffset={initialScrollOffset}
        className={styles["infinity-list"]}
        isItemLoading={isItemLoading}
        cardNamespace="ScheduleCard"
        getItemSize={getItemSize}
        // handleScroll={handleScroll}
        onWheel={onWheel}
        onScroll={onScroll}
      />
    );
  }, [
    scheduleData,
    isShowMastheadAds,
    ScheduleFocusBox.onShow,
    scheduleRef,
    config,
    initialScrollOffset,
    tabs,
    getItemSize,
    onWheel,
    onScroll,
  ]);

  return (
    <FocusContext.Provider value={focusKey}>
      <div
        ref={ref}
        className={`${styles["schedule-page"]}  ${
          isShowMastheadAds && styles["masthead-ads"]
        }`}
      >
        {renderTabs}
        {isLoading ? (
          <Spinner className={styles.spinner} />
        ) : (
          <>
            <MastheadAds />
            <div className="main__body">
              <ScheduleFocusBox />
              <Switch>
                {!!paramId && (
                  <Redirect
                    exact
                    from={ROUTES.SCHEDULE}
                    to={`${ROUTES.SCHEDULE}/${paramId}`}
                  />
                )}

                {tabDatas?.map((tab: any, index: number) => (
                  <Route
                    key={tab?.id || index}
                    path={`${ROUTES.SCHEDULE}/${tab?.id}`}
                    component={() => renderSchedule}
                  />
                ))}
              </Switch>
            </div>
          </>
        )}
      </div>
    </FocusContext.Provider>
  );
};

export default Schedule;
