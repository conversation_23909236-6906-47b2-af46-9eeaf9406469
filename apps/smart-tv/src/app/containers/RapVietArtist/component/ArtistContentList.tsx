import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import classNames from "classnames";
import get from "lodash/get";
import { animate, interpolate, linear } from "popmotion";
import slice from "lodash/slice";
import platform from "services/platform";
import useKeyProps from "hooks/useKeyProps";
import ArtistContentItem from "./ArtistContentItem";
import SCSSModule from "./../RapVietArtist.module.scss";
import { ContentItem } from "../data/type";

const ITEMS_IN_ROW = 3;
const MAX_ITEMS_SHOW = 24;

interface Props {
  items: any[];
  focus: boolean;
  registerKey: RegisterKey;
  onPopout?: PopoutHandler;
  jumpRef?: (fn: any) => void;
  onNextPage?: () => void;
  onReturn?: () => void;
  onEnter?: (index: number) => void;
  onClickItem?: (item: ContentItem) => void;
}

const ArtistContentList: React.FC<Props> = ({
  items = [],
  focus: isFocus,
  registerKey: register,
  onPopout,
  jumpRef = () => {},
  onNextPage,
  onReturn,
  onEnter,
  onClickItem,
}) => {
  const focusBoxRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);
  const animateRef = useRef<{ stop: () => void } | null>(null);
  const [count, setCount] = useState<number>(0);
  const focusIndexXRef = useRef<number>(0);
  const focusIndexYRef = useRef<number>(0);
  const isOnTranslate = useRef<boolean>(false);
  const TRANSITION_DURATION = useRef<number>(
    platform.disableTransition ? 0 : 150
  );
  const canReceiveNextKey = useRef<boolean>(true);
  const waitList = useRef<KeyboardEvent | null>(null);
  const [showItem, setShowItem] = useState<any[]>(() => {
    const leng = items.length;
    if (leng < MAX_ITEMS_SHOW) {
      return items;
    }
    return slice(items, 0, MAX_ITEMS_SHOW);
  });

  const keyHandler = (e: KeyboardEvent) => {
    const { keys } = platform;
    if (focusIndexXRef.current === 0 && keys.left === e.keyCode) {
      if (onPopout && typeof onPopout === "function") {
        onPopout("left");
      }
      return;
    }
    if (focusIndexYRef.current === 0 && keys.up === e.keyCode) {
      if (onPopout && typeof onPopout === "function") {
        onPopout("up");
      }
      return;
    }
    if (!canReceiveNextKey.current) return;
    if (isOnTranslate.current) {
      waitList.current = e;
      return;
    }
    switch (e.keyCode) {
      case keys.up:
        moveUp();
        break;
      case keys.down:
        moveDown();
        break;
      case keys.left:
        moveLeft();
        break;
      case keys.right:
        moveRight();
        break;
      case keys.enter:
        const currentFocus =
          focusIndexYRef.current * ITEMS_IN_ROW + focusIndexXRef.current;
        if (onEnter && typeof onEnter === "function") {
          onEnter(currentFocus);
        }
        break;
      case keys.return: {
        if (focusIndexXRef.current !== 0 || focusIndexYRef.current !== 0) {
          jump(0);
          return;
        }
        if (onReturn && typeof onReturn === "function") {
          onReturn();
        }
        break;
      }
      default:
        break;
    }
  };
  useKeyProps({
    keyHandler,
    defaultFocusIndex: 0,
    register,
  });
  const getCurrentFocusRow = useCallback((focusIndexY: number) => {
    switch (focusIndexY) {
      case 0:
        return 0;
      case 1:
        return 1;
      default:
        return 2;
    }
  }, []);
  const moveLeft = () => {
    const currentFocus = focusIndexXRef.current;
    if (currentFocus === 0) return;
    const row = getCurrentFocusRow(focusIndexYRef.current);
    const itemIndex = row * ITEMS_IN_ROW + currentFocus;
    const nextItemIndex = itemIndex - 1;
    const gridChild = get(gridRef, ["current", "children"]);
    const currentItem = get(gridChild, [itemIndex]);
    const nextItem = get(gridChild, [nextItemIndex]);
    if (!currentItem || !nextItem) return;
    canReceiveNextKey.current = false;
    isOnTranslate.current = true;
    const from = get(currentItem, ["offsetLeft"], 0);
    const to = get(nextItem, ["offsetLeft"], 0);
    animateRef.current = animate({
      to: [from, to],
      ease: linear,
      duration: TRANSITION_DURATION.current,
      onUpdate: (v) => {
        const focusBox = focusBoxRef.current;
        if (!focusBox) return;
        focusBox.style.transform = `translateX(${v}px)`;
      },
      onComplete: () => {
        isOnTranslate.current = false;
        canReceiveNextKey.current = true;
        focusIndexXRef.current = Math.max(focusIndexXRef.current - 1, 0);
        setCount((i) => i + 1);
        const event = waitList.current;
        waitList.current = null;
        setTimeout(() => {
          if (event) {
            keyHandler(event);
          }
        });
      },
    });
  };
  const moveRight = () => {
    const currentFocus = focusIndexXRef.current;
    if (currentFocus === ITEMS_IN_ROW - 1) return;
    const row = getCurrentFocusRow(focusIndexYRef.current);
    const itemIndex = row * ITEMS_IN_ROW + currentFocus;
    const nextItemIndex = itemIndex + 1;
    const gridChild = get(gridRef, ["current", "children"]);
    const currentItem = get(gridChild, [itemIndex]);
    const nextItem = get(gridChild, [nextItemIndex]);
    if (!currentItem || !nextItem) return;
    canReceiveNextKey.current = false;
    isOnTranslate.current = true;
    const from = get(currentItem, ["offsetLeft"], 0);
    const to = get(nextItem, ["offsetLeft"], 0);
    animateRef.current = animate({
      to: [from, to],
      ease: linear,
      duration: TRANSITION_DURATION.current,
      onUpdate: (v) => {
        const focusBox = focusBoxRef.current;
        if (!focusBox) return;
        focusBox.style.transform = `translateX(${v}px)`;
      },
      onComplete: () => {
        isOnTranslate.current = false;
        canReceiveNextKey.current = true;
        focusIndexXRef.current = Math.min(
          focusIndexXRef.current + 1,
          ITEMS_IN_ROW
        );
        setCount((i) => i + 1); // trigger update ref
        const event = waitList.current;
        waitList.current = null;
        setTimeout(() => {
          if (event) {
            keyHandler(event);
          }
        });
      },
    });
  };
  const moveDown = useCallback(() => {
    const currentFocus = getCurrentFocusRow(focusIndexYRef.current);
    const itemIndex = currentFocus * ITEMS_IN_ROW + focusIndexXRef.current;
    const nextItemIndex = itemIndex + ITEMS_IN_ROW;
    const gridChild = get(gridRef, ["current", "children"]);
    const currentItem = get(gridChild, [itemIndex]);
    const nextItem = get(gridChild, [nextItemIndex]);
    if (!nextItem || !currentItem) return;
    canReceiveNextKey.current = false;
    isOnTranslate.current = true;
    const from = get(currentItem, ["offsetTop"], 0);
    const to = get(nextItem, ["offsetTop"], 0);
    const getAnimationPercent = interpolate<number>([from, to], [0, 100]);
    animateRef.current = animate({
      to: [from, to],
      ease: linear,
      duration: TRANSITION_DURATION.current,
      onUpdate: (v) => {
        const progress = getAnimationPercent(v);
        if (progress > 50) {
          canReceiveNextKey.current = true;
        }
        const grid = gridRef.current;
        if (!grid) return;
        grid.style.transform = `translateY(-${v}px)`;
      },
      onComplete: () => {
        focusIndexYRef.current += 1;
        setCount((i) => i + 1);
      },
    });
  }, [items]);
  const moveUp = useCallback(() => {
    const currentFocus = getCurrentFocusRow(focusIndexYRef.current);
    const itemIndex = currentFocus * ITEMS_IN_ROW + focusIndexXRef.current;
    const nextItemIndex = itemIndex - ITEMS_IN_ROW;
    const gridChild = get(gridRef, ["current", "children"]);
    const currentItem = get(gridChild, [itemIndex]);
    const nextItem = get(gridChild, [nextItemIndex]);
    if (!nextItem || !currentItem) return;
    canReceiveNextKey.current = false;
    isOnTranslate.current = true;
    const from = get(currentItem, ["offsetTop"], 0);
    const to = get(nextItem, ["offsetTop"], 0);
    const getAnimationPercent = interpolate<number>([from, to], [0, 100]);
    animateRef.current = animate({
      to: [from, to],
      ease: linear,
      duration: TRANSITION_DURATION.current,
      onUpdate: (v) => {
        const progress = getAnimationPercent(v);
        if (progress > 50) {
          canReceiveNextKey.current = true;
        }
        const grid = gridRef.current;
        if (!grid) return;
        grid.style.transform = `translateY(-${v}px)`;
      },
      onComplete: () => {
        focusIndexYRef.current -= 1;
        setCount((i) => i + 1);
      },
    });
  }, [items]);
  const jumpTo = (toX: number, toY: number) => {
    const focusRow = getCurrentFocusRow(toY);
    const itemIndex = focusRow * ITEMS_IN_ROW + toX;
    const grid = get(gridRef, "current");
    const toItem = get(grid, ["children", itemIndex]) as HTMLDivElement;
    const focusBox = get(focusBoxRef, "current");
    if (!toItem || !grid || !focusBox) return;
    const yPos = toItem.offsetTop;
    const xPos = toItem.offsetLeft;
    grid.style.transform = `translateY(-${yPos}px)`;
    focusBox.style.transform = `translateX(${xPos}px)`;
  };

  const jump = (index: number) => {
    const toY = Math.floor(index / ITEMS_IN_ROW);
    const toX = index % ITEMS_IN_ROW;
    focusIndexXRef.current = toX;
    focusIndexYRef.current = toY;
    jumpTo(toX, toY);
    setCount((i) => i + 1);
  };
  useEffect(() => {
    const sliceFrom = Math.max(focusIndexYRef.current - 2, 0) * ITEMS_IN_ROW;
    const showArr = slice(items, sliceFrom, sliceFrom + MAX_ITEMS_SHOW);
    if (
      focusIndexYRef.current * ITEMS_IN_ROW + focusIndexXRef.current >
        items.length - 1 &&
      items.length
    ) {
      focusIndexYRef.current = Math.floor((items.length - 1) / ITEMS_IN_ROW);
      focusIndexXRef.current = Math.floor((items.length - 1) % ITEMS_IN_ROW);
      jumpTo(focusIndexXRef.current, focusIndexYRef.current);
    }
    setShowItem(showArr);
  }, [items, count]);

  useEffect(() => {
    if (
      items.length > ITEMS_IN_ROW * 3 &&
      focusIndexYRef.current * ITEMS_IN_ROW + focusIndexXRef.current >=
        items.length - ITEMS_IN_ROW * 3
    ) {
      if (onNextPage && typeof onNextPage === "function") {
        onNextPage();
      }
    }
  }, [count]);

  useLayoutEffect(() => {
    isOnTranslate.current = false;
    const event = waitList.current;
    waitList.current = null;
    const grid = gridRef.current;
    if (!grid) return;
    const focusRow = getCurrentFocusRow(focusIndexYRef.current);
    const focusEl = get(
      grid,
      ["children", focusRow * ITEMS_IN_ROW],
      null
    ) as HTMLDivElement;
    if (!focusEl) return;
    grid.style.transform = `translateY(-${focusEl.offsetTop}px)`;
    setTimeout(() => {
      if (event) {
        keyHandler(event);
      }
    });
  }, [showItem]);

  useEffect(() => {
    jumpRef(jump);
    return () => {
      jumpRef(null);
    };
  }, []);

  const handleClickItem = (item: ContentItem) => {
    if (onClickItem && typeof onClickItem === "function") {
      onClickItem(item);
    }
  };

  return (
    <div className={classNames(SCSSModule.artistContentList)}>
      <div
        className="grid"
        style={{ transform: "translateY(0px)" }}
        ref={gridRef}
      >
        {showItem.map((item) => (
          <ArtistContentItem
            key={item.id}
            isFocus={false}
            {...item}
            isHidden={false}
            thumbnail={item.thumbnail}
            onClick={() => handleClickItem(item)}
          />
        ))}
      </div>
      {isFocus && showItem.length > 0 && (
        <div
          ref={focusBoxRef}
          className={classNames(SCSSModule.artistContentList__FocusBox)}
        />
      )}
    </div>
  );
};
export default ArtistContentList;
