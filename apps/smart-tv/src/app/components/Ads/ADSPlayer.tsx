import React, { useEffect, useRef, useState } from "react";

import { VASTClient, VASTTracker } from "@dailymotion/vast-client";
import Hls from "hls.js";
import classNames from "classnames";
import get from "lodash/get";
import useStateRef from "hooks/useStateRef";
import SegmentManager from "app/utils/SegmentManager";
import useKeyService from "hooks/useKeyService";
import platform from "services/platform";
import { AdsURLModel, SLOT_NAME } from "app/models";
import AIActiv from "./AIActiv";
import { TypeADS } from "./ADSBox";
import { trackingCallAdsRequest } from "./TrackingAds";

interface Props {
  vieONDeviceID: string;
  linkADSArr: { [key: string]: AdsURLModel[] };
  idContent: string;
  titleContent: string;
  typeContent: string;
  page: string;
  type: TypeADS;
  timeStartAds: number;
  numberOfAds: number;
  handleEndAds: () => void;
  handleReturn: () => void;
}

const VADSPlayer: React.FC<Props> = ({
  vieONDeviceID,
  linkADSArr,
  idContent,
  titleContent,
  typeContent,
  page,
  type,
  timeStartAds,
  numberOfAds,
  handleEndAds,
  handleReturn,
}) => {
  const [showPlayer, setShowPlayer] = useState(false);

  const player = useRef<HTMLVideoElement | null>(null);
  const loading = useRef<HTMLDivElement | null>(null);

  const isunmount = useRef(false);

  const startTracking = useRef(false);
  const firstQuartileTracking = useRef(false);
  const midpointTracking = useRef(false);
  const thirdQuartileTracking = useRef(false);

  const playerHls = useRef<any>(undefined);

  const linkADS = useRef<{ [key: string]: AdsURLModel[] }>({ ...linkADSArr });

  const currentLinkAds = useRef<AdsURLModel>({
    url: "",
    skip: 0,
    timeSkip: 0,
  });

  const macros = {
    ADVERTISING_IDENTIFIER_TYPE: vieONDeviceID,
    IFA: vieONDeviceID,
    CACHEBUSTER: 0,
    WIDTH: platform.screenType === "HD" ? 1280 : 1920,
    HEIGHT: platform.screenType === "HD" ? 720 : 1080,
    DID: platform.vieONDeviceID,
    IP: platform.ip,
    APP_VERSION: platform.appVersion,
  };

  const [start, setStart, startRef] = useStateRef(false);
  const adsVastTracker = useRef<any>(undefined);
  const adsListCustomEvent = useRef<string[]>([]);
  const [isHaveSkip, setIsHaveSkip, isHaveSkipRef] = useStateRef(false);
  const adsTotalTimeSkip = useRef(0);
  const [adsTimeSkip, setAdsTimeSkip, adsTimeSkipRef] = useStateRef(0);
  const numberStartAds = useRef(0);
  // const numberRequestFirst = useRef(0);

  const keyHandler = (e: KeyboardEvent) => {
    const { keys } = platform;
    if (e.keyCode === keys.enter) {
      onSelectButton(focusIndex);
    } else if (e.keyCode === keys.return) {
      handleReturn();
    }
  };

  const [
    focusIndex,
    setFocusIndex,
    focusIndexRef,
    registerKeyHandler,
    unregisterKeyHandler,
    keyHandlerFn,
    onPopoutFactory,
  ] = useKeyService({
    keyHandler,
    defaultFocusIndex: 0,
  });

  useEffect(() => {
    getLinkADS();
    return () => {
      isunmount.current = true;
      if (Hls.isSupported() && playerHls.current) {
        playerHls.current.on(Hls.Events.MEDIA_DETACHED, () => {});
        playerHls.current.detachMedia();
        playerHls.current.destroy();
        playerHls.current = undefined;
      }
    };
  }, []);

  function replaceUrlMacros(url: string, macros: any) {
    let replacedMacrosUrl = url;
    for (const key in macros) {
      let value = macros[key];
      if (key === "CACHEBUSTER") value = Date.now();
      replacedMacrosUrl = replacedMacrosUrl.replace(
        new RegExp(`(?:\\[|%%|\\$\\$)(${key})(?:\\]|%%|\\$\\$)`, "g"),
        value
      );
    }

    return replacedMacrosUrl;
  }

  function resetInfoAds() {
    startTracking.current = false;
    firstQuartileTracking.current = false;
    midpointTracking.current = false;
    thirdQuartileTracking.current = false;
    playerHls.current = undefined;
    currentLinkAds.current = {
      url: "",
      skip: 0,
      timeSkip: 0,
    };
    setStart(false);
    adsVastTracker.current = undefined;
    adsListCustomEvent.current = [];
    setIsHaveSkip(false);
    adsTotalTimeSkip.current = 0;
    setAdsTimeSkip(0);
  }

  function trackinagAds(id: string, statusAds: boolean, message: string) {
    const typeAds =
      type === "preroll"
        ? "pre-roll"
        : type === "midroll"
        ? "mid-roll"
        : "post-roll";
    const status = statusAds ? "success" : "failed";
    trackingCallAdsRequest(
      "instream_ads",
      id,
      idContent,
      typeAds,
      "",
      `${numberStartAds.current + 1}`,
      status,
      message
    );
  }

  function getLinkADS() {
    resetInfoAds();
    if (
      linkADS.current?.[`${SLOT_NAME}${numberStartAds.current + 1}`]?.length > 0
    ) {
      currentLinkAds.current =
        linkADS.current[`${SLOT_NAME}${numberStartAds.current + 1}`]?.[0];
      linkADS.current[`${SLOT_NAME}${numberStartAds.current + 1}`] =
        linkADS.current[`${SLOT_NAME}${numberStartAds.current + 1}`].filter(
          (_, index) => index !== 0
        );
      const asUrl = currentLinkAds.current?.url || "";
      if (asUrl !== "") {
        const regexPatternAIActiv = /^aiactiv:\/\/(?<inventoryId>.*$)/i.exec(
          asUrl
        );
        const inventoryId = get(regexPatternAIActiv, "groups.inventoryId");
        if (regexPatternAIActiv && inventoryId) {
          AIActiv.getVASTTagByInventory(
            { inventoryId: Number(inventoryId), placementId: "ads-popup" },
            (vastTag) => {
              if (vastTag !== "") {
                parseADS(vastTag, inventoryId);
              } else {
                getLinkADS();
                trackinagAds(inventoryId, false, "");
              }
            }
          );
        } else {
          parseADS(asUrl);
        }
      } else {
        getLinkADS();
      }
    } else {
      endADS("error");
    }
  }
  function parseADS(asUrl: string, idAA?: string) {
    // numberRequestFirst.current++;
    onBuffer();
    if (asUrl.indexOf("&correlator=") !== -1) {
      asUrl += new Date().getTime();
    }
    const vastClient = new VASTClient();
    const vastParser = vastClient.getParser();
    vastParser.addURLTemplateFilter((url: string) => {
      return replaceUrlMacros(url, macros);
    });
    vastClient
      .get(asUrl, {
        timeout: 2000,
        withCredentials: true,
        allowMultipleAds: true,
      })
      .then((response: any) => {
        if (
          response &&
          response !== undefined &&
          response.ads &&
          response.ads.length > 0 &&
          !isunmount.current
        ) {
          const vastTracker = new VASTTracker(
            vastClient,
            response.ads[0],
            response.ads[0].creatives[0]
          );
          const listCustomEvent: string[] = [];
          try {
            const customTracking: any[] = [];
            const extensions: any[] = vastTracker.ad?.extensions || [];
            if (extensions.length > 0) {
              extensions.forEach((element) => {
                const children: any[] = element.children || [];
                if (children.length > 0) {
                  const items = children[0].children || [];
                  customTracking.push(...items);
                }
              });
            }
            customTracking.forEach((item) => {
              const eventName = item.attributes?.event || "";
              const value = item.value || "";
              const name = item.name || "";
              if (
                name === "Tracking" &&
                eventName === "viewable_impression" &&
                value !== ""
              ) {
                listCustomEvent.push(eventName);
                vastTracker.trackingEvents[eventName] = [value];
              }
            });
            // if (isAIActiv) {
            //   const impressionURLTemplates =
            //     response.ads[0]?.impressionURLTemplates ?? [];
            //   impressionURLTemplates.forEach((m: any, index: number) => {
            //     if (m?.url) {
            //       const key = `impression_${index}`;
            //       listCustomEvent.push(key);
            //       vastTracker.trackingEvents[key] = [m?.url];
            //     }
            //   });
            // }
          } catch (error) {
            // error
          }

          let url;
          let urlHLS;
          let urlMP4;
          let urlMP4480;
          let urlMP4720;
          if (response.ads[0].creatives[0].mediaFiles.length > 0) {
            // tslint:disable-next-line:prefer-for-of
            for (
              let i = 0;
              i < response.ads[0].creatives[0].mediaFiles.length;
              i++
            ) {
              if (
                response.ads[0].creatives[0].mediaFiles[i].mimeType ===
                  "application/x-mpegURL" &&
                !urlHLS
              ) {
                urlHLS = response.ads[0].creatives[0].mediaFiles[i].fileURL;
              }
              if (
                response.ads[0].creatives[0].mediaFiles[i].mimeType ===
                "video/mp4"
              ) {
                if (!urlMP4) {
                  urlMP4 = response.ads[0].creatives[0].mediaFiles[i].fileURL;
                }
                if (response.ads[0].creatives[0].mediaFiles[i].height === 480) {
                  urlMP4480 =
                    response.ads[0].creatives[0].mediaFiles[i].fileURL;
                }
                if (response.ads[0].creatives[0].mediaFiles[i].height === 720) {
                  urlMP4720 =
                    response.ads[0].creatives[0].mediaFiles[i].fileURL;
                }
              }
            }
          }
          if (urlMP4480) {
            urlMP4 = urlMP4480;
          } else if (urlMP4720) {
            urlMP4 = urlMP4720;
          }
          let time =
            response.ads[0].creatives[0].skipDelay !== undefined
              ? response.ads[0].creatives[0].skipDelay
              : 0;
          const { skip } = currentLinkAds.current;
          const { timeSkip } = currentLinkAds.current;
          time = time > 0 ? time : skip > 0 ? timeSkip : 0;

          adsVastTracker.current = vastTracker;
          adsListCustomEvent.current = listCustomEvent;
          adsTotalTimeSkip.current = time;

          // setAdsType(!!urlHLS ? 'application/x-mpegURL' : 'video/mp4');
          setAdsTimeSkip(time);

          let type = "application/x-mpegURL";
          if (!!urlHLS && urlHLS.indexOf("gcdn.2mdn.net") < 0) {
            url = urlHLS;
            type = "application/x-mpegURL";
          } else {
            url = urlMP4;
            type = "video/mp4";
          }
          if (url) {
            initVideo(url, type);
            trackinagAds(idAA ?? asUrl, true, "");
          } else {
            getLinkADS();
            trackinagAds(idAA ?? asUrl, false, "");
          }
        } else {
          getLinkADS();
          trackinagAds(idAA ?? asUrl, false, "");
        }
      })
      .catch((error: any) => {
        getLinkADS();
        const message = get(error, "message", "");
        trackinagAds(idAA ?? asUrl, false, message);
      });
  }

  function trackWithValidate(name: string, isParam: boolean = true) {
    if (typeof adsVastTracker.current?.track === "function") {
      adsVastTracker.current.track(name, isParam);
    }
  }

  function adsVastTrackerWithValidate(
    funcName: "complete" | "skip" | "trackImpression"
  ) {
    if (
      adsVastTracker.current &&
      typeof adsVastTracker.current[funcName] === "function"
    ) {
      adsVastTracker.current[funcName]();
    }
  }

  function initVideo(url: string, type: string) {
    setShowPlayer(true);
    onBuffer();
    if (type === "video/mp4") {
      if (player.current) {
        player.current.src = url;
        player.current.load();
      }
    } else {
      if (url !== "" && Hls.isSupported()) {
        console.log("HLS version: ", Hls.version);
        playerHls.current = new Hls({
          debug: false,
          maxBufferLength: 24,
          maxMaxBufferLength: 200,
          maxBufferSize: 20 * 1000 * 1000,

          maxLoadingDelay: 0,

          manifestLoadingTimeOut: 10000, // time out
          manifestLoadingMaxRetry: 3, // retry
          manifestLoadingRetryDelay: 1000,
          manifestLoadingMaxRetryTimeout: 64000,

          levelLoadingTimeOut: 10000,
          levelLoadingMaxRetry: 3,
          levelLoadingRetryDelay: 1000,
          levelLoadingMaxRetryTimeout: 64000,

          nudgeMaxRetry: 6,
        });
        playerHls.current.attachMedia(player.current);
        playerHls.current.on(Hls.Events.MEDIA_ATTACHED, () => {
          playerHls.current.loadSource(url);
          playerHls.current.on(Hls.Events.ERROR, (event: any, data: any) => {
            console.error("Error : ", event, "object :", data);
            if (data.fatal) {
              endADS("error");
            }
          });
        });
      } else {
        getLinkADS();
      }
    }
    setTimeout(() => {
      if (!isunmount.current) {
        if (startRef.current === false) {
          endADS("error");
        }
      }
    }, 15000);
  }
  function endADS(action: string) {
    setShowPlayer(false);
    setStart(false);
    if (action === "end") {
      adsVastTrackerWithValidate("complete");
    } else if (action === "skip") {
      adsVastTrackerWithValidate("skip");
    }
    if (action === "error") {
      setTimeout(() => {
        if (!isunmount.current) {
          end();
        }
      }, 1000);
    } else {
      end();
    }
  }

  function end() {
    if (numberStartAds.current + 1 < numberOfAds) {
      numberStartAds.current += 1;
      getLinkADS();
    } else {
      handleEndAds();
    }
  }

  function trackingADS() {
    if (player.current) {
      const currentTime: number = Math.round(player.current.currentTime);
      const { duration } = player.current;

      if (
        currentTime === Math.round(duration * 0.25) &&
        !firstQuartileTracking.current
      ) {
        trackWithValidate("firstQuartile", true);
        firstQuartileTracking.current = true;
      }

      if (
        currentTime === Math.round(duration * 0.5) &&
        !midpointTracking.current
      ) {
        trackWithValidate("midpoint", true);
        midpointTracking.current = true;
      }

      if (
        currentTime === Math.round(duration * 0.75) &&
        !thirdQuartileTracking.current
      ) {
        trackWithValidate("thirdQuartile", true);
        thirdQuartileTracking.current = true;
      }
    }
  }

  function handleOnPlay() {
    setIsHaveSkip(adsTotalTimeSkip.current > 0);
    const duration = get(player.current, "duration", 0);
    setStart(true);

    if (!startTracking.current) {
      adsVastTrackerWithValidate("trackImpression");

      trackWithValidate("start", true);
      startTracking.current = true;

      if (adsListCustomEvent.current.length > 0) {
        adsListCustomEvent.current.forEach((element) => {
          trackWithValidate(element);
        });
      }
    }

    // if (numberStartAds.current === 1 && type === "preroll") {
    //   SegmentManager.segmentAction("requests_for_first_preroll", {
    //     content_id: idContent,
    //     content_title: titleContent,
    //     content_type: typeContent,
    //     num_request: numberRequestFirst.current,
    //   });
    // }

    SegmentManager.segmentAction("video ad started", {
      session_id: new Date().getTime(),
      content_id: idContent,
      ad_duration: duration,
      ad_link: currentLinkAds.current,
      content_name: titleContent,
      current_page: page,
    });

    const time = new Date().getTime() - timeStartAds;
    if (type === "preroll" && time > 0) {
      SegmentManager.segmentAction("video_startup_preroll_load", {
        content_id: idContent,
        content_title: titleContent,
        content_type: typeContent,
        total_time: time,
      });
    }
  }
  function onBuffer() {
    if (loading.current) {
      loading.current.classList.remove("hide");
    }
  }
  function onEndBuffer() {
    if (loading.current) {
      loading.current.classList.add("hide");
    }
  }
  function handleOnEnded() {
    const duration = get(player, "duration", 0);
    SegmentManager.segmentAction("video ad completed", {
      session_id: new Date().getTime(),
      is_finished: true,
      is_skipped: false,
      played_duration: duration,
      ad_link: currentLinkAds.current,
      ad_duration: duration,
      content_id: idContent,
      content_title: titleContent,
      current_page: page,
    });
    endADS("end");
  }
  function handleOnTimeUpdate() {
    if (player) {
      if (isHaveSkip) {
        let time = adsTotalTimeSkip.current;
        time -= Math.floor(player.current!.currentTime);
        if (time <= 0) {
          time = 0;
        }
        setAdsTimeSkip(time);
      }
      trackingADS();
    }
  }

  function onClick(e: React.MouseEvent) {
    const currentEl = e.currentTarget as HTMLElement;
    if (currentEl) {
      const index = currentEl.getAttribute("data-index");
      if (index) {
        onSelectButton(parseInt(index, 10));
      }
    }
  }

  function onSelectButton(index: number) {
    if (index === 0) {
      if (adsTimeSkipRef.current <= 0 && isHaveSkipRef.current) {
        const playedDuration = get(player, "currentTime", 0);
        const duration = get(player, "duration", 0);
        SegmentManager.segmentAction("skipad button selected", {
          current_page: page,
          content_id: idContent,
          content_name: titleContent,
        });
        SegmentManager.segmentAction("video ad completed", {
          session_id: new Date().getTime(),
          is_finished: false,
          is_skipped: true,
          played_duration: playedDuration,
          ad_link: currentLinkAds.current,
          ad_duration: duration,
          content_id: idContent,
          content_title: titleContent,
          current_page: page,
        });
        endADS("skip");
      }
    }
  }

  return (
    <>
      {showPlayer && (
        <video
          ref={(el) => (player.current = el)}
          autoPlay
          id="playerADS"
          height="100%"
          width="100%"
          preload="none"
          onPlay={handleOnPlay}
          onLoadStart={onBuffer}
          onWaiting={onBuffer}
          onLoadedData={onEndBuffer}
          onCanPlay={onEndBuffer}
          onPlaying={onEndBuffer}
          onCanPlayThrough={onEndBuffer}
          onEnded={handleOnEnded}
          onTimeUpdate={handleOnTimeUpdate}
        >
          <source src="" type="application/x-mpegURL" />
        </video>
      )}
      <div
        id="loading-section"
        ref={(el) => (loading.current = el)}
        className="hide"
        style={{
          position: "absolute",
          left: 0,
          top: 0,
          width: "100%",
          height: "100%",
          zIndex: 1,
        }}
      >
        <div
          className="spinner-wrapper"
          style={{ width: "100%", height: "100%" }}
        >
          <div className="spinner animate-spin" />
        </div>
      </div>
      {isHaveSkip && start ? (
        <div
          className="skip skip--ads"
          style={{
            marginTop: "90px",
            position: "absolute",
            right: "4em",
            bottom: "4em",
          }}
        >
          <button
            data-index={0}
            className={`btn btn--ghost${adsTimeSkip <= 0 ? " focus" : ""}`}
            type="button"
            style={{ padding: "0.5em", minWidth: "10em" }}
            onClick={onClick}
          >
            {adsTimeSkip > 0 ? `Bỏ qua sau ${adsTimeSkip} giây` : "Bỏ qua"}
            {adsTimeSkip === 0 ? (
              <span className="btn__icon" style={{ paddingLeft: "0.5em" }}>
                <svg
                  className={classNames("vie", "vie-play-next-o")}
                  viewBox="0 0 32 32"
                  dangerouslySetInnerHTML={{
                    __html:
                      '<path d="M30.481 0.846c0.839 0 1.519 0.68 1.519 1.519v0 27.27c0 0.839-0.68 1.519-1.519 1.519v0h-1.519c-0.839 0-1.519-0.68-1.519-1.519v0-27.27c0-0.839 0.68-1.519 1.519-1.519v0zM0 3.529c0-2.105 2.331-3.375 4.1-2.234v0l19.225 12.403c1.623 1.047 1.623 3.421-0 4.468v0l-19.224 12.403c-1.769 1.141-4.1-0.129-4.1-2.234v0z"></path>',
                  }}
                />
              </span>
            ) : null}
          </button>
        </div>
      ) : null}
      {numberOfAds > 1 && start && (
        <div
          style={{
            position: "absolute",
            left: "12px",
            bottom: "3vw",
            height: "1.8vw",
            fontSize: "1.389vw",
            textShadow: "0px 0px 4px rgba(0, 0, 0, 0.50)",
            textAlign: "center",
            verticalAlign: "mid",
            color: "white",
          }}
        >
          Quảng cáo {numberStartAds.current + 1}/{numberOfAds}
        </div>
      )}
    </>
  );
};
export default VADSPlayer;
