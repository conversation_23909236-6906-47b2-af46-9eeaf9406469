@use "src/assets/scss/settings/function" as fn;
@use "src/assets/scss/settings/variables" as var;

$animation-collapse: width 0.1s, height 0.1s;

.masthead-ads-container {
  // background-color: black;
  box-sizing: content-box;
  width: fn.percent-unit(100vw);
  height: fn.percent-unit(100vh);
  position: fixed;
  top: fn.percent-unit(0);
  left: fn.percent-unit(0);
  margin-left: fn.percent-unit(0);
  z-index: 99999;
  background-color: #000000;

  &.livetv {
    &.collapse {
      margin-left: fn.percent-unit(var.$sidebar-max-width + 15);
    }
  }

  &.sport {
    &.collapse {
      margin-left: fn.percent-unit(var.$sidebar-max-width - 5);
    }
  }

  &.fade-out {
    // transition: width 0.1s, height 0.1s, transform 0.1s;
    transition: transform 0.1s;
  }

  &.collapse {
    //z-index: 1;
    margin-left: fn.percent-unit(var.$sidebar-max-width);
    width: fn.percent-unit(100%);
    height: fn.percent-unit(200);

    .content {
      margin-top: fn.percent-unit(30);
      width: fn.percent-unit(100%);
      height: fn.percent-unit(170);
    }
  }

  .content {
    width: fn.percent-unit(100%);
    height: fn.percent-unit(100%);
    position: relative;
    margin-top: fn.percent-unit(0);

    // &.fade-out {
    //   transition: $animation-collapse;
    // }

    &.collapse {
      width: fn.percent-unit(1705);
      height: fn.percent-unit(170);
    }

    .node-count-down {
      width: fn.percent-unit(40);
      height: fn.percent-unit(40);
      background: #646464;
      color: #fff;
      font-weight: 700;
      font-size: fn.percent-unit(20);
      position: absolute;
      bottom: fn.percent-unit(60);
      left: fn.percent-unit(60);
      line-height: fn.percent-unit(30);
      border-radius: fn.percent-unit(100%);
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .image-ads-zone {
      width: fn.percent-unit(100%);
      height: fn.percent-unit(100%);

      // &.fade-out {
      //   transition: $animation-collapse;
      // }

      &.collapse {
        margin-right: fn.percent-unit(0);
        width: fn.percent-unit(1705);
        height: fn.percent-unit(170);
      }
    }

    .text-tag {
      bottom: fn.percent-unit(60);
      left: fn.percent-unit(60);
    }

    .video-frame {
      width: fn.percent-unit(1152);
      height: fn.percent-unit(649);
      // background: #05931a;
      top: fn.percent-unit(60);
      right: fn.percent-unit(60);
      position: absolute;

      .time {
        font-size: fn.percent-unit(24);
        color: #fff;
        font-weight: 400;
        line-height: fn.percent-unit(36);
        position: absolute;
        bottom: fn.percent-unit(20);
        right: fn.percent-unit(20);
      }
    }

    .text-tag {
      position: absolute;
      bottom: fn.percent-unit(10);
      left: fn.percent-unit(10);
      color: #fff;
      width: fn.percent-unit(125);
      height: fn.percent-unit(36);
      padding: fn.percent-unit(6);
      background: #00000080;
      font-size: fn.percent-unit(24);
      text-align: center;
      align-content: center;
    }
  }
}
