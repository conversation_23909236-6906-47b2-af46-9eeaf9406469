import platform from "services/platform";
// import xmlConvert from "xml-js";

const macros = {
  ADVERTISING_IDENTIFIER_TYPE: platform.vieONDeviceID,
  IFA: platform.vieONDeviceID,
  CACHEBUSTER: 0,
  WIDTH: platform.screenType === "HD" ? 1280 : 1920,
  HEIGHT: platform.screenType === "HD" ? 720 : 1080,
};

function replaceUrlMacros(url: string, macros: any) {
  let replacedMacrosUrl = url;
  for (const key in macros) {
    let value = macros[key];
    if (key === "CACHEBUSTER") value = Date.now();
    replacedMacrosUrl = replacedMacrosUrl.replace(
      new RegExp(`(?:\\[|%%|\\$\\$)(${key})(?:\\]|%%|\\$\\$)`, "g"),
      value
    );
  }

  return replacedMacrosUrl;
}

// export function parseADS(adXML: string) {
//   const jObj: any = xmlConvert.xml2js(adXML, { compact: true }); // to convert xml text to javascript object

//   const resData = jObj?.VAST?.Ad?.InLine?.Creatives?.Creative?.Linear;
//   const mediaFile = resData?.MediaFiles.MediaFile;

//   let url;
//   let urlHLS;
//   let urlMP4;
//   let urlMP4480;
//   let urlMP4720;

//   const { _cdata: mediaUrl, _attributes: mediaFileAttributes } =
//     mediaFile ?? {};
//   if (mediaFileAttributes.type === "application/x-mpegURL" && !urlHLS) {
//     urlHLS = mediaUrl;
//   }
//   if (mediaFileAttributes.type === "video/mp4") {
//     if (!urlMP4) {
//       urlMP4 = mediaUrl;
//     }
//     if (mediaFileAttributes.height === "480") {
//       urlMP4480 = mediaUrl;
//     }
//     if (mediaFileAttributes.height === "720") {
//       urlMP4720 = mediaUrl;
//     }
//   }

//   if (urlMP4480) {
//     urlMP4 = urlMP4480;
//   } else if (urlMP4720) {
//     urlMP4 = urlMP4720;
//   }

//   let type = "application/x-mpegURL";

//   if (!!urlHLS && urlHLS.indexOf("gcdn.2mdn.net") < 0) {
//     url = urlHLS;
//     type = "application/x-mpegURL";
//   } else {
//     url = urlMP4;
//     type = "video/mp4";
//   }

//   return { type, url };
// }
