import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { ROUTES } from "app/utils/constants";
import { resetMastheadAdsStatus, updateMastheadAdsStatus } from "app/store/actions";
import { RootState } from "app/store/store";
import { MASTHEAD_ADS_DOM_ID } from "./MastheadAdsElement";

const routesDisplay = [
  ROUTES.VOD,
  ROUTES.SPORT_FOOTBALL,
  // ROUTES.LIVE_STREAM,
  ROUTES.LIVE_TV_NEW_UI,
  ROUTES.SCHEDULE
];

const useMastheadAdsLogic = () => {
  const location = useLocation();
  const dispatch = useDispatch();
  const showMastheadAds = useSelector((state: RootState) => state.app.mastheadAds?.status);
  const isKidProfile = useSelector((state: RootState) => state.app.currentProfile?.isKid);

  const [domReady, setDomReady] = React.useState(false);

  const [showAdsAtPage, setShowAdsAtPage] = React.useState<boolean>(false);

  React.useEffect(() => {
    for (const r of routesDisplay) {
      const found = location.pathname.match(new RegExp(r, "g"));
      if (found) {
        setShowAdsAtPage(true);
        break;
      }
    }

    return () => {
      setShowAdsAtPage(false);
    };
  }, [location]);

  React.useEffect(() => {
    if (isKidProfile) {
      dispatch(resetMastheadAdsStatus());
    }
  }, [isKidProfile]);

  React.useEffect(() => {
    if (domReady && showAdsAtPage && ["COLLAPSE", "INVISIBLE", "FULLSCREEN"].includes(showMastheadAds)) {
      const dom = document.getElementById(MASTHEAD_ADS_DOM_ID);
      switch (showMastheadAds) {
        case "COLLAPSE":
        case "FULLSCREEN":
          if (dom) {
            dom.style.transform = `translateY(0px)`;
          }
          break;
        case "INVISIBLE":
          if (dom) {
            dom.style.transform = `translateY(-200px)`;
          }
          break;
        default:
      }
    }
  }, [showMastheadAds, domReady, showAdsAtPage]);

  const toggleVisibleAdsInViewport = React.useCallback(
    (actionInput?: "INVISIBLE" | "COLLAPSE" | null) => {
      if (showMastheadAds !== actionInput && ["COLLAPSE", "INVISIBLE"].includes(showMastheadAds)) {
        const dom = document.getElementById(MASTHEAD_ADS_DOM_ID);

        if (dom) {
          const action = actionInput ?? (showMastheadAds === "COLLAPSE" ? "INVISIBLE" : "COLLAPSE");

          dispatch(
            updateMastheadAdsStatus({
              status: action
            })
          );
        }
      }
    },
    [showMastheadAds]
  );

  return {
    showAdsAtPage,
    toggleVisibleAdsInViewport,
    showMastheadAds,
    domReady,
    setDomReady
  };
};

export default useMastheadAdsLogic;
