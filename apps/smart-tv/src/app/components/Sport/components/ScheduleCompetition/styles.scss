@use "src/assets/scss/settings/function" as fn;
@use "src/assets/scss/settings/palettes" as palettes;

$BOXRADIUS: fn.percent-unit(20);

.sch-comp {
  color: palettes.$white;
  width: fn.percent-unit(700);
  height: fn.percent-unit(700);
  position: relative;
  z-index: 1;

  &.focus {
    &::before {
      display: block;
    }
  }

  &::before {
    content: "";
    display: none;
    position: absolute;
    top: fn.percent-unit(-10);
    left: fn.percent-unit(-10);
    width: calc(100% + 8px);
    height: calc(100% + 8px);
    border: fn.percent-unit(6) solid palettes.$white;
    border-radius: fn.percent-unit(25);
  }

  .blur {
    width: fn.percent-unit(100%);
    height: fn.percent-unit(67);
    position: absolute;
    bottom: fn.percent-unit(0);
    left: fn.percent-unit(0);
    background: linear-gradient(
      to bottom,
      rgba(51, 51, 51, 0) 0%,
      rgba(51, 51, 51, 1) 100%
    );
    z-index: 1;
    border-bottom-left-radius: $BOXRADIUS;
    border-bottom-right-radius: $BOXRADIUS;
  }

  &__header {
    background-size: cover !important;
    border-top-left-radius: $BOXRADIUS;
    border-top-right-radius: $BOXRADIUS;

    &__title {
      font-weight: 500;
      font-size: fn.percent-unit(32);
      line-height: fn.percent-unit(37);
      text-align: center;
      padding: fn.percent-unit(20) 0;
    }
  }

  &__body {
    display: block;
    overflow: hidden;
    border-bottom-left-radius: $BOXRADIUS;
    border-bottom-right-radius: $BOXRADIUS;
    height: fn.percent-unit(623);
    background: palettes.$gray-33;
    position: relative;
    transform: translateY(0px);

    .row-focus-box {
      background: #ffffff3d;
      width: fn.percent-unit(100%);
      height: fn.percent-unit(112);
      position: absolute;
    }

    &__day-title {
      padding: fn.percent-unit(20) 0;
      padding-left: fn.percent-unit(34);

      span {
        font-weight: 500;
        font-size: fn.percent-unit(28);
        line-height: fn.percent-unit(40);
      }
    }

    &__record {
      display: flex;
      flex-direction: row;
      height: fn.percent-unit(112);
      padding-left: fn.percent-unit(35);

      //padding: 0 fn.percent-unit(34);
      //padding-top: fn.percent-unit(25);
      &.focus {
        background: rgba(255, 255, 255, 0.239);
      }

      .vie {
        color: #fff;
        fill: currentColor;
        width: fn.percent-unit(54);
        height: fn.percent-unit(20);
      }

      div {
        //flex: 1;
        text-align: center;
      }

      .main-info {
        margin-left: fn.percent-unit(20);
        padding: fn.percent-unit(25) 0;
        display: flex;
        width: fn.percent-unit(565);
        height: inherit;
        justify-content: flex-start;
        border-bottom: fn.percent-unit(1) solid rgba(255, 255, 255, 0.5);

        &.focus {
          border-bottom: none;
        }

        .home-team,
        .away-team {
          display: flex;
          align-items: center;
          margin: 0 fn.percent-unit(20);
          width: fn.percent-unit(120);

          img {
            width: fn.percent-unit(60);
            height: fn.percent-unit(60);
            min-width: fn.percent-unit(60);
            min-height: fn.percent-unit(60);
          }

          .tla {
            margin: 0 fn.percent-unit(20);
            font-size: fn.percent-unit(24);
            line-height: fn.percent-unit(36);
            font-weight: 700;
            min-width: fn.percent-unit(70);
            text-align: left;
          }
        }

        .match-info {
          align-self: center;
          margin: 0 fn.percent-unit(20);
          padding-left: fn.percent-unit(20);

          div {
            &.live {
              color: #3ac882;
            }

            border: 1px solid palettes.$gray-9b;
            width: fn.percent-unit(114);
            height: fn.percent-unit(40);
            background: linear-gradient(
              to bottom,
              rgba(17, 17, 17, 1) 0%,
              rgba(17, 17, 17, 0) 100%
            );
            color: palettes.$white;
            font-weight: 500;
            font-size: fn.percent-unit(28);
            line-height: fn.percent-unit(40);
          }

          img {
            margin-top: fn.percent-unit(5);
            width: fn.percent-unit(40);
            height: fn.percent-unit(25);
          }
        }

        .channel {
          margin: 0 fn.percent-unit(20);
          align-self: center;
        }
      }

      .tag {
        width: fn.percent-unit(54);
        align-self: center;

        img {
          width: fn.percent-unit(54);
          height: fn.percent-unit(20);
        }
      }
    }
  }
}
