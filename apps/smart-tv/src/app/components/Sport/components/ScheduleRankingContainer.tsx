import React, { useCallback } from "react";
import { useHistory } from "react-router-dom";
import KeepAlive, { KeepAliveData } from "app/utils/KeepAlive";
import { Enter<PERSON><PERSON><PERSON><PERSON><PERSON>, FocusContext, useFocusable } from "core/KeyHandle";
import { Tournament } from "app/models/index";
import { SportScheduleCompetition } from "app/models/Sport/SportScheduleCompetition";
import { VideoType } from "types/page";
import { ROUTES } from "app/utils/constants";
import ScheduleCompetition from "./ScheduleCompetition";
import RankingTable from "./RankingTable";
import { CompetitionData, MATCH_STATUS } from "./ScheduleCompetition/types";
import { FOCUS_KEY } from "../index";

interface Props {
  data: Tournament | null;
  selectedTournament: Tournament | null;
  keepAliveData?: KeepAliveData;
  onPopout: VoidFunction;
}

export enum FocusSection {
  Schedule = 0,
  Ranking = 1
}

function ScheduleRankingContainer({ selectedTournament, data, keepAliveData, onPopout }: Props) {
  const history = useHistory();
  const { ref, focusKey } = useFocusable({
    focusKey: FOCUS_KEY.ScheduleRankingSection.ScheduleRankingContainer.ScheduleRankingContainer
  });

  // const trackingOnCardSelect = useCallback(
  //   (item, indexX, indexY, ribbonName, ribbonId, playNow) => {
  //     const contentId = get(item, "id", "");
  //     const contentName = get(item, "name", "") || get(item, "title", "");
  //     const contentType = get(VIDEO_TYPE, get(item, "type", -1), "");
  //     const contentOrder = indexX;
  //     const ribbonOrder = indexY;
  //     const genre = get(item, "tags_genre_txt", "") || "";

  //     const search = get(locationRef, ["current", "search"], "");
  //     const urlParams = new URLSearchParams(search);
  //     const submenu_id = urlParams.get("submenu_id") || "";
  //     const submenu_name = urlParams.get("submenu_name") || "";
  //     const submenu_order = urlParams.get("submenu_order") || "";
  //     const menu_id_param = urlParams.get("menu_id") || "";
  //     const menu_name_param = urlParams.get("menu_name") || "";
  //     const menu_order_param = urlParams.get("menu_order") || "";
  //     const menu_id = menu_id_param || currentPageRef.current.id;
  //     const menu_name = menu_name_param || currentPageRef.current.name;
  //     const menu_order = menu_order_param || currentPageRef.current.order;
  //     const current_page = submenu_name || currentPageRef.current.name;
  //     const data: { [k: string]: any } = {
  //       current_page,
  //       menu_id,
  //       menu_name: menu_id && menu_name,
  //       menu_order,
  //       submenu_id,
  //       submenu_name,
  //       submenu_order,
  //       content_id: contentId,
  //       content_name: contentName,
  //       content_type: contentType,
  //       content_order: contentOrder,
  //       ribbon_name: ribbonName,
  //       ribbon_id: ribbonId,
  //       ribbon_order: ribbonOrder,
  //       genre,
  //       user_type: makeTypeOfUser(isAuthen, userType),
  //       content_play_type: makeTypeOfContent(
  //         get(item, "is_premium", -10),
  //         get(item, "is_vip", -1),
  //         get(item, "trial_duration", 0) > 0,
  //         get(item, "permission", 0)
  //       ),
  //       content_selected_button: get(item, "tvod.benefit_type", ""),
  //     };
  //     if (playNow) {
  //       data.play_now = true;
  //     }
  //     SegmentManager.segmentAction("content selected", data);
  //   },
  //   [currentPageRef, isAuthen, userType]
  // );

  const onCompetitionSelect = useCallback<
    EnterPressHandler<{
      item: CompetitionData;
      focusKey: string;
      index: number;
    }>
  >(
    ({ item, focusKey }) => {
      if (item) {
        const { contentType, contentId, contentUrl, matchStatus } = item.record as SportScheduleCompetition;
        if (contentId && [MATCH_STATUS.FINISHED, MATCH_STATUS.LIVE].includes(matchStatus as any)) {
          const keepAliveData: KeepAliveData = {
            path: window.location.hash,
            focus: {
              x: 1,
              y: selectedTournament?.index || 0
            },
            focusKey,
            extra: {
              tournament: selectedTournament?.code || ""
            }
          };
          KeepAlive.saveData(keepAliveData);

          switch (contentType) {
            case VideoType.LIVETV: {
              history.push(`${ROUTES.LIVE_TV}/${contentId}`);
              break;
            }
            case VideoType.EPG: {
              const searchParams = new URLSearchParams();
              if (contentUrl) {
                searchParams.append("epg", contentUrl);
                history.push({
                  pathname: `${ROUTES.LIVE_TV}/${contentId}`,
                  search: searchParams.toString()
                });
              }
              break;
            }
            default:
              history.push(`${ROUTES.VIDEO_INTRO}/${contentId}`);
              break;
          }
        }
      }
    },
    [history, selectedTournament?.code, selectedTournament?.index]
  );

  return (
    <FocusContext.Provider value={focusKey}>
      <div ref={ref} className="sport-schedule__ranking__container">
        {data && (
          <>
            <ScheduleCompetition
              keepAliveData={keepAliveData}
              competitionCode={data?.code || ""}
              backgroundHeader={data?.background}
              onCompetitionSelect={onCompetitionSelect}
              onPopout={onPopout}
            />
            <RankingTable competitionCode={data?.code || ""} backgroundHeader={data?.background} onPopout={onPopout} />
          </>
        )}
      </div>
    </FocusContext.Provider>
  );
}

export default ScheduleRankingContainer;
