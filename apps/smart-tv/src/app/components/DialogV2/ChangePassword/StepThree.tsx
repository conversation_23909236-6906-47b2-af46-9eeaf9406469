import React, { useCallback, useEffect } from "react";
import classNames from "classnames";
import useStateRef from "hooks/useStateRef";
import { confirmOtpResetPassword } from "services/endpoint";
import Icon from "app/components/Common/Icon";
import Input from "app/components/Common/Input/Input";
import NumberQWERTYKeyboard from "app/components/Common/KeyBoard/NumberQWERTYKeyboard";
import Button from "app/containers/Error/components/Button";
import {
  EnterPressHandler,
  VieOnNavigation,
  VieOnNavigationDialog,
} from "core/KeyHandle";
import { KeepAliveData } from "app/utils/KeepAlive";

interface Props {
  onOK: VoidFunction | undefined;
  onClose: VoidFunction;
  keepAliveData?: KeepAliveData;
  prevPassword: string;
  onBack: VoidFunction;
  prevOtp: string;
  prevSessionId: string;
}

const MIN_PASSWORD_LENGTH = 6;
const MAX_PASSWORD_LENGTH = 20;

const StepThree: React.FC<Props> = ({
  keepAliveData,
  onOK,
  onClose,
  prevPassword,
  onBack,
  prevOtp,
  prevSessionId,
}) => {
  const [capslock, setCapslock, capslockRef] = useStateRef(false);
  const [otp, setOtp] = useStateRef(prevOtp);
  const [session, setSession] = useStateRef(prevSessionId);
  const [error, setError, errorRef] = useStateRef("");
  const [currentPassword, setCurrentPassword] = useStateRef(prevPassword);
  const [password, setPassword, passwordRef] = useStateRef("");
  const [passwordVisible, setPasswordVisible, passwordVisibleRef] =
    useStateRef(false);

  useEffect(() => {
    if (keepAliveData && keepAliveData.extra?.step === 3) {
      setCapslock(keepAliveData.extra.capslock);
      setError(keepAliveData.extra.error);
      setCurrentPassword(keepAliveData.extra.currentPassword);
      setSession(keepAliveData.extra.session);
      setOtp(keepAliveData.extra.otp);
      setPassword(keepAliveData.extra.password);
      setPasswordVisible(keepAliveData.extra.passwordVisible);
    }
  }, [keepAliveData]);

  const onKeyboardPopout = (direction: string) => {
    if (direction === "down") {
      if (
        passwordRef.current.length >= MIN_PASSWORD_LENGTH &&
        passwordRef.current.length <= MAX_PASSWORD_LENGTH
      ) {
        VieOnNavigationDialog.setFocus("VN:STEP2_CONTINUE");
      } else {
        VieOnNavigationDialog.setFocus("VN:STEP2_BACK");
      }
    }
  };
  const onKeyboardEnter = (currentKey: string) => {
    const currentKeyLower = currentKey.toLowerCase();
    switch (currentKeyLower) {
      case "capslock": {
        setCapslock(!capslockRef.current);
        break;
      }
      case "clear": {
        setPassword("");
        break;
      }
      case "backspace": {
        setPassword(passwordRef.current.slice(0, -1));
        break;
      }
      default: {
        if (passwordRef.current.length === MAX_PASSWORD_LENGTH) return;
        setPassword(passwordRef.current + currentKey);
        break;
      }
    }
  };
  const onButtonEnter = useCallback<
    EnterPressHandler<{
      focusKey?: string;
      type?: string | undefined;
    }>
  >(({ focusKey, type }) => {
    switch (type) {
      case "BACK": {
        onBack();
        break;
      }
      case "CONTINUE": {
        setError("");
        confirmOtpResetPassword(
          otp,
          session,
          passwordRef.current,
          currentPassword
        )
          .then((res) => {
            onClose();
            onOK?.();
            VieOnNavigation.unblock();
          })
          .catch((error) => {
            setError(error.message);
          });
        break;
      }
      case "SHOW_PASSWORD": {
        setPasswordVisible(!passwordVisibleRef.current);
        break;
      }
      default:
        break;
    }
  }, []);

  return (
    <div className="block block--login">
      <h2 className="block__title">Đổi mật khẩu</h2>
      <div className="grid">
        <div className="col col-6 panel-left triangle-right-2">
          <h3 className="block__title">Nhập mật khẩu mới</h3>
          <NumberQWERTYKeyboard
            loop="all"
            capslock={capslock}
            onPopout={onKeyboardPopout}
            onEnter={onKeyboardEnter}
            onReturn={onBack}
          />
          <div className="button-group">
            <Button
              onEnter={onButtonEnter}
              onReturn={onBack}
              className="button"
              focusKey="VN:STEP2_BACK"
              type="BACK"
            >
              Quay lại
            </Button>
            <Button
              onEnter={onButtonEnter}
              onReturn={onBack}
              disabled={password.length < MIN_PASSWORD_LENGTH}
              className="button"
              focusKey="VN:STEP2_CONTINUE"
              type="CONTINUE"
            >
              Cập nhật
            </Button>
          </div>
        </div>
        <div className="col col-6 panel-right">
          <div className="muted">
            <span className="icon">
              <Icon name="vie-key-skeleton-o" />
            </span>
            <p
              className={classNames("hint", {
                censored: !passwordVisible,
              })}
            >
              {currentPassword}
            </p>
          </div>

          <div className="muted">
            <span className="icon">
              <Icon name="vie-key-skeleton-o" />
            </span>
            <Input
              className={classNames("input--custom", {
                password: !passwordVisible,
                warning: capslock && error === "",
              })}
              placeholder="Mật khẩu mới (6-20 ký tự)"
              value={password}
            />
          </div>
          <p
            className={classNames("error", {
              hide: error === "",
            })}
          >
            {error}
          </p>
          <p
            className={classNames("warning", {
              hide: !capslock || error !== "",
            })}
          >
            Bạn đang mở CAPSLOCK
          </p>
        </div>
        <div className="col col-12 panel-bottom">
          <div className="button-group">
            <Button
              onEnter={onButtonEnter}
              onReturn={onBack}
              className="button small"
              focusKey="VN:STEP2_SHOW_PASSWORD"
              type="SHOW_PASSWORD"
            >
              {passwordVisible ? "Ẩn mật khẩu" : "Hiện mật khẩu"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
export default StepThree;
