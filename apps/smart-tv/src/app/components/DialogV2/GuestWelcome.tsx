import React from "react";
import { CSSTransition } from "react-transition-group";
import Button from "app/components/Button";
import platform from "services/platform";
import useStateRef from "hooks/useStateRef";
import useDialog from "./useDialog";
import CONFIG from "./config";

export interface Props {
  onOk?: () => void;
  onCancel?: () => void;
  visible: boolean;
  close: () => void;
  afterClose: () => void;
}

const Dialog: React.FC<Props> = ({
  close,
  onOk,
  onCancel,
  visible,
  afterClose,
}) => {
  const [focusIndex, setFocusIndex, focusIndexRef] = useStateRef<number>(0);
  const keyHandler: KeyHandler = (e: KeyboardEvent) => {
    const { keys } = platform;
    switch (e.keyCode) {
      case keys.left:
        setFocusIndex(0);
        break;
      case keys.right:
        setFocusIndex(1);
        break;
      case keys.enter:
        if (focusIndexRef.current === 0) {
          close();
          setHandler(() => {
            return onOk;
          });
        } else {
          close();
          setHandler(() => {
            return onCancel;
          });
        }
        break;
      default:
        break;
    }
  };
  const { onClickHandler, afterCloseHandler, setHandler } = useDialog(
    keyHandler,
    close,
    afterClose
  );

  return (
    <CSSTransition
      in={visible}
      appear
      timeout={100}
      classNames="alert"
      onExited={afterCloseHandler}
      unmountOnExit
    >
      <div
        className="dialog-v2 dialog-v2--guest-welcome"
        style={{ backgroundImage: `url(${CONFIG.guestWelcome.image})` }}
      >
        <div className="dialog-v2__inner">
          <div
            className="dialog-v2__title"
            dangerouslySetInnerHTML={{
              __html: CONFIG.guestWelcome.title,
            }}
          />
          <div className="dialog-v2__button-wrapper dialog-v2__button-wrapper--group-two-button">
            <Button
              focus={focusIndex === 0}
              onClick={onClickHandler.bind(null, onOk)}
            >
              {CONFIG.guestWelcome.okText}
            </Button>
            <Button
              focus={focusIndex === 1}
              onClick={onClickHandler.bind(null, onCancel)}
            >
              {CONFIG.guestWelcome.cancelText}
            </Button>
          </div>
        </div>
      </div>
    </CSSTransition>
  );
};

export default Dialog;
