@use "src/assets/scss/settings/function" as fn;

.global-stop-country-service {
  width: 100vw;
  height: fn.percent-unit(1080);
  color: white;
  position: relative;
  .bg-screen {
    z-index: 0;
    position: absolute;
    width: 100%;
  }
  .container {
    z-index: 1;
    position: absolute;
    top: fn.percent-unit(375);
    left: fn.percent-unit(64);
    width: fn.percent-unit(980);
    display: flex;
    color: white;
    flex-direction: column;
    .title {
      margin-bottom: fn.percent-unit(40);
      font-size: fn.percent-unit(48);
      font-weight: 500;
      font-weight: fn.percent-unit(56);
    }
    .row {
      font-size: fn.percent-unit(28);
      font-weight: 400;
      font-weight: fn.percent-unit(54);
      img {
        width: fn.percent-unit(204);
        height: fn.percent-unit(204);
      }
    }
    .descript {
      font-size: fn.percent-unit(24);
      font-weight: 400;
      line-height: fn.percent-unit(36);
    }
    .line {
      margin: fn.percent-unit(24) fn.percent-unit(0);
      width: fn.percent-unit(100%);
      border: fn.percent-unit(1) solid #cccccc;
    }
  }
}
