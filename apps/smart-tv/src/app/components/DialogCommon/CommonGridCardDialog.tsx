import React from "react";
import classNames from "classnames";
import { CSSTransition } from "react-transition-group";
import useStateRef from "hooks/useStateRef";
import { FocusContext, useFocusable } from "core/KeyHandle";
import { DataGridCardDialog, GridCard } from "./types";
import { initDataGridCardDialog } from "./transform";
import GridList from "./components/GridList";
import ButtonActionPanel from "./components/ButtonActionPanel";
import TipBox from "../Common/Portal/TipBox";

type ActionLayoutType = "above" | "below";

export interface Props {
  keyName?: string;
  layout: "single" | "multiple";
  actionPanelLayout?: ActionLayoutType;
  onCardSelected: (item: any | undefined, index: number | undefined) => void;
  onBack?: () => void;
  visible: boolean;
  close: () => void;
  afterClose: () => void;
  dataDialog?: DataGridCardDialog;
  showTipBoxGoback?: boolean;
  title?: JSX.Element | string | null;
  description?: JSX.Element | string | null;
  onRemove?: () => void;
  afterRender?: (...arg: any[]) => void;
  renderTags?: (item: any) => React.ReactElement;
  disabledOnback?: boolean;
  defaultFocusIndex?: number;
}

const KEY_HANDLE_GRID_LIST_UP: string = "KEY_HANDLE_GRID_LIST_UP";
const KEY_HANDLE_GRID_LIST_DOWN: string = "KEY_HANDLE_GRID_LIST_DOWN";
const KEY_HANDLE_ACTION: string = "KEY_HANDLE_ACTION";

const CommonGridCardDialog = React.memo(
  ({
    keyName = "",
    onBack: onBackProp,
    visible,
    close,
    layout = "single",
    onCardSelected: onCardSelectedProp,
    afterClose,
    dataDialog: dataDialogInput = initDataGridCardDialog,
    showTipBoxGoback = true,
    actionPanelLayout = "below",
    title = null,
    description = null,
    onRemove = () => {},
    afterRender = () => {},
    renderTags,
    disabledOnback = false,
  }: Props): JSX.Element => {
    const [dataDialog, setDataDialog] = useStateRef<DataGridCardDialog>(
      initDataGridCardDialog
    );
    React.useEffect(() => {
      setDataDialog(dataDialogInput ?? initDataGridCardDialog);
    }, [dataDialogInput]);

    const isMultiple = layout === "multiple";
    const lengthItem = isMultiple ? 6 : 12;
    const isActionAbove = actionPanelLayout === "above";

    const [buttonsAction, buttonsEventClick] = React.useMemo(() => {
      const { actions = [] } = dataDialog;

      const buttonsAction =
        actions?.map((m) => ({
          key: m.key,
          title: m.title,
        })) ?? [];
      const buttonsEventClick = actions?.map((m) => m.onClick) ?? [];

      return [buttonsAction, buttonsEventClick];
    }, [dataDialog]);

    const gridsData: GridCard[] = React.useMemo(() => {
      const grids = [];
      const girdKeys = [];
      if (dataDialog.gridUp) {
        grids.push({
          ...dataDialog.gridUp,
          key: KEY_HANDLE_GRID_LIST_UP,
        });
        girdKeys.push(KEY_HANDLE_GRID_LIST_UP);
      }
      if (isMultiple && dataDialog.gridDown) {
        grids.push({
          ...dataDialog.gridDown,
          key: KEY_HANDLE_GRID_LIST_DOWN,
        });
        girdKeys.push(KEY_HANDLE_GRID_LIST_DOWN);
      }

      if (isActionAbove) {
        girdKeys.unshift(KEY_HANDLE_ACTION);
      } else {
        girdKeys.push(KEY_HANDLE_ACTION);
      }
      return grids;
    }, [dataDialog]);

    const { focusKey, setFocus, focusSelf, ref } = useFocusable({
      focusKey: keyName,
    });

    React.useEffect(() => {
      if (visible && gridsData.length > 0) {
        setFocus(KEY_HANDLE_ACTION);
      }
    }, [visible, gridsData]);

    const onBack = () => {
      if (!disabledOnback) {
        if (onBackProp) {
          onBackProp();
        }
        close();
      }
    };

    const onCardSelected = (
      item: any | undefined,
      index: number | undefined
    ) => {
      afterRender();
      onCardSelectedProp(item, index);
      close();
    };

    const onCardBack = () => {
      setFocus(KEY_HANDLE_ACTION);
    };

    const onActionEnter = (index: number) => {
      afterRender();
      const action = buttonsEventClick[index];
      if (action) {
        action();
      }
      close();
    };

    const renderGridCard = React.useCallback(() => {
      const renderActionPanel = () => {
        return (
          <ButtonActionPanel
            layout={actionPanelLayout}
            focusKey={KEY_HANDLE_ACTION}
            onBack={onBack}
            onClick={onActionEnter}
            actions={buttonsAction}
          />
        );
      };
      return (
        <>
          {isActionAbove && renderActionPanel()}
          {gridsData.map((grid) => {
            const key = grid.key ?? "";
            return (
              <div
                className="space dialog-grid-card__basic__grid"
                key={`dialog-grid-card-${key}`}
              >
                {grid?.title && <div className="title">{grid?.title}</div>}
                <section className="sect">
                  <div className="focus-box" />
                  <GridList
                    keyName={key}
                    items={grid?.items?.slice(0, lengthItem) ?? []}
                    onSelect={onCardSelected}
                    renderTags={renderTags}
                    ribbonName={grid?.title?.toString()}
                    onCardBack={onCardBack}
                  />
                </section>
              </div>
            );
          })}
          {!isActionAbove && renderActionPanel()}
        </>
      );
    }, [isActionAbove, buttonsAction, gridsData, actionPanelLayout]);

    function exitDialog() {
      onRemove();
      afterClose();
    }

    return (
      <CSSTransition
        in={visible}
        appear
        timeout={200}
        classNames="alert"
        onExited={exitDialog}
        unmountOnExit
      >
        {visible ? (
          <FocusContext.Provider value={focusKey}>
            <div
              className={classNames("dialog-grid-card", keyName)}
              style={{ backgroundImage: `url(${dataDialog.bgScreen})` }}
              ref={ref}
            >
              <div className="dialog-grid-card__basic popup__inner">
                <div className="space dialog-grid-card__basic__header">
                  {title && <div className="title">{title}</div>}
                  {!isMultiple && description && (
                    <div className="description">{description}</div>
                  )}
                </div>
                {renderGridCard()}
              </div>
              {showTipBoxGoback && <TipBox zIndex={999} tip="để quay lại" />}
            </div>
          </FocusContext.Provider>
        ) : (
          <></>
        )}
      </CSSTransition>
    );
  }
);

export default CommonGridCardDialog;
