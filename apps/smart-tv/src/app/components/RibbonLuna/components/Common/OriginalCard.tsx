import React, {
  ReactNode,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import classNames from "classnames";
import { animate, useMotionTemplate, useMotionValue } from "framer-motion";
import { TYPE_CLASS, VideoType } from "types/page";
import CommonTags, {
  LiveTag,
  PremiereTag,
} from "app/components/Common/CommonTagsLuna";
import defaultVideoImg from "assets/images/video-default.png";
import { EnterPressHandler, useFocusable } from "core/KeyHandle";
import MotionFocusBox from "app/components/Motions/MotionFocusBox";
import {
  CARD_DELAY_EXPAND_TIME,
  CARD_MIN_EXPAND_TIME,
} from "app/utils/constants";
import MotionDiv from "app/components/Motions/Motion";
import { convertPxToVW } from "app/utils/Dom";
import { TYPE_OF_CONTENT } from "types/endpoint";
import Tag, { TagSize, TagType } from "app/components/Common/Tag/Tag";
import TvodTag from "app/components/Common/TvodTagLuna";
import CardThumbnail from "../../Shared/CardThumbnail";
import useThumbPreviewPlayer from "../../Shared/useThumbPreviewPlayer";
import InfoBox from "../../Shared/Infobox";
import { fetchTips, makeTipData } from "../../Shared/makeInfoBoxOrWatchMoreBox";
import { makeInfoFn, Key } from "../../Shared/makeInfo";
import { RibbonPropsV3 } from "../../Shared/types";
import { RibbonDataItemModel } from "app/models/RibbonData/RibbonDataItem";

const INFO_FIELD: Key[] = [
  "releaseYear",
  "duration",
  "isVip",
  "isPremiumDisplay",
  "subtitleAudio",
  "ranking",
  "description",
  "award",
  "country",
  "season",
  "age",
  "type",
  "title",
  "liveStreamProgrammeTime",
  "isComingSoon",
  "startTime",
  "isLive",
  "tags",
  "isPremiere",
  "tvodEndedAt",
  "tvodPrice",
  "tvodBenefitType",
  "typeOfContent",
  "preOrderEndedAt",
  "preOrderStartedAt",
  "preOrderPrice",
  "rentedTvodBenefitType",
  "hasPvod",
];

const makeInfo = (item: any) => {
  return makeInfoFn(item, INFO_FIELD);
};

const ORIGINAL_WIDTH = convertPxToVW(348, "FullHD");
const ORIGINAL_WIDTH_EXPANDED = convertPxToVW(348 * 3, "FullHD");

const OriginalCard: React.FC<RibbonPropsV3> = React.memo(function OriginalCard({
  focusKey,
  cardIndex,
  type,
  item,
  isRibbonHover,
  onCardFocus,
  onCardEnter,
  onCardReturn,
  onArrowPress
}) {
  const [onAnimationComplete, onAnimationStop, onEnterItem] =
    useThumbPreviewPlayer();

  const onCardEnterHandler = useCallback<
    EnterPressHandler<{
      item: RibbonDataItemModel | undefined;
      index: number | undefined;
      focusKey: string;
    }>
  >(
    ({ focusKey, index, item }) => {
      onEnterItem();
      onCardEnter?.({ focusKey, index, item });
    },
    [onCardEnter, onEnterItem]
  );

  const [expanded, setExpanded] = useState<boolean>(false);
  const [extraData, setExtraData] = useState<any>({});
  const width = useMotionValue(ORIGINAL_WIDTH);
  const widthValue = useMotionTemplate`${width}vw`;

  const focusTimer = useRef<TimerHandle>(null);
  const revertCardTimer = useRef<TimerHandle>(null);
  const infoBoxRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const keepDirectionRef = useRef<string>("");

  // TODO: Revert Animation will trigger after trailer play complete

  const revertAnimation: any = useCallback(() => {
    const imageEl = imageRef.current;
    if (imageEl) imageEl.style.visibility = "visible";

    onAnimationStop();

    animate(width, ORIGINAL_WIDTH, {
      onPlay: () => {
        setExpanded(false);
      },
      onComplete: () => {},
    });
  }, [onAnimationStop, width]);

  const { ref, focused, navigateByDirection } = useFocusable({
    focusKey,
    onFocus: onCardFocus,
    onEnterPress: onCardEnterHandler,
    onReturnPress: onCardReturn,
    enterOnClick: true,
    focusImplicitOnHover: true,
    onArrowPress: (direction) => {
      onArrowPress?.(direction);
      if (expanded) {
        const imageEl = imageRef.current;
        if (imageEl) imageEl.style.visibility = "visible";
        onAnimationStop();

        animate(width, ORIGINAL_WIDTH, {
          onPlay: () => {
            setExpanded(false);
          },
          onComplete: () => {
            if (keepDirectionRef.current) {
              navigateByDirection(keepDirectionRef.current, {});
              keepDirectionRef.current = "";
            }
          },
        });
        keepDirectionRef.current = direction;
        return false;
      }
      return true;
    },
    onBlur: revertAnimation,
    extraProps: {
      item,
      index: cardIndex,
      focusKey,
    },
  });

  useEffect(() => {
    if (focused && !isRibbonHover) {
      focusTimer.current = setTimeout(async () => {
        animate(width, ORIGINAL_WIDTH_EXPANDED, {
          onPlay: () => {
            setExpanded(true);
          },
          onComplete: () => {
            const linkPlay = item?.linkPlay.hlsLinkPlay;
            if (linkPlay) {
              onAnimationComplete({
                id: item?.id || "",
                linkPlay,
                infoBoxEl: ref.current,
                revertAnimation,
                zIndex: "1",
                extendClass: ["object-fit-cover"],
                imageEl: imageRef.current || undefined,
              });
            } else {
              revertCardTimer.current = setTimeout(() => {
                revertAnimation();
              }, CARD_MIN_EXPAND_TIME);
            }
          },
        });
      }, CARD_DELAY_EXPAND_TIME);
    } else {
      if (focusTimer.current) {
        clearTimeout(focusTimer.current);
        focusTimer.current = null;
      }

      if (revertCardTimer.current) {
        clearTimeout(revertCardTimer.current);
        revertCardTimer.current = null;
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [focused, isRibbonHover]);

  useEffect(() => {
    if (!item || !item.id) return;
    // Fetch metadata
    const videoType = item?.type;
    if (videoType !== VideoType.EPG && videoType !== -1) {
      const params: {
        eps_id?: string;
        eps_slug?: string;
        type?: string;
      } = {};
      if (videoType === VideoType.LIVETV) {
        params.eps_slug = item?.slug;
        params.type = "live_tv";
      } else if (videoType === VideoType.LIVESTREAM) {
        params.type = "live_event";
      } else {
        params.eps_id = item?.id;
        params.type = "vod";
      }

      fetchTips({
        id: item.id,
        params,
        callback: ({ status, data: tipData }) => {
          if (status === "success" && tipData) {
            // fetchTips success
            setExtraData(makeTipData(tipData));
          }
        },
      });
    }
    // eslint-disable-next-line consistent-return
    return () => {
      // Clean up job
      if (focusTimer.current) {
        clearTimeout(focusTimer.current);
        focusTimer.current = null;
      }

      if (revertCardTimer.current) {
        clearTimeout(revertCardTimer.current);
        revertCardTimer.current = null;
      }
    };
  }, [item]);

  const infoBoxProps = React.useMemo(() => {
    return item
      ? makeInfo({
          ...item,
          ...extraData,
          metadata: {
            ...item.metadata,
            award: extraData?.metadata?.award || [],
          },
        })
      : {};
  }, [extraData, item]);

  const makeTags = useCallback(
    ({
      typeOfContent,
      isSimulcast,
      tvodEndedAt,
      tvodBenefitType,
      tvodPrice,
      isVip,
      typeOfShowedTvodTag,
      isPremiumDisplay,
      preOrderStartedAt,
      preOrderEndedAt,
      preOrderPrice,
      rentedTvodBenefitType,
      hasPvod,
      type,
    }: {
      typeOfContent: number;
      isSimulcast: boolean;
      tvodEndedAt: number;
      tvodBenefitType: number;
      tvodPrice: number;
      isVip: any;
      typeOfShowedTvodTag: "price" | "time";
      isPremiumDisplay: string;
      preOrderStartedAt: number;
      preOrderEndedAt: number;
      preOrderPrice: number;
      rentedTvodBenefitType: number;
      hasPvod: boolean;
      type: VideoType;
    }) => {
      if (typeOfContent === TYPE_OF_CONTENT.TVOD) {
        return [
          <TvodTag
            key={TagType.TvodPrice}
            item={{
              isPremium: typeOfContent,
              isSimulcast,
              tvod: {
                benefit_ended_at: tvodEndedAt,
                benefit_type: tvodBenefitType,
                price: tvodPrice,
                tvod_benefit_type: rentedTvodBenefitType,
                pre_order: {
                  pre_order_started_at: preOrderStartedAt,
                  pre_order_ended_at: preOrderEndedAt,
                  pre_order_price: preOrderPrice,
                },
              },
            }}
            isPriceShown={typeOfShowedTvodTag === "price"}
            isTimeRemainingShown={typeOfShowedTvodTag === "time"}
            isForRibbon={false}
            isUpperCase={false}
            size={TagSize.Large}
          />,
        ];
      } else if (typeOfShowedTvodTag === "price") {
        const tags: ReactNode[] = [];
        let liveTag;
        let premiereTag;
        if (
          type === VideoType.EPISODE &&
          (typeOfContent === TYPE_OF_CONTENT.PVOD ||
            typeOfContent === TYPE_OF_CONTENT.PVOD_NOT_HAVE_SVOD)
        ) {
          tags.push(
            <Tag
              type={TagType.EarlyAccessYellow}
              key={TagType.EarlyAccessYellow}
              size={TagSize.Large}
            />
          );
        } else if (isVip) {
          tags.push(
            <Tag
              type={TagType.Vip}
              key={TagType.Vip}
              premiumDisplay={isPremiumDisplay}
              size={TagSize.Large}
            />
          );
        }
        if (hasPvod && (type === VideoType.SEASON || type === VideoType.SHOW)) {
          tags.push(
            <Tag
              type={TagType.EarlyAccessGreen}
              style={{
                marginLeft:
                  tags.length > 0 ? `${convertPxToVW(12, "FullHD")}vw` : "",
              }}
              key={TagType.EarlyAccessGreen}
              size={TagSize.Large}
            />
          );
        }
        if (item) {
          liveTag = LiveTag({
            item,
            size: TagSize.Large,
            style: {
              marginLeft:
                tags.length > 0 ? `${convertPxToVW(12, "FullHD")}vw` : "",
            },
          });
          premiereTag = PremiereTag({
            item,
            size: TagSize.Large,
            style: {
              marginLeft:
                tags.length > 0 ? `${convertPxToVW(12, "FullHD")}vw` : "",
            },
          });
        }
        if (liveTag) {
          tags.push(liveTag);
        } else if (premiereTag) {
          tags.push(premiereTag);
        }
        return tags;
      }
      return [];
    },
    []
  );

  return (
    <MotionDiv
      className={classNames("card", `card--${TYPE_CLASS[type] || "poster"}`)}
      data-title={item?.title || ""}
      data-rank={item?.ranking || ""}
      data-index={cardIndex}
      data-id={item?.id || ""}
      ref={ref}
      style={{ width: widthValue }}
    >
      <div className={classNames("card__thumbnail")}>
        {item?.id ? (
          <>
            <div className="card__mask" />
            <div className={classNames("card__img")} ref={imageRef}>
              <div
                className="bg-box"
                style={{
                  backgroundImage: `url(${item?.images.thumbOriginal}), url(${defaultVideoImg})`,
                }}
              />
            </div>
            {expanded ? (
              <InfoBox
                fwRef={infoBoxRef}
                {...infoBoxProps}
                tagsMore={makeTags({
                  typeOfContent: infoBoxProps.typeOfContent,
                  isSimulcast: infoBoxProps.isSimulcast,
                  tvodEndedAt: infoBoxProps.tvodEndedAt,
                  tvodBenefitType: infoBoxProps.tvodBenefitType,
                  tvodPrice: infoBoxProps.tvodPrice,
                  isVip: infoBoxProps.isVip,
                  typeOfShowedTvodTag: "price",
                  isPremiumDisplay: infoBoxProps.isPremiumDisplay,
                  preOrderEndedAt: infoBoxProps.preOrderEndedAt,
                  preOrderPrice: infoBoxProps.preOrderPrice,
                  preOrderStartedAt: infoBoxProps.preOrderStartedAt,
                  rentedTvodBenefitType: infoBoxProps.rentedTvodBenefitType,
                  hasPvod: infoBoxProps.hasPvod,
                  type: infoBoxProps.type,
                })}
              >
                <div className="card__title-card-bottom">
                  <img src={item?.images?.titleCardLight} alt="" />
                </div>
              </InfoBox>
            ) : (
              <>
                <div className="card__tag-box">
                  <CommonTags item={item} typeOfRibbon={type} />
                </div>
                <div className="card__title-card-bottom">
                  <img src={item?.images?.titleCardLight} alt="" />
                </div>
              </>
            )}
          </>
        ) : (
          <>
            <div className="card__img">
              <CardThumbnail type={type} />
            </div>
            <div className="card__tag-box">
              <div className="tag tag--center">
                <div className="dot-flashing" />
              </div>
            </div>
          </>
        )}
        {focused && <MotionFocusBox layoutId="" />}
      </div>
    </MotionDiv>
  );
});

export default OriginalCard;
