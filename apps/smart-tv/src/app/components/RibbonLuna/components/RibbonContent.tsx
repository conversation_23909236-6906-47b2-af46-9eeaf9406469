import React, {
  memo,
  useCallback,
  useContext,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState
} from "react";
import classNames from "classnames";
import { useLocation } from "react-router-dom";
import KeepAlive, { KeepAliveData } from "app/utils/KeepAlive";
import { RibbonType, TYPE_CLASS } from "types/page";
import {
  EnterPressHandler,
  FocusableComponentLayout,
  FocusContext,
  FocusDetails,
  FocusHandlerType,
  useFocusable,
  VieOnNavigation
} from "core/KeyHandle";
import { isNullOrEmpty } from "app/utils";
import BillboardContext from "context/BillboardLunaContext";
import { RibbonDataItemModel } from "app/models/RibbonData/RibbonDataItem";
import { getSubList } from "services/endpoint";
import { useRibbonVisible } from "context/RibbonVisibleContext";
import useCurrentPageRef from "hooks/useCurrentPageRef";
import { useTrackCard } from "hooks/useTrackCard";
import { getVisibleHorizontalIndexes } from "app/utils/visibleUtils";
import CommonCard from "./Common/Card";
import ComingSoonCard from "./Common/ComingSoonCard";
import OriginalCard from "./Common/OriginalCard";
import OutstreamAdsCard from "./Common/OutstreamAdsCard";
import TopViewsCard from "./Common/TopViewsCard";
import RapVietCandidateCard from "./Common/RapVietCard/CandidateCard";
import ComingBanner from "./Common/RapVietCard/CommingBanner";
import CardLiveTV from "./Common/CardLiveTV";
import useRibbonBase from "../Shared/useRibbonContentBase";
import CardFocusBox from "../Shared/CardFocusBox";
import { RibbonPerformance } from "../useRibbonContentList";
import { getLogicCardFocusBoxCustomCSSStyle } from "../transform";

interface Props {
  ribbon: RibbonPerformance;
  focusKey: string;
  hidden?: boolean;
  keepAliveData?: KeepAliveData;
  watchLayout?: boolean;
  onRibbonFocus: (
    layout: FocusableComponentLayout,
    props: object,
    details: FocusDetails
  ) => void;
  onContentFocus?: (
    type: RibbonType,
    item: any,
    index: number,
    ribbonName?: string,
    ribbonId?: string,
    ribbonIndex?: number,
    callback?: () => void
  ) => void;
  onSelect?: (
    type: RibbonType,
    item: any,
    index: number,
    ribbonName?: string,
    ribbonId?: string,
    ribbonIndex?: number,
    callback?: () => void
  ) => void;
  onCardReturn?: (props: object) => void;
  loadMore?: (
    ribbon: RibbonPerformance,
    currentIndex: number,
    callback?: (obj: any, ...rest: any) => void
  ) => void;
  onTrackKeyAction?: (direction: string) => boolean;
  updatePosition?: (ribbonIndex: any, position: any) => void;
  isFocus?: boolean;
  init?: boolean;
}

export const autoSlideRibbons: RibbonType[] = [
  RibbonType.RegistrationBanner,
  RibbonType.PromotionBanner,
  RibbonType.OutstreamAds,
  RibbonType.PaymentBanner,
  RibbonType.FunctionalRibbon
];

const realFocusBoxRibons = [RibbonType.TopViews, RibbonType.Original];

const genCardFocusKey = (
  ribbonIndex: number,
  ribbonId: string,
  itemIndex: number
) => {
  return `VN:CARD_[${ribbonIndex}][${ribbonId}]_${itemIndex}`;
};
const VIEWABLE_DURATION = 1000; // ms

const RibbonContent: React.FC<Props> = memo(
  ({
    ribbon,
    hidden = false,
    watchLayout = false,
    focusKey: focusKeyProp,
    keepAliveData,
    onSelect,
    onRibbonFocus,
    onContentFocus,
    onCardReturn,
    loadMore,
    updatePosition,
    onTrackKeyAction = () => true,
    init = true,
    isFocus = false
  }) => {
    const isMountRef = useRef<boolean>(false);
    const { prevAutoItem, removedMasterBannerItem } =
      useContext(BillboardContext);
    const ribbonData = useMemo(() => {
      return ribbon.data;
    }, [ribbon]);
    const ribbonPosition = useMemo(() => ribbon.position, [ribbon.position]);
    const ribbonIndex = useMemo(() => ribbon.index, [ribbon.index]);
    const [preferredChildFocusKey, setPreferredChildFocusKey] =
      useState<string>(() => {
        if (ribbonPosition.focusKey) {
          return ribbonPosition.focusKey;
        }
        if (
          ribbonData.id === keepAliveData?.extra.ribbonId &&
          keepAliveData?.focusKey
        ) {
          if (VieOnNavigation.isFocusableComponent(keepAliveData?.focusKey)) {
            return keepAliveData.focusKey;
          }
          const newCardFocusKey = genCardFocusKey(
            ribbonIndex,
            ribbonData.id,
            keepAliveData?.extra?.itemIndex || -1
          );
          return newCardFocusKey;
        }
        return "";
      });

    const [implicitHover, setImplicitHover] = useState<boolean>(false);
    const ribbonRef = useRef<HTMLDivElement>(null);
    const timerRef = useRef<TimerHandle>(null);

    const location = useLocation();
    const urlParams = new URLSearchParams(location.search);
    const currentPageRef = useCurrentPageRef();
    const checkVisibleTimer = useRef<TimerHandle>(null);
    const trackedCardSetRef = useRef<Set<string>>(new Set());
    const prevPathnameRef = useRef<string>(location.pathname);
    const { register, unregister, visibleRibbonIds } = useRibbonVisible();
    const isVisible = visibleRibbonIds.includes(ribbonData.id);
    const trackCard = useTrackCard({
      urlParams,
      currentPageRef,
      ribbonData,
      ribbonIndex
    });

    const {
      ref,
      focusKey,
      focused,
      hasFocusedChild,
      setFocus,
      focusSelf,
      pause,
      resume,
      navigateByDirection
    } = useFocusable({
      // focusable: ribbon.init,
      focusKey: focusKeyProp,
      preferredChildFocusKey,
      saveLastFocusedChild: false,
      onFocus: onRibbonFocus,
      trackChildren: true,
      extraProps: {
        type: ribbonData.type as RibbonType,
        index: ribbonIndex,
        init: ribbon.init
      }
    });

    const { index, onFocusHandler, showItems, listItems } = useRibbonBase({
      hasFocusedChild,
      init: ribbon.init,
      ribbonData,
      ribbonIndex,
      ribbonPosition,
      ribbonType: ribbonData.type,
      ribbonRef,
      xPosition: ribbon?.position.x ?? 0,
      pause,
      resume,
      keepAliveData,
      updatePosition
    });

    const [subscribeContentList, setSubscribeContentList] = useState<Record<
      string,
      boolean
    > | null>(null);

    const indexRef = useRef<number>(index);
    useEffect(() => {
      indexRef.current = index;
    }, [index]);
    const hasFocusedChildRef = useRef<boolean>(hasFocusedChild);
    useEffect(() => {
      hasFocusedChildRef.current = hasFocusedChild;
    }, [hasFocusedChild]);
    const listItemsRef = useRef<typeof listItems>(listItems);
    useEffect(() => {
      listItemsRef.current = listItems;
    }, [listItems]);

    const ribbonTittle = useMemo(() => {
      switch (ribbonData.type) {
        case RibbonType.Category:
        case RibbonType.PromotionBanner:
        case RibbonType.RegistrationBanner:
        case RibbonType.OutstreamAds:
        case RibbonType.PaymentBanner:
        case RibbonType.NewMasterBanner:
        case RibbonType.FunctionalRibbon:
          return "";

        default:
          return ribbonData.name;
      }
    }, [ribbonData]);

    const getSubscribeList = useCallback(
      async (items: RibbonDataItemModel[]) => {
        try {
          const subscribe = await getSubList({
            list_id: items?.map((item) => item.id)
          });
          setSubscribeContentList(subscribe);
        } catch (error) {
          console.error("getSubscribeList Error: ", error);
        }
      },
      []
    );

    const checkVisibleCards = useCallback(() => {
      if (!isVisible || !ribbonRef.current) return;

      const visible = getVisibleHorizontalIndexes(ribbonRef.current, 0.9);

      visible.forEach((i) => {
        const el = ribbonRef.current!.children[i] as HTMLElement;
        const cardKey = `${el.dataset.id}`;
        const itemInfo = showItems.find((item) => item.id === cardKey);
        if (!!itemInfo?.id && !trackedCardSetRef.current.has(cardKey)) {
          trackCard(itemInfo);
          trackedCardSetRef.current.add(cardKey);
        }
      });
    }, [isVisible, showItems, trackCard]);

    const onCardEnter = useCallback<
      EnterPressHandler<{
        item: (RibbonDataItemModel & { index?: number }) | undefined;
        index: number | undefined;
        focusKey: string;
        callback?: () => void;
      }>
    >(
      ({ item, index, focusKey, callback }) => {
        const keepAliveData: KeepAliveData = {
          path: window.location.hash,
          focus: {
            x: index || 0,
            y: ribbonIndex
          },
          extra: {
            crashRibbonPaging: ribbon.crashRibbonPaging,
            ribbonId: ribbonData.id,
            itemIndex: item?.index,
            beId: ribbonData.beId,
            cachedPages: ribbon.cachedPages
          },
          focusKey
        };

        KeepAlive.saveData(keepAliveData);

        onSelect?.(
          ribbonData.type,
          item,
          index as number,
          ribbonData.name,
          ribbonData.id,
          ribbonIndex,
          callback
        );
      },
      [onSelect, ribbonData, ribbonIndex]
    );

    const onCardFocus = useCallback<FocusHandlerType>(
      async ({ x: newX, node }, { index, focusKey }, { implicit }) => {
        const item = ribbonData.items[index];

        if (item?.id) {
          onContentFocus?.(
            ribbonData.type,
            item,
            index,
            ribbonData.name,
            ribbonData.id,
            ribbonIndex
          );
          if (
            ribbonData.type === RibbonType.CommingSoon &&
            !subscribeContentList
          ) {
            getSubscribeList(ribbonData.items);
          }
        }
        if (init) {
          autoSlide();
          onFocusHandler(newX, index, focusKey, node, implicit);

          loadMore?.(ribbon, index);

          if (!implicit) {
            setPreferredChildFocusKey(focusKey);
            setImplicitHover(false);
          } else {
            setTimeout(() => {
              resume();
            });
            setImplicitHover(true);
          }
        }
      },
      [
        init,
        focused,
        loadMore,
        onContentFocus,
        onFocusHandler,
        ribbonData,
        ribbon,
        ribbonIndex
      ]
    );

    const onCardReturnHandler = useCallback(() => {
      onCardReturn?.({ ribbonIndex, ribbonType: ribbonData.type });
    }, [onCardReturn, ribbonIndex, ribbonData.type]);

    React.useLayoutEffect(() => {
      if (!init) return;

      const isLastItem = index === listItems.length - 1;
      const isMasterBanner = ribbonData.type === RibbonType.NewMasterBanner;
      const lastIndexOfDotCharacter = prevAutoItem.lastIndexOf(".");
      const currentItemId = prevAutoItem.slice(0, lastIndexOfDotCharacter);
      const isCurrentItem = currentItemId === listItems[index]?.id;
      if (
        !ribbonData.extenalData?.disableScroll &&
        isMasterBanner &&
        isCurrentItem &&
        hasFocusedChild
      ) {
        if (!isLastItem) navigateByDirection("right", {});
      }
    }, [prevAutoItem, ribbonData]);

    React.useLayoutEffect(() => {
      if (!init) {
        loadMore?.(ribbon, 0);
      }
      return () => {
        if (timerRef.current) {
          clearTimeout(timerRef.current);
          timerRef.current = null;
        }
      };
    }, [loadMore]);

    useEffect(() => {
      if (
        ribbon.data.type === RibbonType.NewMasterBanner &&
        ribbon.data?.items?.length &&
        removedMasterBannerItem
      ) {
        focusSelf();
      }
    }, [removedMasterBannerItem, ribbon]);

    useEffect(() => {
      if (isVisible && ribbonData.type !== RibbonType.Category) {
        checkVisibleCards();
      }
    }, [isVisible]);

    useEffect(() => {
      register(ribbonData.id, ribbonRef, ribbonData);
      return () => unregister(ribbonData.id);
    }, [register, unregister, ribbonData.id]);

    useEffect(() => {
      if (location.pathname !== prevPathnameRef.current) {
        trackedCardSetRef.current = new Set();
        prevPathnameRef.current = location.pathname;
      }
    }, [location.pathname]);

    // TODO: handle auto move next
    const autoSlide = () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }

      if (
        !init ||
        listItems.length === 1 ||
        ribbonData.extenalData?.disableScroll ||
        !autoSlideRibbons.includes(ribbonData.type)
      ) {
        return;
      }

      timerRef.current = setTimeout(() => {
        if (indexRef.current < listItemsRef.current.length - 1) {
          navigateByDirection("right", {});
        }
      }, 5000);
    };

    useEffect(() => {
      if (!hasFocusedChild && timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    }, [hasFocusedChild]);

    useEffect(() => {
      return () => {
        if (timerRef.current) {
          clearTimeout(timerRef.current);
          timerRef.current = null;
        }
      };
    }, []);

    React.useEffect(() => {
      if (showItems.length > 0) {
        if (
          !isNullOrEmpty(preferredChildFocusKey) &&
          !VieOnNavigation.isFocusableComponent(preferredChildFocusKey)
        ) {
          // Case wrong Ribbon Index, preferredChildFocusKey had useless.
          // => preGen new Key of Ribbon by X Index KeepAlive and check

          const preGenNewKeyCheckByXIndex = genCardFocusKey(
            ribbon.index,
            ribbonData.id,
            keepAliveData?.focus?.x || 0
          );

          const isAvailableKey = VieOnNavigation.isFocusableComponent(
            preGenNewKeyCheckByXIndex
          );
          setFocus(
            isAvailableKey
              ? preGenNewKeyCheckByXIndex
              : genCardFocusKey(ribbon.index, ribbonData.id, 0)
          );
        }
      }
    }, []);

    const Card = useMemo(() => {
      switch (ribbonData.type as RibbonType) {
        case RibbonType.TopViews:
          return TopViewsCard;
        case RibbonType.LiveTVV2:
          return CardLiveTV;
        case RibbonType.OutstreamAds:
          return OutstreamAdsCard;
        case RibbonType.CommingSoon:
          return ComingSoonCard;
        case RibbonType.Original:
          return OriginalCard;
        case RibbonType.RapViet:
          // ROUND ONE
          return RapVietCandidateCard;
        default:
          return CommonCard;
      }
    }, [ribbonData.type]);

    useLayoutEffect(() => {
      if (!isMountRef.current && (isFocus || focused) && showItems.length) {
        const focusKey = genCardFocusKey(ribbon.index, ribbonData.id, 0);
        isMountRef.current = true;
        if (JSON.stringify(showItems) === JSON.stringify([{ index: 0 }])) {
          setTimeout(() => {
            setFocus(focusKey);
          }, 100);
        } else {
          focusSelf();
        }
      }
    }, [showItems, focused]);

    const onArrowPress = useCallback(
      (direction: string) => {
        const isAutoDirect = onTrackKeyAction?.(direction);
        if (
          hasFocusedChildRef.current &&
          indexRef.current === listItemsRef.current.length - 1 &&
          autoSlideRibbons.includes(ribbonData.type) &&
          direction === "right"
        ) {
          const focusKey = genCardFocusKey(ribbon.index, ribbonData.id, 0);
          setFocus(focusKey);
          return false;
        }
        if (
          ["left", "right"].includes(direction) &&
          ribbonData.type !== RibbonType.Category
        ) {
          if (checkVisibleTimer.current) {
            clearTimeout(checkVisibleTimer.current);
          }
          checkVisibleTimer.current = setTimeout(() => {
            checkVisibleCards?.();
          }, VIEWABLE_DURATION);
        }
        return isAutoDirect;
      },
      [
        setFocus,
        onTrackKeyAction,
        ribbon.index,
        ribbonData.id,
        checkVisibleCards
      ]
    );

    const cardElements = useMemo(() => {
      if (
        ribbonData.displayType === 0 &&
        ribbonData.type === RibbonType.RapViet
      ) {
        // COMING BANNER for RapViet
        return (
          <ComingBanner
            key={focusKey}
            focusKey={focusKey}
            type={ribbonData.type}
            isRibbonHover={implicitHover}
            onCardFocus={onCardFocus}
            onCardReturn={onCardReturnHandler}
            startedAt={ribbonData.startedAt} // Only for Coming Banner Of RapViet
          />
        );
      }

      return showItems.map((item) => {
        const focusKey = genCardFocusKey(
          ribbon.index,
          ribbonData.id,
          item.index
        );

        return (
          <Card
            key={focusKey}
            focusKey={focusKey}
            cardIndex={item.index}
            type={ribbonData.type}
            item={item}
            initSubscribe={subscribeContentList?.[item.id]}
            isRibbonHover={implicitHover}
            onCardEnter={onCardEnter}
            onCardFocus={onCardFocus}
            onCardReturn={onCardReturnHandler}
            onArrowPress={onArrowPress}
            // style={isTracked ? { outline: "3px dashed #00ff63" } : undefined}
          />
        );
      });
    }, [
      subscribeContentList,
      showItems,
      ribbon.index,
      ribbonData,
      implicitHover,
      onCardEnter,
      // onCardFocus,
      onCardReturnHandler
    ]);

    const dotsRibbonIndicator = useMemo(() => {
      return showItems.length > 1 ? (
        <>
          <div className="ribbon__indicator">
            {showItems.map((i: any, indIndex: number) => {
              return (
                <div
                  key={`indicator_${ribbonData.id}_${indIndex}`}
                  className={classNames("indicator", {
                    focus: indIndex === index
                  })}
                />
              );
            })}
          </div>
        </>
      ) : null;
    }, [showItems, ribbonData.id, index]);

    const isLiveTVEmptyRibbon = useMemo(
      () =>
        ribbonData.type === RibbonType.LiveTVV2 &&
        ribbonData?.items?.length === 1 &&
        !ribbonData?.items[0]?.id,
      [ribbonData]
    );

    const styleCardFocusBox = React.useMemo(() => {
      return getLogicCardFocusBoxCustomCSSStyle(ribbonData.type);
    }, [ribbonData.type]);

    const Ribbon = React.useMemo(() => {
      return (
        <div
          className={classNames(
            "ribbon",
            `ribbon--${TYPE_CLASS[ribbonData.type as RibbonType] || "poster"}`,
            {
              focus: isFocus,
              hidden,
              "ribbon--live-tv-v2-empty": isLiveTVEmptyRibbon
            }
          )}
          ref={ref}
          data-title={ribbonData.name}
          data-index={ribbonIndex}
          data-ribbon-type={ribbonData.type}
        >
          {ribbonTittle && (
            <div className="ribbon__title">
              <div>{ribbonTittle} Node 20 Only </div>
            </div>
          )}
          {hasFocusedChild &&
            !implicitHover &&
            !realFocusBoxRibons.includes(ribbonData.type) && (
              <CardFocusBox
                type={ribbonData.type}
                customStyle={styleCardFocusBox}
              />
            )}
          <div
            // style={{ transform }}
            ref={ribbonRef}
            className={classNames("ribbon__body", {
              ribbonWatchLayout: watchLayout
            })}
            data-type="slide"
          >
            {cardElements}
          </div>
          {/* Indicator for Ribbon Full */}
          {dotsRibbonIndicator}
        </div>
      );
    }, [
      watchLayout,
      ribbonIndex,
      ribbonTittle,
      hidden,
      isFocus,
      cardElements,
      dotsRibbonIndicator,
      hasFocusedChild,
      implicitHover,
      realFocusBoxRibons,
      ribbonData,
      isVisible
    ]);

    return (
      <FocusContext.Provider value={focusKey}>{Ribbon}</FocusContext.Provider>
    );
  }
);

export default RibbonContent;
