import React, { ReactElement } from "react";
import classNames from "classnames";
import Tag, { TagSize, TagType } from "app/components/Common/Tag/Tag";
import { makeRemainingTimeStr } from "app/utils/formatTime";
import { TYPE_OF_CONTENT, TVOD_STATUS } from "types/endpoint";
import { formatMoney, formatTvodDevidedPrice } from "app/utils/formatNumber";
import Icon from "app/components/Common/Icon";
import { RibbonDataItemModel } from "app/models/RibbonData/RibbonDataItem";
import { VideoType } from "types/page";
import GroupTagBottom, { GroupTagBottomType } from "./Tag/GroupTagBottom";
import TagPriceUnit from "./Tag/TagPriceUnit";

interface TvodTagItemType {
  isPremium?: TYPE_OF_CONTENT;
  isSimulcast?: boolean;
  type?: VideoType;
  tvod: any;
}

interface Props {
  item: TvodTagItemType;
  isTimeRemainingShown?: boolean;
  isPriceShown?: boolean;
  isForRibbon?: boolean;
  isPriceDevided?: boolean;
  isUpperCase?: boolean;
  style?: any;
  isBorderRadius?: boolean;
  size?: TagSize;
}

export function getTvodStatus(params: { item: TvodTagItemType }): number[] {
  const { item } = params;
  const isTvodContent: boolean = item?.isPremium === TYPE_OF_CONTENT.TVOD;
  if (!isTvodContent) {
    return [TVOD_STATUS.notTvod];
  }
  const status = [];
  const isSimulcast: boolean =
    item?.tvod?.tvod_benefit_type === TVOD_STATUS.simulcast;
  const isLiveEvent: boolean =
    item?.tvod?.tvod_benefit_type === TVOD_STATUS.liveEvent;
  if (isSimulcast) {
    status.push(TVOD_STATUS.simulcast);
  }
  if (isLiveEvent) {
    status.push(TVOD_STATUS.liveEvent);
  }
  const tvodBenefitType: number = item?.tvod?.benefit_type;
  if (tvodBenefitType <= 0) {
    status.push(TVOD_STATUS.notRented);
  } else if (tvodBenefitType === 1) {
    status.push(TVOD_STATUS.rentedButNotWatch);
  } else if (tvodBenefitType === 2) {
    status.push(TVOD_STATUS.rentedButWatched);
  }
  return status;
}

interface TransformTvodItemStructureType {
  isSimulcast: boolean;
  isPremium: TYPE_OF_CONTENT;
  isPremiumDisplay?: string;
  isFree?: number;
  type: VideoType;
  tvod: {
    benefit_ended_at: any;
    benefit_type: any;
    price: any;
    tvod_benefit_type: any;
    product_owner_type: any;
    pre_order: {
      pre_order_ended_at: any;
      pre_order_price: any;
      pre_order_started_at: any;
    };
  };
}

export function transformTvodTagItemStructure(
  data: any
): TransformTvodItemStructureType {
  return {
    isPremium: data?.is_premium ?? -10,
    isFree: data?.is_free ?? 0,
    type: data?.type,
    isPremiumDisplay: data?.is_premium_display ?? "",
    isSimulcast: data?.is_simulcast ?? false,
    tvod: data?.tvod as any,
  };
}

export function makeTvodTagItem(data: any) {
  return {
    benefit_ended_at: data?.tvodEndedAt,
    benefit_type: data?.tvodBenefitType,
    price: data?.tvodPrice,
    tvod_benefit_type: data?.rentedTvodBenefitType,
    product_owner_type: data?.productOwnerType,
    pre_order: {
      pre_order_ended_at: data?.preOrderEndedAt,
      pre_order_price: data?.preOrderPrice,
      pre_order_started_at: data?.preOrderStartedAt,
    },
  };
}

export const RemainingTimeMsg = ({
  remainingTimeMsg,
  sameTextColor = true,
}: {
  remainingTimeMsg: string;
  sameTextColor?: boolean;
}) => {
  return (
    <>
      {remainingTimeMsg ? (
        <div
          className={classNames("msg--remaining-time", {
            notSameTextColor: !sameTextColor,
          })}
        >
          <span className="icon">
            <Icon viewBox="0 0 32 32" name="vie-clock-o-rc-medium" />
          </span>
          Hết hạn sau {remainingTimeMsg}
        </div>
      ) : null}
    </>
  );
};

export const makeTag: (params: Props) => any = ({
  item,
  isTimeRemainingShown = true,
  isPriceShown = true,
  isForRibbon = true,
  isPriceDevided = false,
  isUpperCase = true,
  style = {},
  isBorderRadius = false,
  size,
}: Props) => {
  const isTvodContent: boolean =
    item?.isPremium === TYPE_OF_CONTENT.TVOD ||
    item?.isPremium === TYPE_OF_CONTENT.TVOD_AND_SVOD;
  if (isTvodContent) {
    const remainingTime: number = item?.tvod?.benefit_ended_at;
    const remainingTimeStr = makeRemainingTimeStr(remainingTime, isUpperCase);
    const tvodBenefitType: number = item?.tvod?.benefit_type;
    let hasRemainingTag = true;
    if (item?.type === VideoType.LIVESTREAM || item?.isSimulcast) {
      hasRemainingTag = false;
    }

    const tvodPrice: number = item?.tvod?.price;
    const preOrderEndedAt: number =
      item?.tvod?.pre_order?.pre_order_ended_at ?? 0;
    const preOrderStartedAt: number =
      item?.tvod?.pre_order?.pre_order_started_at ?? 0;
    const now: number = new Date().getTime() / 1000;
    const preOrderPrice: number = item?.tvod?.pre_order?.pre_order_price ?? 0;
    const isPreOrderAvailable =
      now >= preOrderStartedAt && now <= preOrderEndedAt && preOrderPrice;
    const totalPrice: number = isPreOrderAvailable ? preOrderPrice : tvodPrice;
    if (tvodBenefitType <= 0) {
      if (isPriceShown && totalPrice > 0) {
        return (
          <>
            <Tag
              type={TagType.TvodPrice}
              isBorderRadius={isBorderRadius}
              size={size}
              key={TagType.TvodPrice}
              style={style}
              isPosition={isForRibbon}
            >
              <>
                Thuê&nbsp;
                {isPriceDevided
                  ? formatTvodDevidedPrice(totalPrice)
                  : formatMoney(totalPrice)}
                đ{/* <TagPriceUnit /> */}
              </>
            </Tag>
          </>
        );
      }
    } else if (isTimeRemainingShown && hasRemainingTag) {
      if (remainingTime !== -1 && remainingTimeStr) {
        return isForRibbon ? (
          <GroupTagBottom
            type={GroupTagBottomType.MultipleLine}
            key={GroupTagBottomType.MultipleLine}
          >
            <Tag
              type={TagType.TvodRemainingTime}
              isPosition
              isBorderRadius={isBorderRadius}
              size={size}
              key={TagType.TvodRemainingTime}
              style={style}
            >
              Hết hạn sau {remainingTimeStr}
            </Tag>
          </GroupTagBottom>
        ) : (
          <RemainingTimeMsg remainingTimeMsg={remainingTimeStr} />
        );
      }
    }
  }
  return null;
};

function TvodTagLuna({
  item,
  isTimeRemainingShown = true,
  isPriceShown = true,
  isForRibbon = true,
  isPriceDevided = false,
  isUpperCase = true,
  isBorderRadius,
  size,
  style,
}: Props): ReactElement {
  return makeTag({
    item,
    isTimeRemainingShown,
    isPriceShown,
    isForRibbon,
    isPriceDevided,
    isUpperCase,
    isBorderRadius,
    size,
    style,
  });
}

export default TvodTagLuna;
