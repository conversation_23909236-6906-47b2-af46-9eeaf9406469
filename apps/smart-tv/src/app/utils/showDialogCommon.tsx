import React from "react";
import CountryStopServiceForm from "app/components/DialogCommon/dialogForm/CountryStopServiceForm";
import DialogCommon from "app/components/DialogCommon";
import GlobalCountryLimitForm from "app/components/DialogCommon/dialogForm/GlobalCountryLimitForm";

export function showDialogGlobalCountryLimit(
  dialogItem: any,
  onClose: VoidFunction = () => {}
) {
  console.log("showDialogGlobalCountryLimit");
  return DialogCommon.fullScreenDialog({
    keyName: "dialog_global_country_limit",
    children: <GlobalCountryLimitForm />,
    preferredChildFocusKey: "VN:DIALOG_FULL_SCREEN_GLOBAL_COUNTRY_LIMIT",
    data: dialogItem,
    onClose,
  });
}

export function showDialogGlobalCountryStopService(
  onClose: VoidFunction = () => {}
) {
  return DialogCommon.fullScreenDialog({
    keyName: "dialog_global_country_stop_service",
    children: <CountryStopServiceForm />,
    preferredChildFocusKey: "VN:DIALOG_FULL_SCREEN_GLOBAL_COUNTRY_STOP_SERVICE",
    onClose,
  });
}
