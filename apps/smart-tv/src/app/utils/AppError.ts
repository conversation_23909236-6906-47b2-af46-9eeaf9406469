export enum ErrorType {
  None = 0,
  NotGeoValid = 1,
  Maintainance,
  GetMenuError,
  SubMenuEmpty,
  PageEmpty,
  UnderConstruction,
  FailToMakeRibbonList,
  FailToLoadCommentList,
  LoadDataEmpty,
  Unexpected = 99,
}
class AppError extends Error {
  code: ErrorType;

  constructor(code: ErrorType) {
    super();
    this.code = code;
    this.name = "App Error";
  }
}

export default AppError;
