import get from "lodash/get";
import { MenuItem, SubMenu } from "types/endpoint/Menu";
import store from "app/store/store";
import { ROUTES } from "./constants";
import { getFirstMenuMustHomePage } from "../routines/common";

export const makeRoute = (menu: MenuItem | SubMenu): string => {
  switch (menu.display_type) {
    case "schedule":
      return ROUTES.SCHEDULE;
    case "livetv":
      return ROUTES.LIVE_TV_NEW_UI;
    case "livestream":
      return `${ROUTES.LIVE_STREAM}/${menu.id}`;
    case "sport-football":
      return `${ROUTES.SPORT_FOOTBALL}/${menu.id}`;
    case "vod":
    default:
      return `${ROUTES.VOD}/${menu.id}`;
  }
};

export const makeSubPageRoute = (
  typeOfPage: "subpage" | "ribbon",
  slug: string,
  id: string
) => {
  if (typeOfPage === "subpage") {
    return `${ROUTES.SUB_PAGE}/${slug}/${id}`;
  }
  return `${ROUTES.RIBBON}/${id}`;
};

export const checkHomePage = (pathname: string, menuId: string): boolean => {
  const {
    app: { menus },
  } = store.getState();
  let isHomePage = false;
  if (pathname === ROUTES.ROOT) {
    isHomePage = true;
  } else {
    const menuHomepage = getFirstMenuMustHomePage(menus);
    if (menuHomepage) {
      if (
        pathname === ROUTES.LIVE_TV_NEW_UI &&
        menuHomepage.display_type === "livetv"
      ) {
        isHomePage = true;
      } else if (pathname.includes(menuHomepage.id)) {
        isHomePage = true;
      }
    }
  }

  return isHomePage;
};

export const generatePathForCategoryItem = (
  subMenu: any,
  slug: string,
  typeOfPage: "subpage" | "ribbon"
): string => {
  const {
    app: { menus },
  } = store.getState();
  const menuIdRedirect = get(subMenu, ["menu_id_redirect"], "");
  const id = get(subMenu, ["id"], "");

  if (menuIdRedirect === "") {
    const ribbonIdRedirect = get(subMenu, ["ribbon_id_redirect"], "");
    if (ribbonIdRedirect) {
      return makeSubPageRoute("ribbon", slug, ribbonIdRedirect);
    }

    return makeSubPageRoute(typeOfPage, slug, id);
  }

  const redirectItem = menus.find((menu) => {
    return menu.id === menuIdRedirect;
  });

  if (redirectItem) {
    const { display_type, id } = redirectItem;

    switch (display_type) {
      case "livetv":
        return ROUTES.LIVE_TV_NEW_UI;
      case "livestream":
        return `${ROUTES.LIVE_STREAM}/${id}`;
      case "sport-football":
        return `${ROUTES.SPORT_FOOTBALL}/${id}`;
      case "vod":
        return `${ROUTES.VOD}/${id}`;
      default:
        return makeSubPageRoute(typeOfPage, slug, id);
    }
  }

  return makeSubPageRoute(typeOfPage, slug, menuIdRedirect);
};
