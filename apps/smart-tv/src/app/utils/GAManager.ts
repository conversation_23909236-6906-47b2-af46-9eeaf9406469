import { useEffect } from "react";
import ReactGA from "react-ga";
import platform from "services/platform";

class GAManagerSingleton {
  constructor() {
    const isGA = !(
      process.env.REACT_APP_INJECT_SDK_GA &&
      process.env.REACT_APP_INJECT_SDK_GA === "false"
    );
    try {
      if (isGA) {
        ReactGA.initialize(platform.trackingGAID, {
          titleCase: false,
          debug: false,
        });
      }
    } catch (e) {
      throw e;
    }
  }

  gaPageView(pagePath: string = "/") {
    try {
      ReactGA.ga("set", "checkProtocolTask", null);
      ReactGA.pageview(pagePath);
    } catch (e) {
      throw e;
    }
  }

  gaSetUser(userId: string = "anon_id") {
    try {
      ReactGA.ga("set", "userId", userId);
    } catch (e) {
      throw e;
    }
  }

  gaEvent(
    action: string = "",
    category: string = "",
    label: string = "",
    value: boolean = false
  ) {
    try {
      const gaEventObj = {
        category,
        action,
        label,
        nonInteraction: value,
      };
      ReactGA.event(gaEventObj);
    } catch (e) {
      throw e;
    }
  }
}
const GAManager: GAManagerSingleton = new GAManagerSingleton();
export default GAManager;

export const useGAPageView = (pagePath: string = "/") => {
  useEffect(() => {
    try {
      GAManager.gaPageView(pagePath);
    } catch (e) {
      throw e;
    }
  }, []);
};
