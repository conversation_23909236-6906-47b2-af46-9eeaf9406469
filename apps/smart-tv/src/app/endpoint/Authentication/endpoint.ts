import platform from "services/platform";
import { callApiNotDomain, callApiServiceUser } from "services/api";

/**
 <PERSON><PERSON><PERSON> tra email hoặc sdt
 @param userName Tên đăng nhập (số điện thoại hoặc email)
 @param countryCode 
  Mã quốc gia - 2 ký tự Bắt buộc khi username là số điện thoại
  Ví dụ:
  Việt Nam - VN
  Hoa Kỳ - US
  Truyền rỗng nếu username là email
 */
export const authenticationValidate = ({
  userName,
  countryCode,
  type,
}: {
  userName: string;
  countryCode: string;
  type?: string;
}) => {
  const data = {
    username: userName,
    country_code: countryCode,
    platform: platform.platformName,
    model: platform.model,
    device_id: platform.vieONDeviceID,
    device_name: platform.manufacturer,
    device_type: "smarttv",
    ...(type ? { notification_type: type } : {}),
  };
  return callApiServiceUser(
    {
      url: "v2/register",
      method: "POST",
      data,
      cache: false,
    },
    null,
    true
  );
};

/**
  API login
 */
export const authenticationLogin = (
  username: string,
  countryCode: string,
  password: string
) => {
  const data = {
    username,
    country_code: countryCode,
    password,
    platform: platform.platformName,
    model: platform.model,
    device_id: platform.vieONDeviceID,
    device_name: platform.manufacturer,
    device_type: "smarttv",
  };
  return callApiServiceUser(
    {
      url: "v2/login",
      method: "POST",
      data,
      cache: false,
    },
    null,
    true
  );
};

/**
  API get info account
 */
export const authenticationGetProfile = (token: string = "") => {
  return callApiServiceUser({
    url: "profile",
    method: "GET",
    headers: {
      Authorization: token,
    },
  });
};

/**
 Confirm OTP cho flow Đăng ký hoặc quên mật khẩu.
 @param oldPassword Bắt buộc khi trong flow Thay đổi mật khẩu
 @param password 
  Mật khẩu mới: 
  Flow Đăng ký
  Flow Quên mật khẩu
  Flow Thay đổi mặt khẩu
 */
export const authenticationOTPConfirm = (
  confirmationNo: string,
  code: string,
  token?: string,
  password?: string,
  oldPassword?: string
) => {
  const data = {
    confirmation_no: confirmationNo,
    code,
    old_password: oldPassword,
    password,
    platform: platform.platformName,
  };
  return callApiServiceUser(
    {
      url: "v2/otp/confirm",
      method: "POST",
      data,
      cache: false,
      headers: {
        Authorization: token || ""
      }
    },
    null,
    true
  );
};

/**
 API OTP validate
 */
export const authenticationOTPValidate = (
  confirmationNo: string,
  code: string,
  token?: string
) => {
  const data = {
    confirmation_no: confirmationNo,
    code,
    platform: platform.platformName
  };
  return callApiServiceUser(
    {
      url: "v2/otp/validate",
      method: "POST",
      data,
      cache: false,
      headers: {
        Authorization: token || ""
      }
    },
    null,
    true
  );
};

/**
 Quên mật khẩu.
 @param userName Tên đăng nhập (số điện thoại hoặc email)
 @param countryCode 
  Mã quốc gia - 2 ký tự Bắt buộc khi username là số điện thoại
  Ví dụ:
  Việt Nam - VN
  Hoa Kỳ - US
  Truyền rỗng nếu username là email
 */
export const authenticationPasswordForgot = (
  userName: string,
  countryCode: string,
  type?: string
) => {
  const data = {
    username: userName,
    country_code: countryCode,
    platform: platform.platformName,
    model: platform.model,
    device_id: platform.vieONDeviceID,
    device_name: platform.manufacturer,
    device_type: "smarttv",
    ...(type ? { notification_type: type } : {}),
  };
  return callApiServiceUser({
    url: "v2/password/forgot",
    method: "POST",
    data,
    cache: false,
  });
};

/**
 API lấy code login trên WEB/APP
 */
export const authenticationDeviceLogin = () => {
  const data = {
    platform: platform.platformName,
    model: platform.model,
    device_id: platform.vieONDeviceID,
    device_name: platform.manufacturer,
    device_type: "smarttv",
  };
  return callApiServiceUser(
    {
      url: "v2/device/login",
      method: "POST",
      data,
      cache: false,
    },
    null,
    true
  );
};

/**
 API check status login trên WEB/APP
 */
export const authenticationDeviceLoginStatus = (code: string) => {
  const data = {
    code,
    platform: platform.platformName,
  };
  return callApiServiceUser({
    url: "v2/device/login",
    method: "GET",
    params: data,
    cache: false,
  });
};

/**
  API refresh token
 */
export const authenticationRefreshToken = (
  refreshToken: string,
  accessToken: string,
  profileToken: string
) => {
  const data = {
    token: accessToken,
    profile_token: profileToken,
    platform: platform.platformName,
  };
  return callApiServiceUser({
    url: "v2/auth/token/refresh",
    method: "POST",
    cache: false,
    headers: {
      Authorization: refreshToken,
      "Profile-Token": profileToken,
    },
    data,
  });
};

/**
 Khôi phục tài khoản.
 */
export const authenticationRestoreAccount = (token: string, type?: string) => {
  const data = {
    platform: platform.platformName,
    model: platform.model,
    device_id: platform.vieONDeviceID,
    device_name: platform.manufacturer,
    device_type: "smarttv",
    ...(type ? { notification_type: type } : {}),
  };
  return callApiServiceUser({
    url: "v2/me/restore",
    method: "POST",
    data,
    cache: false,
    headers: {
      Authorization: token,
    },
  });
};
