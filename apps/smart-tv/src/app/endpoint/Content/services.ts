import { ProfileMapRestrictedContent } from "app/models/VideoIntro/ProfileMapRestrictedContent";
import { RestrictedContentModel } from "app/models/Content/RestrictedContentModel";
import * as api from "./endpoint";

export const getProfilesRestrictedByContent = async (
  contentId: string
): Promise<{
  caption: string;
  items: ProfileMapRestrictedContent[];
}> => {
  const response = await api
    .getProfilesRestrictedByContent(contentId)
    .then((res) => {
      const { items = [], caption } = res?.result ?? {};
      return {
        items: items.map((m: any) => new ProfileMapRestrictedContent(m)),
        caption,
      };
    })
    .catch(() => {
      return { items: [], caption: "" };
    });
  return response;
};

export const getRestrictedContentByProfileId = async (
  profileId: string
): Promise<RestrictedContentModel[]> => {
  const response = await api
    .getRestrictedContentByProfileId(profileId)
    .then((res) => {
      const { items = [] } = res?.result ?? {};
      return items.map((m: any) => new RestrictedContentModel(m));
    })
    .catch(() => {
      return [];
    });
  return response;
};

export const updateProfilesWithRestrictContents = async (
  data: { profileId: string; contents: { id: string; status: boolean }[] }[]
): Promise<{
  result: "success" | "fail";
  message: string;
  code: number;
}> => {
  const response = (await api
    .updateProfilesWithRestrictContents(data)
    .then((res: any) => {
      return {
        result: "success",
        code: res.code ?? -1,
        message: res.message ?? "",
      };
    })
    .catch((error) => {
      return {
        result: "fail",
        code: error?.data?.code ?? -1,
        message: error?.data?.message ?? "",
      };
    })) as any;
  return response;
};
