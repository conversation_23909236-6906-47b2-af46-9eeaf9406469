include:
  - project: "devops/ci/templates/ci-template"
    ref: v1.4.5
    file: "static-web.gitlab-ci.yml"

stages:
  - test
  - build
  - deploy
  - dockerize
  - deploy-k8s
  - release
  - promote
  - clear-cache-cdn

.dockerfile: &dockerfile
  - |
    cat << EOF > Dockerfile
    # Build static files
    FROM $DOCKER_HUB/$DOCKER_IMAGE_NODE20_ALPINE_S3CMD AS builder
    WORKDIR /usr/src/app
    COPY . .
    # RUN apk add tzdata && cp /usr/share/zoneinfo/Asia/Ho_Chi_Minh /etc/localtime
    RUN npm install --force
    RUN CI=$CHECK_RUN_BUILD npm run build
    RUN echo "{ \"appVersion\": \"${CI_COMMIT_BRANCH:8}\", \"buildVersion\": \"${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}\" }" >> build/version.json
    RUN rm -rf public/${PLATFORM}
    RUN mv build/ public/${PLATFORM}
    # Upload to s3
    # Trên node-20-alpine cần dùng ~/pyvenv/bin/s3cmd, thay vì s3cmd trên node-18-alpine trở xuống
    RUN ~/pyvenv/bin/s3cmd --host-bucket='https://hcm-vt-s3.vieon.vn' \
      --host=https://hcm-vt-s3.vieon.vn  \
      --access_key=$S3_ACCESS_KEY  \
      --secret_key=$S3_SECRET_KEY \
      --no-mime-magic --guess-mime-type \
      put -r --acl-public -f public/${PLATFORM} s3://$DIR_NAME/
    # Upload to old storate for Samsung
    RUN if [ "$ENVIRONMENT" = "production" ] && [ "$PLATFORM" = "$PLATFORM_TIZEN" ] ; then \
      s3cmd --host-bucket='https://hcm-vt-s3.vieon.vn' \
        --host=https://hcm-vt-s3.vieon.vn  \
        --access_key=$S3_ACCESS_KEY  \
        --secret_key=$S3_SECRET_KEY \
        --no-mime-magic --guess-mime-type \
        put -r --acl-public -f public/${PLATFORM}/static s3://$DIR_NAME_OLD_STORAGE/ ;\
    fi

    # Serve web static
    FROM nginx:alpine AS deploy
    COPY --from=builder /usr/src/app/public/${PLATFORM} /usr/share/nginx/html
    COPY _config/default.conf /etc/nginx/conf.d/default.conf

    EOF

.clear-cache-cdn: &clear-cache
  image:
    name: curlimages/curl:7.79.1
  script:
    - DIR_TIME=$(date +%s)
    - |
      curl -X POST "${CDN_API}" -H "Content-Type: application/json" -H "Accept: application/json" -H "Authorization: ${CDN_API_TOKEN_TYPE} ${CDN_API_TOKEN}" --data "{\"cdnDomain\": \"${CDN_STATIC_DOMAIN}\", \"patterns\": [\"/${DIR_NAME}/\"], \"type\": \"BEGIN\"}"
    - domains="${CDN_DOMAIN}"
    - |
      curl -X POST -F token="${CLEAR_CACHE_TOKEN}" -F "ref=main" -F "variables[CDN_DOMAIN]=static2.vieon.vn" -F "variables[CDN_PATH]=/${DIR_NAME}" "${CLEAR_CACHE_PIPELINE}"
  only:
    refs:
      - /^release\/*/i
      - master
      - feature/clear-cache

.clear-cache-old-storage: &clear-cache-old-storage
  image:
    name: curlimages/curl:7.79.1
  script:
  - DIR_TIME=$(date +%s)
  - domains="${CDN_DOMAIN}"
  - |
      curl -X POST -F token="${CLEAR_CACHE_TOKEN}" -F "ref=main" -F "variables[CDN_DOMAIN]=static2.vieon.vn" -F "variables[CDN_PATH]=/${DIR_NAME_OLD_STORAGE}" "${CLEAR_CACHE_PIPELINE}"
      curl -X POST -F token="${CLEAR_CACHE_TOKEN}" -F "ref=main" -F "variables[CDN_DOMAIN]=static.vieon.vn" -F "variables[CDN_PATH]=/${DIR_NAME_OLD_STORAGE}" "${CLEAR_CACHE_PIPELINE}"
  only:
    refs:
      - /^release\/*/i
      - master

test:unit-test:
  stage: test
  image: ${DOCKER_HUB}/${DOCKER_IMAGE_NODE20_ALPINE_S3CMD}
  script:
    - node -v
  only:
    refs:
      - merge_requests
      - master
      - tags
      - /^release\/*/i

# Dev env
build:dev:lg:
  extends:
    - .dev-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_LG}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_LG}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-lg3
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=develop
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-lg3
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}
    - docker rmi ${IMAGE_TAG} || echo "No such image"
  # only:
  #   refs:
  #     - update/only_node_20

deploy:dev:lg:
  extends:
    - .dev-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-lg3
    - ENVIRONMENT=develop
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-lg
    - IMAGE_NAME=${DOCKER_HUB}/${CI_ENVIRONMENT_NAME}-vieon-lg3
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}
  # only:
  #   refs:
  #     - update/only_node_20

build:dev:tizen:
  extends:
    - .dev-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_TIZEN}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_TIZEN}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=develop
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}
  # only:
  #   refs:
  #     - update/only_node_20


deploy:dev:tizen:
  extends:
    - .dev-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - ENVIRONMENT=develop
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-tizen
    - IMAGE_NAME=${DOCKER_HUB}/${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}
  # only:
  #   refs:
  #     - update/only_node_20

build:dev:vidaa:
  extends:
    - .dev-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_VIDAA}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_VIDAA}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=develop
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}
  # only:
  #   refs:
  #     - update/only_node_20

deploy:dev:vidaa:
  extends:
    - .dev-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - ENVIRONMENT=develop
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-vidaa
    - IMAGE_NAME=${DOCKER_HUB}/${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}
  # only:
  #   refs:
  #     - update/only_node_20

build:dev:coolita:
  extends:
    - .dev-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_COOLITA}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_COOLITA}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-coolita
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=develop
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-coolita
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}
  # only:
  #   refs:
  #     - update/only_node_20

deploy:dev:coolita:
  extends:
    - .dev-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-coolita
    - ENVIRONMENT=develop
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-coolita
    - IMAGE_NAME=${DOCKER_HUB}/${CI_ENVIRONMENT_NAME}-vieon-coolita
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}
  # only:
  #   refs:
  #     - update/only_node_20

# Testing Env
build:testing:lg:
  extends:
    - .testing-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_LG}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_LG}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-lg3
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-lg3
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}

deploy:testing:lg:
  extends:
    - .testing-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-lg3
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-lg
    - IMAGE_NAME=${DOCKER_HUB}/${CI_ENVIRONMENT_NAME}-vieon-lg3
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}

build:testing:tizen:
  extends:
    - .testing-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_TIZEN}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_TIZEN}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}

deploy:testing:tizen:
  extends:
    - .testing-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-tizen
    - IMAGE_NAME=${DOCKER_HUB}/${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}

build:testing:vidaa:
  extends:
    - .testing-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_VIDAA}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_VIDAA}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}

deploy:testing:vidaa:
  extends:
    - .testing-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-vidaa
    - IMAGE_NAME=${DOCKER_HUB}/${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}

build:testing:coolita:
  extends:
    - .testing-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_COOLITA}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_COOLITA}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-coolita
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-coolita
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}

deploy:testing:coolita:
  extends:
    - .testing-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-coolita
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-coolita
    - IMAGE_NAME=${DOCKER_HUB}/${CI_ENVIRONMENT_NAME}-vieon-coolita
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}


# Staging Env
.staging-env:
  environment:
    name: staging
  only:
    refs:
      - /^release\/*/i
  when: manual

build:staging:lg:
  extends:
    - .staging-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_LG}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_LG}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-lg3
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-lg3
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}

deploy:staging:lg:
  extends:
    - .testing-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-lg3
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-lg
    - IMAGE_NAME=${DOCKER_HUB}/staging-vieon-lg3
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}
  when: manual

build:staging:tizen:
  extends:
    - .staging-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_TIZEN}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_TIZEN}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}

deploy:staging:tizen:
  extends:
    - .testing-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-tizen
    - IMAGE_NAME=${DOCKER_HUB}/staging-vieon-tizen3
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}
  when: manual

build:staging:vidaa:
  extends:
    - .staging-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_VIDAA}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_VIDAA}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}

deploy:staging:vidaa:
  extends:
    - .testing-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-vidaa
    - IMAGE_NAME=${DOCKER_HUB}/staging-vieon-vidaa
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}
  when: manual

build:staging:coolita:
  extends:
    - .staging-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_COOLITA}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_COOLITA}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-coolita
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-coolita
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}

deploy:staging:coolita:
  extends:
    - .testing-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-coolita
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-coolita
    - IMAGE_NAME=${DOCKER_HUB}/staging-vieon-coolita
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}


# Production env
build:production:lg:
  extends:
    - .production-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_LG}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_LG}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-lg3
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-lg3
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}

deploy:production:lg:
  extends:
    - .production-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-lg3
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-lg
    - IMAGE_NAME=${DOCKER_HUB}/${CI_ENVIRONMENT_NAME}-vieon-lg3
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}

build:production:tizen:
  extends:
    - .production-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_TIZEN}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_TIZEN}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - DIR_NAME_OLD_STORAGE=${S3_BUCKET_SS}
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}

deploy:production:tizen:
  extends:
    - .production-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - DIR_NAME_OLD_STORAGE=${S3_BUCKET_SS}
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-tizen
    - IMAGE_NAME=${DOCKER_HUB}/${CI_ENVIRONMENT_NAME}-vieon-tizen3
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}

build:production:vidaa:
  extends:
    - .production-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_VIDAA}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_VIDAA}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}

deploy:production:vidaa:
  extends:
    - .production-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-vidaa
    - IMAGE_NAME=${DOCKER_HUB}/${CI_ENVIRONMENT_NAME}-vieon-vidaa
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}

build:production:coolita:
  extends:
    - .production-env
    - .dockerize
  before_script:
    - BUILD_ENV_FILE=${BUILD_ENV_FILE_COOLITA}
    - BUILD_OUTPUT_DIR=${BUILD_OUTPUT_DIR}
    - PLATFORM=${PLATFORM_COOLITA}
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-coolita
    - echo "REACT_APP_VERSION=${CI_COMMIT_BRANCH:8}" >> ${BUILD_ENV_FILE}
    - echo "REACT_BUILD_VERSION=${CI_COMMIT_BRANCH:8}-${CI_COMMIT_SHORT_SHA}" >> ${BUILD_ENV_FILE}
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - DOCKER_IMAGE_NAME=${CI_ENVIRONMENT_NAME}-vieon-coolita
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}:commit-${CI_COMMIT_SHORT_SHA}
  script:
    # script build
    - cat ${BUILD_ENV_FILE} > .env
    - cat .env
    - cat ${DOCKER_FILE}
    # script dockerize
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    - docker push ${IMAGE_TAG}

deploy:production:coolita:
  extends:
    - .production-env
    - .deploy-k8s
  before_script:
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-vieon-coolita
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}-coolita
    - IMAGE_NAME=${DOCKER_HUB}/${CI_ENVIRONMENT_NAME}-vieon-coolita
    - IMAGE_TAG=commit-${CI_COMMIT_SHORT_SHA}


clear-cache-cdn-dev:tizen:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=dev-vieon-tizen3
  when: manual

clear-cache-cdn-dev:lg:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=dev-vieon-lg3
  when: manual
  # only:
  #   refs:
  #     - fix/update-shaka-player

clear-cache-cdn-dev:vidaa:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=dev-vieon-vidaa
  when: manual

clear-cache-cdn-dev:coolita:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=dev-vieon-coolita
  when: manual

clear-cache-cdn-testing:tizen:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=testing-vieon-tizen3
  when: manual

clear-cache-cdn-testing:lg:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=testing-vieon-lg3
  when: manual

clear-cache-cdn-testing:vidaa:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=testing-vieon-vidaa
  when: manual

clear-cache-cdn-testing:coolita:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=testing-vieon-coolita
  when: manual

clear-cache-cdn-staging:tizen:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=staging-vieon-tizen3
  when: manual

clear-cache-cdn-staging:lg:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=staging-vieon-lg3
  when: manual

clear-cache-cdn-staging:vidaa:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=staging-vieon-vidaa
  when: manual

clear-cache-cdn-staging:coolita:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=staging-vieon-coolita
  when: manual

clear-cache-cdn-production:tizen:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=production-vieon-tizen3
  when: manual

clear-cache-cdn-production:lg:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=production-vieon-lg3
  when: manual

clear-cache-cdn-production:vidaa:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=production-vieon-vidaa
  when: manual

clear-cache-cdn-production:coolita:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME=production-vieon-coolita
  when: manual

clear-cache-old-storage:
  extends:
    - .clear-cache-old-storage
  stage: clear-cache-cdn
  before_script:
    - DIR_NAME_OLD_STORAGE=${S3_BUCKET_SS}
  when: manual
