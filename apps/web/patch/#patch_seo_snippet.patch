Subject: [PATCH] #patch seo snippet
---
Index: src/components/seo/SeoAllPage.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/seo/SeoAllPage.tsx b/src/components/seo/SeoAllPage.tsx
--- a/src/components/seo/SeoAllPage.tsx	(revision ff178b3ef848f6ede706df286c8fc1e423d89044)
+++ b/src/components/seo/SeoAllPage.tsx	(date 1750043670361)
@@ -1,15 +1,13 @@
 import React from 'react';
 import { LocalBusinessJsonLd, NextSeo } from 'next-seo';
 import Head from 'next/head';
-import { ROBOTS, STATIC_DOMAIN, DOMAIN_WEB, BUILD_ID } from '@config/ConfigEnv';
+import { BUILD_ID, DOMAIN_WEB, ROBOTS, STATIC_DOMAIN } from '@config/ConfigEnv';
 import ConfigSeo from '@config/ConfigSeo';
 import { HTTP_CODE, PAGE } from '@constants/constants';
-import { getSeoData } from '@services/detailServices';
 import { useSelector } from 'react-redux';
 
 const SeoAllPage = React.memo((props: any) => {
-  const { seo, listArrRibbon, contentEpisode, content, seo_config, oldData, isSeaSon } =
-    props || {};
+  const { seo, listArrRibbon, oldData } = props || {};
   if (!seo?.open_graph) return null;
   const defaultTitle = oldData?.title || seo?.open_graph?.page_title;
   const defaultDescription = oldData?.description || seo?.open_graph?.page_description;
@@ -81,18 +79,26 @@
     arrRibbonImage: listArrRibbon,
     slug: seo?.slug
   });
-  const dataListItemsJsonLd = getListItemsJsonLD(listArrRibbon);
-  const dataJsonLd = getJsonLD({ content, contentEpisode, isSeaSon, seo });
-  const dataSnippet =
-    !!seo?.snippet && getJsonLD({ content, contentEpisode, isSeaSon, snippet: seo?.snippet, seo });
-
-  const dataSnippetDefault = parseSnippetJsonLd({ data: seo_config });
+  const dataSnippet = seo?.snippet;
+  const matches = [
+    ...dataSnippet.matchAll(/<script[^>]*type="application\/ld\+json"[^>]*>([\s\S]*?)<\/script>/gi)
+  ];
+  const jsonSnippets = matches.map((m) => m[1].trim());
   return (
     <>
       <Head>
         <title>{defaultTitleSEOTag}</title>
         {defaultImageURL.map((e) => e)}
-        {dataListItemsJsonLd}
+        {/* {dataListItemsJsonLd} */}
+        {jsonSnippets.map((json, index) => (
+          <script
+            key={index}
+            async
+            defer
+            type="application/ld+json"
+            dangerouslySetInnerHTML={{ __html: json }}
+          />
+        ))}
       </Head>
       <NextSeo
         noindex={!!isNoIndexRobots}
@@ -118,10 +124,7 @@
       />
       {/* Schema LD JSON */}
       {dataBreadcrumbs}
-      {dataSnippetDefault}
       {dataLocalBusinessJsonLd}
-      {dataJsonLd}
-      {dataSnippet}
     </>
   );
 });
@@ -145,53 +148,15 @@
   }
   return data;
 };
-const getListItemsJsonLD = (listArrRibbon: any) => {
-  if (!listArrRibbon) return '';
-  const listItems: any = [];
-  listArrRibbon.map((item: any, index: any) =>
-    listItems.push({
-      '@type': 'ListItem',
-      position: index + 1,
-      url: DOMAIN_WEB + item.url
-    })
-  );
-  const data = {
-    '@context': 'http://schema.org',
-    '@type': 'ItemList',
-    itemListElement: listItems
-  };
-  const jsonData = data ? JSON.stringify(data) : '';
 
-  return (
-    <script async defer type="application/ld+json" dangerouslySetInnerHTML={{ __html: jsonData }} />
-  );
-};
 const parseSnippetJsonLd = ({ data }: any) => {
   if (data) {
     const jsonDataParsed = data.toString() || '';
     return <div dangerouslySetInnerHTML={{ __html: jsonDataParsed }} />;
   }
-  return null;
+  return;
 };
-const getJsonLD = ({ content, contentEpisode, isSeaSon, snippet, seo }: any) => {
-  if (snippet) return parseSnippetJsonLd({ data: snippet });
-  if (!content) return;
-  const vodData = getSeoData(content, contentEpisode, isSeaSon, seo);
-  const jsonVod = vodData ? JSON.stringify(vodData) : '';
 
-  return (
-    <>
-      {jsonVod && (
-        <script
-          async
-          defer
-          type="application/ld+json"
-          dangerouslySetInnerHTML={{ __html: jsonVod }}
-        />
-      )}
-    </>
-  );
-};
 const getLocalBusinessJsonLd = ({ arrRibbonImage, slug }: any) => {
   if (!arrRibbonImage || slug !== PAGE.HOME_DEFAULT) return;
   const dataImageDefault = [
Index: src/containers/LiveStream/StreamContainer.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/containers/LiveStream/StreamContainer.tsx b/src/containers/LiveStream/StreamContainer.tsx
--- a/src/containers/LiveStream/StreamContainer.tsx	(revision ff178b3ef848f6ede706df286c8fc1e423d89044)
+++ b/src/containers/LiveStream/StreamContainer.tsx	(date 1750043670361)
@@ -2,17 +2,17 @@
 import { setLoadedData, setToast } from '@actions/app';
 import { checkStatusLiveStream, getEventRelated } from '@actions/livestream';
 import {
-  getSEOAllPage,
-  getDataLivestreamEventsById,
   getDataLivestreamEvents,
+  getDataLivestreamEventsById,
+  getDataRibbonsId,
   getPageRibbons,
-  getDataRibbonsId
+  getSEOAllPage
 } from '@actions/page';
 import { getPopupTriggerDialog, openPopup } from '@actions/popup';
 import {
+  getUserNotifyComingSoon,
   getUserSubcribeNotifyComingSoon,
-  getUserUnSubcribeNotifyComingSoon,
-  getUserNotifyComingSoon
+  getUserUnSubcribeNotifyComingSoon
 } from '@actions/user';
 import { useDispatch, useSelector } from 'react-redux';
 import {
@@ -43,7 +43,7 @@
 import LiveStream from '@components/livestream/LiveStream';
 import { moEngageEvent } from '@tracking/TrackingMoEngage';
 import ListRibbons from '@components/Sections/ListRibbons';
-import SeoAllPage from '@components/seo/SeoAllPage';
+import SeoAllPage, { redirectTool } from '@components/seo/SeoAllPage';
 import ContentNotFound from '@components/notfound/ContentNotFound';
 import { MOE_NAME, MOE_PROPERTY } from '@config/ConfigMoEnage';
 import { TEXT } from '@constants/text';
@@ -893,7 +893,7 @@
       });
     }
     // get data SEO
-    await store.dispatch(
+    const resp: any = await store.dispatch(
       getSEOAllPage({
         slug: req.path,
         keyBreadcrumbs,
@@ -903,6 +903,7 @@
         origin
       })
     );
+    redirectTool({ redirect: resp?.data?.redirect, res });
     return {
       renderNotFoundPage,
       ssr: true
