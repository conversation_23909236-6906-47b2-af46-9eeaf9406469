/**
 * SocketCluster JavaScript client v16.0.1
 */
(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.socketClusterClient = f()}})(function(){var define,module,exports;return (function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(require,module,exports){
    (function (global){
      function AuthEngine() {
        this._internalStorage = {};
        this.isLocalStorageEnabled = this._checkLocalStorageEnabled();
      }

      AuthEngine.prototype._checkLocalStorageEnabled = function () {
        let err;
        try {
          // Some browsers will throw an error here if localStorage is disabled.
          global.localStorage;

          // Safari, in Private Browsing Mode, looks like it supports localStorage but all calls to setItem
          // throw QuotaExceededError. We're going to detect this and avoid hard to debug edge cases.
          global.localStorage.setItem('__scLocalStorageTest', 1);
          global.localStorage.removeItem('__scLocalStorageTest');
        } catch (e) {
          err = e;
        }
        return !err;
      };

      AuthEngine.prototype.saveToken = function (name, token, options) {
        if (this.isLocalStorageEnabled && global.localStorage) {
          global.localStorage.setItem(name, token);
        } else {
          this._internalStorage[name] = token;
        }
        return Promise.resolve(token);
      };

      AuthEngine.prototype.removeToken = function (name) {
        let loadPromise = this.loadToken(name);

        if (this.isLocalStorageEnabled && global.localStorage) {
          global.localStorage.removeItem(name);
        } else {
          delete this._internalStorage[name];
        }

        return loadPromise;
      };

      AuthEngine.prototype.loadToken = function (name) {
        let token;

        if (this.isLocalStorageEnabled && global.localStorage) {
          token = global.localStorage.getItem(name);
        } else {
          token = this._internalStorage[name] || null;
        }

        return Promise.resolve(token);
      };

      module.exports = AuthEngine;

    }).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})
  },{}],2:[function(require,module,exports){
    (function (global){
      const StreamDemux = require('stream-demux');
      const AsyncStreamEmitter = require('async-stream-emitter');
      const AGChannel = require('ag-channel');
      const AuthEngine = require('./auth');
      const formatter = require('sc-formatter');
      const AGTransport = require('./transport');
      const querystring = require('querystring');
      const LinkedList = require('linked-list');
      const cloneDeep = require('clone-deep');
      const Buffer = require('buffer/').Buffer;
      const wait = require('./wait');

      const scErrors = require('sc-errors');
      const InvalidArgumentsError = scErrors.InvalidArgumentsError;
      const InvalidMessageError = scErrors.InvalidMessageError;
      const InvalidActionError = scErrors.InvalidActionError;
      const SocketProtocolError = scErrors.SocketProtocolError;
      const TimeoutError = scErrors.TimeoutError;
      const BadConnectionError = scErrors.BadConnectionError;

      const isBrowser = typeof window !== 'undefined';

      function AGClientSocket(socketOptions) {
        AsyncStreamEmitter.call(this);

        let defaultOptions = {
          path: '/socketcluster/',
          secure: false,
          protocolScheme: null,
          socketPath: null,
          autoConnect: true,
          autoReconnect: true,
          autoSubscribeOnConnect: true,
          connectTimeout: 20000,
          ackTimeout: 10000,
          timestampRequests: false,
          timestampParam: 't',
          authTokenName: 'socketcluster.authToken',
          binaryType: 'arraybuffer',
          batchOnHandshake: false,
          batchOnHandshakeDuration: 100,
          batchInterval: 50,
          protocolVersion: 2,
          wsOptions: {},
          cloneData: false
        };
        let opts = Object.assign(defaultOptions, socketOptions);

        this.id = null;
        this.version = opts.version || null;
        this.protocolVersion = opts.protocolVersion;
        this.state = this.CLOSED;
        this.authState = this.UNAUTHENTICATED;
        this.signedAuthToken = null;
        this.authToken = null;
        this.pendingReconnect = false;
        this.pendingReconnectTimeout = null;
        this.preparingPendingSubscriptions = false;
        this.clientId = opts.clientId;
        this.wsOptions = opts.wsOptions;

        this.connectTimeout = opts.connectTimeout;
        this.ackTimeout = opts.ackTimeout;
        this.channelPrefix = opts.channelPrefix || null;
        this.disconnectOnUnload = opts.disconnectOnUnload == null ? true : opts.disconnectOnUnload;
        this.authTokenName = opts.authTokenName;

        // pingTimeout will be connectTimeout at the start, but it will
        // be updated with values provided by the 'connect' event
        opts.pingTimeout = opts.connectTimeout;
        this.pingTimeout = opts.pingTimeout;
        this.pingTimeoutDisabled = !!opts.pingTimeoutDisabled;

        let maxTimeout = Math.pow(2, 31) - 1;

        let verifyDuration = (propertyName) => {
          if (this[propertyName] > maxTimeout) {
            throw new InvalidArgumentsError(
              `The ${propertyName} value provided exceeded the maximum amount allowed`
            );
          }
        };

        verifyDuration('connectTimeout');
        verifyDuration('ackTimeout');
        verifyDuration('pingTimeout');

        this.connectAttempts = 0;

        this.isBatching = false;
        this.batchOnHandshake = opts.batchOnHandshake;
        this.batchOnHandshakeDuration = opts.batchOnHandshakeDuration;

        this._batchingIntervalId = null;
        this._outboundBuffer = new LinkedList();
        this._channelMap = {};

        this._channelEventDemux = new StreamDemux();
        this._channelDataDemux = new StreamDemux();

        this._receiverDemux = new StreamDemux();
        this._procedureDemux = new StreamDemux();

        this.options = opts;

        this._cid = 1;

        this.options.callIdGenerator = () => {
          return this._cid++;
        };

        if (this.options.autoReconnect) {
          if (this.options.autoReconnectOptions == null) {
            this.options.autoReconnectOptions = {};
          }

          // Add properties to the this.options.autoReconnectOptions object.
          // We assign the reference to a reconnectOptions variable to avoid repetition.
          let reconnectOptions = this.options.autoReconnectOptions;
          if (reconnectOptions.initialDelay == null) {
            reconnectOptions.initialDelay = 10000;
          }
          if (reconnectOptions.randomness == null) {
            reconnectOptions.randomness = 10000;
          }
          if (reconnectOptions.multiplier == null) {
            reconnectOptions.multiplier = 1.5;
          }
          if (reconnectOptions.maxDelay == null) {
            reconnectOptions.maxDelay = 60000;
          }
        }

        if (this.options.subscriptionRetryOptions == null) {
          this.options.subscriptionRetryOptions = {};
        }

        if (this.options.authEngine) {
          this.auth = this.options.authEngine;
        } else {
          this.auth = new AuthEngine();
        }

        if (this.options.codecEngine) {
          this.codec = this.options.codecEngine;
        } else {
          // Default codec engine
          this.codec = formatter;
        }

        if (this.options.protocol) {
          let protocolOptionError = new InvalidArgumentsError(
            'The "protocol" option does not affect socketcluster-client - ' +
            'If you want to utilize SSL/TLS, use "secure" option instead'
          );
          this._onError(protocolOptionError);
        }

        this.options.query = opts.query || {};
        if (typeof this.options.query === 'string') {
          this.options.query = querystring.parse(this.options.query);
        }

        if (isBrowser && this.disconnectOnUnload && global.addEventListener && global.removeEventListener) {
          this._handleBrowserUnload();
        }

        if (this.options.autoConnect) {
          this.connect();
        }
      }

      AGClientSocket.prototype = Object.create(AsyncStreamEmitter.prototype);

      AGClientSocket.CONNECTING = AGClientSocket.prototype.CONNECTING = AGTransport.prototype.CONNECTING;
      AGClientSocket.OPEN = AGClientSocket.prototype.OPEN = AGTransport.prototype.OPEN;
      AGClientSocket.CLOSED = AGClientSocket.prototype.CLOSED = AGTransport.prototype.CLOSED;

      AGClientSocket.AUTHENTICATED = AGClientSocket.prototype.AUTHENTICATED = 'authenticated';
      AGClientSocket.UNAUTHENTICATED = AGClientSocket.prototype.UNAUTHENTICATED = 'unauthenticated';

      AGClientSocket.SUBSCRIBED = AGClientSocket.prototype.SUBSCRIBED = AGChannel.SUBSCRIBED;
      AGClientSocket.PENDING = AGClientSocket.prototype.PENDING = AGChannel.PENDING;
      AGClientSocket.UNSUBSCRIBED = AGClientSocket.prototype.UNSUBSCRIBED = AGChannel.UNSUBSCRIBED;

      AGClientSocket.ignoreStatuses = scErrors.socketProtocolIgnoreStatuses;
      AGClientSocket.errorStatuses = scErrors.socketProtocolErrorStatuses;

      Object.defineProperty(AGClientSocket.prototype, 'isBufferingBatch', {
        get: function () {
          return this.transport.isBufferingBatch;
        }
      });

      AGClientSocket.prototype.getBackpressure = function () {
        return Math.max(
          this.getAllListenersBackpressure(),
          this.getAllReceiversBackpressure(),
          this.getAllProceduresBackpressure(),
          this.getAllChannelsBackpressure()
        );
      };

      AGClientSocket.prototype._handleBrowserUnload = async function () {
        let unloadHandler = () => {
          this.disconnect();
        };
        let isUnloadHandlerAttached = false;

        let attachUnloadHandler = () => {
          if (!isUnloadHandlerAttached) {
            isUnloadHandlerAttached = true;
            global.addEventListener('beforeunload', unloadHandler, false);
          }
        };

        let detachUnloadHandler = () => {
          if (isUnloadHandlerAttached) {
            isUnloadHandlerAttached = false;
            global.removeEventListener('beforeunload', unloadHandler, false);
          }
        };

        (async () => {
          let consumer = this.listener('connecting').createConsumer();
          while (true) {
            let packet = await consumer.next();
            if (packet.done) break;
            attachUnloadHandler();
          }
        })();

        (async () => {
          let consumer = this.listener('close').createConsumer();
          while (true) {
            let packet = await consumer.next();
            if (packet.done) break;
            detachUnloadHandler();
          }
        })();
      };

      AGClientSocket.prototype._setAuthToken = function (data) {
        this._changeToAuthenticatedState(data.token);

        (async () => {
          try {
            await this.auth.saveToken(this.authTokenName, data.token, {});
          } catch (err) {
            this._onError(err);
          }
        })();
      };

      AGClientSocket.prototype._removeAuthToken = function (data) {
        (async () => {
          let oldAuthToken;
          try {
            oldAuthToken = await this.auth.removeToken(this.authTokenName);
          } catch (err) {
            // Non-fatal error - Do not close the connection
            this._onError(err);
            return;
          }
          this.emit('removeAuthToken', {oldAuthToken});
        })();

        this._changeToUnauthenticatedStateAndClearTokens();
      };

      AGClientSocket.prototype._privateDataHandlerMap = {
        '#publish': function (data) {
          let undecoratedChannelName = this._undecorateChannelName(data.channel);
          let isSubscribed = this.isSubscribed(undecoratedChannelName, true);

          if (isSubscribed) {
            this._channelDataDemux.write(undecoratedChannelName, data.data);
          }
        },
        '#kickOut': function (data) {
          let undecoratedChannelName = this._undecorateChannelName(data.channel);
          let channel = this._channelMap[undecoratedChannelName];
          if (channel) {
            this.emit('kickOut', {
              channel: undecoratedChannelName,
              message: data.message
            });
            this._channelEventDemux.write(`${undecoratedChannelName}/kickOut`, {message: data.message});
            this._triggerChannelUnsubscribe(channel);
          }
        },
        '#setAuthToken': function (data) {
          if (data) {
            this._setAuthToken(data);
          }
        },
        '#removeAuthToken': function (data) {
          this._removeAuthToken(data);
        }
      };

      AGClientSocket.prototype._privateRPCHandlerMap = {
        '#setAuthToken': function (data, request) {
          if (data) {
            this._setAuthToken(data);

            request.end();
          } else {
            request.error(new InvalidMessageError('No token data provided by #setAuthToken event'));
          }
        },
        '#removeAuthToken': function (data, request) {
          this._removeAuthToken(data);
          request.end();
        }
      };

      AGClientSocket.prototype.getState = function () {
        return this.state;
      };

      AGClientSocket.prototype.getBytesReceived = function () {
        return this.transport.getBytesReceived();
      };

      AGClientSocket.prototype.deauthenticate = async function () {
        (async () => {
          let oldAuthToken;
          try {
            oldAuthToken = await this.auth.removeToken(this.authTokenName);
          } catch (err) {
            this._onError(err);
            return;
          }
          this.emit('removeAuthToken', {oldAuthToken});
        })();

        if (this.state !== this.CLOSED) {
          this.transmit('#removeAuthToken');
        }
        this._changeToUnauthenticatedStateAndClearTokens();
        await wait(0);
      };

      AGClientSocket.prototype.connect = function () {
        if (this.state === this.CLOSED) {
          this.pendingReconnect = false;
          this.pendingReconnectTimeout = null;
          clearTimeout(this._reconnectTimeoutRef);

          this.state = this.CONNECTING;
          this.emit('connecting', {});

          if (this.transport) {
            this.transport.clearAllListeners();
          }

          let transportHandlers = {
            onOpen: (value) => {
              this.state = this.OPEN;
              this._onOpen(value);
            },
            onOpenAbort: (value) => {
              if (this.state !== this.CLOSED) {
                this.state = this.CLOSED;
                this._destroy(value.code, value.reason, true);
              }
            },
            onClose: (value) => {
              if (this.state !== this.CLOSED) {
                this.state = this.CLOSED;
                this._destroy(value.code, value.reason);
              }
            },
            onEvent: (value) => {
              this.emit(value.event, value.data);
            },
            onError: (value) => {
              this._onError(value.error);
            },
            onInboundInvoke: (value) => {
              this._onInboundInvoke(value);
            },
            onInboundTransmit: (value) => {
              this._onInboundTransmit(value.event, value.data);
            }
          };

          this.transport = new AGTransport(this.auth, this.codec, this.options, this.wsOptions, transportHandlers);
        }
      };

      AGClientSocket.prototype.reconnect = function (code, reason) {
        this.disconnect(code, reason);
        this.connect();
      };

      AGClientSocket.prototype.disconnect = function (code, reason) {
        code = code || 1000;

        if (typeof code !== 'number') {
          throw new InvalidArgumentsError('If specified, the code argument must be a number');
        }

        let isConnecting = this.state === this.CONNECTING;
        if (isConnecting || this.state === this.OPEN) {
          this.state = this.CLOSED;
          this._destroy(code, reason, isConnecting);
          this.transport.close(code, reason);
        } else {
          this.pendingReconnect = false;
          this.pendingReconnectTimeout = null;
          clearTimeout(this._reconnectTimeoutRef);
        }
      };

      AGClientSocket.prototype._changeToUnauthenticatedStateAndClearTokens = function () {
        if (this.authState !== this.UNAUTHENTICATED) {
          let oldAuthState = this.authState;
          let oldAuthToken = this.authToken;
          let oldSignedAuthToken = this.signedAuthToken;
          this.authState = this.UNAUTHENTICATED;
          this.signedAuthToken = null;
          this.authToken = null;

          let stateChangeData = {
            oldAuthState,
            newAuthState: this.authState
          };
          this.emit('authStateChange', stateChangeData);
          this.emit('deauthenticate', {oldSignedAuthToken, oldAuthToken});
        }
      };

      AGClientSocket.prototype._changeToAuthenticatedState = function (signedAuthToken) {
        this.signedAuthToken = signedAuthToken;
        this.authToken = this._extractAuthTokenData(signedAuthToken);

        if (this.authState !== this.AUTHENTICATED) {
          let oldAuthState = this.authState;
          this.authState = this.AUTHENTICATED;
          let stateChangeData = {
            oldAuthState,
            newAuthState: this.authState,
            signedAuthToken: signedAuthToken,
            authToken: this.authToken
          };
          if (!this.preparingPendingSubscriptions) {
            this.processPendingSubscriptions();
          }

          this.emit('authStateChange', stateChangeData);
        }
        this.emit('authenticate', {signedAuthToken, authToken: this.authToken});
      };

      AGClientSocket.prototype.decodeBase64 = function (encodedString) {
        return Buffer.from(encodedString, 'base64').toString('utf8');
      };

      AGClientSocket.prototype.encodeBase64 = function (decodedString) {
        return Buffer.from(decodedString, 'utf8').toString('base64');
      };

      AGClientSocket.prototype._extractAuthTokenData = function (signedAuthToken) {
        let tokenParts = (signedAuthToken || '').split('.');
        let encodedTokenData = tokenParts[1];
        if (encodedTokenData != null) {
          let tokenData = encodedTokenData;
          try {
            tokenData = this.decodeBase64(tokenData);
            return JSON.parse(tokenData);
          } catch (e) {
            return tokenData;
          }
        }
        return null;
      };

      AGClientSocket.prototype.getAuthToken = function () {
        return this.authToken;
      };

      AGClientSocket.prototype.getSignedAuthToken = function () {
        return this.signedAuthToken;
      };

// Perform client-initiated authentication by providing an encrypted token string.
      AGClientSocket.prototype.authenticate = async function (signedAuthToken) {
        let authStatus;

        try {
          authStatus = await this.invoke('#authenticate', signedAuthToken);
        } catch (err) {
          if (err.name !== 'BadConnectionError' && err.name !== 'TimeoutError') {
            // In case of a bad/closed connection or a timeout, we maintain the last
            // known auth state since those errors don't mean that the token is invalid.
            this._changeToUnauthenticatedStateAndClearTokens();
          }
          await wait(0);
          throw err;
        }

        if (authStatus && authStatus.isAuthenticated != null) {
          // If authStatus is correctly formatted (has an isAuthenticated property),
          // then we will rehydrate the authError.
          if (authStatus.authError) {
            authStatus.authError = scErrors.hydrateError(authStatus.authError);
          }
        } else {
          // Some errors like BadConnectionError and TimeoutError will not pass a valid
          // authStatus object to the current function, so we need to create it ourselves.
          authStatus = {
            isAuthenticated: this.authState,
            authError: null
          };
        }

        if (authStatus.isAuthenticated) {
          this._changeToAuthenticatedState(signedAuthToken);
        } else {
          this._changeToUnauthenticatedStateAndClearTokens();
        }

        (async () => {
          try {
            await this.auth.saveToken(this.authTokenName, signedAuthToken, {});
          } catch (err) {
            this._onError(err);
          }
        })();

        await wait(0);
        return authStatus;
      };

      AGClientSocket.prototype._tryReconnect = function (initialDelay) {
        let exponent = this.connectAttempts++;
        let reconnectOptions = this.options.autoReconnectOptions;
        let timeout;

        if (initialDelay == null || exponent > 0) {
          let initialTimeout = Math.round(reconnectOptions.initialDelay + (reconnectOptions.randomness || 0) * Math.random());

          timeout = Math.round(initialTimeout * Math.pow(reconnectOptions.multiplier, exponent));
        } else {
          timeout = initialDelay;
        }

        if (timeout > reconnectOptions.maxDelay) {
          timeout = reconnectOptions.maxDelay;
        }

        clearTimeout(this._reconnectTimeoutRef);

        this.pendingReconnect = true;
        this.pendingReconnectTimeout = timeout;
        this._reconnectTimeoutRef = setTimeout(() => {
          this.connect();
        }, timeout);
      };

      AGClientSocket.prototype._onOpen = function (status) {
        if (this.isBatching) {
          this._startBatching();
        } else if (this.batchOnHandshake) {
          this._startBatching();
          setTimeout(() => {
            if (!this.isBatching) {
              this._stopBatching();
            }
          }, this.batchOnHandshakeDuration);
        }
        this.preparingPendingSubscriptions = true;

        if (status) {
          this.id = status.id;
          this.pingTimeout = status.pingTimeout;
          if (status.isAuthenticated) {
            this._changeToAuthenticatedState(status.authToken);
          } else {
            this._changeToUnauthenticatedStateAndClearTokens();
          }
        } else {
          // This can happen if auth.loadToken (in transport.js) fails with
          // an error - This means that the signedAuthToken cannot be loaded by
          // the auth engine and therefore, we need to unauthenticate the client.
          this._changeToUnauthenticatedStateAndClearTokens();
        }

        this.connectAttempts = 0;

        if (this.options.autoSubscribeOnConnect) {
          this.processPendingSubscriptions();
        }

        // If the user invokes the callback while in autoSubscribeOnConnect mode, it
        // won't break anything.
        this.emit('connect', {
          ...status,
          processPendingSubscriptions: () => {
            this.processPendingSubscriptions();
          }
        });

        if (this.state === this.OPEN) {
          this._flushOutboundBuffer();
        }
      };

      AGClientSocket.prototype._onError = function (error) {
        this.emit('error', {error});
      };

      AGClientSocket.prototype._suspendSubscriptions = function () {
        Object.keys(this._channelMap).forEach((channelName) => {
          let channel = this._channelMap[channelName];
          this._triggerChannelUnsubscribe(channel, true);
        });
      };

      AGClientSocket.prototype._abortAllPendingEventsDueToBadConnection = function (failureType) {
        let currentNode = this._outboundBuffer.head;
        let nextNode;

        while (currentNode) {
          nextNode = currentNode.next;
          let eventObject = currentNode.data;
          clearTimeout(eventObject.timeout);
          delete eventObject.timeout;
          currentNode.detach();
          currentNode = nextNode;

          let callback = eventObject.callback;

          if (callback) {
            delete eventObject.callback;
            let errorMessage = `Event "${eventObject.event}" was aborted due to a bad connection`;
            let error = new BadConnectionError(errorMessage, failureType);

            callback.call(eventObject, error, eventObject);
          }
          // Cleanup any pending response callback in the transport layer too.
          if (eventObject.cid) {
            this.transport.cancelPendingResponse(eventObject.cid);
          }
        }
      };

      AGClientSocket.prototype._destroy = function (code, reason, openAbort) {
        this.id = null;
        this._cancelBatching();

        if (this.transport) {
          this.transport.clearAllListeners();
        }

        this.pendingReconnect = false;
        this.pendingReconnectTimeout = null;
        clearTimeout(this._reconnectTimeoutRef);

        this._suspendSubscriptions();

        if (openAbort) {
          this.emit('connectAbort', {code, reason});
        } else {
          this.emit('disconnect', {code, reason});
        }
        this.emit('close', {code, reason});

        if (!AGClientSocket.ignoreStatuses[code]) {
          let closeMessage;
          if (reason) {
            closeMessage = 'Socket connection closed with status code ' + code + ' and reason: ' + reason;
          } else {
            closeMessage = 'Socket connection closed with status code ' + code;
          }
          let err = new SocketProtocolError(AGClientSocket.errorStatuses[code] || closeMessage, code);
          this._onError(err);
        }

        this._abortAllPendingEventsDueToBadConnection(openAbort ? 'connectAbort' : 'disconnect');

        // Try to reconnect
        // on server ping timeout (4000)
        // or on client pong timeout (4001)
        // or on close without status (1005)
        // or on handshake failure (4003)
        // or on handshake rejection (4008)
        // or on socket hung up (1006)
        if (this.options.autoReconnect) {
          if (code === 4000 || code === 4001 || code === 1005) {
            // If there is a ping or pong timeout or socket closes without
            // status, don't wait before trying to reconnect - These could happen
            // if the client wakes up after a period of inactivity and in this case we
            // want to re-establish the connection as soon as possible.
            this._tryReconnect(0);

            // Codes 4500 and above will be treated as permanent disconnects.
            // Socket will not try to auto-reconnect.
          } else if (code !== 1000 && code < 4500) {
            this._tryReconnect();
          }
        }
      };

      AGClientSocket.prototype._onInboundTransmit = function (event, data) {
        let handler = this._privateDataHandlerMap[event];
        if (handler) {
          handler.call(this, data);
        } else {
          this._receiverDemux.write(event, data);
        }
      };

      AGClientSocket.prototype._onInboundInvoke = function (request) {
        let {procedure, data} = request;
        let handler = this._privateRPCHandlerMap[procedure];
        if (handler) {
          handler.call(this, data, request);
        } else {
          this._procedureDemux.write(procedure, request);
        }
      };

      AGClientSocket.prototype.decode = function (message) {
        return this.transport.decode(message);
      };

      AGClientSocket.prototype.encode = function (object) {
        return this.transport.encode(object);
      };

      AGClientSocket.prototype._flushOutboundBuffer = function () {
        let currentNode = this._outboundBuffer.head;
        let nextNode;

        while (currentNode) {
          nextNode = currentNode.next;
          let eventObject = currentNode.data;
          currentNode.detach();
          this.transport.transmitObject(eventObject);
          currentNode = nextNode;
        }
      };

      AGClientSocket.prototype._handleEventAckTimeout = function (eventObject, eventNode) {
        if (eventNode) {
          eventNode.detach();
        }
        delete eventObject.timeout;

        let callback = eventObject.callback;
        if (callback) {
          delete eventObject.callback;
          let error = new TimeoutError(`Event response for "${eventObject.event}" timed out`);
          callback.call(eventObject, error, eventObject);
        }
        // Cleanup any pending response callback in the transport layer too.
        if (eventObject.cid) {
          this.transport.cancelPendingResponse(eventObject.cid);
        }
      };

      AGClientSocket.prototype._processOutboundEvent = function (event, data, options, expectResponse) {
        options = options || {};

        if (this.state === this.CLOSED) {
          this.connect();
        }
        let eventObject = {
          event
        };

        let promise;

        if (expectResponse) {
          promise = new Promise((resolve, reject) => {
            eventObject.callback = (err, data) => {
              if (err) {
                reject(err);
                return;
              }
              resolve(data);
            };
          });
        } else {
          promise = Promise.resolve();
        }

        let eventNode = new LinkedList.Item();

        if (this.options.cloneData) {
          eventObject.data = cloneDeep(data);
        } else {
          eventObject.data = data;
        }
        eventNode.data = eventObject;

        let ackTimeout = options.ackTimeout == null ? this.ackTimeout : options.ackTimeout;

        eventObject.timeout = setTimeout(() => {
          this._handleEventAckTimeout(eventObject, eventNode);
        }, ackTimeout);

        this._outboundBuffer.append(eventNode);
        if (this.state === this.OPEN) {
          this._flushOutboundBuffer();
        }
        return promise;
      };

      AGClientSocket.prototype.send = function (data) {
        this.transport.send(data);
      };

      AGClientSocket.prototype.transmit = function (event, data, options) {
        return this._processOutboundEvent(event, data, options);
      };

      AGClientSocket.prototype.invoke = function (event, data, options) {
        return this._processOutboundEvent(event, data, options, true);
      };

      AGClientSocket.prototype.transmitPublish = function (channelName, data) {
        let pubData = {
          channel: this._decorateChannelName(channelName),
          data
        };
        return this.transmit('#publish', pubData);
      };

      AGClientSocket.prototype.invokePublish = function (channelName, data) {
        let pubData = {
          channel: this._decorateChannelName(channelName),
          data
        };
        return this.invoke('#publish', pubData);
      };

      AGClientSocket.prototype._triggerChannelSubscribe = function (channel, subscriptionOptions) {
        let channelName = channel.name;

        if (channel.state !== AGChannel.SUBSCRIBED) {
          let oldChannelState = channel.state;
          channel.state = AGChannel.SUBSCRIBED;

          let stateChangeData = {
            oldChannelState,
            newChannelState: channel.state,
            subscriptionOptions
          };
          this._channelEventDemux.write(`${channelName}/subscribeStateChange`, stateChangeData);
          this._channelEventDemux.write(`${channelName}/subscribe`, {
            subscriptionOptions
          });
          this.emit('subscribeStateChange', {
            channel: channelName,
            ...stateChangeData
          });
          this.emit('subscribe', {
            channel: channelName,
            subscriptionOptions
          });
        }
      };

      AGClientSocket.prototype._triggerChannelSubscribeFail = function (err, channel, subscriptionOptions) {
        let channelName = channel.name;
        let meetsAuthRequirements = !channel.options.waitForAuth || this.authState === this.AUTHENTICATED;
        let hasChannel = !!this._channelMap[channelName];

        if (hasChannel && meetsAuthRequirements) {
          delete this._channelMap[channelName];

          this._channelEventDemux.write(`${channelName}/subscribeFail`, {
            error: err,
            subscriptionOptions
          });
          this.emit('subscribeFail', {
            error: err,
            channel: channelName,
            subscriptionOptions: subscriptionOptions
          });
        }
      };

// Cancel any pending subscribe callback
      AGClientSocket.prototype._cancelPendingSubscribeCallback = function (channel) {
        if (channel._pendingSubscriptionCid != null) {
          this.transport.cancelPendingResponse(channel._pendingSubscriptionCid);
          delete channel._pendingSubscriptionCid;
        }
      };

      AGClientSocket.prototype._decorateChannelName = function (channelName) {
        if (this.channelPrefix) {
          channelName = this.channelPrefix + channelName;
        }
        return channelName;
      };

      AGClientSocket.prototype._undecorateChannelName = function (decoratedChannelName) {
        if (this.channelPrefix && decoratedChannelName.indexOf(this.channelPrefix) === 0) {
          return decoratedChannelName.replace(this.channelPrefix, '');
        }
        return decoratedChannelName;
      };

      AGClientSocket.prototype.startBatch = function () {
        this.transport.startBatch();
      };

      AGClientSocket.prototype.flushBatch = function () {
        this.transport.flushBatch();
      };

      AGClientSocket.prototype.cancelBatch = function () {
        this.transport.cancelBatch();
      };

      AGClientSocket.prototype._startBatching = function () {
        if (this._batchingIntervalId != null) {
          return;
        }
        this.startBatch();
        this._batchingIntervalId = setInterval(() => {
          this.flushBatch();
          this.startBatch();
        }, this.options.batchInterval);
      };

      AGClientSocket.prototype.startBatching = function () {
        this.isBatching = true;
        this._startBatching();
      };

      AGClientSocket.prototype._stopBatching = function () {
        if (this._batchingIntervalId != null) {
          clearInterval(this._batchingIntervalId);
        }
        this._batchingIntervalId = null;
        this.flushBatch();
      };

      AGClientSocket.prototype.stopBatching = function () {
        this.isBatching = false;
        this._stopBatching();
      };

      AGClientSocket.prototype._cancelBatching = function () {
        if (this._batchingIntervalId != null) {
          clearInterval(this._batchingIntervalId);
        }
        this._batchingIntervalId = null;
        this.cancelBatch();
      };

      AGClientSocket.prototype.cancelBatching = function () {
        this.isBatching = false;
        this._cancelBatching();
      };

      AGClientSocket.prototype._trySubscribe = function (channel) {
        let meetsAuthRequirements = !channel.options.waitForAuth || this.authState === this.AUTHENTICATED;

        // We can only ever have one pending subscribe action at any given time on a channel
        if (
          this.state === this.OPEN &&
          !this.preparingPendingSubscriptions &&
          channel._pendingSubscriptionCid == null &&
          meetsAuthRequirements
        ) {

          let options = {
            noTimeout: true
          };

          let subscriptionOptions = {};
          if (channel.options.waitForAuth) {
            options.waitForAuth = true;
            subscriptionOptions.waitForAuth = options.waitForAuth;
          }
          if (channel.options.data) {
            subscriptionOptions.data = channel.options.data;
          }

          channel._pendingSubscriptionCid = this.transport.invokeRaw(
            '#subscribe',
            {
              channel: this._decorateChannelName(channel.name),
              ...subscriptionOptions
            },
            options,
            (err) => {
              if (err) {
                if (err.name === 'BadConnectionError') {
                  // In case of a failed connection, keep the subscription
                  // as pending; it will try again on reconnect.
                  return;
                }
                delete channel._pendingSubscriptionCid;
                this._triggerChannelSubscribeFail(err, channel, subscriptionOptions);
              } else {
                delete channel._pendingSubscriptionCid;
                this._triggerChannelSubscribe(channel, subscriptionOptions);
              }
            }
          );
          this.emit('subscribeRequest', {
            channel: channel.name,
            subscriptionOptions
          });
        }
      };

      AGClientSocket.prototype.subscribe = function (channelName, options) {
        options = options || {};
        let channel = this._channelMap[channelName];

        let sanitizedOptions = {
          waitForAuth: !!options.waitForAuth
        };

        if (options.priority != null) {
          sanitizedOptions.priority = options.priority;
        }
        if (options.data !== undefined) {
          sanitizedOptions.data = options.data;
        }

        if (!channel) {
          channel = {
            name: channelName,
            state: AGChannel.PENDING,
            options: sanitizedOptions
          };
          this._channelMap[channelName] = channel;
          this._trySubscribe(channel);
        } else if (options) {
          channel.options = sanitizedOptions;
        }

        let channelIterable = new AGChannel(
          channelName,
          this,
          this._channelEventDemux,
          this._channelDataDemux
        );

        return channelIterable;
      };

      AGClientSocket.prototype._triggerChannelUnsubscribe = function (channel, setAsPending) {
        let channelName = channel.name;

        this._cancelPendingSubscribeCallback(channel);

        if (channel.state === AGChannel.SUBSCRIBED) {
          let stateChangeData = {
            oldChannelState: channel.state,
            newChannelState: setAsPending ? AGChannel.PENDING : AGChannel.UNSUBSCRIBED
          };
          this._channelEventDemux.write(`${channelName}/subscribeStateChange`, stateChangeData);
          this._channelEventDemux.write(`${channelName}/unsubscribe`, {});
          this.emit('subscribeStateChange', {
            channel: channelName,
            ...stateChangeData
          });
          this.emit('unsubscribe', {channel: channelName});
        }

        if (setAsPending) {
          channel.state = AGChannel.PENDING;
        } else {
          delete this._channelMap[channelName];
        }
      };

      AGClientSocket.prototype._tryUnsubscribe = function (channel) {
        if (this.state === this.OPEN) {
          let options = {
            noTimeout: true
          };
          // If there is a pending subscribe action, cancel the callback
          this._cancelPendingSubscribeCallback(channel);

          // This operation cannot fail because the TCP protocol guarantees delivery
          // so long as the connection remains open. If the connection closes,
          // the server will automatically unsubscribe the client and thus complete
          // the operation on the server side.
          let decoratedChannelName = this._decorateChannelName(channel.name);
          this.transport.transmit('#unsubscribe', decoratedChannelName, options);
        }
      };

      AGClientSocket.prototype.unsubscribe = function (channelName) {
        let channel = this._channelMap[channelName];

        if (channel) {
          this._triggerChannelUnsubscribe(channel);
          this._tryUnsubscribe(channel);
        }
      };

// ---- Receiver logic ----

      AGClientSocket.prototype.receiver = function (receiverName) {
        return this._receiverDemux.stream(receiverName);
      };

      AGClientSocket.prototype.closeReceiver = function (receiverName) {
        this._receiverDemux.close(receiverName);
      };

      AGClientSocket.prototype.closeAllReceivers = function () {
        this._receiverDemux.closeAll();
      };

      AGClientSocket.prototype.killReceiver = function (receiverName) {
        this._receiverDemux.kill(receiverName);
      };

      AGClientSocket.prototype.killAllReceivers = function () {
        this._receiverDemux.killAll();
      };

      AGClientSocket.prototype.killReceiverConsumer = function (consumerId) {
        this._receiverDemux.killConsumer(consumerId);
      };

      AGClientSocket.prototype.getReceiverConsumerStats = function (consumerId) {
        return this._receiverDemux.getConsumerStats(consumerId);
      };

      AGClientSocket.prototype.getReceiverConsumerStatsList = function (receiverName) {
        return this._receiverDemux.getConsumerStatsList(receiverName);
      };

      AGClientSocket.prototype.getAllReceiversConsumerStatsList = function () {
        return this._receiverDemux.getConsumerStatsListAll();
      };

      AGClientSocket.prototype.getReceiverBackpressure = function (receiverName) {
        return this._receiverDemux.getBackpressure(receiverName);
      };

      AGClientSocket.prototype.getAllReceiversBackpressure = function () {
        return this._receiverDemux.getBackpressureAll();
      };

      AGClientSocket.prototype.getReceiverConsumerBackpressure = function (consumerId) {
        return this._receiverDemux.getConsumerBackpressure(consumerId);
      };

      AGClientSocket.prototype.hasReceiverConsumer = function (receiverName, consumerId) {
        return this._receiverDemux.hasConsumer(receiverName, consumerId);
      };

      AGClientSocket.prototype.hasAnyReceiverConsumer = function (consumerId) {
        return this._receiverDemux.hasConsumerAll(consumerId);
      };

// ---- Procedure logic ----

      AGClientSocket.prototype.procedure = function (procedureName) {
        return this._procedureDemux.stream(procedureName);
      };

      AGClientSocket.prototype.closeProcedure = function (procedureName) {
        this._procedureDemux.close(procedureName);
      };

      AGClientSocket.prototype.closeAllProcedures = function () {
        this._procedureDemux.closeAll();
      };

      AGClientSocket.prototype.killProcedure = function (procedureName) {
        this._procedureDemux.kill(procedureName);
      };

      AGClientSocket.prototype.killAllProcedures = function () {
        this._procedureDemux.killAll();
      };

      AGClientSocket.prototype.killProcedureConsumer = function (consumerId) {
        this._procedureDemux.killConsumer(consumerId);
      };

      AGClientSocket.prototype.getProcedureConsumerStats = function (consumerId) {
        return this._procedureDemux.getConsumerStats(consumerId);
      };

      AGClientSocket.prototype.getProcedureConsumerStatsList = function (procedureName) {
        return this._procedureDemux.getConsumerStatsList(procedureName);
      };

      AGClientSocket.prototype.getAllProceduresConsumerStatsList = function () {
        return this._procedureDemux.getConsumerStatsListAll();
      };

      AGClientSocket.prototype.getProcedureBackpressure = function (procedureName) {
        return this._procedureDemux.getBackpressure(procedureName);
      };

      AGClientSocket.prototype.getAllProceduresBackpressure = function () {
        return this._procedureDemux.getBackpressureAll();
      };

      AGClientSocket.prototype.getProcedureConsumerBackpressure = function (consumerId) {
        return this._procedureDemux.getConsumerBackpressure(consumerId);
      };

      AGClientSocket.prototype.hasProcedureConsumer = function (procedureName, consumerId) {
        return this._procedureDemux.hasConsumer(procedureName, consumerId);
      };

      AGClientSocket.prototype.hasAnyProcedureConsumer = function (consumerId) {
        return this._procedureDemux.hasConsumerAll(consumerId);
      };

// ---- Channel logic ----

      AGClientSocket.prototype.channel = function (channelName) {
        let currentChannel = this._channelMap[channelName];

        let channelIterable = new AGChannel(
          channelName,
          this,
          this._channelEventDemux,
          this._channelDataDemux
        );

        return channelIterable;
      };

      AGClientSocket.prototype.closeChannel = function (channelName) {
        this.channelCloseOutput(channelName);
        this.channelCloseAllListeners(channelName);
      };

      AGClientSocket.prototype.closeAllChannelOutputs = function () {
        this._channelDataDemux.closeAll();
      };

      AGClientSocket.prototype.closeAllChannelListeners = function () {
        this._channelEventDemux.closeAll();
      };

      AGClientSocket.prototype.closeAllChannels = function () {
        this.closeAllChannelOutputs();
        this.closeAllChannelListeners();
      };

      AGClientSocket.prototype.killChannel = function (channelName) {
        this.channelKillOutput(channelName);
        this.channelKillAllListeners(channelName);
      };

      AGClientSocket.prototype.killAllChannelOutputs = function () {
        this._channelDataDemux.killAll();
      };

      AGClientSocket.prototype.killAllChannelListeners = function () {
        this._channelEventDemux.killAll();
      };

      AGClientSocket.prototype.killAllChannels = function () {
        this.killAllChannelOutputs();
        this.killAllChannelListeners();
      };

      AGClientSocket.prototype.killChannelOutputConsumer = function (consumerId) {
        this._channelDataDemux.killConsumer(consumerId);
      };

      AGClientSocket.prototype.killChannelListenerConsumer = function (consumerId) {
        this._channelEventDemux.killConsumer(consumerId);
      };

      AGClientSocket.prototype.getChannelOutputConsumerStats = function (consumerId) {
        return this._channelDataDemux.getConsumerStats(consumerId);
      };

      AGClientSocket.prototype.getChannelListenerConsumerStats = function (consumerId) {
        return this._channelEventDemux.getConsumerStats(consumerId);
      };

      AGClientSocket.prototype.getAllChannelOutputsConsumerStatsList = function () {
        return this._channelDataDemux.getConsumerStatsListAll();
      };

      AGClientSocket.prototype.getAllChannelListenersConsumerStatsList = function () {
        return this._channelEventDemux.getConsumerStatsListAll();
      };

      AGClientSocket.prototype.getChannelBackpressure = function (channelName) {
        return Math.max(
          this.channelGetOutputBackpressure(channelName),
          this.channelGetAllListenersBackpressure(channelName)
        );
      };

      AGClientSocket.prototype.getAllChannelOutputsBackpressure = function () {
        return this._channelDataDemux.getBackpressureAll();
      };

      AGClientSocket.prototype.getAllChannelListenersBackpressure = function () {
        return this._channelEventDemux.getBackpressureAll();
      };

      AGClientSocket.prototype.getAllChannelsBackpressure = function () {
        return Math.max(
          this.getAllChannelOutputsBackpressure(),
          this.getAllChannelListenersBackpressure()
        );
      };

      AGClientSocket.prototype.getChannelListenerConsumerBackpressure = function (consumerId) {
        return this._channelEventDemux.getConsumerBackpressure(consumerId);
      };

      AGClientSocket.prototype.getChannelOutputConsumerBackpressure = function (consumerId) {
        return this._channelDataDemux.getConsumerBackpressure(consumerId);
      };

      AGClientSocket.prototype.hasAnyChannelOutputConsumer = function (consumerId) {
        return this._channelDataDemux.hasConsumerAll(consumerId);
      };

      AGClientSocket.prototype.hasAnyChannelListenerConsumer = function (consumerId) {
        return this._channelEventDemux.hasConsumerAll(consumerId);
      };

      AGClientSocket.prototype.getChannelState = function (channelName) {
        let channel = this._channelMap[channelName];
        if (channel) {
          return channel.state;
        }
        return AGChannel.UNSUBSCRIBED;
      };

      AGClientSocket.prototype.getChannelOptions = function (channelName) {
        let channel = this._channelMap[channelName];
        if (channel) {
          return {...channel.options};
        }
        return {};
      };

      AGClientSocket.prototype._getAllChannelStreamNames = function (channelName) {
        let streamNamesLookup = this._channelEventDemux.getConsumerStatsListAll()
          .filter((stats) => {
            return stats.stream.indexOf(`${channelName}/`) === 0;
          })
          .reduce((accumulator, stats) => {
            accumulator[stats.stream] = true;
            return accumulator;
          }, {});
        return Object.keys(streamNamesLookup);
      };

      AGClientSocket.prototype.channelCloseOutput = function (channelName) {
        this._channelDataDemux.close(channelName);
      };

      AGClientSocket.prototype.channelCloseListener = function (channelName, eventName) {
        this._channelEventDemux.close(`${channelName}/${eventName}`);
      };

      AGClientSocket.prototype.channelCloseAllListeners = function (channelName) {
        let listenerStreams = this._getAllChannelStreamNames(channelName)
          .forEach((streamName) => {
            this._channelEventDemux.close(streamName);
          });
      };

      AGClientSocket.prototype.channelKillOutput = function (channelName) {
        this._channelDataDemux.kill(channelName);
      };

      AGClientSocket.prototype.channelKillListener = function (channelName, eventName) {
        this._channelEventDemux.kill(`${channelName}/${eventName}`);
      };

      AGClientSocket.prototype.channelKillAllListeners = function (channelName) {
        let listenerStreams = this._getAllChannelStreamNames(channelName)
          .forEach((streamName) => {
            this._channelEventDemux.kill(streamName);
          });
      };

      AGClientSocket.prototype.channelGetOutputConsumerStatsList = function (channelName) {
        return this._channelDataDemux.getConsumerStatsList(channelName);
      };

      AGClientSocket.prototype.channelGetListenerConsumerStatsList = function (channelName, eventName) {
        return this._channelEventDemux.getConsumerStatsList(`${channelName}/${eventName}`);
      };

      AGClientSocket.prototype.channelGetAllListenersConsumerStatsList = function (channelName) {
        return this._getAllChannelStreamNames(channelName)
          .map((streamName) => {
            return this._channelEventDemux.getConsumerStatsList(streamName);
          })
          .reduce((accumulator, statsList) => {
            statsList.forEach((stats) => {
              accumulator.push(stats);
            });
            return accumulator;
          }, []);
      };

      AGClientSocket.prototype.channelGetOutputBackpressure = function (channelName) {
        return this._channelDataDemux.getBackpressure(channelName);
      };

      AGClientSocket.prototype.channelGetListenerBackpressure = function (channelName, eventName) {
        return this._channelEventDemux.getBackpressure(`${channelName}/${eventName}`);
      };

      AGClientSocket.prototype.channelGetAllListenersBackpressure = function (channelName) {
        let listenerStreamBackpressures = this._getAllChannelStreamNames(channelName)
          .map((streamName) => {
            return this._channelEventDemux.getBackpressure(streamName);
          });
        return Math.max(...listenerStreamBackpressures.concat(0));
      };

      AGClientSocket.prototype.channelHasOutputConsumer = function (channelName, consumerId) {
        return this._channelDataDemux.hasConsumer(channelName, consumerId);
      };

      AGClientSocket.prototype.channelHasListenerConsumer = function (channelName, eventName, consumerId) {
        return this._channelEventDemux.hasConsumer(`${channelName}/${eventName}`, consumerId);
      };

      AGClientSocket.prototype.channelHasAnyListenerConsumer = function (channelName, consumerId) {
        return this._getAllChannelStreamNames(channelName)
          .some((streamName) => {
            return this._channelEventDemux.hasConsumer(streamName, consumerId);
          });
      };

      AGClientSocket.prototype.subscriptions = function (includePending) {
        let subs = [];
        Object.keys(this._channelMap).forEach((channelName) => {
          if (includePending || this._channelMap[channelName].state === AGChannel.SUBSCRIBED) {
            subs.push(channelName);
          }
        });
        return subs;
      };

      AGClientSocket.prototype.isSubscribed = function (channelName, includePending) {
        let channel = this._channelMap[channelName];
        if (includePending) {
          return !!channel;
        }
        return !!channel && channel.state === AGChannel.SUBSCRIBED;
      };

      AGClientSocket.prototype.processPendingSubscriptions = function () {
        this.preparingPendingSubscriptions = false;
        let pendingChannels = [];

        Object.keys(this._channelMap).forEach((channelName) => {
          let channel = this._channelMap[channelName];
          if (channel.state === AGChannel.PENDING) {
            pendingChannels.push(channel);
          }
        });

        pendingChannels.sort((a, b) => {
          let ap = a.options.priority || 0;
          let bp = b.options.priority || 0;
          if (ap > bp) {
            return -1;
          }
          if (ap < bp) {
            return 1;
          }
          return 0;
        });

        pendingChannels.forEach((channel) => {
          this._trySubscribe(channel);
        });
      };

      module.exports = AGClientSocket;

    }).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})
  },{"./auth":1,"./transport":4,"./wait":5,"ag-channel":7,"async-stream-emitter":9,"buffer/":11,"clone-deep":12,"linked-list":19,"querystring":22,"sc-errors":24,"sc-formatter":25,"stream-demux":28}],3:[function(require,module,exports){
    (function (global){
      const AGClientSocket = require('./clientsocket');
      const uuid = require('uuid');
      const scErrors = require('sc-errors');
      const InvalidArgumentsError = scErrors.InvalidArgumentsError;

      function isUrlSecure() {
        return global.location && location.protocol === 'https:';
      }

      function getPort(options, isSecureDefault) {
        let isSecure = options.secure == null ? isSecureDefault : options.secure;
        return options.port || (global.location && location.port ? location.port : isSecure ? 443 : 80);
      }

      function create(options) {
        options = options || {};

        if (options.host && !options.host.match(/[^:]+:\d{2,5}/)) {
          throw new InvalidArgumentsError(
            'The host option should include both' +
            ' the hostname and the port number in the format "hostname:port"'
          );
        }

        if (options.host && options.hostname) {
          throw new InvalidArgumentsError(
            'The host option should already include' +
            ' the hostname and the port number in the format "hostname:port"' +
            ' - Because of this, you should never use host and hostname options together'
          );
        }

        if (options.host && options.port) {
          throw new InvalidArgumentsError(
            'The host option should already include' +
            ' the hostname and the port number in the format "hostname:port"' +
            ' - Because of this, you should never use host and port options together'
          );
        }

        let isSecureDefault = isUrlSecure();

        let opts = {
          clientId: uuid.v4(),
          port: getPort(options, isSecureDefault),
          hostname: global.location && location.hostname || 'localhost',
          secure: isSecureDefault
        };

        Object.assign(opts, options);

        return new AGClientSocket(opts);
      }

      module.exports = {
        create
      };

    }).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})
  },{"./clientsocket":2,"sc-errors":24,"uuid":29}],4:[function(require,module,exports){
    (function (global){
      const AGRequest = require('ag-request');
      const querystring = require('querystring');

      let WebSocket;
      let createWebSocket;

      if (global.WebSocket) {
        WebSocket = global.WebSocket;
        createWebSocket = function (uri, options) {
          return new WebSocket(uri);
        };
      } else {
        WebSocket = require('ws');
        createWebSocket = function (uri, options) {
          return new WebSocket(uri, null, options);
        };
      }

      const scErrors = require('sc-errors');
      const TimeoutError = scErrors.TimeoutError;
      const BadConnectionError = scErrors.BadConnectionError;

      function AGTransport(authEngine, codecEngine, options, wsOptions, handlers) {
        this.state = this.CLOSED;
        this.auth = authEngine;
        this.codec = codecEngine;
        this.options = options;
        this.wsOptions = wsOptions;
        this.protocolVersion = options.protocolVersion;
        this.connectTimeout = options.connectTimeout;
        this.pingTimeout = options.pingTimeout;
        this.pingTimeoutDisabled = !!options.pingTimeoutDisabled;
        this.callIdGenerator = options.callIdGenerator;
        this.authTokenName = options.authTokenName;
        this.isBufferingBatch = false;

        this._pingTimeoutTicker = null;
        this._callbackMap = {};
        this._batchBuffer = [];

        if (!handlers) {
          handlers = {};
        }

        this._onOpenHandler = handlers.onOpen || function () {};
        this._onOpenAbortHandler = handlers.onOpenAbort || function () {};
        this._onCloseHandler = handlers.onClose || function () {};
        this._onEventHandler = handlers.onEvent || function () {};
        this._onErrorHandler = handlers.onError || function () {};
        this._onInboundInvokeHandler  = handlers.onInboundInvoke || function () {};
        this._onInboundTransmitHandler = handlers.onInboundTransmit || function () {};

        // Open the connection.

        this.state = this.CONNECTING;
        let uri = this.uri();

        let wsSocket = createWebSocket(uri, wsOptions);
        wsSocket.binaryType = this.options.binaryType;

        this.socket = wsSocket;

        wsSocket.onopen = () => {
          this._onOpen();
        };

        wsSocket.onclose = async (event) => {
          let code;
          if (event.code == null) {
            // This is to handle an edge case in React Native whereby
            // event.code is undefined when the mobile device is locked.
            // TODO: This is not ideal since this condition could also apply to
            // an abnormal close (no close control frame) which would be a 1006.
            code = 1005;
          } else {
            code = event.code;
          }
          this._destroy(code, event.reason);
        };

        wsSocket.onmessage = (message, flags) => {
          this._onMessage(message.data);
        };

        wsSocket.onerror = (error) => {
          // The onclose event will be called automatically after the onerror event
          // if the socket is connected - Otherwise, if it's in the middle of
          // connecting, we want to close it manually with a 1006 - This is necessary
          // to prevent inconsistent behavior when running the client in Node.js
          // vs in a browser.
          if (this.state === this.CONNECTING) {
            this._destroy(1006);
          }
        };

        this._connectTimeoutRef = setTimeout(() => {
          this._destroy(4007);
          this.socket.close(4007);
        }, this.connectTimeout);

        if (this.protocolVersion === 1) {
          this._handlePing = (message) => {
            if (message === '#1') {
              this._resetPingTimeout();
              if (this.socket.readyState === this.socket.OPEN) {
                this.send('#2');
              }
              return true;
            }
            return false;
          };
        } else {
          this._handlePing = (message) => {
            if (message === '') {
              this._resetPingTimeout();
              if (this.socket.readyState === this.socket.OPEN) {
                this.send('');
              }
              return true;
            }
            return false;
          };
        }
      }

      AGTransport.CONNECTING = AGTransport.prototype.CONNECTING = 'connecting';
      AGTransport.OPEN = AGTransport.prototype.OPEN = 'open';
      AGTransport.CLOSED = AGTransport.prototype.CLOSED = 'closed';

      AGTransport.prototype.uri = function () {
        let query = this.options.query || {};
        let scheme;
        if (this.options.protocolScheme == null) {
          scheme = this.options.secure ? 'wss' : 'ws';
        } else {
          scheme = this.options.protocolScheme;
        }

        if (this.options.timestampRequests) {
          query[this.options.timestampParam] = (new Date()).getTime();
        }

        query = querystring.encode(query);

        if (query.length) {
          query = '?' + query;
        }

        let host;
        let path;
        if (this.options.socketPath == null) {
          if (this.options.host) {
            host = this.options.host;
          } else {
            let port = '';

            if (this.options.port && ((scheme === 'wss' && this.options.port !== 443)
              || (scheme === 'ws' && this.options.port !== 80))) {
              port = ':' + this.options.port;
            }
            host = this.options.hostname + port;
          }
          path = this.options.path;
        } else {
          host = this.options.socketPath;
          path = `:${this.options.path}`;
        }
        return scheme + '://' + host + path + query;
      };

      AGTransport.prototype._onOpen = async function () {
        clearTimeout(this._connectTimeoutRef);
        this._resetPingTimeout();

        let status;

        try {
          status = await this._handshake();
        } catch (err) {
          if (err.statusCode == null) {
            err.statusCode = 4003;
          }
          this._onError(err);
          this._destroy(err.statusCode, err.toString());
          this.socket.close(err.statusCode);
          return;
        }

        this.state = this.OPEN;
        if (status) {
          this.pingTimeout = status.pingTimeout;
        }
        this._resetPingTimeout();
        this._onOpenHandler(status);
      };

      AGTransport.prototype._handshake = async function () {
        let token = await this.auth.loadToken(this.authTokenName);
        // Don't wait for this.state to be 'open'.
        // The underlying WebSocket (this.socket) is already open.
        let options = {
          force: true
        };
        let status = await this.invoke('#handshake', {authToken: token}, options);
        if (status) {
          // Add the token which was used as part of authentication attempt
          // to the status object.
          status.authToken = token;
          if (status.authError) {
            status.authError = scErrors.hydrateError(status.authError);
          }
        }
        return status;
      };

      AGTransport.prototype._abortAllPendingEventsDueToBadConnection = function (failureType) {
        Object.keys(this._callbackMap || {}).forEach((i) => {
          let eventObject = this._callbackMap[i];
          delete this._callbackMap[i];

          clearTimeout(eventObject.timeout);
          delete eventObject.timeout;

          let errorMessage = `Event "${eventObject.event}" was aborted due to a bad connection`;
          let badConnectionError = new BadConnectionError(errorMessage, failureType);

          let callback = eventObject.callback;
          delete eventObject.callback;

          callback.call(eventObject, badConnectionError, eventObject);
        });
      };

      AGTransport.prototype._destroy = function (code, reason) {
        let protocolReason = scErrors.socketProtocolErrorStatuses[code];
        if (!reason && scErrors.socketProtocolErrorStatuses[code]) {
          reason = scErrors.socketProtocolErrorStatuses[code];
        }
        delete this.socket.onopen;
        delete this.socket.onclose;
        delete this.socket.onmessage;
        delete this.socket.onerror;

        clearTimeout(this._connectTimeoutRef);
        clearTimeout(this._pingTimeoutTicker);

        if (this.state === this.OPEN) {
          this.state = this.CLOSED;
          this._abortAllPendingEventsDueToBadConnection('disconnect');
          this._onCloseHandler({code, reason});
        } else if (this.state === this.CONNECTING) {
          this.state = this.CLOSED;
          this._abortAllPendingEventsDueToBadConnection('connectAbort');
          this._onOpenAbortHandler({code, reason});
        } else if (this.state === this.CLOSED) {
          this._abortAllPendingEventsDueToBadConnection('connectAbort');
        }
      };

      AGTransport.prototype._processInboundPacket = function (packet, message) {
        if (packet && packet.event != null) {
          if (packet.cid == null) {
            this._onInboundTransmitHandler({...packet});
          } else {
            let request = new AGRequest(this, packet.cid, packet.event, packet.data);
            this._onInboundInvokeHandler(request);
          }
        } else if (packet && packet.rid != null) {
          let eventObject = this._callbackMap[packet.rid];
          if (eventObject) {
            clearTimeout(eventObject.timeout);
            delete eventObject.timeout;
            delete this._callbackMap[packet.rid];

            if (eventObject.callback) {
              let rehydratedError = scErrors.hydrateError(packet.error);
              eventObject.callback(rehydratedError, packet.data);
            }
          }
        } else {
          this._onEventHandler({event: 'raw', data: {message}});
        }
      };

      AGTransport.prototype._onMessage = function (message) {
        this._onEventHandler({event: 'message', data: {message}});

        if (this._handlePing(message)) {
          return;
        }

        let packet = this.decode(message);

        if (Array.isArray(packet)) {
          let len = packet.length;
          for (let i = 0; i < len; i++) {
            this._processInboundPacket(packet[i], message);
          }
        } else {
          this._processInboundPacket(packet, message);
        }
      };

      AGTransport.prototype._onError = function (error) {
        this._onErrorHandler({error});
      };

      AGTransport.prototype._resetPingTimeout = function () {
        if (this.pingTimeoutDisabled) {
          return;
        }

        let now = (new Date()).getTime();
        clearTimeout(this._pingTimeoutTicker);
        this._pingTimeoutTicker = setTimeout(() => {
          this._destroy(4000);
          this.socket.close(4000);
        }, this.pingTimeout);
      };

      AGTransport.prototype.clearAllListeners = function () {
        this._onOpenHandler = function () {};
        this._onOpenAbortHandler = function () {};
        this._onCloseHandler = function () {};
        this._onEventHandler = function () {};
        this._onErrorHandler = function () {};
        this._onInboundInvokeHandler  = function () {};
        this._onInboundTransmitHandler = function () {};
      };

      AGTransport.prototype.startBatch = function () {
        this.isBufferingBatch = true;
        this._batchBuffer = [];
      };

      AGTransport.prototype.flushBatch = function () {
        this.isBufferingBatch = false;
        if (!this._batchBuffer.length) {
          return;
        }
        let serializedBatch = this.serializeObject(this._batchBuffer);
        this._batchBuffer = [];
        this.send(serializedBatch);
      };

      AGTransport.prototype.cancelBatch = function () {
        this.isBufferingBatch = false;
        this._batchBuffer = [];
      };

      AGTransport.prototype.getBytesReceived = function () {
        return this.socket.bytesReceived;
      };

      AGTransport.prototype.close = function (code, reason) {
        if (this.state === this.OPEN || this.state === this.CONNECTING) {
          code = code || 1000;
          this._destroy(code, reason);
          this.socket.close(code, reason);
        }
      };

      AGTransport.prototype.transmitObject = function (eventObject) {
        let simpleEventObject = {
          event: eventObject.event,
          data: eventObject.data
        };

        if (eventObject.callback) {
          simpleEventObject.cid = eventObject.cid = this.callIdGenerator();
          this._callbackMap[eventObject.cid] = eventObject;
        }

        this.sendObject(simpleEventObject);

        return eventObject.cid || null;
      };

      AGTransport.prototype._handleEventAckTimeout = function (eventObject) {
        if (eventObject.cid) {
          delete this._callbackMap[eventObject.cid];
        }
        delete eventObject.timeout;

        let callback = eventObject.callback;
        if (callback) {
          delete eventObject.callback;
          let error = new TimeoutError(`Event response for "${eventObject.event}" timed out`);
          callback.call(eventObject, error, eventObject);
        }
      };

      AGTransport.prototype.transmit = function (event, data, options) {
        let eventObject = {
          event,
          data
        };

        if (this.state === this.OPEN || options.force) {
          this.transmitObject(eventObject);
        }
        return Promise.resolve();
      };

      AGTransport.prototype.invokeRaw = function (event, data, options, callback) {
        let eventObject = {
          event,
          data,
          callback
        };

        if (!options.noTimeout) {
          eventObject.timeout = setTimeout(() => {
            this._handleEventAckTimeout(eventObject);
          }, this.options.ackTimeout);
        }
        let cid = null;
        if (this.state === this.OPEN || options.force) {
          cid = this.transmitObject(eventObject);
        }
        return cid;
      };

      AGTransport.prototype.invoke = function (event, data, options) {
        return new Promise((resolve, reject) => {
          this.invokeRaw(event, data, options, (err, data) => {
            if (err) {
              reject(err);
              return;
            }
            resolve(data);
          });
        });
      };

      AGTransport.prototype.cancelPendingResponse = function (cid) {
        delete this._callbackMap[cid];
      };

      AGTransport.prototype.decode = function (message) {
        return this.codec.decode(message);
      };

      AGTransport.prototype.encode = function (object) {
        return this.codec.encode(object);
      };

      AGTransport.prototype.send = function (data) {
        if (this.socket.readyState !== this.socket.OPEN) {
          this._destroy(1005);
        } else {
          this.socket.send(data);
        }
      };

      AGTransport.prototype.serializeObject = function (object) {
        let str;
        try {
          str = this.encode(object);
        } catch (error) {
          this._onError(error);
          return null;
        }
        return str;
      };

      AGTransport.prototype.sendObject = function (object) {
        if (this.isBufferingBatch) {
          this._batchBuffer.push(object);
          return;
        }
        let str = this.serializeObject(object);
        if (str != null) {
          this.send(str);
        }
      };

      module.exports = AGTransport;

    }).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})
  },{"ag-request":8,"querystring":22,"sc-errors":24,"ws":6}],5:[function(require,module,exports){
    function wait(duration) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve();
        }, duration);
      });
    }

    module.exports = wait;

  },{}],6:[function(require,module,exports){
    let global;
    if (typeof WorkerGlobalScope !== 'undefined') {
      global = self;
    } else {
      global = typeof window !== 'undefined' && window || (function() { return this; })();
    }

    const WebSocket = global.WebSocket || global.MozWebSocket;

    /**
     * WebSocket constructor.
     *
     * The third `opts` options object gets ignored in web browsers, since it's
     * non-standard, and throws a TypeError if passed to the constructor.
     * See: https://github.com/einaros/ws/issues/227
     *
     * @param {String} uri
     * @param {Array} protocols (optional)
     * @param {Object} opts (optional)
     * @api public
     */

    function ws(uri, protocols, opts) {
      let instance;
      if (protocols) {
        instance = new WebSocket(uri, protocols);
      } else {
        instance = new WebSocket(uri);
      }
      return instance;
    }

    if (WebSocket) ws.prototype = WebSocket.prototype;

    module.exports = WebSocket ? ws : null;

  },{}],7:[function(require,module,exports){
    const ConsumableStream = require('consumable-stream');

    class AGChannel extends ConsumableStream {
      constructor(name, client, eventDemux, dataDemux) {
        super();
        this.PENDING = AGChannel.PENDING;
        this.SUBSCRIBED = AGChannel.SUBSCRIBED;
        this.UNSUBSCRIBED = AGChannel.UNSUBSCRIBED;

        this.name = name;
        this.client = client;

        this._eventDemux = eventDemux;
        this._dataStream = dataDemux.stream(this.name);
      }

      createConsumer(timeout) {
        return this._dataStream.createConsumer(timeout);
      }

      listener(eventName) {
        return this._eventDemux.stream(`${this.name}/${eventName}`);
      }

      close() {
        this.client.closeChannel(this.name);
      }

      kill() {
        this.client.killChannel(this.name);
      }

      killOutputConsumer(consumerId) {
        if (this.hasOutputConsumer(consumerId)) {
          this.client.killChannelOutputConsumer(consumerId);
        }
      }

      killListenerConsumer(consumerId) {
        if (this.hasAnyListenerConsumer(consumerId)) {
          this.client.killChannelListenerConsumer(consumerId);
        }
      }

      getOutputConsumerStats(consumerId) {
        if (this.hasOutputConsumer(consumerId)) {
          return this.client.getChannelOutputConsumerStats(consumerId);
        }
        return undefined;
      }

      getListenerConsumerStats(consumerId) {
        if (this.hasAnyListenerConsumer(consumerId)) {
          return this.client.getChannelListenerConsumerStats(consumerId);
        }
        return undefined;
      }

      getBackpressure() {
        return this.client.getChannelBackpressure(this.name);
      }

      getListenerConsumerBackpressure(consumerId) {
        if (this.hasAnyListenerConsumer(consumerId)) {
          return this.client.getChannelListenerConsumerBackpressure(consumerId);
        }
        return 0;
      }

      getOutputConsumerBackpressure(consumerId) {
        if (this.hasOutputConsumer(consumerId)) {
          return this.client.getChannelOutputConsumerBackpressure(consumerId);
        }
        return 0;
      }

      closeOutput() {
        this.client.channelCloseOutput(this.name);
      }

      closeListener(eventName) {
        this.client.channelCloseListener(this.name, eventName);
      }

      closeAllListeners() {
        this.client.channelCloseAllListeners(this.name);
      }

      killOutput() {
        this.client.channelKillOutput(this.name);
      }

      killListener(eventName) {
        this.client.channelKillListener(this.name, eventName);
      }

      killAllListeners() {
        this.client.channelKillAllListeners(this.name);
      }

      getOutputConsumerStatsList() {
        return this.client.channelGetOutputConsumerStatsList(this.name);
      }

      getListenerConsumerStatsList(eventName) {
        return this.client.channelGetListenerConsumerStatsList(this.name, eventName);
      }

      getAllListenersConsumerStatsList() {
        return this.client.channelGetAllListenersConsumerStatsList(this.name);
      }

      getOutputBackpressure() {
        return this.client.channelGetOutputBackpressure(this.name);
      }

      getListenerBackpressure(eventName) {
        return this.client.channelGetListenerBackpressure(this.name, eventName);
      }

      getAllListenersBackpressure() {
        return this.client.channelGetAllListenersBackpressure(this.name);
      }

      hasOutputConsumer(consumerId) {
        return this.client.channelHasOutputConsumer(this.name, consumerId);
      }

      hasListenerConsumer(eventName, consumerId) {
        return this.client.channelHasListenerConsumer(this.name, eventName, consumerId);
      }

      hasAnyListenerConsumer(consumerId) {
        return this.client.channelHasAnyListenerConsumer(this.name, consumerId);
      }

      get state() {
        return this.client.getChannelState(this.name);
      }

      set state(value) {
        throw new Error('Cannot directly set channel state');
      }

      get options() {
        return this.client.getChannelOptions(this.name);
      }

      set options(value) {
        throw new Error('Cannot directly set channel options');
      }

      subscribe(options) {
        this.client.subscribe(this.name, options);
      }

      unsubscribe() {
        this.client.unsubscribe(this.name);
      }

      isSubscribed(includePending) {
        return this.client.isSubscribed(this.name, includePending);
      }

      transmitPublish(data) {
        return this.client.transmitPublish(this.name, data);
      }

      invokePublish(data) {
        return this.client.invokePublish(this.name, data);
      }
    }

    AGChannel.PENDING = 'pending';
    AGChannel.SUBSCRIBED = 'subscribed';
    AGChannel.UNSUBSCRIBED = 'unsubscribed';

    module.exports = AGChannel;

  },{"consumable-stream":13}],8:[function(require,module,exports){
    const scErrors = require('sc-errors');
    const InvalidActionError = scErrors.InvalidActionError;

    function AGRequest(socket, id, procedureName, data) {
      this.socket = socket;
      this.id = id;
      this.procedure = procedureName;
      this.data = data;
      this.sent = false;

      this._respond = (responseData, options) => {
        if (this.sent) {
          throw new InvalidActionError(`Response to request ${this.id} has already been sent`);
        }
        this.sent = true;
        this.socket.sendObject(responseData, options);
      };

      this.end = (data, options) => {
        let responseData = {
          rid: this.id
        };
        if (data !== undefined) {
          responseData.data = data;
        }
        this._respond(responseData, options);
      };

      this.error = (error, options) => {
        let responseData = {
          rid: this.id,
          error: scErrors.dehydrateError(error)
        };
        this._respond(responseData, options);
      };
    }

    module.exports = AGRequest;

  },{"sc-errors":24}],9:[function(require,module,exports){
    const StreamDemux = require('stream-demux');

    function AsyncStreamEmitter() {
      this._listenerDemux = new StreamDemux();
    }

    AsyncStreamEmitter.prototype.emit = function (eventName, data) {
      this._listenerDemux.write(eventName, data);
    };

    AsyncStreamEmitter.prototype.listener = function (eventName) {
      return this._listenerDemux.stream(eventName);
    };

    AsyncStreamEmitter.prototype.closeListener = function (eventName) {
      this._listenerDemux.close(eventName);
    };

    AsyncStreamEmitter.prototype.closeAllListeners = function () {
      this._listenerDemux.closeAll();
    };

    AsyncStreamEmitter.prototype.getListenerConsumerStats = function (consumerId) {
      return this._listenerDemux.getConsumerStats(consumerId);
    };

    AsyncStreamEmitter.prototype.getListenerConsumerStatsList = function (eventName) {
      return this._listenerDemux.getConsumerStatsList(eventName);
    };

    AsyncStreamEmitter.prototype.getAllListenersConsumerStatsList = function () {
      return this._listenerDemux.getConsumerStatsListAll();
    };

    AsyncStreamEmitter.prototype.killListener = function (eventName) {
      this._listenerDemux.kill(eventName);
    };

    AsyncStreamEmitter.prototype.killAllListeners = function () {
      this._listenerDemux.killAll();
    };

    AsyncStreamEmitter.prototype.killListenerConsumer = function (consumerId) {
      this._listenerDemux.killConsumer(consumerId);
    };

    AsyncStreamEmitter.prototype.getListenerBackpressure = function (eventName) {
      return this._listenerDemux.getBackpressure(eventName);
    };

    AsyncStreamEmitter.prototype.getAllListenersBackpressure = function () {
      return this._listenerDemux.getBackpressureAll();
    };

    AsyncStreamEmitter.prototype.getListenerConsumerBackpressure = function (consumerId) {
      return this._listenerDemux.getConsumerBackpressure(consumerId);
    };

    AsyncStreamEmitter.prototype.hasListenerConsumer = function (eventName, consumerId) {
      return this._listenerDemux.hasConsumer(eventName, consumerId);
    };

    AsyncStreamEmitter.prototype.hasAnyListenerConsumer = function (consumerId) {
      return this._listenerDemux.hasConsumerAll(consumerId);
    };

    module.exports = AsyncStreamEmitter;

  },{"stream-demux":28}],10:[function(require,module,exports){
    'use strict'

    exports.byteLength = byteLength
    exports.toByteArray = toByteArray
    exports.fromByteArray = fromByteArray

    var lookup = []
    var revLookup = []
    var Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array

    var code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
    for (var i = 0, len = code.length; i < len; ++i) {
      lookup[i] = code[i]
      revLookup[code.charCodeAt(i)] = i
    }

// Support decoding URL-safe base64 strings, as Node.js does.
// See: https://en.wikipedia.org/wiki/Base64#URL_applications
    revLookup['-'.charCodeAt(0)] = 62
    revLookup['_'.charCodeAt(0)] = 63

    function getLens (b64) {
      var len = b64.length

      if (len % 4 > 0) {
        throw new Error('Invalid string. Length must be a multiple of 4')
      }

      // Trim off extra bytes after placeholder bytes are found
      // See: https://github.com/beatgammit/base64-js/issues/42
      var validLen = b64.indexOf('=')
      if (validLen === -1) validLen = len

      var placeHoldersLen = validLen === len
        ? 0
        : 4 - (validLen % 4)

      return [validLen, placeHoldersLen]
    }

// base64 is 4/3 + up to two characters of the original data
    function byteLength (b64) {
      var lens = getLens(b64)
      var validLen = lens[0]
      var placeHoldersLen = lens[1]
      return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen
    }

    function _byteLength (b64, validLen, placeHoldersLen) {
      return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen
    }

    function toByteArray (b64) {
      var tmp
      var lens = getLens(b64)
      var validLen = lens[0]
      var placeHoldersLen = lens[1]

      var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))

      var curByte = 0

      // if there are placeholders, only get up to the last complete 4 chars
      var len = placeHoldersLen > 0
        ? validLen - 4
        : validLen

      var i
      for (i = 0; i < len; i += 4) {
        tmp =
          (revLookup[b64.charCodeAt(i)] << 18) |
          (revLookup[b64.charCodeAt(i + 1)] << 12) |
          (revLookup[b64.charCodeAt(i + 2)] << 6) |
          revLookup[b64.charCodeAt(i + 3)]
        arr[curByte++] = (tmp >> 16) & 0xFF
        arr[curByte++] = (tmp >> 8) & 0xFF
        arr[curByte++] = tmp & 0xFF
      }

      if (placeHoldersLen === 2) {
        tmp =
          (revLookup[b64.charCodeAt(i)] << 2) |
          (revLookup[b64.charCodeAt(i + 1)] >> 4)
        arr[curByte++] = tmp & 0xFF
      }

      if (placeHoldersLen === 1) {
        tmp =
          (revLookup[b64.charCodeAt(i)] << 10) |
          (revLookup[b64.charCodeAt(i + 1)] << 4) |
          (revLookup[b64.charCodeAt(i + 2)] >> 2)
        arr[curByte++] = (tmp >> 8) & 0xFF
        arr[curByte++] = tmp & 0xFF
      }

      return arr
    }

    function tripletToBase64 (num) {
      return lookup[num >> 18 & 0x3F] +
        lookup[num >> 12 & 0x3F] +
        lookup[num >> 6 & 0x3F] +
        lookup[num & 0x3F]
    }

    function encodeChunk (uint8, start, end) {
      var tmp
      var output = []
      for (var i = start; i < end; i += 3) {
        tmp =
          ((uint8[i] << 16) & 0xFF0000) +
          ((uint8[i + 1] << 8) & 0xFF00) +
          (uint8[i + 2] & 0xFF)
        output.push(tripletToBase64(tmp))
      }
      return output.join('')
    }

    function fromByteArray (uint8) {
      var tmp
      var len = uint8.length
      var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes
      var parts = []
      var maxChunkLength = 16383 // must be multiple of 3

      // go through the array every three bytes, we'll deal with trailing stuff later
      for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {
        parts.push(encodeChunk(
          uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)
        ))
      }

      // pad the end with zeros, but make sure to not forget the extra bytes
      if (extraBytes === 1) {
        tmp = uint8[len - 1]
        parts.push(
          lookup[tmp >> 2] +
          lookup[(tmp << 4) & 0x3F] +
          '=='
        )
      } else if (extraBytes === 2) {
        tmp = (uint8[len - 2] << 8) + uint8[len - 1]
        parts.push(
          lookup[tmp >> 10] +
          lookup[(tmp >> 4) & 0x3F] +
          lookup[(tmp << 2) & 0x3F] +
          '='
        )
      }

      return parts.join('')
    }

  },{}],11:[function(require,module,exports){
    (function (Buffer){
      /*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
      /* eslint-disable no-proto */

      'use strict'

      var base64 = require('base64-js')
      var ieee754 = require('ieee754')
      var customInspectSymbol =
        (typeof Symbol === 'function' && typeof Symbol.for === 'function')
          ? Symbol.for('nodejs.util.inspect.custom')
          : null

      exports.Buffer = Buffer
      exports.SlowBuffer = SlowBuffer
      exports.INSPECT_MAX_BYTES = 50

      var K_MAX_LENGTH = 0x7fffffff
      exports.kMaxLength = K_MAX_LENGTH

      /**
       * If `Buffer.TYPED_ARRAY_SUPPORT`:
       *   === true    Use Uint8Array implementation (fastest)
       *   === false   Print warning and recommend using `buffer` v4.x which has an Object
       *               implementation (most compatible, even IE6)
       *
       * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,
       * Opera 11.6+, iOS 4.2+.
       *
       * We report that the browser does not support typed arrays if the are not subclassable
       * using __proto__. Firefox 4-29 lacks support for adding new properties to `Uint8Array`
       * (See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438). IE 10 lacks support
       * for __proto__ and has a buggy typed array implementation.
       */
      Buffer.TYPED_ARRAY_SUPPORT = typedArraySupport()

      if (!Buffer.TYPED_ARRAY_SUPPORT && typeof console !== 'undefined' &&
        typeof console.error === 'function') {
        console.error(
          'This browser lacks typed array (Uint8Array) support which is required by ' +
          '`buffer` v5.x. Use `buffer` v4.x if you require old browser support.'
        )
      }

      function typedArraySupport () {
        // Can typed array instances can be augmented?
        try {
          var arr = new Uint8Array(1)
          var proto = { foo: function () { return 42 } }
          Object.setPrototypeOf(proto, Uint8Array.prototype)
          Object.setPrototypeOf(arr, proto)
          return arr.foo() === 42
        } catch (e) {
          return false
        }
      }

      Object.defineProperty(Buffer.prototype, 'parent', {
        enumerable: true,
        get: function () {
          if (!Buffer.isBuffer(this)) return undefined
          return this.buffer
        }
      })

      Object.defineProperty(Buffer.prototype, 'offset', {
        enumerable: true,
        get: function () {
          if (!Buffer.isBuffer(this)) return undefined
          return this.byteOffset
        }
      })

      function createBuffer (length) {
        if (length > K_MAX_LENGTH) {
          throw new RangeError('The value "' + length + '" is invalid for option "size"')
        }
        // Return an augmented `Uint8Array` instance
        var buf = new Uint8Array(length)
        Object.setPrototypeOf(buf, Buffer.prototype)
        return buf
      }

      /**
       * The Buffer constructor returns instances of `Uint8Array` that have their
       * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of
       * `Uint8Array`, so the returned instances will have all the node `Buffer` methods
       * and the `Uint8Array` methods. Square bracket notation works as expected -- it
       * returns a single octet.
       *
       * The `Uint8Array` prototype remains unmodified.
       */

      function Buffer (arg, encodingOrOffset, length) {
        // Common case.
        if (typeof arg === 'number') {
          if (typeof encodingOrOffset === 'string') {
            throw new TypeError(
              'The "string" argument must be of type string. Received type number'
            )
          }
          return allocUnsafe(arg)
        }
        return from(arg, encodingOrOffset, length)
      }

// Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97
      if (typeof Symbol !== 'undefined' && Symbol.species != null &&
        Buffer[Symbol.species] === Buffer) {
        Object.defineProperty(Buffer, Symbol.species, {
          value: null,
          configurable: true,
          enumerable: false,
          writable: false
        })
      }

      Buffer.poolSize = 8192 // not used by this implementation

      function from (value, encodingOrOffset, length) {
        if (typeof value === 'string') {
          return fromString(value, encodingOrOffset)
        }

        if (ArrayBuffer.isView(value)) {
          return fromArrayLike(value)
        }

        if (value == null) {
          throw new TypeError(
            'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +
            'or Array-like Object. Received type ' + (typeof value)
          )
        }

        if (isInstance(value, ArrayBuffer) ||
          (value && isInstance(value.buffer, ArrayBuffer))) {
          return fromArrayBuffer(value, encodingOrOffset, length)
        }

        if (typeof value === 'number') {
          throw new TypeError(
            'The "value" argument must not be of type number. Received type number'
          )
        }

        var valueOf = value.valueOf && value.valueOf()
        if (valueOf != null && valueOf !== value) {
          return Buffer.from(valueOf, encodingOrOffset, length)
        }

        var b = fromObject(value)
        if (b) return b

        if (typeof Symbol !== 'undefined' && Symbol.toPrimitive != null &&
          typeof value[Symbol.toPrimitive] === 'function') {
          return Buffer.from(
            value[Symbol.toPrimitive]('string'), encodingOrOffset, length
          )
        }

        throw new TypeError(
          'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +
          'or Array-like Object. Received type ' + (typeof value)
        )
      }

      /**
       * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError
       * if value is a number.
       * Buffer.from(str[, encoding])
       * Buffer.from(array)
       * Buffer.from(buffer)
       * Buffer.from(arrayBuffer[, byteOffset[, length]])
       **/
      Buffer.from = function (value, encodingOrOffset, length) {
        return from(value, encodingOrOffset, length)
      }

// Note: Change prototype *after* Buffer.from is defined to workaround Chrome bug:
// https://github.com/feross/buffer/pull/148
      Object.setPrototypeOf(Buffer.prototype, Uint8Array.prototype)
      Object.setPrototypeOf(Buffer, Uint8Array)

      function assertSize (size) {
        if (typeof size !== 'number') {
          throw new TypeError('"size" argument must be of type number')
        } else if (size < 0) {
          throw new RangeError('The value "' + size + '" is invalid for option "size"')
        }
      }

      function alloc (size, fill, encoding) {
        assertSize(size)
        if (size <= 0) {
          return createBuffer(size)
        }
        if (fill !== undefined) {
          // Only pay attention to encoding if it's a string. This
          // prevents accidentally sending in a number that would
          // be interpretted as a start offset.
          return typeof encoding === 'string'
            ? createBuffer(size).fill(fill, encoding)
            : createBuffer(size).fill(fill)
        }
        return createBuffer(size)
      }

      /**
       * Creates a new filled Buffer instance.
       * alloc(size[, fill[, encoding]])
       **/
      Buffer.alloc = function (size, fill, encoding) {
        return alloc(size, fill, encoding)
      }

      function allocUnsafe (size) {
        assertSize(size)
        return createBuffer(size < 0 ? 0 : checked(size) | 0)
      }

      /**
       * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.
       * */
      Buffer.allocUnsafe = function (size) {
        return allocUnsafe(size)
      }
      /**
       * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.
       */
      Buffer.allocUnsafeSlow = function (size) {
        return allocUnsafe(size)
      }

      function fromString (string, encoding) {
        if (typeof encoding !== 'string' || encoding === '') {
          encoding = 'utf8'
        }

        if (!Buffer.isEncoding(encoding)) {
          throw new TypeError('Unknown encoding: ' + encoding)
        }

        var length = byteLength(string, encoding) | 0
        var buf = createBuffer(length)

        var actual = buf.write(string, encoding)

        if (actual !== length) {
          // Writing a hex string, for example, that contains invalid characters will
          // cause everything after the first invalid character to be ignored. (e.g.
          // 'abxxcd' will be treated as 'ab')
          buf = buf.slice(0, actual)
        }

        return buf
      }

      function fromArrayLike (array) {
        var length = array.length < 0 ? 0 : checked(array.length) | 0
        var buf = createBuffer(length)
        for (var i = 0; i < length; i += 1) {
          buf[i] = array[i] & 255
        }
        return buf
      }

      function fromArrayBuffer (array, byteOffset, length) {
        if (byteOffset < 0 || array.byteLength < byteOffset) {
          throw new RangeError('"offset" is outside of buffer bounds')
        }

        if (array.byteLength < byteOffset + (length || 0)) {
          throw new RangeError('"length" is outside of buffer bounds')
        }

        var buf
        if (byteOffset === undefined && length === undefined) {
          buf = new Uint8Array(array)
        } else if (length === undefined) {
          buf = new Uint8Array(array, byteOffset)
        } else {
          buf = new Uint8Array(array, byteOffset, length)
        }

        // Return an augmented `Uint8Array` instance
        Object.setPrototypeOf(buf, Buffer.prototype)

        return buf
      }

      function fromObject (obj) {
        if (Buffer.isBuffer(obj)) {
          var len = checked(obj.length) | 0
          var buf = createBuffer(len)

          if (buf.length === 0) {
            return buf
          }

          obj.copy(buf, 0, 0, len)
          return buf
        }

        if (obj.length !== undefined) {
          if (typeof obj.length !== 'number' || numberIsNaN(obj.length)) {
            return createBuffer(0)
          }
          return fromArrayLike(obj)
        }

        if (obj.type === 'Buffer' && Array.isArray(obj.data)) {
          return fromArrayLike(obj.data)
        }
      }

      function checked (length) {
        // Note: cannot use `length < K_MAX_LENGTH` here because that fails when
        // length is NaN (which is otherwise coerced to zero.)
        if (length >= K_MAX_LENGTH) {
          throw new RangeError('Attempt to allocate Buffer larger than maximum ' +
            'size: 0x' + K_MAX_LENGTH.toString(16) + ' bytes')
        }
        return length | 0
      }

      function SlowBuffer (length) {
        if (+length != length) { // eslint-disable-line eqeqeq
          length = 0
        }
        return Buffer.alloc(+length)
      }

      Buffer.isBuffer = function isBuffer (b) {
        return b != null && b._isBuffer === true &&
          b !== Buffer.prototype // so Buffer.isBuffer(Buffer.prototype) will be false
      }

      Buffer.compare = function compare (a, b) {
        if (isInstance(a, Uint8Array)) a = Buffer.from(a, a.offset, a.byteLength)
        if (isInstance(b, Uint8Array)) b = Buffer.from(b, b.offset, b.byteLength)
        if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {
          throw new TypeError(
            'The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array'
          )
        }

        if (a === b) return 0

        var x = a.length
        var y = b.length

        for (var i = 0, len = Math.min(x, y); i < len; ++i) {
          if (a[i] !== b[i]) {
            x = a[i]
            y = b[i]
            break
          }
        }

        if (x < y) return -1
        if (y < x) return 1
        return 0
      }

      Buffer.isEncoding = function isEncoding (encoding) {
        switch (String(encoding).toLowerCase()) {
          case 'hex':
          case 'utf8':
          case 'utf-8':
          case 'ascii':
          case 'latin1':
          case 'binary':
          case 'base64':
          case 'ucs2':
          case 'ucs-2':
          case 'utf16le':
          case 'utf-16le':
            return true
          default:
            return false
        }
      }

      Buffer.concat = function concat (list, length) {
        if (!Array.isArray(list)) {
          throw new TypeError('"list" argument must be an Array of Buffers')
        }

        if (list.length === 0) {
          return Buffer.alloc(0)
        }

        var i
        if (length === undefined) {
          length = 0
          for (i = 0; i < list.length; ++i) {
            length += list[i].length
          }
        }

        var buffer = Buffer.allocUnsafe(length)
        var pos = 0
        for (i = 0; i < list.length; ++i) {
          var buf = list[i]
          if (isInstance(buf, Uint8Array)) {
            buf = Buffer.from(buf)
          }
          if (!Buffer.isBuffer(buf)) {
            throw new TypeError('"list" argument must be an Array of Buffers')
          }
          buf.copy(buffer, pos)
          pos += buf.length
        }
        return buffer
      }

      function byteLength (string, encoding) {
        if (Buffer.isBuffer(string)) {
          return string.length
        }
        if (ArrayBuffer.isView(string) || isInstance(string, ArrayBuffer)) {
          return string.byteLength
        }
        if (typeof string !== 'string') {
          throw new TypeError(
            'The "string" argument must be one of type string, Buffer, or ArrayBuffer. ' +
            'Received type ' + typeof string
          )
        }

        var len = string.length
        var mustMatch = (arguments.length > 2 && arguments[2] === true)
        if (!mustMatch && len === 0) return 0

        // Use a for loop to avoid recursion
        var loweredCase = false
        for (;;) {
          switch (encoding) {
            case 'ascii':
            case 'latin1':
            case 'binary':
              return len
            case 'utf8':
            case 'utf-8':
              return utf8ToBytes(string).length
            case 'ucs2':
            case 'ucs-2':
            case 'utf16le':
            case 'utf-16le':
              return len * 2
            case 'hex':
              return len >>> 1
            case 'base64':
              return base64ToBytes(string).length
            default:
              if (loweredCase) {
                return mustMatch ? -1 : utf8ToBytes(string).length // assume utf8
              }
              encoding = ('' + encoding).toLowerCase()
              loweredCase = true
          }
        }
      }
      Buffer.byteLength = byteLength

      function slowToString (encoding, start, end) {
        var loweredCase = false

        // No need to verify that "this.length <= MAX_UINT32" since it's a read-only
        // property of a typed array.

        // This behaves neither like String nor Uint8Array in that we set start/end
        // to their upper/lower bounds if the value passed is out of range.
        // undefined is handled specially as per ECMA-262 6th Edition,
        // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.
        if (start === undefined || start < 0) {
          start = 0
        }
        // Return early if start > this.length. Done here to prevent potential uint32
        // coercion fail below.
        if (start > this.length) {
          return ''
        }

        if (end === undefined || end > this.length) {
          end = this.length
        }

        if (end <= 0) {
          return ''
        }

        // Force coersion to uint32. This will also coerce falsey/NaN values to 0.
        end >>>= 0
        start >>>= 0

        if (end <= start) {
          return ''
        }

        if (!encoding) encoding = 'utf8'

        while (true) {
          switch (encoding) {
            case 'hex':
              return hexSlice(this, start, end)

            case 'utf8':
            case 'utf-8':
              return utf8Slice(this, start, end)

            case 'ascii':
              return asciiSlice(this, start, end)

            case 'latin1':
            case 'binary':
              return latin1Slice(this, start, end)

            case 'base64':
              return base64Slice(this, start, end)

            case 'ucs2':
            case 'ucs-2':
            case 'utf16le':
            case 'utf-16le':
              return utf16leSlice(this, start, end)

            default:
              if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)
              encoding = (encoding + '').toLowerCase()
              loweredCase = true
          }
        }
      }

// This property is used by `Buffer.isBuffer` (and the `is-buffer` npm package)
// to detect a Buffer instance. It's not possible to use `instanceof Buffer`
// reliably in a browserify context because there could be multiple different
// copies of the 'buffer' package in use. This method works even for Buffer
// instances that were created from another copy of the `buffer` package.
// See: https://github.com/feross/buffer/issues/154
      Buffer.prototype._isBuffer = true

      function swap (b, n, m) {
        var i = b[n]
        b[n] = b[m]
        b[m] = i
      }

      Buffer.prototype.swap16 = function swap16 () {
        var len = this.length
        if (len % 2 !== 0) {
          throw new RangeError('Buffer size must be a multiple of 16-bits')
        }
        for (var i = 0; i < len; i += 2) {
          swap(this, i, i + 1)
        }
        return this
      }

      Buffer.prototype.swap32 = function swap32 () {
        var len = this.length
        if (len % 4 !== 0) {
          throw new RangeError('Buffer size must be a multiple of 32-bits')
        }
        for (var i = 0; i < len; i += 4) {
          swap(this, i, i + 3)
          swap(this, i + 1, i + 2)
        }
        return this
      }

      Buffer.prototype.swap64 = function swap64 () {
        var len = this.length
        if (len % 8 !== 0) {
          throw new RangeError('Buffer size must be a multiple of 64-bits')
        }
        for (var i = 0; i < len; i += 8) {
          swap(this, i, i + 7)
          swap(this, i + 1, i + 6)
          swap(this, i + 2, i + 5)
          swap(this, i + 3, i + 4)
        }
        return this
      }

      Buffer.prototype.toString = function toString () {
        var length = this.length
        if (length === 0) return ''
        if (arguments.length === 0) return utf8Slice(this, 0, length)
        return slowToString.apply(this, arguments)
      }

      Buffer.prototype.toLocaleString = Buffer.prototype.toString

      Buffer.prototype.equals = function equals (b) {
        if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')
        if (this === b) return true
        return Buffer.compare(this, b) === 0
      }

      Buffer.prototype.inspect = function inspect () {
        var str = ''
        var max = exports.INSPECT_MAX_BYTES
        str = this.toString('hex', 0, max).replace(/(.{2})/g, '$1 ').trim()
        if (this.length > max) str += ' ... '
        return '<Buffer ' + str + '>'
      }
      if (customInspectSymbol) {
        Buffer.prototype[customInspectSymbol] = Buffer.prototype.inspect
      }

      Buffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {
        if (isInstance(target, Uint8Array)) {
          target = Buffer.from(target, target.offset, target.byteLength)
        }
        if (!Buffer.isBuffer(target)) {
          throw new TypeError(
            'The "target" argument must be one of type Buffer or Uint8Array. ' +
            'Received type ' + (typeof target)
          )
        }

        if (start === undefined) {
          start = 0
        }
        if (end === undefined) {
          end = target ? target.length : 0
        }
        if (thisStart === undefined) {
          thisStart = 0
        }
        if (thisEnd === undefined) {
          thisEnd = this.length
        }

        if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {
          throw new RangeError('out of range index')
        }

        if (thisStart >= thisEnd && start >= end) {
          return 0
        }
        if (thisStart >= thisEnd) {
          return -1
        }
        if (start >= end) {
          return 1
        }

        start >>>= 0
        end >>>= 0
        thisStart >>>= 0
        thisEnd >>>= 0

        if (this === target) return 0

        var x = thisEnd - thisStart
        var y = end - start
        var len = Math.min(x, y)

        var thisCopy = this.slice(thisStart, thisEnd)
        var targetCopy = target.slice(start, end)

        for (var i = 0; i < len; ++i) {
          if (thisCopy[i] !== targetCopy[i]) {
            x = thisCopy[i]
            y = targetCopy[i]
            break
          }
        }

        if (x < y) return -1
        if (y < x) return 1
        return 0
      }

// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,
// OR the last index of `val` in `buffer` at offset <= `byteOffset`.
//
// Arguments:
// - buffer - a Buffer to search
// - val - a string, Buffer, or number
// - byteOffset - an index into `buffer`; will be clamped to an int32
// - encoding - an optional encoding, relevant is val is a string
// - dir - true for indexOf, false for lastIndexOf
      function bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {
        // Empty buffer means no match
        if (buffer.length === 0) return -1

        // Normalize byteOffset
        if (typeof byteOffset === 'string') {
          encoding = byteOffset
          byteOffset = 0
        } else if (byteOffset > 0x7fffffff) {
          byteOffset = 0x7fffffff
        } else if (byteOffset < -0x80000000) {
          byteOffset = -0x80000000
        }
        byteOffset = +byteOffset // Coerce to Number.
        if (numberIsNaN(byteOffset)) {
          // byteOffset: it it's undefined, null, NaN, "foo", etc, search whole buffer
          byteOffset = dir ? 0 : (buffer.length - 1)
        }

        // Normalize byteOffset: negative offsets start from the end of the buffer
        if (byteOffset < 0) byteOffset = buffer.length + byteOffset
        if (byteOffset >= buffer.length) {
          if (dir) return -1
          else byteOffset = buffer.length - 1
        } else if (byteOffset < 0) {
          if (dir) byteOffset = 0
          else return -1
        }

        // Normalize val
        if (typeof val === 'string') {
          val = Buffer.from(val, encoding)
        }

        // Finally, search either indexOf (if dir is true) or lastIndexOf
        if (Buffer.isBuffer(val)) {
          // Special case: looking for empty string/buffer always fails
          if (val.length === 0) {
            return -1
          }
          return arrayIndexOf(buffer, val, byteOffset, encoding, dir)
        } else if (typeof val === 'number') {
          val = val & 0xFF // Search for a byte value [0-255]
          if (typeof Uint8Array.prototype.indexOf === 'function') {
            if (dir) {
              return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)
            } else {
              return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)
            }
          }
          return arrayIndexOf(buffer, [val], byteOffset, encoding, dir)
        }

        throw new TypeError('val must be string, number or Buffer')
      }

      function arrayIndexOf (arr, val, byteOffset, encoding, dir) {
        var indexSize = 1
        var arrLength = arr.length
        var valLength = val.length

        if (encoding !== undefined) {
          encoding = String(encoding).toLowerCase()
          if (encoding === 'ucs2' || encoding === 'ucs-2' ||
            encoding === 'utf16le' || encoding === 'utf-16le') {
            if (arr.length < 2 || val.length < 2) {
              return -1
            }
            indexSize = 2
            arrLength /= 2
            valLength /= 2
            byteOffset /= 2
          }
        }

        function read (buf, i) {
          if (indexSize === 1) {
            return buf[i]
          } else {
            return buf.readUInt16BE(i * indexSize)
          }
        }

        var i
        if (dir) {
          var foundIndex = -1
          for (i = byteOffset; i < arrLength; i++) {
            if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {
              if (foundIndex === -1) foundIndex = i
              if (i - foundIndex + 1 === valLength) return foundIndex * indexSize
            } else {
              if (foundIndex !== -1) i -= i - foundIndex
              foundIndex = -1
            }
          }
        } else {
          if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength
          for (i = byteOffset; i >= 0; i--) {
            var found = true
            for (var j = 0; j < valLength; j++) {
              if (read(arr, i + j) !== read(val, j)) {
                found = false
                break
              }
            }
            if (found) return i
          }
        }

        return -1
      }

      Buffer.prototype.includes = function includes (val, byteOffset, encoding) {
        return this.indexOf(val, byteOffset, encoding) !== -1
      }

      Buffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {
        return bidirectionalIndexOf(this, val, byteOffset, encoding, true)
      }

      Buffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {
        return bidirectionalIndexOf(this, val, byteOffset, encoding, false)
      }

      function hexWrite (buf, string, offset, length) {
        offset = Number(offset) || 0
        var remaining = buf.length - offset
        if (!length) {
          length = remaining
        } else {
          length = Number(length)
          if (length > remaining) {
            length = remaining
          }
        }

        var strLen = string.length

        if (length > strLen / 2) {
          length = strLen / 2
        }
        for (var i = 0; i < length; ++i) {
          var parsed = parseInt(string.substr(i * 2, 2), 16)
          if (numberIsNaN(parsed)) return i
          buf[offset + i] = parsed
        }
        return i
      }

      function utf8Write (buf, string, offset, length) {
        return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)
      }

      function asciiWrite (buf, string, offset, length) {
        return blitBuffer(asciiToBytes(string), buf, offset, length)
      }

      function latin1Write (buf, string, offset, length) {
        return asciiWrite(buf, string, offset, length)
      }

      function base64Write (buf, string, offset, length) {
        return blitBuffer(base64ToBytes(string), buf, offset, length)
      }

      function ucs2Write (buf, string, offset, length) {
        return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)
      }

      Buffer.prototype.write = function write (string, offset, length, encoding) {
        // Buffer#write(string)
        if (offset === undefined) {
          encoding = 'utf8'
          length = this.length
          offset = 0
          // Buffer#write(string, encoding)
        } else if (length === undefined && typeof offset === 'string') {
          encoding = offset
          length = this.length
          offset = 0
          // Buffer#write(string, offset[, length][, encoding])
        } else if (isFinite(offset)) {
          offset = offset >>> 0
          if (isFinite(length)) {
            length = length >>> 0
            if (encoding === undefined) encoding = 'utf8'
          } else {
            encoding = length
            length = undefined
          }
        } else {
          throw new Error(
            'Buffer.write(string, encoding, offset[, length]) is no longer supported'
          )
        }

        var remaining = this.length - offset
        if (length === undefined || length > remaining) length = remaining

        if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {
          throw new RangeError('Attempt to write outside buffer bounds')
        }

        if (!encoding) encoding = 'utf8'

        var loweredCase = false
        for (;;) {
          switch (encoding) {
            case 'hex':
              return hexWrite(this, string, offset, length)

            case 'utf8':
            case 'utf-8':
              return utf8Write(this, string, offset, length)

            case 'ascii':
              return asciiWrite(this, string, offset, length)

            case 'latin1':
            case 'binary':
              return latin1Write(this, string, offset, length)

            case 'base64':
              // Warning: maxLength not taken into account in base64Write
              return base64Write(this, string, offset, length)

            case 'ucs2':
            case 'ucs-2':
            case 'utf16le':
            case 'utf-16le':
              return ucs2Write(this, string, offset, length)

            default:
              if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)
              encoding = ('' + encoding).toLowerCase()
              loweredCase = true
          }
        }
      }

      Buffer.prototype.toJSON = function toJSON () {
        return {
          type: 'Buffer',
          data: Array.prototype.slice.call(this._arr || this, 0)
        }
      }

      function base64Slice (buf, start, end) {
        if (start === 0 && end === buf.length) {
          return base64.fromByteArray(buf)
        } else {
          return base64.fromByteArray(buf.slice(start, end))
        }
      }

      function utf8Slice (buf, start, end) {
        end = Math.min(buf.length, end)
        var res = []

        var i = start
        while (i < end) {
          var firstByte = buf[i]
          var codePoint = null
          var bytesPerSequence = (firstByte > 0xEF) ? 4
            : (firstByte > 0xDF) ? 3
              : (firstByte > 0xBF) ? 2
                : 1

          if (i + bytesPerSequence <= end) {
            var secondByte, thirdByte, fourthByte, tempCodePoint

            switch (bytesPerSequence) {
              case 1:
                if (firstByte < 0x80) {
                  codePoint = firstByte
                }
                break
              case 2:
                secondByte = buf[i + 1]
                if ((secondByte & 0xC0) === 0x80) {
                  tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)
                  if (tempCodePoint > 0x7F) {
                    codePoint = tempCodePoint
                  }
                }
                break
              case 3:
                secondByte = buf[i + 1]
                thirdByte = buf[i + 2]
                if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {
                  tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)
                  if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {
                    codePoint = tempCodePoint
                  }
                }
                break
              case 4:
                secondByte = buf[i + 1]
                thirdByte = buf[i + 2]
                fourthByte = buf[i + 3]
                if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {
                  tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)
                  if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {
                    codePoint = tempCodePoint
                  }
                }
            }
          }

          if (codePoint === null) {
            // we did not generate a valid codePoint so insert a
            // replacement char (U+FFFD) and advance only 1 byte
            codePoint = 0xFFFD
            bytesPerSequence = 1
          } else if (codePoint > 0xFFFF) {
            // encode to utf16 (surrogate pair dance)
            codePoint -= 0x10000
            res.push(codePoint >>> 10 & 0x3FF | 0xD800)
            codePoint = 0xDC00 | codePoint & 0x3FF
          }

          res.push(codePoint)
          i += bytesPerSequence
        }

        return decodeCodePointsArray(res)
      }

// Based on http://stackoverflow.com/a/22747272/680742, the browser with
// the lowest limit is Chrome, with 0x10000 args.
// We go 1 magnitude less, for safety
      var MAX_ARGUMENTS_LENGTH = 0x1000

      function decodeCodePointsArray (codePoints) {
        var len = codePoints.length
        if (len <= MAX_ARGUMENTS_LENGTH) {
          return String.fromCharCode.apply(String, codePoints) // avoid extra slice()
        }

        // Decode in chunks to avoid "call stack size exceeded".
        var res = ''
        var i = 0
        while (i < len) {
          res += String.fromCharCode.apply(
            String,
            codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)
          )
        }
        return res
      }

      function asciiSlice (buf, start, end) {
        var ret = ''
        end = Math.min(buf.length, end)

        for (var i = start; i < end; ++i) {
          ret += String.fromCharCode(buf[i] & 0x7F)
        }
        return ret
      }

      function latin1Slice (buf, start, end) {
        var ret = ''
        end = Math.min(buf.length, end)

        for (var i = start; i < end; ++i) {
          ret += String.fromCharCode(buf[i])
        }
        return ret
      }

      function hexSlice (buf, start, end) {
        var len = buf.length

        if (!start || start < 0) start = 0
        if (!end || end < 0 || end > len) end = len

        var out = ''
        for (var i = start; i < end; ++i) {
          out += hexSliceLookupTable[buf[i]]
        }
        return out
      }

      function utf16leSlice (buf, start, end) {
        var bytes = buf.slice(start, end)
        var res = ''
        for (var i = 0; i < bytes.length; i += 2) {
          res += String.fromCharCode(bytes[i] + (bytes[i + 1] * 256))
        }
        return res
      }

      Buffer.prototype.slice = function slice (start, end) {
        var len = this.length
        start = ~~start
        end = end === undefined ? len : ~~end

        if (start < 0) {
          start += len
          if (start < 0) start = 0
        } else if (start > len) {
          start = len
        }

        if (end < 0) {
          end += len
          if (end < 0) end = 0
        } else if (end > len) {
          end = len
        }

        if (end < start) end = start

        var newBuf = this.subarray(start, end)
        // Return an augmented `Uint8Array` instance
        Object.setPrototypeOf(newBuf, Buffer.prototype)

        return newBuf
      }

      /*
 * Need to make sure that buffer isn't trying to write out of bounds.
 */
      function checkOffset (offset, ext, length) {
        if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')
        if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')
      }

      Buffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {
        offset = offset >>> 0
        byteLength = byteLength >>> 0
        if (!noAssert) checkOffset(offset, byteLength, this.length)

        var val = this[offset]
        var mul = 1
        var i = 0
        while (++i < byteLength && (mul *= 0x100)) {
          val += this[offset + i] * mul
        }

        return val
      }

      Buffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {
        offset = offset >>> 0
        byteLength = byteLength >>> 0
        if (!noAssert) {
          checkOffset(offset, byteLength, this.length)
        }

        var val = this[offset + --byteLength]
        var mul = 1
        while (byteLength > 0 && (mul *= 0x100)) {
          val += this[offset + --byteLength] * mul
        }

        return val
      }

      Buffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {
        offset = offset >>> 0
        if (!noAssert) checkOffset(offset, 1, this.length)
        return this[offset]
      }

      Buffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {
        offset = offset >>> 0
        if (!noAssert) checkOffset(offset, 2, this.length)
        return this[offset] | (this[offset + 1] << 8)
      }

      Buffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {
        offset = offset >>> 0
        if (!noAssert) checkOffset(offset, 2, this.length)
        return (this[offset] << 8) | this[offset + 1]
      }

      Buffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {
        offset = offset >>> 0
        if (!noAssert) checkOffset(offset, 4, this.length)

        return ((this[offset]) |
          (this[offset + 1] << 8) |
          (this[offset + 2] << 16)) +
          (this[offset + 3] * 0x1000000)
      }

      Buffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {
        offset = offset >>> 0
        if (!noAssert) checkOffset(offset, 4, this.length)

        return (this[offset] * 0x1000000) +
          ((this[offset + 1] << 16) |
            (this[offset + 2] << 8) |
            this[offset + 3])
      }

      Buffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {
        offset = offset >>> 0
        byteLength = byteLength >>> 0
        if (!noAssert) checkOffset(offset, byteLength, this.length)

        var val = this[offset]
        var mul = 1
        var i = 0
        while (++i < byteLength && (mul *= 0x100)) {
          val += this[offset + i] * mul
        }
        mul *= 0x80

        if (val >= mul) val -= Math.pow(2, 8 * byteLength)

        return val
      }

      Buffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {
        offset = offset >>> 0
        byteLength = byteLength >>> 0
        if (!noAssert) checkOffset(offset, byteLength, this.length)

        var i = byteLength
        var mul = 1
        var val = this[offset + --i]
        while (i > 0 && (mul *= 0x100)) {
          val += this[offset + --i] * mul
        }
        mul *= 0x80

        if (val >= mul) val -= Math.pow(2, 8 * byteLength)

        return val
      }

      Buffer.prototype.readInt8 = function readInt8 (offset, noAssert) {
        offset = offset >>> 0
        if (!noAssert) checkOffset(offset, 1, this.length)
        if (!(this[offset] & 0x80)) return (this[offset])
        return ((0xff - this[offset] + 1) * -1)
      }

      Buffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {
        offset = offset >>> 0
        if (!noAssert) checkOffset(offset, 2, this.length)
        var val = this[offset] | (this[offset + 1] << 8)
        return (val & 0x8000) ? val | 0xFFFF0000 : val
      }

      Buffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {
        offset = offset >>> 0
        if (!noAssert) checkOffset(offset, 2, this.length)
        var val = this[offset + 1] | (this[offset] << 8)
        return (val & 0x8000) ? val | 0xFFFF0000 : val
      }

      Buffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {
        offset = offset >>> 0
        if (!noAssert) checkOffset(offset, 4, this.length)

        return (this[offset]) |
          (this[offset + 1] << 8) |
          (this[offset + 2] << 16) |
          (this[offset + 3] << 24)
      }

      Buffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {
        offset = offset >>> 0
        if (!noAssert) checkOffset(offset, 4, this.length)

        return (this[offset] << 24) |
          (this[offset + 1] << 16) |
          (this[offset + 2] << 8) |
          (this[offset + 3])
      }

      Buffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {
        offset = offset >>> 0
        if (!noAssert) checkOffset(offset, 4, this.length)
        return ieee754.read(this, offset, true, 23, 4)
      }

      Buffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {
        offset = offset >>> 0
        if (!noAssert) checkOffset(offset, 4, this.length)
        return ieee754.read(this, offset, false, 23, 4)
      }

      Buffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {
        offset = offset >>> 0
        if (!noAssert) checkOffset(offset, 8, this.length)
        return ieee754.read(this, offset, true, 52, 8)
      }

      Buffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {
        offset = offset >>> 0
        if (!noAssert) checkOffset(offset, 8, this.length)
        return ieee754.read(this, offset, false, 52, 8)
      }

      function checkInt (buf, value, offset, ext, max, min) {
        if (!Buffer.isBuffer(buf)) throw new TypeError('"buffer" argument must be a Buffer instance')
        if (value > max || value < min) throw new RangeError('"value" argument is out of bounds')
        if (offset + ext > buf.length) throw new RangeError('Index out of range')
      }

      Buffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {
        value = +value
        offset = offset >>> 0
        byteLength = byteLength >>> 0
        if (!noAssert) {
          var maxBytes = Math.pow(2, 8 * byteLength) - 1
          checkInt(this, value, offset, byteLength, maxBytes, 0)
        }

        var mul = 1
        var i = 0
        this[offset] = value & 0xFF
        while (++i < byteLength && (mul *= 0x100)) {
          this[offset + i] = (value / mul) & 0xFF
        }

        return offset + byteLength
      }

      Buffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {
        value = +value
        offset = offset >>> 0
        byteLength = byteLength >>> 0
        if (!noAssert) {
          var maxBytes = Math.pow(2, 8 * byteLength) - 1
          checkInt(this, value, offset, byteLength, maxBytes, 0)
        }

        var i = byteLength - 1
        var mul = 1
        this[offset + i] = value & 0xFF
        while (--i >= 0 && (mul *= 0x100)) {
          this[offset + i] = (value / mul) & 0xFF
        }

        return offset + byteLength
      }

      Buffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {
        value = +value
        offset = offset >>> 0
        if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)
        this[offset] = (value & 0xff)
        return offset + 1
      }

      Buffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {
        value = +value
        offset = offset >>> 0
        if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)
        this[offset] = (value & 0xff)
        this[offset + 1] = (value >>> 8)
        return offset + 2
      }

      Buffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {
        value = +value
        offset = offset >>> 0
        if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)
        this[offset] = (value >>> 8)
        this[offset + 1] = (value & 0xff)
        return offset + 2
      }

      Buffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {
        value = +value
        offset = offset >>> 0
        if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)
        this[offset + 3] = (value >>> 24)
        this[offset + 2] = (value >>> 16)
        this[offset + 1] = (value >>> 8)
        this[offset] = (value & 0xff)
        return offset + 4
      }

      Buffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {
        value = +value
        offset = offset >>> 0
        if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)
        this[offset] = (value >>> 24)
        this[offset + 1] = (value >>> 16)
        this[offset + 2] = (value >>> 8)
        this[offset + 3] = (value & 0xff)
        return offset + 4
      }

      Buffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {
        value = +value
        offset = offset >>> 0
        if (!noAssert) {
          var limit = Math.pow(2, (8 * byteLength) - 1)

          checkInt(this, value, offset, byteLength, limit - 1, -limit)
        }

        var i = 0
        var mul = 1
        var sub = 0
        this[offset] = value & 0xFF
        while (++i < byteLength && (mul *= 0x100)) {
          if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {
            sub = 1
          }
          this[offset + i] = ((value / mul) >> 0) - sub & 0xFF
        }

        return offset + byteLength
      }

      Buffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {
        value = +value
        offset = offset >>> 0
        if (!noAssert) {
          var limit = Math.pow(2, (8 * byteLength) - 1)

          checkInt(this, value, offset, byteLength, limit - 1, -limit)
        }

        var i = byteLength - 1
        var mul = 1
        var sub = 0
        this[offset + i] = value & 0xFF
        while (--i >= 0 && (mul *= 0x100)) {
          if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {
            sub = 1
          }
          this[offset + i] = ((value / mul) >> 0) - sub & 0xFF
        }

        return offset + byteLength
      }

      Buffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {
        value = +value
        offset = offset >>> 0
        if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)
        if (value < 0) value = 0xff + value + 1
        this[offset] = (value & 0xff)
        return offset + 1
      }

      Buffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {
        value = +value
        offset = offset >>> 0
        if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)
        this[offset] = (value & 0xff)
        this[offset + 1] = (value >>> 8)
        return offset + 2
      }

      Buffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {
        value = +value
        offset = offset >>> 0
        if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)
        this[offset] = (value >>> 8)
        this[offset + 1] = (value & 0xff)
        return offset + 2
      }

      Buffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {
        value = +value
        offset = offset >>> 0
        if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)
        this[offset] = (value & 0xff)
        this[offset + 1] = (value >>> 8)
        this[offset + 2] = (value >>> 16)
        this[offset + 3] = (value >>> 24)
        return offset + 4
      }

      Buffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {
        value = +value
        offset = offset >>> 0
        if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)
        if (value < 0) value = 0xffffffff + value + 1
        this[offset] = (value >>> 24)
        this[offset + 1] = (value >>> 16)
        this[offset + 2] = (value >>> 8)
        this[offset + 3] = (value & 0xff)
        return offset + 4
      }

      function checkIEEE754 (buf, value, offset, ext, max, min) {
        if (offset + ext > buf.length) throw new RangeError('Index out of range')
        if (offset < 0) throw new RangeError('Index out of range')
      }

      function writeFloat (buf, value, offset, littleEndian, noAssert) {
        value = +value
        offset = offset >>> 0
        if (!noAssert) {
          checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)
        }
        ieee754.write(buf, value, offset, littleEndian, 23, 4)
        return offset + 4
      }

      Buffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {
        return writeFloat(this, value, offset, true, noAssert)
      }

      Buffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {
        return writeFloat(this, value, offset, false, noAssert)
      }

      function writeDouble (buf, value, offset, littleEndian, noAssert) {
        value = +value
        offset = offset >>> 0
        if (!noAssert) {
          checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)
        }
        ieee754.write(buf, value, offset, littleEndian, 52, 8)
        return offset + 8
      }

      Buffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {
        return writeDouble(this, value, offset, true, noAssert)
      }

      Buffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {
        return writeDouble(this, value, offset, false, noAssert)
      }

// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)
      Buffer.prototype.copy = function copy (target, targetStart, start, end) {
        if (!Buffer.isBuffer(target)) throw new TypeError('argument should be a Buffer')
        if (!start) start = 0
        if (!end && end !== 0) end = this.length
        if (targetStart >= target.length) targetStart = target.length
        if (!targetStart) targetStart = 0
        if (end > 0 && end < start) end = start

        // Copy 0 bytes; we're done
        if (end === start) return 0
        if (target.length === 0 || this.length === 0) return 0

        // Fatal error conditions
        if (targetStart < 0) {
          throw new RangeError('targetStart out of bounds')
        }
        if (start < 0 || start >= this.length) throw new RangeError('Index out of range')
        if (end < 0) throw new RangeError('sourceEnd out of bounds')

        // Are we oob?
        if (end > this.length) end = this.length
        if (target.length - targetStart < end - start) {
          end = target.length - targetStart + start
        }

        var len = end - start

        if (this === target && typeof Uint8Array.prototype.copyWithin === 'function') {
          // Use built-in when available, missing from IE11
          this.copyWithin(targetStart, start, end)
        } else if (this === target && start < targetStart && targetStart < end) {
          // descending copy from end
          for (var i = len - 1; i >= 0; --i) {
            target[i + targetStart] = this[i + start]
          }
        } else {
          Uint8Array.prototype.set.call(
            target,
            this.subarray(start, end),
            targetStart
          )
        }

        return len
      }

// Usage:
//    buffer.fill(number[, offset[, end]])
//    buffer.fill(buffer[, offset[, end]])
//    buffer.fill(string[, offset[, end]][, encoding])
      Buffer.prototype.fill = function fill (val, start, end, encoding) {
        // Handle string cases:
        if (typeof val === 'string') {
          if (typeof start === 'string') {
            encoding = start
            start = 0
            end = this.length
          } else if (typeof end === 'string') {
            encoding = end
            end = this.length
          }
          if (encoding !== undefined && typeof encoding !== 'string') {
            throw new TypeError('encoding must be a string')
          }
          if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {
            throw new TypeError('Unknown encoding: ' + encoding)
          }
          if (val.length === 1) {
            var code = val.charCodeAt(0)
            if ((encoding === 'utf8' && code < 128) ||
              encoding === 'latin1') {
              // Fast path: If `val` fits into a single byte, use that numeric value.
              val = code
            }
          }
        } else if (typeof val === 'number') {
          val = val & 255
        } else if (typeof val === 'boolean') {
          val = Number(val)
        }

        // Invalid ranges are not set to a default, so can range check early.
        if (start < 0 || this.length < start || this.length < end) {
          throw new RangeError('Out of range index')
        }

        if (end <= start) {
          return this
        }

        start = start >>> 0
        end = end === undefined ? this.length : end >>> 0

        if (!val) val = 0

        var i
        if (typeof val === 'number') {
          for (i = start; i < end; ++i) {
            this[i] = val
          }
        } else {
          var bytes = Buffer.isBuffer(val)
            ? val
            : Buffer.from(val, encoding)
          var len = bytes.length
          if (len === 0) {
            throw new TypeError('The value "' + val +
              '" is invalid for argument "value"')
          }
          for (i = 0; i < end - start; ++i) {
            this[i + start] = bytes[i % len]
          }
        }

        return this
      }

// HELPER FUNCTIONS
// ================

      var INVALID_BASE64_RE = /[^+/0-9A-Za-z-_]/g

      function base64clean (str) {
        // Node takes equal signs as end of the Base64 encoding
        str = str.split('=')[0]
        // Node strips out invalid characters like \n and \t from the string, base64-js does not
        str = str.trim().replace(INVALID_BASE64_RE, '')
        // Node converts strings with length < 2 to ''
        if (str.length < 2) return ''
        // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not
        while (str.length % 4 !== 0) {
          str = str + '='
        }
        return str
      }

      function utf8ToBytes (string, units) {
        units = units || Infinity
        var codePoint
        var length = string.length
        var leadSurrogate = null
        var bytes = []

        for (var i = 0; i < length; ++i) {
          codePoint = string.charCodeAt(i)

          // is surrogate component
          if (codePoint > 0xD7FF && codePoint < 0xE000) {
            // last char was a lead
            if (!leadSurrogate) {
              // no lead yet
              if (codePoint > 0xDBFF) {
                // unexpected trail
                if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
                continue
              } else if (i + 1 === length) {
                // unpaired lead
                if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
                continue
              }

              // valid lead
              leadSurrogate = codePoint

              continue
            }

            // 2 leads in a row
            if (codePoint < 0xDC00) {
              if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
              leadSurrogate = codePoint
              continue
            }

            // valid surrogate pair
            codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000
          } else if (leadSurrogate) {
            // valid bmp char, but last char was a lead
            if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
          }

          leadSurrogate = null

          // encode utf8
          if (codePoint < 0x80) {
            if ((units -= 1) < 0) break
            bytes.push(codePoint)
          } else if (codePoint < 0x800) {
            if ((units -= 2) < 0) break
            bytes.push(
              codePoint >> 0x6 | 0xC0,
              codePoint & 0x3F | 0x80
            )
          } else if (codePoint < 0x10000) {
            if ((units -= 3) < 0) break
            bytes.push(
              codePoint >> 0xC | 0xE0,
              codePoint >> 0x6 & 0x3F | 0x80,
              codePoint & 0x3F | 0x80
            )
          } else if (codePoint < 0x110000) {
            if ((units -= 4) < 0) break
            bytes.push(
              codePoint >> 0x12 | 0xF0,
              codePoint >> 0xC & 0x3F | 0x80,
              codePoint >> 0x6 & 0x3F | 0x80,
              codePoint & 0x3F | 0x80
            )
          } else {
            throw new Error('Invalid code point')
          }
        }

        return bytes
      }

      function asciiToBytes (str) {
        var byteArray = []
        for (var i = 0; i < str.length; ++i) {
          // Node's code seems to be doing this and not & 0x7F..
          byteArray.push(str.charCodeAt(i) & 0xFF)
        }
        return byteArray
      }

      function utf16leToBytes (str, units) {
        var c, hi, lo
        var byteArray = []
        for (var i = 0; i < str.length; ++i) {
          if ((units -= 2) < 0) break

          c = str.charCodeAt(i)
          hi = c >> 8
          lo = c % 256
          byteArray.push(lo)
          byteArray.push(hi)
        }

        return byteArray
      }

      function base64ToBytes (str) {
        return base64.toByteArray(base64clean(str))
      }

      function blitBuffer (src, dst, offset, length) {
        for (var i = 0; i < length; ++i) {
          if ((i + offset >= dst.length) || (i >= src.length)) break
          dst[i + offset] = src[i]
        }
        return i
      }

// ArrayBuffer or Uint8Array objects from other contexts (i.e. iframes) do not pass
// the `instanceof` check but they should be treated as of that type.
// See: https://github.com/feross/buffer/issues/166
      function isInstance (obj, type) {
        return obj instanceof type ||
          (obj != null && obj.constructor != null && obj.constructor.name != null &&
            obj.constructor.name === type.name)
      }
      function numberIsNaN (obj) {
        // For IE11 support
        return obj !== obj // eslint-disable-line no-self-compare
      }

// Create lookup table for `toString('hex')`
// See: https://github.com/feross/buffer/issues/219
      var hexSliceLookupTable = (function () {
        var alphabet = '0123456789abcdef'
        var table = new Array(256)
        for (var i = 0; i < 16; ++i) {
          var i16 = i * 16
          for (var j = 0; j < 16; ++j) {
            table[i16 + j] = alphabet[i] + alphabet[j]
          }
        }
        return table
      })()

    }).call(this,require("buffer").Buffer)
  },{"base64-js":10,"buffer":11,"ieee754":14}],12:[function(require,module,exports){
    'use strict';

    /**
     * Module dependenices
     */

    const clone = require('shallow-clone');
    const typeOf = require('kind-of');
    const isPlainObject = require('is-plain-object');

    function cloneDeep(val, instanceClone) {
      switch (typeOf(val)) {
        case 'object':
          return cloneObjectDeep(val, instanceClone);
        case 'array':
          return cloneArrayDeep(val, instanceClone);
        default: {
          return clone(val);
        }
      }
    }

    function cloneObjectDeep(val, instanceClone) {
      if (typeof instanceClone === 'function') {
        return instanceClone(val);
      }
      if (instanceClone || isPlainObject(val)) {
        const res = new val.constructor();
        for (let key in val) {
          res[key] = cloneDeep(val[key], instanceClone);
        }
        return res;
      }
      return val;
    }

    function cloneArrayDeep(val, instanceClone) {
      const res = new val.constructor(val.length);
      for (let i = 0; i < val.length; i++) {
        res[i] = cloneDeep(val[i], instanceClone);
      }
      return res;
    }

    /**
     * Expose `cloneDeep`
     */

    module.exports = cloneDeep;

  },{"is-plain-object":15,"kind-of":17,"shallow-clone":26}],13:[function(require,module,exports){
    class ConsumableStream {
      async next(timeout) {
        let asyncIterator = this.createConsumer(timeout);
        let result = await asyncIterator.next();
        asyncIterator.return();
        return result;
      }

      async once(timeout) {
        let result = await this.next(timeout);
        if (result.done) {
          // If stream was ended, this function should never resolve.
          await new Promise(() => {});
        }
        return result.value;
      }

      createConsumer() {
        throw new TypeError('Method must be overriden by subclass');
      }

      [Symbol.asyncIterator]() {
        return this.createConsumer();
      }
    }

    module.exports = ConsumableStream;

  },{}],14:[function(require,module,exports){
    exports.read = function (buffer, offset, isLE, mLen, nBytes) {
      var e, m
      var eLen = (nBytes * 8) - mLen - 1
      var eMax = (1 << eLen) - 1
      var eBias = eMax >> 1
      var nBits = -7
      var i = isLE ? (nBytes - 1) : 0
      var d = isLE ? -1 : 1
      var s = buffer[offset + i]

      i += d

      e = s & ((1 << (-nBits)) - 1)
      s >>= (-nBits)
      nBits += eLen
      for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}

      m = e & ((1 << (-nBits)) - 1)
      e >>= (-nBits)
      nBits += mLen
      for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}

      if (e === 0) {
        e = 1 - eBias
      } else if (e === eMax) {
        return m ? NaN : ((s ? -1 : 1) * Infinity)
      } else {
        m = m + Math.pow(2, mLen)
        e = e - eBias
      }
      return (s ? -1 : 1) * m * Math.pow(2, e - mLen)
    }

    exports.write = function (buffer, value, offset, isLE, mLen, nBytes) {
      var e, m, c
      var eLen = (nBytes * 8) - mLen - 1
      var eMax = (1 << eLen) - 1
      var eBias = eMax >> 1
      var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)
      var i = isLE ? 0 : (nBytes - 1)
      var d = isLE ? 1 : -1
      var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0

      value = Math.abs(value)

      if (isNaN(value) || value === Infinity) {
        m = isNaN(value) ? 1 : 0
        e = eMax
      } else {
        e = Math.floor(Math.log(value) / Math.LN2)
        if (value * (c = Math.pow(2, -e)) < 1) {
          e--
          c *= 2
        }
        if (e + eBias >= 1) {
          value += rt / c
        } else {
          value += rt * Math.pow(2, 1 - eBias)
        }
        if (value * c >= 2) {
          e++
          c /= 2
        }

        if (e + eBias >= eMax) {
          m = 0
          e = eMax
        } else if (e + eBias >= 1) {
          m = ((value * c) - 1) * Math.pow(2, mLen)
          e = e + eBias
        } else {
          m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)
          e = 0
        }
      }

      for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}

      e = (e << mLen) | m
      eLen += mLen
      for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}

      buffer[offset + i - d] |= s * 128
    }

  },{}],15:[function(require,module,exports){
    /*!
 * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */

    'use strict';

    var isObject = require('isobject');

    function isObjectObject(o) {
      return isObject(o) === true
        && Object.prototype.toString.call(o) === '[object Object]';
    }

    module.exports = function isPlainObject(o) {
      var ctor,prot;

      if (isObjectObject(o) === false) return false;

      // If has modified constructor
      ctor = o.constructor;
      if (typeof ctor !== 'function') return false;

      // If has modified prototype
      prot = ctor.prototype;
      if (isObjectObject(prot) === false) return false;

      // If constructor does not have an Object-specific method
      if (prot.hasOwnProperty('isPrototypeOf') === false) {
        return false;
      }

      // Most likely a plain Object
      return true;
    };

  },{"isobject":16}],16:[function(require,module,exports){
    /*!
 * isobject <https://github.com/jonschlinkert/isobject>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */

    'use strict';

    module.exports = function isObject(val) {
      return val != null && typeof val === 'object' && Array.isArray(val) === false;
    };

  },{}],17:[function(require,module,exports){
    var toString = Object.prototype.toString;

    module.exports = function kindOf(val) {
      if (val === void 0) return 'undefined';
      if (val === null) return 'null';

      var type = typeof val;
      if (type === 'boolean') return 'boolean';
      if (type === 'string') return 'string';
      if (type === 'number') return 'number';
      if (type === 'symbol') return 'symbol';
      if (type === 'function') {
        return isGeneratorFn(val) ? 'generatorfunction' : 'function';
      }

      if (isArray(val)) return 'array';
      if (isBuffer(val)) return 'buffer';
      if (isArguments(val)) return 'arguments';
      if (isDate(val)) return 'date';
      if (isError(val)) return 'error';
      if (isRegexp(val)) return 'regexp';

      switch (ctorName(val)) {
        case 'Symbol': return 'symbol';
        case 'Promise': return 'promise';

        // Set, Map, WeakSet, WeakMap
        case 'WeakMap': return 'weakmap';
        case 'WeakSet': return 'weakset';
        case 'Map': return 'map';
        case 'Set': return 'set';

        // 8-bit typed arrays
        case 'Int8Array': return 'int8array';
        case 'Uint8Array': return 'uint8array';
        case 'Uint8ClampedArray': return 'uint8clampedarray';

        // 16-bit typed arrays
        case 'Int16Array': return 'int16array';
        case 'Uint16Array': return 'uint16array';

        // 32-bit typed arrays
        case 'Int32Array': return 'int32array';
        case 'Uint32Array': return 'uint32array';
        case 'Float32Array': return 'float32array';
        case 'Float64Array': return 'float64array';
      }

      if (isGeneratorObj(val)) {
        return 'generator';
      }

      // Non-plain objects
      type = toString.call(val);
      switch (type) {
        case '[object Object]': return 'object';
        // iterators
        case '[object Map Iterator]': return 'mapiterator';
        case '[object Set Iterator]': return 'setiterator';
        case '[object String Iterator]': return 'stringiterator';
        case '[object Array Iterator]': return 'arrayiterator';
      }

      // other
      return type.slice(8, -1).toLowerCase().replace(/\s/g, '');
    };

    function ctorName(val) {
      return typeof val.constructor === 'function' ? val.constructor.name : null;
    }

    function isArray(val) {
      if (Array.isArray) return Array.isArray(val);
      return val instanceof Array;
    }

    function isError(val) {
      return val instanceof Error || (typeof val.message === 'string' && val.constructor && typeof val.constructor.stackTraceLimit === 'number');
    }

    function isDate(val) {
      if (val instanceof Date) return true;
      return typeof val.toDateString === 'function'
        && typeof val.getDate === 'function'
        && typeof val.setDate === 'function';
    }

    function isRegexp(val) {
      if (val instanceof RegExp) return true;
      return typeof val.flags === 'string'
        && typeof val.ignoreCase === 'boolean'
        && typeof val.multiline === 'boolean'
        && typeof val.global === 'boolean';
    }

    function isGeneratorFn(name, val) {
      return ctorName(name) === 'GeneratorFunction';
    }

    function isGeneratorObj(val) {
      return typeof val.throw === 'function'
        && typeof val.return === 'function'
        && typeof val.next === 'function';
    }

    function isArguments(val) {
      try {
        if (typeof val.length === 'number' && typeof val.callee === 'function') {
          return true;
        }
      } catch (err) {
        if (err.message.indexOf('callee') !== -1) {
          return true;
        }
      }
      return false;
    }

    /**
     * If you need to support Safari 5-7 (8-10 yr-old browser),
     * take a look at https://github.com/feross/is-buffer
     */

    function isBuffer(val) {
      if (val.constructor && typeof val.constructor.isBuffer === 'function') {
        return val.constructor.isBuffer(val);
      }
      return false;
    }

  },{}],18:[function(require,module,exports){
    'use strict';

    /**
     * Constants.
     */

    var errorMessage;

    errorMessage = 'An argument without append, prepend, ' +
      'or detach methods was given to `List';

    /**
     * Creates a new List: A linked list is a bit like an Array, but
     * knows nothing about how many items are in it, and knows only about its
     * first (`head`) and last (`tail`) items. Each item (e.g. `head`, `tail`,
     * &c.) knows which item comes before or after it (its more like the
     * implementation of the DOM in JavaScript).
     * @global
     * @private
     * @constructor
     * @class Represents an instance of List.
     */

    function List(/*items...*/) {
      if (arguments.length) {
        return List.from(arguments);
      }
    }

    var ListPrototype;

    ListPrototype = List.prototype;

    /**
     * Creates a new list from the arguments (each a list item) passed in.
     * @name List.of
     * @param {...ListItem} [items] - Zero or more items to attach.
     * @returns {list} - A new instance of List.
     */

    List.of = function (/*items...*/) {
      return List.from.call(this, arguments);
    };

    /**
     * Creates a new list from the given array-like object (each a list item)
     * passed in.
     * @name List.from
     * @param {ListItem[]} [items] - The items to append.
     * @returns {list} - A new instance of List.
     */
    List.from = function (items) {
      var list = new this(), length, iterator, item;

      if (items && (length = items.length)) {
        iterator = -1;

        while (++iterator < length) {
          item = items[iterator];

          if (item !== null && item !== undefined) {
            list.append(item);
          }
        }
      }

      return list;
    };

    /**
     * List#head
     * Default to `null`.
     */
    ListPrototype.head = null;

    /**
     * List#tail
     * Default to `null`.
     */
    ListPrototype.tail = null;

    /**
     * Returns the list's items as an array. This does *not* detach the items.
     * @name List#toArray
     * @returns {ListItem[]} - An array of (still attached) ListItems.
     */
    ListPrototype.toArray = function () {
      var item = this.head,
        result = [];

      while (item) {
        result.push(item);
        item = item.next;
      }

      return result;
    };

    /**
     * Prepends the given item to the list: Item will be the new first item
     * (`head`).
     * @name List#prepend
     * @param {ListItem} item - The item to prepend.
     * @returns {ListItem} - An instance of ListItem (the given item).
     */
    ListPrototype.prepend = function (item) {
      if (!item) {
        return false;
      }

      if (!item.append || !item.prepend || !item.detach) {
        throw new Error(errorMessage + '#prepend`.');
      }

      var self, head;

      // Cache self.
      self = this;

      // If self has a first item, defer prepend to the first items prepend
      // method, and return the result.
      head = self.head;

      if (head) {
        return head.prepend(item);
      }

      // ...otherwise, there is no `head` (or `tail`) item yet.

      // Detach the prependee.
      item.detach();

      // Set the prependees parent list to reference self.
      item.list = self;

      // Set self's first item to the prependee, and return the item.
      self.head = item;

      return item;
    };

    /**
     * Appends the given item to the list: Item will be the new last item (`tail`)
     * if the list had a first item, and its first item (`head`) otherwise.
     * @name List#append
     * @param {ListItem} item - The item to append.
     * @returns {ListItem} - An instance of ListItem (the given item).
     */

    ListPrototype.append = function (item) {
      if (!item) {
        return false;
      }

      if (!item.append || !item.prepend || !item.detach) {
        throw new Error(errorMessage + '#append`.');
      }

      var self, head, tail;

      // Cache self.
      self = this;

      // If self has a last item, defer appending to the last items append
      // method, and return the result.
      tail = self.tail;

      if (tail) {
        return tail.append(item);
      }

      // If self has a first item, defer appending to the first items append
      // method, and return the result.
      head = self.head;

      if (head) {
        return head.append(item);
      }

      // ...otherwise, there is no `tail` or `head` item yet.

      // Detach the appendee.
      item.detach();

      // Set the appendees parent list to reference self.
      item.list = self;

      // Set self's first item to the appendee, and return the item.
      self.head = item;

      return item;
    };

    /**
     * Creates a new ListItem: A linked list item is a bit like DOM node:
     * It knows only about its "parent" (`list`), the item before it (`prev`),
     * and the item after it (`next`).
     * @global
     * @private
     * @constructor
     * @class Represents an instance of ListItem.
     */

    function ListItem() {}

    List.Item = ListItem;

    var ListItemPrototype = ListItem.prototype;

    ListItemPrototype.next = null;

    ListItemPrototype.prev = null;

    ListItemPrototype.list = null;

    /**
     * Detaches the item operated on from its parent list.
     * @name ListItem#detach
     * @returns {ListItem} - The item operated on.
     */
    ListItemPrototype.detach = function () {
      // Cache self, the parent list, and the previous and next items.
      var self = this,
        list = self.list,
        prev = self.prev,
        next = self.next;

      // If the item is already detached, return self.
      if (!list) {
        return self;
      }

      // If self is the last item in the parent list, link the lists last item
      // to the previous item.
      if (list.tail === self) {
        list.tail = prev;
      }

      // If self is the first item in the parent list, link the lists first item
      // to the next item.
      if (list.head === self) {
        list.head = next;
      }

      // If both the last and first items in the parent list are the same,
      // remove the link to the last item.
      if (list.tail === list.head) {
        list.tail = null;
      }

      // If a previous item exists, link its next item to selfs next item.
      if (prev) {
        prev.next = next;
      }

      // If a next item exists, link its previous item to selfs previous item.
      if (next) {
        next.prev = prev;
      }

      // Remove links from self to both the next and previous items, and to the
      // parent list.
      self.prev = self.next = self.list = null;

      // Return self.
      return self;
    };

    /**
     * Prepends the given item *before* the item operated on.
     * @name ListItem#prepend
     * @param {ListItem} item - The item to prepend.
     * @returns {ListItem} - The item operated on, or false when that item is not
     * attached.
     */
    ListItemPrototype.prepend = function (item) {
      if (!item || !item.append || !item.prepend || !item.detach) {
        throw new Error(errorMessage + 'Item#prepend`.');
      }

      // Cache self, the parent list, and the previous item.
      var self = this,
        list = self.list,
        prev = self.prev;

      // If self is detached, return false.
      if (!list) {
        return false;
      }

      // Detach the prependee.
      item.detach();

      // If self has a previous item...
      if (prev) {
        // ...link the prependees previous item, to selfs previous item.
        item.prev = prev;

        // ...link the previous items next item, to self.
        prev.next = item;
      }

      // Set the prependees next item to self.
      item.next = self;

      // Set the prependees parent list to selfs parent list.
      item.list = list;

      // Set the previous item of self to the prependee.
      self.prev = item;

      // If self is the first item in the parent list, link the lists first item
      // to the prependee.
      if (self === list.head) {
        list.head = item;
      }

      // If the the parent list has no last item, link the lists last item to
      // self.
      if (!list.tail) {
        list.tail = self;
      }

      // Return the prependee.
      return item;
    };

    /**
     * Appends the given item *after* the item operated on.
     * @name ListItem#append
     * @param {ListItem} item - The item to append.
     * @returns {ListItem} - The item operated on, or false when that item is not
     * attached.
     */
    ListItemPrototype.append = function (item) {
      // If item is falsey, return false.
      if (!item || !item.append || !item.prepend || !item.detach) {
        throw new Error(errorMessage + 'Item#append`.');
      }

      // Cache self, the parent list, and the next item.
      var self = this,
        list = self.list,
        next = self.next;

      // If self is detached, return false.
      if (!list) {
        return false;
      }

      // Detach the appendee.
      item.detach();

      // If self has a next item...
      if (next) {
        // ...link the appendees next item, to selfs next item.
        item.next = next;

        // ...link the next items previous item, to the appendee.
        next.prev = item;
      }

      // Set the appendees previous item to self.
      item.prev = self;

      // Set the appendees parent list to selfs parent list.
      item.list = list;

      // Set the next item of self to the appendee.
      self.next = item;

      // If the the parent list has no last item or if self is the parent lists
      // last item, link the lists last item to the appendee.
      if (self === list.tail || !list.tail) {
        list.tail = item;
      }

      // Return the appendee.
      return item;
    };

    /**
     * Expose `List`.
     */

    module.exports = List;

  },{}],19:[function(require,module,exports){
    'use strict';

    module.exports = require('./_source/linked-list.js');

  },{"./_source/linked-list.js":18}],20:[function(require,module,exports){
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.

    'use strict';

// If obj.hasOwnProperty has been overridden, then calling
// obj.hasOwnProperty(prop) will break.
// See: https://github.com/joyent/node/issues/1707
    function hasOwnProperty(obj, prop) {
      return Object.prototype.hasOwnProperty.call(obj, prop);
    }

    module.exports = function(qs, sep, eq, options) {
      sep = sep || '&';
      eq = eq || '=';
      var obj = {};

      if (typeof qs !== 'string' || qs.length === 0) {
        return obj;
      }

      var regexp = /\+/g;
      qs = qs.split(sep);

      var maxKeys = 1000;
      if (options && typeof options.maxKeys === 'number') {
        maxKeys = options.maxKeys;
      }

      var len = qs.length;
      // maxKeys <= 0 means that we should not limit keys count
      if (maxKeys > 0 && len > maxKeys) {
        len = maxKeys;
      }

      for (var i = 0; i < len; ++i) {
        var x = qs[i].replace(regexp, '%20'),
          idx = x.indexOf(eq),
          kstr, vstr, k, v;

        if (idx >= 0) {
          kstr = x.substr(0, idx);
          vstr = x.substr(idx + 1);
        } else {
          kstr = x;
          vstr = '';
        }

        k = decodeURIComponent(kstr);
        v = decodeURIComponent(vstr);

        if (!hasOwnProperty(obj, k)) {
          obj[k] = v;
        } else if (isArray(obj[k])) {
          obj[k].push(v);
        } else {
          obj[k] = [obj[k], v];
        }
      }

      return obj;
    };

    var isArray = Array.isArray || function (xs) {
      return Object.prototype.toString.call(xs) === '[object Array]';
    };

  },{}],21:[function(require,module,exports){
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.

    'use strict';

    var stringifyPrimitive = function(v) {
      switch (typeof v) {
        case 'string':
          return v;

        case 'boolean':
          return v ? 'true' : 'false';

        case 'number':
          return isFinite(v) ? v : '';

        default:
          return '';
      }
    };

    module.exports = function(obj, sep, eq, name) {
      sep = sep || '&';
      eq = eq || '=';
      if (obj === null) {
        obj = undefined;
      }

      if (typeof obj === 'object') {
        return map(objectKeys(obj), function(k) {
          var ks = encodeURIComponent(stringifyPrimitive(k)) + eq;
          if (isArray(obj[k])) {
            return map(obj[k], function(v) {
              return ks + encodeURIComponent(stringifyPrimitive(v));
            }).join(sep);
          } else {
            return ks + encodeURIComponent(stringifyPrimitive(obj[k]));
          }
        }).join(sep);

      }

      if (!name) return '';
      return encodeURIComponent(stringifyPrimitive(name)) + eq +
        encodeURIComponent(stringifyPrimitive(obj));
    };

    var isArray = Array.isArray || function (xs) {
      return Object.prototype.toString.call(xs) === '[object Array]';
    };

    function map (xs, f) {
      if (xs.map) return xs.map(f);
      var res = [];
      for (var i = 0; i < xs.length; i++) {
        res.push(f(xs[i], i));
      }
      return res;
    }

    var objectKeys = Object.keys || function (obj) {
      var res = [];
      for (var key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) res.push(key);
      }
      return res;
    };

  },{}],22:[function(require,module,exports){
    'use strict';

    exports.decode = exports.parse = require('./decode');
    exports.encode = exports.stringify = require('./encode');

  },{"./decode":20,"./encode":21}],23:[function(require,module,exports){
// Based on https://github.com/dscape/cycle/blob/master/cycle.js

    module.exports = function decycle(object) {
// Make a deep copy of an object or array, assuring that there is at most
// one instance of each object or array in the resulting structure. The
// duplicate references (which might be forming cycles) are replaced with
// an object of the form
//      {$ref: PATH}
// where the PATH is a JSONPath string that locates the first occurance.
// So,
//      var a = [];
//      a[0] = a;
//      return JSON.stringify(JSON.decycle(a));
// produces the string '[{"$ref":"$"}]'.

// JSONPath is used to locate the unique object. $ indicates the top level of
// the object or array. [NUMBER] or [STRING] indicates a child member or
// property.

      var objects = [],   // Keep a reference to each unique object or array
        paths = [];     // Keep the path to each unique object or array

      return (function derez(value, path) {

// The derez recurses through the object, producing the deep copy.

        var i,          // The loop counter
          name,       // Property name
          nu;         // The new object or array

// typeof null === 'object', so go on if this value is really an object but not
// one of the weird builtin objects.

        if (typeof value === 'object' && value !== null &&
          !(value instanceof Boolean) &&
          !(value instanceof Date)    &&
          !(value instanceof Number)  &&
          !(value instanceof RegExp)  &&
          !(value instanceof String)) {

// If the value is an object or array, look to see if we have already
// encountered it. If so, return a $ref/path object. This is a hard way,
// linear search that will get slower as the number of unique objects grows.

          for (i = 0; i < objects.length; i += 1) {
            if (objects[i] === value) {
              return {$ref: paths[i]};
            }
          }

// Otherwise, accumulate the unique value and its path.

          objects.push(value);
          paths.push(path);

// If it is an array, replicate the array.

          if (Object.prototype.toString.apply(value) === '[object Array]') {
            nu = [];
            for (i = 0; i < value.length; i += 1) {
              nu[i] = derez(value[i], path + '[' + i + ']');
            }
          } else {

// If it is an object, replicate the object.

            nu = {};
            for (name in value) {
              if (Object.prototype.hasOwnProperty.call(value, name)) {
                nu[name] = derez(value[name],
                  path + '[' + JSON.stringify(name) + ']');
              }
            }
          }
          return nu;
        }
        return value;
      }(object, '$'));
    };

  },{}],24:[function(require,module,exports){
    var decycle = require('./decycle');

    var isStrict = (function () { return !this; })();

    function AuthTokenExpiredError(message, expiry) {
      this.name = 'AuthTokenExpiredError';
      this.message = message;
      this.expiry = expiry;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    AuthTokenExpiredError.prototype = Object.create(Error.prototype);


    function AuthTokenInvalidError(message) {
      this.name = 'AuthTokenInvalidError';
      this.message = message;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    AuthTokenInvalidError.prototype = Object.create(Error.prototype);


    function AuthTokenNotBeforeError(message, date) {
      this.name = 'AuthTokenNotBeforeError';
      this.message = message;
      this.date = date;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    AuthTokenNotBeforeError.prototype = Object.create(Error.prototype);


// For any other auth token error.
    function AuthTokenError(message) {
      this.name = 'AuthTokenError';
      this.message = message;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    AuthTokenError.prototype = Object.create(Error.prototype);

// For any other auth error; not specifically related to the auth token itself.
    function AuthError(message) {
      this.name = 'AuthError';
      this.message = message;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    AuthError.prototype = Object.create(Error.prototype);


    function SilentMiddlewareBlockedError(message, type) {
      this.name = 'SilentMiddlewareBlockedError';
      this.message = message;
      this.type = type;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    SilentMiddlewareBlockedError.prototype = Object.create(Error.prototype);


    function InvalidActionError(message) {
      this.name = 'InvalidActionError';
      this.message = message;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    InvalidActionError.prototype = Object.create(Error.prototype);

    function InvalidArgumentsError(message) {
      this.name = 'InvalidArgumentsError';
      this.message = message;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    InvalidArgumentsError.prototype = Object.create(Error.prototype);

    function InvalidOptionsError(message) {
      this.name = 'InvalidOptionsError';
      this.message = message;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    InvalidOptionsError.prototype = Object.create(Error.prototype);


    function InvalidMessageError(message) {
      this.name = 'InvalidMessageError';
      this.message = message;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    InvalidMessageError.prototype = Object.create(Error.prototype);


    function SocketProtocolError(message, code) {
      this.name = 'SocketProtocolError';
      this.message = message;
      this.code = code;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    SocketProtocolError.prototype = Object.create(Error.prototype);


    function ServerProtocolError(message) {
      this.name = 'ServerProtocolError';
      this.message = message;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    ServerProtocolError.prototype = Object.create(Error.prototype);

    function HTTPServerError(message) {
      this.name = 'HTTPServerError';
      this.message = message;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    HTTPServerError.prototype = Object.create(Error.prototype);


    function ResourceLimitError(message) {
      this.name = 'ResourceLimitError';
      this.message = message;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    ResourceLimitError.prototype = Object.create(Error.prototype);


    function TimeoutError(message) {
      this.name = 'TimeoutError';
      this.message = message;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    TimeoutError.prototype = Object.create(Error.prototype);


    function BadConnectionError(message, type) {
      this.name = 'BadConnectionError';
      this.message = message;
      this.type = type;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    BadConnectionError.prototype = Object.create(Error.prototype);


    function BrokerError(message) {
      this.name = 'BrokerError';
      this.message = message;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    BrokerError.prototype = Object.create(Error.prototype);


    function ProcessExitError(message, code) {
      this.name = 'ProcessExitError';
      this.message = message;
      this.code = code;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    ProcessExitError.prototype = Object.create(Error.prototype);


    function UnknownError(message) {
      this.name = 'UnknownError';
      this.message = message;
      if (Error.captureStackTrace && !isStrict) {
        Error.captureStackTrace(this, arguments.callee);
      } else {
        this.stack = (new Error()).stack;
      }
    }
    UnknownError.prototype = Object.create(Error.prototype);


// Expose all error types.

    module.exports = {
      AuthTokenExpiredError: AuthTokenExpiredError,
      AuthTokenInvalidError: AuthTokenInvalidError,
      AuthTokenNotBeforeError: AuthTokenNotBeforeError,
      AuthTokenError: AuthTokenError,
      AuthError: AuthError,
      SilentMiddlewareBlockedError: SilentMiddlewareBlockedError,
      InvalidActionError: InvalidActionError,
      InvalidArgumentsError: InvalidArgumentsError,
      InvalidOptionsError: InvalidOptionsError,
      InvalidMessageError: InvalidMessageError,
      SocketProtocolError: SocketProtocolError,
      ServerProtocolError: ServerProtocolError,
      HTTPServerError: HTTPServerError,
      ResourceLimitError: ResourceLimitError,
      TimeoutError: TimeoutError,
      BadConnectionError: BadConnectionError,
      BrokerError: BrokerError,
      ProcessExitError: ProcessExitError,
      UnknownError: UnknownError
    };

    module.exports.socketProtocolErrorStatuses = {
      1001: 'Socket was disconnected',
      1002: 'A WebSocket protocol error was encountered',
      1003: 'Server terminated socket because it received invalid data',
      1005: 'Socket closed without status code',
      1006: 'Socket hung up',
      1007: 'Message format was incorrect',
      1008: 'Encountered a policy violation',
      1009: 'Message was too big to process',
      1010: 'Client ended the connection because the server did not comply with extension requirements',
      1011: 'Server encountered an unexpected fatal condition',
      4000: 'Server ping timed out',
      4001: 'Client pong timed out',
      4002: 'Server failed to sign auth token',
      4003: 'Failed to complete handshake',
      4004: 'Client failed to save auth token',
      4005: 'Did not receive #handshake from client before timeout',
      4006: 'Failed to bind socket to message broker',
      4007: 'Client connection establishment timed out',
      4008: 'Server rejected handshake from client',
      4009: 'Server received a message before the client handshake'
    };

    module.exports.socketProtocolIgnoreStatuses = {
      1000: 'Socket closed normally',
      1001: 'Socket hung up'
    };

// Properties related to error domains cannot be serialized.
    var unserializableErrorProperties = {
      domain: 1,
      domainEmitter: 1,
      domainThrown: 1
    };

// Convert an error into a JSON-compatible type which can later be hydrated
// back to its *original* form.
    module.exports.dehydrateError = function dehydrateError(error, includeStackTrace) {
      var dehydratedError;

      if (error && typeof error === 'object') {
        dehydratedError = {
          message: error.message
        };
        if (includeStackTrace) {
          dehydratedError.stack = error.stack;
        }
        for (var i in error) {
          if (!unserializableErrorProperties[i]) {
            dehydratedError[i] = error[i];
          }
        }
      } else if (typeof error === 'function') {
        dehydratedError = '[function ' + (error.name || 'anonymous') + ']';
      } else {
        dehydratedError = error;
      }

      return decycle(dehydratedError);
    };

// Convert a dehydrated error back to its *original* form.
    module.exports.hydrateError = function hydrateError(error) {
      var hydratedError = null;
      if (error != null) {
        if (typeof error === 'object') {
          hydratedError = new Error(error.message);
          for (var i in error) {
            if (error.hasOwnProperty(i)) {
              hydratedError[i] = error[i];
            }
          }
        } else {
          hydratedError = error;
        }
      }
      return hydratedError;
    };

    module.exports.decycle = decycle;

  },{"./decycle":23}],25:[function(require,module,exports){
    (function (global){
      var base64Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
      var validJSONStartRegex = /^[ \n\r\t]*[{\[]/;

      var arrayBufferToBase64 = function (arraybuffer) {
        var bytes = new Uint8Array(arraybuffer);
        var len = bytes.length;
        var base64 = '';

        for (var i = 0; i < len; i += 3) {
          base64 += base64Chars[bytes[i] >> 2];
          base64 += base64Chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];
          base64 += base64Chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];
          base64 += base64Chars[bytes[i + 2] & 63];
        }

        if ((len % 3) === 2) {
          base64 = base64.substring(0, base64.length - 1) + '=';
        } else if (len % 3 === 1) {
          base64 = base64.substring(0, base64.length - 2) + '==';
        }

        return base64;
      };

      var binaryToBase64Replacer = function (key, value) {
        if (global.ArrayBuffer && value instanceof global.ArrayBuffer) {
          return {
            base64: true,
            data: arrayBufferToBase64(value)
          };
        } else if (global.Buffer) {
          if (value instanceof global.Buffer){
            return {
              base64: true,
              data: value.toString('base64')
            };
          }
          // Some versions of Node.js convert Buffers to Objects before they are passed to
          // the replacer function - Because of this, we need to rehydrate Buffers
          // before we can convert them to base64 strings.
          if (value && value.type === 'Buffer' && Array.isArray(value.data)) {
            var rehydratedBuffer;
            if (global.Buffer.from) {
              rehydratedBuffer = global.Buffer.from(value.data);
            } else {
              rehydratedBuffer = new global.Buffer(value.data);
            }
            return {
              base64: true,
              data: rehydratedBuffer.toString('base64')
            };
          }
        }
        return value;
      };

// Decode the data which was transmitted over the wire to a JavaScript Object in a format which SC understands.
// See encode function below for more details.
      module.exports.decode = function (input) {
        if (input == null) {
          return null;
        }
        // Leave ping or pong message as is
        if (input === '#1' || input === '#2') {
          return input;
        }
        var message = input.toString();

        // Performance optimization to detect invalid JSON packet sooner.
        if (!validJSONStartRegex.test(message)) {
          return message;
        }

        try {
          return JSON.parse(message);
        } catch (err) {}
        return message;
      };

// Encode a raw JavaScript object (which is in the SC protocol format) into a format for
// transfering it over the wire. In this case, we just convert it into a simple JSON string.
// If you want to create your own custom codec, you can encode the object into any format
// (e.g. binary ArrayBuffer or string with any kind of compression) so long as your decode
// function is able to rehydrate that object back into its original JavaScript Object format
// (which adheres to the SC protocol).
// See https://github.com/SocketCluster/socketcluster/blob/master/socketcluster-protocol.md
// for details about the SC protocol.
      module.exports.encode = function (object) {
        // Leave ping or pong message as is
        if (object === '#1' || object === '#2') {
          return object;
        }
        return JSON.stringify(object, binaryToBase64Replacer);
      };

    }).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})
  },{}],26:[function(require,module,exports){
    (function (Buffer){
      /*!
 * shallow-clone <https://github.com/jonschlinkert/shallow-clone>
 *
 * Copyright (c) 2015-present, Jon Schlinkert.
 * Released under the MIT License.
 */

      'use strict';

      const valueOf = Symbol.prototype.valueOf;
      const typeOf = require('kind-of');

      function clone(val, deep) {
        switch (typeOf(val)) {
          case 'array':
            return val.slice();
          case 'object':
            return Object.assign({}, val);
          case 'date':
            return new val.constructor(Number(val));
          case 'map':
            return new Map(val);
          case 'set':
            return new Set(val);
          case 'buffer':
            return cloneBuffer(val);
          case 'symbol':
            return cloneSymbol(val);
          case 'arraybuffer':
            return cloneArrayBuffer(val);
          case 'float32array':
          case 'float64array':
          case 'int16array':
          case 'int32array':
          case 'int8array':
          case 'uint16array':
          case 'uint32array':
          case 'uint8clampedarray':
          case 'uint8array':
            return cloneTypedArray(val);
          case 'regexp':
            return cloneRegExp(val);
          case 'error':
            return Object.create(val);
          default: {
            return val;
          }
        }
      }

      function cloneRegExp(val) {
        const flags = val.flags !== void 0 ? val.flags : (/\w+$/.exec(val) || void 0);
        const re = new val.constructor(val.source, flags);
        re.lastIndex = val.lastIndex;
        return re;
      }

      function cloneArrayBuffer(val) {
        const res = new val.constructor(val.byteLength);
        new Uint8Array(res).set(new Uint8Array(val));
        return res;
      }

      function cloneTypedArray(val, deep) {
        return new val.constructor(val.buffer, val.byteOffset, val.length);
      }

      function cloneBuffer(val) {
        const len = val.length;
        const buf = Buffer.allocUnsafe ? Buffer.allocUnsafe(len) : Buffer.from(len);
        val.copy(buf);
        return buf;
      }

      function cloneSymbol(val) {
        return valueOf ? Object(valueOf.call(val)) : {};
      }

      /**
       * Expose `clone`
       */

      module.exports = clone;

    }).call(this,require("buffer").Buffer)
  },{"buffer":11,"kind-of":17}],27:[function(require,module,exports){
    const ConsumableStream = require('consumable-stream');

    class DemuxedConsumableStream extends ConsumableStream {
      constructor(streamDemux, name) {
        super();
        this.name = name;
        this._streamDemux = streamDemux;
      }

      createConsumer(timeout) {
        return this._streamDemux.createConsumer(this.name, timeout);
      }
    }

    module.exports = DemuxedConsumableStream;

  },{"consumable-stream":13}],28:[function(require,module,exports){
    const WritableConsumableStream = require('writable-consumable-stream');
    const DemuxedConsumableStream = require('./demuxed-consumable-stream');

    class StreamDemux {
      constructor() {
        this._mainStream = new WritableConsumableStream();
      }

      write(streamName, value) {
        this._mainStream.write({
          stream: streamName,
          data: {
            value,
            done: false
          }
        });
      }

      close(streamName, value) {
        this._mainStream.write({
          stream: streamName,
          data: {
            value,
            done: true
          }
        });
      }

      closeAll(value) {
        this._mainStream.close(value);
      }

      writeToConsumer(consumerId, value) {
        this._mainStream.writeToConsumer(consumerId, {
          consumerId,
          data: {
            value,
            done: false
          }
        });
      }

      closeConsumer(consumerId, value) {
        this._mainStream.closeConsumer(consumerId, {
          consumerId,
          data: {
            value,
            done: true
          }
        });
      }

      getConsumerStats(consumerId) {
        return this._mainStream.getConsumerStats(consumerId);
      }

      getConsumerStatsList(streamName) {
        let consumerList = this._mainStream.getConsumerStatsList();
        return consumerList.filter((consumerStats) => {
          return consumerStats.stream === streamName;
        });
      }

      getConsumerStatsListAll() {
        return this._mainStream.getConsumerStatsList();
      }

      kill(streamName, value) {
        let consumerList = this.getConsumerStatsList(streamName);
        let len = consumerList.length;
        for (let i = 0; i < len; i++) {
          this.killConsumer(consumerList[i].id, value);
        }
      }

      killAll(value) {
        this._mainStream.kill(value);
      }

      killConsumer(consumerId, value) {
        this._mainStream.killConsumer(consumerId, value);
      }

      getBackpressure(streamName) {
        let consumerList = this.getConsumerStatsList(streamName);
        let len = consumerList.length;

        let maxBackpressure = 0;
        for (let i = 0; i < len; i++) {
          let consumer = consumerList[i];
          if (consumer.backpressure > maxBackpressure) {
            maxBackpressure = consumer.backpressure;
          }
        }
        return maxBackpressure;
      }

      getBackpressureAll() {
        return this._mainStream.getBackpressure();
      }

      getConsumerBackpressure(consumerId) {
        return this._mainStream.getConsumerBackpressure(consumerId);
      }

      hasConsumer(streamName, consumerId) {
        let consumerStats = this._mainStream.getConsumerStats(consumerId);
        return !!consumerStats && consumerStats.stream === streamName;
      }

      hasConsumerAll(consumerId) {
        return this._mainStream.hasConsumer(consumerId);
      }

      createConsumer(streamName, timeout) {
        let mainStreamConsumer = this._mainStream.createConsumer(timeout);

        let consumerNext = mainStreamConsumer.next;
        mainStreamConsumer.next = async function () {
          while (true) {
            let packet = await consumerNext.apply(this, arguments);
            if (packet.value) {
              if (
                packet.value.stream === streamName ||
                packet.value.consumerId === this.id
              ) {
                if (packet.value.data.done) {
                  this.return();
                }
                return packet.value.data;
              }
            }
            if (packet.done) {
              return packet;
            }
          }
        };

        let consumerGetStats = mainStreamConsumer.getStats;
        mainStreamConsumer.getStats = function () {
          let stats = consumerGetStats.apply(this, arguments);
          stats.stream = streamName;
          return stats;
        };

        let consumerApplyBackpressure = mainStreamConsumer.applyBackpressure;
        mainStreamConsumer.applyBackpressure = function (packet) {
          if (packet.value) {
            if (
              packet.value.stream === streamName ||
              packet.value.consumerId === this.id
            ) {
              consumerApplyBackpressure.apply(this, arguments);

              return;
            }
          }
          if (packet.done) {
            consumerApplyBackpressure.apply(this, arguments);
          }
        };

        let consumerReleaseBackpressure = mainStreamConsumer.releaseBackpressure;
        mainStreamConsumer.releaseBackpressure = function (packet) {
          if (packet.value) {
            if (
              packet.value.stream === streamName ||
              packet.value.consumerId === this.id
            ) {
              consumerReleaseBackpressure.apply(this, arguments);

              return;
            }
          }
          if (packet.done) {
            consumerReleaseBackpressure.apply(this, arguments);
          }
        };

        return mainStreamConsumer;
      }

      stream(streamName) {
        return new DemuxedConsumableStream(this, streamName);
      }
    }

    module.exports = StreamDemux;

  },{"./demuxed-consumable-stream":27,"writable-consumable-stream":35}],29:[function(require,module,exports){
    var v1 = require('./v1');
    var v4 = require('./v4');

    var uuid = v4;
    uuid.v1 = v1;
    uuid.v4 = v4;

    module.exports = uuid;

  },{"./v1":32,"./v4":33}],30:[function(require,module,exports){
    /**
     * Convert array of 16 byte values to UUID string format of the form:
     * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
     */
    var byteToHex = [];
    for (var i = 0; i < 256; ++i) {
      byteToHex[i] = (i + 0x100).toString(16).substr(1);
    }

    function bytesToUuid(buf, offset) {
      var i = offset || 0;
      var bth = byteToHex;
      // join used to fix memory issue caused by concatenation: https://bugs.chromium.org/p/v8/issues/detail?id=3175#c4
      return ([
        bth[buf[i++]], bth[buf[i++]],
        bth[buf[i++]], bth[buf[i++]], '-',
        bth[buf[i++]], bth[buf[i++]], '-',
        bth[buf[i++]], bth[buf[i++]], '-',
        bth[buf[i++]], bth[buf[i++]], '-',
        bth[buf[i++]], bth[buf[i++]],
        bth[buf[i++]], bth[buf[i++]],
        bth[buf[i++]], bth[buf[i++]]
      ]).join('');
    }

    module.exports = bytesToUuid;

  },{}],31:[function(require,module,exports){
// Unique ID creation requires a high quality random # generator.  In the
// browser this is a little complicated due to unknown quality of Math.random()
// and inconsistent support for the `crypto` API.  We do the best we can via
// feature-detection

// getRandomValues needs to be invoked in a context where "this" is a Crypto
// implementation. Also, find the complete implementation of crypto on IE11.
    var getRandomValues = (typeof(crypto) != 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto)) ||
      (typeof(msCrypto) != 'undefined' && typeof window.msCrypto.getRandomValues == 'function' && msCrypto.getRandomValues.bind(msCrypto));

    if (getRandomValues) {
      // WHATWG crypto RNG - http://wiki.whatwg.org/wiki/Crypto
      var rnds8 = new Uint8Array(16); // eslint-disable-line no-undef

      module.exports = function whatwgRNG() {
        getRandomValues(rnds8);
        return rnds8;
      };
    } else {
      // Math.random()-based (RNG)
      //
      // If all else fails, use Math.random().  It's fast, but is of unspecified
      // quality.
      var rnds = new Array(16);

      module.exports = function mathRNG() {
        for (var i = 0, r; i < 16; i++) {
          if ((i & 0x03) === 0) r = Math.random() * 0x100000000;
          rnds[i] = r >>> ((i & 0x03) << 3) & 0xff;
        }

        return rnds;
      };
    }

  },{}],32:[function(require,module,exports){
    var rng = require('./lib/rng');
    var bytesToUuid = require('./lib/bytesToUuid');

// **`v1()` - Generate time-based UUID**
//
// Inspired by https://github.com/LiosK/UUID.js
// and http://docs.python.org/library/uuid.html

    var _nodeId;
    var _clockseq;

// Previous uuid creation time
    var _lastMSecs = 0;
    var _lastNSecs = 0;

// See https://github.com/uuidjs/uuid for API details
    function v1(options, buf, offset) {
      var i = buf && offset || 0;
      var b = buf || [];

      options = options || {};
      var node = options.node || _nodeId;
      var clockseq = options.clockseq !== undefined ? options.clockseq : _clockseq;

      // node and clockseq need to be initialized to random values if they're not
      // specified.  We do this lazily to minimize issues related to insufficient
      // system entropy.  See #189
      if (node == null || clockseq == null) {
        var seedBytes = rng();
        if (node == null) {
          // Per 4.5, create and 48-bit node id, (47 random bits + multicast bit = 1)
          node = _nodeId = [
            seedBytes[0] | 0x01,
            seedBytes[1], seedBytes[2], seedBytes[3], seedBytes[4], seedBytes[5]
          ];
        }
        if (clockseq == null) {
          // Per 4.2.2, randomize (14 bit) clockseq
          clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;
        }
      }

      // UUID timestamps are 100 nano-second units since the Gregorian epoch,
      // (1582-10-15 00:00).  JSNumbers aren't precise enough for this, so
      // time is handled internally as 'msecs' (integer milliseconds) and 'nsecs'
      // (100-nanoseconds offset from msecs) since unix epoch, 1970-01-01 00:00.
      var msecs = options.msecs !== undefined ? options.msecs : new Date().getTime();

      // Per 4.2.1.2, use count of uuid's generated during the current clock
      // cycle to simulate higher resolution clock
      var nsecs = options.nsecs !== undefined ? options.nsecs : _lastNSecs + 1;

      // Time since last uuid creation (in msecs)
      var dt = (msecs - _lastMSecs) + (nsecs - _lastNSecs)/10000;

      // Per 4.2.1.2, Bump clockseq on clock regression
      if (dt < 0 && options.clockseq === undefined) {
        clockseq = clockseq + 1 & 0x3fff;
      }

      // Reset nsecs if clock regresses (new clockseq) or we've moved onto a new
      // time interval
      if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {
        nsecs = 0;
      }

      // Per 4.2.1.2 Throw error if too many uuids are requested
      if (nsecs >= 10000) {
        throw new Error('uuid.v1(): Can\'t create more than 10M uuids/sec');
      }

      _lastMSecs = msecs;
      _lastNSecs = nsecs;
      _clockseq = clockseq;

      // Per 4.1.4 - Convert from unix epoch to Gregorian epoch
      msecs += 12219292800000;

      // `time_low`
      var tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;
      b[i++] = tl >>> 24 & 0xff;
      b[i++] = tl >>> 16 & 0xff;
      b[i++] = tl >>> 8 & 0xff;
      b[i++] = tl & 0xff;

      // `time_mid`
      var tmh = (msecs / 0x100000000 * 10000) & 0xfffffff;
      b[i++] = tmh >>> 8 & 0xff;
      b[i++] = tmh & 0xff;

      // `time_high_and_version`
      b[i++] = tmh >>> 24 & 0xf | 0x10; // include version
      b[i++] = tmh >>> 16 & 0xff;

      // `clock_seq_hi_and_reserved` (Per 4.2.2 - include variant)
      b[i++] = clockseq >>> 8 | 0x80;

      // `clock_seq_low`
      b[i++] = clockseq & 0xff;

      // `node`
      for (var n = 0; n < 6; ++n) {
        b[i + n] = node[n];
      }

      return buf ? buf : bytesToUuid(b);
    }

    module.exports = v1;

  },{"./lib/bytesToUuid":30,"./lib/rng":31}],33:[function(require,module,exports){
    var rng = require('./lib/rng');
    var bytesToUuid = require('./lib/bytesToUuid');

    function v4(options, buf, offset) {
      var i = buf && offset || 0;

      if (typeof(options) == 'string') {
        buf = options === 'binary' ? new Array(16) : null;
        options = null;
      }
      options = options || {};

      var rnds = options.random || (options.rng || rng)();

      // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`
      rnds[6] = (rnds[6] & 0x0f) | 0x40;
      rnds[8] = (rnds[8] & 0x3f) | 0x80;

      // Copy bytes to buffer, if provided
      if (buf) {
        for (var ii = 0; ii < 16; ++ii) {
          buf[i + ii] = rnds[ii];
        }
      }

      return buf || bytesToUuid(rnds);
    }

    module.exports = v4;

  },{"./lib/bytesToUuid":30,"./lib/rng":31}],34:[function(require,module,exports){
    class Consumer {
      constructor(stream, id, startNode, timeout) {
        this.id = id;
        this._backpressure = 0;
        this.stream = stream;
        this.currentNode = startNode;
        this.timeout = timeout;
        this.isAlive = true;
        this.stream.setConsumer(this.id, this);
      }

      getStats() {
        let stats = {
          id: this.id,
          backpressure: this._backpressure
        };
        if (this.timeout != null) {
          stats.timeout = this.timeout;
        }
        return stats;
      }

      _resetBackpressure() {
        this._backpressure = 0;
      }

      applyBackpressure(packet) {
        this._backpressure++;
      }

      releaseBackpressure(packet) {
        this._backpressure--;
      }

      getBackpressure() {
        return this._backpressure;
      }

      write(packet) {
        if (this._timeoutId !== undefined) {
          clearTimeout(this._timeoutId);
          delete this._timeoutId;
        }
        this.applyBackpressure(packet);
        if (this._resolve) {
          this._resolve();
          delete this._resolve;
        }
      }

      kill(value) {
        if (this._timeoutId !== undefined) {
          clearTimeout(this._timeoutId);
          delete this._timeoutId;
        }
        this._killPacket = {value, done: true};
        this._destroy();

        if (this._resolve) {
          this._resolve();
          delete this._resolve;
        }
      }

      _destroy() {
        this.isAlive = false;
        this._resetBackpressure();
        this.stream.removeConsumer(this.id);
      }

      async _waitForNextItem(timeout) {
        return new Promise((resolve, reject) => {
          this._resolve = resolve;
          let timeoutId;
          if (timeout !== undefined) {
            // Create the error object in the outer scope in order
            // to get the full stack trace.
            let error = new Error('Stream consumer iteration timed out');
            (async () => {
              let delay = wait(timeout);
              timeoutId = delay.timeoutId;
              await delay.promise;
              error.name = 'TimeoutError';
              delete this._resolve;
              reject(error);
            })();
          }
          this._timeoutId = timeoutId;
        });
      }

      async next() {
        this.stream.setConsumer(this.id, this);

        while (true) {
          if (!this.currentNode.next) {
            try {
              await this._waitForNextItem(this.timeout);
            } catch (error) {
              this._destroy();
              throw error;
            }
          }
          if (this._killPacket) {
            this._destroy();
            let killPacket = this._killPacket;
            delete this._killPacket;

            return killPacket;
          }

          this.currentNode = this.currentNode.next;
          this.releaseBackpressure(this.currentNode.data);

          if (this.currentNode.consumerId && this.currentNode.consumerId !== this.id) {
            continue;
          }

          if (this.currentNode.data.done) {
            this._destroy();
          }

          return this.currentNode.data;
        }
      }

      return() {
        delete this.currentNode;
        this._destroy();
        return {};
      }

      [Symbol.asyncIterator]() {
        return this;
      }
    }

    function wait(timeout) {
      let timeoutId;
      let promise = new Promise((resolve) => {
        timeoutId = setTimeout(resolve, timeout);
      });
      return {timeoutId, promise};
    }

    module.exports = Consumer;

  },{}],35:[function(require,module,exports){
    const ConsumableStream = require('consumable-stream');
    const Consumer = require('./consumer');

    class WritableConsumableStream extends ConsumableStream {
      constructor() {
        super();
        this.nextConsumerId = 1;
        this._consumers = {};

        // Tail node of a singly linked list.
        this._tailNode = {
          next: null,
          data: {
            value: undefined,
            done: false
          }
        };
      }

      _write(value, done, consumerId) {
        let dataNode = {
          data: {value, done},
          next: null
        };
        if (consumerId) {
          dataNode.consumerId = consumerId;
        }
        this._tailNode.next = dataNode;
        this._tailNode = dataNode;

        let consumerList = Object.values(this._consumers);
        let len = consumerList.length;

        for (let i = 0; i < len; i++) {
          let consumer = consumerList[i];
          consumer.write(dataNode.data);
        }
      }

      write(value) {
        this._write(value, false);
      }

      close(value) {
        this._write(value, true);
      }

      writeToConsumer(consumerId, value) {
        this._write(value, false, consumerId);
      }

      closeConsumer(consumerId, value) {
        this._write(value, true, consumerId);
      }

      kill(value) {
        let consumerIdList = Object.keys(this._consumers);
        let len = consumerIdList.length;
        for (let i = 0; i < len; i++) {
          this.killConsumer(consumerIdList[i], value);
        }
      }

      killConsumer(consumerId, value) {
        let consumer = this._consumers[consumerId];
        if (!consumer) {
          return;
        }
        consumer.kill(value);
      }

      getBackpressure() {
        let consumerList = Object.values(this._consumers);
        let len = consumerList.length;

        let maxBackpressure = 0;
        for (let i = 0; i < len; i++) {
          let consumer = consumerList[i];
          let backpressure = consumer.getBackpressure();
          if (backpressure > maxBackpressure) {
            maxBackpressure = backpressure;
          }
        }
        return maxBackpressure;
      }

      getConsumerBackpressure(consumerId) {
        let consumer = this._consumers[consumerId];
        if (consumer) {
          return consumer.getBackpressure();
        }
        return 0;
      }

      hasConsumer(consumerId) {
        return !!this._consumers[consumerId];
      }

      setConsumer(consumerId, consumer) {
        this._consumers[consumerId] = consumer;
        if (!consumer.currentNode) {
          consumer.currentNode = this._tailNode;
        }
      }

      removeConsumer(consumerId) {
        delete this._consumers[consumerId];
      }

      getConsumerStats(consumerId) {
        let consumer = this._consumers[consumerId];
        if (consumer) {
          return consumer.getStats();
        }
        return undefined;
      }

      getConsumerStatsList() {
        let consumerStats = [];
        let consumerList = Object.values(this._consumers);
        let len = consumerList.length;
        for (let i = 0; i < len; i++) {
          let consumer = consumerList[i];
          consumerStats.push(consumer.getStats());
        }
        return consumerStats;
      }

      createConsumer(timeout) {
        return new Consumer(this, this.nextConsumerId++, this._tailNode, timeout);
      }
    }

    module.exports = WritableConsumableStream;

  },{"./consumer":34,"consumable-stream":13}],"socketcluster-client":[function(require,module,exports){
    const AGClientSocket = require('./lib/clientsocket');
    const factory = require('./lib/factory');
    const version = '16.0.1';

    module.exports.factory = factory;
    module.exports.AGClientSocket = AGClientSocket;

    module.exports.create = function (options) {
      return factory.create({...options, version});
    };

    module.exports.version = version;

  },{"./lib/clientsocket":2,"./lib/factory":3}]},{},["socketcluster-client"])("socketcluster-client")
});
