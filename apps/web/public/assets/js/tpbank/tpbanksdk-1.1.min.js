var tpbanksdk=new function(){var t="",e=null;function n(t){try{var n=t.stack||t.stacktrace||" ";this.isIOS()?e.webkit.messageHandlers.sendDataError.postMessage(a(n)):this.isAndroid()&&JSBridge.sendDataError(a(n))}catch(t){}}function a(t){return e.btoa(encodeURI(t))}this.sendMessage=function(t,n){e=t;var i=JSON.stringify(n);this.isIOS()?e.webkit.messageHandlers.sendDataToNative.postMessage(a(i)):this.isAndroid()?JSBridge.sendDataToNative(a(i)):e.parent.postMessage(a(i),"*")},this.updateFromNative=function(e){try{!function(e){try{"setPlatform"==e.event&&(t=e.value.platform),"function"==typeof partnerHandler[e.event]&&partner<PERSON>and<PERSON>[e.event](JSON.stringify(e.value))}catch(t){n(t.toString())}}(JSON.parse(decodeURI(atob(decodeURI(e)))))}catch(t){n(t)}},this.isAndroid=function(){return"Android"==t||navigator.userAgent.match(/Android/i)},this.isIOS=function(){return"IOS"==t||navigator.userAgent.match(/iPhone|iPad/i)},this.isWeb=function(){return!0}};