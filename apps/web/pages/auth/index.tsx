import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import get from 'lodash/get';
import Authentication from '@components/Authentication';
import { useVieRouter } from '@customHook';
import { PAGE } from '@constants/constants';
import { TEXT } from '@constants/text';
import Head from 'next/head';
import { pushToLobby } from '@services/multiProfileServices';
import ConfigLocalStorage from '@/config/ConfigLocalStorage';
import LocalStorage from '@/config/LocalStorage';

const Index = () => {
  const router = useVieRouter();
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const { currentProfile } = useSelector((state: any) => state?.MultiProfile || {});
  const handleAccessPage = () => {
    if (get(profile, 'id', '')) {
      if (!currentProfile?.id) {
        return pushToLobby({ asPath: router?.asPath, router, profile });
      }
      return router.push(PAGE.HOME);
    }
  };

  useEffect(() => {
    handleAccessPage();
  }, []);

  useEffect(() => {
    const reLoginParams = ConfigLocalStorage.get(LocalStorage.RE_LOGIN_PARAMS);
    if (typeof reLoginParams !== 'string') return;
    try {
      const { url } = JSON.parse(reLoginParams || '{}');
      if (!url) return;
      const urlResultParams = new URLSearchParams(url.slice(url.indexOf('?')));
      const queryAuth = Object.fromEntries(urlResultParams.entries());
      if (queryAuth?.isTriggerAuth) {
        ConfigLocalStorage.remove(LocalStorage.RE_LOGIN_PARAMS);
      }
    } catch (error) {
      console.error('Error parsing reLoginParams:', error);
    }
  }, []);

  return (
    <>
      <Head>
        <title>{TEXT.TITLE_PAGE_AUTH}</title>
      </Head>
      <Authentication />
    </>
  );
};

export default Index;
