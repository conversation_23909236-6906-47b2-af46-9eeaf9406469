import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import get from 'lodash/get';
import Head from 'next/head';
import Authentication from '@components/Authentication';
import { useVieRouter } from '@customHook';
import { FLOW_GLOBAL_AUTH, PAGE } from '@constants/constants';
import { TEXT } from '@constants/text';

const Index = () => {
  const router = useVieRouter();
  const { profile } = useSelector((state: any) => state?.Profile || {});

  useEffect(() => {
    if (!get(profile, 'id', '')) {
      router.push(PAGE.HOME);
    }
  }, [profile]);

  return (
    <>
      <Head>
        <title>{TEXT.TITLE_PAGE_LINK_PHONE_NUMBER}</title>
      </Head>
      <Authentication mainFlow={FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER} />
    </>
  );
};

export default Index;
