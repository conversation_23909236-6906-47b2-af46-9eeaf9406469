import React from 'react';
import PaymentResultContainer from '@containers/Payment/PaymentResultContainer';
import { GetServerSideProps } from 'next';

interface PaymentResultProps {
  pageProps: {
    napasResult: string;
    methodRequest: string;
  };
}

interface PaymentRequestBody {
  napasResult?: string;
  [key: string]: string | undefined;
}

const PaymentResult = ({ pageProps }: PaymentResultProps) => (
  <PaymentResultContainer pageProps={pageProps} />
);

export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  let reqBody: PaymentRequestBody = {};
  if (req.method === 'POST') {
    const buffers = [];
    for await (const chunk of req) {
      buffers.push(chunk);
    }
    const data = Buffer.concat(buffers).toString();
    const urlResultParams = new URLSearchParams(data);
    reqBody = Object.fromEntries(urlResultParams.entries()) as PaymentRequestBody;
  }
  return { props: { napasResult: reqBody.napasResult || '', methodRequest: req.method } };
};

export default PaymentResult;
