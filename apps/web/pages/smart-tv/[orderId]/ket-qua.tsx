import React, { useEffect, useMemo, useState } from 'react';
import Head from 'next/head';
import { useDispatch, useSelector } from 'react-redux';
import { GetServerSideProps } from 'next';
import Failed from '@components/payment/result/Failed';
import Process from '@components/payment/result/Process';
import Success from '@components/payment/result/Success';
import { getResultTransaction, getStatusTransaction } from '@actions/napas';
import ConfigCookie from '@config/ConfigCookie';
import ConfigSeo from '@config/ConfigSeo';
import ConfigPayment from '@config/ConfigPayment';
import { KEY_NAPAS_RESULT_SMART_TV, PAYMENT_TYPE } from '@constants/constants';
import { tvodCheckTransaction } from '@actions/payment';
import { useVieRouter } from '@customHook';

interface PageProps {
  napasResult: string;
  methodRequest: string;
}

interface PaymentRequestBody {
  napasResult?: string;
  [key: string]: string | undefined;
}

let intervalCheckTransaction: NodeJS.Timeout | null = null;

const ResultTransactionOfSmartTv = ({ pageProps }: { pageProps: PageProps }) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const result = useSelector((state: any) => state?.Payment?.transactionResult);
  const [isRunningCheck, setIsRunningCheck] = useState(false);
  const orderId = useMemo(() => router?.query?.orderId, [router]);
  const methodRequest = useMemo(() => pageProps?.methodRequest, [pageProps]);
  const napasResult = useMemo(() => pageProps?.napasResult, [pageProps]);
  const status = useMemo(() => result?.status, [result]);

  useEffect(
    () => () => {
      if (intervalCheckTransaction) {
        clearInterval(intervalCheckTransaction);
      }
    },
    []
  );

  useEffect(() => {
    const napasResultCookie = ConfigCookie.load(KEY_NAPAS_RESULT_SMART_TV);
    if (napasResultCookie && orderId && methodRequest !== 'POST') {
      const dataResult = JSON.parse(JSON.stringify(napasResultCookie));
      dispatch(getResultTransaction({ ...dataResult, orderId, isSmartTv: true }));
    }
  }, [orderId, methodRequest]);

  useEffect(() => {
    if (orderId && napasResult && methodRequest === 'POST') {
      const dataResult = JSON.parse(napasResult);
      ConfigCookie.save(KEY_NAPAS_RESULT_SMART_TV, JSON.stringify({ ...dataResult, orderId }));
      dispatch(getResultTransaction({ ...dataResult, orderId, isSmartTv: true }));
    }
  }, [methodRequest, napasResult, orderId]);

  useEffect(() => {
    if (orderId) {
      dispatch(tvodCheckTransaction({ orderId }));
    }
  }, [orderId]);

  useEffect(() => {
    if (Object.keys(result || {}).length > 0 && orderId) {
      if (result.status === ConfigPayment.STATUS_TRANSACTION.PROCESSING && !isRunningCheck) {
        if (intervalCheckTransaction) {
          clearInterval(intervalCheckTransaction);
        }
        intervalCheckTransaction = setInterval(() => {
          if (result?.paymentType !== PAYMENT_TYPE.SVOD) {
            // TODO: [Forest] to do check pvod
            dispatch(tvodCheckTransaction({ orderId }));
          } else {
            dispatch(getStatusTransaction({ orderId }));
          }
        }, ConfigPayment.CHECK_NAPAS_TIMER);
        setIsRunningCheck(true);
      } else if (result.status !== ConfigPayment.STATUS_TRANSACTION.PROCESSING && isRunningCheck) {
        if (intervalCheckTransaction) {
          clearInterval(intervalCheckTransaction);
        }
        setIsRunningCheck(false);
      }
    }
  }, [result, isRunningCheck, orderId]);

  return (
    <>
      <Head>
        <title>{ConfigSeo.TITLE.PAYMENT}</title>
        <link rel="shortcut icon" href={ConfigSeo.seoDefault.shortcut} />
      </Head>
      <section className="section section--payment section--payment-result !py-4 md:!py-6 overflow">
        <div className="container canal-v">
          <div className="section__body">
            {status === 1 && <Success supportSmartTv orderId={orderId} {...result} />}
            {(status === 2 || status === 3 || status === 4) && (
              <Failed
                supportSmartTv
                orderId={orderId}
                errorCode={result?.errorCode}
                message={result?.message}
                name="NAPAS"
                tel="024 3936 1818"
              />
            )}
            {status === 0 && (
              <Process supportSmartTv orderId={orderId} name="NAPAS" tel="024 3936 1818" />
            )}
          </div>
        </div>
      </section>
    </>
  );
};

export const getServerSideProps: GetServerSideProps<{ pageProps: PageProps }> = async ({ req }) => {
  let reqBody: PaymentRequestBody = {};
  const method = req.method || 'GET';

  if (method === 'POST') {
    const buffers = [];
    for await (const chunk of req) {
      buffers.push(chunk);
    }
    const data = Buffer.concat(buffers).toString();
    const urlResultParams = new URLSearchParams(data);
    reqBody = Object.fromEntries(urlResultParams.entries()) as PaymentRequestBody;
  }

  return {
    props: { pageProps: { napasResult: reqBody.napasResult || '', methodRequest: method } }
  };
};

export default ResultTransactionOfSmartTv;
