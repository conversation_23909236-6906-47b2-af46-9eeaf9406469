import Moment from 'moment';
import { AD_TYPE, CONTENT_TYPE } from '@constants/constants';

/**
 * @desc check if a time is in the middle of a time period
 * @param {number} - second
 * @return {boolean}
 */
export const checkBetweenTime = ({ started, ended, time }: any) => {
  const checkedTime = time || new Date().getTime() / 1000;
  return checkedTime > 0 && checkedTime >= started && checkedTime <= ended;
};

/**
 * @desc Replace all params key in a string: "This is a string {key}"
 * @param string - string, params - object
 * @return string
 */
export const stringReplaceKeys = (string: any, params: any) => {
  if (!string) return '';
  let resultStr = string;
  Object.keys(params || {}).forEach((key) => {
    resultStr = resultStr.replace(`{${key}}`, params[key] || '');
  });
  return resultStr;
};

/**
 * @desc Convert timestamp to expected format - DD/MM/YYYY || HH:mm:ss
 * @param timestamp - int, format - string, milliseconds
 * @return string
 */
export const convertToDateTime = (timestamp: any, format = 'DD/MM/YYYY') => {
  const timestampValue = timestamp ? new Date(timestamp) : new Date();
  return Moment(timestampValue).format(format);
};

export const formatLikeCount = (count: any) => {
  if (count < 1000) return count.toString();

  const units = ['', 'K', 'M', 'B'];
  const unitIndex = Math.floor(Math.log10(count) / 3);
  let value = count / 1000 ** unitIndex;

  value = Math.round(value * 10) / 10;

  return `${value.toString().replace('.', ',')} ${units[unitIndex]}`;
};

export const mapAdsToStore = (ads: any) => {
  // handle save overlay ads to store
  const outStreamAds: any = {
    pipAds: null,
    companionBanner1: null,
    companionBanner2: null
    // isHasCompanionBanner: false
  };
  const overlayAd = ads.filter((ad: any) => ad.ads_type === AD_TYPE.OVERLAY);
  const pipAd = overlayAd?.find((ad: any) => ad.type === 'onstream-pip');
  const companionAd = overlayAd.filter((ad: any) => ad.type.includes('companion'));

  if (pipAd) {
    outStreamAds.pipAds = {
      id: pipAd?.url,
      isOffAds: pipAd?.ads_config?.isOffAds || false,
      countdown: pipAd?.ads_config?.countdown || 0,
      duration: pipAd?.ads_config?.duration || 0
    };
  }
  if (companionAd?.length > 0) {
    // outStreamAds.isHasCompanionBanner = true;
    companionAd.forEach((ad: any) => {
      if (ad.type === 'companion-banner-above') {
        outStreamAds.companionBanner1 = {
          size: ad?.ads_config?.size
        };
      }
      if (ad.type === 'companion-banner-below') {
        outStreamAds.companionBanner2 = {
          size: ad?.ads_config?.size
        };
      }
    });
  }

  return outStreamAds;
};

export const findItemInRibbonById = (ribbonData: any, targetId: any) => {
  for (const path in ribbonData) {
    if (Object.prototype.hasOwnProperty.call(ribbonData, path)) {
      const ribbon = ribbonData[path];
      const foundItem = ribbon.items.find((item: any) => item.id === targetId);
      if (foundItem) {
        return foundItem;
      }
    }
  }
  return null;
};

export const getContentTypeText = (contentType: any) => {
  switch (contentType) {
    case CONTENT_TYPE.LIVESTREAM:
      return 'Livestream';
    case CONTENT_TYPE.MOVIE:
      return 'Movie';
    case CONTENT_TYPE.SEASON:
      return 'Season';
    case CONTENT_TYPE.EPISODE:
    case CONTENT_TYPE.SHORT_CONTENT:
      return 'Episode';
    case CONTENT_TYPE.LIVE_TV:
      return 'Live TV';
    case CONTENT_TYPE.TRAILER:
      return 'Trailer';
    case CONTENT_TYPE.EPG:
      return 'EPG';
    case CONTENT_TYPE.RIBBON:
      return 'Ribbon';
    case CONTENT_TYPE.ADS:
      return 'Ads';
    case CONTENT_TYPE.BANNER_TRIGGER:
      return 'Banner Trigger';
    case CONTENT_TYPE.MASK_ID:
      return 'Mask ID';
    default:
      return 'Unknown';
  }
};
