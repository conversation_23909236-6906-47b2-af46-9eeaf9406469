import CMApi from '@apis/cmApi';
import { HTTP_CODE } from '@constants/constants';
import { createAction, ACTION_TYPE } from './actionType';

const ACTIVE_MENU = 'ACTIVE_MENU';
const RESET_MENU = 'RESET_MENU';
const setMenuSuccess = (data: any) => createAction(ACTION_TYPE.SET_MENU, data);
const setActiveMenuSuccess = (data: any) => createAction(ACTION_TYPE.SET_ACTIVE_MENU, data);
const setActiveSubMenuSuccess = (data: any) => createAction(ACTION_TYPE.SET_ACTIVE_SUB_MENU, data);
const setActiveSubHeaderSuccess = (data: any) => createAction(ACTION_TYPE.SET_SUB_HEADER, data);
const setActiveMenuFromPathSuccess = (data: any) =>
  createAction(ACTION_TYPE.SET_ACTIVE_MENU_FROM_PATH, data);
const setTokenExpired = (data: any) => createAction(ACTION_TYPE.SET_TOKEN_EXPIRED, data);

const getMenu =
  ({ slug, accessToken, profileToken, isMobile, ssr, ipAddress, userAgent, origin }: any) =>
  (dispatch: any) =>
    CMApi.getMenu({
      slug,
      accessToken,
      profileToken,
      isMobile,
      ssr,
      ipAddress,
      userAgent,
      origin
    })?.then((res: any) => {
      dispatch(setMenuSuccess(res?.menuList));
      dispatch(setActiveMenuSuccess(res?.activeMenu));
      dispatch(setActiveSubMenuSuccess(res?.activeSubMenu));
      dispatch(setActiveSubHeaderSuccess(res?.subHeader));
      dispatch(setTokenExpired(res?.httpCode === HTTP_CODE.EXPIRE));
      return res;
    });

const setActiveMenuFromPath = (path: any) => (dispatch: any) => {
  dispatch(setActiveMenuFromPathSuccess(path));
};

const setActiveMenu =
  ({ menu, pathname }: any) =>
  (dispatch: any) =>
    dispatch({
      type: ACTIVE_MENU,
      payload: { menu, pathname }
    });

const resetMenu = () => (dispatch: any) =>
  dispatch({
    type: RESET_MENU
  });

const setOpenTagMenu = (status: any) => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.SET_OPEN_TAG_MENU, status));
};

export {
  ACTIVE_MENU,
  RESET_MENU,
  getMenu,
  setActiveMenuFromPath,
  setActiveMenu,
  resetMenu,
  setOpenTagMenu
};
