import LiveStreamApi from '@apis/LiveStream';
import { ACTION_TYPE, createAction } from '@actions/actionType';

const checkStatusLiveStream: any = (id: any) => (dispatch: any) =>
  LiveStreamApi.checkStatus(id)?.then((res: any) => {
    dispatch(createAction(ACTION_TYPE.CHECK_STATUS_LIVESTREAM_SUCCESS, res));
    return res;
  });
const getEventRelated: any =
  ({ id }: any) =>
  (dispatch: any) =>
    LiveStreamApi.eventRelated({ id })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_EVENT_RELATED, res));
    });

export { checkStatusLiveStream, getEventRelated };
