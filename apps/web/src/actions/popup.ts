import get from 'lodash/get';
import { handleTrackingPopup } from '@tracking/TrackingSegment';
import Detail<PERSON>pi from '@apis/detailApi';
import { CONFIG_KEY, CONTENT_TYPE, EL_ID } from '@constants/constants';
import { checkIsFullscreen } from '@helpers/common';
import User<PERSON><PERSON> from '@apis/userApi';
import PaymentApi from '@apis/Payment';
import { parseTVodFromInfo } from '@services/contentService';
import { Triggers, TriggersGlobal } from '@models/subModels';
import { ACTION_TYPE, createAction } from './actionType';
import Trigger<PERSON>pi from '@/apis/cm/TriggerApi';
import { TEXT } from '@/constants/text';
import { setToast } from './app';

declare global {
  interface Document {
    exitFullscreen(): void;
    webkitCancelFullScreen(): void;
  }

  interface HTMLElement {
    webkitExitFullscreen(): void;
  }
}

const openCardHover = (data: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.OPEN_CARD_HOVER, data));
const openCardRelatedHover = (data: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.OPEN_CARD_RELATED_HOVER, data));

const openPopup = (params?: any) => {
  // TRACKING POPUP
  handleTrackingPopup(params);
  // handlePopupOpen
  handlePopupOpen(params);
  return (dispatch: any) => dispatch({ type: ACTION_TYPE.OPEN_POPUP, data: params });
};

const handlePopupOpen = (params: any) => {
  const { name } = params || {};
  const videoTag = document.getElementById(EL_ID.VIE_PLAYER);
  if (name) {
    if (checkIsFullscreen()) {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitCancelFullScreen) {
        document.webkitCancelFullScreen();
      } else if (videoTag?.webkitExitFullscreen) {
        videoTag?.webkitExitFullscreen();
      }
    }
  }
};

const previewCard = (previewData: any) => (dispatch: any) =>
  dispatch({ type: ACTION_TYPE.PREVIEW_CARD, data: { ...previewData } });

const expandPreview =
  ({ expand }: any) =>
  (dispatch: any) =>
    dispatch({ type: ACTION_TYPE.EXPAND_PREVIEW, data: { expand } });
export const getPreviewDataById: any =
  ({ contentId, isGlobal, profile, vidItem }: any) =>
  (dispatch: any) =>
    DetailApi.getContentById({ contentId, isGlobal })?.then(async (res: any) => {
      const expand = !!res?.data?.id;
      const contentType = res?.data?.type;

      let tVodInfo = null;
      let tvod = null;
      let triggers = res?.data?.triggers;
      if (res?.data?.isPremiumTVod || res?.data?.isSvodTvod) {
        const isLiveEvent = get(res, 'data.tvod.isLiveEvent', false);
        const isSimulcast = get(res, 'data.tvod.isSimulcast', false);
        tVodInfo = await PaymentApi.getTVodInfo({
          contentId,
          contentType,
          isSimulcast,
          isLiveEvent
        });
        tvod = parseTVodFromInfo(tVodInfo);
        triggers = Triggers({ ...res?.data, tvod, vidItem });
      }

      const dataEpisode = contentType === CONTENT_TYPE.EPISODE ? (expand ? res?.data : null) : null;
      let seasonData = contentType !== CONTENT_TYPE.EPISODE ? res?.data : null;

      if (res?.data?.type === CONTENT_TYPE.EPISODE) {
        const groupId = res?.data?.groupId;
        const contentRes = await DetailApi.getContentById({ contentId: groupId });
        seasonData = expand ? contentRes?.data : null;
      }

      if (
        (seasonData.type === CONTENT_TYPE.MOVIE || seasonData.type === CONTENT_TYPE.SEASON) &&
        seasonData.isPremium &&
        isGlobal
      ) {
        triggers = TriggersGlobal({
          ...res.data,
          isPremium: seasonData.isPremium,
          packageInfoGlobal: seasonData?.previewEnabled
            ? { id: 1, isUserVip: profile?.isPremium }
            : {}
        });
      }
      if (seasonData?.id) {
        dispatch(
          previewCard({
            expand,
            data: { ...seasonData, tvod, triggers },
            tVodInfo,
            dataEpisode: dataEpisode ? { ...dataEpisode, triggers } : null
          })
        );
      }
      return res;
    });

const closeAllTabFullScreen =
  ({ off }: any) =>
  (dispatch: any) =>
    dispatch({ type: ACTION_TYPE.CLOSE_ALL_TAB_LIVETV, data: { status: off || false } });

const getConfigPopup = () => (dispatch: any) =>
  UserApi.getConfig({ key: CONFIG_KEY.POPUP_CONFIG })?.then((res: any) => {
    dispatch(createAction(ACTION_TYPE.POPUP_CONFIG, res));
    return res;
  });
const getPopupTriggerDialog = (params: any) => (dispatch: any) =>
  TriggerApi.getDialogPaymentTrigger(params)?.then((res: any) => {
    if (res?.success) {
      dispatch(createAction(ACTION_TYPE.POPUP_TRIGGER_PAYMENT, res?.data?.result));
    } else {
      dispatch(setToast({ message: TEXT.MSG_ERROR }));
    }
    return res;
  });
const clearPopupTriggerDialog = () => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.POPUP_TRIGGER_PAYMENT, null));
};

const setPlayerTrigger = (data: any) => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.IS_PLAYER_TRIGGER, data));
};

export {
  openCardRelatedHover,
  openCardHover,
  openPopup,
  getConfigPopup,
  previewCard,
  expandPreview,
  closeAllTabFullScreen,
  getPopupTriggerDialog,
  clearPopupTriggerDialog,
  setPlayerTrigger
};
