// Global
export const ACTION_LINK_PHONE_NUMBER = 'ACTION_LINK_PHONE_NUMBER';
export const ACTION_GLOBAL_UPDATE_PASSWORD = 'ACTION_GLOBAL_UPDATE_PASSWORD';
export const ACTION_GLOBAL_REGISTER = 'ACTION_GLOBAL_REGISTER';
export const ACTION_GLOBAL_LOGIN = 'ACTION_GLOBAL_LOGIN';
export const ACTION_RESET_GLOBAL_AUTH = 'ACTION_RESET_GLOBAL_AUTH';
export const ACTION_GLOBAL_CONFIRM_OTP = 'ACTION_GLOBAL_CONFIRM_OTP';
export const ACTION_GLOBAL_FORGOT_PASSWORD = 'ACTION_GLOBAL_FORGOT_PASSWORD';
export const ACTION_GLOBAL_VALIDATE_OTP = 'ACTION_GLOBAL_VALIDATE_OTP';
export const ACTION_GLOBAL_RESTORE_ACCOUNT = 'ACTION_GLOBAL_RESTORE_ACCOUNT';

export const ACTION_TYPE = {
  OPEN_CARD_HOVER: 'OPEN_CARD_HOVER',
  OPEN_CARD_RELATED_HOVER: 'OPEN_CARD_RELATED_HOVER',
  SET_VIEW_PORT: 'SET_VIEW_PORT',
  SET_DATA_INTRO_PACKAGES: 'SET_DATA_INTRO_PACKAGES',
  SET_PERSONALIZATION_FLOW: 'SET_PERSONALIZATION_FLOW',
  SET_FEATURES_FLAG: 'SET_FEATURES_FLAG',
  WEB_CONFIG: 'WEB_CONFIG',

  // voting result action
  GET_LIST_ROUNDS_VOTING: 'GET_LIST_ROUNDS_VOTING',
  GET_LIST_EPISODES_OF_ROUND: 'GET_LIST_EPISODES_OF_ROUND',
  GET_DETAIL_EPISODE_IN_ROUND: 'GET_DETAIL_EPISODE_IN_ROUND',
  GET_LIST_RATING_OF_EPISODE: 'GET_LIST_RATING_OF_EPISODE',
  GET_FINAL_RESULT: 'GET_FINAL_RESULT',
  // quiz result
  GET_LIST_CAMPAIGNS: 'GET_LIST_CAMPAIGNS',
  GET_LIST_WINNERS_OF_CAMPAIGNS: 'GET_LIST_WINNERS_OF_CAMPAIGNS',

  // user action
  SET_BLOCK_PLAYER: 'SET_BLOCK_PLAYER',

  // app action
  SET_TOKEN: 'SET_TOKEN',
  SET_DEVICE_INFO: 'SET_DEVICE_INFO',
  SET_DEVICE_ID: 'SET_DEVICE_ID',
  SET_SEO_TEXT: 'SET_SEO_TEXT',
  SET_LOADING: 'SET_LOADING',
  SET_LOADED_DATA: 'SET_LOADED_DATA',
  SET_STATUS_LOAD_OUT_STREAM_ADS: 'SET_STATUS_LOAD_OUT_STREAM_ADS',
  SET_STATUS_REPAY_CONVERSION: 'SET_STATUS_REPAY_CONVERSION',
  SET_STATUS_LOAD_MASTHEAD_ADS: 'SET_STATUS_LOAD_MASTHEAD_ADS',
  SET_STATUS_ONBOARDING: 'SET_STATUS_ONBOARDING',
  SET_STATUS_DOWNLOAD_APP: 'SET_STATUS_DOWNLOAD_APP',
  SET_STATUS_DIALOG_ONBOARDING: 'SET_STATUS_DIALOG_ONBOARDING',
  SET_STATUS_PAYMENT_CONVERSION: 'SET_STATUS_PAYMENT_CONVERSION',
  SET_STATUS_TVOD_REMINDER_SCREEN: 'SET_STATUS_TVOD_REMINDER_SCREEN',
  SET_STATUS_COMPANION_BANNER: 'SET_STATUS_COMPANION_BANNER',

  // menu action
  SET_MENU: 'SET_MENU',
  SET_ACTIVE_MENU: 'SET_ACTIVE_MENU',
  SET_ACTIVE_SUB_MENU: 'SET_ACTIVE_SUB_MENU',
  SET_SUB_HEADER: 'SET_SUB_HEADER',
  SET_ACTIVE_MENU_FROM_PATH: 'SET_ACTIVE_MENU_FROM_PATH',
  GEO_CHECK: 'GEO_CHECK',
  SET_HEIGHT_HEADER: 'SET_HEIGHT_HEADER',

  // page action
  SET_PAGE_BANNER: 'SET_PAGE_BANNER',
  SET_PAGE_RIBBON: 'SET_PAGE_RIBBON',
  SET_RIBBON_DATA: 'SET_RIBBON_DATA',
  CLEAR_RIBBON_DATA: 'CLEAR_RIBBON_DATA',
  GET_LIVESTREAM_EVENTS: 'GET_LIVESTREAM_EVENTS',
  ADD_MY_LIST: 'ADD_MY_LIST',
  SET_SUBSCRIBE_COMING_SOON: 'SET_SUBSCRIBE_COMING_SOON',
  SEO_TEMPLATE_CONFIG: 'SEO_TEMPLATE_CONFIG',
  SET_IS_MASTER_BANNER: 'SET_IS_MASTER_BANNER',

  // profile action
  GET_PROFILE: 'GET_PROFILE',
  UPDATE_PHONE_NUMBER: 'UPDATE_PHONE_NUMBER',
  UPDATE_CONFIRM_MOBILE: 'UPDATE_CONFIRM_MOBILE',
  UPDATE_PASSWORD: 'UPDATE_PASSWORD',
  UPDATE_DOB: 'UPDATE_DOB',
  RESTORE_ACCOUNT: 'RESTORE_ACCOUNT',
  CONFIRM_RESTORE_ACCOUNT: 'CONFIRM_RESTORE_ACCOUNT',
  GET_OTP_UPDATE_PASSWORD: 'GET_OTP_UPDATE_PASSWORD',
  CHECK_OTP_UPDATE_PASSWORD: 'CHECK_OTP_UPDATE_PASSWORD',
  GET_DEVICES_MANAGEMENT: 'GET_DEVICES_MANAGEMENT',
  DISABLED_DEVICES_MANAGEMENT: 'DISABLED_DEVICES_MANAGEMENT',
  GET_SEGMENT_USER: 'GET_SEGMENT_USER',

  // popup action
  OPEN_POPUP: 'OPEN_POPUP',
  PREVIEW_CARD: 'PREVIEW_CARD',
  EXPAND_PREVIEW: 'EXPAND_PREVIEW',
  POPUP_CONFIG: 'POPUP_CONFIG',
  POPUP_TRIGGER_PAYMENT: 'POPUP_TRIGGER_PAYMENT',
  IS_PLAYER_TRIGGER: 'IS_PLAYER_TRIGGER',

  // detail action
  GET_EPISODE_LIST_SUCCESS: 'GET_EPISODE_LIST_SUCCESS',
  CLEAR_EPISODE_LIST: 'CLEAR_EPISODE_LIST',
  REMOVE_WATCH_LATER: 'REMOVE_WATCH_LATER',
  ADD_WATCH_LATER: 'ADD_WATCH_LATER',

  // vod action
  GET_VOD_CONTENT_SUCCESS: 'GET_VOD_CONTENT_SUCCESS',

  // EPISODE
  SET_EPISODE: 'SET_EPISODE',
  SET_NEXT_EPISODE: 'SET_NEXT_EPISODE',

  // player action
  GET_PLAYER_DATA_SUCCESS: 'GET_PLAYER_DATA_SUCCESS',
  SET_SETTING_SUCCESS: 'SET_SETTING_SUCCESS',
  SET_STATUS_FULLSCREEN: 'SET_STATUS_FULLSCREEN',

  // seo action
  GET_SEO_CONFIG: 'GET_SEO_CONFIG',
  GET_SEO_ALL_PAGE: 'GET_SEO_ALL_PAGE',

  // tip action
  GET_TIP_DATA_SUCCESS: 'GET_TIP_DATA_SUCCESS',

  // Close All tab Channel
  CLOSE_ALL_TAB_LIVETV: 'CLOSE_ALL_TAB_LIVETV',

  CLEAR_TAGS_DATA: 'CLEAR_TAGS_DATA',

  // LIVE TV
  SET_ACTIVE_CATEGORY: 'SET_ACTIVE_CATEGORY',
  GET_LIST_SUB_CATEGORIES_OF_CHANNEL_LIST: 'GET_LIST_SUB_CATEGORIES_OF_CHANNEL_LIST',
  GET_LIST_SUB_CATEGORIES_OF_BROADCASTING: 'GET_LIST_SUB_CATEGORIES_OF_BROADCASTING',
  GET_LIST_FILTER_CHANNEL_PAGE: 'GET_LIST_FILTER_CHANNEL_PAGE',
  SET_ACTIVE_FILTER_CHANNEL_PAGE: 'SET_ACTIVE_FILTER_CHANNEL_PAGE',
  SET_ACTIVE_SUB_CATEGORY: 'SET_ACTIVE_SUB_CATEGORY',
  SET_ACTIVE_EPG: 'SET_ACTIVE_EPG',
  GET_LIST_ALL_CHANNELS: 'GET_LIST_ALL_CHANNELS',
  GET_LIST_CHANNELS: 'GET_LIST_CHANNELS',
  GET_LIST_FAVORITE_CHANNEL: 'GET_LIST_FAVORITE_CHANNEL',
  GET_LIST_WATCHED_CHANNEL: 'GET_LIST_WATCHED_CHANNEL',
  GET_DETAIL_CHANNEL_BY_ID: 'GET_DETAIL_CHANNEL_BY_ID',
  CLEAR_DETAIL_CHANNEL: 'CLEAR_DETAIL_CHANNEL',
  GET_DETAIL_CHANNEL_BY_SLUG: 'GET_DETAIL_CHANNEL_BY_SLUG',
  GET_LIST_EPGS: 'GET_LIST_EPGS',
  GET_QNET_INFO_OF_CHANNEL: 'GET_QNET_INFO_OF_CHANNEL',
  SET_FAVORITE_CHANNEL: 'SET_FAVORITE_CHANNEL',
  GET_INFO_NOTIFY_LIST_EPGS: 'GET_INFO_NOTIFY_LIST_EPGS',
  SET_NOTIFY_COMING_SOON_EPG: 'SET_NOTIFY_COMING_SOON_EPG',
  GET_CHANNEL_RIBBON_NOTFOUND: 'GET_CHANNEL_RIBBON_NOTFOUND',
  VALIDATE_K_PLUS: 'VALIDATE_K_PLUS',
  RESET_VALIDATE_K_PLUS: 'RESET_VALIDATE_K_PLUS',

  // PAYMENT
  GET_TEMPORARY_DATA: 'GET_TEMPORARY_DATA',
  GET_PAYMENT_CONFIG_SUCCESS: 'GET_PAYMENT_CONFIG_SUCCESS',
  GET_CONTENT_CONFIG_SUCCESS: 'GET_CONTENT_CONFIG_SUCCESS',
  GET_TRIGGER_CONFIG_SUCCESS: 'GET_TRIGGER_CONFIG_SUCCESS',
  GET_PLAYER_CONFIG_SUCCESS: 'GET_PLAYER_CONFIG_SUCCESS',
  CREATE_TRANSACTION: 'CREATE_TRANSACTION',
  CONFIRM_RESULT_TRANSACTION: 'CONFIRM_RESULT_TRANSACTION',
  GET_INFO_TRANSACTION: 'GET_INFO_TRANSACTION',
  GET_LIST_TOKENS_SAVED: 'GET_LIST_TOKENS_SAVED',
  CHECK_FIRST_PAY: 'CHECK_FIRST_PAY',
  CHECK_REFERRAL_CODE: 'CHECK_REFERRAL_CODE',
  SET_VALUE_REFERRAL_CODE: 'SET_VALUE_REFERRAL_CODE',
  SELECT_TOKEN_SAVED: 'SELECT_TOKEN_SAVED',
  GET_PACKAGES: 'GET_PACKAGES',
  SELECT_PACKAGE: 'SELECT_PACKAGE',
  SELECT_TERM: 'SELECT_TERM',
  SELECT_METHOD: 'SELECT_METHOD',
  CLEAR_METHOD_CONFIG: 'CLEAR_METHOD_CONFIG',
  SET_CARD_INFO: 'SET_CARD_INFO',
  SET_BILLING_INFO: 'SET_BILLING_INFO',
  SET_CARD_INFO_CAKE: 'SET_CARD_INFO_CAKE',
  GET_VN_PAY_LIST: 'GET_VN_PAY_LIST',
  SET_BANK: 'SET_BANK',
  SET_PROMOTION_CODE: 'SET_PROMOTION_CODE',
  SET_PROMOTION_DATA: 'SET_PROMOTION_DATA',
  RESET_PROMOTION_DATA: 'RESET_PROMOTION_DATA',
  STATUS_LOGIN_PAYMENT: 'STATUS_LOGIN_PAYMENT',
  CHECK_STATUS_LOGIN_PROMOTION: 'CHECK_STATUS_LOGIN_PROMOTION',
  RESET_VALUE_REFERRAL_CODE: 'RESET_VALUE_REFERRAL_CODE',
  SET_TRANSACTION_RESULT: 'SET_TRANSACTION_RESULT',
  SET_LINKED_ZALO_PAY: 'SET_LINKED_ZALO_PAY',
  GET_LINKED_SHOPEE_PAY: 'GET_LINKED_SHOPEE_PAY',
  GET_TVOD_INFO: 'GET_TVOD_INFO',
  CLEAR_TVOD_INFO: 'CLEAR_TVOD_INFO',
  CLEAR_TVOD_OFFER: 'CLEAR_TVOD_OFFER',
  GET_TVOD_OFFER: 'GET_TVOD_OFFER',
  GET_PVOD_INFO: 'GET_PVOD_INFO',
  CLEAR_PVOD_INFO: 'CLEAR_PVOD_INFO',
  CLEAR_PVOD_OFFER: 'CLEAR_PVOD_OFFER',
  GET_PVOD_OFFER: 'GET_PVOD_OFFER',
  GET_PACKAGE_DISCOUNT: 'GET_PACKAGE_DISCOUNT',
  GET_PREORDER_REMINDER: 'GET_PREORDER_REMINDER',
  TRANSACTION: 'TRANSACTION',
  CLEAR_TRANSACTION: 'CLEAR_TRANSACTION',
  GET_REFERRAL_CODE: 'GET_REFERRAL_CODE',
  GET_CAMPAIGN: 'GET_CAMPAIGN',
  RECURRING_STATUS_PAYMENT: 'RECURRING_STATUS_PAYMENT',

  GET_FAQS: 'GET_FAQS',
  GET_PRIVACY: 'GET_PRIVACY',
  GET_USAGE: 'GET_USAGE',
  GET_ABOUT_US: 'GET_ABOUT_US',
  GET_USAGE_V1: 'WEB_USAGE_V1',
  GET_USAGE_V2: 'WEB_USAGE_V2',
  GET_USAGE_V3: 'WEB_USAGE_V3',
  WEB_ANNOUNCE: 'WEB_ANNOUNCE',
  GET_AGREEMENT: 'GET_AGREEMENT',
  GET_LICENSE: 'GET_LICENSE',
  WEB_REGULATION: 'WEB_REGULATION',
  WEB_POLICY_CANCELLATION: 'WEB_POLICY_CANCELLATION',

  SET_RANKING_BOARD: 'SET_RANKING_BOARD',
  SET_COMPETITIONS: 'SET_COMPETITIONS',
  SET_RANKING_TAB: 'SET_RANKING_TAB',
  SET_ACTIVE_COMPETITION: 'SET_ACTIVE_COMPETITION',
  SET_MATCH: 'SET_MATCH',
  SET_RANKING_LIST: 'SET_RANKING_LIST',
  SET_OUT_STREAM_ADS: 'SET_OUT_STREAM_ADS',
  SET_MASTHEAD_ADS: 'SET_MASTHEAD_ADS',
  SET_FLOAT_BUTTON: 'SET_FLOAT_BUTTON',

  SET_SOCIAL_CONFIG: 'SET_SOCIAL_CONFIG',
  SET_BIND_ACCOUNT_CONFIG: 'SET_BIND_ACCOUNT_CONFIG',
  SET_REGISTRATION_TRIGGER_CONFIG: 'SET_REGISTRATION_TRIGGER_CONFIG',
  SET_PAYMENT_CONVERSION_CONFIG: 'SET_PAYMENT_CONVERSION_CONFIG',

  SET_HIDE_CLICK_LATER_BIND_ACCOUNT: 'SET_HIDE_CLICK_LATER_BIND_ACCOUNT',
  SET_TOKEN_EXPIRED: 'SET_TOKEN_EXPIRED',
  OFF_BIND_ACCOUNT: 'OFF_BIND_ACCOUNT',

  TVOD: 'TVOD',
  USER_TVOD_INFO: 'USER_TVOD_INFO',
  SHOW_TVOD: 'SHOW_TVOD',

  // toast
  SET_TOAST: 'SET_TOAST',
  CLEAR_TOAST: 'CLEAR_TOAST',

  TVOD_REMINDER_PRE_ODER_INFO: 'TVOD_REMINDER_PRE_ODER_INFO',

  // Livestream
  GET_LIVESTREAM_TVOD_INFO: 'GET_LIVESTREAM_TVOD_INFO',
  GET_LIVESTREAM_TVOD_INFO_RESET: 'GET_LIVESTREAM_TVOD_INFO_RESET',
  CHECK_STATUS_LIVESTREAM_SUCCESS: 'CHECK_STATUS_LIVESTREAM_SUCCESS',
  ACTION_CHECK_STATUS_LIVESTREAM: 'ACTION_CHECK_STATUS_LIVESTREAM',
  GET_EVENT_RELATED: 'GET_EVENT_RELATED',

  // Search
  ACTION_GET_TREND_KEYWORD: 'SEARCH_TREND_KEYWORD',
  ACTION_GET_SEARCH_CONTENT: 'SEARCH_CONTENT',
  ACTION_GET_SEARCH_SUGGEST: 'SEARCH_SUGGEST',
  ACTION_GET_SEARCH_HISTORY: 'SEARCH_HISTORY',
  ACTION_DELETE_SEARCH_HISTORY: 'DELETE_SEARCH_HISTORY',
  ACTION_POST_SEARCH_HISTORY: 'POST_SEARCH_HISTORY',
  ACTION_GET_SEARCH_FORYOU: 'SEARCH_FORYOU',
  ACTION_FOCUS_SEARCHBOX: 'FOCUS_SEARCHBOX',
  SET_SEARCH_SHOW: 'SET_SEARCH_SHOW',

  SET_OPEN_TAG_MENU: 'SET_OPEN_TAG_MENU',

  // LOBBY PROFILE
  SET_LOBBY_STEP: 'SET_LOBBY_STEP',
  CREATE_PROFILE: 'CREATE_PROFILE',
  CACHE_FORM: 'CACHE_FORM',
  RESET_FORM: 'RESET_FORM',
  DEFAULT_FORM: 'DEFAULT_FORM',
  AVATAR_SELECTED: 'AVATAR_SELECTED',
  GET_AGE_RANGES: 'GET_AGE_RANGES',
  GET_AGE_RANGES_KID: 'GET_AGE_RANGES_KID',
  GET_GENDERS: 'GET_GENDERS',
  GET_AVATARS: 'GET_AVATARS',
  DELETE_PROFILE: 'DELETE_PROFILE',
  GET_MULTI_PROFILE: 'MULTI_PROFILE',
  GET_MULTI_PROFILE_KID: 'MULTI_PROFILE_KID',
  RESET_ALL_STATE_MULTI_PROFILE: 'RESET_ALL_STATE_MULTI_PROFILE',
  SET_STATUS_LOBBY_PROFILE: 'SET_STATUS_LOBBY_PROFILE',
  SELECTED_PROFILE: 'SELECTED_PROFILE',
  SET_TOKEN_PROFILE: 'SET_TOKEN_PROFILE',
  GET_PROFILE_ID: 'GET_PROFILE_ID',
  CHANGE_PIN_CODE: 'CHANGE_PIN_CODE',
  AGREEMENT: 'AGREEMENT',
  LOGOUT_PROFILE: 'LOGOUT_PROFILE',
  CLEAR_PROFILE: 'CLEAR_PROFILE',

  // Kid activity
  IS_EXPORT_EMAIL: 'IS_EXPORT_EMAIL',
  POST_EXPORT_EMAIL: 'POST_EXPORT_EMAIL',
  GET_DETAIL_WATCH: 'GET_DETAIL_WATCH',
  GET_DETAIL_WATCH_PENDING: 'GET_DETAIL_WATCH_PENDING',
  GET_MORE_DETAIL_WATCH: 'GET_MORE_DETAIL_WATCH',
  GET_RESTRICTION_CONTENT: 'GET_RESTRICTION_CONTENT',

  // video indexing
  REGISTER_CONSULTATION: 'REGISTER_CONSULTATION',
  REGISTER_CONSULTATION_FAIL: 'REGISTER_CONSULTATION_FAIL',
  CLEAR_REGISTER_CONSULTATION: 'CLEAR_REGISTER_CONSULTATION',
  GET_ITEM_INDICATOR: 'GET_ITEM_INDICATOR',
  SET_SESSION_ID_VI_INDEXING: 'SET_SESSION_ID_VI_INDEXING',

  // Loyalty
  SEE_ALL_VOUCHER: 'SEE_ALL_VOUCHER',
  INFO_REDEEM_DATA: 'INFO_REDEEM_DATA',
  GET_INFO_LOYALTY: 'GET_INFO_LOYALTY',
  GET_TIER_BENEFITS: 'GET_TIER_BENEFITS',
  GET_EARNING_ACTIVITIES: 'GET_EARNING_ACTIVITIES',
  GET_USED_POINTS_HISTORY: 'GET_USED_POINTS_HISTORY',
  GET_ACTIVITIES: 'GET_ACTIVITIES',
  GET_VOUCHERS: 'GET_VOUCHERS',
  GET_VOUCHERS_BY_CATEGORY: 'GET_VOUCHERS_BY_CATEGORY',
  REDEEM_VOUCHER: 'REDEEM_VOUCHER',
  UPDATE_STATUS_REDEEM_CODE: 'UPDATE_STATUS_REDEEM_CODE',
  CLEAR_REDEEM_CODE: 'CLEAR_REDEEM_CODE',
  GET_FAIL_INFO_LOYALTY: 'GET_FAIL_INFO_LOYALTY',
  SUB_ITEM_CATEGORY: 'SUB_ITEM_CATEGORY',
  GET_DEVICE_TOKEN: 'GET_DEVICE_TOKEN',

  // TITLE RESTRICTION
  GET_RESTRICTED_LIST_OF_KIDS: 'GET_RESTRICTED_LIST_OF_KIDS',
  IS_REMOVE_CONTENT_FLOW: 'IS_REMOVE_CONTENT_FLOW',
  IS_EMPTY_RESTRICTED_CONTENT: 'IS_EMPTY_RESTRICTED_CONTENT',

  // CONCURRENT SCREEN
  SET_SESSION_PLAY: 'SET_SESSION_PLAY',
  REFRESH_SESSION_PLAY: 'REFRESH_SESSION_PLAY',

  // SCHEDULE
  GET_CONTENT_BROADCASTING: 'GET_CONTENT_BROADCASTING',
  GET_CONTENT_BROADCASTING_PENDING: 'GET_CONTENT_BROADCASTING_PENDING',
  GET_CONTENT_BROADCASTING_MORE: 'GET_CONTENT_BROADCASTING_MORE',
  GET_CONTENT_UP_COMING_SOON: 'GET_CONTENT_UP_COMING_SOON',
  GET_CONTENT_UP_COMING_SOON_PENDING: 'GET_CONTENT_UP_COMING_SOON_PENDING',
  GET_CONTENT_UP_COMING_SOON_MORE: 'GET_CONTENT_UP_COMING_SOON_MORE',

  // END SCREEN VOD
  SET_STATUS_END_SCREEN_VOD: 'SET_STATUS_END_SCREEN_VOD',

  // ANNOUNCE POLICY NOTIFICATION
  ACTION_GET_LIST_NOTIFICATION: 'ACTION_GET_LIST_NOTIFICATION',
  ACTION_COUNT_NOTIFICATION: 'ACTION_COUNT_NOTIFICATION',
  CHECK_POLICY_CONFIRM: 'CHECK_POLICY_CONFIRM',
  GET_DETAIL_POLICY_ANNOUNCE: 'GET_DETAIL_POLICY_ANNOUNCE',
  CONFIRM_DETAIL_POLICY_ANNOUNCE: 'CONFIRM_DETAIL_POLICY_ANNOUNCE',
  ACTION_TRACKING_NOTIFICATION: 'ACTION_TRACKING_NOTIFICATION',
  STATUS_POLICY_CONFIRM: 'STATUS_POLICY_CONFIRM',

  // TRIGGER ALWAYS
  GET_DATA_TRIGGER: 'GET_DATA_TRIGGER',

  // TRIGGER FIRST PAY
  IS_TRIGGER_FIRST_PAY: 'IS_TRIGGER_FIRST_PAY',
  CANCEL_FIRST_PAY: 'CANCEL_FIRST_PAY',

  // TRACKING
  IS_TRACKING_FORGOT_PASSWORD: 'IS_TRACKING_FORGOT_PASSWORD',

  COLLAPSE_FOOTER_RECOMMEND: 'COLLAPSE_FOOTER_RECOMMEND'
};

export const createAction = (type: any, data?: any, dispatch?: any) => {
  if (type === ACTION_TYPE.SET_TOAST) {
    const duration = data?.duration || 3000;
    if (dispatch) {
      setTimeout(() => {
        dispatch(createAction(ACTION_TYPE.CLEAR_TOAST, data));
      }, duration);
    }
  }
  return {
    type,
    data
  };
};
