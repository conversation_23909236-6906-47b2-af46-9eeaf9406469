import { getMatches as getMatchesApi, getRanking } from '@apis/sportApi';
import { ACTION_TYPE, createAction } from './actionType';

const setRankingTab =
  ({ tapKey }: any) =>
  (dispatch: any) => {
    dispatch(createAction(ACTION_TYPE.SET_RANKING_TAB, tapKey));
  };

const setActiveCompetition =
  ({ competition }: any) =>
  (dispatch: any) => {
    dispatch(createAction(ACTION_TYPE.SET_ACTIVE_COMPETITION, competition));
  };

const getMatches =
  ({ competitionCode }: any) =>
  (dispatch: any) =>
    getMatchesApi({ competitionCode })?.then((res: any) => {
      dispatch(
        createAction(ACTION_TYPE.SET_MATCH, { code: competitionCode, matches: res?.data?.items })
      );
    });

const getRankingList =
  ({ competitionCode }: any) =>
  (dispatch: any) =>
    getRanking({ competitionCode })?.then((res: any) => {
      dispatch(
        createAction(ACTION_TYPE.SET_RANKING_LIST, {
          code: competitionCode,
          rankingList: res?.data?.items || null
        })
      );
    });

export { setRankingTab, setActiveCompetition, getMatches, getRankingList };
