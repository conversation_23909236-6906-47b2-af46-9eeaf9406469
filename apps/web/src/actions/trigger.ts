import ContentApi from '@apis/cm/ContentApi';
import { ACTION_TYPE, createAction } from '@actions/actionType';
import TriggerApi from '@apis/cm/TriggerApi';
export const addWatchlater = async (content_id: any) => ContentApi.addContentWatchlater(content_id);

export const getDataTriggerPoint =
  ({ type, contentId }: any) =>
  (dispatch: any) => {
    TriggerApi.getDataTriggerPoint({ type, contentId })?.then((res: any) => {
      return dispatch(createAction(ACTION_TYPE.GET_DATA_TRIGGER, { res, type }));
    });
  };
