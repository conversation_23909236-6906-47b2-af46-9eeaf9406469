import React, { useEffect } from 'react';
import { FB_APP_ID, ENABLE_SDK_FB } from '@config/ConfigEnv';

const FBSdk = () => {
  useEffect(() => {
    const loadFBScript = async () => {
      if (!ENABLE_SDK_FB || ENABLE_SDK_FB === 'false') {
        return;
      }

      try {
        const script = document.createElement('script');
        script.id = 'FBSdk';
        script.async = true;
        script.text = `
          window.fbAsyncInit = function () {
            FB.init({
              appId: "${FB_APP_ID}",
              xfbml: true,
              version: "v2.8"
            });
            FB.AppEvents.logPageView();
          };
          (function (d, s, id) {
            var js,
              fjs = d.getElementsByTagName(s)[0];
            if (d.getElementById(id)) {
              return;
            }
            js = d.createElement(s);
            js.id = id;
            js.src = "//connect.facebook.net/vi_VN/sdk.js";
            js.async = true;
            js.defer = true;
            fjs.parentNode.insertBefore(js, fjs);
          })(document, "script", "facebook-jssdk");
        `;

        await new Promise((resolve, reject) => {
          script.onload = resolve;
          script.onerror = reject;
          document.body.appendChild(script);
        });

        console.log('Facebook SDK loaded successfully');
      } catch (error) {
        console.error('Failed to load Facebook SDK:', error);
      }
    };

    loadFBScript();

    return () => {
      const script = document.getElementById('FBSdk');
      if (script) {
        document.body.removeChild(script);
      }
    };
  }, []);

  return null;
};

export default React.memo(FBSdk);
