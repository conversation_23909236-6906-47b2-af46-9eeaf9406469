import React, { useEffect } from 'react';
import { AADNETWORK_ID, AADNETWORK_SDK, ENABLE_SDK_AIACTIV } from '@config/ConfigEnv';

const AAdsNetwork = () => {
  useEffect(() => {
    const loadAAdsScript = async () => {
      if (!ENABLE_SDK_AIACTIV || ENABLE_SDK_AIACTIV === 'false') {
        return;
      }

      try {
        const script = document.createElement('script');
        script.id = 'DMP';
        script.async = true;
        script.text = `
          window.AiactivSDK||(window.AiactivSDK={}),AiactivSDK.load=function(t){var e=document.createElement("script");e.async=!0,e.type="text/javascript",e.src="${AADNETWORK_SDK}?t="+Date.now(),e.addEventListener?e.addEventListener("load",function(e){"function"==typeof t&&t(e)},!1):e.onreadystatechange=function(){("complete"==this.readyState||"loaded"==this.readyState)&&t(window.event)};let a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(e,a)},AiactivSDK.load(function(){AiactivSDK.initialize({containerId:"${AADNETWORK_ID}", type: ['adnetwork', 'dmp']}),AiactivSDK.callMethodsFromContainer()});
        `;

        await new Promise((resolve, reject) => {
          script.onload = resolve;
          script.onerror = reject;
          document.body.appendChild(script);
        });

        console.log('AAdsNetwork script loaded successfully');
      } catch (error) {
        console.error('Failed to load AAdsNetwork script:', error);
      }
    };

    loadAAdsScript();

    return () => {
      const script = document.getElementById('DMP');
      if (script) {
        document.body.removeChild(script);
      }
    };
  }, []);

  return null;
};

export default React.memo(AAdsNetwork);
