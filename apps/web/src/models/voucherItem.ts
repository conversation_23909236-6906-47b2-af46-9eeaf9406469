import { parseExpiredDateTime } from '@helpers/common';
import { VOUCHER_PACKAGE_TYPE, VOUCHER_TVOD_TYPE } from '@constants/constants';

class VoucherItem {
  expiredDate: any;
  groupName: any;
  isLifeTimeTVodType: any;
  isPackageTypeTVod: any;
  startDate: any;
  tVodConsumingDuration: any;
  tVodDurationType: any;
  tVodWaitingDuration: any;
  constructor(props: any) {
    Object.assign(this, props);
    this.expiredDate = parseExpiredDateTime(props?.expired_date);
    this.startDate = parseExpiredDateTime(props?.start_date);
    this.isPackageTypeTVod = props?.package_type === VOUCHER_PACKAGE_TYPE.TVOD;
    this.isLifeTimeTVodType = props?.tvod_type === VOUCHER_TVOD_TYPE.LIFE_TIME;
    this.tVodDurationType = props?.tvod_duration_type || ''; // hours
    this.tVodWaitingDuration = props?.tvod_waiting_duration;
    this.tVodConsumingDuration = props?.tvod_consuming_duration;
    this.groupName = props?.name_group;
  }
}

export default VoucherItem;
