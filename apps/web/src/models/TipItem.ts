import { numberWithCommas, parseRemainText } from '@helpers/common';

class TipItem {
  avgRate: any;
  id: any;
  isSubscribe: any;
  isWatchLater: any;
  progress: any;
  progressPercent: any;
  releaseYear: any;
  remainText: any;
  runtimeDuration: any;
  tags: any;
  text: any;
  views: any;
  constructor(props: any) {
    this.id = props?.id;
    this.avgRate = props?.avg_rate;
    this.isSubscribe = props?.is_subscribe;
    this.progress = props?.progress || 0;
    this.releaseYear = props?.release_year || 0;
    this.runtimeDuration = props?.runtime_duration || 0;
    this.isWatchLater = props?.is_watchlater;
    const { progressPercent, remainText } = parseRemainText(this.progress, this?.runtimeDuration);
    this.progressPercent = progressPercent;
    this.remainText = remainText;
    this.text = props?.text;
    this.tags = props?.tags;
    this.views = numberWithCommas(props?.views);
  }
}

export default TipItem;
