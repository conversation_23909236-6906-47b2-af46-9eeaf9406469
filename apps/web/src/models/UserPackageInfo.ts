import { subscriptions } from './subModels';

class UserPackageInfo {
  currentTime: any;
  isSubscriptions: any;
  network: any;
  subscriptions: any;
  technicalErrorMessage: any;
  constructor(props: any) {
    this.currentTime = props?.result?.current_time;
    this.network = props?.result?.network;
    this.isSubscriptions = props?.result?.subscriptions.length > 0;
    this.subscriptions = subscriptions(props?.result?.subscriptions?.[0]);
    this.technicalErrorMessage = props?.result?.technical_error_message;
  }
}

export default UserPackageInfo;
