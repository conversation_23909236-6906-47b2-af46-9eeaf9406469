// Modules
@use 'sass:math';

/* =============== function caculate rem =============== */
@function rem($size) {
  //
  $remSize: math.div($size, 16);

  //
  @return #{$remSize}rem;
}

/* =============== style =============== */
.section--lobby-view {
  .title-font {
    font-size: rem(40);
    font-weight: 700;
  }
  article {
    margin-right: rem(36);
  }
  .right {
    top: 0;
  }

  @media screen and (max-width: rem(1024)) {
    .scrollLobby {
      overflow-x: auto;
      -ms-overflow-style: none;
      scrollbar-width: none;
      &::-webkit-scrollbar {
        display: none;
      }
      & > * {
        max-width: rem(80);
        margin-right: rem(8) !important;
        & > span {
          max-width: rem(80);
          max-height: rem(80);
          &:nth-child(2) {
            font-size: rem(14);
            line-height: 1.25;
          }
        }
      }
    }
    .right {
      position: absolute;
      top: rem(1);
      right: rem(8);
    }
  }

  @media screen and (max-width: rem(480)) {
    .title-font {
      font-size: rem(22);
    }
  }
}
