import React, { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getProfile } from '@actions/profile';
import ProfileCard from '../profileInfoSection/ProfileCard';
import { TEXT } from '@constants/text';
import ProfileCardItem from '../profileInfoSection/ProfileCardItem';
import { POPUP, PROVIDER } from '@constants/constants';
import UserApi from '@apis/userApi';
import { setToast } from '@actions/app';
import { openPopup } from '@actions/popup';
import GoogleButtonCustom from './GoogleButton';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import LocalStorage from '@config/LocalStorage';

declare const window: any;

const SocialLinked = ({ profile, isKid }: any) => {
  const { social, deviceModel, deviceName, deviceType } = useSelector(
    (state: any) => state?.App || {}
  );
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const dispatch = useDispatch();

  const loggedByProvider = useMemo(
    () => ConfigLocalStorage.get(LocalStorage.LOGGED_SOCIAL_BY_PROVIDER || ''),
    []
  );
  const { mobile, email, emailVerified, google, facebook, apple } = profile || {};
  const isFacebookLinked = facebook?.linked;
  const isGoogleLinked = google?.linked;
  const isAppleLinked = apple?.linked;

  const renderButtonGoogle = () =>
    mobile ||
    (!mobile && !isGoogleLinked) ||
    loggedByProvider === PROVIDER.FACEBOOK ||
    (isGlobal && loggedByProvider === PROVIDER.GOOGLE && (mobile || emailVerified))
      ? isGoogleLinked
        ? TEXT.USER_ACTION.UNLINK
        : TEXT.USER_ACTION.LINK
      : null;

  const renderButtonFacebook = () =>
    mobile ||
    (!mobile && !isFacebookLinked) ||
    loggedByProvider === PROVIDER.GOOGLE ||
    (isGlobal && loggedByProvider === PROVIDER.FACEBOOK && (mobile || emailVerified))
      ? isFacebookLinked
        ? TEXT.USER_ACTION.UNLINK
        : TEXT.USER_ACTION.LINK
      : null;

  const handleLinkFailed = (res: any) => {
    if (res?.code === 400) {
      dispatch(openPopup({ name: POPUP.NAME.LINK_SOCIAL_FAILED }));
    } else {
      dispatch(setToast({ message: TEXT.MSG_ERROR }));
    }
  };
  const handleUnLinkWithSocial = async ({ provider }: any) => {
    const res = await UserApi.unlinkWithSocial({ provider });
    if (res?.code === 0) {
      dispatch(setToast({ message: TEXT.UNLINK_SUCCESSFULLY }));
      dispatch(getProfile({ deviceModel, deviceName, deviceType }));
      return;
    }
    dispatch(setToast({ message: TEXT.MSG_ERROR }));
  };

  const handleLinkWithSocial = async ({ provider, token }: any) => {
    const res = await UserApi.linkWithSocial({ provider, token });
    if (res?.code === 0) {
      dispatch(setToast({ message: TEXT.LINK_SUCCESSFULLY }));
      dispatch(getProfile({ deviceModel, deviceName, deviceType }));
      return;
    }
    handleLinkFailed(res);
  };

  const handleLinkWithFacebook = async ({ provider }: any) => {
    const resp: any = await new Promise((resolve) => {
      window.FB.login(resolve, { scope: 'email' });
    });
    if (resp) {
      const token = resp?.authResponse?.accessToken;
      if (token) {
        handleLinkWithSocial({ provider, token });
      }
    }
  };

  const handleCheckProfileInfoToLink = () => {
    if (isGlobal) {
      if ((!email && !mobile) || (!emailVerified && !mobile)) {
        dispatch(openPopup({ name: POPUP.NAME.LINKED_SOCIAL_REQUEST_UPDATE_INFO }));
        return true;
      }
    } else if (!mobile) {
      dispatch(openPopup({ name: POPUP.NAME.LINKED_SOCIAL_REQUEST_UPDATE_PHONE }));
      return true;
    }
    return false;
  };

  const handleLinkSocial = async ({ provider, token }: any) => {
    if (provider === PROVIDER.FACEBOOK) {
      if (isFacebookLinked) {
        handleUnLinkWithSocial({ provider });
      }
    } else if (provider === PROVIDER.GOOGLE) {
      if (isGoogleLinked) {
        handleUnLinkWithSocial({ provider });
      }
    }
    if (handleCheckProfileInfoToLink()) return;
    if (provider === PROVIDER.FACEBOOK) {
      if (!isFacebookLinked) {
        await handleLinkWithFacebook({ provider });
      }
    } else if (provider === PROVIDER.GOOGLE) {
      if (!isGoogleLinked) {
        handleLinkWithSocial({ provider, token });
      }
    }
  };

  const socialProviders = [
    {
      label: apple?.fullname || TEXT.USER_LABEL.APPLE,
      provider: PROVIDER.APPLE,
      linked: isAppleLinked
    },
    {
      label: facebook?.fullname || TEXT.USER_LABEL.FACEBOOK,
      action: {
        name: renderButtonFacebook(),
        func: () => handleLinkSocial({ provider: PROVIDER.FACEBOOK })
      },
      provider: PROVIDER.FACEBOOK,
      linked: true
    },
    {
      label: google?.fullname || TEXT.USER_LABEL.GOOGLE,
      action: {
        name: (
          <GoogleButtonCustom
            isGlobal={isGlobal}
            isAllowTriggerGoogleLogin={((isGlobal && emailVerified) || mobile) && !isGoogleLinked}
            onCallback={handleLinkSocial}
            onSocialLink={() => handleLinkSocial({ provider: PROVIDER.GOOGLE })}
          >
            {renderButtonGoogle()}
          </GoogleButtonCustom>
        )
      },
      provider: PROVIDER.GOOGLE,
      linked: true
    }
  ];

  const renderContent = () =>
    socialProviders
      .filter((item) => item?.linked)
      .map((item: any) => (
        <ProfileCardItem
          key={item.label}
          label={item.label}
          value={item.value}
          action={item?.action}
          isDisabled={isKid}
          iconSocial={item.provider}
          title={item?.action?.name || ''}
        />
      ));

  if (!social?.allowLoginFB && !social?.allowLoginGG) {
    return null;
  }
  return <ProfileCard title={TEXT.SOCIAL_LINKED} renderContent={renderContent} />;
};

export default React.memo(SocialLinked);
