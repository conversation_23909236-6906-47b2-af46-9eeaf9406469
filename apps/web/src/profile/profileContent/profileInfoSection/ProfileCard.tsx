import React from 'react';
import classNames from 'classnames';
import styles from '@profile/profileContent/accountBilling/AccountBilling.module.scss';

const ProfileCard = ({ title, renderContent, isReferralCode, isNotBorderBottom }: any) => {
  const rootClass = classNames(
    'card card--profile card--profile-info',
    isReferralCode && 'card p-t4'
  );

  return (
    <div className={rootClass}>
      <div
        className={classNames(
          'grid-x relative card-section',
          !isNotBorderBottom && styles['border-bottom']
        )}
      >
        <div className="cell medium-3 large-2">
          <div className="card-divider">
            <div className={classNames('title title-white', isReferralCode && 'text-16 p-b2')}>
              {title || ''}
            </div>
          </div>
        </div>
        <div className="cell medium-9 large-10">{renderContent && renderContent()}</div>
      </div>
    </div>
  );
};
export default ProfileCard;
