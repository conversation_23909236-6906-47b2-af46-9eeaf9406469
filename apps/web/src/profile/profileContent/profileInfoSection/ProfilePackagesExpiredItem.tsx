import React from 'react';

const ProfilePackagesExpiredItem = (props: any) => {
  const { label, expiredDate } = props;
  let expiredDateLabel = 'Hết hạn: ';
  expiredDateLabel += expiredDate;
  return (
    <div className="cell shrink">
      <div className="card card--package-history expired md:pb-4 pb-3 !h-full">
        <h4 className="card__title title-white">{label || ''}</h4>
        <div className="relative">
          <p className="text text-white">{expiredDateLabel}</p>
        </div>
      </div>
    </div>
  );
};
export default ProfilePackagesExpiredItem;
