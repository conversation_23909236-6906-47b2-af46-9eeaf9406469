import React from 'react';
import { useSelector } from 'react-redux';
import Button from '@components/basic/Buttons/Button';
import isEmpty from 'lodash/isEmpty';
import styles from './CardItem.module.scss';
import ConfigImage from '@config/ConfigImage';
import Image from '@components/basic/Image/Image';
import classNames from 'classnames';

const ProfileCardItem = ({
  label,
  value,
  action,
  verified,
  isDisabled,
  iconSocial,
  title,
  customClassName
}: any) => {
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const renderSocialIcon = () => {
    if (iconSocial) {
      return (
        <div className={styles.socialIcon}>
          {iconSocial === 'apple' && (
            <Image notWebp src={ConfigImage.linkedApple} alt="linked with apple icon" />
          )}
          {iconSocial === 'facebook' && (
            <Image notWebp src={ConfigImage.linkedFacebook} alt="linked with facebook icon" />
          )}
          {iconSocial === 'google' && (
            <Image notWebp src={ConfigImage.linkedGoogle} alt="linked with google icon" />
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="grid-x m-b2">
      <div
        className={classNames(
          'cell medium-7',
          action?.name && isMobile && 'small-8',
          customClassName && styles[customClassName]
        )}
      >
        <span className={iconSocial ? styles.centerFlex : ''}>
          {renderSocialIcon()}
          <span className="text text-white text-large-up-16 font-normal">{label || ''}</span>
        </span>
        {value && <span className={styles.text}>{value}</span>}
        {verified && (
          <span className={styles.boxIcon}>
            <span
              className={`${styles.icon} ${verified?.value ? styles.active : ''}`}
              title={verified?.value ? 'Đã được xác nhận' : 'Chưa được xác nhận'}
            >
              <i className="vie vie-tick" />
            </span>
          </span>
        )}
      </div>
      {action?.func && (
        <>
          {!verified?.value && !isDisabled && isEmpty(iconSocial) && (
            <div className="cell small-4 medium-5 text-right">
              <Button
                className="button button--action p-0 size-h-auto button--medium"
                onClick={action?.func || (() => {})}
                title={action?.name}
                textClass="break-line"
              />
            </div>
          )}
          {iconSocial && (
            <div className="cell small-4 medium-5 text-right">
              <button
                className="button button--action p-0 size-h-auto button--medium"
                onClick={action?.func || (() => {})}
                title={title}
              >
                <span className="break-line">{title}</span>
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ProfileCardItem;
