import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getItemSubCate } from '@actions/user';
import { ID } from '@constants/constants';
import { TEXT } from '@constants/text';
import MemberPoint from './memberPoint';
import Tabs from './Tabs';
import styles from './ProfileLoyalty.module.scss';
import SeeAllPage from './memberPoint/seeAllPage';
import ActivityHistory from './activityHistory';

const TABS: any = [
  { title: TEXT.TAB.MEMBER_POINT, id: ID.MEMBER_POINT },
  { title: TEXT.TAB.ACTIVITY_HISTORY, id: ID.ACTIVITY_HISTORY }
];
const ProfileLoyalty = ({ tabId }: any) => {
  const [tab, setTab] = useState(TABS[0].id);
  const seeAllStatus = useSelector((state: any) => state?.User?.loyalty?.seeAllStatus || '');
  const dispatch = useDispatch();
  useEffect(() => () => dispatch(getItemSubCate(0)), []);

  const renderByStep = () => {
    if (seeAllStatus) {
      return <SeeAllPage />;
    }
    return (
      <>
        <div className="padding-small-up-top-16 padding-large-up-top-40">
          <Tabs data={TABS} id={tabId} setTab={setTab} tab={tab} />
        </div>
        {tab === ID.MEMBER_POINT && <MemberPoint />}
        {tab === ID.ACTIVITY_HISTORY && <ActivityHistory tabId={tabId} />}
      </>
    );
  };

  return (
    <div
      className={classNames(
        'tabs-panel size-w-full m-x-auto',
        tabId === ID.LOYALTY_POINT && 'active',
        styles.bg,
        tab === ID.ACTIVITY_HISTORY && styles['min-h-60vh'],
        seeAllStatus && styles['min-h-60vh'],
        seeAllStatus ? styles['loyalty-see-all'] : styles.loyalty
      )}
    >
      {renderByStep()}
    </div>
  );
};

export default React.memo(ProfileLoyalty);
