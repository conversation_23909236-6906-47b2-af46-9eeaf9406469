import React, { useEffect, useRef, useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import SwiperCore, { Navigation } from 'swiper/core';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import { getItemSubCate } from '@actions/user';
import 'swiper/swiper.min.css';
import 'swiper/components/navigation/navigation.min.css';
import Button from '@components/basic/Buttons/Button';
import UserApi from '@apis/userApi';
import { PAGE_MAX_SIZE } from '@constants/constants';
import styles from './RedeemVoucher.module.scss';

SwiperCore.use([Navigation]);

const SubItemSlider = ({ data, setListFiltered, categoryId, seeAllStatus }: any) => {
  const isMobile = useSelector((state: any) => state?.App?.isMobile || false);
  const swiperRef = useRef<any>(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [isEndItem, setEndItem] = useState(false);
  const [slideClass, setSlideClass] = useState<any>(0);
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const subItemCategory = useSelector((state: any) => state?.User?.loyalty?.subItemCategory || 0);
  const dispatch = useDispatch();

  const modifiedData = [{ id: 'view-all', name: 'Tất cả', description: 'view all' }, ...data];

  useEffect(() => {
    if (isEndItem) {
      setSlideClass(styles['m-r-full']);
    } else {
      setSlideClass('');
    }
  }, [isEndItem]);

  useEffect(() => {
    if (subItemCategory) {
      UserApi.getVouchers({
        userId: profile?.id,
        categoryIds: categoryId,
        subCategoryIds: subItemCategory,
        voucherPageSize: seeAllStatus ? PAGE_MAX_SIZE : 6
      }).then((res) => {
        if (res?.isSuccess) {
          setListFiltered(res?.data?.items?.[0]?.vouchers);
        }
      });
    }
  }, [subItemCategory, profile?.id, seeAllStatus]);

  const handleClickItem = (item: any, index: any) => {
    if ((swiperRef.current && activeIndex !== index) || subItemCategory !== item?.id) {
      setActiveIndex(index);
      swiperRef.current.slideTo(index - 1);
      dispatch(getItemSubCate(item?.id));
    }
  };

  const handleSlideChangeTransitionEnd = () => {
    if (swiperRef.current) {
      const { isEnd } = swiperRef.current;
      setEndItem(isEnd);
    }
  };

  const handleClickNext = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideNext();
    }
  };

  const handleClickPrev = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slidePrev();
    }
  };

  return (
    <div className="relative">
      <Swiper
        navigation={{
          nextEl: '.custom-next',
          prevEl: '.custom-prev'
        }}
        slidesPerView="auto"
        allowTouchMove={isMobile}
        spaceBetween={isMobile ? 12 : 24}
        onSwiper={(swiper) => (swiperRef.current = swiper)}
        className={classNames(styles['slider-sub-items'], 'overflow', slideClass)}
        onSlideChangeTransitionEnd={handleSlideChangeTransitionEnd}
      >
        {(data?.length > 1 ? modifiedData : data)?.map((item: any, index: any) => (
          <SwiperSlide key={item?.id + index}>
            <Button
              title={item?.name}
              onClick={() => handleClickItem(item, index)}
              subTitle={item?.name}
              className={classNames(
                'text-14',
                (subItemCategory ? subItemCategory === item?.id : activeIndex === index)
                  ? 'text-white text-bold'
                  : 'text-gray239'
              )}
            />
          </SwiperSlide>
        ))}
      </Swiper>

      <button
        className={classNames(styles.prev, 'custom-prev left absolute layer-1')}
        onClick={handleClickPrev}
      >
        <span className="icon icon--tiny-xs icon--small text-white">
          <i className="vie vie-chevron-left-r-medium" />
        </span>
      </button>
      <button
        className={classNames(styles.next, 'custom-next right absolute layer-1')}
        onClick={handleClickNext}
      >
        <span className="icon icon--tiny-xs icon--small text-white">
          <i className="vie vie-chevron-right-r-medium" />
        </span>
      </button>
    </div>
  );
};

export default React.memo(SubItemSlider);
