import classNames from 'classnames';
import React, { useEffect, useMemo, useState } from 'react';
import Button from '@components/basic/Buttons/Button';
import SvgIcon from '@components/basic/Icon/SvgIcon';
import { ICON_KEY, TIER } from '@constants/constants';
import { useSelector } from 'react-redux';
import { TEXT } from '@constants/text';
import styles from './Banner.module.scss';

const Benefit = ({ currentTier }: any) => {
  const [tier, setTier] = useState(currentTier || TIER.BRONZE);
  const [benefitDataById, setBenefitDataById] = useState<any>({});
  const tierBenefits = useSelector((state: any) => state?.User?.loyalty?.tierBenefits || []);
  const isMobile = useSelector((state: any) => state?.App?.isMobile || false);

  const currentTierData = useMemo(() => {
    const map = {
      [TIER.BRONZE]: { id: 1, class: styles.bronze, iconClass: styles['bg-icon-bronze'] },
      [TIER.SILVER]: { id: 2, class: styles.silver, iconClass: styles['bg-icon-silver'] },
      [TIER.GOLD]: { id: 3, class: styles.gold, iconClass: styles['bg-gold'] },
      [TIER.DIAMOND]: { id: 4, class: styles.diamond, iconClass: styles['bg-diamond'] }
    };
    return map[tier];
  }, [tier]);

  const currentBackgroundClass = useMemo(() => {
    const map = {
      [TIER.BRONZE]: styles['bg-bronze'],
      [TIER.SILVER]: styles['bg-silver'],
      [TIER.GOLD]: styles['bg-gold'],
      [TIER.DIAMOND]: styles['bg-diamond']
    };
    return map[currentTier];
  }, [currentTier]);

  useEffect(() => {
    if (currentTier) return setTier(currentTier);
  }, [currentTier]);

  useEffect(() => {
    setBenefitDataById(tierBenefits?.find((item: any) => item?.name === currentTier));
  }, [currentTier, tierBenefits]);

  const handleClickTab = (e: any, item: any) => {
    e.preventDefault();
    if (item.name !== benefitDataById?.name) {
      setTier(item.name);
      setBenefitDataById(tierBenefits?.find((it: any) => it?.name === item.name));
    }
  };

  return (
    <div className="padding-small-up-bottom-8 padding-medium-up-bottom">
      <div className="flex-box align-middle padding-xlarge-up-top-12 padding-small-up-bottom-8 padding-xlarge-up-bottom-16">
        {tierBenefits?.map((item: any) => (
          <Button
            key={item.id}
            onClick={(e: any) => handleClickTab(e, item)}
            className={classNames(
              styles['button-shadow'],
              item.name === benefitDataById?.name && currentTierData?.class,
              item.name === benefitDataById?.name && styles.active,
              item.name === benefitDataById?.name && currentBackgroundClass,
              'text-bold text-14 text-xlarge-up-16 text-uppercase padding-x-small-up-8 padding-y-small-up-6 padding-x-large-up-16 padding-x-xlarge-up-24'
            )}
            title={item.name}
            subTitle={item.name}
            textClass="text-medium"
          />
        ))}
      </div>
      {tier === TIER.BRONZE && (
        <div className="text-gray173 text-14 text-large-up-16 p-b2">
          {TEXT.BENEFIT_CONTENT_MEMBER}
        </div>
      )}
      <div className={styles['w-75']}>
        {benefitDataById?.tierBenefits?.slice(0, 6)?.map((item: any, index: any) => (
          <div
            className={classNames(styles['benefit-item'], styles.flex, 'p-b2 align-middle')}
            key={item.sequence + index}
          >
            <div
              className={classNames(
                'icon icon--tiny',
                styles['icon-check'],
                currentTierData?.iconClass
              )}
            >
              <SvgIcon type={tier === TIER.BRONZE ? ICON_KEY.LOCK : ICON_KEY.CHECK} />
            </div>
            <span
              className="text-12 text-large-up-16 padding-small-up-left-8 padding-large-up-left-16 text-gray173 line-clamp"
              data-line-clamp="1"
            >
              {item.benefitDescription}
            </span>
          </div>
        ))}
        {benefitDataById?.tierBenefits?.length > 6 && (
          <div
            className={classNames(
              'text-gray173 text-12 text-large-up-16 absolute',
              isMobile && 'bottom'
            )}
          >
            {TEXT.BENEFIT_OTHER_VOUCHER}
            <span className="text-12">({TEXT.BENEFIT_OTHER_VOUCHER_2})</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(Benefit);
