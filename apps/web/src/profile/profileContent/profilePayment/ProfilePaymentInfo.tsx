import React from 'react';
import { ID } from '@constants/constants';
import EmptyTransaction from '@components/empty/EmptyTransaction';
import PaymentInfo from './PaymentInfo';

const ProfilePaymentInfo = (props: any) => {
  const { tabId, profile, onHandleAction, onViewDetail, transactions, purchased } = props;
  const { subscription_expired, subscription_remain, subscription_use } = purchased?.data || {};
  const subscriptionExpired = subscription_expired?.items;
  const subscriptionRemain = subscription_remain?.items;
  const subscriptionUse = subscription_use?.items;
  const isTransaction =
    (subscriptionUse || [])?.length > 0 ||
    (subscriptionRemain || [])?.length > 0 ||
    (subscriptionExpired || [])?.length > 0 ||
    transactions?.data?.items?.length > 0;
  let itemClass = 'tabs-panel';
  if (tabId === ID.PAYMENT_INFO) {
    itemClass += ' is-active';
  }
  return (
    <div className={itemClass}>
      {isTransaction ? (
        <PaymentInfo
          {...props}
          purchased={purchased}
          profile={profile}
          transactions={transactions}
          onHandleAction={onHandleAction}
          onViewDetail={onViewDetail}
          isTransaction={isTransaction}
          subscriptionUse={subscriptionUse}
          subscriptionExpired={subscriptionExpired}
          subscriptionRemain={subscriptionRemain}
        />
      ) : (
        <EmptyTransaction {...props} />
      )}
    </div>
  );
};

export default ProfilePaymentInfo;
