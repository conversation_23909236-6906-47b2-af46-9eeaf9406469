import React from 'react';
import { ID } from '@constants/constants';
import ProfileRestrictionContent from '@profile/profileContent/restrictionContent/ProfileRestrictionContent';
import AccountBilling from './accountBilling/AccountBilling';
import ProfilePaymentInfo from './profilePayment/ProfilePaymentInfo';
import ProfileWatching from './profileWatching/ProfileWatching';
import ProfileFavourite from './profileFavourite/ProfileFavourite';
import ProfileContentRent from './profileContentRent/ProfileContentRent';
import ProfileDeviceManagement from './profileDeviceManagement/ProfileDeviceManagement';
import ProfileKidsActivity from './profileKidsActivity';
import ProfileLoyalty from './profileLoyalty';

const ProfileContent = ({
  tabId,
  profile,
  onHandleAction,
  favoriteData,
  watchingData,
  handleLoadMoreWatching,
  handleLoadMoreFavorite,
  updateAllowPush,
  transactions,
  onViewDetail,
  purchased,
  isOnKidsActivity,
  isOnLoyalty,
  isShowTitleRestriction,
  handleRestriction,
  isProfileDefault
}: any) => (
  <div className="tabs-content" data-tabs-content="profiles">
    <AccountBilling
      tabId={tabId}
      profile={profile}
      onHandleAction={onHandleAction}
      updateAllowPush={updateAllowPush}
    />
    {tabId === ID.PAYMENT_INFO && (
      <ProfilePaymentInfo
        purchased={purchased}
        tabId={tabId}
        profile={profile}
        transactions={transactions}
        onHandleAction={onHandleAction}
        updateAllowPush={updateAllowPush}
        onViewDetail={onViewDetail}
      />
    )}

    {tabId === ID.WATCHING && (
      <ProfileWatching
        tabId={tabId}
        profile={profile}
        onHandleAction={onHandleAction}
        updateAllowPush={updateAllowPush}
        watchingData={watchingData}
        loadMoreData={handleLoadMoreWatching}
      />
    )}
    {tabId === ID.FAVORITE && (
      <ProfileFavourite
        tabId={tabId}
        profile={profile}
        favoriteData={favoriteData}
        onHandleAction={onHandleAction}
        updateAllowPush={updateAllowPush}
        loadMoreData={handleLoadMoreFavorite}
      />
    )}
    {tabId === ID.CONTENT_RENT && <ProfileContentRent tabId={tabId} profile={profile} />}
    {tabId === ID.DEVICE_MANAGEMENT && <ProfileDeviceManagement tabId={tabId} />}
    {tabId === ID.KIDS_MANAGEMENT && isOnKidsActivity !== false && (
      <ProfileKidsActivity onHandleAction={onHandleAction} tabId={tabId} />
    )}
    {tabId === ID.LOYALTY_POINT && isOnLoyalty && <ProfileLoyalty tabId={tabId} />}
    {tabId === ID.RESTRICTION_CONTENT && isShowTitleRestriction && isProfileDefault && (
      <ProfileRestrictionContent tabId={tabId} handleRestriction={handleRestriction} />
    )}
  </div>
);

export default ProfileContent;
