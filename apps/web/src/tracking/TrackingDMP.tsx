import React from 'react';
import { NODE_ENV } from '@constants/constants';
import { DMP_ID, ENV, ENABLE_SDK_AIACTIV } from '@config/ConfigEnv';

let dmpLoadedTimerCheck: any = null;
let timerCount = 0;

declare const AiactivSDK: any;

export const DMPScript = ({ defer }: any) => {
  if (ENV !== NODE_ENV.PROD || !DMP_ID || !ENABLE_SDK_AIACTIV) return null;
  const scriptContent = `window.AiactivSDK||(window.AiactivSDK={}),AiactivSDK.load=function(t){var e=document.createElement("script");e.async=!0,e.type="text/javascript",e.src="https://sdk-cdn.aiactiv.io/aiactiv-sdk.min.js?t="+Date.now(),e.addEventListener?e.addEventListener("load",function(e){"function"==typeof t&&t(e)},!1):e.onreadystatechange=function(){("complete"==this.readyState||"loaded"==this.readyState)&&t(window.event)};let a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(e,a)},AiactivSDK.load(function(){AiactivSDK.initialize({containerId:"${DMP_ID}", type: ["adnetwork", "dmp"]}),AiactivSDK.callMethodsFromContainer()});`;
  const script = (
    <script
      defer={defer}
      type="text/javascript"
      dangerouslySetInnerHTML={{ __html: scriptContent }}
    />
  );
  return script;
};

export const dmpIdentify = (userId: any, params: any) => {
  if (typeof window === 'undefined') return;
  if (
    typeof window?.AiactivSDK === 'undefined' ||
    typeof window?.AiactivSDK?.identify !== 'function'
  ) {
    if (DMP_ID) {
      if (dmpLoadedTimerCheck) clearInterval(dmpLoadedTimerCheck);
      dmpLoadedTimerCheck = setInterval(() => {
        timerCount += 1;
        if (typeof window?.AiactivSDK?.identify === 'function') {
          if (dmpLoadedTimerCheck) clearInterval(dmpLoadedTimerCheck);
          timerCount = 0;
          window.AiactivSDK.identify(userId, params);
        }
        if (timerCount >= 5) {
          if (dmpLoadedTimerCheck) clearInterval(dmpLoadedTimerCheck);
          timerCount = 0;
        }
      }, 2000);
    }
  } else {
    if (dmpLoadedTimerCheck) clearInterval(dmpLoadedTimerCheck);
    AiactivSDK.identify(userId, params);
    timerCount = 0;
  }
};

export const dmpEvent = (eventName: any, params?: any) => {
  if (!window || !window?.AiactivSDK || !window?.AiactivSDK.track) return;
  AiactivSDK.track(eventName, params);
};

// TODO: DMP và AIActiv Adnetwork đang dùng chung, để function của AIActiv Adnetwork trong đây tạm, để dễ quản lý
// sau này nếu dùng DMP khác thì tách function của AIActiv Adnetwork ra sau
export const getVASTTagByInventory = async (inventoryId: any, callback: any) => {
  if (!window || !window?.AiactivSDK || !window?.AiactivSDK.requestAds) return;
  try {
    const res = await window.AiactivSDK.requestAds([{ inventoryId, placementId: 'ads-popup' }]);
    callback(res[0].vastTagURL || '');
  } catch (e) {
    callback('');
  }
};
