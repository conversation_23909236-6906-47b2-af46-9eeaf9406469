import * as Sentry from '@sentry/nextjs';
import { SENTRY_SERVER, SENTRY_ENV } from '@config/ConfigEnv';
import { CONTENT_TYPE } from '@constants/constants';
import { ERROR_PLAYER } from '@constants/player';
import { ERROR } from '@config/ConfigErrorPlayer';
import { detectWebViewUA } from '@helpers/common';

const SENTRY_DEF = {
  MESSAGE: {
    VOD_PLAYER: 'VOD_PLAYER',
    LIVETV_PLAYER: 'LIVETV_PLAYER',
    LIVESTREAM_PLAYER: 'LIVESTREAM_PLAYER',
    API: 'API',
    PAYMENT: 'PAYMENT'
  },
  TYPE: {
    PLAYER: 'PLAYER',
    API_DETAIL: 'API_DETAIL',
    EMPTY_LINK: 'EMPTY_LINK',
    QNET: 'QNET',
    VALIDATE_KPLUS: 'VALIDATE_KPLUS',
    SOCKET: 'SOCKET'
  },
  LEVEL: {
    ERROR: 'error'
  }
};

const sentryInit = () => {
  Sentry.init({
    dsn: SENTRY_SERVER,
    defaultIntegrations: false,
    integrations: [
      new Sentry.Integrations.Breadcrumbs(),
      new Sentry.Integrations.HttpContext(),
      new Sentry.Integrations.Dedupe()
    ],
    normalizeDepth: 5,
    tracesSampleRate: 0.0,
    environment: SENTRY_ENV || 'TESTING'
  });
};

const sentryCaptureEvent = (params: any) => {
  if (typeof window === 'undefined') return;
  const token = params?.extra?.token;
  if (!token) {
    const store = window.__NEXT_REDUX_STORE__;
    const state = store.getState();
    const appState = state?.App;
    if (params?.extra) params.extra.token = appState?.token;
  }

  Sentry.captureMessage(params.message, params);
};

const sentryCatError = ({
  vodData,
  liveTVData,
  livestreamData,
  socketData,
  qnetData,
  detailData,
  playerData,
  validateKPlusData,
  errorType
}: any) => {
  if (typeof window === 'undefined') return;
  const { linkPlayDetail }: any = window;
  const store = window.__NEXT_REDUX_STORE__;
  const state = store.getState();
  const appState = state?.App;
  const profile = state?.Profile?.profile;
  const { mobile: phone, email } = profile || {};

  let message = '';
  let errorCode = '';
  let errorMessage = '';
  let contentType = '';
  let contentId = '';
  let contentName = '';
  const { content, currentEpisode, contentDetail } = vodData || {};
  const { detailChannel } = liveTVData || {};
  const { streamDetail } = livestreamData || {};

  if (vodData) {
    message = SENTRY_DEF.MESSAGE.VOD_PLAYER;
    contentType = currentEpisode?.type || content?.type;
    contentId = contentDetail?.id || currentEpisode?.id || content?.id;
    contentName = `${content?.title}${currentEpisode?.title ? ` - ${currentEpisode?.title}` : ''}`;
  } else if (liveTVData) {
    message = SENTRY_DEF.MESSAGE.LIVETV_PLAYER;
    contentType = CONTENT_TYPE.LIVE_TV;
    contentId = detailChannel?.id;
    contentName = detailChannel?.title;
  } else if (livestreamData) {
    message = SENTRY_DEF.MESSAGE.LIVESTREAM_PLAYER;
    contentType = CONTENT_TYPE.LIVESTREAM;
    contentId = streamDetail?.id;
    contentName = streamDetail?.title;
  }

  if (playerData) {
    errorCode = playerData.code;

    errorMessage = ERROR[errorCode];
  } else if (socketData) {
    errorCode = socketData.type_msg;
    errorMessage = socketData.type_msg;
  } else if (qnetData) {
    errorCode = qnetData.httpCode;
  } else if (detailData) {
    errorCode = detailData.httpCode;
    errorMessage = detailData.message;
  } else if (validateKPlusData) {
    errorCode = validateKPlusData.httpCode;
    errorMessage = validateKPlusData.message;
  }

  const isWebView = detectWebViewUA(appState?.userAgent);

  const tags = {
    errorType: errorType || (playerData ? ERROR_PLAYER.TYPE.PLAYER : ERROR_PLAYER.TYPE.UNDEFINED),
    errorCode: errorCode || errorType,
    errorMessage: errorMessage || 'NONE',
    contentType,
    contentId,
    contentName,
    phone: phone || 'NONE',
    email: email || 'NONE',
    webView: isWebView,
    recordId: generateRandomString(6)
  };

  if (message) {
    sentryCaptureEvent({
      message,
      level: 'error',
      extra: { linkPlayDetail },
      user: profile,
      tags
    });
  }
};

function generateRandomString(length: any) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let randomString = '';
  for (let i = 0; i < length; i += 1) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    randomString += characters.charAt(randomIndex);
  }
  return randomString;
}

export { sentryInit, sentryCaptureEvent, sentryCatError };
