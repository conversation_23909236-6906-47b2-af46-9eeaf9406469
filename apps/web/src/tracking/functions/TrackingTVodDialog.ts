import { segmentEvent } from '../TrackingSegment';

const { NAME, PROPERTY } = require('@config/ConfigSegment');

const dialogLivestreamEndLoaded = ({ flowName }: any) => {
  segmentEvent(NAME.TVOD.DIALOG_LIVE_STREAM_END_LOADED, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};
const dialogLivestreamEndHomePage = ({ flowName }: any) => {
  segmentEvent(NAME.TVOD.DIALOG_LIVE_STREAM_END_HOME_PAGE, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};
const dialogLivestreamEndWatch = ({ flowName }: any) => {
  segmentEvent(NAME.TVOD.DIALOG_LIVE_STREAM_END_WATCH, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};
const dialogLivestreamEndClose = ({ flowName }: any) => {
  segmentEvent(NAME.TVOD.DIALOG_LIVE_STREAM_END_CLOSE, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};
const dialogRentLoaded = ({ userType, data }: any) => {
  if (!data) return null;
  return segmentEvent(NAME.TVOD.DIALOG_RENT_LOADED, {
    [PROPERTY.USER_TYPE]: userType || '',
    [PROPERTY.CONTENT_NAME]: data?.title || '',
    [PROPERTY.CONTENT_ID]: data?.id || ''
  });
};
const dialogRentLoadedWatch = ({ userType, data }: any) => {
  if (!data) return null;
  return segmentEvent(NAME.TVOD.DIALOG_RENT_LOADED_WATCH, {
    [PROPERTY.USER_TYPE]: userType || '',
    [PROPERTY.CONTENT_NAME]: data?.title || '',
    [PROPERTY.CONTENT_ID]: data?.id || ''
  });
};
const dialogRentLoadedHomePage = ({ data, userType }: any) => {
  if (!data) return null;
  return segmentEvent(NAME.TVOD.DIALOG_RENT_LOADED_HOME_PAGE, {
    [PROPERTY.USER_TYPE]: userType || '',
    [PROPERTY.CONTENT_NAME]: data?.title || '',
    [PROPERTY.CONTENT_ID]: data?.id || ''
  });
};
const dialogMissedEventLoaded = ({ data, userType, flowName }: any) => {
  if (!data) return null;
  return segmentEvent(NAME.TVOD.DIALOG_MISSED_EVENT_LOADED, {
    [PROPERTY.USER_TYPE]: userType || '',
    [PROPERTY.CONTENT_NAME]: data?.title || '',
    [PROPERTY.CONTENT_ID]: data?.id || '',
    [PROPERTY.FLOW_NAME]: flowName || ''
  });
};
const dialogMissedEventHomePage = ({ data, userType, flowName }: any) => {
  if (!data) return null;
  return segmentEvent(NAME.TVOD.DIALOG_MISSED_EVENT_HOME_PAGE, {
    [PROPERTY.USER_TYPE]: userType || '',
    [PROPERTY.CONTENT_NAME]: data?.title || '',
    [PROPERTY.CONTENT_ID]: data?.id || '',
    [PROPERTY.FLOW_NAME]: flowName || ''
  });
};
const dialogMissedEventClose = ({ data, userType, flowName }: any) => {
  if (!data) return null;
  return segmentEvent(NAME.TVOD.DIALOG_MISSED_EVENT_CLOSE, {
    [PROPERTY.USER_TYPE]: userType || '',
    [PROPERTY.CONTENT_NAME]: data?.title || '',
    [PROPERTY.CONTENT_ID]: data?.id || '',
    [PROPERTY.FLOW_NAME]: flowName || ''
  });
};

export {
  dialogLivestreamEndLoaded,
  dialogLivestreamEndHomePage,
  dialogLivestreamEndWatch,
  dialogLivestreamEndClose,
  dialogRentLoaded,
  dialogRentLoadedWatch,
  dialogRentLoadedHomePage,
  dialogMissedEventLoaded,
  dialogMissedEventHomePage,
  dialogMissedEventClose
};
