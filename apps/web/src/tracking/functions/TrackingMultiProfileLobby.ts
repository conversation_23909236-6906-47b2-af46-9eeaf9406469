import { NAME, PROPERTY } from '@config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';

const confirmLoad = ({ flowName, userType }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_VIEW.MULTI_PROFILE_CONFIRM_LOAD,
    {
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.FLOW_NAME]: flowName
    },
    false
  );
};

const confirmOver18 = ({ flowName, userType }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_VIEW.MULTI_PROFILE_CONFIRM_OVER_18,
    {
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.FLOW_NAME]: flowName
    },
    false
  );
};

const confirmUnder18 = ({ flowName, userType, isTrackImmediately }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_VIEW.MULTI_PROFILE_CONFIRM_UNDER_18,
    {
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.FLOW_NAME]: flowName
    },
    isTrackImmediately
  );
};

const registrationButtonSelected = ({ flowName }: any) => {
  segmentEvent(NAME.MULTI_PROFILE.LOBBY_VIEW.REGISTRATION_BUTTON_SELECTED, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};

const lobbyWhoWatchingLoad = ({ flowName, userType, isTrackImmediately }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_VIEW.MULTI_PROFILE_LOBBY_WHO_WATCHING_LOAD,
    {
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.FLOW_NAME]: flowName
    },
    isTrackImmediately
  );
};

const chooseProfile = ({
  userType,
  profileOrder,
  menuName,
  menuId,
  ribbonName,
  ribbonId,
  isTrackImmediately
}: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_VIEW.MULTI_PROFILE_CHOOSE,
    {
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.PROFILE_ORDER]: profileOrder,
      [PROPERTY.MENU_NAME]: menuName,
      [PROPERTY.MENU_ID]: menuId || '',
      [PROPERTY.RIBBON_NAME]: ribbonName || '',
      [PROPERTY.RIBBON_ID]: ribbonId || ''
    },
    isTrackImmediately
  );
};

const config = ({ flowName, userType }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_VIEW.MULTI_PROFILE_CONFIG,
    {
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.FLOW_NAME]: flowName
    },
    false
  );
};

const configChoose = ({ flowName, userType }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_VIEW.MULTI_PROFILE_CONFIG_CHOOSE,
    {
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.FLOW_NAME]: flowName
    },
    false
  );
};

const configComplete = ({ flowName, userType }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_VIEW.MULTI_PROFILE_CONFIG_COMPLETE,
    {
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.FLOW_NAME]: flowName
    },
    false
  );
};

const addProfile = ({ flowName, userType }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_VIEW.MULTI_PROFILE_ADD,
    {
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.FLOW_NAME]: flowName
    },
    false
  );
};

export {
  confirmLoad,
  confirmOver18,
  confirmUnder18,
  lobbyWhoWatchingLoad,
  chooseProfile,
  config,
  configChoose,
  configComplete,
  addProfile,
  registrationButtonSelected
};
