import { NAME } from '@config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';

const dialogLogoutKidModeLoad = () => {
  segmentEvent(NAME.MULTI_PROFILE.CHOOSE_PROFILE.DIALOG_LOGOUT_KID_MODE_LOAD, null, false);
};

const dialogLogoutKidModeAccept = ({ isTrackImmediately }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.CHOOSE_PROFILE.DIALOG_LOGOUT_KID_MODE_ACCEPT,
    null,
    isTrackImmediately
  );
};

const dialogLogoutKidModeClose = () => {
  segmentEvent(NAME.MULTI_PROFILE.CHOOSE_PROFILE.DIALOG_LOGOUT_KID_MODE_CANCEL, null, false);
};

const dialogNotSpendForKidLoad = () => {
  segmentEvent(NAME.MULTI_PROFILE.CHOOSE_PROFILE.DIALOG_NOT_SPEND_FOR_KID_LOAD, null, false);
};

const dialogNotSpendForKidReturnHome = () => {
  segmentEvent(
    NAME.MULTI_PROFILE.CHOOSE_PROFILE.DIALOG_NOT_SPEND_FOR_KID_RETURN_HOMEPAGE,
    null,
    true
  );
};

const dialogCannotAccessContentLoad = () => {
  segmentEvent(NAME.MULTI_PROFILE.CHOOSE_PROFILE.DIALOG_CANNOT_ACCESS_CONTENT_LOAD, null, false);
};

const dialogCannotAccessContentClose = () => {
  segmentEvent(NAME.MULTI_PROFILE.CHOOSE_PROFILE.DIALOG_CANNOT_ACCESS_CONTENT_CLOSE, null, false);
};

const emptyAvatarLoaded = () => {
  segmentEvent(NAME.MULTI_PROFILE.CHOOSE_PROFILE.AVATAR_NOT_LOAD_LOAD, null, false);
};

const emptyAvatarLoadedTryAgain = () => {
  segmentEvent(NAME.MULTI_PROFILE.CHOOSE_PROFILE.AVATAR_NOT_LOAD_TRY_AGAIN, null, false);
};

export {
  dialogLogoutKidModeLoad,
  dialogLogoutKidModeAccept,
  dialogLogoutKidModeClose,
  dialogNotSpendForKidLoad,
  dialogNotSpendForKidReturnHome,
  dialogCannotAccessContentLoad,
  dialogCannotAccessContentClose,
  emptyAvatarLoaded,
  emptyAvatarLoadedTryAgain
};
