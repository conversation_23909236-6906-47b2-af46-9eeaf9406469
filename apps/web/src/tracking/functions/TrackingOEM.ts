import { NAME, PROPERTY } from '@config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';
import { getMenuInfo } from './TrackingApp';

export const impressionBanner = ({ bannerName, bannerId, bannerOrder }: any) => {
  const { menuId, menuName, menuOrder, subMenuId, subMenuName, subMenuOrder } = getMenuInfo() || {};
  segmentEvent(NAME.IMPRESSION_BANNER, {
    [PROPERTY.BANNER_NAME]: bannerName,
    [PROPERTY.BANNER_ID]: bannerId,
    [PROPERTY.CURRENT_PAGE]: window?.location?.href,
    [PROPERTY.BANNER_ORDER]: bannerOrder,
    [PROPERTY.MENU_ID]: menuId,
    [PROPERTY.MENU_NAME]: menuName,
    [PROPERTY.MENU_ORDER]: menuOrder,
    [PROPERTY.SUB_MENU_ID]: subMenuId || null,
    [PROPERTY.SUB_MENU_NAME]: subMenuName || null,
    [PROPERTY.SUB_MENU_ORDER]: subMenuOrder || null
  });
};
