import { NAME, PROPERTY } from '@config/ConfigSegment';
import { segmentEvent } from '@tracking/TrackingSegment';
import isEmpty from 'lodash/isEmpty';

const showIndicator = ({ data, sessionId }: any) => {
  if (isEmpty(data)) return;
  const params: any = {
    [PROPERTY.CONTENT_ID]: data?.id,
    [PROPERTY.CONTENT_TITLE]: data?.title,
    [PROPERTY.SESSION_ID]: sessionId
  };
  segmentEvent(NAME.SHOW_INDICATOR, params, false);
};
const showIndicatorSelected = () => {
  segmentEvent(NAME.SHOW_INDICATOR_SELECTED, {}, false);
};
const hideIndicatorSelected = () => {
  segmentEvent(NAME.HIDE_INDICATOR_SELECTED, {}, false);
};
const indicatorSelected = ({ data, item, sessionId }: any) => {
  if (isEmpty(data)) return;
  const params: any = {
    [PROPERTY.CONTENT_ID]: data?.id,
    [PROPERTY.CONTENT_TITLE]: data?.title,
    [PROPERTY.SESSION_ID]: sessionId,
    [PROPERTY.PRODUCT_NAME]: item?.name,
    [PROPERTY.BRAND_NAME]: item?.props?.brand?.name
  };
  segmentEvent(NAME.INDICATOR_SELECTED, params, false);
};
const showAllProductInScene = ({ data, sessionId }: any) => {
  if (isEmpty(data)) return;
  const params: any = {
    [PROPERTY.CONTENT_ID]: data?.id,
    [PROPERTY.CONTENT_TITLE]: data?.title,
    [PROPERTY.SESSION_ID]: sessionId
  };
  segmentEvent(NAME.SHOW_ALL_PRODUCT_IN_SCENE, params, false);
};
const goProductPageButtonSelected = ({ data, item, sessionId }: any) => {
  if (isEmpty(data)) return;
  const params: any = {
    [PROPERTY.CONTENT_ID]: data?.id,
    [PROPERTY.CONTENT_TITLE]: data?.title,
    [PROPERTY.SESSION_ID]: sessionId,
    [PROPERTY.PRODUCT_NAME]: item?.name,
    [PROPERTY.BRAND_NAME]: item?.props?.brand?.name
  };
  segmentEvent(NAME.GO_PRODUCT_PAGE_BUTTON_SELECTED, params, false);
};
const telConsultButtonSelected = ({ data, item, sessionId }: any) => {
  if (isEmpty(data)) return;
  const params: any = {
    [PROPERTY.CONTENT_ID]: data?.id,
    [PROPERTY.CONTENT_TITLE]: data?.title,
    [PROPERTY.SESSION_ID]: sessionId,
    [PROPERTY.PRODUCT_NAME]: item?.name,
    [PROPERTY.BRAND_NAME]: item?.props?.brand?.name
  };
  segmentEvent(NAME.TEL_CONSULT_BUTTON_SELECTED, params, false);
};
const telConsultConfirmButtonSelected = ({ data, item, phoneNumber, sessionId }: any) => {
  if (isEmpty(data)) return;
  const params: any = {
    [PROPERTY.CONTENT_ID]: data?.id,
    [PROPERTY.CONTENT_TITLE]: data?.title,
    [PROPERTY.SESSION_ID]: sessionId,
    [PROPERTY.PRODUCT_NAME]: item?.name,
    [PROPERTY.BRAND_NAME]: item?.props?.brand?.name,
    [PROPERTY.MOBILE]: phoneNumber
  };
  segmentEvent(NAME.TEL_CONSULT_CONFIRM_BUTTON_SELECTED, params, false);
};
const telConsultCancelButtonSelected = ({ data, item, sessionId }: any) => {
  if (isEmpty(data)) return;
  const params: any = {
    [PROPERTY.CONTENT_ID]: data?.id,
    [PROPERTY.CONTENT_TITLE]: data?.title,
    [PROPERTY.SESSION_ID]: sessionId,
    [PROPERTY.PRODUCT_NAME]: item?.name,
    [PROPERTY.BRAND_NAME]: item?.props?.brand?.name
  };
  segmentEvent(NAME.TEL_CONSULT_CANCEL_BUTTON_SELECTED, params, false);
};
const showAllProductInContent = ({ data, sessionId }: any) => {
  if (isEmpty(data)) return;
  const params: any = {
    [PROPERTY.CONTENT_ID]: data?.id,
    [PROPERTY.CONTENT_TITLE]: data?.title,
    [PROPERTY.SESSION_ID]: sessionId
  };
  segmentEvent(NAME.SHOW_ALL_PRODUCT_IN_CONTENT, params, false);
};
const productPageClosed = ({ data, sessionId }: any) => {
  if (isEmpty(data)) return;
  const params: any = {
    [PROPERTY.CONTENT_ID]: data?.id,
    [PROPERTY.CONTENT_TITLE]: data?.title,
    [PROPERTY.SESSION_ID]: sessionId
  };
  segmentEvent(NAME.PRODUCT_PAGE_CLOSED, params, false);
};
const exploreBrandButtonSelected = ({ data, item, sessionId }: any) => {
  if (isEmpty(data)) return;
  const params: any = {
    [PROPERTY.CONTENT_ID]: data?.id,
    [PROPERTY.CONTENT_TITLE]: data?.title,
    [PROPERTY.SESSION_ID]: sessionId,
    [PROPERTY.BRAND_NAME]: item?.name
  };
  segmentEvent(NAME.EXPLORE_BRAND_BUTTON_SELECTED, params, false);
};
const viewBrandDetailSelected = ({ data, sessionId }: any) => {
  if (isEmpty(data)) return;
  const params: any = {
    [PROPERTY.CONTENT_ID]: data?.id,
    [PROPERTY.CONTENT_TITLE]: data?.title,
    [PROPERTY.SESSION_ID]: sessionId
  };
  segmentEvent(NAME.VIEW_BRAND_DETAIL_SELECTED, params, false);
};

const showBrand = ({ data, sessionId, brandName }: any) => {
  if (isEmpty(data)) return;
  const params: any = {
    [PROPERTY.CONTENT_ID]: data?.id,
    [PROPERTY.CONTENT_TITLE]: data?.title,
    [PROPERTY.SESSION_ID]: sessionId,
    [PROPERTY.BRAND_NAME]: brandName
  };
  segmentEvent(NAME.SHOW_BRAND, params, false);
};

export default {
  showIndicator,
  showIndicatorSelected,
  hideIndicatorSelected,
  indicatorSelected,
  showAllProductInScene,
  goProductPageButtonSelected,
  telConsultButtonSelected,
  telConsultConfirmButtonSelected,
  telConsultCancelButtonSelected,
  showAllProductInContent,
  productPageClosed,
  exploreBrandButtonSelected,
  viewBrandDetailSelected,
  showBrand
};
