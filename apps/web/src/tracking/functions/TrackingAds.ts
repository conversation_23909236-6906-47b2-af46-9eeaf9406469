import { NAME, PROPERTY } from '@config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';
import { getMenuInfo } from './TrackingApp';

const createParams = (flowName: any, additionalParams = {}) => {
  const baseParams = {
    [PROPERTY.INVENTORY_ID]: flowName.inventoryId || '',
    [PROPERTY.TYPE]: flowName.type || '',
    [PROPERTY.STATUS]: flowName.status || '',
    [PROPERTY.ERROR_MSG]: flowName.errorMsg || ''
  };

  return {
    ...baseParams,
    ...additionalParams
  };
};

export const requestAdsInstream = (flowName: any) => {
  const params: any = createParams(flowName, {
    [PROPERTY.SLOT_NUMBER]: flowName.slotNumber || 1,
    [PROPERTY.CONTENT_ID]: flowName.contentId,
    [PROPERTY.ADS_TYPE]: 'instream_ads'
  });
  segmentEvent(NAME.CALL_ADS_REQUEST, params, false);
};

export const requestAdsOutstream = (flowName: any) => {
  const { menuName } = getMenuInfo() || {};
  const params: any = createParams(flowName, {
    [PROPERTY.CURRENT_PAGE]: menuName,
    [PROPERTY.ADS_TYPE]: 'outstream_ads'
  });
  segmentEvent(NAME.CALL_ADS_REQUEST, params, false);
};

export const requestAdsOverlay = (flowName: any) => {
  const params: any = createParams(flowName, {
    [PROPERTY.ADS_TYPE]: 'overlay_ads'
  });
  segmentEvent(NAME.CALL_ADS_REQUEST, params, false);
};
