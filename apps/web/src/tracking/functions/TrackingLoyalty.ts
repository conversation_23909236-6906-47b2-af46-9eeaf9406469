import { NAME, PROPERTY } from '@config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';

const trackingLoyaltyUpdatePhoneNumber = () => {
  segmentEvent(NAME.UPDATE_PHONE_NUMBER, {}, true);
};

const trackingLoyaltyUpdateEmail = (userIdParam: any) => {
  segmentEvent(NAME.UPDATE_EMAIL, {}, true, userIdParam);
};

const trackingLoyaltyUpdateGender = () => {
  segmentEvent(NAME.UPDATE_GENDER, {}, true);
};

const trackingShareContentToFacebook = ({ contentId, contentTitle }: any) => {
  const params: any = {
    [PROPERTY.CONTENT_ID]: contentId || '',
    [PROPERTY.CONTENT_TITLE]: contentTitle || ''
  };
  segmentEvent(NAME.SHARE_CONTENT_TO_FACEBOOK, params, true);
};

export default {
  trackingShareContentToFacebook,
  trackingLoyaltyUpdateGender,
  trackingLoyaltyUpdateEmail,
  trackingLoyaltyUpdatePhoneNumber
};
