import { NAME } from '@config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';

export const trackingViewContentDetailAtOnAirTab = () => {
  segmentEvent(NAME.VIEW_CONTENT_DETAIL_AT_ON_AIR_TAB, {}, false);
};

export const trackingViewContentAtOnAirTab = () => {
  segmentEvent(NAME.VIEW_CONTENT_AT_ON_AIR_TAB, {}, false);
};

export const trackingAddContentAtOnAirTab = () => {
  segmentEvent(NAME.ADD_CONTENT_AT_ON_AIR_TAB, {}, false);
};

export const trackingRemindMeAtComingSoonTab = () => {
  segmentEvent(NAME.REMIND_ME_AT_COMING_SOON_TAB, {}, false);
};

export const trackingAddContentAtComingSoonTab = () => {
  segmentEvent(NAME.ADD_CONTENT_AT_COMING_SOON_TAB, {}, false);
};

export const trackingViewContentDetailAtComingSoonTab = () => {
  segmentEvent(NAME.VIEW_CONTENT_DETAIL_AT_COMING_SOON_TAB, {}, false);
};
