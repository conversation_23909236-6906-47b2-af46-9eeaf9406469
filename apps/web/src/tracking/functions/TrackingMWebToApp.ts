import { NAME, PROPERTY } from '@config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';

const smartBannerDownloadLoad = ({ flowName }: any) => {
  segmentEvent(NAME.SMART_BANNER_DOWNLOAD_LOAD, { [PROPERTY.FLOW_NAME]: flowName });
};
const smartBannerDownloadAccept = ({ flowName }: any) => {
  segmentEvent(NAME.SMART_BANNER_DOWNLOAD_ACCEPT, { [PROPERTY.FLOW_NAME]: flowName });
};
const smartBannerDownloadClose = ({ flowName }: any) => {
  segmentEvent(NAME.SMART_BANNER_DOWNLOAD_CLOSE, { [PROPERTY.FLOW_NAME]: flowName });
};
const footerAppStoreDownloadTouch = ({ flowName }: any) => {
  segmentEvent(NAME.FOOTER_APP_STORE_DOWNLOAD_TOUCH, { [PROPERTY.FLOW_NAME]: flowName });
};
const footerGooglePlayDownloadTouch = ({ flowName }: any) => {
  segmentEvent(NAME.FOOTER_GOOGLE_PLAY_DOWNLOAD_TOUCH, { [PROPERTY.FLOW_NAME]: flowName });
};
const fabLoad = ({ flowName, data }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: flowName,
    [PROPERTY.FAB_ID]: data
  };
  segmentEvent(NAME.FAB_LOAD, params);
};
const fabTouch = ({ flowName, data }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: flowName,
    [PROPERTY.FAB_ID]: data
  };
  segmentEvent(NAME.FAB_TOUCH, params);
};
const contentSelected = ({ flowName, data }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: flowName,
    [PROPERTY.CONTENT_ID]: data.id,
    [PROPERTY.CONTENT_NAME]: data.title,
    [PROPERTY.RIBBON_ID]: data.ribbonId,
    [PROPERTY.RIBBON_NAME]: data.ribbonName
  };
  segmentEvent(NAME.CONTENT_SELECT, params);
};
const onlyAppTriggerSelected = ({ flowName, data }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: flowName,
    [PROPERTY.RIBBON_ID]: data.id,
    [PROPERTY.RIBBON_NAME]: data.name
  };
  segmentEvent(NAME.ONLY_APP_TRIGGER_SELECTED, params);
};
const contentOnlyInAppTouch = ({ flowName }: any) => {
  segmentEvent(NAME.CONTENT_ONLY_IN_APP_TOUCH, { [PROPERTY.FLOW_NAME]: flowName }, true);
};
const contentOnlyInAppAddList = ({ flowName }: any) => {
  segmentEvent(NAME.CONTENT_ONLY_IN_APP_ADD_LIST, { [PROPERTY.FLOW_NAME]: flowName });
};
const tvseriesTrialEpisodesInfoboxListLandscapeLoad = ({ flowName }: any) => {
  segmentEvent(NAME.TVSERIES_TRIAL_EPISODES_INFOBOX_LIST_LANDSCAPE_LOAD, {
    [PROPERTY.FLOW_NAME]: flowName
  });
};
const tvseriesTrialEpisodesInfoboxListLandscapeTouch = ({ flowName }: any) => {
  segmentEvent(
    NAME.TVSERIES_TRIAL_EPISODES_INFOBOX_LIST_LANDSCAPE_TOUCH,
    {
      [PROPERTY.FLOW_NAME]: flowName
    },
    true
  );
};

const movieMWebToAppDialogLoad = ({ eventName, flowName }: any) => {
  segmentEvent(eventName, { [PROPERTY.FLOW_NAME]: flowName });
};
const movieMWebToAppDialog = ({ eventName, flowName }: any) => {
  segmentEvent(eventName, { [PROPERTY.FLOW_NAME]: flowName }, true);
};
const movieMWebToAppInfoBox = ({ eventName, flowName }: any) => {
  segmentEvent(eventName, { [PROPERTY.FLOW_NAME]: flowName }, true);
};
const movieMWebToAppBanner = ({ eventName, flowName }: any) => {
  segmentEvent(eventName, { [PROPERTY.FLOW_NAME]: flowName }, true);
};
const inAppNewsLoad = ({ flowName, data }: any) => {
  const params: any = {
    [PROPERTY.TRIGGER_BY_BENEFIT_NEWS]: data,
    [PROPERTY.FLOW_NAME]: flowName
  };
  segmentEvent(NAME.IN_APP_NEWS_LOAD, params);
};
const inAppDownload = ({ flowName }: any) => {
  segmentEvent(NAME.IN_APP_DOWNLOAD, { [PROPERTY.FLOW_NAME]: flowName }, true);
};

export default {
  smartBannerDownloadLoad,
  smartBannerDownloadAccept,
  smartBannerDownloadClose,
  footerAppStoreDownloadTouch,
  footerGooglePlayDownloadTouch,
  fabLoad,
  fabTouch,
  contentSelected,
  onlyAppTriggerSelected,
  contentOnlyInAppTouch,
  contentOnlyInAppAddList,
  tvseriesTrialEpisodesInfoboxListLandscapeLoad,
  tvseriesTrialEpisodesInfoboxListLandscapeTouch,
  movieMWebToAppDialogLoad,
  movieMWebToAppDialog,
  movieMWebToAppInfoBox,
  movieMWebToAppBanner,
  inAppNewsLoad,
  inAppDownload
};
