import { NAME, PROPERTY } from '@config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';

export const trackingEndScreenSuggestionLiveEventShow = () => {
  segmentEvent(NAME.END_SCREEN_SUGGESTION_LIVE_EVENT_SHOW, {}, false);
};

export const trackingEndScreenSuggestionLiveEventSelected = ({ contentId }: any) => {
  const params: any = {
    [PROPERTY.CONTENT_ID]: contentId || ''
  };
  segmentEvent(NAME.END_SCREEN_SUGGESTION_LIVE_EVENT_SELECTED, params, false);
};
export const trackingEndScreenSuggestionVodShow = () => {
  segmentEvent(NAME.END_SCREEN_SUGGESTION_VOD_SHOW, {}, false);
};

export const trackingEndScreenSuggestionVodSelected = ({ contentId }: any) => {
  const params: any = {
    [PROPERTY.CONTENT_ID]: contentId || ''
  };
  segmentEvent(NAME.END_SCREEN_SUGGESTION_VOD_SELECTED, params, false);
};
