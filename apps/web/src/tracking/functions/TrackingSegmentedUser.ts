import { NAME, PROPERTY, VALUE } from '@config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';

const offerSubscriptionDialogButtonSelected = () => {
  segmentEvent(NAME.SEGMENTED_USER.OFFER_SUBSCRIPTION_DIALOG_BUTTON_SELECTED, {
    [PROPERTY.FLOW_NAME]: VALUE.OFFER_FREE_TO_SUB
  });
};
const offerSubscriptionDialogButtonClose = () => {
  segmentEvent(NAME.SEGMENTED_USER.OFFER_SUBSCRIPTION_DIALOG_BUTTON_CLOSE, {
    [PROPERTY.FLOW_NAME]: VALUE.OFFER_FREE_TO_SUB
  });
};
const offerSubscriptionDialogLoaded = () => {
  segmentEvent(NAME.SEGMENTED_USER.OFFER_SUBSCRIPTION_DIALOG_LOADED, {
    [PROPERTY.FLOW_NAME]: VALUE.OFFER_FREE_TO_SUB
  });
};
const ratingButtonSelected = ({
  contentId,
  contentName,
  contentType,
  isBlockVip,
  userType
}: any) => {
  segmentEvent(NAME.PAYMENT.RATING_BUTTON_SELECTED, {
    [PROPERTY.CONTENT_ID]: contentId || '',
    [PROPERTY.CONTENT_NAME]: contentName || '',
    [PROPERTY.CONTENT_TYPE]: contentType || '',
    [PROPERTY.IS_BLOCK_VIP]: isBlockVip || false,
    [PROPERTY.USER_TYPE]: userType || 'guest' // userType should be 'free', 'guest', or 'vip'
  });
};

const trackSubmitRating = ({
  contentId,
  contentName,
  contentType,
  isBlockVip,
  userType,
  ratingScore
}: any) => {
  segmentEvent(NAME.PAYMENT.SEND_RATING_BUTTON_SELECTED, {
    [PROPERTY.CONTENT_ID]: contentId || '',
    [PROPERTY.CONTENT_NAME]: contentName || '',
    [PROPERTY.CONTENT_TYPE]: contentType || '',
    [PROPERTY.IS_BLOCK_VIP]: isBlockVip || false,
    [PROPERTY.USER_TYPE]: userType || 'guest', // userType should be 'free', 'guest', or 'vip'
    [PROPERTY.RATING_SCORE]: ratingScore
  });
};

const trackAddToListButton = ({
  contentId,
  contentName,
  contentType,
  isBlockVip,
  userType,
  buttonStatus
}: any) => {
  segmentEvent(NAME.PAYMENT.ADD_TO_LIST_BUTTON_SELECTED, {
    [PROPERTY.CONTENT_ID]: contentId || '',
    [PROPERTY.CONTENT_NAME]: contentName || '',
    [PROPERTY.CONTENT_TYPE]: contentType || '',
    [PROPERTY.IS_BLOCK_VIP]: isBlockVip || false,
    [PROPERTY.USER_TYPE]: userType || 'guest', // userType should be 'free', 'guest', or 'vip'
    [PROPERTY.BUTTON_NAME]: 'add_to_list',
    [PROPERTY.BUTTON_STATUS]: buttonStatus || ''
  });
};

export {
  offerSubscriptionDialogButtonSelected,
  offerSubscriptionDialogButtonClose,
  offerSubscriptionDialogLoaded,
  ratingButtonSelected,
  trackSubmitRating,
  trackAddToListButton
};
