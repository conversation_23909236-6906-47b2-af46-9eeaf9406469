import ConfigApi from '@config/ConfigApi';
import { API_METHOD } from '@constants/constants';
import CardItem from '@models/CardItem';
import { parseConfigParams } from '@helpers/common';
import AxiosClient from '../axiosClient';

export default class TagApi {
  static getListTagsFilter() {
    const url = ConfigApi.cm.listTagsFilter;
    const method = API_METHOD.GET;
    const params: any = { is_filter: 1 };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => ({
      data: { ...res?.data }
    }));
  }

  static getContentTags({
    tags,
    page,
    limit,
    sort,
    accessToken,
    profileToken,
    ssr,
    userAgent,
    ipAddress,
    isGlobal,
    origin
  }: any) {
    const url = `${ConfigApi.cm.contentTag}`;
    page = page || 0;
    limit = limit || 30;
    sort = sort || 2;

    const formatTag = `"/${tags instanceof Array ? tags.join('","/') : tags}"`;
    tags = `[${formatTag}]`;
    const method = API_METHOD.POST;
    const params: any = {
      tags,
      page,
      limit,
      sort
    };

    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      accessToken,
      profileToken,
      ssr,
      config
    })?.then((res: any) => {
      const items =
        (res?.data?.items || []).map((it: any) => new CardItem({ ...it, isGlobal })) || [];
      return { data: { ...res?.data, items } };
    });
  }
}
