import moment from 'moment';
import { API_METHOD, PERMISSION, PLATFORM } from '@constants/constants';
import ConfigApi from '@config/ConfigApi';
import ChannelItemObject from '@models/channelItem';
import EpgItem from '@models/epgItem';
import CardItem from '@models/CardItem';
import { setDateFilterListEpgs } from '@services/liveTVServices';
import { parseConfigParams, parseUrlString } from '@helpers/common';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import LocalStorage from '@config/LocalStorage';
import { setSessionPlay } from '@actions/detail';
import { LinkPlaysToRetry } from '@models/subModels';
import isEmpty from 'lodash/isEmpty';
import AxiosClient from './axiosClient';

class LiveTVApi {
  static validateKPlus({ accessToken, deviceId, isMobile, liveTVId, userId, browserName }: any) {
    const method = API_METHOD.GET;
    const url = parseUrlString(`${ConfigApi.cm.liveTV.validateKPlus}`, 'userId', userId);
    const params: any = {
      livetv_id: liveTVId,
      platform: isMobile ? PLATFORM.MOBILE_WEB : PLATFORM.WEB,
      device_id: deviceId,
      device_brand: browserName
    };
    return AxiosClient.executeWithCache({ accessToken, url, method, params })?.then((res: any) => {
      const validateOb: any = {
        httpCode: res?.httpCode,
        validation: res?.data?.result?.validation,
        mustRevalidateBefore: res?.data?.result?.must_revalidate_before || 0
      };
      const validateDuration = validateOb?.mustRevalidateBefore
        ? validateOb?.mustRevalidateBefore * 1000 - new Date().getTime()
        : 0;
      validateOb.validateDuration = validateDuration <= 0 ? 0 : Math.floor(validateDuration);
      if (validateOb?.validation === 1 && validateOb?.validateDuration > 0) {
        ConfigLocalStorage.set(LocalStorage.K_PLUS_VALIDATE, JSON.stringify(validateOb));
      }
      return validateOb;
    });
  }

  static getBroadcastingList({ accessToken, ssr }: any) {
    const method = API_METHOD.GET;
    const url = `${ConfigApi.cm.liveTV.broadcastingList}`;
    return AxiosClient.executeWithCache({ accessToken, url, method, ssr })?.then((res: any) => {
      let broadcastList = [];
      if (res?.data?.items) {
        broadcastList = res.data.items.map((item: any, i: any) => ({
          id: item.id,
          type: item.type,
          index: i,
          name: item.name,
          seo: item.seo
        }));
      }
      return broadcastList;
    });
  }

  static getChannelListAll({ page, limit }: any) {
    const method = API_METHOD.GET;
    const url = `${ConfigApi.cm.liveTV.channelListAll}?page=${page || 0}&limit=${limit || 0}`;
    return AxiosClient.executeWithCache({ url, method })?.then((res: any) => {
      const result: any = [];
      (res?.data?.items || []).map((item: any) => {
        result.push({
          id: item.id,
          title: item.title,
          seo: item.seo,
          active: false
        });
      });
      return { items: result, metadata: res?.data?.metadata };
    });
  }

  static getChannelPage({ ipAddress, userAgent, origin }: any) {
    const method = API_METHOD.GET;
    const url = `${ConfigApi.cm.liveTV.channelPage}`;
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({ url, method, config })?.then((res: any) => res?.data);
  }

  static getFavoriteList({
    accessToken,
    profileToken,
    ipAddress,
    ssr,
    userAgent,
    isGlobal,
    origin
  }: any) {
    const method = API_METHOD.GET;
    const url = ConfigApi.cm.liveTV.favoriteList;
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({
      url,
      method,
      config,
      accessToken,
      profileToken,
      ssr
    })?.then((res: any) => {
      const data = (res?.data?.items || []).map(
        (item: any, index: any) => new CardItem({ ...item, isTV: true, isGlobal }, index)
      );
      return data;
    });
  }

  static getWatchedList({
    accessToken,
    profileToken,
    ssr,
    ipAddress,
    userAgent,
    isGlobal,
    origin
  }: any) {
    const method = API_METHOD.GET;
    const url = `${ConfigApi.cm.liveTV.watchedList}`;
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({
      url,
      method,
      accessToken,
      profileToken,
      ssr,
      config
    })?.then((res: any) => {
      const items = (res?.data?.items || []).map(
        (item: any, i: any) => new CardItem({ ...item, isTV: true, isGlobal }, i)
      );
      return items;
    });
  }

  static getChannelListByCategory({ id, category, asPath, activeChannelId }: any) {
    const method = API_METHOD.GET;
    const url = parseUrlString(ConfigApi.cm.liveTV.channelListByCategory, 'groupId', id);
    const params: any = { category };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      let data = null;
      let activeChannel = null;
      const items = res?.data?.items || [];
      if (items && items.length > 0) {
        data = items.map((item: any, index: any) => {
          const channelItem = new ChannelItemObject({ data: item, asPath, index, activeChannelId });
          if (channelItem.active) activeChannel = channelItem;
          return channelItem;
        });
      }
      return { data, activeChannel, isFetched: true };
    });
  }

  static getChannelDetailBySlug({
    slug,
    accessToken,
    profileToken,
    epg,
    ipAddress,
    ssr,
    iOS,
    userAgent,
    origin,
    dispatch
  }: any) {
    let newSlug = (slug || '').split('?')?.[0];
    const isLastTrailingSlash = (newSlug || '').lastIndexOf('/') === (newSlug || '').length - 1;
    if (epg && isLastTrailingSlash) {
      newSlug = slug.substring(0, newSlug.length - 1);
    }
    const method = API_METHOD.POST;
    const url = ConfigApi.cm.liveTV.channelDetailBySlug;
    const params: any = { livetv_slug: newSlug };
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      accessToken,
      profileToken,
      config,
      ssr
    }).then(async (res) => {
      if (res.httpCode !== 200) {
        return { ...res.data, success: res.success, httpCode: res.httpCode };
      }
      const { id, title, content_concurrent_group, permission } = res.data || {};
      const linkPlaysToRetry = LinkPlaysToRetry(res.data, iOS);
      let concurrentScreen = '';
      if (
        permission === PERMISSION.CAN_WATCH &&
        content_concurrent_group &&
        !isEmpty(linkPlaysToRetry)
      ) {
        concurrentScreen = await dispatch(
          setSessionPlay({
            titleId: id,
            titleName: title,
            contentConcurrentGroup: content_concurrent_group,
            accessToken
          })
        );
      }
      const detailData: any = new ChannelItemObject({ data: res.data, iOS, concurrentScreen });
      detailData.error = res?.data?.error;
      detailData.success = res?.success;
      detailData.httpCode = res?.httpCode;
      if (detailData?.permission === PERMISSION.CAN_WATCH && detailData?.qnetDrm) {
        return LiveTVApi.getQNetInfo({
          accessToken,
          profileToken,
          ssr,
          contentId: detailData?.id,
          type: 'livetv'
        }).then((qnetInfo) => {
          detailData.qnetInfo = qnetInfo;
          return detailData;
        });
      }
      ConfigLocalStorage.set('livetvDetail', JSON.stringify(detailData));
      return detailData;
    });
  }
  static getRibbonLiveTvNotFound({ accessToken, ssr, ipAddress, userAgent, origin }: any) {
    const method = API_METHOD.GET;
    const url = ConfigApi.cm.liveTV.ribbonLiveTVNotFound;
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({ url, method, accessToken, ssr, config }).then(
      (res) => res
    );
  }

  static getChannelDetail({ id, isHover, iOS, dispatch }: any) {
    const method = API_METHOD.GET;
    const url = parseUrlString(ConfigApi.cm.liveTV.channelDetail, 'channelId', id);
    return AxiosClient.executeWithCache({ url, method }).then(async (res) => {
      const { id, title, content_concurrent_group, permission } = res?.data || {};
      const linkPlaysToRetry = LinkPlaysToRetry(res.data, iOS);
      let concurrentScreen = '';
      if (
        permission === PERMISSION.CAN_WATCH &&
        content_concurrent_group &&
        !isEmpty(linkPlaysToRetry)
      ) {
        concurrentScreen = await dispatch(
          setSessionPlay({
            titleId: id,
            titleName: title,
            contentConcurrentGroup: content_concurrent_group
          })
        );
      }
      const detailData: any = new ChannelItemObject({ data: res.data, iOS, concurrentScreen });
      detailData.error = res?.data?.error;
      detailData.success = res?.success;
      detailData.httpCode = res?.httpCode;
      detailData.message = res?.message;
      if (!isHover && detailData?.permission === PERMISSION.CAN_WATCH && detailData?.qnetDrm) {
        return LiveTVApi.getQNetInfo({ contentId: detailData?.id, type: 'livetv' }).then(
          (qnetInfo) => {
            detailData.qnetInfo = qnetInfo;
            return detailData;
          }
        );
      }
      return detailData;
    });
  }

  static getEpgList({ id, strDate }: any) {
    const method = API_METHOD.GET;
    const url = ConfigApi.cm.liveTV.epgList;
    const isRequestListToDay = strDate === moment().format('YYYY-MM-DD');
    const listFilterDate = isRequestListToDay ? setDateFilterListEpgs() : [];
    const params: any = { livetv_id: id, str_date: strDate };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => {
      const list = res?.data?.items || [];
      let liveEpg = null;
      let nextEpg = null;
      let indexLiveEpg = -1;
      const comingSoonListIdEpgs: any = [];
      const listEpgs = list.map((item: any, index: any) => {
        const epg = new EpgItem(item);
        if (epg.isLive) {
          liveEpg = epg;
          indexLiveEpg = index;
        }
        if (epg.isComingSoon) {
          comingSoonListIdEpgs.push(epg?.id);
        }
        if (indexLiveEpg !== -1 && indexLiveEpg + 1 === index) {
          nextEpg = epg;
        }
        return epg;
      });
      return {
        idChannel: id,
        listFilterDate,
        strDate,
        listEpgs,
        liveEpg,
        nextEpg,
        comingSoonListIdEpgs
      };
    });
  }
  static addFavorite({ id }: any) {
    const method = API_METHOD.POST;
    const url = `${ConfigApi.cm.liveTV.addFavorite}`;
    const params: any = { livetv_ids: `["${id}"]` };
    return AxiosClient.executeWithCache({ url, method, params })?.then((res: any) => res);
  }

  static getQNetInfo({ contentId, type, accessToken, ssr, profileToken }: any) {
    const method = API_METHOD.GET;
    const url = `${ConfigApi.cm.liveTV.getQNetInfo}?id=${contentId}&type=${type}`;
    return AxiosClient.executeWithCache({ url, method, accessToken, ssr, profileToken }).then(
      (res) => {
        if (!res.success) return null;
        return {
          userId: res?.data?.UserId,
          sessionId: res?.data?.SessionId,
          operatorId: res?.data?.OperatorId
        };
      }
    );
  }
  static addWatching({ channelId }: any) {
    const method = ConfigApi.METHOD.POST;
    const url = ConfigApi.cm.liveTV.addWatched;
    const params: any = { livetv_ids: JSON.stringify([channelId]) };
    return AxiosClient.executeWithCache({ url, method, params });
  }
}

export default LiveTVApi;
