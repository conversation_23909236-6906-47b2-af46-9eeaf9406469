import { ACTION_TYPE } from '@actions/actionType';
import initialState from './initialState';
const searchReducer = (state: any = initialState.search, action: any) => {
  const result: any = { ...state };
  const dataPayload = action.payload && action.payload.data ? action.payload.data : [];
  switch (action.type) {
    case ACTION_TYPE.ACTION_GET_SEARCH_CONTENT: {
      const key1 = action.keyword;
      const key2 = action.tags ? action.tags : -1; // -1 l<PERSON> không filter theo tag nào
      const key3 = action.page;

      if (!result[action.type]) {
        result[action.type] = {};
      }
      // nếu chưa tồn tại key1 thì khởi tạo giá trị {}

      if (!result[action.type][key1]) {
        result[action.type][key1] = {};
      }
      // nếu chưa tồn tại key2 thì khởi tạo giá trị {}

      if (!result[action.type][key1][key2]) {
        result[action.type][key1][key2] = {};
      }

      result[action.type][key1][key2][key3] = dataPayload;

      break;
    }
    case ACTION_TYPE.ACTION_GET_TREND_KEYWORD:
      if (state[action.type] && action?.payload?.data?.items) {
        const dataItemsAdded = [...state[action.type].items, ...action.payload.data.items];
        const uniqueDataItems = dataItemsAdded.reduce((unique, o) => {
          if (!unique.some((obj: any) => obj.id === o.id)) {
            unique.push(o);
          }
          return unique;
        }, []);
        action.payload.data.items = uniqueDataItems;
      }

      result[action.type] = action.payload.data;

      break;
    case ACTION_TYPE.ACTION_FOCUS_SEARCHBOX:
    case ACTION_TYPE.ACTION_GET_SEARCH_FORYOU:
    case ACTION_TYPE.ACTION_GET_SEARCH_HISTORY:
      result[action.type] = action.payload.data;
      break;
    case ACTION_TYPE.ACTION_GET_SEARCH_SUGGEST:
      if (!result[action.type]) {
        result[action.type] = {};
      }

      result[action.type][action.keyword] = action.payload.data;
      break;
    case ACTION_TYPE.SET_SEARCH_SHOW:
      result.searchShow = action.payload.data;
      break;
    default:
      break;
  }

  return result;
};

export default searchReducer;
