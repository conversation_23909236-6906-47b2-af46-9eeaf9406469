import { ACTION_GET_CONTENT_TAGS, ACTION_GET_LIST_TAGS_FILTER } from '@actions/tags';
import { ACTION_TYPE } from '@actions/actionType';

const tagsReducer = (state = {}, action: any) => {
  const result: any = { ...state };
  const dataPayload = action.payload && action.payload.data ? action.payload.data : [];

  switch (action.type) {
    case ACTION_GET_CONTENT_TAGS: {
      const oldItems = result?.[action.type]?.items || [];
      const newItems = dataPayload?.items || [];

      result[action.type] = { ...dataPayload, items: [...oldItems, ...newItems] };
      break;
    }
    case ACTION_GET_LIST_TAGS_FILTER: {
      result[action.type] = dataPayload;
      break;
    }
    case ACTION_TYPE.CLEAR_TAGS_DATA: {
      result[ACTION_GET_CONTENT_TAGS] = null;
      break;
    }
    default:
      break;
  }
  return result;
};

export default tagsReducer;
