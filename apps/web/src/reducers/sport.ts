import { ACTION_TYPE } from '@actions/actionType';
import { RANKING_TAB } from '@constants/constants';
import initialState from './initialState';

const sportReducer = (state: any = initialState.sport, { type, data }: any) => {
  switch (type) {
    case ACTION_TYPE.SET_RANKING_BOARD: {
      return { ...state, isRankingBoard: !!data };
    }
    case ACTION_TYPE.SET_COMPETITIONS: {
      return { ...state, competitions: data || [], activeCompetition: data?.[0] };
    }
    case ACTION_TYPE.SET_RANKING_TAB: {
      return { ...state, rankingTab: data || RANKING_TAB.SCHEDULE };
    }
    case ACTION_TYPE.SET_ACTIVE_COMPETITION: {
      return { ...state, activeCompetition: data };
    }
    case ACTION_TYPE.SET_MATCH: {
      const newState = { ...state };
      const newComps = (newState.competitions || []).map((item: any) => {
        if (item?.code === data?.code) {
          const activeItem = { ...item, matches: data?.matches };
          return activeItem;
        }
        return item;
      });
      return { ...state, competitions: newComps };
    }
    case ACTION_TYPE.SET_RANKING_LIST: {
      const newState = { ...state };
      const newComps = (newState.competitions || []).map((item: any) => {
        if (item?.code === data?.code) {
          const activeItem = { ...item, rankingList: data?.rankingList };
          return activeItem;
        }
        return item;
      });
      return { ...state, competitions: newComps };
    }
    default:
      return state;
  }
};

export default sportReducer;
