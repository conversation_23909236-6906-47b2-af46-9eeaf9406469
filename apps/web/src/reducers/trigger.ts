import { produce } from 'immer';
import { ACTION_TYPE } from '@actions/actionType';
import { TYPE_TRIGGER_ALWAYS } from '@constants/constants';

const INITIAL_STATE = {
  dataTrigger: {}
};

const triggerReducer = produce((draft: any, action: any) => {
  const data = action?.data;
  switch (action.type) {
    case ACTION_TYPE.GET_DATA_TRIGGER: {
      switch (data.type) {
        case TYPE_TRIGGER_ALWAYS.LIVE_TV:
          draft.dataTriggerLiveTv = data?.res;
          break;
        case TYPE_TRIGGER_ALWAYS.NOTIFICATION:
          draft.dataTriggerNoti = data?.res;
          break;
        case TYPE_TRIGGER_ALWAYS.SEARCH:
          draft.dataTriggerSearch = data?.res;
          break;
        case TYPE_TRIGGER_ALWAYS.COMING_SOON:
          draft.dataTriggerComingSoon = data?.res;
          break;
        case TYPE_TRIGGER_ALWAYS.VOD_INTRO:
          draft.dataTriggerVodIntro = data?.res;
          break;
        case TYPE_TRIGGER_ALWAYS.PROFILE:
          draft.dataTriggerProfile = data?.res;
          break;
        default:
          break;
      }
      break;
    }
    default:
      break;
  }
}, INITIAL_STATE);

export default triggerReducer;
