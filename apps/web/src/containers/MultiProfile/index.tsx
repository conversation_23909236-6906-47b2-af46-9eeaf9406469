import React, { useEffect } from 'react';
import LobbyView from '@components/LobbyProfile/LobbyView';
import { LOBBY_PROFILE_STEP, PAGE } from '@constants/constants';
import LobbyAddUserProfile from '@components/LobbyProfile/LobbyUserProfile/LobbyAddUserProfile';
import LobbyEditUserProfile from '@components/LobbyProfile/LobbyUserProfile/LobbyEditUserProfile';
import LobbyAvatarProfileOption from '@components/LobbyProfile/AvatarProfileOption';
import { setLobbyStep } from '@actions/multiProfile';
import { useSelector, useDispatch } from 'react-redux';
import Logo from '@components/basic/Logo/Logo';
import { useVieRouter } from '@customHook';
import LocalStorage from '@config/LocalStorage';
import ConfigLocalStorage from '@config/ConfigLocalStorage';

const MultiProfile = () => {
  const router = useVieRouter();
  const { query } = router || {};
  const step = useSelector((state: any) => state?.MultiProfile?.step || '');
  const profile = useSelector((state: any) => state?.Profile?.profile || '');

  // Redux control
  const dispatch = useDispatch();
  useEffect(() => {
    if (!profile?.id) router.push(PAGE.HOME);
    if (query?.from === 'login') {
      ConfigLocalStorage.set(LocalStorage.CHECK_PROFILE_ID, true);
    }
    return () => {
      dispatch(setLobbyStep(''));
      return;
    };
  }, []);

  const renderByStep = () => {
    switch (step) {
      case LOBBY_PROFILE_STEP.ADD:
        return <LobbyAddUserProfile />;
      case LOBBY_PROFILE_STEP.EDIT:
        return <LobbyEditUserProfile />;
      case LOBBY_PROFILE_STEP.AVATAR_SELECT:
        return <LobbyAvatarProfileOption />;
      default:
        return <LobbyView />;
    }
  };

  return (
    <>
      <div className="canal-v relative layer-2 height-small-up-48rem height-large-up-56rem">
        <Logo
          className="logo logo-st-1 logo-st-1-md logo-st-1-lg flex-box"
          onClickLogo={() => {}}
        />
      </div>
      {renderByStep()}
    </>
  );
};

export default React.memo(MultiProfile);
