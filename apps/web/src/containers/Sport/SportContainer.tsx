import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import dynamic from 'next/dynamic';
import classNames from 'classnames';
import { setLoadedData } from '@actions/app';
import {
  getDataRibbonsId,
  getPageBanners,
  getPageRibbons,
  getSEOAllPage,
  setPageBannerSuccess
} from '@actions/page';
import { getRibbonDetailNotFound } from '@actions/detail';
import { decodeSignature, getCookie, getPathFromUrl } from '@helpers/common';
import { DOMAIN_WEB } from '@config/ConfigEnv';
import MasterBanner from '@components/MasterBanner';
import { pageView } from '@tracking/functions/TrackingApp';
import SeoText from '@components/seo/SeoText';
import { ItemList } from '@models/subModels';
import { TEXT } from '@constants/text';
import SeoAllPage, { redirectTool } from '@components/seo/SeoAllPage';
import { PAGE, SEO_PAGES } from '@constants/constants';
import Button from '@components/basic/Buttons/Button';
import RankingBoard from '@components/sport/RankingBoard/RankingBoard';
import ConfigCookie from '@config/ConfigCookie';
import ListRibbons from '@components/Sections/ListRibbons';
import styles from './Sport.module.scss';

const ContentNotFound = dynamic(import('@components/notfound/ContentNotFound'), { ssr: false });
const keyBreadcrumbs = SEO_PAGES.CATEGORY;

class SportContainer extends Component {
  currenPath: any;

  static async getInitialProps({ store, req, res }: any) {
    // call in server
    if (req) {
      // get menu from store redux
      const { Menu, App }: any = store.getState() || {};
      const isGlobal = App?.geoCheck?.isGlobal;
      const accessToken = App?.accessToken || App?.token;
      const cookie = req?.cookies;
      const keySignature = ConfigCookie.KEY.SIGNATURE;
      const origin = req?.headers?.host || '';
      const { profileToken } =
        decodeSignature({
          value: cookie?.[keySignature] || getCookie(keySignature, req?.headers.cookie) || ''
        }) || {};
      const { activeMenu } = Menu;
      const { menuList } = Menu;
      const { activeSubMenu } = Menu;
      const dataMenu = activeSubMenu || activeMenu;
      const userAgent = req.headers['user-agent'];
      const ipAddress = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '')
        .split(':')
        .pop();
      let firstRibbonPath = '';
      let renderNotFoundPage = false;

      if (dataMenu) {
        const pageSlug = dataMenu?.dataSlug || dataMenu?.seo?.url;
        if (pageSlug && pageSlug[pageSlug.length - 1] !== '/') {
          res.status(410);
          renderNotFoundPage = true;
          const ribbonDetail = await getRibbonDetailNotFound({
            accessToken,
            profileToken,
            ssr: true,
            userAgent,
            ipAddress,
            isGlobal,
            origin
          });
          await store.dispatch(ribbonDetail);
        }
        const getBannerTask = getPageBanners({
          pageSlug,
          accessToken,
          profileToken,
          ssr: true,
          ipAddress,
          userAgent,
          isGlobal,
          origin
        });
        const getPageRibbonTask = getPageRibbons({
          pageSlug,
          accessToken,
          ssr: true,
          ipAddress,
          isSettingRankingBoard: true,
          userAgent,
          isGlobal,
          origin
        });
        const [dataBanner, dataPageRibbon] = await Promise.all([getBannerTask, getPageRibbonTask]);
        // dispatch data to store
        await Promise.all([
          store.dispatch(dataBanner),
          store
            .dispatch(
              getSEOAllPage({
                slug: pageSlug,
                keyBreadcrumbs,
                ssr: true,
                ipAddress,
                userAgent,
                origin
              })
            )
            .then((resp: any) => redirectTool({ redirect: resp?.data?.redirect, res })),
          store
            .dispatch(dataPageRibbon)
            .then(async (response: any) => {
              const ribbon0 = response?.data?.data?.[0];
              const ribbonId0 = ribbon0?.id;
              firstRibbonPath = ribbon0?.seo?.url;
              const dataRibbons = response?.data?.data;
              if (dataRibbons.length === 0) {
                res.status(410);
                renderNotFoundPage = true;
                const ribbonDetail = await getRibbonDetailNotFound({
                  accessToken,
                  profileToken,
                  ssr: true,
                  ipAddress,
                  userAgent,
                  isGlobal,
                  origin
                });
                await store.dispatch(ribbonDetail);
              } else if (ribbonId0) {
                const ribbonData0 = getDataRibbonsId({
                  id: ribbonId0,
                  accessToken,
                  profileToken,
                  ssr: true,
                  ipAddress,
                  userAgent,
                  isGlobal,
                  origin
                });
                await store.dispatch(ribbonData0);
              }
            })
            .catch(async () => {
              res.status(410);
              renderNotFoundPage = true;
              const ribbonDetail = await getRibbonDetailNotFound({
                accessToken,
                profileToken,
                ssr: true,
                ipAddress,
                userAgent,
                isGlobal,
                origin
              });
              await store.dispatch(ribbonDetail);
            })
        ]);
      } else if (menuList?.length && menuList?.length > 0) {
        res.status(410);
        const ribbonDetail = await getRibbonDetailNotFound({
          ssr: true,
          accessToken,
          profileToken,
          ipAddress,
          userAgent,
          isGlobal,
          origin
        });
        renderNotFoundPage = true;
        await store.dispatch(ribbonDetail);
      }
      return { ssr: true, firstRibbonPath, renderNotFoundPage };
    }
    return {};
  }

  state = {
    path: (this.props as any)?.router?.asPath,
    classNameRanking: ''
  };

  componentDidMount() {
    const { router, dataSEOAllPage, pageProps, getSEOAllPage, setLoadedData }: any = this.props;
    this.currenPath = router?.asPath;
    const seoData = this.getSeoData();
    pageView(seoData);
    if (pageProps?.ssr) {
      this.getPageData();
    }
    if (dataSEOAllPage?.seo?.slug !== this.currenPath) {
      getSEOAllPage({ slug: this.currenPath, keyBreadcrumbs });
    }
    setLoadedData(true);
  }

  componentDidUpdate(prevProps: any, prevState: any) {
    const { router, dataSEOAllPage, getSEOAllPage }: any = this.props;
    const path = router?.asPath;
    const { activeMenu, activeSubMenu }: any = this.props;
    const dataMenu = activeSubMenu || activeMenu;
    const prevDataMenu = prevProps?.activeSubMenu || prevProps?.activeMenu;
    if (prevState?.path !== path) {
      this.setState({ path });
      const seoData = this.getSeoData();
      pageView(seoData);
      this.currenPath = path;
      if (dataSEOAllPage?.seo?.slug !== this.currenPath) {
        getSEOAllPage({ slug: this.currenPath, keyBreadcrumbs });
      }
    }
    if (dataMenu?.id !== prevDataMenu?.id) {
      this.getPageData();
    }
  }

  componentWillUnmount() {
    const { clearDataPageBanner }: any = this.props;
    if (typeof clearDataPageBanner === 'function') {
      clearDataPageBanner();
    }
  }

  getSeoData = () => {
    const { menuList, pageProps, router, activeMenu, ribbonData }: any = this.props;
    const prefixPath = getPathFromUrl(router?.asPath);
    const slugPrefixPath = prefixPath.split('/').join('');
    const menuMainItem = (menuList || []).find((menu: any) => menu?.seo?.url === prefixPath);
    const subMenuItem = (menuList || []).map((el: any) =>
      (el.subMenu || []).find((subEl: any) => subEl?.seo?.url === prefixPath)
    );
    const firstRibbonPath = pageProps?.firstRibbonPath || null;
    let data = menuMainItem || Object.assign({}, ...subMenuItem);
    const url = prefixPath === PAGE.HOME ? DOMAIN_WEB : DOMAIN_WEB + prefixPath;
    if (prefixPath === PAGE.HOME || prefixPath === PAGE.VIP) {
      data = (menuList || []).find((menu: any) => menu?.seo?.url === activeMenu?.seo?.url);
    }
    let listArrRibbon = [];
    if (firstRibbonPath) {
      const dataRibbon = ribbonData?.[firstRibbonPath] || null;
      listArrRibbon = (dataRibbon?.items || []).map((item: any) => ItemList(item));
    }
    const uniqueItemList: any = [];
    listArrRibbon.forEach((item: any) => {
      if (uniqueItemList.findIndex((temp: any) => temp?.url === item?.url) === -1) {
        uniqueItemList.push(item);
      }
    });
    const dataSeo = {
      page: prefixPath,
      url,
      data,
      prefixPath,
      listArrRibbon: uniqueItemList,
      slug: slugPrefixPath
    };
    return dataSeo;
  };

  getPageData = () => {
    const {
      activeMenu,
      activeSubMenu,
      pageBanner,
      pageRibbon,
      geoCheck,
      getPageBanners,
      getPageRibbons
    }: any = this.props;
    const dataMenu = activeSubMenu || activeMenu;
    if (!dataMenu) return;
    const pageSlug = dataMenu?.dataSlug || dataMenu?.seo?.url;
    const dataPageBanner = pageBanner?.[pageSlug];
    const dataPageRibbon = pageRibbon?.[pageSlug];

    if (!dataPageBanner) {
      getPageBanners({ pageSlug, isGlobal: geoCheck?.isGlobal });
    }
    if (!dataPageRibbon) {
      getPageRibbons({
        pageSlug,
        isSettingRankingBoard: true,
        isGlobal: geoCheck?.isGlobal
      });
    }
  };

  handleClickLandingSport = () => {
    this.setState((prevState: any) => ({
      isRankingMobile: !prevState.isRankingMobile
    }));
  };

  render() {
    const {
      pageProps,
      pageBanner,
      dataSEOAllPage,
      isRankingBoard,
      activeMenu,
      activeSubMenu,
      ribbonNotFound,
      isMobile
    }: any = this.props;
    const { isRankingMobile }: any = this.state;
    const seoData = this.getSeoData();
    const dataMenu = activeSubMenu || activeMenu;
    const pageSlug = dataMenu?.dataSlug || dataMenu?.seo?.url;
    let dataPageBanner = null;
    const dataBannerProps = pageBanner || null;
    if (dataBannerProps?.[pageSlug]) {
      dataPageBanner = dataBannerProps?.[pageSlug];
    } else {
      // Recall data if master banner null
      this.getPageData();
      dataPageBanner = dataBannerProps?.[pageSlug];
    }

    if (pageProps?.renderNotFoundPage) {
      return <ContentNotFound dataRibbon={{ 0: ribbonNotFound || [] }} />;
    }

    return (
      <>
        <SeoAllPage {...dataSEOAllPage} listArrRibbon={seoData?.listArrRibbon} />
        <SeoText seo={dataSEOAllPage?.seo} />
        <MasterBanner data={dataPageBanner} />
        <div
          className={classNames({
            [(styles.Container as string) || 'Container']: !isRankingBoard,
            [(styles.ContainerAside as string) || 'ContainerAside']: isRankingBoard
          })}
        >
          {isRankingBoard && (
            <div className={classNames('md:shrink')} id="rankingBoard">
              <RankingBoard
                isRankingMobile={isRankingMobile}
                setRankingMobile={this.handleClickLandingSport}
              />
            </div>
          )}
          {isRankingBoard && isMobile && (
            <Button
              className="open m-b2 w-fit"
              theme="primaryOutlineGlass"
              size="large"
              onClick={this.handleClickLandingSport}
              title={TEXT.RANKING_BOARD}
            />
          )}
          <ListRibbons
            id={pageSlug}
            isRankingBoard={isRankingBoard}
            customizeClassName={classNames(
              'flex-1 max-w-full space-y-5 md:space-y-6 xl:space-y-8 3xl:space-y-14',
              {
                'md:w-[calc(100vw-32.27083vw)] 2xl:w-[72.72917vw] overflow-hidden canal-r':
                  !isMobile && isRankingBoard
              }
            )}
          />
        </div>
      </>
    );
  }
}
const mapStateToProps = ({ Menu, Page, Detail, Profile, User, Billing, App, Sport }: any) => ({
  ...Menu,
  ...Page,
  ...Detail,
  ...Profile,
  ...User,
  ...Billing,
  ...App,
  ...Sport,
  isMobile: App?.isMobile
});

const mapDispatchToProps = (dispatch: any) => {
  const actions = bindActionCreators(
    {
      clearDataPageBanner: setPageBannerSuccess,
      setLoadedData: setLoadedData,
      getPageBanners: getPageBanners,
      getPageRibbons: getPageRibbons,
      getSEOAllPage: getSEOAllPage
    },
    dispatch
  );
  return { ...actions, dispatch };
};

export default connect(mapStateToProps, mapDispatchToProps)(SportContainer);
