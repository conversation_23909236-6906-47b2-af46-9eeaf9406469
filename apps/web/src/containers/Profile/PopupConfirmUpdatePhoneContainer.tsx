import React, { Component } from 'react';
import PopupConfirmUpdatePhone from '@components/profile/PopupConfirmUpdatePhone';

class PopupConfirmUpdatePhoneContainer extends Component<any> {
  constructor(props: any) {
    super(props);
    this.state = {
      isTogglePopupConfirmUpdatePhone: false,
      registerSessionId: ''
    };
  }

  componentDidUpdate = () => {
    //TODO: check if need to update state ACTION_POPUP_CONFIRM_UPDATE_PHONE
    const { ['ACTION_POPUP_CONFIRM_UPDATE_PHONE']: isActive }: any = this.props;
    const { isTogglePopupConfirmUpdatePhone }: any = this.state;

    if (isActive && !isTogglePopupConfirmUpdatePhone) {
      this.setState({ isTogglePopupConfirmUpdatePhone: isActive });
    }
    if (!isActive && isTogglePopupConfirmUpdatePhone) {
      this.setState({ isTogglePopupConfirmUpdatePhone: isActive });
    }
  };

  getDataRender = () => {
    //TODO: check if need to update state ACTION_GET_PROFILE and ACTION_CONFIRM_UPDATE_PHONE
    const { profile: dataProfile = {}, phoneUpdated: dataUpdatePhone = {} }: any = this.props;
    return {
      dataProfile,
      dataUpdatePhone
    };
  };

  closePopup = () => {
    const { onOpenPopup }: any = this.props;
    onOpenPopup();
  };

  confirmUpdatePhone = async (otpCode: any, password: any, confirmPassword: any) => {
    // const { registerSessionId } = this.props;
    const { confirmUpdateMobile }: any = this.props;
    let { registerSessionId: registerSessionIdTemp }: any = this.state;

    if (!registerSessionIdTemp) {
      const { registerSessionId: dataReduxConfirm }: any = this.props;
      registerSessionIdTemp = dataReduxConfirm?.register_session_id;
    }
    const result = await confirmUpdateMobile(
      registerSessionIdTemp,
      otpCode,
      password,
      confirmPassword
    );
    return result;
  };

  render() {
    const { profile, actionName, onOpenPopup, phoneUpdated, resendOtpUpdatePhone }: any =
      this.props;

    return (
      <PopupConfirmUpdatePhone
        dataProfile={profile}
        actionName={actionName}
        phoneUpdated={phoneUpdated}
        onOpenPopup={onOpenPopup}
        closePopup={this.closePopup}
        confirmUpdatePhone={this.confirmUpdatePhone}
        resendOtpUpdatePhone={resendOtpUpdatePhone}
      />
    );
  }
}

export default PopupConfirmUpdatePhoneContainer;
