import React, { Component } from 'react';
import PopupUpdateProfile from '@components/profile/PopupUpdateProfile';
import { ID } from '@constants/constants';

class PopupUpdateProfileContainer extends Component<any> {
  closePopupProfile = () => {
    const { onOpenPopup }: any = this.props;
    if (onOpenPopup) onOpenPopup();
  };

  openPopupConfirmUpdatePhone = ({ phoneNumber }: any) => {
    const { onOpenPopup }: any = this.props;
    if (onOpenPopup) {
      onOpenPopup(ID.CONFIRM_PHONE, { phoneUpdated: phoneNumber });
    }
  };

  updateEmail = async (email: any) => {
    const { updateEmail }: any = this.props;
    const result = await updateEmail(email);
    return result;
  };

  updateMobile = async (phoneNumber: any) => {
    const { updateMobile, setUpdateMobileData }: any = this.props;
    const result = await updateMobile(phoneNumber);
    if (setUpdateMobileData) setUpdateMobileData(result);
    return result;
  };

  updatePassword = async (
    sessionId: any,
    oldPassword: any,
    password: any,
    confirmPassword: any
  ) => {
    const { updatePassword }: any = this.props;
    const result = await updatePassword(sessionId, oldPassword, password, confirmPassword);
    return result;
  };

  updateGivenName = async (givenName: any) => {
    const { updateProfile, deviceModel, deviceName, deviceType }: any = this.props;
    const result = await updateProfile({
      givenName,
      deviceModel,
      deviceName,
      deviceType
    });
    return result;
  };

  updateGender = async (gender: any) => {
    const { updateProfile, deviceModel, deviceName, deviceType }: any = this.props;
    const result = await updateProfile({
      gender,
      deviceModel,
      deviceName,
      deviceType
    });
    return result;
  };

  handleUpdateDob = async ({ dob }: any) => {
    const { updateDob }: any = this.props;
    const result = await updateDob({ dob });
    return result;
  };

  handleUpdateInvoice = async (invoice_data_update: any) => {
    const { updateInvoiceInfo }: any = this.props;
    const result = await updateInvoiceInfo(invoice_data_update);
    return result;
  };

  render() {
    const { actionName, profile, onDeleteHistory }: any = this.props;
    return (
      <PopupUpdateProfile
        closePopupProfile={this.closePopupProfile}
        openPopupConfirmUpdatePhone={this.openPopupConfirmUpdatePhone}
        actionName={actionName}
        dataProfile={profile}
        updateGivenName={this.updateGivenName}
        updateGender={this.updateGender}
        updatePassword={this.updatePassword}
        updateEmail={this.updateEmail}
        updateMobile={this.updateMobile}
        onDeleteHistory={onDeleteHistory}
        updateDob={this.handleUpdateDob}
        updateInvoiceInfo={this.handleUpdateInvoice}
      />
    );
  }
}

export default PopupUpdateProfileContainer;
