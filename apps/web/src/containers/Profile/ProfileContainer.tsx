/* eslint-disable class-methods-use-this */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { setLoadedData, setToast } from '@actions/app';
import { getContentWatchlater, getContentWatchmore, delContentWatchmore } from '@actions/detail';
import { resetMenu } from '@actions/menu';
import {
  updateProfile,
  updateEmail,
  updateMobile,
  updatePassword,
  confirmUpdateMobile,
  updateDob,
  updateInvoiceInfo
} from '@actions/profile';
import { getSearchHistory, delSearchHistory } from '@actions/search';
import {
  getDevicesManagement,
  getTransactions,
  getPurchased,
  getReferralProg
} from '@actions/user';
import Profile from '@profile/Profile';
import { checkIsEndPage, scrollToTop, encodeParamDestination } from '@helpers/common';
import LocalStorage from '@config/LocalStorage';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import { pageView } from '@tracking/functions/TrackingApp';
import { DOMAIN_WEB } from '@config/ConfigEnv';
import ConfigImage from '@config/ConfigImage';
import Head from 'next/head';
import UserApi from '@apis/userApi';
import { TEXT } from '@constants/text';
import { ID, PAGE } from '@constants/constants';
import isEmpty from 'lodash/isEmpty';
import { getTriggerConfig } from '@actions/appConfig';
import PopupConfirmUpdatePhoneContainer from './PopupConfirmUpdatePhoneContainer';
import PopupUpdateProfileContainer from './PopupUpdateProfileContainer';
import { TYPE_TRIGGER_AUTH } from '@constants/types';

class ProfileContainer extends Component {
  deleteContentWatchmore: any;
  deleteSearchHistory: any;
  constructor(props: any) {
    super(props);
    this.state = {
      actionName: '',
      isLoadMore: true,
      listDataAllPage: null,
      metadata: {
        page: 0,
        limit: 30,
        total: 0
      }
    };
  }

  componentDidMount() {
    const { profile, triggerConfig, setLoadedData, setToast, getTriggerConfig }: any = this.props;
    if (!profile?.id) {
      window.location.href = DOMAIN_WEB + (window?.location?.search || '');
      return;
    }
    if (isEmpty(triggerConfig)) {
      getTriggerConfig();
    }

    pageView(this.getSeoData());
    this.fetchData();
    setLoadedData(true);

    scrollToTop();
    const showToastUpdatePasswordSuccess = ConfigLocalStorage.get(
      LocalStorage.UPDATE_PASSWORD_SUCCESS_TOAST
    );
    if (showToastUpdatePasswordSuccess === 'true') {
      setToast({ message: TEXT.TOAST_UPDATE_PASSWORD_SUCCESS });
      ConfigLocalStorage.remove(LocalStorage.UPDATE_PASSWORD_SUCCESS_TOAST);
    }
  }

  componentDidUpdate(prevProps: any, prevState: any) {
    const { metadata }: any = this.state;
    // if change active or page => fetch data api
    if (metadata.page !== prevState.metadata.page) {
      this.fetchData();
    }
  }

  getSeoData = () => {
    const seo = {
      url: PAGE.PROFILE,
      title: 'Trang Cá Nhân - VieON',
      description: 'Thông tin cá nhân.'
    };
    const seoData = {
      page: PAGE.PROFILE,
      data: { seo },
      prefixPath: PAGE.PROFILE
    };
    return seoData;
  };

  fetchData = async (page?: any, limit?: any) => {
    const { state, props }: any = this;
    const { page: statePage, limit: stateLimit }: any = state;

    page = page || statePage || 0;
    limit = limit || stateLimit || 30;
    await props.getTransactions({ page, pageSize: limit });
    const { listDataAllPage }: any = state;
    if (!listDataAllPage) {
      if (props.getContentWatchlater) {
        props.getContentWatchlater({ page, limit, isGlobal: props?.geoCheck?.isGlobal });
      }
    }

    // TODO : Check Watch Later data, Fetch data if empty
    if (props.getContentWatchmore) {
      props.getContentWatchmore({
        page,
        limit,
        ribbonName: TEXT.WATCH_MORE,
        isGlobal: props?.geoCheck?.isGlobal
      });
    }
    if (!props?.currentProfile?.isKid) {
      if (props.getPurchased) props.getPurchased();
      if (props.getReferralProg) props.getReferralProg();
      if (props.getDevicesManagement) props.getDevicesManagement();
    }
  };

  onHandleAction = (actionName: any) => {
    if (actionName === ID.ALLOW_PUSH) {
      this.handleAllowPush();
      return;
    }
    const { router }: any = this.props;
    if (actionName === ID.UPDATE_PASSWORD) {
      const remakeDestination = encodeParamDestination(router?.asPath);
      router?.push(`${PAGE.USER_UPDATE_PASSWORD}/?destination=${remakeDestination}`);
      return;
    }
    if (actionName === ID.UPDATE_PHONE) {
      const remakeDestination = encodeParamDestination(router?.asPath);
      router?.push(
        `${PAGE.USER_UPDATE_PHONE_NUMBER}/?destination=${remakeDestination}&trigger=${TYPE_TRIGGER_AUTH.PROFILE_UPDATE_PHONE}`
      );
      return;
    }
    this.onOpenPopup(actionName);
  };

  handleAllowPush = () => {
    const { profile, setToast }: any = this.props;
    this.updateAllowPush(profile?.allowPush === 1 ? 0 : 1).then((res) => {
      let message = TEXT.MSG_ERROR;
      if (res?.success) {
        message = TEXT.UPDATE_SUCCESS;
      }
      setToast({ message });
    });
  };

  setUpdateMobileData = (data: any) => {
    this.setState({ registerSessionId: data?.data?.register_session_id });
  };

  onDeleteHistory = (actionName: any) => {
    const { props }: any = this;
    if (actionName === ID.CLEAR_HISTORY) {
      if (props.delContentWatchmore) {
        props.delContentWatchmore().then((res: any) => {
          let message = TEXT.MSG_ERROR;
          if (res?.success) {
            message = TEXT.ALREADY_DELETE_HISTORY;
          }
          props.setToast({ message });
        });
      }
    } else if (actionName === ID.CLEAR_SEARCH) {
      if (props.delSearchHistory) {
        props.delSearchHistory().then((res: any) => {
          let message = TEXT.MSG_ERROR;
          if (res?.data?.success) {
            message = TEXT.ALREADY_DELETE_SEARCH;
            props.getSearchHistory();
          }
          props.setToast({ message });
        });
      }
    }
    this.onOpenPopup();
  };

  onOpenPopup = (actionName?: any, params?: any) => {
    const phoneUpdated = params?.phoneUpdated;

    this.setState({ actionName, phoneUpdated });
  };

  updateAllowPush = async (allowPush: any) => {
    const { props }: any = this;
    const result = await props.updateProfile({
      givenName: '',
      dob: '',
      gender: '',
      allowPush,
      deviceModel: props?.deviceModel,
      deviceName: props?.deviceName,
      deviceType: props?.deviceType
    });
    return result;
  };

  handleLoadMoreFavorite = () => {
    const { props }: any = this;
    const metadata = props.listContentsWatchLater?.metadata;
    if (props.getContentWatchlater) {
      props.getContentWatchlater({
        page: (metadata?.page || 0) + 1,
        limit: 30,
        isGlobal: props?.geoCheck?.isGlobal
      });
    }
  };

  handleLoadMoreWatching = () => {
    const { props }: any = this;
    const metadata = props.listContentsWatchMore?.metadata;
    if (props.getContentWatchmore) {
      props.getContentWatchmore({
        page: (metadata?.page || 0) + 1,
        limit: 30,
        ribbonName: TEXT.WATCH_MORE,
        isGlobal: props?.geoCheck?.isGlobal
      });
    }
  };

  onScrollDownWatchMore = () => {
    const { token, geoCheck }: any = this.props;
    const data = token.listContentsWatchMore;
    const { page, limit, total } = data?.metadata || {};
    const totalPage = Math.floor(+total / 30);
    const nextPage = page + 1;
    const accessToken = token;

    if (checkIsEndPage({ page, limit, total })) {
      this.setState({ isLoadMore: false });
      return;
    }
    if (nextPage <= totalPage) {
      token
        .getContentWatchmore({
          page: nextPage,
          limit,
          accessToken,
          ribbonName: TEXT.WATCH_MORE,
          isGlobal: geoCheck?.isGlobal
        })
        .then((res: any) => {
          if (res && res?.payload?.data) {
            const dataRibbonDetail = res?.payload?.data;
            this.setState({
              metadata: dataRibbonDetail?.metadata
            });
          }
        });
    }
  };

  onScrollDownWatchLater = () => {
    const { token, geoCheck }: any = this.props;
    const data = token.listContentsWatchLater || null;

    const { page, limit, total } = data?.metadata || {};
    const totalPage = Math.floor(+total / 30);
    const nextPage = page + 1;
    const accessToken = token;
    if (checkIsEndPage({ page, limit, total })) {
      this.setState({ isLoadMore: false });
      return;
    }
    if (nextPage <= totalPage) {
      token
        .getContentWatchlater({ page: nextPage, limit, accessToken, isGlobal: geoCheck?.isGlobal })
        .then((res: any) => {
          if (res && res?.payload?.data) {
            const dataRibbonDetail = res?.payload?.data;
            this.setState({
              metadata: dataRibbonDetail?.metadata
            });
          }
        });
    }
  };

  resendOtpUpdatePhone = () => {
    const { state }: any = this;
    if (!state.phoneUpdated) return null;
    return UserApi.updateMobile(state.phoneUpdated).then((res) => {
      state.setState({ registerSessionId: res?.data?.register_session_id });
    });
  };

  render() {
    const {
      profile,
      transactions,
      listContentsWatchLater,
      listContentsWatchMore,
      PURCHASED_PAYMENT,
      currentProfile,
      router,
      updateProfile,
      updateMobile,
      updateEmail,
      updatePassword,
      updateDob,
      deviceType,
      deviceName,
      deviceModel,
      confirmUpdateMobile,
      updateInvoiceInfo
    }: any = this.props;
    const { actionName, phoneUpdated, registerSessionId, metadata, isLoadMore }: any = this.state;

    return (
      <>
        <Head>
          <link rel="icon" type="image/png" href={ConfigImage.favicon} />
          <title>VieON - Tài khoản</title>
          <meta name="format-detection" content="telephone=no" />
        </Head>
        <Profile
          purchased={PURCHASED_PAYMENT}
          router={router}
          profile={profile}
          currentProfile={currentProfile}
          transactions={transactions}
          favoriteData={listContentsWatchLater}
          watchingData={listContentsWatchMore}
          onHandleAction={this.onHandleAction}
          updateAllowPush={this.updateAllowPush}
          deleteSearchHistory={this.deleteSearchHistory}
          deleteContentWatchmore={this.deleteContentWatchmore}
          handleLoadMoreFavorite={this.handleLoadMoreFavorite}
          handleLoadMoreWatching={this.handleLoadMoreWatching}
          isLoadMore={isLoadMore}
          metadata={metadata}
          onScrollDownWatchMore={this.onScrollDownWatchMore}
          onScrollDownWatchLater={this.onScrollDownWatchLater}
        />

        {profile && actionName && (
          <PopupUpdateProfileContainer
            updateAllowPush={this.updateAllowPush}
            actionName={actionName}
            onOpenPopup={this.onOpenPopup}
            profile={profile}
            updateProfile={updateProfile}
            updateMobile={updateMobile}
            updateEmail={updateEmail}
            updatePassword={updatePassword}
            setUpdateMobileData={this.setUpdateMobileData}
            onDeleteHistory={this.onDeleteHistory}
            updateDob={updateDob}
            deviceType={deviceType}
            deviceName={deviceName}
            deviceModel={deviceModel}
            updateInvoiceInfo={updateInvoiceInfo}
          />
        )}

        {profile && actionName && (
          <>
            <PopupConfirmUpdatePhoneContainer
              profile={profile}
              actionName={actionName}
              phoneUpdated={phoneUpdated}
              onOpenPopup={this.onOpenPopup}
              confirmUpdateMobile={confirmUpdateMobile}
              registerSessionId={registerSessionId}
              resendOtpUpdatePhone={this.resendOtpUpdatePhone}
            />
          </>
        )}
      </>
    );
  }
}
const mapStateToProps = (state: any) => {
  const { Profile, User, Detail, App, Menu, Page, Payment, MultiProfile } = state || {};
  return {
    ...(Profile || {}),
    ...(Payment || {}),
    ...(User || {}),
    ...(Detail || {}),
    ...(App || {}),
    ...(Menu || {}),
    ...(Page || {}),
    ...(MultiProfile || {})
  };
};

const mapDispatchToProps = (dispatch: any) => {
  const actions = bindActionCreators(
    {
      setLoadedData: setLoadedData,
      setToast: setToast,
      updateProfile: updateProfile,
      delSearchHistory: delSearchHistory,
      getSearchHistory: getSearchHistory,
      delContentWatchmore: delContentWatchmore,
      resetMenu: resetMenu,
      updatePassword: updatePassword,
      updateEmail: updateEmail,
      updateMobile: updateMobile,
      updateDob: updateDob,
      confirmUpdateMobile: confirmUpdateMobile,
      getTransactions: getTransactions,
      getPurchased: getPurchased,
      getContentWatchlater: getContentWatchlater,
      getContentWatchmore: getContentWatchmore,
      getReferralProg: getReferralProg,
      getDevicesManagement: getDevicesManagement,
      getTriggerConfig: getTriggerConfig,
      updateInvoiceInfo: updateInvoiceInfo
    },
    dispatch
  );
  return { ...actions, dispatch };
};

export default connect(mapStateToProps, mapDispatchToProps)(ProfileContainer);
