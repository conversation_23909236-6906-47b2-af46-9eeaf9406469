import {
  clearToast,
  getGeoCheck,
  getWebConfig,
  setDeviceId,
  setDeviceInfo,
  setToast,
  setToken,
  setViewPort
} from '@actions/app';
import { loginAnonymous } from '@actions/globalAuth';
import { getMenu } from '@actions/menu';
import { getMultiProfile, getProfileSelected } from '@actions/multiProfile';
import { openPopup } from '@actions/popup';
import { getProfile } from '@actions/profile';
import { getDeviceToken, getIsFirstPay, getPreorderReminder } from '@actions/user';
import UserApi from '@apis/userApi';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import Footer from '@components/footer/Footer';
import Splash from '@components/splash/Splash';
import ConfigCookie from '@config/ConfigCookie';
import { DISABLE_3RD, ENABLE_SDK_GPT } from '@config/ConfigEnv';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import LocalStorage from '@config/LocalStorage';
import {
  BANNER_VIP_TYPE,
  DEVICE_TYPE,
  DEVICE_TYPE_DETECT,
  HTTP_CODE,
  ID,
  LOGIN_TYPE,
  OUTSTREAM_TYPE,
  PAGE,
  PLATFORM,
  POPUP,
  POSITION,
  TOAST,
  TOAST_KEY,
  TVOD
} from '@constants/constants';
import { TEXT } from '@constants/text';
import { useVieRouter, useViewport } from '@customHook';
import {
  controlScroll,
  createTimeout,
  decodeSignature,
  encodeSignature,
  getCookie,
  getModelDevice,
  onOpenPayment,
  parseJwt
} from '@helpers/common';
import ScriptAfterInteractive from '@script/ScriptAfterInteractive';
import { pushToLobby } from '@services/multiProfileServices';
import { handleCheckLocalGlobal } from '@services/popupServices';
import { dmpIdentify } from '@tracking/TrackingDMP';
import { gtmSignIn, gtmSignUp, shootLoadDeepLink } from '@tracking/TrackingGTM';
import { identifyMoe } from '@tracking/TrackingMoEngage';
import { initSegment, shootIdentify } from '@tracking/TrackingSegment';
import TrackingApp from '@tracking/functions/TrackingApp';
import { sentryInit } from '@tracking/sentry';
import classNames from 'classnames';
import Fingerprint2 from 'fingerprintjs2';
import isEmpty from 'lodash/isEmpty';
import 'moment/locale/vi';
import dynamic from 'next/dynamic';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { getSelectorsByUserAgent } from 'react-device-detect';
import { useDispatch, useSelector } from 'react-redux';
import NotAvailable from '../components/NotAvailable';
import '../customRequest';
import HeaderContainer from './Header/HeaderContainer';
import CMApi from '@/apis/cmApi';
import { ACTION_TYPE, createAction } from '@/actions/actionType';
import { VALUE } from '@/config/ConfigSegment';
import MultiProfileApi from '@apis/MultiProfile';
import { FirebaseConfig } from '../script/firebase';
import styles from './LayoutContainer.module.scss';

declare const window: any;

const RibbonPreview = dynamic(() => import('@components/basic/Ribbon/RibbonPreview'), {
  ssr: false
});
const CardHover = dynamic(() => import('@components/Card/CardHover'), { ssr: false });
const MastheadAiAds = dynamic(() => import('@components/OutstreamAds/MastheadAIAds'), {
  ssr: false
});
const SmartBanner = dynamic(() => import('@components/SmartBanner/SmartBanner'), { ssr: false });
const InPageBannerAds = dynamic(() => import('@components/OutstreamAds/InPageBannerAds'), {
  ssr: false
});
const WelcomeAdsFullscreen = dynamic(
  () => import('@components/OutstreamAds/welcome-ads/Fullscreen'),
  {
    ssr: false
  }
);
const Popup = dynamic(() => import('@components/popup'), { ssr: false });
const Loading = dynamic(() => import('@components/basic/Loading/Loading'), {
  ssr: false
});
const ToastHandler = dynamic(() => import('@components/basic/Toast/ToastHandler'), { ssr: false });

const LayoutContainer = (layoutProps: any) => {
  const [tokenDevice, setTokenDevice] = useState<any>('');
  const { children, isUnderConstruction } = layoutProps || {};
  const renderNotFoundPage = children?.props?.pageProps?.renderNotFoundPage;
  const dispatch = useDispatch();
  const preorderRemindTimerRef = useRef<any>(null);
  const timeOutToastRemindRef = useRef<any>(null);
  const viewPort = useViewport();
  const isMobileViewPort = viewPort.width < 1024;
  const PopupStore = useSelector((state: any) => state?.Popup);

  const {
    isMobile,
    loading,
    deviceId,
    token,
    outStreamAds,
    configPersonalizationFlow,
    isTokenExpired,
    toastData,
    deviceModel,
    deviceName,
    deviceType
  } = useSelector((state: any) => state?.App || {});
  const { menuList } = useSelector((state: any) => state?.Menu);
  const { previewCard, popupName, cardHover } = PopupStore || {};
  const Profile = useSelector((state: any) => state?.Profile);
  const { isGlobal, globalHost, notAvailable } = useSelector((state: any) => state?.App?.geoCheck);
  const { activeMenu } = useSelector((state: any) => state?.Menu || {});
  const { profile } = Profile || {};
  const { currentProfile } = useSelector((state: any) => state?.MultiProfile || {});
  const User = useSelector((state: any) => state?.User);
  const preorderReminder = useSelector((state: any) => state?.User?.preorderReminder);
  const deviceTokenData = useSelector((state: any) => state?.User?.loyalty?.deviceTokenData);
  const { isOnLoyalty } = useSelector((state: any) => state?.App?.webConfig?.featureFlag || false);
  const { isMasterBanner } = useSelector((state: any) => state?.Page || {});

  const isDaySegmentUserFullScreen = ConfigLocalStorage.get(
    LocalStorage.DAY_SEGMENT_USER_FULL_SCREEN
  );
  const { masthead } = useSelector((state: any) => state?.App?.outStreamAds) || {};
  const { USER_TYPE, isTriggerFirstPay, dataSegment, isStatusRepay } = User || {};
  const userType = USER_TYPE;
  const router = useVieRouter();
  const { pathname, query, asPath } = router || {};

  const isAuthPage = useMemo(
    () =>
      (pathname || '').includes(PAGE.AUTH) ||
      (pathname || '').includes(PAGE.USER_UPDATE_PASSWORD) ||
      (pathname || '').includes(PAGE.USER_UPDATE_PHONE_NUMBER),
    [pathname]
  );
  const isPayment = useMemo(() => {
    const tempPath = pathname || '';
    return (
      tempPath.includes(PAGE.PAYMENT) ||
      tempPath.includes(PAGE.PAYMENT_RESULT) ||
      tempPath.includes(PAGE.ZALOPAY) ||
      tempPath.includes(PAGE.PAGE_SUPPORT_SMART_TV) ||
      tempPath.includes(PAGE.RENTAL_CONTENT)
    );
  }, [pathname]);

  const emailVerified = useMemo(() => pathname === PAGE.EMAIL_VERIFIED, [pathname]);
  const isLiveSupport = useMemo(() => pathname === '/' && query?.support, [pathname, query]);
  const isViewApp = query?.isViewApp;
  const isKid = currentProfile?.isKid;
  const isLobby = pathname.includes(PAGE.LOBBY_PROFILES);
  const vid = query?.vid;
  const isInAppZaloPayAboutUsPage =
    pathname === PAGE.ZALOPAY_USAGE ||
    pathname === PAGE.ZALOPAY_AGREEMENT ||
    pathname === PAGE.ZALOPAY_REGULATION ||
    pathname === PAGE.ZALOPAY_COPY_RIGHT ||
    pathname === PAGE.ZALOPAY_POLICY_CANCELLATION ||
    pathname === PAGE.ZALOPAY_POLICY_PRIVACY;
  const isPaymentSpaceTop =
    !isInAppZaloPayAboutUsPage && (isPayment || pathname.includes(PAGE.VOUCHER));

  const mainClass = useMemo(() => {
    return classNames(
      !isPaymentSpaceTop && 'main',
      styles.main,
      isPaymentSpaceTop && styles.mainPayment,
      {
        'p-b':
          pathname.includes(PAGE.VOD) ||
          isLobby ||
          isAuthPage ||
          query?.slug?.includes(ID.LOYALTY_POINT) ||
          query?.slug?.includes(ID.RESTRICTION_CONTENT)
      }
    );
  }, [
    pathname,
    query,
    isInAppZaloPayAboutUsPage,
    isPayment,
    isLobby,
    isAuthPage,
    isMasterBanner,
    isViewApp,
    notAvailable
  ]);

  const firebaseConfigInstance: any = useMemo(() => {
    if (profile?.id) {
      return new FirebaseConfig();
    }
    return null;
  }, [profile?.id]);

  useEffect(() => {
    if (isTokenExpired) return;
    document.addEventListener('scrollend', handleRedirect);
    document.addEventListener('mousedown', handleRedirect);
    document.addEventListener('touchstart', handleRedirect);
    const oldAccessToken = ConfigCookie.load(ConfigCookie.KEY.ACCESS_TOKEN);
    const triggerAfterLogin = ConfigLocalStorage.get(LocalStorage.TRIGGER_AFTER_LOGIN);
    const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE);
    const reLoginParams: any = ConfigLocalStorage.get(LocalStorage.RE_LOGIN_PARAMS);
    const { url } = JSON.parse(reLoginParams || '{}');
    const urlResultParams = new URLSearchParams(url?.slice(url.indexOf('?')));
    const queryAuth = Object.fromEntries(urlResultParams.entries());

    if (
      !isPayment &&
      pathname !== PAGE.VOUCHER &&
      !pathname.includes(PAGE.AUTH) &&
      !isLobby &&
      !queryAuth?.isTriggerAuth
    ) {
      if (
        accessToken &&
        profile?.id &&
        !currentProfile?.id &&
        triggerAfterLogin !== LOGIN_TYPE.FIRST_LOGIN
      ) {
        dispatch(getMultiProfile());
        pushToLobby({
          asPath: router?.asPath,
          router,
          profile,
          dispatch
        });
      }
    }

    if (oldAccessToken) {
      const valueEncode = encodeSignature({ accessToken: oldAccessToken });
      ConfigCookie.save(ConfigCookie.KEY.SIGNATURE, valueEncode);
      ConfigCookie.remove(ConfigCookie.KEY.ACCESS_TOKEN);
      window.location.reload();
    }
    sentryInit();
    return () => {
      clearTimeout(preorderRemindTimerRef.current);
      clearTimeout(timeOutToastRemindRef.current);
      document.removeEventListener('scrollend', handleRedirect);
      document.removeEventListener('mousedown', handleRedirect);
      document.removeEventListener('touchstart', handleRedirect);
    };
  }, [pathname]);

  useEffect(() => {
    handleRedirect();
  }, [asPath]);

  useEffect(() => {
    if (isTokenExpired) return;
    dispatch(setViewPort(isMobileViewPort));
  }, [isMobileViewPort]);

  useEffect(() => {
    if (isTokenExpired) return;
    if (!isUnderConstruction) {
      handleGetAndSetDeviceId();
      if (ENABLE_SDK_GPT === true || ENABLE_SDK_GPT === 'true') {
        GPT.configure({
          // seedFileUrl: 'https://securepubads.g.doubleclick.net/tag/js/gpt.js',
          renderWhenViewable: false,
          viewableThreshold: 0
        });
      }
    }
  }, [isUnderConstruction]);

  useEffect(() => {
    if (isTokenExpired) return;
    if (profile?.hadTvod) {
      dispatch(getPreorderReminder());
    }
  }, [profile?.id]);

  useEffect(() => {
    if (isTokenExpired) return;
    const { profileToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};
    if (profile?.id && profileToken && !currentProfile?.id) {
      dispatch(getProfileSelected({ router }));
    }
  }, [profile?.id, currentProfile?.id]);

  useEffect(() => {
    if (isTokenExpired) return;
    if (
      !isUnderConstruction &&
      profile?.id &&
      profile?.showGuestFlow &&
      configPersonalizationFlow?.userFlow &&
      pathname === PAGE.HOME
    ) {
      dispatch(openPopup({ name: POPUP.NAME.REQUEST_FAVORITE_LIST }));
    }
  }, [isUnderConstruction, profile, pathname, configPersonalizationFlow]);

  useEffect(() => {
    if (!isUnderConstruction) {
      if (isTokenExpired) {
        handleRefreshToken();
      } else {
        initSegment();
      }
    }
  }, [isUnderConstruction, isTokenExpired]);

  useEffect(() => {
    if (isTokenExpired) return;
    if (!isUnderConstruction) {
      const anonymousToken = ConfigCookie.load(ConfigCookie.KEY.ANONYMOUS_TOKEN);
      const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};
      if (!profile?.id) {
        if (anonymousToken && anonymousToken !== 'undefined') {
          const dataToken = parseJwt(anonymousToken);
          ConfigLocalStorage.set(LocalStorage.ANONYMOUS_ID, dataToken?.sub || '');
          dispatch(setToken(anonymousToken));
        }
        if (accessToken) {
          dispatch(setToken(accessToken));
          dispatch(
            getProfile({
              deviceModel,
              deviceName,
              deviceType
            })
          );
          const timeNow = new Date();
          timeNow.setFullYear(timeNow.getFullYear() + 1);
          const removeOption = {
            expires: timeNow,
            domain: '.vieon.vn',
            path: '/'
          };
          ConfigCookie.remove(ConfigCookie.KEY.ANONYMOUS_TOKEN, removeOption);
        } else {
          ConfigCookie.remove(ConfigCookie.KEY.SIGNATURE);
        }
        handleUserGuest();
      } else {
        handleProfile();
        // MoEngage Identify
        identifyMoe(profile);
      }
    }
  }, [isUnderConstruction, profile?.id]);

  useEffect(() => {
    if (!isTokenExpired && !isUnderConstruction && profile?.id) {
      const showToastUpdatePhoneNumberSuccess = ConfigLocalStorage.get(
        LocalStorage.UPDATE_PHONE_NUMBER_SUCCESS_TOAST
      );
      if (showToastUpdatePhoneNumberSuccess === 'true') {
        setToast({ message: TEXT.UPDATE_PHONE_SUCCESS });
        ConfigLocalStorage.remove(LocalStorage.UPDATE_PHONE_NUMBER_SUCCESS_TOAST);
      }
      if (!(pathname || '').includes(PAGE.AUTH)) {
        const loginType = ConfigLocalStorage.get(LocalStorage.LOGIN_TYPE);
        if (loginType) {
          let message = '';
          if (loginType === LOGIN_TYPE.FIRST_LOGIN) {
            if (pathname !== PAGE.ZALOPAY_RESULT && pathname !== PAGE.PAYMENT_RESULT) {
              message = TEXT.VIEON_WELCOME;
            }
          } else if (loginType === LOGIN_TYPE.RE_LOGIN) {
            message = TEXT.RELOGIN_WELCOME;
          }
          if (message) {
            dispatch(setToast({ message }));
          }
          ConfigLocalStorage.remove(LocalStorage.LOGIN_TYPE);
        }
      }
    }
  }, [isTokenExpired, isUnderConstruction, profile?.id, pathname]);

  useEffect(() => {
    if (isTokenExpired) return;
    if (!isUnderConstruction && deviceId && deviceModel) {
      if (!profile?.id) {
        const anonymousToken = ConfigCookie.load(ConfigCookie.KEY.ANONYMOUS_TOKEN);
        const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};
        const dataToken = anonymousToken ? parseJwt(anonymousToken) : '';
        const timeExp = dataToken?.exp;
        const currentTime = new Date().getTime() / 1000;
        if (
          ((!anonymousToken || anonymousToken === 'undefined') && !accessToken) ||
          (anonymousToken && timeExp < currentTime)
        ) {
          dispatch(
            loginAnonymous({
              deviceId,
              model: deviceModel
            })
          );
        }
      } else {
        const { id, givenName, email, mobile, createdAt, isPremium } = profile || {};
        // SEGMENT IDENTIFY
        const identifyParams = {
          username: givenName,
          login: true,
          vip: isPremium,
          created_at: new Date(createdAt * 1000).toISOString(),
          email,
          mobile,
          device_id: deviceId,
          Platform: isMobile ? PLATFORM.MOBILE_WEB : PLATFORM.WEB
        };
        // DMP Identify
        dmpIdentify(id, identifyParams);
      }
    }
  }, [isUnderConstruction, profile?.id, deviceId, deviceModel]);

  useEffect(() => {
    if (isTokenExpired) return;
    if (previewCard?.expand && previewCard?.data) {
      controlScroll({ isScroll: false });
    } else {
      controlScroll({ isScroll: true });
    }
  }, [previewCard?.expand]);

  useEffect(() => {
    if (isTokenExpired) return;
    if (token) {
      shootLoadDeepLink({
        token,
        router
      });
    }
  }, [token]);

  useEffect(() => {
    if (isTokenExpired) return;
    if (!isUnderConstruction && deviceId && token) {
      handleShootIdentify(deviceId);
      const loginType = ConfigLocalStorage.get(LocalStorage.LOGIN_TYPE);
      const { mobile, email, id } = profile || {};
      const loginAuto = !loginType;
      TrackingApp.webVisited({
        loginMethod: mobile ? VALUE.PHONE : VALUE.SOCIAL,
        isAuto: !!loginAuto,
        flowName: pathname === PAGE.RENTAL_CONTENT ? TVOD.TVOD_TYPE : '',
        userId: id,
        mobile,
        email,
        query
      });
    }
  }, [isUnderConstruction, deviceId, token]);

  useEffect(() => {
    if (isTokenExpired) return;
    if (!isUnderConstruction && profile?.httpCode === HTTP_CODE.BLOCKED_ACCOUNT) {
      dispatch(
        openPopup({
          name: POPUP.NAME.BLOCK_ACCOUNT
        })
      );
    }
  }, [isUnderConstruction, profile]);

  useEffect(() => {
    if (isTokenExpired) return;
    if ((preorderReminder || []).length > 0) {
      handleAutoRemindLiveEvent(preorderReminder);
    }
    return () => {
      clearTimeout(preorderRemindTimerRef.current);
      clearTimeout(timeOutToastRemindRef.current);
    };
  }, [preorderReminder]);

  useEffect(() => {
    if (profile?.id && !isKid && isOnLoyalty) {
      firebaseConfigInstance.getMessagingToken(setTokenDevice);
      if (tokenDevice) {
        dispatch(getDeviceToken(tokenDevice));
      }
    }
  }, [profile?.id, tokenDevice, isKid]);

  useEffect(() => {
    if (profile?.id && deviceId && deviceTokenData && !isKid && isOnLoyalty) {
      UserApi.postDeviceToken({
        userId: profile?.id,
        token: deviceTokenData,
        deviceType: deviceType?.toUpperCase(),
        deviceId
      });
    }
  }, [profile?.id, deviceType, deviceId, deviceTokenData, isKid, isOnLoyalty]);

  useEffect(() => {
    const notiPromoteTierData =
      JSON.parse(sessionStorage.getItem(LocalStorage.NOTI_PROMOTE_TIER) as string) || null;
    if (
      !pathname.includes(PAGE.VOD) &&
      !pathname.includes(PAGE.LIVE_STREAM) &&
      !pathname.includes(PAGE.LIVE_TV) &&
      !isPayment &&
      !isEmpty(notiPromoteTierData) &&
      !isKid &&
      !isLobby &&
      isOnLoyalty
    ) {
      dispatch(
        setToast({
          title: notiPromoteTierData?.notification?.title,
          content: notiPromoteTierData?.notification?.body,
          duration: 10000,
          noIcon: true,
          kind: TOAST.KIND.TIMER,
          type: notiPromoteTierData?.data?.tierName,
          position: POSITION.BOTTOM_RIGHT,
          btnClick: () =>
            dispatch(
              clearToast({
                position: POSITION.BOTTOM_RIGHT
              })
            )
        })
      );
      sessionStorage.removeItem(LocalStorage.NOTI_PROMOTE_TIER);
    }
  }, [pathname, isPayment, isKid, isOnLoyalty, isLobby]);

  useEffect(() => {
    if (profile?.id && !isKid) {
      const handleTierChanged = (res: any) => {
        if (query?.slug?.includes(ID.LOYALTY_POINT)) {
          window.location.reload();
          sessionStorage.setItem(LocalStorage.NOTI_PROMOTE_TIER, JSON.stringify(res));
        } else {
          if (
            !pathname.includes(PAGE.VOD) &&
            !pathname.includes(PAGE.LIVE_STREAM) &&
            !pathname.includes(PAGE.LIVE_TV) &&
            !isPayment &&
            !isLobby
          ) {
            dispatch(
              setToast({
                title: res?.notification?.title,
                content: res?.notification?.body,
                duration: 10000,
                noIcon: true,
                kind: TOAST.KIND.TIMER,
                type: res?.data?.tierName,
                position: POSITION.BOTTOM_RIGHT,
                btnClick: dispatch(
                  clearToast({
                    position: POSITION.BOTTOM_RIGHT
                  })
                )
              })
            );
            return;
          }
          sessionStorage.setItem(LocalStorage.NOTI_PROMOTE_TIER, JSON.stringify(res));
        }
      };

      const handleEarningPoints = (res: any) => {
        if (!isLobby) {
          dispatch(
            setToast({
              message: res?.notification?.body
            })
          );
        }
      };

      firebaseConfigInstance.onMessageListener({
        handleTierChanged,
        handleEarningPoints
      });
    }
  });

  useEffect(() => {
    // Register service worker
    const registerServiceWorker = async () => {
      if ('serviceWorker' in navigator) {
        try {
          const registration = await navigator.serviceWorker.register('/serviceworker.js');
          console.log('ServiceWorker registration successful');

          // Force update if needed
          registration.update();
        } catch (err) {
          console.log('ServiceWorker registration failed:', err);
        }
      }
    };
    registerServiceWorker();
    const getOutstreamAdsData = async () => {
      const outstreamAdsData = await CMApi.getOutstreamAd({
        ads_format: Object.values(OUTSTREAM_TYPE).map((type) => type.value)
      });

      const adsMapping = {
        [OUTSTREAM_TYPE.WELCOME_AD_HOME.name]: 'welcomeHome',
        [OUTSTREAM_TYPE.WELCOME_AD_MAIN_MENU.name]: 'welcomeMainMenu',
        [OUTSTREAM_TYPE.WELCOME_AD_VIDEO_INTRO.name]: 'welcomeIntro',
        [OUTSTREAM_TYPE.MASTER_BANNER.name]: 'masterBanner',
        [OUTSTREAM_TYPE.BANNER_RIBBON.name]: 'bannerRibbonAds',
        [OUTSTREAM_TYPE.PROMOTED_RIBBON.name]: 'promotedRibbon',
        [OUTSTREAM_TYPE.INPAGE.name]: 'inpage',
        [OUTSTREAM_TYPE.LOBBY.name]: 'lobby'
      };

      Object.entries(adsMapping).forEach(([adType, storeKey]) => {
        const ad = outstreamAdsData?.items?.find((item: any) => item?.type === adType);
        if (ad) {
          dispatch(
            createAction(ACTION_TYPE.SET_OUT_STREAM_ADS, {
              [storeKey]: Array.isArray(ad?.ads_config)
                ? ad?.ads_config.map((config: any) => ({
                    ...config,
                    path: ad?.url
                  }))
                : { ...ad?.ads_config, path: ad?.url }
            })
          );
        }
      });
    };

    getOutstreamAdsData();
  }, [asPath]);

  useEffect(() => {
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    router.events.on('routeChangeStart', hanldeChangePage);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      router.events.off('routeChangeStart', hanldeChangePage);
    };
  }, [router]);

  useEffect(() => {
    if (!currentProfile?.id || isPayment || pathname.includes(PAGE.VOUCHER) || isLobby) {
      return;
    }

    const controller = new AbortController();
    MultiProfileApi.getAgreement()
      .then((res) => {
        if (res?.httpCode === 404) {
          router.push(`${PAGE.LOBBY_PROFILES}/?from=login`);
        }
      })
      .catch((err) => {
        console.error('Failed to fetch agreement:', err);
      });

    return () => controller.abort();
  }, [currentProfile?.id, isPayment, pathname, isLobby]);
  const hanldeChangePage = () => {
    if (!navigator.onLine) {
      return setTimeout(() => {
        router.reload();
      }, 1500);
    }
    return;
  };

  const handleOnline = () => {
    if (
      !pathname.includes(PAGE.VOD) &&
      !pathname.includes(PAGE.LIVE_STREAM) &&
      !pathname.includes(PAGE.LIVE_TV) &&
      !asPath.includes('?vid=')
    ) {
      dispatch(
        setToast({
          message: TEXT.ONLINE,
          kind: TOAST.KIND.WARNING,
          position: POSITION.TOP_RIGHT
        })
      );
    }

    if (
      !pathname.includes(PAGE.VOD) &&
      !pathname.includes(PAGE.LIVE_STREAM) &&
      !pathname.includes(PAGE.LIVE_TV)
    ) {
      setTimeout(() => {
        router.reload();
      }, 1000);
    }
  };

  const handleOffline = async () => {
    if (
      !pathname.includes(PAGE.VOD) &&
      !pathname.includes(PAGE.LIVE_STREAM) &&
      !pathname.includes(PAGE.LIVE_TV)
    ) {
      const cache = await caches.open('offline-cache-v1');
      await cache.put('/tracking-data', new Response(JSON.stringify({})));
      if (!asPath.includes('?vid=')) {
        dispatch(
          setToast({
            message: (
              <p>
                Mất kết nối mạng!
                <br />
                Vui lòng kiểm tra đường truyền
              </p>
            ),
            kind: TOAST.KIND.WARNING,
            position: POSITION.TOP_RIGHT
          })
        );
      }
    }
  };

  useEffect(() => {
    if (isGlobal) return;
    const handleTriggerAfterLogin = async () => {
      try {
        const res = await UserApi.getTriggerAfterRegister();
        if (res?.success) {
          dispatch(openPopup({ name: POPUP.NAME.TRIGGER_FIRSTPAY, data: res?.data?.result }));
          dispatch(getIsFirstPay(true));
        }
      } catch (error) {
        console.log(error, 'error when call api firstpay trigger');
      }
    };

    const triggerAfterLogin = ConfigLocalStorage.get(LocalStorage.TRIGGER_AFTER_LOGIN);
    if (
      ![PAGE.PAYMENT, PAGE.LOBBY_PROFILES, PAGE.AUTH].some((page) => asPath.includes(page)) &&
      profile?.id &&
      !profile?.isPremium &&
      triggerAfterLogin === LOGIN_TYPE.FIRST_LOGIN &&
      currentProfile?.id
    ) {
      handleTriggerAfterLogin();
    }
  }, [asPath, profile, dispatch, currentProfile, isGlobal]);

  const handleRedirect = (e?: any) => {
    if (!window.interacted && e) {
      window.interacted = true;
      document.removeEventListener('scrollend', handleRedirect);
      document.removeEventListener('mousedown', handleRedirect);
      document.removeEventListener('touchstart', handleRedirect);
    }
    if (handleCheckLocalGlobal(pathname) && window.interacted) {
      if (isGlobal && !globalHost) {
        dispatch(openPopup({ name: POPUP.NAME.LOCAL_TO_GLOBAL }));
      } else if (!isGlobal && globalHost) {
        dispatch(openPopup({ name: POPUP.NAME.GLOBAL_TO_LOCAL }));
      }
    }
  };

  const handleAutoRemindLiveEvent = (data: any) => {
    const firstData = data?.[0] || null;
    if (!firstData) return;
    const { remainTime, liveTextNotify, seo } = firstData || {};
    clearTimeout(preorderRemindTimerRef.current);
    clearTimeout(timeOutToastRemindRef.current);
    if (remainTime >= 0) {
      const onClick = () => {
        if (seo?.url) {
          router.push(PAGE.LIVE_STREAM_SLUG, seo?.url);
        }
      };
      timeOutToastRemindRef.current = createTimeout(() => {
        dispatch(
          setToast({
            message: liveTextNotify || '',
            duration: 10000,
            noIcon: true,
            btnText: TEXT.WATCH_NOW,
            toastKey: TOAST_KEY.REMINDER,
            btnClick: onClick
          })
        );
        preorderRemindTimerRef.current = createTimeout(() => {
          dispatch(getPreorderReminder());
        }, 2000);
      }, remainTime * 1000);
    }
  };

  const handleShootIdentify = (deviceId: any) => {
    const { id, givenName, email, mobile, createdAt, isPremium } = profile || {};
    let params: any = {
      device_id: deviceId,
      Platform: isMobile ? PLATFORM.MOBILE_WEB : PLATFORM.WEB
    };

    if (id) {
      params = {
        username: givenName,
        login: true,
        vip: isPremium,
        created_at: new Date(createdAt * 1000).toISOString(),
        email,
        mobile,
        device_id: deviceId,
        Platform: isMobile ? PLATFORM.MOBILE_WEB : PLATFORM.WEB,

        profile_id: currentProfile?.id || null,
        profile_name: currentProfile?.name || null,
        profile_type: currentProfile?.isKid ? TEXT.CHILDREN : TEXT.ADULT
      };
    }

    shootIdentify(id, params);
  };

  const handleGetAndSetDeviceId = () => {
    Fingerprint2.get(async (components: any) => {
      const values = components.map((component: any) => component.value);
      const murmur = Fingerprint2.x64hash128(values.join(''), 31);
      dispatch(setDeviceId(murmur));
    });
  };

  const handleUserGuest = () => {
    const userGuestId = ConfigLocalStorage.get(LocalStorage.USER_GUEST);
    if (userGuestId) {
      dispatch(setToast({ message: TEXT.VIEON_WELCOME }));
      ConfigLocalStorage.remove(LocalStorage.USER_GUEST);
    }
  };

  const handleRefreshToken = () =>
    UserApi.globalRefreshToken().then(() => window.location.reload());

  const handleProfile = () => {
    ConfigCookie.remove(ConfigCookie.KEY.ANONYMOUS_TOKEN);
    const loginType = ConfigLocalStorage.get(LocalStorage.LOGIN_TYPE);
    const loginPayment = ConfigLocalStorage.get(LocalStorage.LOGIN_PAYMENT);
    const loginBannerVip = ConfigLocalStorage.get(LocalStorage.LOGIN_BANNER_VIP);
    const registrationTriggerData: any = ConfigLocalStorage.get(LocalStorage.REGISTRATION_TRIGGER);

    if (loginType) {
      if (loginType === LOGIN_TYPE.FIRST_LOGIN) {
        let registrationTrigger = null;
        if (registrationTriggerData) {
          registrationTrigger = JSON.parse(registrationTriggerData) || null;
        }
        TrackingApp.signUpSuccessfully({
          registrationTrigger,
          query
        });
        gtmSignUp({
          profile,
          query
        });
      } else if (loginType === LOGIN_TYPE.RE_LOGIN) {
        gtmSignIn({
          profile,
          query
        });
      }
      ConfigLocalStorage.remove(LocalStorage.REGISTRATION_TRIGGER);
    } else {
      gtmSignIn({
        profile,
        query
      });
    }
    if (loginPayment === 'true' || loginBannerVip === 'true') {
      ConfigLocalStorage.remove(LocalStorage.LOGIN_PAYMENT);
      ConfigLocalStorage.remove(LocalStorage.RE_LOGIN_PARAMS);
      ConfigLocalStorage.remove(LocalStorage.LOGIN_BANNER_VIP);
      const type = userType?.type;
      const isHide = userType?.hideButtonBuyPackage;
      const groupPackageId = userType?.packageGroupId;
      if (type === BANNER_VIP_TYPE.VIP_K_PLUS && loginBannerVip === 'true') {
        if (!isHide && loginPayment === 'true') {
          if (onOpenPayment) onOpenPayment(router, { gpk: groupPackageId });
        } else {
          const kUrl = PAGE.LIVE_TV;
          const search = window?.location?.search || '';
          const categoryId = userType?.livetvGroupId;
          router.push({
            pathname: kUrl + search,
            hash: categoryId
          });
        }
      } else if (onOpenPayment) onOpenPayment(router);
    }
  };

  if (isTokenExpired) return null;

  const renderIntro = () => {
    if (vid && !isLobby) {
      return <RibbonPreview />;
    }
    return null;
  };

  return (
    <>
      {!isViewApp && isMobile && !isPayment && !isLobby && <SmartBanner />}

      {!isMobile &&
        !profile?.isPremium &&
        !isKid &&
        !isGlobal &&
        !isStatusRepay &&
        (!dataSegment?.isPermission ||
          (dataSegment?.isPermission && isDaySegmentUserFullScreen)) && (
          <MastheadAiAds activeMenu={activeMenu} />
        )}

      {!isAuthPage &&
        !isViewApp &&
        !isLobby &&
        !notAvailable &&
        React.createElement(HeaderContainer as any, {
          pathname,
          isPayment,
          renderNotFoundPage,
          emailVerified
        })}

      <main id="main" className={mainClass}>
        {notAvailable ? <NotAvailable /> : children}
      </main>
      {!isMobile && !profile?.isPremium && !isKid && !isGlobal && <InPageBannerAds />}
      {renderIntro()}
      {cardHover?.id && <CardHover data={cardHover} />}
      {popupName && <Popup isUnderConstruction={isUnderConstruction} />}
      {!isMobile &&
        !profile?.isPremium &&
        !isKid &&
        (!masthead || masthead?.isOffAds) &&
        !isTriggerFirstPay && (
          <WelcomeAdsFullscreen
            menuList={menuList}
            router={router}
            data={outStreamAds}
            isMobile={isMobile}
          />
        )}
      {!isAuthPage && !isViewApp && !isLobby && (
        <Footer isLiveSupport={isLiveSupport} emailVerified={emailVerified} isMobile={isMobile} />
      )}
      {!isViewApp && !emailVerified && <Splash />}
      {loading && <Loading />}
      {toastData && <ToastHandler />}
      {!DISABLE_3RD && <ScriptAfterInteractive isKid={isKid} />}
    </>
  );
};

LayoutContainer.getInitialProps = async ({ store, req, res, asPath, query }: any) => {
  if (req) {
    // call on server
    const pathname = req?._parsedUrl?.pathname;
    const origin = req?.headers?.host || '';
    const globalHost = (req?.headers?.host || '').includes('.global');
    const isViewApp = query?.isViewApp;
    // Detect is mobile
    const userAgent = req?.headers?.['user-agent'];
    const {
      isMobile,
      isTablet,
      isSafari,
      isIOS,
      isAndroid,
      isMobileOnly,
      browserName,
      osName,
      osVersion,
      browserVersion,
      isWindows,
      deviceType,
      isMacOs
    } = getSelectorsByUserAgent(userAgent);
    const deviceModel = osName ? `${osName} ${osVersion || ''}` : '';

    // Check smarttv platform
    const isSmartTvPlatform = deviceType === DEVICE_TYPE_DETECT.SMART_TV || false;

    // GET cookie from server
    const cookie = req?.cookies;
    const keySignature = ConfigCookie.KEY.SIGNATURE;
    const { accessToken, profileToken } = decodeSignature({
      value: cookie?.[keySignature] || getCookie(keySignature, req?.headers.cookie) || ''
    }) || {
      accessToken: getCookie(ConfigCookie.KEY.ACCESS_TOKEN, req?.headers.cookie)
    };

    const anonymousToken = cookie?.[ConfigCookie.KEY.ANONYMOUS_TOKEN] || '';
    const token = accessToken || anonymousToken;
    // save Access Token
    await store.dispatch(setToken(token));

    const ipAddress = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '')
      .split(':')
      .pop();
    if (accessToken) {
      // Get profile
      await store.dispatch(
        getProfile({
          isMobile,
          isTablet,
          accessToken,
          profileToken,
          userAgent,
          ssr: true,
          ipAddress,
          deviceModel: deviceModel || getModelDevice(userAgent),
          deviceName: `${browserName}/${browserVersion}`,
          isSmartTvPlatform,
          isWebPSupported: isSafari ? Number.parseInt(browserVersion) >= 16 : true,
          deviceType: DEVICE_TYPE.DESKTOP
        })
      );
      if (profileToken) {
        await store.dispatch(
          getProfileSelected({
            accessToken,
            profileToken,
            ssr: true
          })
        );
      }
    }
    // Dispatch setDeviceInfo
    await store.dispatch(
      setDeviceInfo({
        isMobile,
        isTablet,
        isSafari,
        isIOS,
        isAndroid,
        isMobileOnly,
        isWindows,
        isMacOs,
        deviceModel: deviceModel || getModelDevice(userAgent),
        deviceName: `${browserName}/${browserVersion}`,
        isSmartTvPlatform,
        deviceType: DEVICE_TYPE.DESKTOP,
        userAgent
      })
    );
    // Under Construction
    let isUnderConstruction = false;

    if (!isViewApp) {
      await store
        .dispatch(
          getMenu({
            slug: pathname,
            accessToken,
            profileToken,
            isMobile,
            userAgent,
            ssr: true,
            ipAddress,
            origin
          })
        )
        .then((menuRes: any) => {
          const isMaintenance = (asPath || '').includes(PAGE.MAINTENANCE);
          isUnderConstruction = menuRes?.isUnderConstruction || asPath === PAGE.MAINTENANCE;
          if (menuRes?.isUnderConstruction && !isMaintenance) {
            res.writeHead(307, { Location: PAGE.MAINTENANCE }).end();
          }
        });
    }
    // GeoCheck
    await store
      .dispatch(
        getGeoCheck({
          ipAddress,
          ssr: true,
          userAgent,
          globalHost
        })
      )
      .then(async (res: any) => {
        // get web config
        await store.dispatch(getWebConfig({ globalHost })).then((confRes: any) => {
          const codeGuideUrl = confRes?.codeGuideUrl?.link || '';
          const isCodeGuild = asPath === PAGE.CODE_GUIDE;
          if (codeGuideUrl && isCodeGuild) {
            res.writeHead(307, { Location: codeGuideUrl }).end();
          }
        });
      });

    return {
      ssr: true,
      isUnderConstruction
    };
  }
  return {
    ssr: false,
    isUnderConstruction: false
  };
};

export default LayoutContainer;
