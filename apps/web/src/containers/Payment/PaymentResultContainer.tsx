import React, { useEffect, useMemo, useState } from 'react';
import Head from 'next/head';
import { useDispatch, useSelector } from 'react-redux';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import find from 'lodash/find';
import { useVieRouter } from '@customHook';
import Failed from '@components/payment/result/Failed';
import Process from '@components/payment/result/Process';
import Success from '@components/payment/result/Success';
import Icon from '@components/basic/Icon/Icon';
import Button from '@components/basic/Buttons/Button';
import { setLoadedData, setLoading } from '@actions/app';
import {
  tvodCheckTransaction,
  pvodCheckTransaction,
  getTemporaryData,
  getPaymentConfig,
  checkZaloPayTransaction,
  checkMocaTransaction,
  checkVnPayTransaction,
  checkAsiaPayTransaction,
  setSelectedMethod,
  getPackages,
  selectTerm
} from '@actions/payment';
import { getProfile } from '@actions/profile';
import * as NapasAction from '@actions/napas';
import * as MomoAction from '@actions/momo';
import * as MocaAction from '@actions/moca';
import * as ShopeePayAction from '@actions/shopeepay';
import * as ViettelPayAction from '@actions/viettelPay';
import PaymentApi from '@apis/Payment';
import TrackingPayment from '@tracking/functions/payment';
import { createTimeout, handleScrollTop } from '@helpers/common';
import {
  CHECK_STATUS_TIMEOUT,
  CHECK_STATUS_TIMER,
  KEY_NAPAS_RESULT_WEB,
  PAGE,
  PAID_TYPE,
  PAYMENT_METHOD,
  PAYMENT_TYPE,
  PLATFORM,
  STATUS_TRANSACTION
} from '@constants/constants';
import { DOMAIN_WEB } from '@config/ConfigEnv';
import ConfigSeo from '@config/ConfigSeo';
import ConfigCookie from '@config/ConfigCookie';
import { TEXT } from '@constants/text';
import TVodSuccess from '@components/payment/result/TVodSuccess';
import { isMobile } from 'react-device-detect';
import LocalStorage from '@config/LocalStorage';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import PVodSuccess from '@components/payment/result/PVodSuccess';

const trackingPayment = new TrackingPayment();
let checkTransactionTimeout: any = 0;
let checkTransactionTimer: any = 0;

const PaymentResultContainer = ({ pageProps }: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const {
    packages,
    listMethodsConfig,
    transactionResult,
    selectedMethod,
    selectedTerm,
    dataTemporary,
    geoCheck
  } = useSelector((state: any) => state?.Payment);
  const { token, deviceModel, deviceName, deviceType } = useSelector((state: any) => state?.App);

  const isGlobal = geoCheck?.isGlobal;

  const [isTimeout, setIsTimeout] = useState(false);
  const [billingOverlap, setBillOverlap] = useState<any>(null);
  const methodRequest = useMemo(() => pageProps?.methodRequest, [pageProps]);
  const napasResult = useMemo(() => pageProps?.napasResult, [pageProps]);
  const txnRef = useMemo(() => transactionResult?.txnRef || '', [transactionResult]);
  const { query, pathname, asPath } = router || {};

  const {
    // https://local.vieon.vn/mua-goi/ket-qua/?type=4&id=6ddcaf86-0795-446a-b4ba-f9afc50c25b7&vnp_Amount=2200000&vnp_BankCode=NCB&vnp_BankTranNo=VNP********&vnp_CardType=ATM&vnp_OrderInfo=thanh%20toan%20goi%20mua%20pvod%20ung%20ho%20khanh&vnp_PayDate=**************&vnp_ResponseCode=00&vnp_TmnCode=DZONES01&vnp_TransactionNo=********&vnp_TxnRef=****************&vnp_SecureHashType=SHA256&vnp_SecureHash=9be7186d711c09cf644667a386b945c00efd2ba95b234867f4db4471281bce07
    vnp_TxnRef,
    tnxId,
    Ref,
    mcOrderId,
    apptransid,
    binding_id,
    status: statusParam,
    orderId,
    method,
    state: stateMoca,
    confirmed: confirmedTransactionMoca,
    code: codeMoca,
    error: errorMoca,
    error_description: errorDescriptionMoca,
    order_id: vpOrderId,
    error_code: errorCodeVp,
    merchant_code: merchantCodeVp,
    payment_status: paymentStatusVp,
    trans_amount: transAmountVp,
    check_sum: checkSumVp,
    billcode: billCodeVp,
    cust_msisdn: custMsisdnVp,
    reference_id: referenceId
  } = query || {};

  const methodMomo = method === PAYMENT_METHOD.MOMO;
  const methodShopeePay = method === PAYMENT_METHOD.SHOPEE_PAY;
  const sppOrderId = query?.sppOrderId || referenceId || '';

  const supportSmartTv = router?.query?.from === PLATFORM.SMART_TV;
  const zlOrderId = useMemo(() => {
    let zlOrderIdTemp = router?.query?.zlOrderId || apptransid || '';
    const zlOderIdLocal = ConfigLocalStorage.get(LocalStorage.ZL_ORDER_ID);
    if (binding_id && statusParam === '1' && !zlOrderIdTemp) {
      zlOrderIdTemp = zlOderIdLocal;
    }
    return zlOrderIdTemp;
  }, [router, binding_id, statusParam, apptransid]);
  const transId = useMemo(() => {
    let orderIdTemp = '';
    orderIdTemp =
      vnp_TxnRef ||
      Ref ||
      mcOrderId ||
      zlOrderId ||
      orderId ||
      stateMoca ||
      vpOrderId ||
      sppOrderId ||
      '';
    if (!orderIdTemp) {
      orderIdTemp = txnRef;
    }
    return orderIdTemp;
  }, [txnRef, vnp_TxnRef, Ref, mcOrderId, zlOrderId, orderId, stateMoca, vpOrderId, sppOrderId]);

  const inAppZalo = (pathname || '').includes(PAGE.ZALOPAY);
  const {
    status,
    amount,
    expiryDate,
    packageName,
    effectiveDate,
    recurring,
    displayDuration,
    message,
    paymentType
  } = transactionResult || {};
  const { name, tel } = selectedMethod || {};
  const { paidMessageNeed, paidMessage, primaryButtonMessage, paidMessageType } =
    billingOverlap || {};
  const bankCode = query?.vnp_BankCode || null;

  useEffect(() => {
    dispatch(getPaymentConfig({ isGlobal }));
    dispatch(getPackages({}));
  }, []);

  useEffect(() => {
    if (transId && profile?.id) {
      dispatch(getTemporaryData(transId));
    }
  }, [transId, profile?.id]);

  useEffect(() => {
    if (dataTemporary?.pkg && dataTemporary?.term && !isEmpty(packages)) {
      const pkgSelected = find(packages, ['id', parseInt(dataTemporary?.pkg)]);
      if (!isEmpty(pkgSelected)) {
        const termSelected = find(get(pkgSelected, 'terms', []), [
          'id',
          parseInt(dataTemporary?.term)
        ]);
        if (!isEmpty(termSelected)) {
          dispatch(selectTerm(termSelected));
        }
      }
    }
  }, [dataTemporary, packages]);

  useEffect(() => {
    if (dataTemporary?.method && !isEmpty(listMethodsConfig)) {
      const methodSelected = find(listMethodsConfig, ['id', dataTemporary?.method]);
      if (!isEmpty(methodSelected)) {
        dispatch(setSelectedMethod(methodSelected));
      }
    }
  }, [dataTemporary, listMethodsConfig]);
  useEffect(() => {
    if (token) {
      dispatch(setLoadedData(true));
      dispatch(setLoading(true));
      checkTransaction();
      handleScrollTop();
      return () => {
        clearTimeout(checkTransactionTimer);
      };
    }
    return;
  }, [profile?.id, token]);

  useEffect(() => {
    if (stateMoca && !confirmedTransactionMoca) {
      const redirectUri =
        typeof window !== 'undefined' ? window.location.href : `${DOMAIN_WEB}${asPath}`;
      dispatch(
        MocaAction.getResultTransaction({
          state: stateMoca,
          code: codeMoca,
          redirectUri,
          error: errorMoca,
          errorDescription: errorDescriptionMoca,
          router
        })
      );
    }
  }, [stateMoca, confirmedTransactionMoca]);

  useEffect(() => {
    if (vpOrderId) {
      dispatch(
        ViettelPayAction.getResultTransaction({
          vpOrderId,
          router,
          errorCodeVp,
          merchantCodeVp,
          paymentStatusVp: parseInt(paymentStatusVp) || 0,
          transAmountVp,
          checkSumVp,
          billCodeVp,
          custMsisdnVp
        })
      );
    }
  }, [vpOrderId]);

  useEffect(() => {
    const napasResultCookie = ConfigCookie.load(KEY_NAPAS_RESULT_WEB);
    if (napasResultCookie && orderId && methodRequest !== 'POST') {
      const dataResult = JSON.parse(JSON.stringify(napasResultCookie));
      dispatch(
        NapasAction.getResultTransaction({
          ...dataResult,
          orderId
        })
      );
    }
  }, [orderId, methodRequest]);

  useEffect(() => {
    if (orderId && napasResult && methodRequest === 'POST') {
      const dataResult = JSON.parse(napasResult);
      ConfigCookie.save(
        KEY_NAPAS_RESULT_WEB,
        JSON.stringify({
          ...dataResult,
          orderId
        })
      );
      dispatch(
        NapasAction.getResultTransaction({
          ...dataResult,
          orderId
        })
      );
    }
  }, [methodRequest, napasResult, orderId]);

  useEffect(() => {
    if (isTimeout) {
      dispatch(setLoading(false));
    }
  }, [isTimeout]);

  useEffect(() => {
    if (token) {
      if (Object.keys(transactionResult || {}).length > 0) {
        dispatch(setLoading(false));
      }
      if (transactionResult?.status === STATUS_TRANSACTION.PROCESS) {
        if (checkTransactionTimeout > CHECK_STATUS_TIMEOUT) {
          setIsTimeout(true);
        } else {
          checkTransactionTimer = createTimeout(() => {
            checkTransaction();
          }, CHECK_STATUS_TIMER);
        }
      }
    }
  }, [transactionResult, token]);

  useEffect(() => {
    if (
      !isEmpty(transactionResult) &&
      (paymentType === PAYMENT_TYPE.TVOD || !isEmpty(selectedTerm)) &&
      !isEmpty(selectedMethod) &&
      profile?.id &&
      !isEmpty(dataTemporary)
    ) {
      const paramsTracking = {
        transaction: transactionResult,
        isSuccess: transactionResult?.status === STATUS_TRANSACTION.SUCCESS,
        selectedTerm,
        selectedMethod,
        profile,
        bankCode,
        query: dataTemporary,
        transactionStatus:
          transactionResult?.status === STATUS_TRANSACTION.SUCCESS
            ? 'success'
            : status === STATUS_TRANSACTION.PROCESS && !isTimeout
            ? 'pending'
            : 'failed'
      };
      if (transactionResult?.status === STATUS_TRANSACTION.SUCCESS) {
        trackingPayment.orderCompleted(paramsTracking);
      } else if (transactionResult?.status !== STATUS_TRANSACTION.PROCESS) {
        trackingPayment.orderFailed(paramsTracking);
      }
    }
  }, [transactionResult, selectedTerm, selectedMethod, profile?.id, dataTemporary]);

  useEffect(() => {
    if (transactionResult?.status === STATUS_TRANSACTION.SUCCESS && profile?.id) {
      dispatch(
        getProfile({
          deviceModel,
          deviceName,
          deviceType
        })
      );
      handleCheckOverlap(transactionResult);
    }
  }, [transactionResult, profile?.id]);

  const handleCheckOverlap = async (transData: any) => {
    const billingPermission = await PaymentApi.checkBillingPermission2({
      termId: transData?.termId,
      isSuccessScreen: true
    });
    setBillOverlap(billingPermission);
  };
  const checkTransaction = async () => {
    checkTransactionTimeout += CHECK_STATUS_TIMER;

    // TODO: [FOREST] to do update flow for pvod, waiting for API form BE
    if (
      transactionResult?.paymentType !== PAYMENT_TYPE.SVOD &&
      transactionResult?.paymentType !== PAYMENT_TYPE.TVOD
    ) {
      dispatch(pvodCheckTransaction({ orderId: transId }));
    } else if (transactionResult?.paymentType === PAYMENT_TYPE.TVOD) {
      dispatch(tvodCheckTransaction({ orderId: transId }));
    } else if (vnp_TxnRef) {
      dispatch(checkVnPayTransaction({ orderId: vnp_TxnRef }));
    } else if (Ref) {
      dispatch(checkAsiaPayTransaction({ orderId: Ref }));
    } else if (mcOrderId) {
      dispatch(
        checkMocaTransaction({
          mcOrderId,
          tnxId
        })
      );
    } else if (zlOrderId) {
      dispatch(checkZaloPayTransaction({ zlOrderId }));
    } else if (orderId) {
      if (methodMomo) {
        dispatch(MomoAction.getStatusTransaction({ orderId }));
      } else {
        dispatch(NapasAction.getStatusTransaction({ orderId }));
      }
    } else if (stateMoca) {
      dispatch(MocaAction.getStatusTransaction({ orderId: stateMoca }));
    } else if (vpOrderId) {
      dispatch(ViettelPayAction.getStatusTransaction({ orderId: vpOrderId }));
    } else if (sppOrderId) {
      dispatch(ShopeePayAction.getStatusTransaction({ orderId: sppOrderId }));
    }
  };

  const onClickButtonMessage = () => {
    router.push({ pathname: PAGE.PROFILE_SLUG }, { pathname: PAGE.PROFILE_PURCHASED });
  };

  const onClickAccount = () => {
    router.push({ pathname: PAGE.PROFILE_SLUG }, { pathname: PAGE.PROFILE_PURCHASED });
  };

  const renderPaidMessage = () => {
    if (inAppZalo) return null;
    if (status === STATUS_TRANSACTION.SUCCESS && paidMessageNeed) {
      return (
        <div className="badges badges--usp">
          <div className="badges-container grid-x medium-grid-margin-x canal-v medium-justify-content-center">
            <div className="grid-x align-middle">
              <div className="cell shrink">
                <Icon
                  iClass="vie-info-o-c-script text-green"
                  spClass={`${isMobile ? 'icon--tiny' : 'icon--small'}`}
                />
              </div>
              <div className="cell auto">
                <div className="text text-12 text-large-up-16 padding-small-up-left-8 padding-large-up-left-12 ">
                  <span className="text-medium line-height-1">{paidMessage}</span>
                  {paidMessageType === PAID_TYPE.ACCUMULATE && (
                    <>
                      Chi tiết xem tại
                      <span
                        className="under-line"
                        style={{ cursor: 'pointer' }}
                        onClick={onClickAccount}
                      >
                        {`${TEXT.ACCOUNT}/${TEXT.ACCOUNT_SETTING}/${TEXT.PURCHASED_SERVICES}`}
                      </span>
                    </>
                  )}
                </div>
              </div>
              <div className="cell shrink">
                {paidMessageType === PAID_TYPE.OVERLAP && primaryButtonMessage && (
                  <Button
                    className="button hollow m-l2 button--medium "
                    textClass="text-black"
                    title={primaryButtonMessage}
                    onClick={onClickButtonMessage}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  const renderSuccess = () => {
    if (status === STATUS_TRANSACTION.SUCCESS) {
      // TODO: [FOREST] to do check flow for pvod, waiting for API form BE
      if (paymentType === PAYMENT_TYPE.PVOD) {
        return (
          <PVodSuccess
            router={router}
            profile={profile}
            supportSmartTv={supportSmartTv}
            transactionResult={transactionResult}
          />
        );
      }
      if (paymentType === PAYMENT_TYPE.TVOD) {
        return (
          <TVodSuccess
            router={router}
            profile={profile}
            supportSmartTv={supportSmartTv}
            transactionResult={transactionResult}
          />
        );
      }
      return (
        <Success
          router={router}
          profile={profile}
          inAppZalo={inAppZalo}
          orderId={transId}
          txnRef={txnRef}
          methodMomo={methodMomo}
          methodShopeePay={methodShopeePay}
          packageName={packageName}
          displayDuration={displayDuration}
          recurring={recurring}
          expiryDate={expiryDate}
          effectiveDate={effectiveDate}
          amount={amount}
        />
      );
    }
    return null;
  };
  const renderFailed = () => {
    if (
      (!isEmpty(transactionResult) &&
        status !== STATUS_TRANSACTION.PROCESS &&
        status !== STATUS_TRANSACTION.SUCCESS) ||
      isTimeout
    ) {
      return (
        <Failed
          router={router}
          pkgId={dataTemporary?.pkg}
          inAppZalo={inAppZalo}
          supportSmartTv={supportSmartTv}
          orderId={transId}
          txnRef={txnRef}
          methodMomo={methodMomo}
          methodShopeePay={methodShopeePay}
          message={message}
          name={name}
          tel={tel}
          isPvod={paymentType === PAYMENT_TYPE.PVOD}
        />
      );
    }
    return null;
  };
  const renderProcess = () => {
    if (isEmpty(transactionResult) || (status === STATUS_TRANSACTION.PROCESS && !isTimeout)) {
      return (
        <Process
          router={router}
          inAppZalo={inAppZalo}
          orderId={transId}
          name={name}
          tel={tel}
          txnRef={txnRef}
          methodMomo={methodMomo}
          methodShopeePay={methodShopeePay}
        />
      );
    }
    return null;
  };

  return (
    <>
      <Head>
        <title>{ConfigSeo.TITLE.PAYMENT}</title>
        <link rel="shortcut icon" href={ConfigSeo.seoDefault.shortcut} />
      </Head>
      <section className="section section--payment section--payment-result !py-4 md:!py-6 overflow p-t">
        {renderPaidMessage()}
        <div className="container canal-v p-t3">
          <div className="section__body">
            {renderSuccess()}
            {renderFailed()}
            {renderProcess()}
          </div>
        </div>
      </section>
    </>
  );
};

export default PaymentResultContainer;
