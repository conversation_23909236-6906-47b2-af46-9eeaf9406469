import React, { useEffect, useMemo, useState } from 'react';
import isEmpty from 'lodash/isEmpty';
import find from 'lodash/find';
import { useVieRouter } from '@customHook';
import { useDispatch, useSelector } from 'react-redux';
import Step1 from '@components/payment/Step1/Step1';
import Step2 from '@components/payment/Step2/Step2';
import { getSEOAllPage } from '@actions/page';
import {
  checkFirstPay,
  getCampaign,
  getPackages,
  getPaymentConfig,
  getPVodInfo,
  getPVodOffer,
  getTVodInfo,
  getTVodOffer,
  selectPackage
} from '@actions/payment';
import { PAGE, POPUP, SEO_PAGES } from '@constants/constants';
import { getListPackageDiscount } from '@services/paymentServices';
import SeoAllPage from '@components/seo/SeoAllPage';
import LocationNotes from '@components/payment/LocationNotes';
import { openPopup } from '@actions/popup';
import { ACTION_TYPE, createAction } from '@actions/actionType';
import { Term } from '@models/subModels';
import Step1Global from '@components/payment/Step1/Step1Global';
import Step2Global from '@components/payment/Step2/Step2Global';
import PaymentApi from '@apis/Payment';
import Breadcrumb from '@components/payment/breadcrump';
import FooterRecommend from '@components/payment/footer-recommend';
import GlobalRetrictBody from '@/components/basic/GlobalRetrictBody';
import { paymentExit } from '@tracking/functions/TrackingRevisePayment';

const PaymentContainer = ({ isVoucher }: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const { isMobileViewPort, isMobile } = useSelector((state: any) => state?.App || {});
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const { isGlobal, restricted } = useSelector((state: any) => state?.App?.geoCheck);
  const { currentProfile } = useSelector((state: any) => state?.MultiProfile || {});
  const dataSEOAllPage = useSelector((state: any) => state?.Page?.dataSEOAllPage || {});
  const { packages, tvodInfo, pvodInfo } = useSelector((state: any) => state?.Payment);
  const [alreadyHave, setAlreadyHave] = useState<any>(null);
  //
  const dataSegment = useSelector((state: any) => state?.User?.dataSegment || {});
  // check URL
  const { pathname, query } = router || {};
  const { type, id, isSimulcast, isLiveEvent } = query || {};
  const pkgParam = query?.pkg || null;
  const isInAppZalo = useMemo(() => (pathname || '').includes(PAGE.ZALOPAY), [pathname]);
  const isRentalContent = useMemo(() => (pathname || '').includes(PAGE.RENTAL_CONTENT), [pathname]);
  const isPvodContent = useMemo(() => (pathname || '').includes(PAGE.PVOD_CONTENT), [pathname]);
  const packageGlobal = packages?.[0];
  const isNotShowLocationNote = useMemo(
    () =>
      pathname.includes(PAGE.PAYMENT_RESULT) ||
      isVoucher ||
      pathname.includes(PAGE.PAGE_SUPPORT_SMART_TV) ||
      pathname.includes(PAGE.ZALOPAY_RESULT) ||
      pathname.includes(PAGE.ZALOPAY_USAGE),
    [pathname, isVoucher]
  );

  useEffect(() => {
    if (currentProfile?.isKid) {
      dispatch(openPopup({ name: POPUP.NAME.KID_LIMITED_PAGE_DIALOG }));
    }
    dispatch(getPaymentConfig({ isGlobal }));
    dispatch(
      getSEOAllPage({
        slug: isRentalContent
          ? PAGE.RENTAL_CONTENT
          : isPvodContent
          ? PAGE.PVOD_CONTENT
          : `${PAGE.PAYMENT}/`,
        keyBreadcrumbs: SEO_PAGES.PAYMENT
      })
    );
  }, []);

  useEffect(() => {
    if (!isGlobal) {
      dispatch(getCampaign({ isMobile }));
    }
  }, [isGlobal]);

  useEffect(() => {
    if (profile?.id) {
      dispatch(checkFirstPay(profile?.id));
    }
  }, [profile?.id]);

  useEffect(() => {
    if (tvodInfo?.bizInfo?.tvodProductId) {
      dispatch(
        getTVodOffer({
          tvodProductId: tvodInfo.bizInfo.tvodProductId
        })
      );
    }
  }, [tvodInfo?.bizInfo?.tvodProductId]);

  useEffect(() => {
    if (pvodInfo?.bizInfo?.pvodProductId) {
      dispatch(
        getPVodOffer({
          pvodProductId: pvodInfo?.bizInfo?.pvodProductId
        })
      );
    }
  }, [pvodInfo?.bizInfo?.pvodProductId]);

  useEffect(() => {
    if (isRentalContent && type && id) {
      dispatch(
        getTVodInfo({
          contentId: id,
          contentType: +type,
          isSimulcast: isSimulcast === 'true',
          isLiveEvent: isLiveEvent === 'true'
        })
      );
    }
  }, [profile?.id, id, type, isRentalContent, isSimulcast, isLiveEvent]);

  useEffect(() => {
    if (isPvodContent && type && id) {
      dispatch(
        getPVodInfo({
          contentId: id,
          contentType: +type
        })
      );
    }
  }, [profile?.id, id, type, isPvodContent]);

  useEffect(() => {
    if (!isEmpty(packages) && !pkgParam) {
      if (pathname === PAGE.PAYMENT) {
        dispatch(selectPackage(packages?.[0]));
      }
      if (pathname === PAGE.PAYMENT_METHOD) {
        router.push(PAGE.PAYMENT);
      }
    }
  }, [pkgParam, packages, pathname, isMobileViewPort]);

  useEffect(
    () => () => {
      dispatch(createAction(ACTION_TYPE.GET_PACKAGE_DISCOUNT, []));
    },
    []
  );

  useEffect(() => {
    if (pkgParam && !isEmpty(packages)) {
      dispatch(selectPackage(find(packages, ['id', parseInt(pkgParam)])));
    }
  }, [pkgParam, packages]);

  useEffect(() => {
    handlePackageDiscount();
  }, [profile?.id]);

  useEffect(() => {
    if (profile?.id && isGlobal && packageGlobal?.terms?.[0]) {
      handleCheckOverlap();
    } else {
      setAlreadyHave(false);
    }
  }, [profile, isGlobal, packageGlobal]);

  /**
   * Tracking MoEngage Payment Exit
   */
  useEffect(() => {
    const currentPage = window?.location?.origin + window?.location?.pathname;
    return () => {
      paymentExit({ profile, currentPage });
    };
  }, []);

  const handleCheckOverlap = async () => {
    const firstTerm = packageGlobal?.terms?.[0];
    const permission2 = await PaymentApi.checkBillingPermission2({
      termId: firstTerm?.id
    });
    const { isCallback, buyPermission, success } = permission2 || {};

    if (!isCallback && !buyPermission && success) {
      setAlreadyHave(true);
    } else {
      setAlreadyHave(false);
    }
  };

  const handlePackageDiscount = async () => {
    try {
      const groupedPackages: any = await dispatch(getPackages({ isInAppZalo }));
      const discountRes = await PaymentApi.getPackageDiscount();
      const packageDiscount = getListPackageDiscount(discountRes?.data, isMobile);
      dispatch(createAction(ACTION_TYPE.GET_PACKAGE_DISCOUNT, packageDiscount));
      const newPackages = groupedPackages.map((pkg: any) => {
        const { terms = [] } = pkg;

        const firstPayTerms = terms.map((term: any) => {
          const matchingDiscountMain = packageDiscount?.find(
            (pkgD: any) =>
              pkgD?.type === 'CAMPAIGN_DISCOUNT' && pkgD?.detail?.origin_package_id === term?.id
          );

          const matchingDiscountNR = term?.noneRecurring?.id
            ? packageDiscount?.find(
                (pkgD: any) =>
                  pkgD?.type === 'CAMPAIGN_DISCOUNT' &&
                  pkgD?.detail?.origin_package_id === term.noneRecurring.id
              )
            : null;
          if (matchingDiscountMain || matchingDiscountNR) {
            const promoPackage = matchingDiscountMain?.detail?.promo_package?.id
              ? matchingDiscountMain?.detail?.promo_package
              : term || {};

            let newNoneRecurring = term?.noneRecurring;
            if (term?.noneRecurring && matchingDiscountNR) {
              newNoneRecurring = {
                ...term.noneRecurring,
                discount: true,
                discountTag:
                  matchingDiscountNR?.detail?.promo_package?.discount_tag || term?.discountTag,
                discountDesc:
                  matchingDiscountNR?.detail?.promo_package?.discount_desc || term?.discountDesc,
                price: matchingDiscountNR?.detail?.promo_package?.price || term?.price,
                oldPrice: matchingDiscountNR?.detail?.promo_package?.old_price || term?.oldPrice
              };
            }
            return Term({
              ...promoPackage,
              discount: Boolean(matchingDiscountMain?.detail?.promo_package),
              noneRecurring: newNoneRecurring,
              recurringData: term?.recurringData,
              discountTag: term?.discountTag,
              discountDesc: term?.discountDesc,
              promotionPrice: term?.promotionPrice,
              displayPrice: term?.displayPrice
            });
          }
          return term;
        });

        const matchingPkg = packageDiscount?.find(
          (pkgD: any) =>
            pkgD?.type === 'CAMPAIGN_DISCOUNT' &&
            Array.isArray(pkgD.packageGroups) &&
            pkgD.packageGroups.some((pkgGroup: any) => pkgGroup?.id === pkg?.id)
        );

        const filteredPackageGroups =
          matchingPkg?.packageGroups?.filter((pkgGroup: any) => pkgGroup?.id === pkg?.id) || [];

        const OEMTerms = (packageDiscount || [])
          .filter(
            (pkgD: any) =>
              pkgD?.type === 'PACKAGE_DISCOUNT' &&
              pkgD?.detail?.origin_package_id === pkg?.id &&
              !firstPayTerms.some((term: any) => term.id === pkgD?.detail?.promo_package?.id)
          )
          .map((pkgD: any) =>
            Term({
              ...pkgD.detail?.promo_package,
              discount: false
            })
          );

        return {
          ...pkg,
          ...(filteredPackageGroups.length > 0 ? { packageGroups: filteredPackageGroups } : {}),
          terms: [...OEMTerms, ...firstPayTerms],
          sub_name: ''
        };
      });
      dispatch(createAction(ACTION_TYPE.GET_PACKAGES, newPackages));
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      {isGlobal && restricted ? (
        <div className="flex justify-center md:pt-[80px] pb-[113px]">
          <GlobalRetrictBody isPayment />
        </div>
      ) : (
        <>
          {!isNotShowLocationNote && <LocationNotes />}
          {!isGlobal && <Breadcrumb isInAppZalo={isInAppZalo} />}
          {(pathname === PAGE.PAYMENT || pathname === PAGE.ZALOPAY) &&
            packages &&
            (isGlobal ? (
              alreadyHave !== null ? (
                <Step1Global alreadyHave={alreadyHave} />
              ) : null
            ) : (
              <Step1 />
            ))}
          {(pathname === PAGE.PAYMENT_METHOD ||
            pathname === PAGE.RENTAL_CONTENT ||
            pathname === PAGE.PVOD_CONTENT ||
            pathname === PAGE.ZALOPAY_METHOD) &&
            (isGlobal ? <Step2Global /> : <Step2 />)}
          <SeoAllPage {...dataSEOAllPage} />
          {!isGlobal && !dataSegment?.isPermission && <FooterRecommend />}
        </>
      )}
    </>
  );
};

PaymentContainer.getInitialProps = async ({ store, req }: any) => {
  if (req) {
    // get menu from store redux
    const pathname = req?._parsedOriginalUrl?.pathname;
    const isRentalContent = (pathname || '').includes(PAGE.RENTAL_CONTENT);
    const isPvodContent = (pathname || '').includes(PAGE.PVOD_CONTENT);

    await store.dispatch(
      getSEOAllPage({
        slug: isRentalContent ? '/mua-goi/tvod' : isPvodContent ? '/mua-goi/pvod' : '/mua-goi/',
        keyBreadcrumbs: SEO_PAGES.PAYMENT
      })
    );
    return { ssr: true };
  }
  return {};
};

export default PaymentContainer;
