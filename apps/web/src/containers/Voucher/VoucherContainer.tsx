import React, { useEffect, useMemo, useState } from 'react';
import ConfigSeo from '@config/ConfigSeo';
import Head from 'next/head';
import { useVieRouter } from '@customHook';
import Voucher from '@components/Voucher/Voucher';
import VoucherSuccess from '@components/Voucher/VoucherSuccess';
import VoucherTVodSuccess from '@components/Voucher/VoucherTVodSuccess';

const VoucherContainer = () => {
  const [voucher, setVoucher] = useState<any>(null);
  const router = useVieRouter();
  const { data, from, type } = router.query;
  const voucherDataFromPayment = useMemo(() => (data ? JSON.parse(data) : null), [data]);

  const onSetVoucher = (data: any) => {
    setVoucher(data);
  };

  useEffect(() => {
    if (from === 'payment' && voucherDataFromPayment && type === 'coupon_vieon') {
      setVoucher(voucherDataFromPayment);
      const { pathname, query } = router;
      const newQuery = { ...query };
      delete newQuery.from;
      delete newQuery.type;
      router.replace({ pathname, query: newQuery }, undefined, { shallow: true });
    }
  }, [from, voucherDataFromPayment]);

  return (
    <>
      <Head>
        <title>{ConfigSeo.TITLE.VOUCHER}</title>
        <link rel="shortcut icon" href={ConfigSeo.seoDefault.shortcut} />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      {!voucher ? (
        <Voucher onSetVoucher={onSetVoucher} />
      ) : voucher.isPackageTypeTVod ? (
        <VoucherTVodSuccess voucherInfo={voucher} />
      ) : (
        <VoucherSuccess voucherInfo={voucher} />
      )}
    </>
  );
};

export default VoucherContainer;
