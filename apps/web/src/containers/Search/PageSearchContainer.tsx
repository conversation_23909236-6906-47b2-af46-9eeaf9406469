/* eslint-disable react/destructuring-assignment */
import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { setLoading, setLoadedData } from '@actions/app';
import { getContentPopver } from '@actions/detail';
import { resetMenu } from '@actions/menu';
import { getSEOAllPage } from '@actions/page';

import {
  onFocusSearchBox,
  getSearchContent,
  getSearchHistory,
  postSearchHistory
} from '@actions/search';
import { ACTION_GET_LIST_TAGS_FILTER, getListTagsFilter } from '@actions/tags';
import Search from '@components/search/Search';
import NotFound from '@components/notfound/NotFound';
import { checkIsEndPage, getPathFromUrl } from '@helpers/common';
import { pageView } from '@tracking/functions/TrackingApp';
import { segmentEvent } from '@tracking/TrackingSegment';
import { NAME, PROPERTY } from '@config/ConfigSegment';
import { SEO_PAGES, PAGE } from '@constants/constants';
import SeoAllPage, { redirectTool } from '@components/seo/SeoAllPage';
import { ACTION_TYPE } from '@actions/actionType';

const stateInit = {
  keyword: null,
  isLoadMore: true,
  listDataAllPage: null,
  metadata: {
    page: 0,
    limit: 60,
    total: null
  },
  currentFilterId: null,
  dataTagsFilter: [],
  filterName: 'Tất cả nội dung'
};
const keyBreadcrumbs = SEO_PAGES.SEARCH;
class PageSearchContainer extends PureComponent {
  isLoaded: any;
  scrollTop: any;
  state = { ...stateInit };

  static async getInitialProps({ store, req, query, res }: any) {
    if (req) {
      // call in server
      const { q } = query || req.params;
      const { App } = store.getState();
      const isGlobal = App?.geoCheck?.isGlobal;
      const accessToken = App?.token;
      let renderEmptyPage;
      const keyword = q;
      const userAgent = req.headers['user-agent'];
      const ipAddress = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '')
        .split(':')
        .pop();

      if (keyword) {
        const { metadata, currentFilterId } = stateInit;
        const { page, limit } = metadata;

        await store
          .dispatch(getSEOAllPage({ keyword, keyBreadcrumbs, ssr: true, ipAddress, userAgent }))
          .then((resp: any) => redirectTool({ redirect: resp?.data?.redirect, res }));
        await store.dispatch(
          getSearchContent({
            keyword,
            page,
            limit,
            tags: currentFilterId,
            accessToken,
            ssr: true,
            ipAddress,
            userAgent,
            isGlobal
          })
        );
      } else {
        await store
          .dispatch(
            getSEOAllPage({
              slug: req.path,
              keyBreadcrumbs,
              ssr: true,
              ipAddress,
              userAgent
            })
          )
          .then((resp: any) => {
            if (resp?.data?.redirect?.http_status !== 200) {
              return redirectTool({ redirect: resp?.data?.redirect, res });
            }
            renderEmptyPage = true;
          });
      }

      return {
        renderEmptyPage,
        ssr: true
      };
    }
    return {};
  }

  componentDidMount = () => {
    // const { q } = this.props.router.query
    // const keyword = q
    const {
      dataSEOAllPage,
      pageProps,
      router,
      resetMenu,
      setLoading,
      setLoadedData,
      getSEOAllPage
    }: any = this.props;
    const keyword = this.keywordFromPath({ props: this.props });
    const { asPath }: any = router;

    this.fetchData(keyword, this.props);
    this.getListTagsFilter();
    resetMenu();

    let seoData = dataSEOAllPage;
    let params = {};
    if (pageProps?.renderEmptyPage) {
      params = { slug: asPath, keyBreadcrumbs };
    } else {
      params = { keyword, keyBreadcrumbs };
    }
    getSEOAllPage(params).then((data: any) => {
      if (data?.seo) {
        seoData = data?.seo;
      }
    });

    const seo = {
      url: seoData?.data?.seo?.url,
      title: `Tìm kiếm "${seoData?.data?.seo?.title}" - VieON`,
      description: seoData?.data?.seo?.description
    };
    //  GA PAGE VIEW
    pageView({ data: { seo } });

    window.addEventListener('scroll', this.handleScrollingAfterLoadMore);
    setLoadedData(true);
  };

  componentDidUpdate = (prevProps: any, prevState: any) => {
    let { metadata, currentFilterId, keyword, clickKeywordRecommended }: any = this.state;
    const { FOCUS_SEARCHBOX }: any = this.props;
    const keyFromRoute = this.keywordFromPath({ props: this.props });
    const PrevKeyFromRoute = this.keywordFromPath({ props: prevProps });
    const focusedSearchBox = FOCUS_SEARCHBOX?.focused;
    if (focusedSearchBox || !keyword) {
      // Change router => reset all
      keyword = keyFromRoute;
      clickKeywordRecommended = false;
    }
    // if search new keyword then reset state
    if (PrevKeyFromRoute !== keyFromRoute) {
      this.setState(
        (prevState) => ({
          ...prevState,
          keyword,
          metadata: stateInit.metadata,
          currentFilterId: stateInit.currentFilterId,
          filterName: 'Tất cả nội dung',
          clickKeywordRecommended
        }),
        () => {
          this.fetchData(keyword, this.props);
        }
      );
    } else if (
      // if change page or change filter => fetch data api
      (metadata?.page !== prevState?.metadata?.page && metadata?.total) ||
      currentFilterId !== prevState?.currentFilterId
    ) {
      this.fetchData(this.state.keyword, this.props);
    }
  };

  componentWillUnmount() {
    window.removeEventListener('scroll', this.handleScrollingAfterLoadMore);
  }
  keywordFromPath = ({ props }: any) => {
    const { q } = props?.router?.query || {};
    return q;
  };

  handleScrollingAfterLoadMore = (e: any) => {
    const scrollingElement = e?.target?.scrollingElement;
    if (this.isLoaded) {
      if (scrollingElement) {
        scrollingElement?.scrollTo(0, this.scrollTop);
      }
      this.isLoaded = false;
    }
  };

  onScrollDown = () => {
    const { isDisableLoadmore, metadata }: any = this.state;
    const { page, limit, total }: any = metadata;

    const { loading }: any = this.props;
    if (!!loading || isDisableLoadmore) return;
    if ((page + 1) * limit >= total) return;
    this.setState((prevState: any) => ({
      metadata: {
        // object that we want to update
        ...prevState.metadata, // keep all other key-value pairs
        page: page + 1 // update the value of specific key
      }
    }));
    this.setState({ page: (this.state as any)?.page || 0 + 1 });
  };

  onSelectedRelatedKeyword = ({ relKeyword, index }: any) => {
    const { router }: any = this.props;
    const { q } = router?.query;
    segmentEvent(NAME.RELATED_KEYWORD_SELECTED, {
      [PROPERTY.KEYWORD_POSITION]: index + 1,
      [PROPERTY.KEYWORD_NAME]: relKeyword || '',
      [PROPERTY.KEYWORD_INPUTTED]: q
    });
  };

  handleClickItem = ({ id, keyword, type, item, itemPosition }: any) => {
    segmentEvent(NAME.SELECT_SEARCH_RESULT, {
      [PROPERTY.KEYWORD]: keyword,
      [PROPERTY.CONTENT_TITLE]: item?.title,
      [PROPERTY.CONTENT_TYPE]: item?.type,
      [PROPERTY.CONTENT_POSITION]: itemPosition,
      [PROPERTY.CURRENT_PAGE]: window?.location?.href
    });
    const { getSearchHistory }: any = this.props;
    const position = item?.position || '';
    const request_id = item?.trackingData?.recommendation_id || '';

    postSearchHistory({ id, keyword, type, position, request_id });
    if (getSearchHistory) getSearchHistory();
  };

  getDataRender = (props?: any) => {
    let isEmptyItem = false;
    // nếu không truyền props thì lấy props hiện tại
    if (typeof props === 'undefined') {
      props = this.props;
    }

    // lấy slug từ query router
    const { q } = props.router.query;
    const keyword = q || this.keywordFromPath({ props });
    let {
      listDataAllPage,
      isLoadMore,
      currentFilterId,
      dataTagsFilter,
      clickKeywordRecommended
    }: any = this.state;
    const dataCurrentFilter =
      dataTagsFilter && currentFilterId
        ? (dataTagsFilter?.items || []).find((tag: any) => tag.id === currentFilterId)
        : {
            id: null,
            name: 'Tất cả nội dung'
          };

    listDataAllPage =
      listDataAllPage ||
      this.getDataFromRedux({
        props: this.props,
        keyword,
        currentFilterId: this.state.currentFilterId
      });
    const metadata = listDataAllPage?.[0]?.metadata || {};
    // const titlePage = (metadata?.total || 0) + ' KẾT QUẢ'
    const titlePage =
      metadata?.total > 0 && clickKeywordRecommended && keyword !== this.state.keyword
        ? `Kết quả liên quan đến: ${this.state.keyword || keyword}`
        : '';
    // const titlePage = metadata?.total > 0 ? `Tìm kiếm "${keyword}" có ${(metadata?.total || 0)} kết quả` : (metadata?.total || 0) + ' KẾT QUẢ'
    if (listDataAllPage && listDataAllPage[0]) {
      if (!listDataAllPage?.[0]?.items || listDataAllPage?.[0]?.items?.length === 0) {
        isEmptyItem = true;
      }
    }
    const seoData = this.getSeoData(listDataAllPage, getPathFromUrl(props.router.asPath));
    const { loading, isMobile }: any = this.props;

    return {
      listDataAllPage,
      keyword,
      isLoadMore,
      titlePage,
      dataTagsFilter,
      dataCurrentFilter,
      currentFilterId,
      isEmptyItem,
      seoData,
      loading,
      isMobile
    };
  };

  getSeoData = (dataPage: any, prefixPath: any) => {
    const { router, dataSEOAllPage }: any = this.props;
    const { q } = router.query;
    const keyword = q;
    dataPage =
      dataPage ||
      this.getDataFromRedux({
        props: this.props,
        keyword,
        currentFilterId: this.state.currentFilterId
      });
    const dataSeo = {
      page: PAGE.SEARCH,
      data: dataPage?.[0] || dataSEOAllPage || null,
      prefixPath,
      keyword
    };

    return dataSeo;
  };

  getListTagsFilter = () => {
    const { props }: any = this;
    // check exists data
    const dataListTagsFilter = props[ACTION_GET_LIST_TAGS_FILTER] || null;
    if (!dataListTagsFilter) {
      props.getListTagsFilter().then((res: any) => {
        if (res && res.payload) {
          this.setState({ dataTagsFilter: res.payload.data });
        }
      });
    } else {
      this.setState({ dataTagsFilter: dataListTagsFilter });
    }
  };

  fetchData = (key: any, props: any) => {
    if (!key) return;
    // const { q } = props.router.query
    const keyword = key || this.keywordFromPath({ props });
    let { metadata, listDataAllPage, currentFilterId }: any = this.state;
    const { page, limit } = metadata || {};
    // if(page > 0 ) return
    // check data from props
    let listDataPageNew = null;
    // this.props.setLoading(true)
    if (!props) props = this.props;
    if (listDataAllPage && page === 0) listDataAllPage = null;
    props
      .getSearchContent({
        keyword,
        page,
        limit,
        tags: currentFilterId,
        isGlobal: props?.geoCheck?.isGlobal
      })
      .then(async (res: any) => {
        // this.props.setLoading(false)
        if (res) {
          let isDisableLoadmore = false;
          if (!listDataAllPage) listDataAllPage = {};
          listDataPageNew = res.payload.data || [];
          metadata = listDataPageNew.metadata;
          listDataAllPage[page] = listDataPageNew;
          if (!listDataPageNew?.items?.length) isDisableLoadmore = true;
          // listDataAllPage = [...(data?.[keyword] || []), listDataPageNew]
          if (keyword && page === 0) {
            // SEGMENT TRACKING
            segmentEvent(NAME.SEARCH, {
              [PROPERTY.KEYWORD]: keyword,
              [PROPERTY.RESULT_NO]: res?.payload?.data?.metadata?.total || 0
            });
          }
          const seo = {
            url: res?.payload?.data?.seo?.url,
            title: `Tìm kiếm "${res?.payload?.data?.seo?.title}" - VieON`,
            description: res?.payload?.data?.seo?.description
          };
          //  GA PAGE VIEW
          pageView({ data: { seo } });
          // set state data metadata?.page !== prevState?.metadata?.page
          await this.setState({
            keyword,
            listDataAllPage,
            metadata,
            isDisableLoadmore,
            isLoadMore: !checkIsEndPage(metadata)
          });
        }
      });
  };
  getDataFromRedux = ({ props, keyword, currentFilterId, page }: any) => {
    const data = props[ACTION_TYPE.ACTION_GET_SEARCH_CONTENT];
    let listDataAllPage = null;

    if (!currentFilterId) {
      currentFilterId = -1; // default not filter tag
    }
    if (typeof page === 'undefined') {
      // get data all page
      listDataAllPage = data?.[keyword]?.[currentFilterId] || listDataAllPage;
    } else {
      // get data page from input
      listDataAllPage = data?.[keyword]?.[currentFilterId]?.[page] || listDataAllPage;
    }

    return listDataAllPage;
  };

  handleClickloadMoreData = (event: any) => {
    event.preventDefault();
    const { page, limit, total } = this.state.metadata;
    if (checkIsEndPage({ page, limit, total })) {
      this.setState({ isLoadMore: false });
      return;
    }
    // update new state page + 1
    this.setState((prevState: any) => ({
      metadata: {
        // object that we want to update
        ...prevState.metadata, // keep all other key-value pairs
        page: page + 1 // update the value of specific key
      }
    }));

    const html = document.getElementsByTagName('html');
    const scrollTop = html?.[0]?.scrollTop;
    this.scrollTop = scrollTop;
    this.isLoaded = true;
  };

  handleChangeFilter = (filterId: any, filterName: any) => {
    this.setState((prevState: any) => ({
      ...prevState,
      currentFilterId: filterId,
      metadata: stateInit.metadata,
      filterName
    }));
  };

  handleChangeKeyword = ({ keyword, clicked }: any) => {
    const props: any = this.props;
    const focusedSearchBox = props?.FOCUS_SEARCHBOX?.focused;
    const keywordFromRoute = this.keywordFromPath({ props });
    const keywordChanged = keyword || keywordFromRoute;
    const { isLoadedData } = props || {};
    if (focusedSearchBox) {
      props.onFocusSearchBox(false);
    }
    if (keyword === keywordFromRoute && !clicked) props.onFocusSearchBox(true);
    if (isLoadedData) {
      this.setState(
        (prevState: any) => ({
          ...prevState,
          keyword,
          clickKeywordRecommended: clicked || false,
          metadata: stateInit.metadata
        }),
        async () => {
          await this.fetchData(keywordChanged, props);
        }
      );
    }
  };

  render = () => {
    const props: any = this.props;
    const dataRender = this.getDataRender();
    const { listDataAllPage, loading, seoData } = dataRender;
    const { renderEmptyPage } = props || {};

    if (!listDataAllPage && !renderEmptyPage) {
      return null;
    }
    if (listDataAllPage?.length === 0 && !renderEmptyPage) {
      return <NotFound />;
    }
    const dataSEOAllPage = props?.dataSEOAllPage;
    return (
      <>
        <Search
          {...dataRender}
          emptyPage={renderEmptyPage}
          ribbonID={dataSEOAllPage?.seo?.ref_id}
          loadMoreData={this.handleClickloadMoreData}
          changeFilter={this.handleChangeFilter}
          handleClickItem={this.handleClickItem}
          onScrollDown={this.onScrollDown}
          onSelectedRelatedKeyword={this.onSelectedRelatedKeyword}
          filterName={this.state.filterName}
          currentFilterId={this.state.currentFilterId}
          loading={loading}
          handleChangeKeyword={this.handleChangeKeyword}
        />
        <SeoAllPage {...dataSEOAllPage} oldData={renderEmptyPage ? {} : seoData?.data?.seo} />
      </>
    );
  };
}

const mapStateToProps = ({ Search, Detail, Tags, Profile, App, Page }: any) => ({
  ...Detail,
  ...Tags,
  ...Search,
  ...(Profile || {}),
  ...(App || {}),
  ...(Page || {})
});

const mapDispatchToProps = (dispatch: any) => {
  const actions = bindActionCreators(
    {
      getSearchContent: getSearchContent,
      getSearchHistory: getSearchHistory,
      getContentPopver: getContentPopver,
      getListTagsFilter: getListTagsFilter,
      resetMenu: resetMenu,
      setLoading: setLoading,
      setLoadedData: setLoadedData,
      onFocusSearchBox: onFocusSearchBox,
      getSEOAllPage: getSEOAllPage
    },
    dispatch
  );
  return { ...actions, dispatch };
};

export default connect(mapStateToProps, mapDispatchToProps)(PageSearchContainer);
