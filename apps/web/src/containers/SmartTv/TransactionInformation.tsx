import React, { useEffect, useMemo, useState } from 'react';
import Head from 'next/head';
import { useVieRouter } from '@customHook';
import { useDispatch, useSelector } from 'react-redux';
import ErrorTransaction from '@components/payment/result/Error';
import { getInfoTransaction } from '@actions/napas';
import { EL_ID, PAGE, URL_SUPPORT_SMART_TV, CONTENT_TYPE, PLATFORM } from '@constants/constants';
import ConfigSeo from '@config/ConfigSeo';
import PaymentApi from '@apis/Payment';
import NapasCheckout from '@components/payment/Step2/checkoutFunctions/napas';
import { DOMAIN_WEB } from '@config/ConfigEnv';

const TransactionInformation = () => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const infoTransaction = useSelector((state: any) => state?.Payment?.infoTransaction);
  const token = useSelector((state: any) => state?.App?.token);
  const { pathname, query } = router || {};
  const { orderId, type, id } = query || {};
  const isRentalContent = useMemo(
    () => (pathname || '').includes(PAGE.SMART_TV_RENTAL_CONTENT),
    [pathname]
  );
  const [errorTransaction, setErrorTransaction] = useState(false);

  useEffect(() => {
    if (orderId && token) {
      if (isRentalContent) {
        handleTVodTransaction();
      } else {
        dispatch(
          getInfoTransaction({
            orderId,
            returnUrl: `${URL_SUPPORT_SMART_TV}/${orderId}/ket-qua?merchantId=VIEON`,
            router,
            isSmartTv: true
          })
        );
      }
    }
  }, [orderId, token]);

  const handleTVodTransaction = async () => {
    const returnUrl = `${DOMAIN_WEB}${
      PAGE.PAYMENT_RESULT
    }?orderId=${orderId}&merchantId=VIEON&from=${PLATFORM.SMART_TV}&type=${type}&id=${id}${
      parseInt(type) === CONTENT_TYPE.LIVESTREAM ? '&isLiveEvent=true' : ''
    }`;
    const tvodTransactionPay = await PaymentApi.tvodTransactionPay({ orderId, returnUrl });
    const transactionData: any = tvodTransactionPay?.napas || {};
    if (!tvodTransactionPay?.success || !transactionData?.orderId) {
      setErrorTransaction(true);
      return;
    }
    const napasCheckout = new NapasCheckout({
      returnUrl,
      router,
      isRentalContent,
      transactionData,
      isSmartTv: true,
      dispatch
    });
    await napasCheckout.handleNapas();
  };

  return (
    <>
      <Head>
        <title>{ConfigSeo.TITLE.PAYMENT}</title>
        <link rel="shortcut icon" href={ConfigSeo.seoDefault.shortcut} />
      </Head>
      <section className="section section--payment section--payment-result !py-4 md:!py-6 overflow">
        <div className="container canal-v">
          <div className="section__body">
            {(errorTransaction ||
              (Object.keys(infoTransaction || {}).length > 0 &&
                (!infoTransaction?.isSuccess || infoTransaction?.error_code !== 0))) && (
              <ErrorTransaction />
            )}
          </div>
        </div>
      </section>
      <div id={EL_ID.ID_ELM_NAPAS_PAYMENT} />
    </>
  );
};

export default TransactionInformation;
