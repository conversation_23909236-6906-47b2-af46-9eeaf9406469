import React, { Component } from 'react';
import { connect } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import { bindActionCreators } from 'redux';
import { setLoadedData, setToast } from '@actions/app';
import { clearEpisodeList, setEpisode, setNextEpisode } from '@actions/episode';
import { getSEOAllPage, setIsMasterBanner } from '@actions/page';
import { getContentConfig, getTriggerConfig } from '@actions/appConfig';
import { setSetting, setStatusFullscreen } from '@actions/player';
import * as PopupAction from '@actions/popup';
import { openPopup } from '@actions/popup';
import { getVodContent } from '@actions/vod';
import Detail from '@components/detail/Detail';
import {
  buildSlugSeoContentNew,
  checkIsFullscreen,
  checkNextEpisodeToWatch,
  createTimeout,
  decodeSignature,
  encodeParamDestination,
  getCookie,
  getPathFromUrl,
  onOpenPayment
} from '@helpers/common';
import LocalStorage from '@config/LocalStorage';
import DetailApi from '@apis/detailApi';
import ContentNotFound from '@components/notfound/ContentNotFound';
import Router from 'next/router';
import { pageView } from '@tracking/functions/TrackingApp';
import { NAME, VALUE } from '@config/ConfigSegment';
import { getParamsForSegment, handleGetSlugEpsFromPath } from '@services/detailServices';
import UserApi from '@apis/userApi';
import {
  CONTENT_TYPE,
  ERROR_CODE,
  HTTP_CODE,
  PAGE,
  PERMISSION,
  POPUP,
  SEO_PAGES
} from '@constants/constants';
import SeoText from '@components/seo/SeoText';
import SeoAllPage, { redirectTool } from '@components/seo/SeoAllPage';
import { svodTrialSubscribePackageButtonSelected } from '@tracking/functions/TrackingPaymentConversion';
import { TEXT } from '@constants/text';
import TrackingPlayer from '@tracking/functions/TrackingPlayer';
import { parsePopupParams } from '@services/popupServices';
import {
  clearRegisterConsultation,
  endSessionPlay,
  getContent,
  getContentDetailByID,
  getContentEpisode,
  getEpisodeListVod,
  getListComments,
  getRecommendVod,
  getRelatedVod,
  getRibbonDetailNotFound,
  refreshSessionPlay,
  resetContent,
  setSessionId
} from '@actions/detail';
import ConfigCookie from '@config/ConfigCookie';
import { checkSigmaLoaded, getInfoVideoCodec, setPlayerErrorLog } from '@services/playerServices';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import { UtmParams } from '@models/subModels';
import SigmaDRM from '@script/SigmaDRM';
import { SIGMA_DRM_WINDOWS } from '@config/ConfigEnv';
import { ERROR_PLAYER } from '@constants/player';
import TrackingPayment from '@tracking/functions/payment';
import PaymentApi from '@apis/Payment';
import { USER_TYPE as userType } from '@/constants/constants';

const trackingPayment = new TrackingPayment();

class DetailContainer extends Component {
  player: any = null;

  displayTimer: any = null;

  oldSessionToken: any = null;

  sessionToken: any = '';

  playerName = '';

  constructor(props: any) {
    super(props);
    this.dispatch = props.dispatch;
    const { isAndroid, isWindows, isMacOs, isSafari } = props || {};
    this.state = {
      asPath: props?.router.asPath,
      content: null,
      contentDetail: null,
      episodeData: null,
      episodeContent: null,
      contentSlugEpisode: '',
      contentSlug: '',
      currentEpisode: null,
      isLoadingContent: true,
      sigmaLoaded:
        isAndroid || (isWindows && SIGMA_DRM_WINDOWS) || (isMacOs && !isSafari)
          ? checkSigmaLoaded()
          : true
    };
  }

  static async getInitialProps({ store, req, query, res }: any) {
    const renderNotFoundDetail = async (accessToken: any, profileToken: any, isGlobal: any) => {
      const ribbonDetail = await getRibbonDetailNotFound({
        accessToken,
        profileToken,
        ssr: true,
        isGlobal
      });
      await store.dispatch(ribbonDetail);
    };
    if (req) {
      // call on server side
      const { App } = store.getState();
      const accessToken = App?.token;
      const isGlobal = App?.geoCheck?.isGlobal;
      const cookie = req?.cookies;
      const keySignature = ConfigCookie.KEY.SIGNATURE;
      const { profileToken } =
        decodeSignature({
          value: cookie?.[keySignature] || getCookie(keySignature, req?.headers.cookie) || ''
        }) || {};
      const { slug, eps, rel } = query;
      const episode = eps;
      const slugVideo = rel;
      const contentSlug = buildSlugSeoContentNew({ slug });
      const userAgent = req.headers['user-agent'];
      const origin = req.headers.host;

      const ipAddress =
        (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '').split(':').pop() ||
        '';
      const contentSlugEpisode = buildSlugSeoContentNew({
        slug,
        episode,
        slugVideo
      });
      let renderNotFoundPage = false;
      if (contentSlug) {
        const dataContent = await getContent({
          slug: contentSlug,
          accessToken,
          profileToken,
          ssr: true,
          ipAddress,
          userAgent,
          isGlobal,
          origin
        });
        let dataEpisode = null;
        if (episode || slugVideo) {
          dataEpisode = await getContentEpisode({
            slug: contentSlugEpisode,
            accessToken,
            profileToken,
            ssr: true,
            ipAddress,
            userAgent,
            isGlobal,
            origin
          });
        }
        // dispatch entity vod data
        let keyBreadcrumbs = SEO_PAGES.DETAIL;
        const slug = req.path || req.originalUrl;
        if (episode) keyBreadcrumbs = SEO_PAGES.DETAIL_EPISODE;
        if (slugVideo) keyBreadcrumbs = SEO_PAGES.DETAIL_RELATED;

        const dataSEO = await getSEOAllPage({
          slug,
          keyBreadcrumbs,
          ipAddress,
          userAgent,
          origin
        });
        await store.dispatch(dataSEO).then(async (resp: any) => {
          const redirect = resp?.data?.redirect;
          redirectTool({ redirect: resp?.data?.redirect, res });
          if (redirect?.http_status === HTTP_CODE.GONE) {
            res.status(410);
            renderNotFoundPage = true;
          }
          await store.dispatch(dataContent).then(async (response: any) => {
            if (response.httpCode === 400) {
              renderNotFoundPage = true;
              await renderNotFoundDetail(accessToken, profileToken, isGlobal);
              if (redirect?.http_status === HTTP_CODE.OK_200) res.status(404);
            } else {
              // force get related items shop
              const dataRecommend = await getRecommendVod({
                slug: contentSlug,
                accessToken,
                profileToken,
                ssr: true,
                ipAddress,
                userAgent,
                isGlobal,
                origin
              });
              const dataRelated = await getRelatedVod({
                slug: contentSlug,
                accessToken,
                profileToken,
                ssr: true,
                ipAddress,
                userAgent,
                isGlobal,
                origin
              });
              const dataComment = await getListComments({
                contentId: response?.data?.id,
                page: 0,
                limit: 20,
                ipAddress,
                userAgent,
                isGlobal,
                origin
              });
              if (response?.data) {
                if (
                  response?.data?.type === CONTENT_TYPE.SEASON ||
                  response?.data?.type === CONTENT_TYPE.EPISODE
                ) {
                  const dataEpisodeList = await getEpisodeListVod({
                    slug: contentSlug,
                    accessToken,
                    profileToken,
                    ssr: true,
                    ipAddress,
                    userAgent,
                    isGlobal,
                    origin
                  });
                  await store.dispatch(dataEpisodeList);
                }
              }
              await store.dispatch(dataComment);
              await store.dispatch(dataRelated);
              await store.dispatch(dataRecommend);
            }
          });
        });

        if (dataEpisode) {
          await store.dispatch(dataEpisode).then(async (response: any) => {
            if (response?.payload?.httpCode === 400) {
              renderNotFoundPage = true;
              await renderNotFoundDetail(accessToken, profileToken, isGlobal);
              res.status(410);
            }
          });
        }
      } else {
        renderNotFoundPage = true;
        await renderNotFoundDetail(accessToken, profileToken, isGlobal);
      }
      return { renderNotFoundPage, ssr: true };
    }
    return {};
  }

  componentDidMount() {
    const {
      getContentConfig,
      getTriggerConfig,
      setLoadedData,
      setIsMasterBanner,
      GET_CONTENT,
      router,
      setToast,
      geoCheck,
      concurrentScreen,
      dataRefreshSession,
      endSessionPlay
    }: any = this.props;

    const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
    getContentConfig();
    getTriggerConfig();
    setLoadedData(true);
    setIsMasterBanner(true);
    window.scrollTo(0, 0);
    const ssrContent = GET_CONTENT;
    const {
      asPath,
      query: { recommendation_id, recommendation_type }
    } = router;
    const { slugFromPath, episodeFromPath, relatedFromPath } = handleGetSlugEpsFromPath({
      path: asPath
    });
    const episode = episodeFromPath;
    const slug = slugFromPath;
    const slugVideo = relatedFromPath;
    const contentSlug = buildSlugSeoContentNew({ slug });
    const contentSlugEpisode = buildSlugSeoContentNew({ slug, episode, slugVideo });
    if (sessionToken) endSessionPlay(sessionToken);
    if (contentSlug) {
      this.handleChangeContent({
        ssrContent,
        contentSlug,
        contentSlugEpisode,
        episode,
        slugVideo,
        recommendation_id,
        recommendation_type
      });
    } else {
      getRibbonDetailNotFound({ isGlobal: geoCheck?.isGlobal });
      this.handleNotFoundClientPage();
    }
    const enjoyTrial = ConfigLocalStorage.get(LocalStorage.ENJOY_TRIAL);
    if (enjoyTrial) {
      setToast({ message: TEXT.TRIAL_ENJOY });
      ConfigLocalStorage.remove(LocalStorage.ENJOY_TRIAL);
    }
    if (this.prop?.dataSEOAllPage?.seo?.slug !== asPath) {
      this.onGetDataSEO({ episode, slugVideo, asPath, keyBreadcrumbs: SEO_PAGES.DETAIL });
    }

    window.addEventListener('beforeunload', this.onBrowserChange);
  }

  static getDerivedStateFromProps = (props: any, state: any) => {
    const { asPath } = props.router;
    const { slugFromPath, episodeFromPath, relatedFromPath } = handleGetSlugEpsFromPath({
      path: asPath
    });
    const slug = slugFromPath;
    const episode = episodeFromPath;
    const slugVideo = relatedFromPath;
    const contentSlug = buildSlugSeoContentNew({ slug });
    const contentSlugEpisode = buildSlugSeoContentNew({ slug, episode, slugVideo });
    if (contentSlug !== state.contentSlug) {
      return { contentSlug, contentSlugEpisode };
    }
    if (contentSlugEpisode !== state.contentSlugEpisode) {
      return { contentSlugEpisode };
    }

    return null;
  };

  changeName: any;
  dispatch: any;
  prop: any;
  sessionId: any;
  totalPlayedData: any;

  componentDidUpdate(prevProps: any, prevState: any) {
    const {
      dataSEOAllPage,
      token,
      router,
      openPopup,
      concurrentScreen,
      refreshSessionPlay,
      dataRefreshSession
    }: any = this.props;
    const isConcurrentScreen = concurrentScreen?.code === ERROR_CODE.CODE_0;
    const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
    const { contentSlug, contentSlugEpisode }: any = this.state;
    const { asPath } = router;
    const { slugFromPath, episodeFromPath, relatedFromPath } = handleGetSlugEpsFromPath({
      path: asPath
    });
    const episode = episodeFromPath;
    const slugVideo = relatedFromPath;
    if (slugFromPath !== prevState.contentSlug || token !== prevProps?.token) {
      this.handleChangeContent({ contentSlug, contentSlugEpisode, episode, slugVideo, token });
      this.handleSendTracking();
    } else if (contentSlugEpisode !== prevState.contentSlugEpisode) {
      this.handleChangeEpisode({ contentSlugEpisode, episode, slugVideo });
    }
    if (asPath !== (this.state as any)?.asPath) {
      const segmentParams = getParamsForSegment({
        ...this.state,
        sessionId: this.sessionId,
        totalPlayedData: this.totalPlayedData
      });
      TrackingPlayer.vodEvents(NAME.VIDEO_COMPLETED, segmentParams);

      openPopup();
      this.handleTracking();
      this.setState({ asPath });
      if (dataSEOAllPage?.seo?.slug !== asPath) {
        this.onGetDataSEO({ episode, slugVideo, asPath, keyBreadcrumbs: SEO_PAGES.DETAIL });
      }
    }
    if (isConcurrentScreen && sessionToken !== prevProps?.dataRefreshSession?.sessionToken) {
      const timer = dataRefreshSession?.countdownBySec || concurrentScreen?.countdownBySec;
      if (this.displayTimer) clearTimeout(this.displayTimer);
      this.displayTimer = setTimeout(() => {
        refreshSessionPlay(sessionToken).then((res: any) => {
          if (
            !res?.success ||
            res?.httpCode === HTTP_CODE.TOO_MANY_REQUEST ||
            res?.code === ERROR_CODE.CODE_3
          ) {
            this.handleEndSessionPlay(sessionToken);
            clearTimeout(this.displayTimer);
          }
        });
      }, timer * 1000);
    }
    if (this.sessionToken !== sessionToken) {
      this.sessionToken = sessionToken;
    }
  }

  componentWillUnmount() {
    const {
      resetContent,
      setSetting,
      requestLogin,
      setIsMasterBanner,
      clearRegisterConsultation,
      concurrentScreen,
      dataRefreshSession
    }: any = this.props;
    resetContent();
    setSetting();
    if (requestLogin) requestLogin(false);
    setIsMasterBanner(false);
    const segmentParams = getParamsForSegment({
      ...this.state,
      sessionId: this.sessionId,
      totalPlayedData: this.totalPlayedData
    });
    TrackingPlayer.videoCompleted(segmentParams);
    window.removeEventListener('beforeunload', this.onBrowserChange);
    ConfigLocalStorage.remove(LocalStorage.BACK_FROM_PLAYER);
    clearRegisterConsultation();
    if (this.displayTimer) clearTimeout(this.displayTimer);
    const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
    if (sessionToken) {
      this.handleEndSessionPlay(sessionToken);
    }
  }

  onGetDataSEO = ({ episode, slugVideo, asPath, keyBreadcrumbs }: any) => {
    // const breadcrumb = episode
    //   ? SEO_PAGES.DETAIL_EPISODE
    //   : slugVideo
    //   ? SEO_PAGES.DETAIL_RELATED
    //   : keyBreadcrumbs || SEO_PAGES.DETAIL;
    // this.props?.getSEOAllPage({ slug: asPath, keyBreadcrumbs: breadcrumb });
    const { getSEOAllPage }: any = this.props;
    const breadcrumb = episode
      ? SEO_PAGES.DETAIL_EPISODE
      : slugVideo
      ? SEO_PAGES.DETAIL_RELATED
      : keyBreadcrumbs || SEO_PAGES.DETAIL;
    getSEOAllPage({ slug: asPath, keyBreadcrumbs: breadcrumb });
  };

  onSigmaLoaded = () => {
    this.setState({ sigmaLoaded: true });
  };

  handleSetSessionId = (id: any) => {
    const { setSessionId }: any = this.props;
    if (setSessionId) setSessionId(id);
  };

  onBrowserChange = () => {
    const segmentParams = getParamsForSegment({
      ...this.state,
      sessionId: this.sessionId,
      totalPlayedData: this.totalPlayedData
    });
    TrackingPlayer.videoCompleted({ ...segmentParams, isTrackImmediately: true });
    ConfigLocalStorage.remove(LocalStorage.BACK_FROM_PLAYER);
    if (this.sessionToken) {
      this.handleEndSessionPlay(this.sessionToken);
    }
    this.handleTracking();
    return undefined;
  };

  handleTracking = () => {
    const { content, currentEpisode, contentDetail }: any = this.state;
    const contentId = contentDetail?.id;
    const permission = contentDetail?.permission;
    const trialDuration = contentDetail?.trialDuration;
    const triggerLoginDuration = contentDetail?.triggerLoginDuration;
    const playTrial =
      permission !== PERMISSION.CAN_WATCH && (trialDuration > 0 || triggerLoginDuration > 0);
    const contentName =
      content?.title + (currentEpisode?.title ? ` - ${currentEpisode?.title}` : '');
    const contentType = currentEpisode?.type || content?.type;
    let videoElement: any = document.getElementsByTagName('video');
    videoElement = videoElement?.[0];
    if (!videoElement) return;
    const data = [
      {
        action: 'offset',
        duration: parseInt(videoElement?.duration || 0),
        progress: parseInt(videoElement?.currentTime || 0),
        timestamp: Math.floor(new Date().getTime() / 1000)
      }
    ];
    const getInfoTrack = getInfoVideoCodec({ player: this.player, playerName: this.playerName });
    UserApi.trackingWatch({
      contentId,
      contentName,
      contentType,
      data,
      playTrial,
      videoCodec: getInfoTrack?.videoCodec
    });
  };

  handleSendTracking = () => {
    const { contentDetail, content, currentEpisode }: any = this.state;
    const video: any = document.getElementsByTagName('video')?.[0];
    if (!video) return;
    const permission = contentDetail?.permission;
    const trialDuration = contentDetail?.trialDuration;
    const triggerLoginDuration = contentDetail?.triggerLoginDuration;
    const playTrial =
      permission !== PERMISSION.CAN_WATCH && (trialDuration > 0 || triggerLoginDuration > 0);
    const getInfoTrack = getInfoVideoCodec({ player: this.player, playerName: this.playerName });
    const params: any = {
      contentId: contentDetail?.id,
      playTrial,
      contentName:
        (content?.title || '') + (currentEpisode?.title ? ` - ${currentEpisode?.title}` : ''),
      contentType: currentEpisode?.type || content?.type,
      videoCodec: getInfoTrack?.videoCodec,
      data: [
        {
          action: 'play',
          duration: parseInt(video?.duration),
          progress: parseInt(video?.currentTime),
          timestamp: Math.floor(new Date().getTime() / 1000)
        }
      ]
    };
    UserApi.trackingWatch(params);
  };

  handleGotoChapter = () => {
    const { nextEpisode }: any = this.props || {};
    if (nextEpisode?.seo?.url) {
      Router.push(PAGE.VOD, nextEpisode?.seo?.url);
    }
  };

  onControlPlay = ({ playerError }: any) => {
    const { router }: any = this.props || {};
    const { content, contentDetail }: any = this.state || {};
    const { permission, isPremiumTVod } = contentDetail || {};
    if (permission !== PERMISSION.CAN_WATCH && isPremiumTVod) {
      const utmParams = UtmParams(router?.query);
      router.push({
        pathname: PAGE.RENTAL_CONTENT,
        query: { ...utmParams, type: content.type, id: content?.id }
      });
    } else if (playerError) {
      this.handleRetry();
    } else {
      this.handleOpenPopup(contentDetail);
    }
  };

  onClickEpisode = (item: any) => {
    this.handleGotoChapter();
  };

  changeSeason = (item: any) => {
    const { router }: any = this.props;
    if (item?.seo_url) {
      router.push(PAGE.VOD, item?.seo_url);
    }
  };

  getDataRender = () => ({
    ...this.state,
    ...this.props
  });

  handleChangeEpisode = async ({ contentSlugEpisode, episode }: any) => {
    const { GET_CONTENT, setEpisode, geoCheck }: any = this.props;
    this.changeName = 'EPISODE';
    if (contentSlugEpisode) {
      const resEpisodeContent = await DetailApi.getContentBySlug({
        slug: contentSlugEpisode,
        isGlobal: geoCheck?.isGlobal
      });
      const currentEpisode = resEpisodeContent?.data;
      setEpisode(currentEpisode);
      const { content }: any = this.state;
      const contentId = GET_CONTENT?.id || content?.id;
      let episodeId = currentEpisode?.id;
      episodeId = episodeId === contentId ? currentEpisode?.defaultEpisode?.id : episodeId;
      // const content = { ...this.state.content };
      const playerTitle = `${content?.title} - ${currentEpisode?.title}`;
      this.setState({ currentEpisode, playerTitle });
      this.getContentDetail({
        contentId,
        episodeId,
        // content: this.state.content,
        content,
        currentEpisode,
        episode,
        isNotGoToPayment: true
      });
    }
  };

  handleNotFoundClientPage = (notFound?: any) => this.setState({ renderNotFoundPage: notFound });

  handleChangeContent = async ({
    ssrContent,
    contentSlug,
    contentSlugEpisode,
    episode,
    slugVideo,
    recommendation_id,
    recommendation_type
  }: any) => {
    const {
      getContent,
      setNextEpisode,
      clearEpisodeList,
      getVodContent,
      setEpisode,
      geoCheck
    }: any = this.props;
    this.changeName = 'CONTENT';
    if (!contentSlug && !contentSlugEpisode) return;
    const getInfoEpisodeData =
      episode || slugVideo
        ? DetailApi.getContentBySlug({
            slug: contentSlugEpisode,
            recommendation_id,
            recommendation_type,
            isGlobal: geoCheck?.isGlobal
          })
        : null;
    const [resEpisodeContent] = await Promise.all([getInfoEpisodeData]);
    this.setState({ isLoadingContent: true });
    const newContent = await getContent({
      slug: contentSlug,
      recommendation_id,
      recommendation_type,
      isGlobal: geoCheck?.isGlobal
    });
    const content = ssrContent?.id ? ssrContent : newContent?.data;
    this.handleNotFoundClientPage(!newContent.success);
    if (content?.type === CONTENT_TYPE.MOVIE) {
      setNextEpisode(null);
      clearEpisodeList();
    }
    getVodContent(content);
    const defaultEpisode = content?.defaultEpisode;
    const currentEpisode = resEpisodeContent?.data?.id ? resEpisodeContent?.data : defaultEpisode;
    setEpisode(currentEpisode);
    const contentId = content?.id;
    const episodeId = currentEpisode?.id || defaultEpisode?.id || '';
    let playerTitle = content?.title;
    const episodeTitle = currentEpisode?.title;
    if (episodeTitle) playerTitle += ` - ${episodeTitle}`;
    this.setState({
      content,
      currentEpisode,
      playerTitle,
      isLoadingContent: false
    });
    this.getContentDetail({
      contentId,
      episodeId,
      content,
      currentEpisode,
      episode,
      isNotGoToPayment: true
    });
  };

  handleEndSessionPlay = (value: any) => {
    const { endSessionPlay }: any = this.props;
    if (endSessionPlay) {
      endSessionPlay(value);
    }
  };

  getContentDetail = async ({
    contentId,
    episodeId,
    content,
    currentEpisode,
    episode,
    isNotGoToPayment
  }: any) => {
    const {
      router,
      pageProps,
      getContentDetailByID,
      subtitle,
      setSetting,
      openPopup,
      concurrentScreen,
      dataRefreshSession,
      profile,
      currentProfile,
      isMobile,
      geoCheck
    }: any = this.props;
    const isVideoIndexingDataByContentId =
      (!profile?.id || (profile?.id && !profile.isPremium && !currentProfile?.isKid)) &&
      !geoCheck?.isGlobal &&
      !isMobile;
    const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
    if (!contentId || pageProps?.renderNotFoundPage) return;
    if (sessionToken) {
      this.handleEndSessionPlay(sessionToken);
    }
    getContentDetailByID({ contentId, episodeId, content, isVideoIndexingDataByContentId }).then(
      (result: any) => {
        const res = result?.payload;
        setPlayerErrorLog({ isReset: true });
        if (res?.success) {
          const { rel } = router.query;
          const { relatedFromPath } = handleGetSlugEpsFromPath({ path: router.asPath });
          const isRelatedVod = !isEmpty(rel || relatedFromPath);
          if (res?.data) {
            if (res?.data?.linkplay) {
              TrackingPlayer.latency({ linkPlay: res?.data?.linkplay });
            } else if (
              res?.data?.permission === PERMISSION.CAN_WATCH &&
              !res?.data?.isConcurrentScreenLimit
            ) {
              if (navigator.onLine) {
                openPopup({
                  name: POPUP.NAME.PLAYER_ERROR_VOD,
                  errorData: {
                    vodData: { content, currentEpisode, contentDetail: res?.data },
                    errorType: ERROR_PLAYER.TYPE.EMPTY_LINK
                  }
                });
              }
            }
            if (subtitle && res?.data?.settingData) {
              res.data.settingData.subtitle = subtitle;
            }
            setSetting(res?.data?.settingData);
            const thumbs = res?.data?.thumbs;
            this.setState({ contentDetail: { ...res.data, isRelatedVod } });
            this.handleOpenPopup(res?.data, false, isNotGoToPayment, content);
            // SEGMENT TRACKING
            const vtt = thumbs?.vtt;
            const thumb = thumbs?.image;
            if (vtt) {
              DetailApi.getVtt(vtt).then((res) => {
                this.setState({ thumbData: res, thumbSrc: res && res.length > 0 ? thumb : '' });
              });
            }
          }
        } else if (navigator.onLine) {
          openPopup({
            name: POPUP.NAME.PLAYER_ERROR_VOD,
            errorData: {
              vodData: { content, currentEpisode, contentDetail: res?.data },
              errorType: ERROR_PLAYER.TYPE.API_DETAIL,
              detailData: res
            }
          });
        }
      }
    );
    const data = episode ? currentEpisode : content;
    pageView({ data: { seo: data?.seo } });
  };

  setupPlayer = (params: any) => {
    const { player, playerName } = params || {};
    this.player = player;
    this.playerName = playerName;
  };

  updateTotalPlayedData = (totalPlayedData: any) => {
    this.totalPlayedData = totalPlayedData || [];
  };

  handleOpenPopup = async (
    contentDetail: any,
    isEnded?: any,
    isNotGoToPayment?: any,
    contentProps?: any
  ): Promise<any> => {
    const {
      profile,
      currentProfile,
      OPEN_POPUP,
      isMobile,
      USER_TYPE,
      router,
      openPopup,
      geoCheck
    }: any = this.props;
    const { content, currentEpisode }: any = this.state;
    const popupData = OPEN_POPUP;
    const id = contentDetail?.id;
    const groupPackageId = contentDetail?.packages?.[0]?.id || 0;
    const type = content?.type;
    const isPremiumPVodHaveSVod = contentDetail?.isPremiumPVodHaveSVod;
    const isPremiumPVodNotSVod = contentDetail?.isPremiumPVodNotSVod;
    const {
      isVip,
      triggerLoginDuration,
      forceLogin,
      drmServiceName,
      packages,
      isEpisodeTrialInApp,
      isTriggerToApp,
      trialDuration,
      numberTrialEpisode,
      isPremiumTVod,
      permission,
      isMovieTrialInApp,
      isTriggerEngagement,
      isTriggerRegister,
      isSvodTvod
    } = contentDetail || {};
    if (isPremiumTVod && permission !== PERMISSION.KID_LIMITED) return;
    const episode = currentEpisode?.episode;

    const isEndSVodTrial = isEnded && trialDuration;
    let trigger = '';
    let customStyle = null;
    let segmentPopupName = '';
    let isPopup = false;
    let pVodInfo: any = '';
    const isMWebToApp = checkNextEpisodeToWatch({
      isEpisodeTrialInApp,
      numberTrialEpisode,
      episode,
      type,
      isMobile
    });
    const { popupName, goToBuyPackage, authTrigger }: any = parsePopupParams({
      contentType: CONTENT_TYPE.MOVIE,
      drmServiceName,
      profile,
      currentProfile,
      permission,
      forceLogin,
      isEndSVodTrial,
      isVip,
      groupPackageId,
      isMWebToApp,
      isTriggerToApp,
      trialDuration,
      numberTrialEpisode,
      type,
      isEpisodeTrialInApp,
      isMovieTrialInApp,
      isMobile,
      isPremiumTVod,
      isGlobal: geoCheck?.isGlobal,
      isTriggerEngagement,
      isTriggerRegister,
      isPremiumPVodHaveSVod,
      isPremiumPVodNotSVod,
      contentDetail,
      router
    });
    if (isEndSVodTrial && isSvodTvod) {
      if (currentProfile?.isKid) {
        return this.dispatch(openPopup({ name: POPUP.NAME.KID_LIMITED_VIP_DIALOG }));
      } else {
        return this.dispatch(
          openPopup({
            name: POPUP.NAME.TVOD_VIP_UPSELL,
            params: {
              profile: profile,
              content: content
            }
          })
        );
      }
    }
    const action = {
      func: () => {
        if (geoCheck?.isGlobal) {
          trackingPayment.globalPaymentButtonSelected({ currentPage: VALUE.TRIAL_CONTENT });
        }
        if (isEndSVodTrial) {
          svodTrialSubscribePackageButtonSelected({
            triggerFrom: VALUE.SVOD_TRIAL_DIALOG,
            userType: USER_TYPE?.userType,
            contentId: content?.id,
            contentName: content?.title
          });
        }
        onOpenPayment(router, {
          returnUrl: window?.location?.href,
          returnUrlFastTrack: window?.location?.href,
          id: content?.id,
          type: content?.type,
          isPvod: popupName === POPUP.NAME.PVOD_REGISTER,
          pkg: packages?.[0]?.id,
          referralCode: isEndSVodTrial ? 1 : 0,
          curPage: geoCheck?.isGlobal ? VALUE.TRIAL_CONTENT : null,
          newTriggerPaymentBuyPackage: {
            isGlobal: geoCheck?.isGlobal,
            profileId: profile?.id
          }
        });
      }
    };
    let isEndFreeTrial = false;
    let noControl = false;
    if (permission !== PERMISSION.CAN_WATCH && trialDuration > 0) {
      if (isEnded) {
        if (permission === PERMISSION.PAYMENT) {
          isPopup = true;
          trigger = VALUE.VIP_CONTENT;
        } else if (permission === PERMISSION.NON_LOGIN) {
          isPopup = true;
          customStyle = { paddingTop: '241px' };
          segmentPopupName =
            this.changeName === 'CONTENT'
              ? VALUE.POPUP_NAME.GUEST_VOD_LIVESTREAM_VIP
              : VALUE.POPUP_NAME.GUEST_EPISODE_VIP;
        }
        isEndFreeTrial = true;
      }
    } else if (permission === PERMISSION.PAYMENT) {
      isPopup = true;
      trigger = VALUE.VIP_CONTENT;
      if (isTriggerToApp && isMobile) {
        if (!trialDuration && type === CONTENT_TYPE.MOVIE) {
          isPopup = true;
        }
      } else if (goToBuyPackage) {
        if (isNotGoToPayment) {
          return;
        }
        if (typeof action?.func === 'function') {
          action.func();
          return;
        }
      }
    } else if (permission === PERMISSION.NON_LOGIN) {
      if (isPremiumTVod) return;
      isPopup = true;
      if (triggerLoginDuration > 0) {
        isPopup = false;
      }
      customStyle = { paddingTop: '241px' };
      segmentPopupName =
        this.changeName === 'CONTENT'
          ? VALUE.POPUP_NAME.GUEST_VOD_LIVESTREAM_VIP
          : VALUE.POPUP_NAME.GUEST_EPISODE_VIP;
      noControl = true;
    } else if (permission === PERMISSION.CAN_WATCH) {
      if (
        (isTriggerToApp &&
          isMobile &&
          ((!numberTrialEpisode &&
            (type === CONTENT_TYPE?.SEASON || type === CONTENT_TYPE?.EPISODE)) ||
            (!trialDuration && type === CONTENT_TYPE.MOVIE))) ||
        isMWebToApp
      ) {
        isPopup = true;
      }
      if (isEnded && isMovieTrialInApp && type === CONTENT_TYPE.MOVIE) {
        isPopup = true;
        isEndFreeTrial = true;
      }
    } else if (
      permission === PERMISSION.KID_LIMITED ||
      permission === PERMISSION.CONTENT_RESCTRICTED
    ) {
      isPopup = true;
    }
    if (popupData?.name === POPUP.NAME.BLOCK_ACCOUNT) isPopup = false;
    if (popupName === POPUP.NAME.PVOD_REGISTER) {
      pVodInfo = await PaymentApi.getPVodInfo({
        contentId: content?.id || contentProps?.id, // not get content with browser safari
        contentType: content?.type || contentProps?.type
      });
    }
    if (popupName === POPUP.NAME.REQUEST_REGISTER_CONVERSION && isPopup) {
      ConfigLocalStorage.set(
        LocalStorage.RE_LOGIN_PARAMS,
        JSON.stringify({
          contentData: { ...contentDetail },
          pathname: router.pathname,
          url: router.asPath
        })
      );
    }
    if (isPopup) {
      this.onExitFullscreen();
      if (popupName) {
        if (!isSvodTvod) {
          const isGlobal = geoCheck?.isGlobal;
          if (
            ((isVip && profile?.type !== userType.VIP) || content?.isPremium) &&
            !isGlobal &&
            !currentEpisode?.hasPVOD
          ) {
            if (contentDetail?.trialDuration > 0 || contentDetail?.trialDuration) {
              this.dispatch(
                PopupAction.getPopupTriggerDialog({
                  type: 'svod_trial',
                  contentId: id,
                  contentType: CONTENT_TYPE.MOVIE
                })
              );
            } else {
              this.dispatch(
                PopupAction.getPopupTriggerDialog({
                  type: 'svod',
                  contentId: id,
                  contentType: contentDetail?.type
                })
              );
            }
            this.dispatch(
              openPopup({
                name: POPUP.NAME.SVOD_TRIGGER,
                packageName: popupName === POPUP.NAME.KID_ACCESS_SVOD ? packages?.[0]?.name : '',
                isEndFreeTrial,
                id,
                trigger,
                customStyle,
                action,
                segmentPopupName,
                noControl,
                pVodInfo,
                data: {
                  ...contentDetail,
                  title: currentEpisode?.title || contentDetail?.title,
                  id: currentEpisode?.id || contentDetail?.id,
                  images: content?.images || contentProps?.images,
                  titleSeries: content?.title,
                  contentMsg: content?.contentMsg,
                  expirationMsg: content?.expirationMsg
                },
                contentType: CONTENT_TYPE.MOVIE,
                detailData: contentDetail
              })
            );
          } else {
            openPopup({
              name: popupName,
              packageName: popupName === POPUP.NAME.KID_ACCESS_SVOD ? packages?.[0]?.name : '',
              isEndFreeTrial,
              id,
              trigger,
              customStyle,
              action,
              segmentPopupName,
              noControl,
              pVodInfo,
              data: {
                ...contentDetail,
                title: currentEpisode?.title || contentDetail?.title,
                id: currentEpisode?.id || contentDetail?.id,
                images: content?.images || contentProps?.images,
                titleSeries: content?.title,
                contentMsg: content?.contentMsg,
                expirationMsg: content?.expirationMsg
              },
              contentType: contentDetail.MOVIE,
              detailData: contentDetail
            });
          }
        } else {
          if (currentProfile?.isKid) {
            return this.dispatch(openPopup({ name: POPUP.NAME.KID_LIMITED_VIP_DIALOG }));
          } else {
            return this.dispatch(
              openPopup({
                name: POPUP.NAME.TVOD_VIP_UPSELL,
                params: {
                  profile: profile,
                  content: content
                }
              })
            );
          }
        }
      } else if (authTrigger) {
        const remakeDestination = encodeParamDestination(this.props as any).router?.asPath;
        (this.props as any).router.push(
          `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${authTrigger}`
        );
      }
    }
  };
  onExitFullscreen = () => {
    if (checkIsFullscreen()) {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitCancelFullScreen) document.webkitCancelFullScreen();
    }
  };

  getSeoData = () => {
    const { router, GET_CONTENT_EPISODE, GET_CONTENT }: any = this.props;
    const { currentEpisode, content }: any = this.state;
    const { eps, rel } = router.query;
    const { episodeFromPath, relatedFromPath } = handleGetSlugEpsFromPath({ path: router.asPath });
    const episode = eps || episodeFromPath;
    const slugVideo = rel || relatedFromPath;
    const data =
      episode || slugVideo ? currentEpisode || GET_CONTENT_EPISODE : content || GET_CONTENT;
    const page = router?.pathname; // PAGE.VOD
    return {
      page,
      data,
      content: content || GET_CONTENT,
      contentEpisode: currentEpisode || GET_CONTENT_EPISODE,
      prefixPath: getPathFromUrl(router.asPath),
      isSeaSon: !episode && !slugVideo
    };
  };

  setInitPlay = ({ sessionId }: any) => {
    this.sessionId = sessionId || 0;
  };

  handleRetry = () => {
    const { router, GET_CONTENT }: any = this.props;
    const { currentEpisode }: any = this.state;
    const { eps } = router.query;
    const episode = eps;
    const episodeId = currentEpisode?.id;
    const content = GET_CONTENT;
    const contentId = content?.id;
    this.setState({ contentDetail: null }, () => {
      createTimeout(() => {
        this.getContentDetail({
          contentId,
          episodeId,
          content,
          currentEpisode,
          episode
        });
      }, 1000);
    });
  };
  handleStatusFullscreenOfPlayer = (status: any) => {
    const { setStatusFullscreen }: any = this.props;
    setStatusFullscreen(status);
  };

  render() {
    const {
      GET_PROFILE,
      pageProps,
      dataSEOAllPage,
      ribbonNotFound,
      sessionId,
      isAndroid,
      isWindows,
      router,
      isMacOs,
      isSafari
    }: any = this.props;
    const { renderNotFoundPage }: any = this.state;
    const dataRender = this.getDataRender();
    const seoData = this.getSeoData();
    const { ssr } = pageProps || {};
    const { query } = router || {};
    const { category } = query || {};
    const isShortVideoContent = +category === CONTENT_TYPE.SHORT_CONTENT;
    if (pageProps?.renderNotFoundPage || renderNotFoundPage || isShortVideoContent) {
      return (
        <ContentNotFound
          isVerticalPlayer={isShortVideoContent}
          dataRibbon={{ 0: ribbonNotFound || [] }}
        />
      );
    }
    return (
      <>
        <Detail
          profile={GET_PROFILE}
          dataNewSEO={dataSEOAllPage?.seo}
          ssr={ssr}
          {...dataRender}
          playerTitle=""
          changeSeason={this.changeSeason}
          onControlPlay={this.onControlPlay}
          onClickEpisode={this.onClickEpisode}
          handleRetry={this.handleRetry}
          setInitPlay={this.setInitPlay}
          handlePopupFreeTrial={this.handleOpenPopup}
          updateTotalPlayedData={this.updateTotalPlayedData}
          setupPlayer={this.setupPlayer}
          handleSetSessionId={this.handleSetSessionId}
          sessionId={sessionId}
          handleEndSessionPlay={this.handleEndSessionPlay}
          handleStatusFullscreenOfPlayer={this.handleStatusFullscreenOfPlayer}
        />
        {(isAndroid || (isWindows && SIGMA_DRM_WINDOWS) || (isMacOs && !isSafari)) && (
          <SigmaDRM onLoaded={this.onSigmaLoaded} />
        )}
        <SeoText seo={dataSEOAllPage?.seo} disableH1 />
        <SeoAllPage {...dataSEOAllPage} {...seoData} />
      </>
    );
  }
}

const mapStateToProps = (state: any) => {
  const { Detail, User, Popup, Episode, App, LiveTV, Player, Page, Profile, MultiProfile } = state;
  return {
    ...(App || {}),
    ...(Page || {}),
    ...(Profile || {}),
    ...(Detail || {}),
    ...(LiveTV || {}),
    ...(User || {}),
    ...(Popup || {}),
    ...(Episode || {}),
    ...(Player || {}),
    ...(MultiProfile || {})
  };
};

const mapDispatchToProps = (dispatch: any) => {
  const actions = bindActionCreators(
    {
      setLoadedData: setLoadedData,
      setToast: setToast,
      setIsMasterBanner: setIsMasterBanner,
      setNextEpisode: setNextEpisode,
      clearEpisodeList: clearEpisodeList,
      getContent: getContent,
      getContentDetailByID: getContentDetailByID,
      resetContent: resetContent,
      getRibbonDetailNotFound: getRibbonDetailNotFound,
      openPopup: openPopup,
      setSetting,
      setEpisode: setEpisode,
      getVodContent: getVodContent,
      getSEOAllPage: getSEOAllPage,
      getContentConfig: getContentConfig,
      clearRegisterConsultation: clearRegisterConsultation,
      setSessionId: setSessionId,
      refreshSessionPlay: refreshSessionPlay,
      endSessionPlay: endSessionPlay,
      getTriggerConfig: getTriggerConfig,
      setStatusFullscreen
    },
    dispatch
  );
  return { ...actions, dispatch };
};

export default connect(mapStateToProps, mapDispatchToProps)(DetailContainer);
