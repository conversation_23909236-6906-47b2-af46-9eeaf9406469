import React, { useEffect, useRef, useState } from 'react';
import { setLoadedData, setToast } from '@actions/app';
import { checkStatusLiveStream, getEventRelated } from '@actions/livestream';
import {
  getSEOAllPage,
  getDataLivestreamEventsById,
  getDataLivestreamEvents,
  getPageRibbons,
  getDataRibbonsId
} from '@actions/page';
import { getPopupTriggerDialog, openPopup } from '@actions/popup';
import {
  getUserSubcribeNotifyComingSoon,
  getUserUnSubcribeNotifyComingSoon,
  getUserNotifyComingSoon
} from '@actions/user';
import { useDispatch, useSelector } from 'react-redux';
import {
  CONTENT_TYPE,
  CONTENT_TYPE_NOTIFY,
  ERROR_CODE,
  HTTP_CODE,
  PAGE,
  PERMISSION,
  POPUP,
  RIBBON_TYPE,
  SEO_PAGES,
  TVOD,
  USER_TYPE
} from '@constants/constants';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import { VALUE } from '@config/ConfigSegment';
import LocalStorage from '@config/LocalStorage';
import EmptyLiveStream from '@components/empty/EmptyLiveStream';
import {
  addParamToUrlVieON,
  decodeSignature,
  encodeParamDestination,
  getCookie,
  onOpenPayment,
  removeURLQueryParams
} from '@helpers/common';
import LiveStream from '@components/livestream/LiveStream';
import { moEngageEvent } from '@tracking/TrackingMoEngage';
import ListRibbons from '@components/Sections/ListRibbons';
import SeoAllPage from '@components/seo/SeoAllPage';
import ContentNotFound from '@components/notfound/ContentNotFound';
import { MOE_NAME, MOE_PROPERTY } from '@config/ConfigMoEnage';
import { TEXT } from '@constants/text';
import { parsePopupParams } from '@services/popupServices';
import { endSessionPlay, getRibbonDetailNotFound, refreshSessionPlay } from '@actions/detail';
import PageApi from '@apis/cm/PageApi';
import { setStatusFullscreen } from '@actions/player';
import ConfigCookie from '@config/ConfigCookie';
import { getContentConfig } from '@actions/appConfig';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import cookie from 'react-cookies';
import { isEmpty } from 'lodash';
import styles from './Stream.module.scss';

let keyBreadcrumbs = SEO_PAGES.LIVE_STREAM;
const PAGE_SLUG_DEFAULT = `${PAGE.LIVE_STREAM}/`;

const StreamContainer = ({ router, pageProps }: any) => {
  const dispatch = useDispatch();
  const timeRefreshToken = useRef<any>(0);
  const timeEventEndStream = useRef<any>(0);
  const timeEndStreamEl = useRef<any>(0);
  const { activeMenu, activeSubMenu, menuList } = useSelector((state: any) => state?.Menu || {});
  const { eventRelated } = useSelector((state: any) => state?.Livestream) || {};
  const dataMenu =
    activeSubMenu ||
    activeMenu ||
    (menuList || []).find((item: any) => item?.seo?.url === '/live-streaming/');
  const pageSlug = dataMenu?.dataSlug || dataMenu?.seo?.url;
  const { isIOS, isSafari, isMobile, isTablet, geoCheck, deviceId } =
    useSelector((state: any) => state?.App) || {};
  const Page = useSelector((state: any) => state?.App) || {};
  const isGlobal = geoCheck?.isGlobal;
  const accessToken = Page?.accessToken || Page?.token;
  const tokenAnonymous = cookie.load(ConfigCookie.KEY.ANONYMOUS_TOKEN);
  const popupName = useSelector((state: any) => state?.Popup?.popupName) || '';
  const { ribbonNotFound } = useSelector((state: any) => state?.Detail) || {};
  const profile = useSelector((state: any) => state?.Profile?.profile) || null;
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile) || null;
  const { dataSEOAllPage, eventsData, pageRibbon } = useSelector((state: any) => state?.Page) || {};
  const { statusEvent } = useSelector((state: any) => state?.Livestream) || {};
  const { isFullscreen, blockPlayer } = useSelector((state: any) => state?.Player || {});
  const { concurrentScreen, dataRefreshSession } = useSelector((state: any) => state?.Detail || {});
  const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
  const [videoEndOrError, setVideoEndOrError] = useState(false);
  const [behindLive, setBehindLive] = useState(false);
  const [timeEndScreen, setTimeEndScreen] = useState(0);
  const [state, setState] = useState<any>({
    dataEventDetails: null,
    statusLiveEvent: 0
  });
  const [currentTime, setCurrentTime] = useState<any>();

  const containerRef = useRef<any>(null);
  const { slug } = router?.query || {};
  const statusSubscribed = eventsData?.isSubcribed;
  let dataPageRibbon = pageRibbon?.[PAGE_SLUG_DEFAULT] || null;
  const statusEmptyContent = state?.dataPageRibbon?.length === 0 && dataPageRibbon?.length === 0;
  const isPathnameTVod = (router?.pathname || '').includes(PAGE.LIVE_TVOD_SLUG);
  let routeAspath = router?.asPath;
  if ((routeAspath || '').indexOf('#') > -1) routeAspath = removeURLQueryParams(routeAspath, '#');

  useEffect(() => {
    dispatch(getContentConfig());
    return () => {
      clearTimeout(timeRefreshToken?.current);
      if (sessionToken) handleEndSessionPlay(sessionToken);
    };
  }, []);

  useEffect(() => {
    if (currentProfile?.isKid) {
      dispatch(openPopup({ name: POPUP.NAME.KID_LIMITED_CONTENT_DIALOG }));
    }
  }, [currentProfile?.isKid]);

  useEffect(() => {
    // xu ly off fullscren khi block account
    if (blockPlayer && isFullscreen) {
      document.exitFullscreen();
    }
  }, [blockPlayer, isFullscreen]);

  useEffect(() => {
    if (state?.dataEventDetails?.id) {
      checkLiveStreamPermission({
        data: state.dataEventDetails,
        isNotGoToPayment: true
      });
    }
    ConfigLocalStorage.set('STREAM_ID', state?.dataEventDetails?.id);
  }, [state?.dataEventDetails?.id]);

  useEffect(() => {
    // get Data
    const slugFromRouter = isPathnameTVod
      ? router?.asPath
      : routeAspath !== PAGE_SLUG_DEFAULT
      ? getSlugFromRouter(routeAspath)
      : null;
    getDataRibbon(slugFromRouter).then((res: any) => {
      const data = res?.data;
      fetchData(data);
    });
    // SEO
    if (slug) {
      if (isPathnameTVod) {
        keyBreadcrumbs = SEO_PAGES.DETAIL;
      } else {
        keyBreadcrumbs = SEO_PAGES.LIVE_STREAM_DETAIL;
      }
    }
    dispatch(
      getSEOAllPage({
        slug: routeAspath,
        keyBreadcrumbs
      })
    );
    // load data
    dispatch(setLoadedData(true));
    window.scrollTo({
      left: 0,
      top: 0,
      behavior: 'smooth'
    });
  }, [slug]);

  useEffect(() => {
    onClickStatusSubscribed();
  }, [statusSubscribed]);

  useEffect(() => {
    if (videoEndOrError && state.dataEventDetails?.id) {
      dispatch(checkStatusLiveStream(state.dataEventDetails.id));
    }
  }, [videoEndOrError]);

  // TODO: Handle get end screen data
  useEffect(() => {
    const { id, tvod, isEndStream, isPremiumTVod } = state.dataEventDetails || {};
    const isTVodSuccess = tvod?.success || '';
    if (id && (isEndStream || statusEvent?.status === 2 || state.statusLiveEvent === 2)) {
      if (isMobile) {
        openModalEndStreamTVod();
      } else if (isTVodSuccess && isPremiumTVod) {
        openModalEndStreamTVod();
      } else {
        // Get end screen data
        dispatch(getEventRelated({ id }));
      }
    }
  }, [
    state.dataEventDetails?.id,
    state.dataEventDetails?.isEndStream,
    statusEvent,
    state.statusLiveEvent
  ]);

  // TODO: Handle end screen when seeker
  useEffect(() => {
    if (isMobile || !state?.dataEventDetails?.id) return;
    if (state?.dataEventDetails?.timeEnd < new Date().getTime() / 1000) setBehindLive(false);
    if (timeEndScreen === currentTime) {
      setBehindLive(false);
      clearInterval(timeEndStreamEl.current);
    } else if (behindLive) {
      timeEndStreamEl.current = setInterval(() => {
        if (timeEndStreamEl.current) clearInterval(timeEndStreamEl.current);
        setTimeEndScreen(currentTime);
      }, 10000);
    }
    return () => {
      if (timeEndStreamEl.current) clearInterval(timeEndStreamEl.current);
    };
  }, [timeEndScreen, state?.dataEventDetails?.id, behindLive]);

  // TODO: Handle end screen when live
  useEffect(() => {
    if (isMobile) return;
    const { id, isEndStream } = state?.dataEventDetails || {};
    if (!behindLive && !isEndStream && id) {
      if (timeEventEndStream.current) clearInterval(timeEventEndStream.current);
      timeEventEndStream.current = setInterval(() => {
        dispatch(checkStatusLiveStream(state.dataEventDetails.id)).then((res: any) => {
          if (res && res.status === 2) {
            setState((prevState: any) => ({
              ...prevState,
              statusLiveEvent: res?.status
            }));
            clearInterval(timeEventEndStream.current);
          }
        });
      }, 2 * 60 * 1000);
    }
    return () => {
      if (timeEventEndStream.current) clearInterval(timeEventEndStream.current);
    };
  }, [state?.dataEventDetails?.isEndStream, behindLive, isMobile]);

  useEffect(() => {
    if (concurrentScreen?.code === ERROR_CODE.CODE_0) {
      const timer = dataRefreshSession?.countdownBySec || concurrentScreen?.countdownBySec;
      const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
      if (timeRefreshToken.current) clearTimeout(timeRefreshToken.current);
      timeRefreshToken.current = setTimeout(() => {
        dispatch(refreshSessionPlay(sessionToken)).then((res: any) => {
          if (
            !res?.success ||
            res?.httpCode === HTTP_CODE.TOO_MANY_REQUEST ||
            res?.code === ERROR_CODE.CODE_3
          ) {
            handleEndSessionPlay(sessionToken);
            clearTimeout(timeRefreshToken.current);
          }
        });
      }, timer * 1000);
    }
    return () => {
      if (timeRefreshToken.current) clearTimeout(timeRefreshToken.current);
    };
  }, [concurrentScreen, dataRefreshSession]);

  const onStalled = () => {
    if (state.dataEventDetails?.id && isIOS) {
      dispatch(checkStatusLiveStream(state.dataEventDetails.id));
    }
  };

  const handleCheckTVodLivestream = async (newEvent: any) => {
    if (currentProfile?.isKid) return;

    const isContinue = true;
    const id = state?.dataEventDetails?.id;
    if (!id) return;
    let eventData = newEvent;
    if (!eventData) {
      eventData = await PageApi.getLivestreamEventsById({
        id,
        dispatch
      });
    }

    const { isLive, tvod } = eventData || {};
    const { benefitInfo, bizInfo, errorCode } = tvod || {};
    const { preOrder } = bizInfo || {};

    if (isLive && eventData?.permission !== PERMISSION.CAN_WATCH) {
      if (errorCode === ERROR_CODE.CODE_106) {
        openPopupHandle({
          name: POPUP.NAME.TVOD_LIVE_EXPIRED,
          content: eventData
        });
      } else if (
        benefitInfo?.type === TVOD.USER_TYPE.NONE ||
        benefitInfo?.type === TVOD.USER_TYPE.EXPIRED
      ) {
        if (!preOrder?.isPreOrdering && bizInfo?.price > 0) {
          openPopupHandle({
            name: POPUP.NAME.TVOD_LIVE_CAN_RENTAL,
            content: eventData,
            tvodInfo: tvod,
            isChangeToVOD: !!eventData?.contentId,
            isNotCheckPrice: true,
            onContinue: () => {
              const queryParams = addParamToUrlVieON(router?.query, {
                type: CONTENT_TYPE.LIVESTREAM,
                id: eventData?.id || 0,
                isLiveEvent: true,
                fromPrice: tvod?.bizInfo?.price || 0
              });
              router.push(
                {
                  pathname: PAGE.RENTAL_CONTENT,
                  query: queryParams
                },
                {
                  pathname: PAGE.RENTAL_CONTENT,
                  query: queryParams
                }
              );
            }
          });
        }
      }
    }

    // reload events data
    setState((prevState: any) => ({
      ...prevState,
      dataEventDetails: eventData
    }));

    return {
      isContinue
    };
  };

  const onClickStatusSubscribed = (dataEvent?: any) => {
    if (dataEvent) {
      const currentStatus = dataEvent?.isSubcribed;
      if (currentStatus !== statusSubscribed) {
        const newData = dataEvent;
        newData.isSubcribed = statusSubscribed;

        setState((prevState: any) => ({
          ...prevState,
          dataEventDetails: newData
        }));
      }
    }
  };

  const closeModal = () => {
    dispatch(openPopup());
  };

  const openModalPayment = () => {
    dispatch(
      openPopup({
        name: profile?.id ? POPUP.NAME.USER_VOD_TRIAL_GLOBAL : POPUP.NAME.NON_LOGIN_TRIAL_GLOBAL,
        action: {
          func: () => {
            onOpenPayment(router, {
              returnUrl: window?.location?.href,
              newTriggerPaymentBuyPackage: {
                isGlobal,
                profileId: profile?.id
              }
            });
          }
        }
      })
    );
  };

  const openModalEndStreamTVod = () => {
    if (currentProfile?.isKid) return;

    setState((prevState: any) => ({
      ...prevState,
      dataEventDetails: {
        ...state.dataEventDetails,
        linkPlay: '',
        isComingSoonDefault: 0,
        isLive: 0,
        isEndStream: true
      }
    }));
    const contentId = state.dataEventDetails?.contentId || '';
    const { tvod, isPremiumTVod } = state.dataEventDetails || {};
    const isTVodSuccess = tvod?.success || '';
    if (isMobile) {
      dispatch(
        openPopup({
          profile,
          name: POPUP.NAME.END_LIVESTREAM,
          contentId,
          closeModal
        })
      );
    } else if (isTVodSuccess && isPremiumTVod) {
      dispatch(
        openPopup({
          profile,
          name: POPUP.NAME.END_LIVESTREAM,
          contentId,
          idEvent: state.dataEventDetails?.id,
          closeModal
        })
      );
    }
  };

  const autoLoadLiveStream = () => {
    setState((prevState: any) => ({
      ...prevState,
      dataEventDetails: {
        ...state.dataEventDetails,
        isLive: 1,
        isComingSoonDefault: 0
      }
    }));
  };
  const getDataRibbon = async (slugFromRouter: any) => {
    if (!dataPageRibbon) {
      dataPageRibbon = await dispatch(
        getPageRibbons({
          pageSlug: PAGE_SLUG_DEFAULT,
          accessToken,
          isGlobal
        })
      ).then((res: any) => res?.data?.data);
    }
    if (routeAspath.includes('#')) {
      routeAspath = removeURLQueryParams(routeAspath, '#');
    }

    const pathname = routeAspath.split('?')[0];
    if (pathname === PAGE_SLUG_DEFAULT) {
      const firstRibbonLive = (dataPageRibbon || []).find(
        (el: any) => el.type === RIBBON_TYPE.LIVESTREAM
      );
      const firstRibbonID = firstRibbonLive?.id;
      if (firstRibbonID) {
        const res = await dispatch(
          getDataRibbonsId({
            id: firstRibbonID,
            accessToken,
            limit: 12,
            isGlobal
          })
        );
        if (sessionToken) handleEndSessionPlay(sessionToken);
        return dispatch(getDataLivestreamEventsById({ id: res?.data?.items?.[0]?.id }));
      }
    }
    if (slugFromRouter) {
      if (sessionToken) handleEndSessionPlay(sessionToken);
      return dispatch(getDataLivestreamEvents({ slug: slugFromRouter }));
    }

    return null;
  };

  const openPopupHandle = (params: any) => {
    dispatch(openPopup(params));
  };

  const onControlPlay = () => {
    const { dataEventDetails } = state;
    if (isMobile && dataEventDetails?.id && !!dataEventDetails?.isEndStream) {
      openModalEndStreamTVod();
    } else {
      return checkLiveStreamPermission({ data: state.dataEventDetails });
    }
  };
  const checkLiveStreamPermission = ({ data, isNotGoToPayment }: any) => {
    if (currentProfile?.isKid) return;
    const { id, permission, forceLogin, isVip, isPremiumTVod, isLive, isComingSoon } = data || {};
    if (!id) return;
    const groupPackageId = data?.packages?.[0]?.id || 0;
    const { popupName, goToBuyPackage }: any = parsePopupParams({
      profile,
      currentProfile,
      contentType: CONTENT_TYPE.LIVESTREAM,
      permission,
      forceLogin,
      isVip,
      groupPackageId,
      isMobile,
      isSubscribeComingSoon: !isLive || isComingSoon,
      contentDetail: data,
      router
    });
    let trigger = '';
    let segmentPopupName = '';
    let action = null;

    if (permission === PERMISSION.CAN_WATCH) return;
    const packages = data?.packages || [];
    action = {
      func: () =>
        onOpenPayment(router, {
          returnUrl: window?.location?.href,
          pkg: packages?.[0]?.id,
          newTriggerPaymentBuyPackage: {
            isGlobal,
            profileId: profile?.id
          }
        })
    };
    if (permission === PERMISSION.PAYMENT) {
      trigger = VALUE.VIP_CONTENT;
      if (goToBuyPackage) {
        if (isNotGoToPayment) {
          return;
        }
        if (typeof action?.func === 'function') {
          action.func();
          return;
        }
      }
    } else if (permission === PERMISSION.NON_LOGIN) {
      if (isVip) {
        segmentPopupName = VALUE.POPUP_NAME.GUEST_VOD_LIVESTREAM_VIP;
      }
    }
    const currentTime = new Date().getTime() / 1000;
    const dataLive = !isEmpty(state?.dataEventDetails) ? state?.dataEventDetails : state;
    if (isPremiumTVod) {
      handleCheckTVodLivestream(data);
    } else if (
      isVip &&
      !isGlobal &&
      profile?.type !== USER_TYPE.VIP &&
      currentTime > dataLive?.timeStart &&
      (currentTime < dataLive?.timeEnd || dataLive?.timeEnd === 0)
    ) {
      dispatch(
        getPopupTriggerDialog({ type: 'svod', contentId: id, contentType: CONTENT_TYPE.LIVESTREAM })
      );
      dispatch(
        openPopup({
          name: POPUP.NAME.SVOD_TRIGGER,
          contentType: CONTENT_TYPE.LIVESTREAM,
          id,
          trigger,
          action,
          segmentPopupName,
          data: dataLive
        })
      );
    } else {
      if (popupName) {
        dispatch(
          openPopup({
            name: popupName,
            contentType: CONTENT_TYPE.LIVESTREAM,
            id,
            trigger,
            action,
            segmentPopupName,
            data: dataLive
          })
        );
      }
    }

    // Fix MP-2035
    /* if (authTrigger) {
      const remakeDestination = encodeParamDestination(router?.asPath);
      console.log(123, authTrigger)
      router.push(
        `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${authTrigger}`
      );
    } */
  };

  const onClickControlNotify = (eventsDetail: any) => {
    const data = state?.dataEventDetails || eventsDetail;
    if (!data) return;
    if (!profile?.id) {
      ConfigLocalStorage.set(LocalStorage.BACK_FROM_CLICK_NOTIFY, 'clicked');
      const remakeDestination = encodeParamDestination(router?.asPath);
      router.push(
        `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${TYPE_TRIGGER_AUTH.LIVESTREAM_COMING_SOON}`
      );
      return;
    }
    moEngageEvent(MOE_NAME.COMING_SOON_CONTENT, {
      [MOE_PROPERTY.CONTENT_ID]: data?.id,
      [MOE_PROPERTY.CONTENT_TYPE]: CONTENT_TYPE.LIVESTREAM,
      [MOE_PROPERTY.IS_SUB]: !data?.isComingSoon
    });
    if (data?.isComingSoon) {
      dispatch(getUserUnSubcribeNotifyComingSoon(data?.id));
    } else {
      dispatch(setToast({ message: TEXT.LIVE_STREAM_NOTIFY }));
      dispatch(
        getUserSubcribeNotifyComingSoon(data?.id, data?.timeStart, CONTENT_TYPE_NOTIFY.LIVE_EVENT)
      );
    }
    // data.isComingSoon = data.isSubcribed = !state?.dataEventDetails?.isComingSoon;
    data.isComingSoon = !state?.dataEventDetails?.isComingSoon;
    data.isSubcribed = data.isComingSoon;

    setState((prevState: any) => ({
      ...prevState,
      dataEventDetails: data
    }));
  };

  const handleNotifyData = async ({ data }: any) => {
    if (!data) return;
    const isClickedNotify = ConfigLocalStorage.get(LocalStorage.BACK_FROM_CLICK_NOTIFY);
    const toastNotify = ConfigLocalStorage.get(LocalStorage.TOAST_NOTIFY_LIVE_EVENT);
    if (data?.isComingSoon && profile?.id) {
      const id = data?.id;
      const list_id = [id];
      await dispatch(getUserNotifyComingSoon(list_id)).then((res: any) => {
        if (res?.data) {
          data.isComingSoon = res?.data[id];
          data.isSubcribed = res?.data[id];

          setState((prevState: any) => ({
            ...prevState,
            dataEventDetails: { ...data }
          }));
          if (toastNotify) {
            dispatch(setToast({ message: TEXT.LIVE_STREAM_NOTIFY }));
            ConfigLocalStorage.remove(LocalStorage.TOAST_NOTIFY_LIVE_EVENT);
          }
        }
      });
      if (isClickedNotify) {
        onClickControlNotify(data);
        ConfigLocalStorage.remove(LocalStorage.BACK_FROM_CLICK_NOTIFY);
      }
    }
    if (!profile) {
      data.isComingSoon = false;
    }
  };
  const handleStatusFullscreenOfPlayer = (status: any) => {
    dispatch(setStatusFullscreen(status));
  };

  const handleEndSessionPlay = (value: any) => {
    dispatch(endSessionPlay(value));
  };

  /**
   * Updates the behind live status and current time.
   *
   * @param {object} value - The data object containing information about behind live status and current time.
   */
  const handleBehindLive = (value: any) => {
    if (isMobile) return;
    setBehindLive(value);
  };

  /**
   * Handles the received player time data.
   *
   * @param {Object} data - The received player time data.
   */
  const handleGetPlayerTime = (data: any) => {
    if (data?.currentTime && !isMobile) {
      setCurrentTime(data?.currentTime);
    }
  };

  const fetchData = (data: any) => {
    onClickStatusSubscribed(data);
    const linkPlay = data?.linkPlay;
    handleNotifyData({ data });
    setState((prevState: any) => ({
      ...prevState,
      dataEventDetails: {
        ...data,
        linkPlay
      }
    }));
  };

  if (statusEmptyContent) return <EmptyLiveStream />;

  if (pageProps?.renderNotFoundPage) {
    return <ContentNotFound dataRibbon={{ 0: ribbonNotFound || [] }} />;
  }

  const hanldePropToast = (e: any) => {
    dispatch(setToast(e));
  };

  return (
    <>
      <section className="section section--stream-player overflow canal-v">
        <div className="section__body" ref={containerRef}>
          {state.dataEventDetails && (
            <LiveStream
              isIOS={isIOS}
              isSafari={isSafari}
              isMobile={isMobile}
              isTablet={isTablet}
              isGlobal={isGlobal}
              popupName={popupName}
              statusEvent={statusEvent}
              handleVideoEndOrError={setVideoEndOrError}
              currentProfile={currentProfile}
              dataEventDetails={state.dataEventDetails}
              seoData={dataSEOAllPage?.seo}
              containerHeight={containerRef?.current?.clientHeight}
              slug={slug}
              profile={profile}
              blockPlayer={blockPlayer}
              onClickControlNotify={onClickControlNotify}
              onControlPlay={onControlPlay}
              openPopup={openPopup}
              autoLoadLiveStream={autoLoadLiveStream}
              openModalPayment={openModalPayment}
              openModalEndStreamTVod={openModalEndStreamTVod}
              onStalled={onStalled}
              handleCheckTVodLivestream={handleCheckTVodLivestream}
              handleStatusFullscreenOfPlayer={handleStatusFullscreenOfPlayer}
              concurrentScreen={concurrentScreen}
              handleEndSessionPlay={handleEndSessionPlay}
              eventRelated={eventRelated}
              handleBehindLive={handleBehindLive}
              handleGetPlayerTime={handleGetPlayerTime}
              statusLiveEvent={state?.statusLiveEvent}
              deviceId={deviceId}
              tokenAnonymous={tokenAnonymous}
              setToast={hanldePropToast}
            />
          )}
        </div>
      </section>
      <ListRibbons customizeClassName={styles.container} id={pageSlug} isLiveStream />
      <SeoAllPage {...dataSEOAllPage} />
    </>
  );
};

const getSlugFromRouter = (path: any) => {
  if ((path || '').indexOf('#') > -1) path = removeURLQueryParams(path, '#');
  if ((path || '').indexOf('?') > -1) path = ((path || '').split('?') || [])?.[0];
  const pathSplited = path.split('/');
  if (path[path.length - 1] === '/') return pathSplited[pathSplited.length - 2];
  return pathSplited[pathSplited.length - 1];
};

StreamContainer.getInitialProps = async ({ store, req, res, query }: any) => {
  if (req) {
    // get menu from store redux
    const { Menu, App, Detail } = store.getState() || {};
    const accessToken = App?.accessToken || App?.token;
    const isGlobal = App?.geoCheck?.isGlobal;
    const cookie = req?.cookies;
    const { concurrentScreen, dataRefreshSession } = Detail;
    const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
    const keySignature = ConfigCookie.KEY.SIGNATURE;
    const userAgent = req.headers['user-agent'];
    const origin = req.headers.host;
    const ipAddress = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '')
      .split(':')
      .pop();
    const { profileToken } =
      decodeSignature({
        value: cookie?.[keySignature] || getCookie(keySignature, req?.headers.cookie) || ''
      }) || {};
    const { slug } = query || {};
    const { menuList } = Menu;

    const dataMenu =
      Menu.activeMenu ||
      (menuList || []).find((item: any) => item?.seo?.url === '/live-streaming/'); // Menu.activeMenu
    let renderNotFoundPage = false;
    if (dataMenu) {
      const pageSlug = dataMenu?.dataSlug || dataMenu?.seo?.url;
      if (pageSlug && pageSlug[pageSlug.length - 1] !== '/') {
        // response 410 with url not have '/' at last
        res.status(410);
        renderNotFoundPage = true;
        const ribbonDetail = await getRibbonDetailNotFound({
          accessToken,
          profileToken,
          ssr: true,
          ipAddress,
          userAgent,
          isGlobal,
          origin
        });
        await store.dispatch(ribbonDetail);
      }

      const getPageRibbonTask = getPageRibbons({
        pageSlug,
        accessToken,
        profileToken,
        ssr: true,
        ipAddress,
        userAgent,
        isGlobal,
        origin
      });
      const [dataPageRibbon] = await Promise.all([getPageRibbonTask]);
      // dispatch data to store
      await Promise.all([
        store
          .dispatch(dataPageRibbon)
          .then(async (response: any) => {
            const firstRibbon = response?.data?.data?.[0];
            const firstRibbonId = firstRibbon?.id;
            const dataRibbons = response?.data?.data;
            if (dataRibbons.length === 0) {
              const ribbonDetail = await getRibbonDetailNotFound({
                accessToken,
                profileToken,
                ssr: true,
                ipAddress,
                userAgent,
                isGlobal,
                origin
              });
              await store.dispatch(ribbonDetail);
            } else if (firstRibbonId) {
              const firstRibbonData = getDataRibbonsId({
                id: firstRibbonId,
                accessToken,
                profileToken,
                ssr: true,
                ipAddress,
                userAgent,
                isGlobal,
                origin
              });
              await store.dispatch(firstRibbonData);
            }
          })
          .catch(async () => {
            res.status(410);
            renderNotFoundPage = true;
            const ribbonDetail = await getRibbonDetailNotFound({
              accessToken,
              profileToken,
              ssr: true,
              ipAddress,
              userAgent,
              isGlobal,
              origin
            });
            await store.dispatch(ribbonDetail);
          })
      ]);
    }
    if (slug) {
      const ipAddress = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '')
        .split(':')
        .pop();
      if (sessionToken) store.dispatch(endSessionPlay(sessionToken));
      const eventsDetail = await getDataLivestreamEvents({
        slug,
        accessToken,
        profileToken,
        ssr: true,
        ipAddress,
        userAgent,
        origin
      });
      keyBreadcrumbs = SEO_PAGES.LIVE_STREAM_DETAIL;
      await store.dispatch(eventsDetail).then(async (response: any) => {
        if (response?.data?.error === 400) {
          res.status(404);
          renderNotFoundPage = true;
          const ribbonDetail = await getRibbonDetailNotFound({
            accessToken,
            profileToken,
            ssr: true,
            ipAddress,
            userAgent,
            isGlobal,
            origin
          });
          await store.dispatch(ribbonDetail);
        }
      });
    }
    // get data SEO
    await store.dispatch(
      getSEOAllPage({
        slug: req.path,
        keyBreadcrumbs,
        ssr: true,
        ipAddress,
        userAgent,
        origin
      })
    );
    return {
      renderNotFoundPage,
      ssr: true
    };
  }
  return {};
};

export default StreamContainer;
