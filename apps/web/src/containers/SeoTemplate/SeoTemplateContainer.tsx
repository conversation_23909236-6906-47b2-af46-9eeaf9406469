import React from 'react';
import SeoTemplate from '@components/seoTemplate/SeoTemplate';
import { useSelector } from 'react-redux';
import { getSEOAllPage, getDataRibbonsId } from '@actions/page';
import { SEO_PAGES } from '@constants/constants';
import SeoAllPage from '@components/seo/SeoAllPage';

const SeoTemplateContainer = () => {
  const ribbonData = useSelector((state: any) => state?.Page?.ribbonData);
  const dataSEOAllPage = useSelector((state: any) => state?.Page?.dataSEOAllPage);

  const data = ribbonData?.[dataSEOAllPage?.seo?.ref_id];
  const seoData = getSeoData({ data, dataSEOAllPage });
  return (
    <>
      <SeoTemplate dataRibbon={data} seoData={seoData} seoTemplateConfig={dataSEOAllPage?.seo} />
      <SeoAllPage {...dataSEOAllPage} listArrRibbon={seoData?.listArrRibbon} />
    </>
  );
};

SeoTemplateContainer.getInitialProps = async ({ store, req }: any) => {
  if (req) {
    const { App } = store.getState() || {};
    const isGlobal = App?.geoCheck?.isGlobal;
    const origin = req?.headers?.host || '';
    const renderNotFoundPage = false;
    const keyBreadcrumbs = SEO_PAGES.CATEGORY;
    const userAgent = req.headers['user-agent'];
    const ipAddress = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '')
      .split(':')
      .pop();
    await store
      .dispatch(
        getSEOAllPage({
          slug: req.path,
          keyBreadcrumbs,
          ssr: true,
          ipAddress,
          userAgent,
          origin
        })
      )
      .then(async (resConfig: any) => {
        const data = resConfig?.data;
        const ribId = data?.seo?.ref_id;
        if (ribId) {
          const dataRibbon = await getDataRibbonsId({
            id: ribId,
            page: 0,
            limit: 30,
            ribbonSlug: ribId,
            ssr: true,
            ipAddress,
            userAgent,
            isGlobal,
            origin
          });
          await store.dispatch(dataRibbon);
        }
      });
    return {
      renderNotFoundPage,
      ssr: true
    };
  }
  return { ssr: false };
};

const getSeoData = ({ data, dataSEOAllPage }: any) => {
  const listArrRibbon = (data?.items || []).map((item: any) => ({
    id: item?.id,
    imageURL: item?.images?.thumbnail || '',
    title: item?.seo?.title,
    rating: item?.avgRate || 5,
    description: item?.description,
    totalRating: item?.totalRate,
    url: item?.seo?.url
  }));
  const seoData = {
    page: dataSEOAllPage?.seo?.url,
    data: { seo: dataSEOAllPage?.seo || {} },
    listArrRibbon
  };

  return seoData;
};

export default SeoTemplateContainer;
