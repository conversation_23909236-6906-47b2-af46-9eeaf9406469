import React, { useEffect, useState } from 'react';
import HelmetTag from '@components/seo/Helmet';
import { ROBOTS, DOMAIN_WEB } from '@config/ConfigEnv';
import { PAGE } from '@constants/constants';
import { TEXT } from '@constants/text';
import ConfigImage from '@config/ConfigImage';
import { useSelector } from 'react-redux';
import CMApi from '@apis/cmApi';
import CountdownButton from '@components/basic/Buttons/CountdownButton';

declare const window: any;

const Maintenance = () => {
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const [desc, setDesc] = useState<any>('');
  useEffect(() => {
    onRetry();
  }, []);

  const onRetry = () => {
    CMApi.geoCheck({}).then((res) => {
      if (res?.httpCode === 200) {
        window.location.href = DOMAIN_WEB;
      }
      setDesc(res?.data?.message || TEXT.MAINTENANCE_DESC);
    });
  };

  const seo = {
    url: PAGE.MAINTENANCE,
    title: TEXT.MAINTENANCE,
    description: TEXT.MAINTENANCE_NOTICE,
    canonical_tag: `${DOMAIN_WEB}/`,
    meta_robots: ROBOTS || 'noindex,follow'
  };
  return (
    <>
      <HelmetTag props={{ data: { seo } }} />
      <div className="container text-center p-t6" style={{ backgroundColor: 'black' }}>
        <img
          src={ConfigImage.maintenance}
          alt="VieON Maintenance"
          style={{ width: !isMobile ? '410px' : '250px', height: '100%', margin: '0 auto' }}
        />
        <div className="p-b6">
          <p className="p-y4" style={{ color: 'white', margin: '0 auto', maxWidth: '500px' }}>
            {desc}
          </p>
          <CountdownButton
            className="button"
            style={{ backgroundColor: 'white' }}
            buttonName={TEXT.RETRY}
            time={30}
            onResend={onRetry}
          />
        </div>
      </div>
    </>
  );
};

export default Maintenance;
