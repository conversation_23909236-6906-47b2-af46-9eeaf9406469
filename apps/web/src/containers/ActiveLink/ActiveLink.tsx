import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setLoadedData, setToast } from '@actions/app';
import Head from 'next/head';
import InputCustomOtpCode from '@components/basic/Input/InputCustomOtpCode';
import ConfigImage from '@config/ConfigImage';
import UserApi from '@apis/userApi';
import Modal from '@components/basic/Modal';
import { TEXT } from '@constants/text';
import { useVieRouter } from '@customHook';
import { encodeParamDestination } from '@helpers/common';
import { HTTP_CODE, PAGE } from '@constants/constants';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import isEmpty from 'lodash/isEmpty';
import Button from '@components/basic/Buttons/Button';
import TrackingApp from '@tracking/functions/TrackingApp';
let resetInputValue = null;

const ActiveLink = () => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const [value, setValue] = useState<any>('');
  const [error, setError] = useState<any>('');
  const code = router?.query?.code || '';

  useEffect(() => {
    dispatch(setLoadedData(true));
  }, []);

  useEffect(() => {
    if (!profile?.id) {
      const remakeDestination = encodeParamDestination(router?.asPath);
      router.push(
        `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${TYPE_TRIGGER_AUTH.LOGIN_DEVICE}`
      );
    }
  }, [profile]);

  const handleSkip = async () => {
    TrackingApp.ignoreSignInQrcode({ code });
    handleClickCloseSmartTVLogin(true);
  };
  const handleSendCodeLogin = async (valueCode: any, isButtonClick = false) => {
    const result = await UserApi.globalShareLoginDevice(code || valueCode);
    const paramTracking = {
      currentPage: window?.location?.href,
      code: valueCode
    };
    if (!isEmpty(result)) {
      if (!result?.success || result?.data?.code === HTTP_CODE.FAIL) {
        if (!code) {
          setError(result?.data?.message || result?.message || TEXT.MSG_ERROR);
          if (isButtonClick) {
            TrackingApp.acceptSignInSmartTv({
              ...paramTracking,
              buttonStatus: 'fail'
            });
          }
        } else {
          dispatch(
            setToast({
              message: result?.data?.message || result?.message || TEXT.MSG_ERROR
            })
          );
        }
      } else {
        if (isButtonClick) {
          TrackingApp.acceptSignInSmartTv({
            ...paramTracking,
            buttonStatus: 'success'
          });
        }
        handleClickCloseSmartTVLogin();
        dispatch(setToast({ message: result?.data?.message || result?.message }));
      }
    }
    if (code) {
      if (result?.data?.code === 400) {
        TrackingApp.acceptSignInQrcode({ code, status: 'fail', text_error: result?.data?.message });
      } else {
        TrackingApp.acceptSignInQrcode({ code, status: 'success' });
      }
      handleClickCloseSmartTVLogin();
    }
  };

  const handleClickCloseSmartTVLogin = (isIgnore = false) => {
    if (isIgnore) {
      TrackingApp.ignoreSignInQrcode({ code });
    }
    router.push(PAGE.HOME);
  };

  const onChangeOtp = (valueOtpCode: any) => {
    if (valueOtpCode.length !== 4) {
      setError('');
    }
    if ((valueOtpCode || '').length === 4) {
      handleSendCodeLogin(valueOtpCode);
    }
    setValue(valueOtpCode);
  };

  const renderCustom = () => (
    <div className="mask p-b">
      <div className="text-center">
        <img src={ConfigImage.imgLoginTV} alt="welcome-guest" />
      </div>
    </div>
  );

  const renderCustomWithCode = () => (
    <div className="mask p-b">
      <div className="text-center">
        <img src={ConfigImage.imgLoginTVWithCode} alt="welcome-guest" />
      </div>
    </div>
  );

  const handleInput = ({ resetInput }: any) => {
    resetInputValue = resetInput || (() => {});
  };

  const renderBody = () => (
    <div className="block block--for-dark block--notify">
      <h2 className="title text-center">{TEXT.LOGIN_SMART_TV}</h2>
      <p className="text text-center text-muted margin-small-up-bottom-12 margin-large-up-bottom-32 text-large-up-16">
        {TEXT.SUB_LOGIN_SMART_TV}
      </p>
      <form className="form form-for-dark form--register" data-abide="true">
        <InputCustomOtpCode
          id="smartTVCODE"
          type="text"
          handleInput={handleInput}
          errorOtp={error}
          onEnter={handleSendCodeLogin}
          onChangeOtp={onChangeOtp}
        />
        {error && (
          <label className="alert-error form-error is-visible text-center">
            <p>{error}</p>
          </label>
        )}
      </form>
      <div className="button-group child-auto">
        <button
          className={`button button--light button--large${value.length === 4 ? '' : ' disabled'}`}
          title={TEXT.CONFIRM}
          onClick={() => handleSendCodeLogin(value, true)}
          disabled={value.length !== 4}
        >
          <span className="text">{TEXT.CONFIRM}</span>
        </button>
      </div>
    </div>
  );
  const renderBodyWithCode = () => (
    <div className="block block--for-dark block--notify">
      <h2 className="title text-center">{TEXT.CONFIRM_LOGIN}</h2>
      <p className="text text-center text-muted margin-small-up-bottom-12 margin-large-up-bottom-32 text-large-up-16">
        {TEXT.SUB_LOGIN_SMART_TV_WITH_CODE}
      </p>
      <div className="flex flex-col gap-2">
        <Button
          className="button button--light button--large"
          title={TEXT.ACCEPT}
          onClick={handleSendCodeLogin}
        />
        <Button
          className="button hollow button--large white"
          title={TEXT.SKIP}
          onClick={handleSkip}
        />
      </div>
    </div>
  );

  return (
    <>
      <Head>
        <meta name="robots" content="noindex" />
      </Head>
      <div style={styles.SmartLoginWrapper}>
        {profile && (
          <Modal
            renderCustom={code ? renderCustomWithCode : renderCustom}
            className="green-line modal--notify-request"
            renderBody={code ? renderBodyWithCode : renderBody}
            onClosed={handleClickCloseSmartTVLogin}
            classCustom="p-t2"
          />
        )}
      </div>
    </>
  );
};

const styles: any = {
  SmartLoginWrapper: {
    margin: 0,
    padding: 0,
    minHeight: '100vh',
    position: 'relative'
  },
  top: {
    top: '3.5em'
  }
};

export default ActiveLink;
