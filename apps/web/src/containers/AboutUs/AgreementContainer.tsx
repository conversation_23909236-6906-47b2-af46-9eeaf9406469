import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getAgreementConfig, setLoadedData } from '@actions/app';
import { FOOTER } from '@constants/constants';
import { pageView } from '@tracking/functions/TrackingApp';
import HelmetTag from '@components/seo/Helmet';
import { ROBOTS, DOMAIN_WEB } from '@config/ConfigEnv';

const AgreementContainer = () => {
  const dispatch = useDispatch();
  const htmlConfig = useSelector((state: any) => state?.App?.agreement);

  const seo = {
    url: FOOTER.NAV.AGREEMENT.PATH,
    title: FOOTER.NAV.AGREEMENT.TITLE,
    description: FOOTER.NAV.AGREEMENT.DESC,
    canonical_tag: `${DOMAIN_WEB}/`,
    meta_robots: ROBOTS || 'noindex,follow',
    alternate: DOMAIN_WEB + FOOTER.NAV.AGREEMENT.PATH,
    deeplink: 'vieonapp://vieon.vn/aboutus/chinh-sach-va-quy-dinh-chung'
  };

  useEffect(() => {
    if (!htmlConfig) {
      dispatch(getAgreementConfig({}));
    }
    dispatch(setLoadedData(true));
    pageView({ data: { seo } });
  }, []);

  return (
    <>
      <HelmetTag props={{ data: { seo } }} />
      <div dangerouslySetInnerHTML={{ __html: htmlConfig || '' }} />
    </>
  );
};

AgreementContainer.getInitialProps = async ({ store, req }: any) => {
  if (req) {
    // get menu from store redux
    await store.dispatch(getAgreementConfig({ ssr: true }));
    return { ssr: true };
  }
  return {};
};

export default AgreementContainer;
