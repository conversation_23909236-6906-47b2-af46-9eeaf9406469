import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getUsageV3Config, setLoadedData, getUsageV2Config } from '@actions/app';
import { FOOTER } from '@constants/constants';
import { pageView } from '@tracking/functions/TrackingApp';
import HelmetTag from '@components/seo/Helmet';
import { ROBOTS, DOMAIN_WEB } from '@config/ConfigEnv';

const UsageContainer = () => {
  const dispatch = useDispatch();
  const htmlConfig = useSelector((state: any) => state?.App?.usageV3);

  const seo = {
    url: FOOTER.NAV.USAGE_V2.PATH,
    title: FOOTER.NAV.USAGE_V2.TITLE,
    description: FOOTER.NAV.USAGE_V2.DESC,
    canonical_tag: `${DOMAIN_WEB}/`,
    meta_robots: ROBOTS || 'noindex,follow',
    alternate: DOMAIN_WEB + FOOTER.NAV.USAGE_V2.PATH,
    deeplink: 'vieonapp://vieon.vn/aboutus/hop-dong-dien-tu'
  };

  useEffect(() => {
    if (!htmlConfig) {
      dispatch(getUsageV3Config({}));
    }
    dispatch(setLoadedData(true));
    pageView({ data: { seo } });
  }, []);

  return (
    <>
      <HelmetTag props={{ data: { seo } }} />
      <div dangerouslySetInnerHTML={{ __html: htmlConfig || '' }} />
    </>
  );
};

UsageContainer.getInitialProps = async ({ store, req }: any) => {
  if (req) {
    // get menu from store redux
    await store.dispatch(getUsageV2Config({ ssr: true }));
    return { ssr: true };
  }
  return {};
};

export default UsageContainer;
