import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getPolicyCancellationConfig, setLoadedData } from '@actions/app';
import { FOOTER } from '@constants/constants';
import { pageView } from '@tracking/functions/TrackingApp';
import HelmetTag from '@components/seo/Helmet';
import { ROBOTS, DOMAIN_WEB } from '@config/ConfigEnv';

const PolicyCancellationContainer = () => {
  const dispatch = useDispatch();
  const htmlConfig = useSelector((state: any) => state?.App?.policyCancellation);

  const seo = {
    url: FOOTER.NAV.POLICY_CANCELLATION.PATH,
    title: FOOTER.NAV.POLICY_CANCELLATION.TITLE,
    description: FOOTER.NAV.POLICY_CANCELLATION.DESC,
    canonical_tag: `${DOMAIN_WEB}/`,
    meta_robots: ROBOTS || 'noindex,follow',
    alternate: DOMAIN_WEB + FOOTER.NAV.POLICY_CANCELLATION.PATH
  };

  useEffect(() => {
    if (!htmlConfig) {
      dispatch(getPolicyCancellationConfig({}));
    }
    dispatch(setLoadedData(true));
    pageView({ data: { seo } });
  }, []);

  return (
    <>
      <HelmetTag props={{ data: { seo } }} />
      <div dangerouslySetInnerHTML={{ __html: htmlConfig || '' }} />
    </>
  );
};

PolicyCancellationContainer.getInitialProps = async ({ store, req }: any) => {
  if (req) {
    // get menu from store redux
    await store.dispatch(getPolicyCancellationConfig({ ssr: true }));
    return { ssr: true };
  }
  return {};
};

export default PolicyCancellationContainer;
