import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getRegulationConfig, setLoadedData } from '@actions/app';
import { FOOTER, PAGE } from '@constants/constants';
import { pageView } from '@tracking/functions/TrackingApp';
import HelmetTag from '@components/seo/Helmet';
import { ROBOTS, DOMAIN_WEB } from '@config/ConfigEnv';
import { useVieRouter } from '@customHook';
import { addParamToUrlVieON } from '@helpers/common';

const RegulationContainer = () => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const htmlConfig = useSelector((state: any) => state?.App?.regulation);

  const seo = {
    url: FOOTER.NAV.REGULATION.PATH,
    title: FOOTER.NAV.REGULATION.TITLE,
    description: FOOTER.NAV.REGULATION.DESC,
    canonical_tag: `${DOMAIN_WEB}/`,
    meta_robots: ROBOTS || 'noindex,follow',
    alternate: DOMAIN_WEB + FOOTER.NAV.REGULATION.PATH
  };

  useEffect(() => {
    if (!htmlConfig) {
      dispatch(getRegulationConfig({}));
    }
    dispatch(setLoadedData(true));
    pageView({ data: { seo } });

    const handleAnchorClick = (event: any) => {
      event.preventDefault(); // Prevent the default behavior (page reload)
      let href = event.currentTarget.getAttribute('href');
      // Use Next.js router to navigate without full page reload
      const isViewApp = router?.query?.isViewApp;
      const queryParams = addParamToUrlVieON(router?.query, isViewApp ? { isViewApp: true } : {});
      const isInAppZalopay = (router?.pathname || '').includes(PAGE.ZALOPAY);
      if (isInAppZalopay) {
        href = `${PAGE.ZALOPAY}${href}`;
      }
      router.push({ pathname: href, query: queryParams }, { pathname: href, query: queryParams });
    };

    // Find all anchor tags in the rendered HTML
    const anchorTags = document.querySelectorAll('a');

    // Attach click event listeners to each anchor tag
    anchorTags.forEach((anchor) => {
      anchor.addEventListener('click', handleAnchorClick);
    });

    // Cleanup event listeners when the component unmounts
    return () => {
      anchorTags.forEach((anchor) => {
        anchor.removeEventListener('click', handleAnchorClick);
      });
    };
  }, []);

  return (
    <>
      <HelmetTag props={{ data: { seo } }} />
      <div dangerouslySetInnerHTML={{ __html: htmlConfig || '' }} />
    </>
  );
};

RegulationContainer.getInitialProps = async ({ store, req }: any) => {
  if (req) {
    // get menu from store redux
    await store.dispatch(getRegulationConfig({ ssr: true }));
    return { ssr: true };
  }
  return {};
};

export default RegulationContainer;
