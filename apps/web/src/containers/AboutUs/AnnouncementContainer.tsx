import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getAnnounceConfig, setLoadedData } from '@actions/app';
import { FOOTER } from '@constants/constants';
import { pageView } from '@tracking/functions/TrackingApp';
import { ROBOTS, DOMAIN_WEB } from '@config/ConfigEnv';
import { addParamToUrlVieON } from '@helpers/common';
import { useVieRouter } from '@customHook';
import HelmetTag from '@components/seo/Helmet';

const UsageContainer = () => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const htmlConfig = useSelector((state: any) => state?.App?.announce);

  const seo = {
    url: FOOTER.NAV.ANNOUNCEMENT.PATH,
    title: FOOTER.NAV.ANNOUNCEMENT.TITLE,
    description: FOOTER.NAV.ANNOUNCEMENT.DESC,
    canonical_tag: `${DOMAIN_WEB}/`,
    meta_robots: ROBOTS || 'noindex,follow',
    alternate: DOMAIN_WEB + FOOTER.NAV.ANNOUNCEMENT.PATH
    // deeplink: 'vieonapp://vieon.vn/aboutus/hop-dong-dien-tu'
  };

  useEffect(() => {
    if (!htmlConfig) {
      dispatch(getAnnounceConfig({}));
    }
    dispatch(setLoadedData(true));
    pageView({ data: { seo } });

    const handleAnchorClick = (event: any) => {
      event.preventDefault(); // Prevent the default behavior (page reload)
      let href = event.currentTarget.getAttribute('href');
      // Use Next.js router to navigate without full page reload
      const isViewApp = router?.query?.isViewApp;
      const queryParams = addParamToUrlVieON(router?.query, isViewApp ? { isViewApp: true } : {});
      router.push({ pathname: href, query: queryParams }, { pathname: href, query: queryParams });
    };

    // Find all anchor tags in the rendered HTML
    const anchorTags = document.querySelectorAll('a');
    // Attach click event listeners to each anchor tag
    anchorTags.forEach((anchor) => {
      anchor.addEventListener('click', handleAnchorClick);
    });

    // Cleanup event listeners when the component unmounts
    return () => {
      anchorTags.forEach((anchor) => {
        anchor.removeEventListener('click', handleAnchorClick);
      });
    };
  }, []);

  return (
    <>
      <HelmetTag props={{ data: { seo } }} />
      <div dangerouslySetInnerHTML={{ __html: htmlConfig || '' }} />
    </>
  );
};

UsageContainer.getInitialProps = async ({ store, req }: any) => {
  if (req) {
    // get menu from store redux
    await store.dispatch(getAnnounceConfig({ ssr: true }));
    return { ssr: true };
  }
  return {};
};

export default UsageContainer;
