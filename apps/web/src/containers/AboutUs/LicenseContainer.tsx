import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getLicenseConfig, setLoadedData } from '@actions/app';
import { FOOTER } from '@constants/constants';
import { pageView } from '@tracking/functions/TrackingApp';
import HelmetTag from '@components/seo/Helmet';
import { ROBOTS, DOMAIN_WEB } from '@config/ConfigEnv';

const LicenseContainer = () => {
  const dispatch = useDispatch();
  const htmlConfig = useSelector((state: any) => state?.App?.license);

  const seo = {
    url: FOOTER.NAV.COPY_RIGHT.PATH,
    title: FOOTER.NAV.COPY_RIGHT.TITLE,
    description: FOOTER.NAV.COPY_RIGHT.DESC,
    canonical_tag: `${DOMAIN_WEB}/`,
    meta_robots: ROBOTS || 'noindex,follow',
    alternate: DOMAIN_WEB + FOOTER.NAV.COPY_RIGHT.PATH
  };

  useEffect(() => {
    if (!htmlConfig) {
      dispatch(getLicenseConfig({}));
    }
    dispatch(setLoadedData(true));
    pageView({ data: { seo } });
  }, []);

  return (
    <>
      <HelmetTag props={{ data: { seo } }} />
      <div dangerouslySetInnerHTML={{ __html: htmlConfig || '' }} />
    </>
  );
};

LicenseContainer.getInitialProps = async ({ store, req }: any) => {
  if (req) {
    // get menu from store redux
    await store.dispatch(getLicenseConfig({ ssr: true }));
    return { ssr: true };
  }
  return {};
};

export default LicenseContainer;
