import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getUsageV1Config, setLoadedData } from '@actions/app';
import { FOOTER } from '@constants/constants';
import { pageView } from '@tracking/functions/TrackingApp';
import HelmetTag from '@components/seo/Helmet';
import { ROBOTS, DOMAIN_WEB } from '@config/ConfigEnv';

const UsageV1Container = () => {
  const dispatch = useDispatch();
  const htmlConfig = useSelector((state: any) => state?.App?.usageV1);

  const seo = {
    url: FOOTER.NAV.USAGE_V1.PATH,
    title: FOOTER.NAV.USAGE_V1.TITLE,
    description: FOOTER.NAV.USAGE_V1.DESC,
    canonical_tag: `${DOMAIN_WEB}/`,
    meta_robots: ROBOTS || 'noindex,follow',
    alternate: DOMAIN_WEB + FOOTER.NAV.USAGE_V1.PATH
    // deeplink: 'vieonapp://vieon.vn/aboutus/hop-dong-dien-tu'
  };

  useEffect(() => {
    if (!htmlConfig) {
      dispatch(getUsageV1Config({}));
    }
    dispatch(setLoadedData(true));
    pageView({ data: { seo } });
  }, []);

  return (
    <>
      <HelmetTag props={{ data: { seo } }} />
      <div dangerouslySetInnerHTML={{ __html: htmlConfig || '' }} />
    </>
  );
};

UsageV1Container.getInitialProps = async ({ store, req }: any) => {
  if (req) {
    // get menu from store redux
    await store.dispatch(getUsageV1Config({ ssr: true }));
    return { ssr: true };
  }
  return {};
};

export default UsageV1Container;
