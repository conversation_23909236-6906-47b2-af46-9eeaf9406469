import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import { getFaqsConfig, setLoadedData } from '@actions/app';
import { pageView } from '@tracking/functions/TrackingApp';
import HelmetTag from '@components/seo/Helmet';
import { CONFIG_KEY, FOOTER } from '@constants/constants';
import { ROBOTS, DOMAIN_WEB } from '@config/ConfigEnv';

const FaqsContainer = () => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const { query } = router || {};
  const faqsConfig = useSelector((state: any) => state?.App?.faqs);

  const seo = {
    url: FOOTER.NAV.FAQS.PATH,
    title: FOOTER.NAV.FAQS.TITLE,
    description: FOOTER.NAV.FAQS.DESC,
    canonical_tag: `${DOMAIN_WEB}/`,
    meta_robots: ROBOTS || 'noindex,follow',
    alternate: DOMAIN_WEB + FOOTER.NAV.FAQS.PATH,
    deeplink: 'vieonapp://vieon.vn/aboutus/faq'
  };

  useEffect(() => {
    if (!faqsConfig) {
      const configKey = query?.isViewApp ? CONFIG_KEY.APP_FAQS : CONFIG_KEY.WEB_FAQS;
      dispatch(getFaqsConfig({ ssr: false, key: configKey }));
    }
    dispatch(setLoadedData(true));
    pageView({ data: { seo } });
  }, []);

  return (
    <>
      <HelmetTag props={{ data: { seo } }} />
      <div dangerouslySetInnerHTML={{ __html: faqsConfig || '' }} />
    </>
  );
};

FaqsContainer.getInitialProps = async ({ store, req, query }: any) => {
  if (req) {
    // get menu from store redux
    const configKey = query?.isViewApp ? CONFIG_KEY.APP_FAQS : CONFIG_KEY.WEB_FAQS;
    await store.dispatch(getFaqsConfig({ ssr: true, key: configKey }));
    return { ssr: true };
  }
  return {};
};

export default FaqsContainer;
