import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getAboutUsConfig, setLoadedData } from '@actions/app';
import { FOOTER, PAGE } from '@constants/constants';
import { pageView } from '@tracking/functions/TrackingApp';
import HelmetTag from '@components/seo/Helmet';
import { ROBOTS, DOMAIN_WEB } from '@config/ConfigEnv';
import { useVieRouter } from '@customHook';

const PATHNAME_DATA = {
  [PAGE.INTRODUCE]: FOOTER.NAV.INTRODUCE,
  [PAGE.USAGE]: FOOTER.NAV.TERMS,
  [PAGE.USAGE_V1]: FOOTER.NAV.USAGE_V1,
  [PAGE.USAGE_V2]: FOOTER.NAV.USAGE_V2,
  [PAGE.AGREEMENT]: FOOTER.NAV.AGREEMENT,
  [PAGE.INTRO_SERVICE]: FOOTER.NAV.INTRO_SERVICE,
  [PAGE.PAYMENT_POLICY]: FOOTER.NAV.PAYMENT_POLICY,
  [PAGE.PRIVATE_POLICY]: FOOTER.NAV.PRIVATE_POLICY,
  [PAGE.COPY_RIGHT]: FOOTER.NAV.COPY_RIGHT,
  [PAGE.REGULATION]: FOOTER.NAV.REGULATION,
  [PAGE.POLICY_CANCELLATION]: FOOTER.NAV.POLICY_CANCELLATION,
  [PAGE.ANNOUNCEMENT]: FOOTER.NAV.ANNOUNCEMENT,
  [PAGE.FAQS]: FOOTER.NAV.FAQS
};

const AboutUs = () => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const pathnameData = PATHNAME_DATA[router.pathname];
  const aboutUs = useSelector((state: any) => state?.App?.aboutUs);
  const htmlConfig = aboutUs?.[pathnameData.KEY];

  const seo = {
    url: pathnameData?.PATH,
    title: pathnameData?.TITLE,
    description: pathnameData?.DESC,
    canonical_tag: `${DOMAIN_WEB}/`,
    meta_robots: ROBOTS || 'noindex,follow',
    alternate: DOMAIN_WEB + pathnameData?.PATH,
    deeplink: pathnameData?.DEEPLINK
  };

  useEffect(() => {
    if (!htmlConfig && pathnameData.KEY) {
      dispatch(getAboutUsConfig({ key: pathnameData.KEY }));
    }
    dispatch(setLoadedData(true));
    pageView({ data: { seo } });
  }, []);

  useEffect(() => {
    if (!htmlConfig && pathnameData.KEY) {
      dispatch(getAboutUsConfig({ key: pathnameData.KEY }));
    }
    dispatch(setLoadedData(true));
    pageView({ data: { seo } });
  }, [pathnameData]);

  return (
    <>
      <HelmetTag props={{ data: { seo } }} />
      <div dangerouslySetInnerHTML={{ __html: htmlConfig || '' }} />
    </>
  );
};

AboutUs.getInitialProps = async ({ store, req }: any) => {
  if (req) {
    const pathname = req?._parsedUrl?.pathname;
    const data = PATHNAME_DATA[(pathname || '').replace(/\/+$/, '')];
    await store.dispatch(getAboutUsConfig({ ssr: true, key: data?.KEY }));
    return { ssr: true };
  }
  return {};
};

export default AboutUs;
