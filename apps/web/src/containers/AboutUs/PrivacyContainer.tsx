import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getPrivacyConfig, setLoadedData } from '@actions/app';
import { FOOTER } from '@constants/constants';
import { pageView } from '@tracking/functions/TrackingApp';
import HelmetTag from '@components/seo/Helmet';
import { ROBOTS, DOMAIN_WEB } from '@config/ConfigEnv';

const PrivacyContainer = () => {
  const dispatch = useDispatch();
  const htmlConfig = useSelector((state: any) => state?.App?.privacy);

  const seo = {
    url: FOOTER.NAV.PRIVATE_POLICY.PATH,
    title: FOOTER.NAV.PRIVATE_POLICY.TITLE,
    description: FOOTER.NAV.PRIVATE_POLICY.DESC,
    canonical_tag: `${DOMAIN_WEB}/`,
    meta_robots: ROBOTS || 'noindex,follow',
    alternate: DOMAIN_WEB + FOOTER.NAV.PRIVATE_POLICY.PATH,
    deeplink: 'vieonapp://vieon.vn/aboutus/chinh-sach-va-quy-dinh-chung'
  };

  useEffect(() => {
    if (!htmlConfig) {
      dispatch(getPrivacyConfig({}));
    }
    dispatch(setLoadedData(true));
    pageView({ data: { seo } });
  }, []);

  return (
    <>
      <HelmetTag props={{ data: { seo } }} />
      <div dangerouslySetInnerHTML={{ __html: htmlConfig || '' }} />
    </>
  );
};

PrivacyContainer.getInitialProps = async ({ store, req }: any) => {
  if (req) {
    // get menu from store redux
    await store.dispatch(getPrivacyConfig({ ssr: true }));
    return { ssr: true };
  }
  return {};
};

export default PrivacyContainer;
