import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Head from 'next/head';
import { getListCampaigns, getListWinnersOfCampaign } from '@actions/result-voting';
import { ACTION_TYPE } from '@actions/actionType';
import { isEmptyObject } from '@helpers/common';
import { CURRENCY } from '@constants/constants';

const ListWinnersContainer = () => {
  const dispatch = useDispatch();
  const listWinnersRef = useRef<any>(null);
  const [episodeActive, setEpisodeActive] = useState<any>({});
  const listCampaigns = useSelector(
    (state: any) => state.ResultVoting[ACTION_TYPE.GET_LIST_CAMPAIGNS]
  );
  const listWinners = useSelector(
    (state: any) => state.ResultVoting[ACTION_TYPE.GET_LIST_WINNERS_OF_CAMPAIGNS]
  );

  const listWinnersOfCampaign = useMemo(
    () => listWinners && episodeActive && listWinners[episodeActive.content_id],
    [listWinners, episodeActive]
  );

  useEffect(() => {
    dispatch(getListCampaigns());
  }, []);

  useEffect(() => {
    if (listCampaigns && listCampaigns.length > 0) {
      const eps = listCampaigns.find((ep: any) => ep.campaign_status_enable);
      if (!isEmptyObject(eps)) {
        setEpisodeActive(eps);
      }
    }
  }, [listCampaigns]);

  useEffect(() => {
    if (!isEmptyObject(episodeActive)) {
      dispatch(
        getListWinnersOfCampaign({
          contentId: episodeActive.content_id
        })
      );
    }
    const elmActive = listWinnersRef.current.getElementsByClassName('active')[0];
    if (elmActive) {
      elmActive.scrollIntoViewIfNeeded({
        behavior: 'smooth'
      });
    }
  }, [episodeActive]);

  const onSelectTab = (eps: any) => {
    setEpisodeActive(eps);
  };

  return (
    <>
      <Head>
        <title>Danh sách Trúng Giải Game RapViet | VieON </title>
        <meta
          name="description"
          content="Danh sách Trúng Giải Game RapViet mùa 2. Đừng bỏ lỡ RapViet mùa 2 trên VieON, với nhiều bản hit và những món quà bất ngờ từ Game Show."
        />
        <link rel="canonical" href="https://vieon.vn/phim-hay/" />
        <meta name="googlebot" content="noindex,follow" />
        <meta name="robots" content="noindex,follow" />
      </Head>
      <div className="row">
        <div className="column middle">
          <div className="scroll-menu" ref={listWinnersRef}>
            {listCampaigns &&
              listCampaigns.length > 0 &&
              listCampaigns.map((item: any) => (
                <a
                  onClick={() => onSelectTab(item)}
                  className={item.campaign_id === episodeActive.campaign_id ? 'active' : undefined}
                  key={item.campaign_id}
                  title={item.campaign_name}
                >
                  {item.campaign_name}
                </a>
              ))}
          </div>
          <div className="content">
            <ul className="content__list">
              {listWinnersOfCampaign &&
                listWinnersOfCampaign.length > 0 &&
                listWinnersOfCampaign.map((item: any, i: any) => (
                  <li key={item.user_id} className="item">
                    <div className="item__info">
                      <div className="stt">{`${i + 1}.`}</div>
                      <div className="info">
                        <a title={item.name} className="name">
                          {item.name || item.phone || item.email}
                        </a>
                        <span className="description">{item.phone || item.email}</span>
                      </div>
                    </div>
                    <div className="item__award">
                      {`${new Intl.NumberFormat().format(item.award || 0)} ${CURRENCY.VND}`}
                    </div>
                  </li>
                ))}
              {(!listWinnersOfCampaign || listWinnersOfCampaign.length === 0) && (
                <div className="empty-status">Không có dữ liệu ...</div>
              )}
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default memo(ListWinnersContainer);
