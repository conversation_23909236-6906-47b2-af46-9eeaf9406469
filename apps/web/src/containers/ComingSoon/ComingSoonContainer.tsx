import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import SeoAllPage from '@components/seo/SeoAllPage';
import { SEO_PAGES } from '@constants/constants';
import { getSEOAllPage } from '@actions/page';
import isEmpty from 'lodash/isEmpty';
import ComingSoon from '@components/ComingSoon';
import ConfigCookie from '@config/ConfigCookie';
import { decodeSignature, getCookie } from '@helpers/common';
import { getRibbonDetailNotFound } from '@actions/detail';
import { isMobile } from 'react-device-detect';
import ContentNotFound from '@components/notfound/ContentNotFound';
import { getTriggerConfig } from '@actions/appConfig';
let keyBreadcrumbs = SEO_PAGES.CATEGORY;
let keyConfig = '';
const ComingSoonContainer = ({ pageProps }: any) => {
  const dispatch = useDispatch();
  const dataSEOAllPage = useSelector((state: any) => state?.Page?.dataSEOAllPage || {});
  const { activeMenu, activeSubMenu } = useSelector((state: any) => state?.Menu || {});
  const dataMenu = activeSubMenu || activeMenu;
  const { ribbonNotFound } = useSelector((state: any) => state?.Detail || {});
  useEffect(() => {
    if (!isEmpty(dataMenu?.seo?.url) && dataSEOAllPage?.seo?.slug !== dataMenu?.seo?.url) {
      dispatch(
        getSEOAllPage({
          slug: dataMenu?.seo?.url,
          keyBreadcrumbs,
          keyConfig
        })
      );
    }
  }, [dataMenu?.seo?.url, dataSEOAllPage?.seo?.slug]);
  useEffect(() => {
    dispatch(getTriggerConfig());
  }, []);
  if (pageProps?.is410 || isMobile) {
    return <ContentNotFound dataRibbon={{ 0: ribbonNotFound || [] }} />;
  }
  return (
    <>
      <ComingSoon />
      <SeoAllPage {...dataSEOAllPage} />
    </>
  );
};
ComingSoonContainer.getInitialProps = async ({ store, req, res }: any) => {
  if (req) {
    const origin = req?.headers?.host || '';
    // get menu from store redux
    const { Menu, App } = store.getState() || {};
    const isGlobal = App?.geoCheck?.isGlobal;
    const accessToken = App?.accessToken || App?.token;
    const cookie = req?.cookies;
    const keySignature = ConfigCookie.KEY.SIGNATURE;
    const { profileToken } =
      decodeSignature({
        value: cookie?.[keySignature] || getCookie(keySignature, req?.headers.cookie) || ''
      }) || {};
    const { activeMenu, menuList, activeSubMenu } = Menu;
    const dataMenu = activeSubMenu || activeMenu;
    const userAgent = req.headers['user-agent'];
    const ipAddress =
      (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '').split(':').pop() || '';
    let firstRibbonPath = '';
    let is410 = false;
    if (!isEmpty(dataMenu)) {
      const pageSlug = dataMenu?.dataSlug || dataMenu?.seo?.url;
      let seoPageSlug = dataMenu?.seo?.url; // SEO slug must be slug on url
      if (pageSlug && pageSlug[pageSlug.length - 1] !== '/') {
        // response 410 with url not have '/' at last
        is410 = true;
        res.status(410);
        await store.dispatch(
          getRibbonDetailNotFound({
            accessToken,
            profileToken,
            ssr: true,
            isGlobal,
            origin
          })
        );
      }
      await store.dispatch(
        getSEOAllPage({
          slug: seoPageSlug,
          keyBreadcrumbs,
          keyConfig,
          origin
        })
      );
    } else if (menuList?.length && menuList?.length > 0) {
      is410 = true;
      res.status(410);
      await store.dispatch(
        getRibbonDetailNotFound({
          accessToken,
          profileToken,
          ssr: true,
          ipAddress,
          userAgent,
          isGlobal,
          origin
        })
      );
    }
    return {
      ssr: true,
      firstRibbonPath,
      is410
    };
  }
  return {};
};
export default ComingSoonContainer;
