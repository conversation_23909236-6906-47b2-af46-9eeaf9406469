import React, { useEffect, useMemo, useRef, useState } from 'react';
import StepOne from '@components/tpbank/step-1';
import StepTwo from '@components/tpbank/step-2';
import ConfigImage from '@config/ConfigImage';
import TpbankButton from '@components/tpbank/tpbank-button';
import StepThree from '@components/tpbank/step-3';
import StepFour from '@components/tpbank/step-4';
import { useVieRouter } from '@customHook';
import { PAGE, HTTP_CODE } from '@constants/constants';
import { useDispatch, useSelector } from 'react-redux';
import {
  ACTION_TPBANK_UPDATECONFIG,
  checkAccountExist,
  createAccountByPhone,
  loginByPhone,
  getBillingPackage,
  createTransaction
} from '@actions/tpbank';
import FormAlert from '@components/tpbank/form-alert';
import useLanguage from '@components/tpbank/useLangage';
import { createTimeout, detectIphoneX } from '@helpers/common';
import Head from 'next/head';

declare const partnerHandler: any;
declare const window: any;

const TpbankContainer = () => {
  const dispatch = useDispatch();
  const tpbankSelector = useSelector((state: any) => state?.Tpbank ?? {});
  const [step, setStep] = useState(1);
  const [phoneNumber, setPhoneNumber] = useState<any>('');
  const [password, setPassword] = useState<any>('');
  const router = useVieRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [warningType, setWarningType] = useState<any>('');
  const [warningMessage, setWarningMessage] = useState<any>('');
  const timer = useRef<any>(null);
  const [isIphoneX, setIsIphoneX] = useState(false);
  const [isErrPassword, setIsErrPassword] = useState(0);

  const [getString]: any = useLanguage();
  // step 3
  const [packageSelected, setPackageSelected] = useState<any>({});

  // step4`
  const [paymentSelected, setPaymentSelected] = useState<any>({});

  const tpbankUserInfo = useMemo(() => tpbankSelector?.userInfo || {}, [tpbankSelector]);

  const tpbankConfig = useMemo(() => tpbankSelector?.config || {}, [tpbankSelector]);
  const [packageList, setPackageList] = useState([]);
  useEffect(() => {
    if (packageList.length && !packageSelected?.id) {
      setPackageSelected(packageList[0]);
    }
  }, [packageList]);
  const paymentList = useMemo(() => {
    const list = packageSelected?.items ?? [];
    if (list.length) {
      setPaymentSelected(list[0]);
    }
    return list;
  }, [packageSelected]);
  const handleClickStep = () => {
    if (isLoading) return;
    switch (step) {
      // case 1: handleStepOne(); break;
      case 2:
        handleStepTwo();
        break;
      case 3:
        handleStepTree();
        break;
      case 4:
        handleStepFour();
        break;
      case 5:
        handleStepFive();
        break;
      default:
        break;
    }
  };

  const handleStepFive = () => {
    router.push({ pathname: PAGE.PAYMENT_TPBANK_SLUG }, { pathname: `/tpbank/step-1` });
  };
  // checkPhonenumer
  const handleStepOne = () => {
    timer.current = createTimeout(() => setIsLoading(true), 200);
    dispatch(checkAccountExist(phoneNumber, handleCheckNumber));
  };
  const handleStepTwo = () => {
    // Push next step
    timer.current = createTimeout(() => setIsLoading(true), 200);
    dispatch(loginByPhone(phoneNumber, password, handleLoginResult));
  };
  const handleStepTree = () => {
    // Push next step
    router.push({ pathname: PAGE.PAYMENT_TPBANK_SLUG }, { pathname: `/tpbank/step-4` });
  };
  const handleStepFour = () => {
    // Push next step
    setWarningType('');
    const dataLocal = partnerHandler.getDataUI();
    const data = {
      userId: tpbankUserInfo?.user_id ?? tpbankUserInfo?.profile?.id ?? '',
      packageId: paymentSelected?.id ?? '',
      userTokenTpbank: dataLocal?.userToken ?? '',
      diviceCodeTpBank: dataLocal?.deviceCode ?? '',
      platform: dataLocal?.platform ?? '',
      promotionCode: ''
    };
    timer.current = createTimeout(() => setIsLoading(true), 200);
    dispatch(createTransaction(data, handleCreateTracsactionResult));
  };
  // step 1
  const handleCheckNumber = ({ data, httpCode, message, statusText, success }: any) => {
    // Push next step
    if (success) {
      if (data?.exist === 1) {
        if (timer.current) {
          clearTimeout(timer.current);
          timer.current = null;
        }
        setIsLoading(false);
        router.push({ pathname: PAGE.PAYMENT_TPBANK_SLUG }, { pathname: `/tpbank/step-2` });
      } else {
        dispatch(createAccountByPhone(phoneNumber, handleCreateAccountByPhone));
      }
    } else {
      if (timer.current) {
        clearTimeout(timer.current);
        timer.current = null;
      }
      setIsLoading(false);
      setWarningType('bottom');
      if (httpCode === HTTP_CODE.TOO_MANY_REQUEST) {
        setWarningMessage(getString('errorManyRequest'));
      } else {
        setWarningMessage(
          message?.length ? message : statusText?.length ? statusText : getString('errorDefault')
        );
      }
    }
  };
  // step 1
  const handleCreateAccountByPhone = ({ httpCode, message, statusText, success }: any) => {
    if (success) {
      router.push({ pathname: PAGE.PAYMENT_TPBANK_SLUG }, { pathname: `/tpbank/step-3` });
    } else {
      setWarningType('bottom');
      if (httpCode === HTTP_CODE.TOO_MANY_REQUEST) {
        setWarningMessage(getString('errorManyRequest'));
      } else {
        setWarningMessage(
          message?.length ? message : statusText?.length ? statusText : getString('errorDefault')
        );
      }
    }
    if (timer.current) {
      clearTimeout(timer.current);
      timer.current = null;
    }
    setIsLoading(false);
  };

  // step 2 login
  const handleLoginResult = ({ data, httpCode, message, success }: any) => {
    setIsErrPassword(0);
    if (success) {
      router.push({ pathname: PAGE.PAYMENT_TPBANK_SLUG }, { pathname: `/tpbank/step-3` });
    } else if (httpCode === HTTP_CODE.FAIL) {
      if (data?.message === 'Đăng nhập quá 5 lần, vui lòng thử lại sau 5 phút.') {
        setIsErrPassword(1); // Lỗi đăng nhập nhiều lần
      } else {
        setIsErrPassword(2); // Sai mật khẩu
      }
    } else {
      setIsErrPassword(0);
      setWarningType('bottom');
      const msgText = () => {
        switch (httpCode) {
          case HTTP_CODE.BLOCKED_ACCOUNT_423:
          case HTTP_CODE.BLOCKED_ACCOUNT:
            return getString('errorBlockAccount');
          case HTTP_CODE.UNAUTHORIZED:
            return getString('errorUnauthorized');
          case HTTP_CODE.TOO_MANY_REQUEST:
            return getString('errorManyRequest');
          default:
            return `${getString('errorDefault')}: ${message}`;
        }
      };
      setWarningMessage(msgText);
    }
    if (timer.current) {
      clearTimeout(timer.current);
      timer.current = null;
    }
    setIsLoading(false);
  };

  // const step 4
  const handleCreateTracsactionResult = ({ data, message, success }: any) => {
    if (timer.current) {
      clearTimeout(timer.current);
      timer.current = null;
    }
    setIsLoading(false);
    const getStrDurationType = (str: any, number: any) => {
      switch (str) {
        case 'years':
          return getString(`labelYear${number && number > 1 ? 's' : ''}`);
        case 'months':
          return getString(`labelMonth${number && number > 1 ? 's' : ''}`);
        case 'months_end_day':
          return getString(`labelMonth${number && number > 1 ? 's' : ''}`);
        case 'days':
          return getString(`labelDay${number && number > 1 ? 's' : ''}`);
        case 'hours':
          return getString(`labelHours${number && number > 1 ? 's' : ''}`);
        default:
          return '';
      }
    };
    if (success && partnerHandler) {
      partnerHandler.updateDataForNative({
        checksum: data?.tpBankData?.responseBody?.checksum || null,
        userToken: data?.tpBankData?.responseBody?.userToken || null,
        vendorID: data?.vieONData?.vendorID || null,
        transactionID: data?.tpBankData?.responseBody?.id || null,
        providerCode: data?.vieONData?.providerCode || null,
        serviceCode: data?.vieONData?.serviceCode || null,
        invoice: data?.vieONData?.invoiceID || null,
        amount: data?.vieONData?.amount || null,
        transactionDesc: data?.vieONData?.transactionDesc || null,
        currency: data?.vieONData?.currency || null,
        transactionDate: data?.vieONData?.transactionDateTPBank || null,
        viewInfo: [
          {
            title: getString('labelPackageInfo'),
            contents: [
              {
                field: getString('labelAccount'),
                value: customerName
              },
              {
                field: getString('labelPackage'),
                value: packageSelected?.name
              },
              {
                field: getString('labelTime'),
                value: `${paymentSelected?.duration} ${getStrDurationType(
                  paymentSelected?.duration_type,
                  paymentSelected?.duration
                )} ${+paymentSelected?.recurring === 1 ? getString('autoRecurring') : ''}`
              }
            ]
          }
        ]
      });
      partnerHandler.sendDataForNative(window, 'sendDataForNative');
    } else {
      setWarningType('top');
      const msgErr = (message.length ? `${message} ` : '') + (data?.message || 'Error');
      setWarningMessage(msgErr.length ? msgErr : getString('errorDefault'));
    }
  };

  useEffect(() => {
    const slug = router?.query?.slug || 'step-1';
    switch (slug) {
      case 'step-1':
        setStep(1);
        break;
      case 'step-2':
        setStep(2);
        break;
      case 'step-3':
        setStep(3);
        break;
      case 'step-4':
        setStep(4);
        break;
      case 'step-5':
        setStep(5);
        break;
      default:
        break;
    }
    setWarningMessage('');
    setWarningType('');
  }, [router.asPath]);
  function getMobileOperatingSystem() {
    if (navigator || window) {
      const userAgent = navigator?.userAgent ?? navigator?.vendor ?? window.opera ?? '';
      if (/android/i.test(userAgent)) {
        return 'android';
      }
      // iOS detection from: http://stackoverflow.com/a/9039885/177710
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return 'ios';
      }
    }
    return 'unknown';
  }
  useEffect(() => {
    const languageText = router?.query?.language || 'VN';
    const themeText = router?.query?.theme || 'light';
    let dataLocalParse: any = {};
    if (typeof localStorage !== 'undefined') {
      const dataLocal = localStorage.getItem('tpbankDataUI');
      if (dataLocal) {
        dataLocalParse = JSON.parse(dataLocal);
      }
    }
    let platform = dataLocalParse?.platform || '';
    if (platform === '') {
      platform = getMobileOperatingSystem();
    }
    dispatch({
      type: ACTION_TPBANK_UPDATECONFIG,
      data: { language: languageText, theme: themeText, platform: platform.toLowerCase() }
    });
  }, []);

  useEffect(() => {
    if (step === 3) {
      dispatch(
        getBillingPackage(({ data, success }: any) => {
          if (success) {
            setPackageList(data?.items ?? []);
          }
        })
      );
    }
  }, [step]);

  const isDisableButton = useMemo(() => {
    if (step === 2 && password.length < 6) {
      return true;
    }
    return false;
  }, [password, step]);

  function injectDebugScript(key: any) {
    const isMyScriptLoaded = (() => {
      const URL = `https://checking-tv.vieon.vn/target/target-script-min.js`;
      const scripts: any = document.getElementsByTagName('script');
      for (let i = scripts.length; i--; ) {
        if (scripts[i].src?.includes(URL)) {
          return true;
        }
      }
      return false;
    })();

    if (!isMyScriptLoaded) {
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://checking-tv.vieon.vn/target/target-script-min.js${key}`;
      document.head.appendChild(script);
    }
  }

  useEffect(() => {
    const reg = new RegExp('(84|0[3|5|7|8|9])+([0-9]{8})\\b', 'g');
    if (phoneNumber.length && reg.test(phoneNumber) && step === 1) {
      handleStepOne();
    } else {
      // open debug
      if (phoneNumber.includes('#') && phoneNumber.length === 4) {
        injectDebugScript(phoneNumber);
      }
    }
  }, [phoneNumber]);

  useEffect(() => {
    if (document) {
      const eleHead = document.getElementById('html-head');
      if (eleHead) {
        eleHead.classList.remove('theme-dark');
        eleHead.classList.add('theme-light-tpbank');
      }
    }
    setIsIphoneX(detectIphoneX());
  }, []);

  const handleBack = () => {
    if (step === 1) {
      partnerHandler.sendDataForNative(window, 'onCloseView');
    } else {
      router.back();
    }
  };

  const handleCloseApp = () => {
    if (partnerHandler) {
      partnerHandler.sendDataForNative(window, 'onCloseView');
    }
  };

  const customerName = useMemo(
    () => tpbankUserInfo?.profile?.given_name || phoneNumber,
    [tpbankUserInfo]
  );
  return (
    <>
      <Head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      <div className="header overflow">
        <div
          className={`top-bar top-bar-tpbank ${tpbankConfig?.theme?.toLowerCase() ?? ''} ${
            tpbankConfig?.platform?.toLowerCase() ?? 'android'
          } ${isIphoneX ? 'ios-x' : ''}`}
        >
          <button className="button" type="button" onClick={handleBack}>
            <span className="icon">
              {(tpbankConfig?.platform?.toLowerCase() ?? '') === 'ios' ? (
                <i className="vie vie-arrow-left-rc-light" />
              ) : (
                <i className="vie vie-arrow-left-rc-medium" />
              )}
            </span>
          </button>
          <h2 className="title">{getString('vieonTitle')}</h2>
          <button className="button" type="button" onClick={handleCloseApp}>
            <span className="icon">
              {(tpbankConfig?.platform?.toLowerCase() ?? '') === 'ios' ? (
                <i className="vie vie-times-rc-light" />
              ) : (
                <i className="vie vie-times-medium" />
              )}
            </span>
          </button>
        </div>
      </div>
      <main className="main">
        <section
          className={`section section--payment section--payment-tpbank ${
            tpbankConfig?.theme?.toLowerCase() ?? ''
          } ${tpbankConfig?.platform?.toLowerCase() ?? 'android'} ${isIphoneX ? 'ios-x' : ''}`}
        >
          <div className="section__body">
            {warningType === 'top' && <FormAlert message={warningMessage} />}
            {step === 1 && <StepOne phoneNumber={phoneNumber} setPhoneNumber={setPhoneNumber} />}
            {step === 2 && (
              <StepTwo
                isErrorPassword={isErrPassword}
                phoneNumber={phoneNumber}
                password={password}
                setPassword={setPassword}
              />
            )}
            {step === 3 && (
              <StepThree
                packageList={packageList}
                phoneNumber={phoneNumber}
                customerName={customerName}
                isNewUser={tpbankUserInfo?.exist !== 1}
                packageSelected={packageSelected}
                setPackageSelected={setPackageSelected}
              />
            )}
            {step === 4 && (
              <StepFour
                packageName={packageSelected?.name ?? ''}
                paymentList={paymentList}
                paymentSelected={paymentSelected}
                setPaymentSelected={setPaymentSelected}
              />
            )}
            {warningType === 'bottom' && <FormAlert message={warningMessage} />}
            {step !== 1 && <TpbankButton disabled={isDisableButton} onClick={handleClickStep} />}
          </div>
          {isLoading && (
            <div className="loading-tpbank">
              <img src={ConfigImage.tpbankLoading} alt="loading TPBank" />
            </div>
          )}
        </section>
      </main>
    </>
  );
};
export default TpbankContainer;
