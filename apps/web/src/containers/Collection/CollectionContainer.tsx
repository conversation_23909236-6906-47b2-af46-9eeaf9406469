import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { setLoadedData } from '@actions/app';
import { getDataRibbons, getDataRibbonsId, getSEOAllPage } from '@actions/page';
import { getRibbonDetailNotFound } from '@actions/detail';
import { decodeSignature, getCookie, getPathFromUrl, parseQueryString } from '@helpers/common';
import dynamic from 'next/dynamic';
import { pageView } from '@tracking/functions/TrackingApp';
import BannerBillboard from '@components/home/<USER>';
import ContentNotFound from '@components/notfound/ContentNotFound';
import CardImage from '@components/basic/Card/CardImage';
import {
  HTTP_CODE,
  LIMIT_ITEMS_RIBBON_FOR_SHOW,
  PAGE,
  RIBBON_TYPE,
  SEO_PAGES
} from '@constants/constants';
import SeoAllPage, { redirectTool } from '@components/seo/SeoAllPage';
import ConfigCookie from '@config/ConfigCookie';

const NotFound = dynamic(import('@components/notfound/NotFound'));
const ContentList = dynamic(import('@components/basic/ContentList/ContentList'));
const ContentCollection = dynamic(import('@components/basic/ContentList/ContentCollection'));
const stateInit = {
  listDataAllPage: null,
  slug: '',
  asPath: '',
  metadata: {
    page: 0,
    limit: 50,
    total: null
  },
  isLoadMore: true
};
let keyBreadcrumbs = SEO_PAGES.COLLECTION_DETAIL;

class CollectionContainer extends PureComponent {
  constructor(props: any) {
    super(props);

    this.state = stateInit;
  }

  static async getInitialProps({ store, req, asPath, query, res }: any) {
    if (req) {
      // call in server
      const { App } = store.getState();
      const isGlobal = App?.geoCheck?.isGlobal;
      const { slug } = query || req.params;
      let renderNotFoundPage = false;
      const origin = req?.headers?.host || '';
      if (slug) {
        let ribbonSlug = req?._parsedOriginalUrl?.pathname || getPathFromUrl(asPath);
        const userAgent = req.headers['user-agent'];
        const ipAddress = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '')
          .split(':')
          .pop();
        const { metadata } = stateInit;
        const { page, limit } = metadata;
        const isMobile = !!App?.isMobile;
        const { mwebToApp, featureFlag } = App?.webConfig || {};
        const accessToken = App?.accessToken || App?.token;

        const cookie = req?.cookies;
        const keySignature = ConfigCookie.KEY.SIGNATURE;
        const { profileToken } =
          decodeSignature({
            value: cookie?.[keySignature] || getCookie(keySignature, req?.headers.cookie) || ''
          }) || {};

        const isRecommendRib = asPath.indexOf('?id=') > -1 && query?.id;
        let dataContent = null;
        if (isRecommendRib) {
          const id = query?.id;
          ribbonSlug = asPath;
          dataContent = await getDataRibbonsId({
            id,
            page,
            limit,
            accessToken,
            profileToken,
            ssr: true,
            ipAddress,
            isMobile,
            userAgent,
            origin,
            isGlobal
          });
        } else {
          dataContent = await getDataRibbons({
            ribbonSlug,
            page,
            limit,
            accessToken,
            profileToken,
            ssr: true,
            ipAddress,
            userAgent,
            origin,
            isMWebToApp: featureFlag?.mwebToApp,
            imageMWebToApp: isMobile
              ? mwebToApp?.onlyAppImageMWeb || mwebToApp?.onlyAppImage
              : mwebToApp?.onlyAppImage,
            isMobile,
            isGlobal
          });
        }

        await store.dispatch(dataContent).then(async (response: any) => {
          let uncheckURL = true;
          if (response?.data) {
            if (response?.data?.type === RIBBON_TYPE.COLLECTION) {
              keyBreadcrumbs = SEO_PAGES.COLLECTION;
            }
            if (isRecommendRib) uncheckURL = false;
            await store
              .dispatch(
                getSEOAllPage({
                  slug: ribbonSlug,
                  keyBreadcrumbs,
                  ssr: true,
                  uncheckURL,
                  ipAddress,
                  origin,
                  userAgent
                })
              )
              .then(async (resp: any) => {
                const redirect = resp?.data?.redirect;
                redirectTool({ redirect, res });
                if (
                  (response.data && response.data.error === 400) ||
                  !response?.data?.id ||
                  response?.data?.items?.length === 0
                ) {
                  if (isRecommendRib) {
                    res.status(410);
                  } else if (redirect?.http_status === HTTP_CODE.OK_200) res.status(404);
                  const ribbonDetail = await getRibbonDetailNotFound({
                    accessToken,
                    profileToken,
                    ssr: true,
                    ipAddress,
                    userAgent,
                    isGlobal,
                    origin
                  });
                  await store.dispatch(ribbonDetail);
                  renderNotFoundPage = true;
                }
              });
          }
        });
      }
      return {
        renderNotFoundPage,
        ssr: true
      };
    }
    return {};
  }

  componentDidMount() {
    const { router, setLoadedData, dataSEOAllPage, getSEOAllPage }: any = this.props;
    const asPath = router?.asPath;
    const isRecommendRib = (asPath || '').indexOf('?id=') !== -1;
    setLoadedData(true);
    this.fetchData();
    // GA PAGE VIEW
    pageView(this.getSeoData());
    if (dataSEOAllPage?.seo?.slug !== asPath) {
      let uncheckURL = true;
      if (isRecommendRib) uncheckURL = false;
      getSEOAllPage({ slug: asPath, keyBreadcrumbs, uncheckURL });
    }
    this.setState({ asPath: router.asPath });
    window.scrollTo({ left: 0, top: 0, behavior: 'smooth' });
  }

  componentDidUpdate() {
    const { router }: any = this.props;
    const { asPath }: any = this.state;
    const newAsPath = router?.asPath;
    if (asPath && asPath !== newAsPath) {
      this.fetchData();
      this.setState({ asPath: newAsPath });
    }
  }

  handleRecommendRibbon = () => {
    const { router }: any = this.props;
    const { query }: any = router || {};
    let id = query?.id;
    if (!query?.id && typeof window !== 'undefined') {
      id = parseQueryString(window.location.search)?.id;
    }
    const isRibbonRecommend = !!id;

    return { id, isRibbonRecommend };
  };

  getDataRender = () => {
    const { router, profile, subHeader, dataSEOAllPage, ribbonData }: any = this.props;
    const { metadata, dataRibbons }: any = this.state;
    const { id, isRibbonRecommend }: any = this.handleRecommendRibbon();
    let ribbonSlug = getPathFromUrl(router.asPath);
    let title = '';
    let listDataAllPage = dataRibbons;
    if (ribbonSlug && ribbonSlug[ribbonSlug.length - 1] !== '/') ribbonSlug += '/';
    if (isRibbonRecommend) {
      ribbonSlug += `?id=${id}`;
    }
    if (!listDataAllPage) {
      listDataAllPage = ribbonData && ribbonData[ribbonSlug] ? ribbonData[ribbonSlug] : null;
    }

    if (listDataAllPage) {
      title = listDataAllPage ? dataSEOAllPage?.seo?.title || listDataAllPage?.name : '';
    }
    const seoData = dataSEOAllPage?.seo;
    return { listDataAllPage, title, metadata, seoData, profile, subHeader };
  };

  fetchData = () => {
    const {
      router,
      ribbonData,
      webConfig,
      isMobile,
      PageAction,
      getDataRibbonsId,
      getDataRibbons,
      geoCheck
    }: any = this.props;
    const { featureFlag, mwebToApp } = webConfig || {};
    const { metadata, slug }: any = this.state;
    const routerSlug = getPathFromUrl(router.asPath);
    const { id, isRibbonRecommend }: any = this.handleRecommendRibbon();
    let ribbonSlug = slug;
    if (slug !== routerSlug) ribbonSlug = routerSlug;
    if (isRibbonRecommend) ribbonSlug += `?id=${id}`;
    const { limit } = metadata || {};
    const page = 0;
    // check data from props
    const data = PageAction?.ACTION_GET_DATA_RIBBONS || ribbonData;
    let dataRibbonsNew = data && data[ribbonSlug] && data[ribbonSlug] ? data[ribbonSlug] : null;
    // check data from redux
    const { total } = dataRibbonsNew?.metadata || {};
    if (
      !dataRibbonsNew ||
      (dataRibbonsNew?.items?.length || 0) === 0 ||
      (total > dataRibbonsNew?.items?.length &&
        dataRibbonsNew?.items?.length < stateInit.metadata.limit &&
        dataRibbonsNew?.metadata?.page === 0) ||
      (isRibbonRecommend &&
        !dataRibbonsNew &&
        dataRibbonsNew?.items?.length < stateInit.metadata.limit &&
        dataRibbonsNew?.metadata?.page === 0) ||
      (total > dataRibbonsNew?.items?.length &&
        dataRibbonsNew?.items?.length === LIMIT_ITEMS_RIBBON_FOR_SHOW)
    ) {
      if (isRibbonRecommend) {
        getDataRibbonsId({ id, page, limit, isGlobal: geoCheck?.isGlobal }).then((res: any) => {
          if (res) {
            dataRibbonsNew = res?.data || {};
            const { metadata } = dataRibbonsNew;
            this.setState({
              dataRibbons: dataRibbonsNew,
              metadata,
              slug: ribbonSlug
            });
          }
        });
      } else {
        getDataRibbons({ ribbonSlug, page, limit, isGlobal: geoCheck?.isGlobal }).then(
          (res: any) => {
            if (res) {
              dataRibbonsNew = res?.data || {};
              const { metadata } = dataRibbonsNew;
              this.setState({
                dataRibbons: dataRibbonsNew,
                metadata,
                slug: ribbonSlug
              });
            }
          }
        );
      }
    } else if (
      dataRibbonsNew?.type === RIBBON_TYPE.CONVERT_MOBILE_WEB_TO_APP &&
      featureFlag?.mwebToApp &&
      isMobile
    ) {
      getDataRibbons({
        ribbonSlug,
        page,
        limit,
        isMWebToApp: featureFlag?.mwebToApp,
        imageMWebToApp: isMobile
          ? mwebToApp?.onlyAppImageMWeb || mwebToApp?.onlyAppImage
          : mwebToApp?.onlyAppImage,
        isMobile,
        isGlobal: geoCheck?.isGlobal
      }).then((res: any) => {
        if (res) {
          dataRibbonsNew = res?.data || {};
          const { metadata } = dataRibbonsNew;
          this.setState({
            dataRibbons: dataRibbonsNew,
            metadata,
            slug: ribbonSlug
          });
        }
      });
    } else {
      // set state data
      this.setState({
        dataRibbons: dataRibbonsNew,
        metadata: dataRibbonsNew.metadata,
        slug: ribbonSlug
      });
    }
  };

  getSeoData = () => {
    const { router, ribbonData }: any = this.props;
    let prefixPath = getPathFromUrl(router?.asPath);
    if (prefixPath && prefixPath[prefixPath.length - 1] !== '/') prefixPath += '/';
    // check data from props
    const data = ribbonData;
    const { isRibbonRecommend } = this.handleRecommendRibbon();
    if (isRibbonRecommend) prefixPath = router?.asPath;
    const dataRibbon = data && data[prefixPath] && data[prefixPath] ? data[prefixPath] : null;
    const listArrRibbon = (dataRibbon?.items || []).map((item: any) => ({
      id: item.id,
      imageURL: item?.images?.poster || item?.images?.thumbnail || '',
      title: item?.seo?.title,
      rating: item?.avgRate || 5,
      description: item?.description,
      totalRating: item?.totalRate,
      url: item?.seo?.url
    }));
    const dataSeo = {
      page: PAGE.COLLECTION,
      data: dataRibbon || null,
      ...dataRibbon?.seo,
      prefixPath,
      listArrRibbon
    };
    return dataSeo;
  };

  onScrollDown = () => {
    const { slug, metadata, dataRibbons, isLoadMore }: any = this.state;
    const { token, router, getDataRibbonsId, getDataRibbons, geoCheck }: any = this.props;
    const { id, isRibbonRecommend } = this.handleRecommendRibbon();
    const { page, limit } = metadata;
    const nextPage = page + 1;
    const accessToken = token;
    if (!isLoadMore) return;
    if (isRibbonRecommend) {
      getDataRibbonsId({
        id,
        page: nextPage,
        limit: limit || stateInit.metadata.limit,
        accessToken,
        isGlobal: geoCheck?.isGlobal
      }).then((res: any) => {
        if (res && res.data) {
          if (res?.data?.emptyItem) {
            this.setState({ isLoadMore: false });
            return;
          }
          const dataRibbonDetail = res.data;
          const dataItems = dataRibbons?.items || [];
          dataRibbonDetail.items = [...dataItems, ...res?.data?.items];
          this.setState((prevState: any) => ({
            dataRibbons: dataRibbonDetail,
            metadata: {
              ...prevState.metadata,
              page: page + 1
            },
            asPath: router.asPath
          }));
        }
      });
    } else {
      getDataRibbons({
        ribbonSlug: slug,
        page: nextPage,
        limit,
        accessToken,
        isGlobal: geoCheck?.isGlobal
      }).then((res: any) => {
        if (res && res.data) {
          if (res?.data?.emptyItem) {
            this.setState({ isLoadMore: false });
            return;
          }
          const dataRibbonDetail = res.data;
          const dataItems = dataRibbons?.items || [];
          dataRibbonDetail.items = [...dataItems, ...res?.data?.items];
          this.setState({
            dataRibbons: dataRibbonDetail,
            metadata: dataRibbonDetail?.metadata,
            asPath: router.asPath
          });
        }
      });
    }
  };

  render() {
    const { pageProps, dataSEOAllPage, ribbonNotFound }: any = this.props;
    const dataRender = this.getDataRender();
    const seoData = this.getSeoData();
    const { listDataAllPage } = dataRender;
    const dataType = listDataAllPage?.type;
    const dataImages = listDataAllPage?.images;
    const isCollectionChild = dataImages?.carouselNTC && dataImages?.carousel !== '';
    if (pageProps?.renderNotFoundPage) {
      return <ContentNotFound dataRibbon={{ 0: ribbonNotFound || [] }} />;
    }
    if (!listDataAllPage) {
      return null;
    }
    if (!listDataAllPage?.items?.length) {
      return <NotFound />;
    }
    if (dataType === 7) {
      return (
        <>
          <ContentCollection {...dataRender} {...pageProps} onScrollDown={this.onScrollDown} />
          <SeoAllPage {...dataSEOAllPage} listArrRibbon={seoData?.listArrRibbon} />
        </>
      );
    }
    let collectionClass = 'section section--collection canal-v !py-4 md:!py-6 overflow';
    if (isCollectionChild) collectionClass += ' section--collection-detail';
    return (
      <>
        <section className={collectionClass}>
          {isCollectionChild && (
            <>
              <div className="mask mask--collection absolute">
                <div className="mask__inner">
                  <CardImage
                    images={listDataAllPage?.images}
                    className="mask__img"
                    isMasterBanner
                  />
                </div>
              </div>
              <BannerBillboard
                isCollectionBanner
                classBanner="billboard--collection"
                dataBanner={listDataAllPage}
              />
            </>
          )}
          <div className="container">
            <ContentList
              {...dataRender}
              {...pageProps}
              onScrollDown={this.onScrollDown}
              isRibbonCollectionChild={isCollectionChild}
            />
          </div>
        </section>
        <SeoAllPage {...dataSEOAllPage} listArrRibbon={seoData?.listArrRibbon} />
      </>
    );
  }
}

const mapStateToProps = ({ App, Menu, Page, Detail, Profile }: any) => ({
  ...App,
  ...Menu,
  ...Page,
  ...Detail,
  ...Profile
});

const mapDispatchToProps = (dispatch: any) => {
  const actions = bindActionCreators(
    {
      setLoadedData: setLoadedData,
      getDataRibbons: getDataRibbons,
      getDataRibbonsId: getDataRibbonsId,
      getRibbonDetailNotFound: getRibbonDetailNotFound,
      getSEOAllPage: getSEOAllPage
    },
    dispatch
  );
  return { ...actions, dispatch };
};

export default connect(mapStateToProps, mapDispatchToProps)(CollectionContainer);
