/* eslint-disable class-methods-use-this */
/* eslint-disable react/destructuring-assignment */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { setLoadedData } from '@actions/app';
import {
  ACTION_GET_INFO_ARTIST,
  ACTION_GET_CONTENT_ARTIST,
  ACTION_GET_ARTIST_RELATED,
  getInfoArtist,
  getContentArtist,
  getArtistRelated
} from '@actions/artist';
import { getContentPopver } from '@actions/detail';
import { checkIsEndPage, decodeSignature, getCookie, handleScrollTop } from '@helpers/common';
import { getListOrderArtist } from '@helpers/settings';
import dynamic from 'next/dynamic';
import HelmetTag from '@components/seo/Helmet';
import { pageView } from '@tracking/functions/TrackingApp';
import { PAGE } from '@constants/constants';
import ConfigCookie from '@config/ConfigCookie';
import isEmpty from 'lodash/isEmpty';

const NotFound = dynamic(import('@components/notfound/NotFound'));
const Artist = dynamic(import('@components/artist/Artist'));

const stateInit = {
  slug: null,
  isLoadMore: true,
  listDataAllPage: null,
  infoArtist: null,
  listArtistRelated: null,
  metadata: {
    page: 0,
    limit: 30,
    total: null
  },
  currentSortId: 3 // ngày phát hành mới nhất
};

class ArtistContainer extends Component {
  constructor(props: any) {
    super(props);
    this.state = { ...stateInit };
  }

  static async getInitialProps({ store, req, query, res }: any) {
    if (req) {
      // call in server
      const { slug } = query || req.params;
      if (slug) {
        const { App } = store.getState();
        const accessToken = App?.token;
        const isGlobal = App?.geoCheck?.isGlobal;
        const cookie = req?.cookies;
        const keySignature = ConfigCookie.KEY.SIGNATURE;
        const userAgent = req.headers['user-agent'];
        const ipAddress = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '')
          .split(':')
          .pop();
        const { profileToken } =
          decodeSignature({
            value: cookie?.[keySignature] || getCookie(keySignature, req?.headers.cookie) || ''
          }) || {};
        const { metadata, currentSortId } = stateInit;
        const { page, limit } = metadata;
        const taskContent = getContentArtist({
          slug,
          page,
          limit,
          sort: currentSortId,
          accessToken,
          profileToken,
          ssr: true,
          ipAddress,
          userAgent,
          isGlobal
        });
        const taskInfo = getInfoArtist({ slug, ssr: true, ipAddress, userAgent });
        const [dataContent, dataInfo] = await Promise.all([taskContent, taskInfo]);
        await store.dispatch(dataInfo).then(async (response: any) => {
          if (response?.payload?.httpCode === 400) {
            res.status(404);
          } else {
            await store.dispatch(dataContent);
          }
        });
      }

      return {
        ssr: true
      };
    }
    return {};
  }

  componentDidMount() {
    const { setLoadedData, router }: any = this.props;
    setLoadedData(true);
    const { slug } = router.query;

    this.setState({ slug });
    this.getInfoArtist(this.props);
    this.fetchDataContent(this.props);
    this.getListArtistRelated(this.props);

    // GA PAGE VIEW
    const dataSeo = this.getSeoData(router.asPath);
    pageView(dataSeo);
    handleScrollTop();
  }

  componentDidUpdate(prevProps: any, prevState: any) {
    const { metadata, currentSortId, slug }: any = this.state;
    const { router }: any = this.props;
    const newSlug = router.query.slug;

    if (slug !== newSlug) {
      this.setState(
        (prevState) => ({
          ...prevState,
          slug: newSlug,
          metadata: stateInit.metadata,
          currentSortId: stateInit.currentSortId
        }),
        () => {
          Promise.all([
            this.fetchDataContent(this.props),
            this.getInfoArtist(this.props),
            this.getListArtistRelated(this.props)
          ]);
        }
      );
    }

    // if change active or page => fetch data api
    if (metadata?.page !== prevState?.metadata?.page || currentSortId !== prevState.currentSortId) {
      this.fetchDataContent(this.props);
    }
  }

  getDataRender = (props?: any) => {
    props = typeof props === 'undefined' ? this.props : props;
    const { slug } = props.router.query;
    let { listDataAllPage, infoArtist, listArtistRelated, isLoadMore, currentSortId }: any =
      this.state;
    const dataCurrentSort = getListOrderArtist(currentSortId);
    const titlePage = 'NGHỆ SĨ';
    // if empty data state try get data from redux
    listDataAllPage = this.getDataContentFromRedux({ props, slug, currentSortId });

    // if empty infoArtist try get data from redux
    if (!infoArtist) {
      infoArtist = this.getInfoArtistFromRedux({ props, slug });
    }

    // if empty infoArtist try get data from redux
    if (!listArtistRelated) {
      listArtistRelated = this.getArtistRelatedFromRedux({ props, slug });
    }
    const seoData = listDataAllPage?.[0]?.seo;

    return {
      listDataAllPage,
      titlePage,
      isLoadMore,
      currentSortId,
      dataCurrentSort,
      listArtistRelated,
      infoArtist,
      seoData
    };
  };

  getSeoData: any = (infoArtist: any, prefixPath: any) => {
    // check data from props
    const dataSeo = {
      page: PAGE.ARTIST,
      data: infoArtist || null,
      prefixPath
    };
    return dataSeo;
  };

  fetchDataContent = (props: any) => {
    const { slug } = props.router.query;
    let { metadata, listDataAllPage, currentSortId }: any = this.state;
    let { geoCheck }: any = this.props;
    const { page, limit } = metadata || {};
    // check data from props
    let listDataPageNew = this.getDataContentFromRedux({ props, slug, currentSortId, page });

    // check data from redux
    if (!listDataPageNew || listDataPageNew === null) {
      props
        .getContentArtist({ slug, page, limit, sort: currentSortId, isGlobal: geoCheck?.isGlobal })
        .then((res: any) => {
          if (res) {
            if (!listDataAllPage) listDataAllPage = {};
            listDataPageNew = res.payload.data || [];
            metadata = listDataPageNew.metadata;
            listDataAllPage[page] = listDataPageNew;

            // GA PAGE VIEW
            pageView(res.payload);

            // set state data
            this.setState({
              listDataAllPage,
              metadata,
              isLoadMore: !checkIsEndPage(metadata)
            });
          }
        });
    } else {
      // khởi tạo giá trị nếu chưa tồn tại
      if (!listDataAllPage) listDataAllPage = {};
      metadata = listDataPageNew.metadata;
      listDataAllPage[page] = listDataPageNew;

      // set state data
      this.setState({
        listDataAllPage,
        metadata,
        isLoadMore: !checkIsEndPage(metadata)
      });
    }
  };

  getInfoArtist = (props: any) => {
    const { slug } = props.router.query;

    // check data from props
    const infoArtist = this.getInfoArtistFromRedux({ props, slug });

    // check data from redux

    if (!infoArtist) {
      props.getInfoArtist({ slug }).then((res: any) => {
        if (res && res.payload && res.payload.data) {
          // set state data
          this.setState({ infoArtist: res.payload.data });
        }
      });
    } else {
      this.setState({ infoArtist });
    }
  };

  getListArtistRelated = (props: any) => {
    const { slug } = props.router.query || {};
    const page = 0;
    const limit = 5;

    // check data from props
    const listArtistRelated = this.getArtistRelatedFromRedux({ props, slug });

    // check data from redux
    if (!listArtistRelated) {
      props.getArtistRelated({ slug, page, limit }).then((res: any) => {
        if (res && res.payload && res.payload.data) {
          // set state data
          this.setState({ listArtistRelated: res.payload.data });
        }
      });
    } else {
      this.setState({ listArtistRelated });
    }
  };

  handleClickLoadMoreData = (event: any) => {
    event.preventDefault();
    const { metadata }: any = this.state;
    const { page, limit, total } = metadata;
    if (checkIsEndPage({ page, limit, total })) {
      this.setState({ isLoadMore: false });
      return;
    }
    // update new state page + 1
    this.setState((prevState: any) => ({
      metadata: {
        // object that we want to update
        ...prevState.metadata, // keep all other key-value pairs
        page: page + 1 // update the value of specific key
      }
    }));
  };

  handleChangeOrder = (orderId: any) => {
    this.setState((prevState) => ({
      ...prevState,
      currentSortId: orderId,
      metadata: stateInit.metadata
    }));
  };

  getDataContentFromRedux = ({ props, slug, currentSortId, page }: any) => {
    const data = props[ACTION_GET_CONTENT_ARTIST];
    let listDataAllPage = null;

    if (typeof page === 'undefined') {
      // get data all page
      listDataAllPage =
        data && data[slug] && data[slug][currentSortId]
          ? data[slug][currentSortId]
          : listDataAllPage;
    } else {
      // get data page from input
      listDataAllPage =
        data && data[slug] && data[slug][currentSortId] && data[slug][currentSortId][page]
          ? data[slug][currentSortId][page]
          : listDataAllPage;
    }
    return listDataAllPage;
  };

  getInfoArtistFromRedux = ({ props, slug }: any) => {
    const data = props[ACTION_GET_INFO_ARTIST];
    return data && data[slug] ? data[slug] : null;
  };

  getArtistRelatedFromRedux = ({ props, slug }: any) => {
    const data = props[ACTION_GET_ARTIST_RELATED];
    return data && data[slug] ? data[slug] : null;
  };

  onScrollDown = () => {
    const { metadata }: any = this.state;
    const { page } = metadata;
    const { listDataAllPage }: any = this.state;
    const lastElement: any = Object.values(listDataAllPage).pop();
    if (isEmpty(lastElement?.items)) return;
    this.setState((prevState: any) => ({
      metadata: {
        // object that we want to update
        ...prevState.metadata, // keep all other key-value pairs
        page: page + 1 // update the value of specific key
      }
    }));
    this.setState((prevState: any) => ({ page: (prevState.page || 0) + 1 }));
  };

  render() {
    const { router, ARTIST_GET_INFO }: any = this.props;
    const dataRender = this.getDataRender();
    const { slug } = router.query;
    const artistInfo = ARTIST_GET_INFO;
    if (!artistInfo) {
      return null;
    }
    if (artistInfo?.[slug] === null) {
      return <NotFound />;
    }

    const seoData = {
      data: {
        ...(artistInfo?.[slug] || {}),
        seo: dataRender.seoData
      },
      page: PAGE.ARTIST
    };

    return (
      <>
        <HelmetTag props={seoData} />
        <Artist
          {...dataRender}
          seoData={seoData}
          onScrollDown={this.onScrollDown}
          changeOrder={this.handleChangeOrder}
        />
      </>
    );
  }
}

const mapStateToProps = ({ Artist, Detail, App }: any) => ({
  ...Detail,
  ...(Artist || {}),
  ...App
});

const mapDispatchToProps = (dispatch: any) => {
  const actions = bindActionCreators(
    {
      setLoadedData: setLoadedData,
      getInfoArtist: getInfoArtist,
      getContentArtist: getContentArtist,
      getArtistRelated: getArtistRelated,
      getContentPopver: getContentPopver
    },
    dispatch
  );
  return { ...actions, dispatch };
};

// export { ArtistContainer }
export default connect(mapStateToProps, mapDispatchToProps)(ArtistContainer);
