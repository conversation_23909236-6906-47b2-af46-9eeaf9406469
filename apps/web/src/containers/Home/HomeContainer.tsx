import React, { memo, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import isArray from 'lodash/isArray';
import dynamic from 'next/dynamic';
import { setLoadedData } from '@actions/app';
import {
  getDataRibbonsId,
  getPageBanners,
  getPageRibbons,
  getSEOAllPage,
  clearRibbonData,
  setPageBannerSuccess as clearDataPageBanner
} from '@actions/page';
import { getRibbonDetailNotFound } from '@actions/detail';
import { openPopup } from '@actions/popup';
import { getSegmentedUser, getUserPackageInfo } from '@actions/user';
import { decodeSignature, getCookie, getPathFromUrl, scrollToTop } from '@helpers/common';
import LocalStorage from '@config/LocalStorage';
import MasterBanner from '@components/MasterBanner';
import SeoAllPage, { redirectTool } from '@components/seo/SeoAllPage';
import { pageView } from '@tracking/functions/TrackingApp';
import {
  HTTP_CODE,
  PAGE,
  POPUP,
  RIBBON_TYPE,
  SEO_CONFIG,
  SEO_PAGES,
  STATUS_OUTSTREAM_ADS
} from '@constants/constants';
import SeoText from '@components/seo/SeoText';
import { ItemList } from '@models/subModels';
import PaymentApi from '@apis/Payment';
import ConfigCookie from '@config/ConfigCookie';
import ListRibbons from '@components/Sections/ListRibbons';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import classNames from 'classnames';
import { getPackageDiscountOEM } from '@actions/payment';
import styles from './HomeContainer.module.scss';
import RibbonBoard from './RibbonCustomBoard';
import Image from '@components/basic/Image/Image';
import ConfigImage from '@config/ConfigImage';
import { useRibbonBoardData } from '@/hooks/useRibbonBoardData';

const FABWidget = dynamic(import('@components/home/<USER>'), {
  ssr: false
});
const ContentNotFound = dynamic(import('@components/notfound/ContentNotFound'), {
  ssr: false
});
const FullScreenBannerUserPackageInfo = dynamic(
  import('@components/home/<USER>'),
  { ssr: false }
);
const FullScreenSegmentedUser = dynamic(import('@components/home/<USER>'), {
  ssr: false
});
const TVodReminderScreen = dynamic(import('@components/home/<USER>'), { ssr: false });

let keyBreadcrumbs: any = SEO_PAGES.CATEGORY;
let keyConfig: any = '';

const TypeTrigger = memo(
  ({
    isPaymentConversion,
    profile,
    enableTVod,
    isCheckPaymentConversion,
    query,
    asPath,
    currentProfile,
    isGlobal
  }: any) => {
    if (isCheckPaymentConversion) {
      if (isPaymentConversion && currentProfile?.id) {
        return <FullScreenBannerUserPackageInfo />;
      }
      if (!isGlobal && profile?.hadTvod && !enableTVod && !query?.vid && asPath === '/') {
        return <TVodReminderScreen />;
      }
    }
    return null;
  }
);

const HomeContainer = ({ router, pageProps }: any) => {
  const dispatch = useDispatch();
  const { asPath, query } = router || {};
  const {
    isMobile,
    deviceId,
    paymentConversion,
    statusLoadOutStreamAds,
    outStreamAds,
    webConfig,
    seoText
  } = useSelector((state: any) => state?.App || {});
  const { fullScreenExpired } = paymentConversion || {};
  const { welcomeHome } = outStreamAds || {};
  const { tVod, featureFlag, mwebToApp } = webConfig || {};
  const { pageBanner, pageRibbon, ribbonData, dataSEOAllPage } = useSelector(
    (state: any) => state?.Page || {}
  );
  const [oemData, setOemData] = useState<any>({});
  const adsMasterBanner = useSelector((state: any) => state?.App?.outStreamAds?.masterBanner || []);
  const { activeMenu, activeSubMenu, menuList } = useSelector((state: any) => state?.Menu || {});
  const dataMenu = activeSubMenu || activeMenu;
  const { ribbonNotFound } = useSelector((state: any) => state?.Detail || {});
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const { hadTvod, isPremium } = profile || {};
  const { currentProfile } = useSelector((state: any) => state?.MultiProfile || {});
  const { isGlobal, restricted } = useSelector((state: any) => state?.App?.geoCheck);
  const { statusMasthead } = useSelector((state: any) => state?.App);
  const { isKid } = currentProfile || {};
  const { USER_PACKAGE_INFO, enableTVod, dataSegment } = useSelector(
    (state: any) => state?.User || {}
  );
  const { popupName } = useSelector((state: any) => state?.Popup || {});
  const { subscriptions } = USER_PACKAGE_INFO || {};
  const [isCheckPaymentConversion, setIsCheckPaymentConversion] = useState(false);
  const [isTVodOnBoarding, setIsTVodOnBoarding] = useState(false);
  const [isPaymentConversion, setIsPaymentConversion] = useState(false);
  const isTimeSegmentUser = Number.parseInt(
    ConfigLocalStorage.get(LocalStorage.DAY_SEGMENT_USER_FULL_SCREEN) as string
  );
  const pageSlug = useMemo(() => {
    let slugTemp = dataMenu?.dataSlug || dataMenu?.seo?.url;
    if (typeof window !== 'undefined') {
      slugTemp = slugTemp || window.location?.pathname;
    }
    return slugTemp;
  }, [dataMenu]);

  const isGlobalHost = typeof window !== 'undefined' && window.location.hostname.includes('global');
  const EXSHPortraitData = useRibbonBoardData(RIBBON_TYPE.EXSH_BOARD, ribbonData);
  const rapvietData = useRibbonBoardData(RIBBON_TYPE.RAP_RANKING_BOARD, ribbonData);

  const customBoardRibbonType = useMemo(() => {
    const ribbonList = pageRibbon?.[pageSlug] || [];
    let exshIndex = -1;
    let rapIndex = -1;

    ribbonList.forEach((item: any, idx: any) => {
      if (EXSHPortraitData?.length) {
        if (item.type === RIBBON_TYPE.EXSH_BOARD && exshIndex === -1) exshIndex = idx;
      }
      if (rapvietData?.length) {
        if (item.type === RIBBON_TYPE.RAP_RANKING_BOARD && rapIndex === -1) rapIndex = idx;
      }
    });

    if (exshIndex !== -1 && rapIndex !== -1) {
      return exshIndex < rapIndex ? 'EXSH' : 'RAP';
    } else if (exshIndex !== -1) {
      return 'EXSH';
    } else if (rapIndex !== -1) {
      return 'RAP';
    }

    return null;
  }, [pageRibbon, pageSlug, rapvietData, EXSHPortraitData]);

  const bgMap: any = {
    exsh: ConfigImage.exshBackdrop,
    rap: ConfigImage.rapVietBackdrop
  };

  const bgForSection = useMemo(
    () =>
      dataMenu?.iconText && bgMap[dataMenu.iconText.toLowerCase()]
        ? bgMap[dataMenu.iconText.toLowerCase()]
        : '',
    [dataMenu]
  );
  const dataPageBanner = useMemo(() => {
    let dataTemp = null;
    if (!isEmpty(pageBanner) && !isEmpty(pageBanner[pageSlug])) {
      dataTemp = pageBanner[pageSlug];
    }

    return dataTemp;
  }, [pageSlug, pageBanner, adsMasterBanner]);
  const dataPageRibbon = useMemo(() => {
    let dataTemp = null;
    if (!isEmpty(pageRibbon) && pageRibbon[pageSlug]) {
      dataTemp = pageRibbon[pageSlug];
    }
    if (!isEmpty(ribbonData) && ribbonData[pageSlug]) {
      dataTemp = ribbonData[pageSlug];
    }
    return dataTemp;
  }, [pageSlug, pageRibbon, ribbonData]);

  const seoData = useMemo(() => {
    let prefixPath = getPathFromUrl(asPath);
    const slugPrefixPath = prefixPath.split('/').join('');
    if (prefixPath && prefixPath[prefixPath.length - 1] !== '/') prefixPath += '/';
    const menuMainItem = (menuList || []).find((menu: any) => menu?.seo?.url === prefixPath);
    const subMenuItem = (menuList || []).map((el: any) =>
      (el.subMenu || []).find((subEl: any) => subEl?.seo?.url === prefixPath)
    );
    const firstRibbonPath = pageProps?.firstRibbonPath || null;
    let data = menuMainItem || Object.assign({}, ...subMenuItem);
    let listArrRibbon = [];
    if (prefixPath === PAGE.HOME || prefixPath === PAGE.VIP) {
      data = (menuList || []).find((menu: any) => menu?.seo?.url === activeMenu?.seo?.url);
    }
    if (firstRibbonPath) {
      const dataRibbon = ribbonData?.[firstRibbonPath] || null;
      listArrRibbon = (dataRibbon?.items || []).map((item: any) => ItemList(item));
    }
    return {
      page: prefixPath,
      url: prefixPath,
      data,
      prefixPath,
      listArrRibbon,
      slug: slugPrefixPath
    };
  }, [asPath, menuList, pageProps?.firstRibbonPath, activeMenu, ribbonData, seoText, deviceId]);

  const checkPaymentConversion = () => {
    const offBanner = Number.parseInt(
      (ConfigLocalStorage.get(LocalStorage.OFF_BANNER_FULL_SCREEN) as string) || '0'
    );
    const timeBannerOne = Number.parseInt(
      (ConfigLocalStorage.get(LocalStorage.SKIP_BANNER_ONE_DAY) as string) || '0'
    );
    const timeBannerFifteen = Number.parseInt(
      (ConfigLocalStorage.get(LocalStorage.SKIP_BANNER_FIFTEEN_DAY) as string) || '0'
    );
    const total = Number.parseInt(fullScreenExpired?.totalTime);
    const currentTime = new Date().getTime();
    if (
      offBanner < total &&
      ((timeBannerFifteen > 0 && currentTime > timeBannerFifteen) ||
        (timeBannerOne > 0 && currentTime > timeBannerOne) ||
        (timeBannerOne === 0 && timeBannerFifteen === 0)) &&
      profile?.id &&
      subscriptions &&
      deviceId &&
      (asPath || '').split('?')?.[0]?.length === 1 &&
      !query?.vid
    ) {
      return true;
    }
    return false;
  };

  const checkPermissionDaily = () => {
    PaymentApi.checkPermissionDaily().then((res) => {
      if (res?.paidMessageNeed && res?.paidMessage) {
        dispatch(
          openPopup({
            name: POPUP.NAME.SUGGEST_CANCEL_PACKAGE,
            ...res
          })
        );
      }
    });
  };

  const handleSetTVodConfig = ({ tVodDialog, tVodLocalData }: any) => {
    if (isGlobal || popupName === POPUP.NAME.TRIGGER_FIRSTPAY) return;
    const currentDate = new Date().getDate();
    const repeatDay = +tVodDialog?.repeatDay || 1;
    const startDate = tVodDialog?.startDate || '';
    let freqPerDay = 0;
    let maxAppearance = 0;
    let lastShowDate = new Date().getTime();
    const now = new Date();
    let isShowDialog = false;
    if (now.getTime() >= new Date(startDate).getTime()) {
      if (tVodLocalData) {
        lastShowDate = tVodLocalData.lastShowDate || lastShowDate;
        freqPerDay = +tVodLocalData.freqPerDay;
        maxAppearance = +tVodLocalData.maxAppearance;
        const lastShowDateTemp = new Date(lastShowDate);
        const day = lastShowDateTemp.getDay();
        const month = lastShowDateTemp.getMonth();
        const year = lastShowDateTemp.getFullYear();
        const today = now.getDay();
        const currentMonth = now.getMonth();
        const currentYear = now.getFullYear();
        if (maxAppearance < tVodDialog?.maxAppearance) {
          if (day === today && month === currentMonth && year === currentYear) {
            // same day
            if (freqPerDay < +tVodDialog?.freqPerDay) {
              isShowDialog = true;
            }
          } else if (month === currentMonth && year === currentYear && today - day < repeatDay) {
            isShowDialog = false;
          } else {
            lastShowDate = new Date().getTime();
            freqPerDay = 0;
            isShowDialog = true;
          }
        }
      } else {
        isShowDialog = true;
      }
    }
    const isPermissionSegment = dataSegment?.isPermission;
    if (
      isShowDialog &&
      asPath === PAGE.HOME &&
      (!profile?.id || currentProfile?.id) &&
      (isEmpty(welcomeHome) ||
        (!isEmpty(welcomeHome) &&
          (statusMasthead ||
            statusLoadOutStreamAds === STATUS_OUTSTREAM_ADS.FAIL ||
            statusLoadOutStreamAds === STATUS_OUTSTREAM_ADS.CLOSE)) ||
        isMobile ||
        isPremium) &&
      ((isPermissionSegment && isTimeSegmentUser && isTimeSegmentUser === currentDate) ||
        !isPermissionSegment)
    ) {
      maxAppearance += 1;
      freqPerDay += 1;
      dispatch(openPopup({ name: POPUP.NAME.TVOD_ON_BOARDING }));
    }
    ConfigLocalStorage.set(
      LocalStorage.TVOD_CONFIG,
      JSON.stringify({
        startDate,
        freqPerDay,
        maxAppearance,
        lastShowDate
      })
    );
  };
  const handleTVodConfig = () => {
    if (hadTvod || isGlobal) return null;
    if (tVod) {
      const tVodLocal: any = ConfigLocalStorage.get(LocalStorage.TVOD_CONFIG) || '{}';
      const dataKey = profile?.id ? 'userFlow' : 'guestFlow';
      const tVodDialog = tVod?.dialog?.[dataKey] || {};
      if (tVodLocal) {
        try {
          const tVodLocalData = JSON.parse(tVodLocal);
          if (tVodDialog?.startDate && tVodDialog?.startDate !== tVodLocalData?.startDate) {
            handleSetTVodConfig({ tVodDialog });
          } else {
            handleSetTVodConfig({
              tVodDialog,
              tVodLocalData
            });
          }
        } catch (e) {
          // console.log(e);
          // const throwError = function (e) {
          //   throw new Error(e);
          // };
        }
      } else {
        handleSetTVodConfig({ tVodDialog });
      }
    }
    return null;
  };

  // Desktop 6 ribbons/12 items , Mobile all ribbons/4 items
  const handleGetDataRibbon = (ribbonList: any) => {
    dispatch(setLoadedData(false));
    Promise.all(
      (ribbonList || []).map((rib: any) => {
        if (
          ribbonData?.[rib?.seo?.url] ||
          rib?.type === RIBBON_TYPE.BANNER_RIBBON_ADS ||
          rib?.type === RIBBON_TYPE.PROMOTED_RIBBON_ADS
        ) {
          return null;
        }
        return rib; // Return the value explicitly
      })
    )
      .then(() => {
        dispatch(setLoadedData(true));
      })
      .catch(() => {
        dispatch(setLoadedData(true));
      });
  };

  const getPageData = () => {
    dispatch(clearRibbonData());
    if (isEmpty(dataPageBanner) && pageSlug) {
      dispatch(
        getPageBanners({
          pageSlug,
          isGlobal
        })
      );
    }
    if (dataPageRibbon) {
      handleGetDataRibbon(dataPageRibbon);
    } else if (pageSlug) {
      dispatch(
        getPageRibbons({
          pageSlug,
          isMWebToApp: featureFlag?.mwebToApp,
          imageMWebToApp: isMobile
            ? mwebToApp?.onlyAppImageMWeb || mwebToApp?.onlyAppImage
            : mwebToApp?.onlyAppImage,
          isMobile,
          isGlobal
        })
      ).then((res: any) => {
        handleGetDataRibbon(res?.data?.data);
      });
    }
  };
  useEffect(() => {
    if (isGlobal && restricted && isGlobalHost) {
      dispatch(openPopup({ name: POPUP.NAME.LIMIT_REGION }));
    }
  }, [isGlobal, restricted]);

  useEffect(
    () => () => {
      dispatch(clearDataPageBanner());
    },
    []
  );

  useEffect(() => {
    getPageData();
    scrollToTop();
  }, [dataMenu?.id]);
  useEffect(() => {
    if (dataMenu?.seo?.url) {
      if (dataMenu?.seo?.url === '/trang-chu/') {
        keyBreadcrumbs = 'home';
        keyConfig = SEO_CONFIG.SNIPPET_HOME;
      } else if ((dataMenu?.seo?.url || '').indexOf('/m/') > -1) {
        keyBreadcrumbs = SEO_PAGES.CATEGORY_NOT_PROMOTED;
        keyConfig = '';
      } else if ((dataMenu?.seo?.url || '').indexOf('shop') !== -1) {
        keyBreadcrumbs = '';
        keyConfig = '';
      } else {
        keyBreadcrumbs = SEO_PAGES.CATEGORY;
        keyConfig = '';
      }
      if (dataSEOAllPage?.seo?.slug !== dataMenu?.seo?.url) {
        dispatch(
          getSEOAllPage({
            slug: dataMenu?.seo?.url,
            keyBreadcrumbs,
            keyConfig
          })
        );
      }
    }
  }, [dataMenu?.seo?.url, dataSEOAllPage?.seo?.slug]);

  useEffect(() => {
    if (deviceId) {
      pageView(seoData);
    }
  }, [deviceId]);

  useEffect(() => {
    if (profile?.id) {
      checkPermissionDaily();
    } else {
      handleTVodConfig();
    }
  }, [profile?.id, statusMasthead]);

  useEffect(() => {
    if (profile?.id && isEmpty(USER_PACKAGE_INFO) && !isKid) {
      dispatch(getUserPackageInfo());
    }
  }, [profile?.id, USER_PACKAGE_INFO, isKid]);

  useEffect(() => {
    if (isKid) {
      setIsCheckPaymentConversion(true);
      setIsTVodOnBoarding(true);
    }
  }, [isKid]);

  useEffect(() => {
    if (profile?.id && currentProfile?.id && !isKid && isEmpty(dataSegment)) {
      dispatch(getSegmentedUser());
    }
  }, [profile?.id]);

  useEffect(() => {
    const idProfileCurrentLocal = ConfigLocalStorage.get(LocalStorage.CHECK_PROFILE_ID);
    if (deviceId && profile?.id && idProfileCurrentLocal === 'true') {
      const isPaymentConversion = checkPaymentConversion();
      setIsPaymentConversion(isPaymentConversion);
      setIsCheckPaymentConversion(true);
    }
  }, [deviceId, profile?.id, USER_PACKAGE_INFO]);

  useEffect(() => {
    if (isCheckPaymentConversion && !isPaymentConversion) {
      handleTVodConfig();
    }
  }, [isCheckPaymentConversion, isPaymentConversion]);
  useEffect(() => {
    handleTVodConfig();
  }, [statusLoadOutStreamAds]);
  useEffect(() => {
    if (isKid) {
      handleTVodConfig();
    }
  }, [isKid, isTVodOnBoarding]);

  useEffect(() => {
    if (dataSegment?.isPermission && isTimeSegmentUser) {
      handleTVodConfig();
    }
  }, [dataSegment?.isPermission, isTimeSegmentUser]);
  useEffect(() => {
    if (profile?.id) {
      dispatch(getPackageDiscountOEM()).then((data: any) => {
        setOemData(data?.data?.result || {});
      });
    }
  }, [profile]);

  useEffect(() => {
    ConfigLocalStorage.remove('FROM_URL');
  }, []);

  if (
    pageProps?.is410 ||
    (!isEmpty(pageRibbon) &&
      isArray(dataPageRibbon) &&
      isEmpty(dataPageRibbon) &&
      isEmpty(dataPageBanner?.data))
  ) {
    return <ContentNotFound dataRibbon={{ 0: ribbonNotFound || [] }} />;
  }

  return (
    <>
      {!isKid && <FABWidget />}
      <SeoText seo={dataSEOAllPage?.seo} />
      <MasterBanner data={dataPageBanner} />
      {currentProfile?.id &&
        !isKid &&
        dataSegment?.isPermission &&
        (isEmpty(oemData) || oemData?.promos?.[0]?.type === 'CAMPAIGN_DISCOUNT') && (
          <FullScreenSegmentedUser dataSegment={dataSegment} isMobile={isMobile} />
        )}
      {currentProfile?.id &&
        !isKid &&
        !isEmpty(oemData) &&
        oemData?.promos?.[0]?.type === 'PACKAGE_DISCOUNT' && (
          <FullScreenSegmentedUser
            dataSegment={{
              ...dataSegment,
              oemData: oemData?.promos[0]
            }}
            isOem
            isMobile={isMobile}
          />
        )}
      {(oemData?.promos?.[0]?.type === 'CAMPAIGN_DISCOUNT' || isEmpty(oemData)) && (
        <TypeTrigger
          isPaymentConversion={isPaymentConversion}
          profile={profile}
          enableTVod={enableTVod}
          isCheckPaymentConversion={isCheckPaymentConversion}
          query={query}
          asPath={asPath}
          currentProfile={currentProfile}
          isGlobal={isGlobal}
        />
      )}
      {bgForSection && (
        <div className={styles.backdrop}>
          <Image
            width="100%"
            height="100%"
            src={bgForSection}
            alt={bgForSection ? `${dataMenu?.name}` : ''}
            notWebp
          />
        </div>
      )}
      <div className={classNames(customBoardRibbonType ? styles.containerAside : styles.container)}>
        {!!customBoardRibbonType && (
          <RibbonBoard
            pageSlug={pageSlug}
            customBoardRibbonType={customBoardRibbonType}
            ribbonData={customBoardRibbonType === 'EXSH' ? EXSHPortraitData : rapvietData}
          />
        )}

        <ListRibbons
          customizeClassName={classNames(
            customBoardRibbonType === 'EXSH' && styles.Section,
            customBoardRibbonType === 'RAP' && styles.SectionRap
          )}
          showTrigger
          id={pageSlug}
          isRapVietRanking={!!customBoardRibbonType}
          isRankingBoard={!!customBoardRibbonType}
        />
      </div>
      <SeoAllPage {...dataSEOAllPage} listArrRibbon={seoData?.listArrRibbon} />
    </>
  );
};

HomeContainer.getInitialProps = async ({ store, req, res }: any) => {
  if (req) {
    const origin = req?.headers?.host || '';
    // get menu from store redux
    const { Menu, App } = store.getState() || {};
    const { mwebToApp, featureFlag } = App?.webConfig || {};
    const isGlobal = App?.geoCheck?.isGlobal;
    const accessToken = App?.accessToken || App?.token;
    const cookie = req?.cookies;
    const keySignature = ConfigCookie.KEY.SIGNATURE;
    const { profileToken } =
      decodeSignature({
        value: cookie?.[keySignature] || getCookie(keySignature, req?.headers.cookie) || ''
      }) || {};
    const isMobile = App?.isMobile;
    const { activeMenu } = Menu;
    const { menuList } = Menu;
    const { activeSubMenu } = Menu;
    const dataMenu = activeSubMenu || activeMenu;
    const userAgent = req.headers['user-agent'];
    const ipAddress =
      (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '').split(':').pop() || '';
    let firstRibbonPath = '';
    let is410 = false;

    if (!isEmpty(dataMenu)) {
      const pageSlug = dataMenu?.dataSlug || dataMenu?.seo?.url;
      let seoPageSlug = dataMenu?.seo?.url; // SEO slug must be slug on url
      if (pageSlug && pageSlug[pageSlug.length - 1] !== '/') {
        // response 410 with url not have '/' at last
        is410 = true;
        res.status(410);
        await store.dispatch(
          getRibbonDetailNotFound({
            accessToken,
            profileToken,
            ssr: true,
            isGlobal,
            origin
          })
        );
      }
      if (req.path === PAGE.PHIMMOI) {
        seoPageSlug = PAGE.PHIMMOI;
        keyBreadcrumbs = SEO_PAGES.CATEGORY;
        keyConfig = '';
      } else if (seoPageSlug === PAGE.HOME || seoPageSlug === PAGE.HOME_DEFAULT) {
        seoPageSlug = '/trang-chu/';
        keyBreadcrumbs = null;
        keyConfig = SEO_CONFIG.SNIPPET_HOME;
      } else if ((seoPageSlug || '').indexOf('/m/') > -1) {
        keyBreadcrumbs = SEO_PAGES.CATEGORY_NOT_PROMOTED;
        keyConfig = '';
      } else {
        keyBreadcrumbs = SEO_PAGES.CATEGORY;
        keyConfig = '';
      }
      await store
        .dispatch(
          getSEOAllPage({
            slug: seoPageSlug,
            keyBreadcrumbs,
            keyConfig,
            origin
          })
        )
        .then(async (resp: any) => {
          const redirect = resp?.data?.redirect;
          redirectTool({
            redirect: resp?.data?.redirect,
            res
          });
          // dispatch data to store
          await Promise.all([
            store.dispatch(
              getPageBanners({
                pageSlug,
                accessToken,
                profileToken,
                isMobile,
                ssr: true,
                ipAddress,
                userAgent,
                isGlobal,
                origin
              })
            ),
            store
              .dispatch(
                getPageRibbons({
                  pageSlug,
                  accessToken,
                  profileToken,
                  isMobile,
                  ssr: true,
                  ipAddress,
                  origin,
                  userAgent,
                  isMWebToApp: featureFlag?.mwebToApp,
                  imageMWebToApp: isMobile
                    ? mwebToApp?.onlyAppImageMWeb || mwebToApp?.onlyAppImage
                    : mwebToApp?.onlyAppImage,
                  isGlobal
                })
              )
              .then(async (response: any) => {
                const ribbon0 = response?.data?.data?.[0];
                const ribbon0Id = ribbon0?.id;
                const ribbon0Type = ribbon0?.type;
                firstRibbonPath = ribbon0?.seo?.url;
                const dataRibbons = response?.data?.data;
                if (isEmpty(dataRibbons)) {
                  await store.dispatch(
                    getRibbonDetailNotFound({
                      accessToken,
                      profileToken,
                      ssr: true,
                      ipAddress,
                      userAgent,
                      isGlobal,
                      origin
                    })
                  );
                } else {
                  const limitRibBanner =
                    ribbon0Type === RIBBON_TYPE.MULTI_TABS_RIBBON
                      ? undefined
                      : ribbon0Type === RIBBON_TYPE.PROMOTION_BANNER ||
                        ribbon0Type === RIBBON_TYPE.PROMOTION_BANNER_FUNC
                      ? 8
                      : 12;
                  if (
                    ribbon0Id &&
                    ribbon0Type !== RIBBON_TYPE.BANNER_RIBBON_ADS &&
                    ribbon0Type !== RIBBON_TYPE.PROMOTED_RIBBON_ADS
                  ) {
                    await store.dispatch(
                      getDataRibbonsId({
                        id: ribbon0Id,
                        accessToken,
                        profileToken,
                        ribbonOrder: 0,
                        ribbonName: ribbon0Id?.name,
                        limit: limitRibBanner,
                        ssr: true,
                        ipAddress,
                        userAgent,
                        isMWebToApp: featureFlag?.mwebToApp,
                        imageMWebToApp: isMobile
                          ? mwebToApp?.onlyAppImageMWeb || mwebToApp?.onlyAppImage
                          : mwebToApp?.onlyAppImage,
                        isMobile,
                        isGlobal,
                        origin
                      })
                    );
                  }
                }
              })
              .catch(async () => {
                if (redirect?.http_status === HTTP_CODE.OK_200) res.status(404);
                await store.dispatch(
                  getRibbonDetailNotFound({
                    accessToken,
                    profileToken,
                    ssr: true,
                    ipAddress,
                    userAgent,
                    isGlobal,
                    origin
                  })
                );
              })
          ]);
        });
    } else if (menuList?.length && menuList?.length > 0) {
      is410 = true;
      res.status(410);
      await store.dispatch(
        getRibbonDetailNotFound({
          accessToken,
          profileToken,
          ssr: true,
          ipAddress,
          userAgent,
          isGlobal,
          origin
        })
      );
    }
    return {
      ssr: true,
      firstRibbonPath,
      is410
    };
  }
  return {};
};

export default HomeContainer;
