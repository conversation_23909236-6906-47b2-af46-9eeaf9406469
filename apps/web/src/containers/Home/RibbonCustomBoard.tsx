import React, { memo } from 'react';
import PortraitRibbonEXSH from '@/components/EXSH';
import RapVietTopSong from '@/components/RapVietTopSong';

const RibbonBoard = ({ ribbonData, customBoardRibbonType, pageSlug }: any) => {
  if (!customBoardRibbonType) return null;

  return (
    <>
      {customBoardRibbonType === 'EXSH' && (
        <PortraitRibbonEXSH pageSlug={pageSlug} ribbonData={ribbonData} />
      )}
      {customBoardRibbonType === 'RAP' && (
        <RapVietTopSong pageSlug={pageSlug} ribbonData={ribbonData} />
      )}
    </>
  );
};

export default memo(RibbonBoard);
