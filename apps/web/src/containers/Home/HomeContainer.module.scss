.container {
  @apply flex flex-col py-6 md:pb-12 2xl:pb-16 px-4 lg:px-[3.02083vw] overflow-hidden;

  &Aside {
    @apply flex flex-col md:flex-row py-8 md:py-12 lg:py-12 px-4 lg:pl-[3.02083vw] lg:pr-0 md:space-x-4 lg:space-x-6 relative z-[1] overflow-hidden;
    @apply space-y-14 md:space-y-0 justify-end;
    @apply before:w-screen before:h-[12vw] before:xl:h-[28vw] before:absolute before:top-0 before:md:-top-6 before:xl:top-0 before:2xl:top-0 before:3xl:top-[12vw] before:left-0 before:bg-gradient-to-b before:from-vo-dark-gray-950;
  }
}

.backdrop {
  @apply fixed left-0 top-0 w-screen h-screen z-0;
  @apply before:w-screen before:h-[12vw] before:xl:h-[28vw] before:absolute before:top-0 before:md:-top-6 before:xl:top-0 before:2xl:top-0 before:left-0 before:bg-gradient-to-b before:from-vo-dark-gray-950;

  & > img {
    @apply fixed left-1/2 top-0 -translate-x-1/2 min-w-full min-h-full md:w-screen md:h-screen;
  }
}

.Section {
  @apply relative md:w-[calc(100%-22.91667vw-1.5rem)] xl:!pr-[3.02083vw] z-[2] flex-1 overflow-hidden;

  &Rap {
    @apply relative md:w-[calc(100%-15.5rem-1.5rem)] lg:w-[calc(100%-24.4791667vw-1.5rem)] xl:!pr-[3.02083vw] z-[2] flex-1 overflow-hidden;
  }
}
