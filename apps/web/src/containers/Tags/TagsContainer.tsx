import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { setLoadedData } from '@actions/app';
import { getSEOAllPage } from '@actions/page';
import { ACTION_GET_CONTENT_TAGS, getContentTags, clearTagData } from '@actions/tags';
import { getRibbonDetailNotFound } from '@actions/detail';
import { decodeSignature, getCookie } from '@helpers/common';
import { getListOrderTag } from '@helpers/settings';
import { pageView } from '@tracking/functions/TrackingApp';
import ContentNotFound from '@components/notfound/ContentNotFound';
import EmptyTag from '@components/empty/EmptyTag';
import ContentList from '@components/basic/ContentList/ContentList';
import { HTTP_CODE, PAGE, SEO_PAGES } from '@constants/constants';
import SeoAllPage, { redirectTool } from '@components/seo/SeoAllPage';
import ConfigCookie from '@config/ConfigCookie';
import isEmpty from 'lodash/isEmpty';
import { getContentPopver } from '@actions/detail';

const stateInit = {
  slug: '',
  asPath: '',
  isLoadMore: true,
  listDataAllPage: null,
  isLoaded: false,
  metadata: {
    page: 0,
    limit: 30,
    total: null
  },
  currentSortId: 3 // ngày phát hành mới nhất
};
const keyBreadcrumbs = SEO_PAGES.TAG;
class TagsContainer extends PureComponent {
  isLoaded: any;
  scrollTop: any;
  static async getInitialProps({ store, req, query, res }: any) {
    if (req) {
      // call in server
      const { slug } = query || req.params;
      let renderNotFoundPage = false;
      const userAgent = req?.headers?.['user-agent'];
      const ipAddress = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '')
        .split(':')
        .pop();

      if (slug) {
        const tags = [slug];
        const { App } = store.getState() || {};
        const isGlobal = App?.geoCheck?.isGlobal;
        const accessToken = App?.accessToken || App?.token;
        const cookie = req?.cookies;
        const keySignature = ConfigCookie.KEY.SIGNATURE;
        const { profileToken } =
          decodeSignature({
            value: cookie?.[keySignature] || getCookie(keySignature, req?.headers.cookie) || ''
          }) || {};
        const { metadata, currentSortId } = stateInit;
        const { page, limit } = metadata;
        const dataContent = getContentTags({
          tags,
          page,
          limit,
          sort: currentSortId,
          accessToken,
          profileToken,
          ipAddress,
          userAgent,
          isGlobal
        });

        await store
          .dispatch(getSEOAllPage({ slug: req.path, keyBreadcrumbs, ipAddress, userAgent }))
          .then(async (resp: any) => {
            const redirect = resp?.data?.redirect;
            redirectTool({ redirect, res });
            await store.dispatch(dataContent).then(async (response: any) => {
              if ((response && response.error) || response?.payload?.data?.error) {
                if (redirect?.http_status === HTTP_CODE.OK_200) res.status(404);
                const ribbonDetail = await getRibbonDetailNotFound({
                  accessToken,
                  profileToken,
                  ssr: true,
                  ipAddress,
                  userAgent,
                  isGlobal
                });
                await store.dispatch(ribbonDetail);
                renderNotFoundPage = true;
              }
            });
          });
      }
      return {
        renderNotFoundPage,
        ssr: true
      };
    }
    return {};
  }
  state = stateInit;

  componentDidMount() {
    const { router, dataSEOAllPage, getSEOAllPage, setLoadedData }: any = this.props;
    const asPath = router?.asPath;
    this.fetchData(this.props);
    // GA PAGE VIEW
    const dataSeo = this.getSeoData();
    pageView(dataSeo);
    // SEO
    if (dataSEOAllPage?.seo?.slug !== asPath) {
      getSEOAllPage({ slug: asPath, keyBreadcrumbs });
    }
    setLoadedData(true);
  }

  componentDidUpdate(prevProps: any, prevState: any) {
    const { metadata, currentSortId, asPath } = this.state;
    const { router }: any = this.props;
    const newAsPath = router?.asPath;
    // if change active or page => fetch data api

    if (
      asPath !== newAsPath ||
      metadata?.page !== prevState.metadata?.page ||
      currentSortId !== prevState.currentSortId
    ) {
      this.setState({ asPath: newAsPath });
    }
  }

  componentWillUnmount() {
    const { clearTagData }: any = this.props;
    clearTagData();
    window.removeEventListener('scroll', this.handleScrollingAfterLoadMore);
  }

  handleScrollingAfterLoadMore = (e: any) => {
    if (this.isLoaded) {
      const scrollingElement = e?.target?.scrollingElement;
      if (scrollingElement) {
        scrollingElement?.scrollTo(0, this.scrollTop);
      }
      this.isLoaded = false;
    }
  };

  onScrollDown = () => {
    const { token, getContentTags, isGlobal, router }: any = this.props;
    const { currentSortId, isLoadMore } = this.state;
    const { slug } = router?.query;

    const data = (this.props as any)[ACTION_GET_CONTENT_TAGS] || {};
    const { page, limit } = data?.metadata || {};
    const nextPage = page + 1;
    const accessToken = token;
    if (isLoadMore) {
      getContentTags({
        tags: slug,
        page: nextPage,
        limit,
        accessToken,
        sort: currentSortId,
        isGlobal
      }).then((res: any) => {
        if (res && res?.payload?.data) {
          if (isEmpty(res?.payload?.data?.items)) {
            this.setState({ isLoadMore: false });
            return;
          }
          const dataRibbonDetail = res?.payload?.data;
          this.setState({
            metadata: dataRibbonDetail?.metadata
          });
        }
      });
    }
  };
  render() {
    const dataRender = this.getDataRender();
    const seoData = this.getSeoData();
    const { listDataAllPage } = dataRender;
    const { pageProps, dataSEOAllPage, ribbonNotFound }: any = this.props;

    if (pageProps?.renderNotFoundPage) {
      return <ContentNotFound dataRibbon={{ 0: ribbonNotFound || [] }} />;
    }

    if (listDataAllPage && listDataAllPage.length === 0) {
      return <EmptyTag />;
    }
    if (!listDataAllPage || listDataAllPage === null || listDataAllPage === undefined) {
      return null;
    }
    return (
      <>
        <section className="section section--collection canal-v">
          <div className="container">
            <ContentList {...dataRender} {...pageProps} onScrollDown={this.onScrollDown} />
          </div>
        </section>
        <SeoAllPage {...dataSEOAllPage} listArrRibbon={seoData?.listArrRibbon} />
      </>
    );
  }

  getDataRender = (props?: any) => {
    props = typeof props === 'undefined' ? this.props : props;
    const { isLoadMore, currentSortId } = this.state;
    const dataCurrentSort = getListOrderTag(currentSortId);
    let title = '';
    // if empty data state try get data from redux

    const tagData = props[ACTION_GET_CONTENT_TAGS];
    // get title page from listDataAllPage
    const tags = tagData?.metadata?.tags;
    title = tags?.[0]?.name || '';
    const seoData = props?.dataSEOAllPage?.seo; // || this.getSeoData(getPathFromUrl(props.router.asPath))
    return { listDataAllPage: tagData, title, isLoadMore, currentSortId, dataCurrentSort, seoData };
  };

  getSeoData = (prefixPath?: any) => {
    // check data from props
    let listArrRibbon = [];
    const tagData = (this.props as any)[ACTION_GET_CONTENT_TAGS];
    listArrRibbon = (tagData?.items || []).map((item: any) => ({
      id: item.id,
      imageURL: item.images.thumbnail || '',
      title: item?.seo.title,
      rating: item.avgRate || 5,
      description: item.description,
      totalRating: item.totalRate,
      url: item?.seo.url
    }));

    const dataSeo = {
      page: PAGE.TAG,
      data: tagData?.metadata,
      prefixPath,
      listArrRibbon
    };

    return dataSeo;
  };

  fetchData = (props: any) => {
    const { slug } = props.router.query;
    const slugFromPath = props.router.asPath.split('/').pop().replace('tag-', '') || '';
    const tags = slug ? [slug] : [slugFromPath];
    let { metadata, currentSortId } = this.state;

    const { page, limit } = metadata || {};
    // check data from props
    const tagData = (this.props as any)[ACTION_GET_CONTENT_TAGS];
    // check data from redux
    if (!tagData) {
      props
        .getContentTags({
          tags,
          page,
          limit,
          sort: currentSortId,
          isGlobal: props?.geoCheck?.isGlobal
        })
        .then((res: any) => {
          if (res) {
            metadata = tagData?.metadata;
            this.setState({
              metadata
            });
          }
        });
    } else {
      // set state data
      this.setState({ tagData });
    }
  };
}

const mapStateToProps = ({ Tags, App, Menu, Page, Detail, Profile }: any) => ({
  ...App,
  ...Menu,
  ...Page,
  ...Detail,
  ...Profile,
  ...Tags
});

const mapDispatchToProps = (dispatch: any) => {
  const actions = bindActionCreators(
    {
      setLoadedData: setLoadedData,
      getContentTags: getContentTags,
      clearTagData: clearTagData,
      getContentPopver: getContentPopver,
      getRibbonDetailNotFound: getRibbonDetailNotFound,
      getSEOAllPage: getSEOAllPage
    },
    dispatch
  );
  return { ...actions, dispatch };
};

export default connect(mapStateToProps, mapDispatchToProps)(TagsContainer);
