import React, { useEffect, useMemo, useRef, useState } from 'react';
import Head from 'next/head';
import { useVieRouter } from '@customHook';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import omit from 'lodash/omit';
import SeoAllPage, { redirectTool } from '@components/seo/SeoAllPage';
import SeoText from '@components/seo/SeoText';
import LiveTVPlayer from '@components/liveTV/LiveTVPlayer';
import ContentNotFound from '@components/notfound/ContentNotFound';
import SocketCluster from '@components/liveTV/socket/socketCluster';
import { setLoadedData, setToast } from '@actions/app';
import { getContentConfig, getTriggerConfig } from '@actions/appConfig';
import {
  clearDetailChannel,
  getDetailChannelById,
  getDetailChannelBySlug,
  getInfoNotifyListEpgs,
  getListChannels,
  getListEpgs,
  getListFilterChannelPage,
  getListSubCategoriesOfBroadcasting,
  getListSubCategoriesOfChannelList,
  getQNetInfo,
  getRibbonLiveTvNotFound,
  setActiveCategory,
  setActiveEpg,
  setActiveFilterChannelPage,
  setActiveSubCategory
} from '@actions/liveTV';
import { getSEOAllPage } from '@actions/page';
import { getPopupTriggerDialog, openPopup } from '@actions/popup';
import {
  CONTENT_TYPE,
  ERROR_CODE,
  HTTP_CODE,
  PAGE,
  PERMISSION,
  POPUP,
  RIBBON_TYPE,
  SEO_PAGES,
  TAB_LISTS,
  USER_TYPE
} from '@constants/constants';
import { TEXT } from '@constants/text';
import { VALUE } from '@config/ConfigSegment';
import { ItemList } from '@models/subModels';
import {
  decodeSignature,
  encodeParamDestination,
  getCookie,
  getDeviceId,
  handleScrollTop,
  onOpenPayment,
  parseTimeStartNotify
} from '@helpers/common';
import { parsePopupParams } from '@services/popupServices';
import { parseParamsUrlLiveTv } from '@services/liveTVServices';
import { BUILD_ID, SIGMA_DRM_WINDOWS, STATIC_DOMAIN } from '@config/ConfigEnv';
import ConfigCookie from '@config/ConfigCookie';
import SigmaDRM from '@script/SigmaDRM';
import TrackingPayment from '@tracking/functions/payment';
import { endSessionPlay, refreshSessionPlay } from '@actions/detail';
import { checkSigmaLoaded } from '@services/playerServices';
import dynamic from 'next/dynamic';

const Categories = dynamic(() => import('@components/liveTV/categories'), {
  ssr: true
});

const trackingPayment = new TrackingPayment();
const LiveTVContainer = ({ pageProps }: any) => {
  const { renderNotFoundPage } = pageProps || {};
  const dispatch = useDispatch();
  const timerCheckEpgLiveRef = useRef<any>(null);
  const oldChannelDetail = useRef<any>(null);
  const timeRefreshToken = useRef<any>(0);
  const lastSlugChannelRef = useRef<any>(null);
  const router = useVieRouter();
  const {
    isMobile,
    isAndroid,
    isIOS: iOS,
    isWindows,
    deviceId,
    isMacOs,
    isSafari
  } = useSelector((state: any) => state?.App || {});
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile || {});
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { isFullscreen } = useSelector((state: any) => state?.Player || {});
  const { dataSEOAllPage } = useSelector((state: any) => state?.Page || {});
  const [sigmaLoaded, setSigmaLoaded] = useState(false);
  const [isClient, setIsClient] = useState(false);

  const {
    activeCategory,
    activeCategoryFullscreen,
    activeFilterChannelPage,
    activeFilterChannelPageFullscreen,
    activeSubCategoryDSK,
    activeSubCategoryDSKFullscreen,
    activeSubCategoryDPS,
    activeSubCategoryDPSFullscreen,
    detailChannel,
    listChannels,
    listFavoriteChannels,
    listWatchedChannels,
    listFilterChannelPage,
    listSubCategoriesDPS,
    listSubCategoriesDSK,
    listEpgsOfChannel,
    listRibbonNotFound,
    activeEpg
  } = useSelector((state: any) => state?.LiveTV || {});
  const { concurrentScreen, dataRefreshSession } = useSelector((state: any) => state?.Detail || {});
  const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
  const { comingSoonListIdEpgs, liveEpg, nextEpg } = listEpgsOfChannel || {};
  const { asPath, query } = router || {};
  const { slug, epg } = query || {};
  const listArrRibbon = useMemo(() => {
    let arrTemp = [];
    const firstFilterId = listFilterChannelPage?.[0]?.id;
    const firstRibbonId = listSubCategoriesDSK?.[firstFilterId]?.[0]?.id || null;
    if (firstRibbonId) {
      const dataRibbon = listChannels?.[firstRibbonId] || {};
      arrTemp = (dataRibbon?.items || []).map((item: any) => ItemList(item));
    }
    return arrTemp;
  }, [listFilterChannelPage, listSubCategoriesDSK, listChannels]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    dispatch(setLoadedData(true));
    dispatch(getContentConfig());
    dispatch(getTriggerConfig());
    handleScrollTop();
    if (isAndroid || (isWindows && SIGMA_DRM_WINDOWS) || (isMacOs && !isSafari)) {
      setSigmaLoaded(checkSigmaLoaded());
    } else setSigmaLoaded(true);
    return () => {
      dispatch(clearDetailChannel());
      oldChannelDetail.current = null;
      clearInterval(timerCheckEpgLiveRef.current);
      SocketCluster.unsubscribe();
      clearTimeout(timeRefreshToken?.current);
      if (sessionToken) handleEndSessionPlay(sessionToken);
    };
  }, []);

  useEffect(() => {
    if (isEmpty(listFilterChannelPage)) {
      dispatch(getListFilterChannelPage({}));
    }
  }, [listFilterChannelPage]);

  useEffect(() => {
    if (isEmpty(activeCategory)) {
      dispatch(setActiveCategory(TAB_LISTS[0]));
    }
  }, [activeCategory]);

  useEffect(() => {
    if (activeCategory?.id === 'DPS' && isEmpty(listSubCategoriesDPS)) {
      dispatch(getListSubCategoriesOfBroadcasting({}));
    }
  }, [activeCategory, listSubCategoriesDPS]);

  useEffect(() => {
    // xử lý fullscreen
    if (isFullscreen && activeCategoryFullscreen?.id === 'DPS' && isEmpty(listSubCategoriesDPS)) {
      dispatch(getListSubCategoriesOfBroadcasting({}));
    }
  }, [activeCategoryFullscreen, listSubCategoriesDPS, isFullscreen]);

  useEffect(() => {
    if (!isFullscreen && isEmpty(activeFilterChannelPage) && !isEmpty(listFilterChannelPage)) {
      dispatch(setActiveFilterChannelPage(listFilterChannelPage[0]));
    }
  }, [activeFilterChannelPage, listFilterChannelPage, isFullscreen]);

  useEffect(() => {
    // xử lý phần fullscreen
    if (
      isFullscreen &&
      isEmpty(activeFilterChannelPageFullscreen) &&
      !isEmpty(listFilterChannelPage)
    ) {
      dispatch(setActiveFilterChannelPage(listFilterChannelPage[0], true));
    }
  }, [activeFilterChannelPageFullscreen, listFilterChannelPage, isFullscreen]);

  useEffect(() => {
    if (
      !isFullscreen &&
      activeCategory?.id === 'DSK' &&
      !isEmpty(activeFilterChannelPage) &&
      isEmpty(get(listSubCategoriesDSK, activeFilterChannelPage.id, []))
    ) {
      dispatch(getListSubCategoriesOfChannelList({ id: activeFilterChannelPage.id }));
    }
  }, [activeCategory, activeFilterChannelPage, listSubCategoriesDSK, isFullscreen]);

  useEffect(() => {
    // xử lý phần fullscreen
    if (
      isFullscreen &&
      activeCategoryFullscreen?.id === 'DSK' &&
      !isEmpty(activeFilterChannelPageFullscreen) &&
      isEmpty(get(listSubCategoriesDSK, activeFilterChannelPageFullscreen.id, []))
    ) {
      dispatch(
        getListSubCategoriesOfChannelList({
          id: activeFilterChannelPageFullscreen.id
        })
      );
    }
  }, [
    activeCategoryFullscreen,
    activeFilterChannelPageFullscreen,
    listSubCategoriesDSK,
    isFullscreen
  ]);

  useEffect(() => {
    if (
      !isFullscreen &&
      activeCategory?.id === 'DSK' &&
      isEmpty(activeSubCategoryDSK) &&
      !isEmpty(activeFilterChannelPage) &&
      !isEmpty(get(listSubCategoriesDSK, activeFilterChannelPage.id, []))
    ) {
      const itemSubCategory = get(listSubCategoriesDSK, `${activeFilterChannelPage.id}[0]`, {});
      if (!isEmpty(itemSubCategory)) {
        dispatch(setActiveSubCategory(itemSubCategory, activeCategory?.id));
      }
    }
  }, [
    activeCategory,
    activeSubCategoryDSK,
    activeFilterChannelPage,
    listSubCategoriesDSK,
    isFullscreen
  ]);

  useEffect(() => {
    // xử lý phần fullscreen
    if (
      isFullscreen &&
      activeCategoryFullscreen?.id === 'DSK' &&
      isEmpty(activeSubCategoryDSKFullscreen) &&
      !isEmpty(activeFilterChannelPageFullscreen) &&
      !isEmpty(get(listSubCategoriesDSK, activeFilterChannelPageFullscreen.id, []))
    ) {
      const itemSubCategory = get(
        listSubCategoriesDSK,
        `${activeFilterChannelPageFullscreen.id}[0]`,
        {}
      );
      if (!isEmpty(itemSubCategory)) {
        dispatch(setActiveSubCategory(itemSubCategory, activeCategoryFullscreen?.id, true));
      }
    }
  }, [
    activeCategoryFullscreen,
    activeSubCategoryDSKFullscreen,
    activeFilterChannelPageFullscreen,
    listSubCategoriesDSK,
    isFullscreen
  ]);

  useEffect(() => {
    if (!isFullscreen) {
      let subCategoryActive = {};
      if (activeCategory?.id === 'DSK') {
        if (!isEmpty(activeFilterChannelPage)) {
          const firstSubCategory = get(
            listSubCategoriesDSK,
            `${activeFilterChannelPage.id}[0]`,
            {}
          );
          if (!isEmpty(firstSubCategory)) {
            subCategoryActive = firstSubCategory;
          }
        }
      } else if (activeCategory?.id === 'DPS') {
        const firstSubCategory = get(listSubCategoriesDPS, [0], {});
        if (!isEmpty(firstSubCategory)) {
          subCategoryActive = firstSubCategory;
        }
      }
      if (!isEmpty(subCategoryActive)) {
        dispatch(setActiveSubCategory(subCategoryActive, activeCategory?.id));
      }
    }
  }, [
    activeCategory,
    activeFilterChannelPage,
    listSubCategoriesDSK,
    listSubCategoriesDPS,
    isFullscreen
  ]);

  useEffect(() => {
    // xử lý phần fullscreen
    if (isFullscreen) {
      let subCategoryActive = {};
      if (activeCategoryFullscreen?.id === 'DSK') {
        if (!isEmpty(activeFilterChannelPageFullscreen)) {
          const firstSubCategory = get(
            listSubCategoriesDSK,
            `${activeFilterChannelPageFullscreen.id}[0]`,
            {}
          );
          if (!isEmpty(firstSubCategory)) {
            subCategoryActive = firstSubCategory;
          }
        }
      } else if (activeCategoryFullscreen?.id === 'DPS') {
        const firstSubCategory = get(listSubCategoriesDPS, [0], {});
        if (!isEmpty(firstSubCategory)) {
          subCategoryActive = firstSubCategory;
        }
      }
      if (!isEmpty(subCategoryActive)) {
        dispatch(setActiveSubCategory(subCategoryActive, activeCategoryFullscreen?.id, true));
      }
    }
  }, [
    activeCategoryFullscreen,
    activeFilterChannelPageFullscreen,
    listSubCategoriesDSK,
    listSubCategoriesDPS,
    isFullscreen
  ]);

  useEffect(() => {
    if (
      !isFullscreen &&
      activeCategory?.id === 'DPS' &&
      isEmpty(activeSubCategoryDPS) &&
      !isEmpty(listSubCategoriesDPS)
    ) {
      const itemSubCategory = listSubCategoriesDPS[0];
      if (!isEmpty(itemSubCategory)) {
        dispatch(setActiveSubCategory(itemSubCategory, activeCategory?.id));
      }
    }
  }, [activeCategory, activeSubCategoryDPS, listSubCategoriesDPS, isFullscreen]);

  useEffect(() => {
    // xử lý fullscreen
    if (
      isFullscreen &&
      activeCategoryFullscreen?.id === 'DPS' &&
      isEmpty(activeSubCategoryDPSFullscreen) &&
      !isEmpty(listSubCategoriesDPS)
    ) {
      const itemSubCategory = listSubCategoriesDPS[0];
      if (!isEmpty(itemSubCategory)) {
        dispatch(setActiveSubCategory(itemSubCategory, activeCategoryFullscreen?.id, true));
      }
    }
  }, [
    activeCategoryFullscreen,
    activeSubCategoryDPSFullscreen,
    listSubCategoriesDPS,
    isFullscreen
  ]);

  useEffect(() => {
    if (
      !isFullscreen &&
      (activeSubCategoryDSK.type === RIBBON_TYPE.FAVORITE_LIVE_TV ||
        activeSubCategoryDSK.type === RIBBON_TYPE.WATCHED_LIST)
    ) {
      dispatch(getListChannels({ type: activeSubCategoryDSK.type, isGlobal }));
    }
  }, [activeSubCategoryDSK, isFullscreen]);

  useEffect(() => {
    // xử lý phần fullscreen
    if (
      isFullscreen &&
      (activeSubCategoryDSKFullscreen.type === RIBBON_TYPE.FAVORITE_LIVE_TV ||
        activeSubCategoryDSKFullscreen.type === RIBBON_TYPE.WATCHED_LIST)
    ) {
      dispatch(getListChannels({ type: activeSubCategoryDSKFullscreen.type, isGlobal }));
    }
  }, [activeSubCategoryDSKFullscreen, isFullscreen]);

  useEffect(() => {
    if (
      !isFullscreen &&
      !isEmpty(activeSubCategoryDSK) &&
      activeSubCategoryDSK.type !== RIBBON_TYPE.WATCHED_LIST &&
      activeSubCategoryDSK.type !== RIBBON_TYPE.FAVORITE_LIVE_TV &&
      isEmpty(get(listChannels, activeSubCategoryDSK.id, {}))
    ) {
      dispatch(
        getListChannels({
          type: activeSubCategoryDSK.type,
          id: activeSubCategoryDSK.id,
          ribbonOrder: activeSubCategoryDSK.index,
          page: 0,
          limit: 30,
          isGlobal
        })
      );
    }
  }, [activeSubCategoryDSK, listChannels, isFullscreen]);

  useEffect(() => {
    // xử lý phần fullscreen
    if (
      isFullscreen &&
      !isEmpty(activeSubCategoryDSKFullscreen) &&
      activeSubCategoryDSKFullscreen.type !== RIBBON_TYPE.WATCHED_LIST &&
      activeSubCategoryDSKFullscreen.type !== RIBBON_TYPE.FAVORITE_LIVE_TV &&
      isEmpty(get(listChannels, activeSubCategoryDSKFullscreen.id, {}))
    ) {
      dispatch(
        getListChannels({
          type: activeSubCategoryDSKFullscreen.type,
          id: activeSubCategoryDSKFullscreen.id,
          ribbonOrder: activeSubCategoryDSKFullscreen.index,
          page: 0,
          limit: 30,
          isGlobal
        })
      );
    }
  }, [activeSubCategoryDSKFullscreen, listChannels, isFullscreen]);

  useEffect(() => {
    if (
      !isFullscreen &&
      !isEmpty(activeSubCategoryDPS) &&
      isEmpty(get(listChannels, activeSubCategoryDPS.id, {}))
    ) {
      dispatch(
        getListChannels({
          type: activeSubCategoryDPS.type,
          id: activeSubCategoryDPS.id,
          ribbonOrder: activeSubCategoryDPS.index,
          page: 0,
          limit: 30,
          isGlobal
        })
      );
    }
  }, [activeSubCategoryDPS, listChannels, isFullscreen]);

  useEffect(() => {
    // xử lý phần fullscreen
    if (
      isFullscreen &&
      !isEmpty(activeSubCategoryDPSFullscreen) &&
      isEmpty(get(listChannels, activeSubCategoryDPSFullscreen.id, {}))
    ) {
      dispatch(
        getListChannels({
          type: activeSubCategoryDPSFullscreen.type,
          id: activeSubCategoryDPSFullscreen.id,
          ribbonOrder: activeSubCategoryDPSFullscreen.index,
          page: 0,
          limit: 30,
          isGlobal
        })
      );
    }
  }, [activeSubCategoryDPSFullscreen, listChannels, isFullscreen]);

  useEffect(() => {
    let keyBreadcrumbs = SEO_PAGES.LIVETV;
    let path = asPath;
    if (slug) keyBreadcrumbs = SEO_PAGES.LIVETV_DETAIL;
    if (epg) {
      keyBreadcrumbs = SEO_PAGES.LIVETV_EPG;
      path = `${PAGE.LIVE_TV}/${slug}/`;
    }
    const curSEOSlug = get(dataSEOAllPage, 'seo.slug', '');
    if (isEmpty(dataSEOAllPage) || (curSEOSlug && curSEOSlug !== path)) {
      dispatch(getSEOAllPage({ slug: path, keyBreadcrumbs, uncheckURL: true }));
    }
  }, [dataSEOAllPage, asPath]);

  useEffect(() => {
    if (
      (!slug || slug?.[0] === '#') &&
      isEmpty(detailChannel) &&
      !isEmpty(listFilterChannelPage) &&
      !isEmpty(listSubCategoriesDSK)
    ) {
      const firstFilterId = get(listFilterChannelPage, '[0].id', '');
      const firstSubCategory = get(listSubCategoriesDSK, `${firstFilterId}[0]`, {});
      if (!isEmpty(firstSubCategory)) {
        let idFirstChannel = '';
        if (
          firstSubCategory.type === RIBBON_TYPE.FAVORITE_LIVE_TV &&
          !isEmpty(listFavoriteChannels)
        ) {
          idFirstChannel = get(listFavoriteChannels, '[0].id', '');
        } else if (
          firstSubCategory.type === RIBBON_TYPE.WATCHED_LIST &&
          !isEmpty(listWatchedChannels)
        ) {
          idFirstChannel = get(listWatchedChannels, '[0].id', '');
        } else if (
          firstSubCategory.type !== RIBBON_TYPE.WATCHED_LIST &&
          firstSubCategory.type !== RIBBON_TYPE.FAVORITE_LIVE_TV &&
          !isEmpty(get(listChannels, firstSubCategory.id, {}))
        ) {
          idFirstChannel = get(listChannels, `${firstSubCategory.id}.items[0].id`, '');
        }
        if (idFirstChannel) {
          if (sessionToken) handleEndSessionPlay(sessionToken);
          dispatch(getDetailChannelById(idFirstChannel, iOS));
        }
      }
    }
  }, [
    slug,
    detailChannel,
    listFilterChannelPage,
    listChannels,
    listFavoriteChannels,
    listWatchedChannels,
    listSubCategoriesDSK
  ]);

  useEffect(() => {
    const paramsUrl = parseParamsUrlLiveTv(detailChannel?.link); // tương đương seo?.url
    const slugChannel = paramsUrl?.slug || '';

    if (
      slug &&
      slug[0] !== '#' &&
      deviceId &&
      (isEmpty(detailChannel) || slugChannel !== slug) &&
      lastSlugChannelRef.current !== slug // Only trigger if slug is different
    ) {
      lastSlugChannelRef.current = slug;

      SocketCluster.unsubscribe();

      if (sessionToken) {
        handleEndSessionPlay(sessionToken);
      }

      dispatch(
        getDetailChannelBySlug({
          slug: asPath,
          epg,
          deviceId
        })
      );
    }
  }, [slug, detailChannel?.link, deviceId, sessionToken]);

  useEffect(() => {
    if (concurrentScreen?.code === ERROR_CODE.CODE_0) {
      const timer = dataRefreshSession?.countdownBySec || concurrentScreen?.countdownBySec;
      const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;

      if (timeRefreshToken.current) clearTimeout(timeRefreshToken.current);
      timeRefreshToken.current = setTimeout(() => {
        dispatch(refreshSessionPlay(sessionToken)).then((res: any) => {
          if (
            !res?.success ||
            res?.httpCode === HTTP_CODE.TOO_MANY_REQUEST ||
            res?.code === ERROR_CODE.CODE_3
          ) {
            handleEndSessionPlay(sessionToken);
            clearTimeout(timeRefreshToken.current);
          }
        });
      }, timer * 1000);
    }

    return () => {
      if (timeRefreshToken.current) clearTimeout(timeRefreshToken.current);
    };
  }, [concurrentScreen, dataRefreshSession, sessionToken]);

  useEffect(() => {
    const paramsUrl = parseParamsUrlLiveTv(detailChannel?.epg?.seo?.url);
    const slugChannel = paramsUrl?.slug || '';
    const epgChannel = paramsUrl?.epg || '';

    if (slug && slug[0] !== '#' && slugChannel === slug && epg && epg !== epgChannel && deviceId) {
      if (sessionToken) handleEndSessionPlay(sessionToken);

      dispatch(getDetailChannelBySlug({ slug: asPath, epg, deviceId }));
    }
  }, [detailChannel?.link, slug, epg, deviceId]);

  useEffect(() => {
    if (detailChannel?.id) {
      window.scrollTo({ left: 0, top: 0, behavior: 'smooth' });

      dispatch(getListEpgs({ id: detailChannel.id, strDate: moment().format('YYYY-MM-DD') }));
    }
  }, [detailChannel?.id]);

  useEffect(() => {
    if (detailChannel?.id === oldChannelDetail?.current?.id) {
      return;
    }

    if (detailChannel?.id && getDeviceId()) {
      const isNotGoToPayment = !oldChannelDetail?.current?.id;
      checkChannelPermission(detailChannel, isNotGoToPayment);
    }
    if (!profile?.id && !router?.query?.slug) {
      return;
    }
    if (oldChannelDetail?.current?.id !== detailChannel?.id) {
      oldChannelDetail.current = detailChannel;
    }
  }, [detailChannel?.id, getDeviceId()]);

  useEffect(() => {
    if (
      detailChannel?.id &&
      detailChannel.qnetDrm &&
      !detailChannel?.qnetInfo &&
      detailChannel?.permission === PERMISSION.CAN_WATCH
    ) {
      dispatch(getQNetInfo({ contentId: detailChannel.id, type: 'livetv' }));
    }
  }, [
    detailChannel?.id,
    detailChannel?.qnetInfo,
    detailChannel?.qnetDrm,
    detailChannel?.permission
  ]);

  useEffect(() => {
    const epgValid = checkValidEpg(detailChannel?.epg);
    if (detailChannel?.epg?.id && epg && !epgValid && deviceId) {
      SocketCluster.unsubscribe();

      if (sessionToken) handleEndSessionPlay(sessionToken);

      dispatch(getDetailChannelBySlug({ slug: detailChannel?.link, deviceId }));
    }

    dispatch(setActiveEpg(detailChannel?.epg));
  }, [detailChannel?.epg?.id]);

  useEffect(() => {
    if (!isEmpty(comingSoonListIdEpgs) && comingSoonListIdEpgs.length > 0 && profile?.id) {
      dispatch(getInfoNotifyListEpgs(comingSoonListIdEpgs));
    }
  }, [comingSoonListIdEpgs, profile?.id]);

  useEffect(() => {
    if (detailChannel?.id && !isEmpty(activeEpg) && !isEmpty(liveEpg) && !isEmpty(nextEpg)) {
      timerToCheckNextEpgForLive(detailChannel?.id, activeEpg, liveEpg, nextEpg);
    }
    return () => {
      clearInterval(timerCheckEpgLiveRef.current);
    };
  }, [detailChannel?.id, activeEpg, liveEpg, nextEpg]);

  useEffect(() => {
    window.scrollTo({ left: 0, top: 0, behavior: 'smooth' });
  }, [activeEpg?.id]);

  const timerToCheckNextEpgForLive = (idChannel: any, playing: any, living: any, next: any) => {
    if (!idChannel || isEmpty(living) || isEmpty(next) || isEmpty(playing)) return;
    timerCheckEpgLiveRef.current = setInterval(() => {
      const timeNow = Date.now() / 1000;
      if (timeNow >= next.start) {
        dispatch(getListEpgs({ id: idChannel, strDate: moment().format('YYYY-MM-DD') }));
        if (playing.id === living.id) {
          const epgLiveUpdate = {
            ...playing,
            ...omit(next, [
              'hlsLinkPlay',
              'dashLinkPlay',
              'isLive',
              'isCatchUp',
              'isComingSoon',
              'isNotifyComingSoon'
            ])
          };
          dispatch(setActiveEpg(epgLiveUpdate));
        }
        clearInterval(timerCheckEpgLiveRef.current);
      }
    }, 7000);
  };

  const checkValidEpg = (epgData: any) => {
    if (isEmpty(epgData) || !epgData?.id) return false;
    let valid = true;
    if (epgData.isComingSoon) {
      valid = false;
      const message = parseTimeStartNotify(epgData.start);
      dispatch(setToast({ message }));
    } else if (!epgData.isLive && !epgData.isCatchUp) {
      valid = false;
      dispatch(setToast({ message: TEXT.EPG_OLD }));
    }
    return valid;
  };

  const onSigmaLoaded = () => {
    setSigmaLoaded(true);
  };
  const handleEndSessionPlay = (value: any) => {
    dispatch(endSessionPlay(value));
  };

  const checkChannelPermission = (dataChannel: any, isNotGoToPayment: any) => {
    const permission = dataChannel?.permission;
    const packages = dataChannel?.packages;
    const forceLogin = dataChannel?.forceLogin;
    const drmServiceName = dataChannel?.drmServiceName;
    const isVip = dataChannel?.isPremium === 1;
    const isKPlus = dataChannel?.isKPlus;
    const isHBO = dataChannel?.isHBO;
    const contentConcurrentGroup = dataChannel?.contentConcurrentGroup;
    const groupPackageId = dataChannel?.packages?.[0]?.id || 0;
    let trigger = '';
    let segmentPopupName = '';
    let action = null;
    let { popupName, goToBuyPackage, authTrigger }: any = parsePopupParams({
      profile,
      currentProfile,
      contentType: CONTENT_TYPE.LIVE_TV,
      drmServiceName,
      permission,
      forceLogin,
      groupPackageId,
      isVip,
      isMobile,
      isGlobal,
      router,
      contentDetail: dataChannel
    });
    if (permission === PERMISSION.PAYMENT) {
      trigger = VALUE.VIP_CONTENT;
      action = {
        func: () => {
          if (isGlobal) {
            trackingPayment.globalPaymentButtonSelected({ currentPage: VALUE.LIVETV_PAGE });
          }
          onOpenPayment(router, {
            returnUrl: window?.location?.href,
            pkg: groupPackageId,
            curPage: VALUE.LIVETV_PAGE,
            newTriggerPaymentBuyPackage: {
              isGlobal,
              profileId: profile?.id
            }
          });
        }
      };
      if (goToBuyPackage && !isNotGoToPayment) {
        if (typeof action?.func === 'function') {
          action.func();
        }
      }
    } else if (permission === PERMISSION.NON_LOGIN) {
      action = {
        func: () =>
          onOpenPayment(router, {
            groupPackageId,
            returnUrl: window?.location?.href,
            pkg: groupPackageId,
            newTriggerPaymentBuyPackage: {
              isGlobal,
              profileId: profile?.id
            }
          })
      };
      if (isVip) {
        segmentPopupName = VALUE.POPUP_NAME.LIVETV_VIP;
      }
      if (isKPlus) {
        segmentPopupName = VALUE.POPUP_NAME.LIVETV_K_PLUS;
      }
    } else if (
      permission === PERMISSION.LIMITED_DEVICE ||
      permission === PERMISSION.DONT_ALLOW_BROADCAST
    ) {
      popupName = POPUP.NAME.LIMIT_CCU;
      if (permission === PERMISSION.DONT_ALLOW_BROADCAST) {
        popupName = POPUP.NAME.LIMIT_EPG;
      }
    }
    if (!renderNotFoundPage && (!goToBuyPackage || isNotGoToPayment)) {
      if (popupName) {
        if (
          popupName === POPUP.NAME.LIMIT_CCU &&
          contentConcurrentGroup &&
          dataChannel?.isConcurrentScreenLimit
        ) {
          dispatch(
            openPopup({
              name: POPUP.NAME.CONCURRENT_SCREEN,
              sessionList: concurrentScreen?.sessionList,
              contentConcurrentGroup,
              isHBO,
              isKPlus
            })
          );
          return;
        }
        if (
          ((isVip && profile?.type !== USER_TYPE.VIP) || dataChannel?.isPremium === 1) &&
          !isGlobal
        ) {
          dispatch(
            getPopupTriggerDialog({
              type: 'svod',
              contentId: dataChannel?.id,
              contentType: CONTENT_TYPE.LIVE_TV
            })
          );
          dispatch(
            openPopup({
              name: POPUP.NAME.SVOD_TRIGGER,
              packageName: popupName === POPUP.NAME.KID_ACCESS_SVOD ? packages?.[0]?.name : '',
              id: dataChannel?.id,
              trigger,
              action,
              segmentPopupName,
              contentType: CONTENT_TYPE.LIVE_TV,
              contentConcurrentGroup,
              drmServiceName,
              data: detailChannel,
              isRevisePayment: true
            })
          );
        } else {
          dispatch(
            openPopup({
              name: popupName,
              packageName: popupName === POPUP.NAME.KID_ACCESS_SVOD ? packages?.[0]?.name : '',
              id: dataChannel?.id,
              trigger,
              action,
              segmentPopupName,
              contentType: CONTENT_TYPE.LIVE_TV,
              contentConcurrentGroup,
              drmServiceName,
              data: detailChannel
            })
          );
        }

        return;
      }
      if (authTrigger) {
        const remakeDestination = encodeParamDestination(asPath);
        router.push(
          `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${authTrigger}`
        );
      }
    }
  };

  if (renderNotFoundPage) {
    // return if page not found and will not run all under effect
    const makeupDataRibbons = { 0: listRibbonNotFound?.data || [] };
    return <ContentNotFound dataRibbon={makeupDataRibbons} isLiveTv />;
  }

  return (
    <>
      <Head>
        <script
          defer
          async
          type="text/javascript"
          src={`${STATIC_DOMAIN}assets/js/socketcluster-client.js?v=${BUILD_ID}`}
        />
      </Head>
      <SeoText seo={dataSEOAllPage?.seo} />
      {((isClient && sigmaLoaded) || !isClient) && (
        <LiveTVPlayer
          checkChannelPermission={checkChannelPermission}
          handleEndSessionPlay={handleEndSessionPlay}
          setToast={setToast}
        />
      )}
      <Categories />
      <SeoAllPage {...dataSEOAllPage} listArrRibbon={listArrRibbon} />
      {(isAndroid || (isWindows && SIGMA_DRM_WINDOWS) || (isMacOs && !isSafari)) && (
        <SigmaDRM onLoaded={onSigmaLoaded} />
      )}
    </>
  );
};

LiveTVContainer.getInitialProps = async ({ store, req, res, query }: any) => {
  if (req) {
    const { App, Detail } = store.getState();
    const { geoCheck, token: accessToken, isIOS, isSafari } = App || {};
    const iOS = isIOS || isSafari;
    const isGlobal = geoCheck?.isGlobal;
    const cookie = req?.cookies;
    const { concurrentScreen, dataRefreshSession } = Detail;
    const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
    const keySignature = ConfigCookie.KEY.SIGNATURE;
    const { profileToken } =
      decodeSignature({
        value: cookie?.[keySignature] || getCookie(keySignature, req?.headers.cookie) || ''
      }) || {};
    const { slug, epg } = query || {};
    const origin = req.headers.host;
    const userAgent = req.headers['user-agent'];
    const ipAddress = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '')
      .split(':')
      .pop();
    let renderNotFoundPage = false;
    const path = req.originalUrl || req.path;
    let keyBreadcrumbs = SEO_PAGES.LIVETV;

    if (req.originalUrl === '/truyen-hinh-truc-tuyen') {
      res.status(410);
      renderNotFoundPage = true;
      await store.dispatch(
        getRibbonLiveTvNotFound({
          accessToken,
          profileToken,
          ssr: true,
          ipAddress,
          userAgent,
          origin
        })
      );
    }

    if (slug) {
      keyBreadcrumbs = SEO_PAGES.LIVETV_DETAIL;
      if (sessionToken) {
        store.dispatch(endSessionPlay(sessionToken));
      }
      const response = await store.dispatch(
        getDetailChannelBySlug({
          slug: req.path,
          accessToken,
          profileToken,
          epg,
          ssr: true,
          ipAddress,
          userAgent,
          iOS,
          origin
        })
      );

      const epgData = response?.epg;
      if (response.error === 400 || (epg && !epgData?.id)) {
        res.status(epg ? 410 : 404);
        renderNotFoundPage = true;
        await store.dispatch(
          getRibbonLiveTvNotFound({
            accessToken,
            profileToken,
            ssr: true,
            ipAddress,
            userAgent,
            origin
          })
        );
      }
    }

    if (epg) {
      keyBreadcrumbs = SEO_PAGES.LIVETV_EPG;
    }

    // Get First Ribbon
    await Promise.all([
      (async () => {
        const resp = await store.dispatch(
          getSEOAllPage({
            accessToken,
            profileToken,
            slug: path,
            keyBreadcrumbs,
            ssr: true,
            uncheckURL: true,
            ipAddress,
            userAgent,
            origin
          })
        );
        redirectTool({ redirect: resp?.data?.redirect, res });
      })(),
      (async () => {
        const listFilterChannelPage = await store.dispatch(
          getListFilterChannelPage({ ipAddress, userAgent, origin })
        );
        const pageRibbon1st = listFilterChannelPage?.[0];
        if (!isEmpty(pageRibbon1st)) {
          const listSubCategoriesDSK = await store.dispatch(
            getListSubCategoriesOfChannelList({
              id: pageRibbon1st?.id,
              accessToken,
              profileToken,
              ssr: true,
              ipAddress,
              userAgent,
              origin
            })
          );

          if (!isEmpty(listSubCategoriesDSK)) {
            const dataDefault =
              typeof window !== 'undefined'
                ? listSubCategoriesDSK?.[0]
                : listSubCategoriesDSK.find((subCategory: any) => subCategory.name === 'Nổi bật') ||
                  {};
            if (!isEmpty(dataDefault)) {
              await store.dispatch(
                getListChannels({
                  id: dataDefault?.id,
                  type: dataDefault?.type,
                  page: 0,
                  limit: 50,
                  accessToken,
                  profileToken,
                  ssr: true,
                  ipAddress,
                  userAgent,
                  isGlobal,
                  origin
                })
              );
            }
          }
        }
      })()
    ]);

    return {
      renderNotFoundPage
    };
  }

  return {};
};

export default LiveTVContainer;
