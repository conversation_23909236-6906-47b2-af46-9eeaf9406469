import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { getListNotification, statusConfirmPolicyAnnounce } from '@actions/notification';
import Notification from '@components/header/notification/Notification';
import { checkIsEndPage } from '@helpers/common';
import NotificationApi from '@apis/cm/NotificationApi';
import { ACTION_NOTIFY, HEADER_LAYER_NAME } from '@constants/constants';
import isEmpty from 'lodash/isEmpty';

// Define the props that will be passed to the component from parent components
interface OwnProps {
  profile: any;
  isPayment: any;
}

// Define the props that will be injected by Redux
interface ReduxProps {
  getListNotify?: any;
  statusConfirmPolicyAnnounce?: any;
  isConfirmPolicyAnnounce?: any;
  closeNotify?: any;
  isMobile?: any;
  layerName?: any;
  closeMenu?: any;
  isOffPaneNav?: any;
  setOffPaneNav?: any;
}

// Combined props type
type Props = OwnProps & ReduxProps;

class NotificationContainer extends Component<Props> {
  constructor(props: Props) {
    super(props);
    this.state = {
      listDataAllPage: null,
      page: 1,
      limit: 50,
      metadata: { page: 1, limit: 50, total: 0 },
      unread: 0,
      isLoadMore: true
    };
  }

  componentDidMount() {
    const { profile }: any = this.props;
    if (profile?.id) {
      this.getListNotify();
    }
  }
  componentDidUpdate() {
    const { profile, isConfirmPolicyAnnounce }: any = this.props;
    const { statusConfirmPolicyAnnounce }: any = this.props;
    if (profile?.id && isConfirmPolicyAnnounce) {
      if (typeof statusConfirmPolicyAnnounce === 'function') {
        statusConfirmPolicyAnnounce(false);
      }
      this.getListNotify();
    }
  }

  getListNotify = () => {
    let { metadata, listDataAllPage }: any = this.state;
    const { getListNotify }: any = this.props;
    const { page, limit } = metadata;
    getListNotify({ page, limit }).then((res: any) => {
      if (res?.data?.success) {
        const listDataPageNew = res.data?.data || {};
        metadata = {
          limit: listDataPageNew.limit,
          page: listDataPageNew.page,
          total: listDataPageNew.total
        };
        this.setState({
          listDataAllPage: { ...listDataAllPage, [page]: listDataPageNew?.notifications },
          metadata,
          isLoadMore: !checkIsEndPage(metadata),
          unread: listDataPageNew.unread
        });
      }
    });
  };

  handleReadNotify = (e: any, action: any, notifyId: any) => {
    let { listDataAllPage, metadata, unread }: any = this.state;
    const { getListNotify }: any = this.props;
    const { page } = metadata;
    if (!isEmpty(listDataAllPage?.[1])) {
      if (action === ACTION_NOTIFY.READ_ALL && !unread) return;
      NotificationApi.readNotification({ notifyId, action }).then((res) => {
        if (res?.success) {
          getListNotify({ page, limit: metadata.limit }).then((resp: any) => {
            if (resp?.data?.success) {
              const listDataPageNew = resp?.data?.data || [];
              metadata = {
                limit: listDataPageNew.limit,
                page: listDataPageNew.page,
                total: listDataPageNew.total
              };
              this.setState({
                listDataAllPage: { ...listDataAllPage, [page]: listDataPageNew?.notifications },
                metadata,
                unread: listDataPageNew.unread,
                isLoadMore: !checkIsEndPage(metadata)
              });
            }
          });
        }
      });
    }
  };

  onLoadMore = async () => {
    let { page, limit, listDataAllPage, metadata }: any = this.state;
    const { getListNotify }: any = this.props;
    if (page * limit < metadata.total) {
      await getListNotify({ page, limit }).then((res: any) => {
        if (res?.data?.success) {
          const listDataPageNew = res.data.data || [];
          metadata = {
            limit: listDataPageNew.limit,
            page: listDataPageNew.page,
            total: listDataPageNew.total
          };
          this.setState({
            listDataAllPage: { ...listDataAllPage, [page]: listDataPageNew?.notifications },
            metadata,
            page: page + 1,
            isLoadMore: !checkIsEndPage(metadata)
          });
        }
      });
    }
  };

  render() {
    const {
      isPayment,
      profile,
      closeNotify,
      isMobile,
      layerName,
      closeMenu,
      isOffPaneNav,
      setOffPaneNav
    }: any = this.props;
    const { listDataAllPage, isLoadMore, unread }: any = this.state;
    if (isPayment || !profile || (isMobile && layerName !== HEADER_LAYER_NAME.NOTIFY)) {
      return null;
    }
    return (
      <Notification
        listDataAllPage={listDataAllPage}
        isLoadMore={isLoadMore}
        unread={unread}
        isMobile={isMobile}
        closeMenu={closeMenu}
        readNoti={this.handleReadNotify}
        onLoadMore={this.onLoadMore}
        closeNotify={closeNotify}
        setOffPaneNav={setOffPaneNav}
        isOffPaneNav={isOffPaneNav}
      />
    );
  }
}

const mapStateToProps = ({ Notification }: any) => ({ ...(Notification || {}) });

const mapDispatchToProps = (dispatch: any) => {
  const actions = bindActionCreators(
    {
      getListNotify: getListNotification,
      statusConfirmPolicyAnnounce: statusConfirmPolicyAnnounce
    },
    dispatch
  );
  return { ...actions, dispatch };
};

// Use explicit typing for the connected component to preserve the OwnProps
export default connect<any, any, OwnProps>(
  mapStateToProps,
  mapDispatchToProps
)(NotificationContainer);
