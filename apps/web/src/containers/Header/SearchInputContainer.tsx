import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getTrendKeywords, getSearchSuggest, getSearchHistory } from '@actions/search';
import SearchInput from '@components/header/search/SearchInput';
const SearchInputContainer = React.memo(({ isMobile, setOffPaneNav, isOffPaneNav }: any) => {
  const dispatch = useDispatch();
  const trendKeyword = useSelector((state: any) => state?.Search?.SEARCH_TREND_KEYWORD);
  const searchHistory = useSelector((state: any) => state?.Search?.SEARCH_HISTORY);
  const userProfile = useSelector((state: any) => state?.Profile?.profile);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { searchItem } = useSelector((state: any) => state?.App?.outStreamAds || {});

  const handleOnFirstFocus = () => {
    Promise.all([handleGetTrendKeyword(), handleGetSearchHistory()]);
  };

  const handleGetSearchHistory = () => {
    if (userProfile && !searchHistory) {
      dispatch(getSearchHistory());
    }
  };

  const handleGetTrendKeyword = () => {
    if (!trendKeyword) dispatch(getTrendKeywords({ page: 0, limit: 10, isGlobal }));
  };

  const getSearchSuggestHandler = ({ keyword }: any) => dispatch(getSearchSuggest({ keyword }));

  return (
    <SearchInput
      dataSearchTrendKeyword={trendKeyword || null}
      dataSearchHistory={searchHistory || null}
      userProfile={userProfile}
      isMobile={isMobile}
      searchItemAds={searchItem}
      getSearchSuggest={getSearchSuggestHandler}
      openConfirmDel={() => {}}
      onFirstFocus={handleOnFirstFocus}
      isOffPaneNav={isOffPaneNav}
      setOffPaneNav={setOffPaneNav}
    />
  );
});

export default SearchInputContainer;
