import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withRouter } from 'next/router';
import { NextRouter } from 'next/router';
import debounce from 'lodash/debounce';
import { setToast } from '@actions/app';
import { setActiveMenuFromPath } from '@actions/menu';
import { openPopup, previewCard } from '@actions/popup';
import { getProfile } from '@actions/profile';
import {
  getConfigMessage,
  getUserReport,
  setCurrentHeightHeader,
  getConfigPackage
} from '@actions/user';
import { CONFIG_KEY, CONTENT_TYPE, PAGE, PERMISSION } from '@constants/constants';
import Header from '@components/header/Header';
import HeaderMobile from '@components/header/mobile/HeaderMobile';
import HeaderPayment from '@components/header/HeaderPayment';
import isEmpty from 'lodash/isEmpty';
import { WithRouterProps } from 'next/dist/client/with-router';

// Define a separate interface for the props we want to pass directly
interface HeaderContainerOwnProps {
  isPayment?: boolean;
  renderNotFoundPage?: boolean;
  emailVerified?: boolean;
  [key: string]: any;
}

// Combine with WithRouterProps for the component's actual props
type HeaderContainerProps = HeaderContainerOwnProps & WithRouterProps;
class HeaderContainer extends Component<HeaderContainerProps, any> {
  elementRef: any;
  isPathnameVodPage: any;
  lastScrollTop: any;
  oldUrl: any;
  constructor(props: any) {
    super(props);
    this.elementRef = React.createRef();
    this.state = {
      emptyHeader: false,
      isOpenBenefit: true,
      isHideInDetailPage: props.pathname?.includes(PAGE.VOD)
    };
    this.lastScrollTop = 0;
    const url = (props.router?.asPath || '').split('?')?.[0] || '/';
    this.oldUrl = url;
    this.isPathnameVodPage = props.pathname?.includes(PAGE.VOD);
  }

  componentDidMount() {
    const {
      isUnderConstruction,
      getUserReport,
      getConfigMessage,
      router,
      setActiveMenuFromPath,
      isMobile,
      ACTION_GET_CONTENT_DETAIL_ID
    }: any = this.props;

    const isTriggerTrialLoginDuration =
      (ACTION_GET_CONTENT_DETAIL_ID?.permission === PERMISSION.NON_LOGIN &&
        ACTION_GET_CONTENT_DETAIL_ID?.triggerLoginDuration > 0) ||
      ACTION_GET_CONTENT_DETAIL_ID?.forceLogin === PERMISSION.FORCE_LOGIN;

    if ((this.isPathnameVodPage && window.scrollY <= 0) || isTriggerTrialLoginDuration) {
      this.setState({ isHideInDetailPage: true });
    }
    if (!isUnderConstruction) {
      getUserReport();
      getConfigMessage(CONFIG_KEY.MSG_CONFIG);
      window.addEventListener('scroll', debounce(this.checkScrollHeader, 200), false);
      const asPath = router?.asPath;
      const url = (asPath || '').split('?')?.[0] || '/';
      setActiveMenuFromPath(url);
      if (router?.pathname === PAGE.VOD && isMobile) {
        const {
          permission,
          trialDuration,
          triggerLoginDuration,
          isTriggerToApp,
          isEpisodeTrialInApp,
          isMovieTrialInApp
        } = ACTION_GET_CONTENT_DETAIL_ID || {};
        const isCanWatch =
          (permission === PERMISSION.CAN_WATCH ||
            (permission !== PERMISSION.CAN_WATCH &&
              (trialDuration > 0 || triggerLoginDuration > 0))) &&
          (!isTriggerToApp || isEpisodeTrialInApp || isMovieTrialInApp);
        if (isCanWatch) this.setState({ emptyHeader: true });
      }
    }
  }

  componentDidUpdate() {
    const {
      router,
      ACTION_GET_CONTENT_DETAIL_ID,
      setCurrentHeightHeader,
      pathname,
      setActiveMenuFromPath,
      previewCard,
      openPopup,
      isMobile
    }: any = this.props;

    const { isHideInDetailPage }: any = this.state;

    const asPath = router?.asPath;
    const url = (asPath || '').split('?')?.[0] || '/';

    const {
      permission,
      trialDuration,
      triggerLoginDuration,
      isTriggerToApp,
      isEpisodeTrialInApp,
      isMovieTrialInApp,
      forceLogin
    } = ACTION_GET_CONTENT_DETAIL_ID || {};

    const isTriggerTrialLoginDuration =
      (permission === PERMISSION.NON_LOGIN && triggerLoginDuration > 0) ||
      forceLogin === PERMISSION.FORCE_LOGIN;

    const isCanWatch =
      (permission === PERMISSION.CAN_WATCH ||
        (permission !== PERMISSION.CAN_WATCH && (trialDuration > 0 || triggerLoginDuration > 0))) &&
      (!isTriggerToApp || isEpisodeTrialInApp || isMovieTrialInApp);

    const shouldHideHeader =
      (pathname?.includes(PAGE.VOD) && window.scrollY <= 0) ||
      (isTriggerTrialLoginDuration && pathname?.includes(PAGE.VOD));
    if (isHideInDetailPage !== shouldHideHeader) {
      this.setState({ isHideInDetailPage: shouldHideHeader });
    }

    if (!isCanWatch && this.elementRef?.current) {
      if (setCurrentHeightHeader) setCurrentHeightHeader(this.elementRef?.current?.offsetHeight);
    }

    const isUrlChanged = url !== this.oldUrl;
    if (isUrlChanged) {
      setActiveMenuFromPath(url);
      previewCard({ expand: false, data: null, offset: null });
      openPopup();
      this.oldUrl = url;

      if (router?.pathname === PAGE.VOD && isMobile && isCanWatch) {
        this.setState({ emptyHeader: true });
      } else {
        this.setState({ emptyHeader: false });
      }
    }
  }

  componentWillUnmount() {
    window.removeEventListener('scroll', this.checkScrollHeader, false);
    // window.removeEventListener('online', this.handleOnline);
    // window.removeEventListener('offline', this.handleOffline);
  }
  isBottomPlayerDetailPage = (el: any) => el?.clientHeight <= window.scrollY;

  setStatusBenefitInApp = () => {
    this.setState({ isOpenBenefit: false });
  };

  onClickMenu = (link: any) => {
    if (!window) return;
    const store = window.__NEXT_REDUX_STORE__;
    const profile = store && store?.getState()?.Profile?.profile;
    if (link) {
      let navigateUrl = link;
      if (link?.includes('{accesstoken}') && profile && profile?.token) {
        navigateUrl = link.replace('{accesstoken}', profile.token);
      }
      window.open(navigateUrl, '_blank');
    } else {
      this.handleOpenSubMenu(false);
    }
  };

  // handleOnline = () => {
  //   const { setToast } = this.props;
  //   setToast({ message: TEXT.ONLINE });
  // };

  // handleOffline = () => {
  //   const { setToast } = this.props || {};
  //   setToast({ message: TEXT.DISCONNECT });
  // };

  checkScrollHeader = (): void => {
    const { ACTION_GET_CONTENT_DETAIL_ID, pathname }: any = this.props;
    const {
      permission,
      trialDuration,
      triggerLoginDuration,
      isTriggerToApp,
      isEpisodeTrialInApp,
      isMovieTrialInApp
    } = ACTION_GET_CONTENT_DETAIL_ID || {};
    const isCanWatch =
      (permission === PERMISSION.CAN_WATCH ||
        (permission !== PERMISSION.CAN_WATCH && (trialDuration > 0 || triggerLoginDuration > 0))) &&
      (!isTriggerToApp || isEpisodeTrialInApp || isMovieTrialInApp);
    const st = window.pageYOffset || document.documentElement.scrollTop;
    const bodyEl: any = document.getElementsByTagName('body')[0];
    if (st > this.lastScrollTop && st > 30) {
      // scroll down
      bodyEl.classList.add('scrolling');
      bodyEl.classList.remove('scroll-up');
      bodyEl.classList.add('scroll-down');
      this.handleOpenSubMenu(false);
    } else if (st < this.lastScrollTop) {
      // scroll up
      bodyEl.classList.remove('scroll-down');
      bodyEl.classList.add('scrolling');
      bodyEl.classList.add('scroll-up');
      this.handleOpenSubMenu(false);
    }
    if (st === 0) {
      bodyEl.classList.remove('scrolling');
      bodyEl.classList.remove('scroll-up');
      bodyEl.classList.remove('scroll-down');
    }
    this.lastScrollTop = st <= 0 ? 0 : st; // For Mobile or negative scrolling
    // header in detail page
    if (pathname?.includes(PAGE.VOD) && isCanWatch) {
      const vodDetailPlayerElement = document.getElementsByClassName('section--vod-detail')[0];

      if (vodDetailPlayerElement && this.isBottomPlayerDetailPage(vodDetailPlayerElement)) {
        this.setState({ isHideInDetailPage: false });
      } else {
        this.setState({ isHideInDetailPage: true });
      }
    }
  };

  handleOpenSubMenu = (value: any) => {
    this.setState({ subMenuOpen: !!value });
  };
  render() {
    const {
      emailVerified,
      isPayment,
      USER_TYPE,
      dataNotify,
      subHeader,
      activeSubMenu,
      activeMenu,
      menuList,
      profile,
      introPackages,
      openPopup,
      featureFlag,
      isMobile,
      pathname,
      ACTION_GET_CONTENT_DETAIL_ID,
      renderNotFoundPage,
      router
    }: any = this.props;
    const { emptyHeader, isOpenBenefit, isHideInDetailPage }: any = this.state;
    const {
      permission,
      trialDuration,
      triggerLoginDuration,
      isTriggerToApp,
      isEpisodeTrialInApp,
      isMovieTrialInApp
    } = ACTION_GET_CONTENT_DETAIL_ID || {};
    const isCanWatch =
      ((permission === PERMISSION.CAN_WATCH ||
        (permission !== PERMISSION.CAN_WATCH && (trialDuration > 0 || triggerLoginDuration > 0))) &&
        (!isTriggerToApp || isEpisodeTrialInApp || isMovieTrialInApp)) ||
      ACTION_GET_CONTENT_DETAIL_ID?.forceLogin == PERMISSION.FORCE_LOGIN;
    const isRentalContent = (pathname || '').includes(PAGE.RENTAL_CONTENT);
    const isVoucher = pathname.includes(PAGE.VOUCHER);
    const { query } = router || {};
    const { category } = query || {};
    const isShortVideoContent = +category === CONTENT_TYPE.SHORT_CONTENT;

    if (emailVerified || (emptyHeader && !renderNotFoundPage)) return null;
    if (isPayment || isVoucher || isRentalContent) {
      return <HeaderPayment />;
    }
    if (isMobile) {
      return (
        <HeaderMobile
          featureFlag={featureFlag}
          menuList={menuList}
          profile={profile}
          activeMenu={activeMenu}
          activeSubMenu={activeSubMenu}
          USER_TYPE={USER_TYPE}
          isPayment={isPayment}
          isMobile={isMobile}
          dataCountNotification={dataNotify?.data}
          subHeader={subHeader}
          openPopup={openPopup}
          onClickMenu={this.onClickMenu}
          isOpenBenefit={isOpenBenefit}
          setStatusBenefitInApp={this.setStatusBenefitInApp}
          isCanNotWatch={!isCanWatch && pathname?.includes(PAGE.VOD)}
          ref={this.elementRef}
          contentDetail={ACTION_GET_CONTENT_DETAIL_ID}
        />
      );
    }
    if (
      !isHideInDetailPage ||
      (!isCanWatch && !isEmpty(ACTION_GET_CONTENT_DETAIL_ID)) ||
      renderNotFoundPage ||
      isShortVideoContent
    ) {
      return (
        <Header
          introPackages={introPackages}
          menuList={menuList}
          profile={profile}
          activeMenu={activeMenu}
          activeSubMenu={activeSubMenu}
          USER_TYPE={USER_TYPE}
          isPayment={isPayment}
          isMobile={isMobile}
          subHeader={subHeader}
          openPopup={openPopup}
          onClickMenu={this.onClickMenu}
          isCanNotWatch={!isCanWatch && pathname?.includes(PAGE.VOD)}
          ref={this.elementRef}
        />
      );
    }
    return null;
  }
}

const mapStateToProps = (state: any) => {
  const { Menu, Profile, User, Popup, App, Notification, Detail } = state || {};
  return {
    ...(Menu || {}),
    ...(Profile || {}),
    ...(User || {}),
    ...(Popup || {}),
    ...(App || {}),
    ...(Notification || {}),
    ...(Detail || {})
  };
};

const mapDispatchToProps = (dispatch: any) => {
  const actions = bindActionCreators(
    {
      setActiveMenuFromPath: setActiveMenuFromPath,
      getProfile: getProfile,
      openPopup: openPopup,
      getUserReport: getUserReport,
      getConfigMessage: getConfigMessage,
      getConfigPackage: getConfigPackage,
      previewCard: previewCard,
      setToast: setToast,
      setCurrentHeightHeader: setCurrentHeightHeader
    },
    dispatch
  );
  return { ...actions, dispatch };
};

// Create a properly typed connected component
const ConnectedHeaderContainer = connect(mapStateToProps, mapDispatchToProps)(HeaderContainer);

// Export with withRouter and proper typing
export default withRouter(ConnectedHeaderContainer as any);
