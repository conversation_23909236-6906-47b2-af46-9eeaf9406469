import { SOCKET_SERVER } from './ConfigEnv'

export default {
  SERVER: SOCKET_SERVER || "",
  EVENTS: {
    CANCEL_PLAYER: 'cancel_player',
    SHOW_FINGERING: 'show_fingering',
    CONNECTED: 'CONNECTED',
    DISCONNECTED: 'DISCONNECTED',
    ERROR: 'ERROR',
  },
  SHOW_FINGERING_TIMER: 15,
  POSITION: {
    TOP_LEFT: "top_left",
    TOP_RIGHT: "top_right",
    BOTTOM_LEFT: "bottom_left",
    BOTTOM_RIGHT: "bottom_right",
    MIDDLE: "middle",
    RANDOM: "random",
  },
  MSG_TYPE: {
    LIMIT_STREAM: "limit-stream",
    LIMIT_EPG: "limit-epg",
    BLOCK_ACCOUNT: "block-account",
    ERROR: "error",
  },
  POSITION_LIVE_STREAM: {
    TOP_LEFT: "top-left",
    TOP_RIGHT: "top-right",
    BOTTOM_LEFT: "bottom-left",
    BOTTOM_RIGHT: "bottom-right"
  },
}
