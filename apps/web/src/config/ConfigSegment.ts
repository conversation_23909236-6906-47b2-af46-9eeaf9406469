export const NAME = {
  LOGIN: 'login',
  LOGIN_SUCCESSFULLY: 'login successfully',
  LOGOUT: 'logout',
  START_PLAYING_VIDEO: 'start playing video',
  PLAY_VIDEO: 'play video',
  INITIAL_PLAY: 'Initial play',
  PAUSE_VIDEO: 'pause video',
  VIDEO_PROGRESS: 'video progress',
  LIVESTREAM_PROGRESS: 'livestream progress',
  WEB_VISITED: 'web visited',
  WEB_VISITED_SEO: 'web_visited_seo',
  CLICK_SIGNUP_LINK: 'click signup link',
  SIGN_UP: 'sign up',
  SIGN_UP_SUCCESSFULLY: 'sign up successfully',
  START_PLAYING_ADS: 'start playing ads',
  VIEW_VIP: 'view vip',
  SUBSCRIBE_VIP: 'subscribe vip',
  STOP_PLAYING_ADS: 'stop playing ads',
  INIT_PLAYER: 'init_player',
  EXIT_BEFORE_STARTED: 'exit_before_started',
  VIDEO_STARTUP_PREROLL_LOAD: 'video_startup_preroll_load',
  REBUFFER_CONTENT: 'rebuffer_content',
  VIDEO_STARTED: 'video started',
  VIDEO_COMPLETED: 'video completed',
  VIDEO_PART_STARTED: 'video part started',
  VIDEO_PART_INITIAL_PLAYING: 'video part Initial playing',
  VIDEO_PART_COMPLETED: 'video part completed',
  VIDEO_AD_STARTED: 'video ad started',
  VIDEO_AD_COMPLETED: 'video ad completed',
  SKIP_ADS: 'skipad button selected',
  BANNER_SELECTED: 'banner selected',
  SIGNUP_BUTTON_SELECTED: 'sign_up_button_selected',
  LOST_NETWORK_CONNECTION: 'lost_network_connection',

  // PAYMENT
  CHECK_OUT_STARTED: 'check_out_started',
  ORDER_COMPLETED: 'order completed',
  ORDER_FAIL: 'order failed',
  SELECT_PRODUCT: 'select product',
  SELECT_PAYMENT_METHOD: 'select payment method',
  CHANGE_PRODUCT: 'change product',
  SELECT_BANK: 'select bank',
  TICK_RECURRING: 'tick recurring',
  UPDATE_INFORMATION: 'update information',

  //FOR LIVE TV
  LIVETV_STARTED: 'livetv started',
  LIVETV_COMPLETED: 'livetv completed',
  EPG_STARTED: 'epg started',
  LIVETV_PROGRESS: 'livetv progress',
  LIVETV_ERROR: 'livetv error',

  // SEARCH
  SEARCH: 'search',
  SELECT_SEARCH_RESULT: 'select search result',
  RELATED_KEYWORD_SELECTED: 'related keyword selected',

  BUTTON: {
    TRIAL: 'trial button selected',
    REGISTRATION: 'registration button selected',
    LOGIN: 'login button selected',
    RESEND_OTP: 'resend otp selected',
    CONFIRM_BUTTON_SELECTED: 'confirmation button selected',
    FORGOT_PASS_STARTED: 'forgot password flow started',
    START_TRIAL_WATCHING: 'start watching button selected'
  },
  RESET_PASSWORD_SUCCESS: 'reset password succeed',

  PAYMENT: {
    PACKAGE_SELECTED: 'package_selected',
    PAGE_LOADED: 'payment page loaded',
    PAYMENT_EXIT: 'payment_exit',
    PACKAGE_DURATION_SELECTED: 'package_duration_selected',
    METHOD_SELECTED: 'payment method selected',
    PAY_BUTTON_SELECTED: 'pay button selected',
    CHECK_OUT_RESULT_PAGE_LOADED: 'checkout result page loaded',
    PROMOTION_SELECTED: 'promotion_selected',
    LATER_BUTTON_SELECTED: 'later_button_selected',
    SIGN_UP_BUTTON_SELECTED_IN_PAYMENT_SCREEN: 'sign_up_button_selected_in_payment_screen',
    DIALOG_ADVERTISEMENT_LOADED: 'dialog_advertisement_loaded',
    USER_HIDE_ADVETISEMENT_SELECTED: 'user_hide_advetisement_selected',
    USER_OPEN_ADVETISEMENT_SELECTED: 'user_open_advetisement_selected',
    USER_SEE_PACKAGE_LIST: 'user_see_package_list',
    USER_BACK_BUTTON_SELECTED_IN_PACKAGE_SCREEN: 'user_back_button_selected_in_package_screen',
    DIALOG_SURVEY_LOAD: 'dialog_survey_load',
    CLOSE_SURVEY: 'close_survey',
    USER_SEND_LEAVING_SURVEY_SELECTED: 'user_send_leaving_survey_selected',
    PAYMENT_BUTTON_SELECTED: 'payment_button_selected', // global
    CHECKOUT_BUTTON_SELECTED: 'checkout_button_selected', // global
    SHOW_QRCODE_PAYMENT: 'show_qr_code_payment', // global
    ASIAPAY_CHECKOUT_BUTTON_SELECTED: 'asiapay_checkout_button_selected', // global
    PROMOTION_BUTTON_SELECTED: 'promotion_button_selected',
    CODE_INPUT_PAGE_LOADED: 'code_input_page_loaded',
    RECURRING_TOGGLE_SELECTED: 'recurring_toggle_selected',
    RECURRING_BUTTON_SELECTED: 'recurring_button_selected',
    CANCEL_RECURRING_BUTTON_SELECTED: 'cancel_recurring_button_selected',
    BACK_BUTTON_SELECTED: 'back_button_selected',
    HELP_BUTTON_SELECTED: 'help_button_selected',
    CHAT_WIDGET_SELECTED: 'chat_widget_selected',
    CHAT_WIDGET_CLOSED: 'chat_widget_closed',
    REMIND_BUTTON_SELECTED: 'remind_button_selected',
    RATING_BUTTON_SELECTED: 'rating_button_selected',
    SEND_RATING_BUTTON_SELECTED :'send_rating_button_selected',
    ADD_TO_LIST_BUTTON_SELECTED :'add_to_list_button_selected'
  },

  CONTENT_SELECT: 'content selected',
  RIBBON_SELECT: 'ribbon selected',
  VOUCHER_CODE_INPUTTED: 'voucher_code_inputed',
  SEARCH_ICON_SELECTED: 'search icon selected',
  NOTIFICATION_ICON_SELECTED: 'notification icon selected',
  NOTIFICATION_ITEM_SELECTED: 'notification item selected',
  CATEGORY_SELECTED: 'category selected',
  ADD_LIVE_TV_TO_MY_LIST: 'add livetv to mylist selected',
  LIVESTREAM_STARTED: 'livestream started',
  LIVESTREAM_COMPLETED: 'livestream completed',
  PLAYER_ERROR: 'player error',
  PLAYER_ERROR_RETRY: 'player error retry',
  RESPONSE_TIME: 'cdn_response_time',
  VOUCHER_RESULT_PAGE_LOADED: 'voucher_result_page_loaded',

  MENU: {
    MENU_SELECTED: 'menu selected'
  },

  //PAYMENT CONVERSION
  PAYMENT_CONVERSION: {
    NEARLY_EXPIRE_BANNER_LOADED: 'nearly_expire_banner_loaded',
    NEARLY_EXPIRE_BANNER_SELECTED: 'nearly_expire_banner_selected',
    NEARLY_EXPIRE_FULLSCREEN_LOADED: 'nearly_expire_fullscreen_loaded',
    NEARLY_EXPIRE_FULLSCREEN_BUTTON_SELECTED: 'nearly_expire_fullscreen_button_selected',
    NEARLY_EXPIRE_FULLSCREEN_CONTENT_SELECTED: 'nearly_expire_fullscreen_content_selected',
    EXPIRE_BANNER_LOADED: 'expired_banner_loaded',
    EXPIRE_BANNER_SELECTED: 'expired_banner_selected',
    EXPIRE_FULLSCREEN_LOADED: 'expired_fullscreen_loaded',
    EXPIRE_BUTTON_SELECTED: 'expired_button_selected',
    EXPIRE_FULLSCREEN_CONTENT_SELECTED: 'expired_fullscreen_content_selected',
    SVOD_TRIAL_SUBSCRIBE_PACKAGE_BUTTON_SELECTED: 'svod_trial_subscribe_package_button_selected',
    SVOD_TRIAL_CANCEL_BUTTON_SELECTED: 'svod_trial_cancel_button_selected',
    SVOD_TRIAL_SUBSCRIBE_PACKAGE_BUTTON_LOADED: 'svod_trial_subscribe_package_button_loaded',
    REMOVE_ADS_SUBSCRIBE_PACKAGE_BUTTON_SELECTED: 'remove_ads_subscribe_package_button_selected',
    QUALITY_SUB_SUBSCRIBE_PACKAGE_BUTTON_SELECTED: 'quality_sub_subscribe_package_button_selected',
    EXCLUSIVE_HOT_CONTENT_SUBSCRIBE_PACKAGE_BUTTON_SELECTED:
      'exclusive_hot_content_subscribe_package_button_selected',
    EXCLUSIVE_HOT_CONTENT_SUBSCRIBE_PACKAGE_LOADED:
      'exclusive_hot_content_subscribe_package_loaded',
    FIRST_MONTH_TRIAL_OFFER: 'first_month_trial_offer'
  }, //Segment user
  SEGMENTED_USER: {
    OFFER_SUBSCRIPTION_DIALOG_BUTTON_SELECTED: 'offer_subscription_dialog_button_selected',
    OFFER_SUBSCRIPTION_DIALOG_BUTTON_CLOSE: 'offer_subscription_dialog_button_closed',
    OFFER_SUBSCRIPTION_DIALOG_LOADED: 'offer_subscription_dialog_loaded'
  },
  //SVOD
  SVOD: {
    VIP_FEATURE_SUBSCRIBE_PACKAGE_LOADED: 'vip_feature_subscribe_package_loaded',
    VIP_FEATURE_SUBSCRIBE_PACKAGE_CANCEL_BUTTON_SELECTED:
      'vip_feature_subscribe_package_cancel_button_selected'
  },

  //TVOD
  TVOD: {
    DIALOG_REMINDER_SELECTED_WATCH: 'dialog_reminder_selected_watch',
    DIALOG_REMINDER_SELECTED_CLOSE: 'dialog_reminder_selected_close',
    DIALOG_REMINDER_LOADED: 'dialog_reminder_loaded',
    DIALOG_EVENT_HAPPENING_LOADED: 'dialog_event_happening_loaded',
    DIALOG_EVENT_HAPPENING_WATCH: 'dialog_event_happening_watch',
    DIALOG_EVENT_HAPPENING_CLOSE: 'dialog_event_happening_close',
    DIALOG_DAY3_DAY1_LOADED: 'dialog_day3_day1_loaded',
    DIALOG_DAY3_DAY1_MULTI_LOAD: 'dialog_day3_day1_multi_load',
    DIALOG_DAY3_DAY1_MULTI_SELECT: 'dialog_day3_day1_multi_select',
    DIALOG_DAY3_DAY1_CLOSE: 'dialog_day3_day1_close',
    DIALOG_DAY3_DAY1_MULTI_CLOSE: 'dialog_day3_day1_multi_close',
    DIALOG_DAY3_DAY1_MULTI_INCLUDE_TVOD_CLOSE: 'dialog_day3_day1_multi_include_tvod_close',
    DIALOG_DAY3_DAY1_MULTI_INCLUDE_TVOD_SELECT: 'dialog_day3_day1_multi_include_tvod_select',
    DIALOG_DAY3_DAY1_MULTI_INCLUDE_TVOD_LOAD: 'dialog_day3_day1_multi_include_tvod_load',
    TVOD_WELCOME_TURORIAL_LOADED: 'tvod_welcome_turorial_loaded',
    TVOD_WELCOME_TURORIAL_LOADED_MULTI: 'tvod_welcome_turorial_loaded_multi',
    TVOD_WELCOME_TURORIAL_SELECTED_CONTENT: 'tvod_welcome_turorial_selected_content',
    TVOD_WELCOME_TURORIAL_LOADED_MULTI_SELECTED: 'tvod_welcome_turorial_loaded_multi_select',
    TVOD_WELCOME_TURORIAL_LOADED_MULTI_CLOSE: 'tvod_welcome_turorial_loaded_multi_close',
    TVOD_WELCOME_TURORIAL_SELECTED_CLOSE: 'tvod_welcome_turorial_selected_close',
    LIVE_EVENT_WELCOME_TURORIAL_LOADED: 'live_event_welcome_turorial_loaded',
    LIVE_EVENT_WELCOME_TURORIAL_SELECTED_CONTENT: 'live_event_welcome_turorial_selected_content',
    LIVE_EVENT_WELCOME_TURORIAL_SELECTED_PRE_ORDER:
      'live_event_welcome_turorial_selected_pre_order',
    LIVE_EVENT_WELCOME_TURORIAL_SELECTED_CLOSE: 'live_event_welcome_turorial_selected_close',
    VIEW_LIVE_EVENT_DETAIL_BUTTON_SELECTED: 'view_live_event_detail_button_selected',
    DIALOG_LIVE_STREAM_END_LOADED: 'dialog_livestream_end_loaded',
    DIALOG_LIVE_STREAM_END_HOME_PAGE: 'dialog_livestream_end_homepage',
    DIALOG_LIVE_STREAM_END_WATCH: 'dialog_livestream_end_watch',
    DIALOG_LIVE_STREAM_END_CLOSE: 'dialog_livestream_end_close',
    DIALOG_RENT_LOADED: 'dialog_rent_loaded',
    DIALOG_RENT_LOADED_WATCH: 'dialog_rent_loaded_watch',
    DIALOG_RENT_LOADED_HOME_PAGE: 'dialog_rent_loaded_homepage',
    DIALOG_MISSED_EVENT_LOADED: 'dialog_missed_event_loaded',
    DIALOG_MISSED_EVENT_HOME_PAGE: 'dialog_missed_event_homepage',
    PAYMENT_SUCCESS_SELECT_WATCH: 'payment_success_select_watch',
    PAYMENT_SUCCESS_SELECT_HOMEPAGE: 'payment_success_select_homepage',
    VIEW_VIDEO_DETAIL_BUTTON_SELECTED: 'view_video_detail_button_selected',
    VIEW_VIDEO_DETAIL: 'view_video_detail',
    CONTENT_SELECT: 'content_selected'
  },

  TOAST: {
    TOAST_EVENT_HAPPENING_BROWSING_LOADED: 'toast_event_happening_browsing_loaded',
    TOAST_EVENT_HAPPENING_BROWSING_SELECT: 'toast_event_happening_browsing_select',
    PLAYER_EVENT_HAPPENING_BROWSING_LOADED: 'player_event_happening_browsing_loaded',
    PLAYER_EVENT_HAPPENING_BROWSING_SELECT: 'player_event_happening_browsing_select'
  },

  POPUP: {
    DIALOG_TIME_OUT_SALE_LOADED: 'dialog_time_out_sale_loaded',
    DIALOG_TIME_OUT_SALE_HOMEPAGE: 'dialog_time_out_sale_homepage',
    DIALOG_TIME_OUT_SALE_SELECT: 'dialog_time_out_sale_select'
  },

  //Account deletion
  DIALOG_RESTORE_ACCOUNT_LOADED: 'dialog_restore_account_loaded',
  DIALOG_RESTORE_ACCOUNT_CLOSE: 'dialog_restore_account_close',
  DIALOG_RESTORE_ACCOUNT_ACCEPT: 'dialog_restore_account_accept',
  DIALOG_RESTORE_ACCOUNT_ANOTHER: 'dialog_restore_account_another',
  DIALOG_RESTORE_ACCOUNT_SUCCESS: 'dialog_restore_account_success',
  LOCKED_ACCOUNT_DIALOG_LOADED: 'locked_account_dialog_loaded',
  LOCKED_ACCOUNT_ACCEPT: 'locked_account_accept',

  //Bind Account
  LOGIN_BIND_ACCOUNT_PHONE: 'login_bind_account_phone',
  LOGIN_BIND_ACCOUNT_OTP: 'login_bind_account_otp',
  LOGIN_BIND_ACCOUNT_PASS: 'login_bind_account_pass',
  CONFIRMATION_BUTTON_SELECTED: 'confirmation_button_selected',
  RESEND_OTP_BUTTON_SELECTED: 'resend_otp_button_selected',
  SKIP_BUTTON_SELECTED: 'skip_button_selected',
  LOGIN_BIND_ACCOUNT_SUCCESSFUL: 'login_bind_account_successful',
  BIND_ACCOUNT_BANNER: 'bind_account_banner',
  SIGN_UP_CANCEL: 'sign_up_cancel',
  DIALOG_REGISTRATION_TRIAL_LOADED: 'dialog_registration_trial_loaded',
  DIALOG_REGISTRATION_TRIGGER_LOADED: 'dialog_registration_trigger_loaded',

  //REGISTRATION TRIGGER
  REGISTRATION_TRIGGER: {
    BANNER_RECOMMENDATION_SELECTED: 'banner_register_for_recommendation_selected',
    BANNER_COMMENT_SELECTED: 'banner_register_for_comment_selected',
    TEXT_BUTTON_COMMENT_SELECTED: 'text_button_register_for_comment_selected',
    LOGIN_POPUP_LOADED: 'login_popup_loaded',
    LOGIN_POPUP_CLOSE_BUTTON_SELECTED: 'login_popup_close_button_selected'
  },
  LOAD_DEEP_LINK: 'load_deep_link',

  // MWEB TO APP
  SMART_BANNER_DOWNLOAD_LOAD: 'smart_banner_download_load',
  SMART_BANNER_DOWNLOAD_ACCEPT: 'smart_banner_download_accept',
  SMART_BANNER_DOWNLOAD_CLOSE: 'smart_banner_download_close',
  FAB_LOAD: 'fab_load',
  FAB_TOUCH: 'fab_touch',
  FOOTER_APP_STORE_DOWNLOAD_TOUCH: 'footer_app_store_download_touch',
  FOOTER_GOOGLE_PLAY_DOWNLOAD_TOUCH: 'footer_google_play_download_touch',
  ONLY_APP_TRIGGER_SELECTED: 'only_app_trigger_selected',

  MOVIE_TRIAL_15P_BANNER_LOAD: 'movie_trial_15p_banner_load',
  MOVIE_TRIAL_15P_BANNER_TOUCH: 'movie_trial_15p_banner_touch',
  MOVIE_TRIAL_15P_DIALOG_PORTRAIT_LOAD: 'movie_trial_15p_dialog_portrait_load',
  MOVIE_TRIAL_15P_DIALOG_PORTRAIT_TOUCH: 'movie_trial_15p_dialog_portrait_touch',
  MOVIE_TRIAL_15P_DIALOG_PORTRAIT_CLOSE: 'movie_trial_15p_dialog_portrait_close',
  MOVIE_TRIAL_15P_INFOBOX_LANDSCAPE_LOAD: 'movie_trial_15p_infobox_landscape_load',
  MOVIE_TRIAL_15P_INFOBOX_LANDSCAPE_TOUCH: 'movie_trial_15p_infobox_landscape_touch',
  MOVIE_TRIAL_15P_DIALOG_LANDSCAPE_LOAD: 'movie_trial_15p_dialog_landscape_load',
  MOVIE_TRIAL_15P_DIALOG_LANDSCAPE_TOUCH: 'movie_trial_15p_dialog_landscape_touch',
  MOVIE_TRIAL_15P_DIALOG_LANDSCAPE_CLOSE: 'movie_trial_15p_dialog_landscape_close',

  DIALOG_CONTENT_ONLY_IN_APP_LOAD: 'dialog_content_only_in_app_load',
  DIALOG_CONTENT_ONLY_IN_APP_TOUCH: 'dialog_content_only_in_app_touch',
  DIALOG_CONTENT_ONLY_IN_APP_CLOSE: 'dialog_content_only_in_app_close',
  CONTENT_ONLY_IN_APP_TOUCH: 'content_only_in_app_touch',
  CONTENT_ONLY_IN_APP_ADD_LIST: 'content_only_in_app_add_list',

  TVSERIES_TRIAL_EPISODES_BANNER_PORTRAIT_LOAD: 'tvseries_trial_episodes_banner_portrait_load',
  TVSERIES_TRIAL_EPISODES_BANNER_PORTRAIT_TOUCH: 'tvseries_trial_episodes_banner_portrait_touch',
  TVSERIES_TRIAL_EPISODES_DIALOG_PORTRAIT_LOAD: 'tvseries_trial_episodes_dialog_portrait_load',
  TVSERIES_TRIAL_EPISODES_DIALOG_PORTRAIT_CLOSE: 'tvseries_trial_episodes_dialog_portrait_close',
  TVSERIES_TRIAL_EPISODES_DIALOG_PORTRAIT_TOUCH: 'tvseries_trial_episodes_dialog_portrait_touch',
  TVSERIES_TRIAL_EPISODES_INFOBOX_LANDSCAPE_LOAD: 'tvseries_trial_episodes_infobox_landscape_load',
  TVSERIES_TRIAL_EPISODES_INFOBOX_LANDSCAPE_TOUCH:
    'tvseries_trial_episodes_infobox_landscape_touch',
  TVSERIES_TRIAL_EPISODES_DIALOG_LANDSCAPE_LOAD: 'tvseries_trial_episodes_dialog_landscape_load',
  TVSERIES_TRIAL_EPISODES_DIALOG_LANDSCAPE_CLOSE: 'tvseries_trial_episodes_dialog_landscape_close',
  TVSERIES_TRIAL_EPISODES_DIALOG_LANDSCAPE_TOUCH: 'tvseries_trial_episodes_dialog_landscape_touch',
  TVSERIES_TRIAL_EPISODES_INFOBOX_LIST_LANDSCAPE_LOAD:
    'tvseries_trial_episodes_infobox_list_landscape_load',
  TVSERIES_TRIAL_EPISODES_INFOBOX_LIST_LANDSCAPE_TOUCH:
    'tvseries_trial_episodes_infobox_list_landscape_touch',

  IN_APP_NEWS_LOAD: 'in_app_news_load',
  IN_APP_DOWNLOAD: 'in_app_download',

  // Multi Profile
  MULTI_PROFILE: {
    // Lobby View
    LOBBY_VIEW: {
      MULTI_PROFILE_CONFIRM_LOAD: 'multi_profile_confirm_load',
      MULTI_PROFILE_CONFIRM_OVER_18: 'multi_profile_confirm_over_18',
      MULTI_PROFILE_CONFIRM_UNDER_18: 'multi_profile_confirm_under_18',
      MULTI_PROFILE_LOBBY_LOAD: 'multi_profile_lobby_load',
      MULTI_PROFILE_LOBBY_ACCEPT: 'multi_profile_lobby_accept',
      REGISTRATION_BUTTON_SELECTED: 'registration_button_selected',
      MULTI_PROFILE_LOBBY_CLOSE: 'multi_profile_lobby_close',
      MULTI_PROFILE_LOBBY_WHO_WATCHING_LOAD: 'multi_profile_lobby_who_watching_load',
      MULTI_PROFILE_CHOOSE: 'multi_profile_choose',
      MULTI_PROFILE_CONFIG: 'multi_profile_config',
      MULTI_PROFILE_CONFIG_CHOOSE: 'multi_profile_config_choose',
      MULTI_PROFILE_CONFIG_COMPLETE: 'multi_profile_config_complete',
      MULTI_PROFILE_ADD: 'multi_profile_add'
    }, // End Lobby View

    //lobby add
    LOBBY_ADD_PROFILE: {
      MULTI_PROFILE_ADD_PROFILE_LOAD: 'multi_profile_add_profile_load',
      MULTI_PROFILE_ADD_PROFILE_TYPE: 'multi_profile_add_profile_type',
      MULTI_PROFILE_ADD_PROFILE_CHOOSE_AGE: 'multi_profile_add_profile_choose_age',
      MULTI_PROFILE_ADD_PROFILE_CHOOSE_NAME: 'multi_profile_add_profile_choose_name',
      MULTI_PROFILE_ADD_PROFILE_CHOOSE_GENDER: 'multi_profile_add_profile_choose_gender',
      MULTI_PROFILE_ADD_PROFILE_CHOOSE_EDIT_AVATAR: 'multi_profile_add_profile_choose_edit_avatar',
      MULTI_PROFILE_ADD_PROFILE_CHOOSE_AVATAR: 'multi_profile_add_profile_choose_avatar',
      MULTI_PROFILE_ADD_PROFILE_CHOOSE_BACK: 'multi_profile_add_profile_choose_back',
      MULTI_PROFILE_ADD_PROFILE_CHOOSE_CREATE_PIN: 'multi_profile_add_profile_choose_create_pin',
      MULTI_PROFILE_ADD_PROFILE_CHOOSE_CONFIRM_PIN: 'multi_profile_add_profile_choose_cofirm_pin',
      MULTI_PROFILE_ADD_PROFILE_CHOOSE_CLOSE_PIN: 'multi_profile_add_profile_choose_close_pin',
      MULTI_PROFILE_ADD_PROFILE_CHOOSE_DELETE_PIN: 'multi_profile_add_profile_choose_delete_pin',
      MULTI_PROFILE_ADD_PROFILE_CLOSE: 'multi_profile_add_profile_close',
      MULTI_PROFILE_ADD_PROFILE_ACCEPT: 'multi_profile_add_profile_accept'
    },

    // CHOOSE PROFILE
    CHOOSE_PROFILE: {
      DIALOG_LOGOUT_KID_MODE_LOAD: 'dialog_logout_kid_mode_load',
      DIALOG_LOGOUT_KID_MODE_ACCEPT: 'dialog_logout_kid_mode_accept',
      DIALOG_LOGOUT_KID_MODE_CANCEL: 'dialog_logout_kid_mode_cancel',
      DIALOG_NOT_SPEND_FOR_KID_LOAD: 'dialog_not_spend_for_kid_load',
      DIALOG_NOT_SPEND_FOR_KID_RETURN_HOMEPAGE: 'dialog_not_spend_for_kid_return_homepage',
      DIALOG_CANNOT_ACCESS_CONTENT_LOAD: 'dialog_cannot_access_content_load',
      DIALOG_CANNOT_ACCESS_CONTENT_CLOSE: 'dialog_cannot_access_content_close',
      AVATAR_NOT_LOAD_LOAD: 'avatar_not_load_load',
      AVATAR_NOT_LOAD_TRY_AGAIN: 'avatar_not_load_try_again'
    }
    // END CHOOSE PROFILE
  },

  // UPDATE PROFILE
  UPDATE_GENDER: 'update_gender',
  UPDATE_EMAIL: 'update_email',
  UPDATE_PHONE_NUMBER: 'update_phone_number',
  SHARE_CONTENT_TO_FACEBOOK: 'share_content_to_facebook',

  // Multi Profile
  MULTI_PROFILE_CONFIG_LOAD: 'multi_profile_config_load',
  MULTI_PROFILE_CONFIG_CHOOSE_NAME: 'multi_profile_config_choose_name',
  MULTI_PROFILE_CONFIG_CHOOSE_GENDER: 'multi_profile_config_choose_gender',
  MULTI_PROFILE_CONFIG_CHOOSE_EDIT_AVATAR: 'multi_profile_config_choose_edit_avatar',
  MULTI_PROFILE_CONFIG_CHOOSE_AVATAR: 'multi_profile_config_choose_avatar',
  MULTI_PROFILE_CONFIG_CHOOSE_BACK: 'multi_profile_config_choose_back',
  MULTI_PROFILE_CONFIG_CHOOSE_CHANGE_PIN: 'multi_profile_config_choose_change_pin',
  MULTI_PROFILE_CONFIG_CHOOSE_CONFIRM_PIN: 'multi_profile_config_choose_confirm_pin',
  MULTI_PROFILE_CONFIG_CHOOSE_CLOSE_PIN: 'multi_profile_config_choose_close_pin',
  MULTI_PROFILE_CONFIG_CLOSE: 'multi_profile_config_close',
  MULTI_PROFILE_CONFIG_ACCEPT: 'multi_profile_config_accept',
  MULTI_PROFILE_CONFIG_DELETION: 'multi_profile_config_deletion',
  MULTI_PROFILE_CONFIG_DELELE_LOAD: 'multi_profile_config_delele_load',
  MULTI_PROFILE_CONFIG_DELETE_CANCEL: 'multi_profile_config_delete_cancel',
  MULTI_PROFILE_CONFIG_DELETE: 'multi_profile_config_delete',

  // Revise Payment
  DIALOG_REVISE_PAYMENT_LOADED: 'dialog_revise_payment_loaded',
  REVISE_PAYMENT_SELECTED: 'revise_payment_selected',
  REVISE_PAYMENT_CLOSED: 'revise_payment_closed',

  ALWAYS_ON_TRIGGER_SELECTED: 'always_on_trigger_selected',
  ALWAYS_ON_TRIGGER_CLOSED: 'always_on_trigger_closed',
  ALWAYS_ON_TRIGGER_LOADED: 'always_on_trigger_loaded',

  //vie indexing
  SHOW_INDICATOR: 'show_indicator',
  SHOW_INDICATOR_SELECTED: 'show_indicator_selected',
  HIDE_INDICATOR_SELECTED: 'hide_indicator_selected',
  INDICATOR_SELECTED: 'indicator_selected',
  SHOW_ALL_PRODUCT_IN_SCENE: 'show_all_product_in_scene',
  GO_PRODUCT_PAGE_BUTTON_SELECTED: 'go_product_page_button_selected',
  TEL_CONSULT_BUTTON_SELECTED: 'tel_consult_button_selected',
  TEL_CONSULT_CONFIRM_BUTTON_SELECTED: 'tel_consult_confirm_button_selected',
  TEL_CONSULT_CANCEL_BUTTON_SELECTED: 'tel_consult_cancel_button_selected',
  SHOW_ALL_PRODUCT_IN_CONTENT: 'show_all_product_in_content',
  PRODUCT_SELECTED: 'product_selected',
  PRODUCT_PAGE_CLOSED: 'product_page_closed',
  EXPLORE_BRAND_BUTTON_SELECTED: 'explore_brand_button_selected',
  VIEW_BRAND_DETAIL_SELECTED: 'view_brand_detail_selected',
  SHOW_BRAND: 'show_brand',

  ACCEPT_SIGN_IN_QRCODE: 'accept_sign_in_qrcode',
  IGNORE_SIGN_IN_QRCODE: 'ignore_sign_in_qrcode', //OEM
  IMPRESSION_BANNER: 'impression_banner',
  CONFIRM_BUTTON_SELECTED: 'confirm_button_selected',

  //engagement trigger
  ENGAGEMENT_TRIGGER_DIALOG_LOADED: 'engagement_trigger_dialog_loaded',
  ENGAGEMENT_TRIGGER_SUBSCRIPTION_BUTTON_SELECTED:
    'engagement_trigger_subscription_button_selected',
  ENGAGEMENT_TRIGGER_SIGN_IN_BUTTON_SELECTED: 'engagement_trigger_sign_in_button_selected',

  //   Fast Track
  FAST_TRACK_EPISODE_SELECTED: 'fast_track_episode_selected',
  FAST_TRACK_REGISTER_AT_VIDEO_DETAIL: 'fast_track_register_at_video_detail',
  FAST_TRACK_REGISTER_AT_DIALOG: 'fast_track_register_at_dialog',
  FAST_TRACK_REGISTER_AT_END_SCREEN: 'fast_track_register_at_end_screen',

  // END SCREEN
  END_SCREEN_SUGGESTION_LIVE_EVENT_SHOW: 'end_screen_suggestion_live_event_show',
  END_SCREEN_SUGGESTION_VOD_SHOW: 'end_screen_suggestion_vod_show',
  END_SCREEN_SUGGESTION_LIVE_EVENT_SELECTED: 'end_screen_suggestion_live_event_selected',
  END_SCREEN_SUGGESTION_VOD_SELECTED: 'end_screen_suggestion_vod_selected',

  // COMMING SOON
  VIEW_CONTENT_DETAIL_AT_ON_AIR_TAB: 'view_content_detail_at_on_air_tab',
  VIEW_CONTENT_AT_ON_AIR_TAB: 'view_content_at_on_air_tab',
  ADD_CONTENT_AT_ON_AIR_TAB: 'add_content_at_on_air_tab',
  REMIND_ME_AT_COMING_SOON_TAB: 'remind_me_at_coming_soon_tab',
  ADD_CONTENT_AT_COMING_SOON_TAB: 'add_content_at_coming_soon_tab',
  VIEW_CONTENT_DETAIL_AT_COMING_SOON_TAB: 'view_content_detail_at_coming_soon_tab',

  // FIRST PAY
  DIALOG_FIRST_PAYMENT_LOADED: 'dialog_first_payment_loaded',
  DIALOG_FIRST_PAYMENT_ACCEPTED: 'first_payment_selected',
  DIALOG_FIRST_PAYMENT_CLOSED: 'first_payment_closed',

  // REQUEST ADS TRACKING
  CALL_ADS_REQUEST: 'call_ads_request'
};

export const PROPERTY = {
  FLOW_NAME: 'flow_name',
  CANCEL: 'cancel',
  LOGIN_METHOD: 'log_in_method',
  IS_AUTO: 'is_auto',
  CONTENT_TITLE: 'content_title',
  SHORT_DESCRIPTION: 'short_description',
  THUMBNAIL: 'thumbnail',
  VIDEO_GENRE: 'video_genre',
  CONTENT_SELECT_BUTTON: 'content_selected_button',
  CONTENT_TYPE: 'content_type',
  CONTENT_PLAY_TYPE: 'content_play_type',
  COUNT: 'count',
  VIDEO_SEASON_NAME: 'video_season_name',
  SEASON_NAME: 'season_name',
  CONTENT_ID: 'content_id',
  IS_AUTO_PLAY: 'is_auto_play',
  TIME_FOR_CACHING: 'time_for_caching', // second
  PROGRESS: 'progress',
  AD_LINK: 'ad_link',
  AD_DURATION: 'ad_duration',
  IS_SKIPPED: 'is_skipped',
  IS_SEEKING: 'is_seeking',
  REBUFFER_TIME: 'rebuffer_time',
  IS_FINISHED: 'is_finished',
  TRIGGER: 'trigger',
  PLAYED_DURATION: 'played_duration',
  VIDEO_PLAY_TYPE: 'video_play_type',
  SESSION_ID: 'session_id',
  HAS_ADS: 'has_ads',
  SUBTITLE: 'subtitle',
  AUDIO: 'audio',
  VIDEO_LENGTH: 'video_length',
  TOTAL_PLAYED_DURATION: 'total_played_duration',
  TOTAL_TIME: 'total_time',
  PART_LENGTH: 'part_length',
  PART_INDEX: 'part_index',
  LAG_FORM_VIDEO_STARTED: 'lag_from_video_started',
  LAG_FORM_PART_STARTED: 'lag_from_part_started',
  PLAYED_LENGTH: 'played_length',
  BITRATE: 'bitrate',
  QUALITY: 'quality',
  FEATURE_NAME: 'feature_name',
  FLOW_AUTHEN: 'flow_authen',
  RATING_SCORE: 'rating_score',
  BUTTON_NAME: 'button_name',

  //vie indexing
  IS_INDEXING: 'is_indexing',
  PRODUCT_NAME: 'product_name',
  BRAND_NAME: 'brand_name',
  MOBILE: 'mobile',
  EMAIL: 'email',

  //TVOD
  CHECK_OUT_RESULT: 'check-out_result',

  // FOR LIVE TV
  LIVETV_TITLE: 'livetv_title',
  EPG_TITLE: 'epg_title',
  LIVETV_PLAY_TYPE: 'livetv_play_type',
  IS_LIVE: 'is_live',
  EPG_ID: 'epg_id',
  LIVETV_ID: 'livetv_id',
  ERROR: 'error',

  //LIVESTREAM
  LIVESTREAM_ID: 'livestream_id',
  LIVESTREAM_TITLE: 'livestream_title',
  LIVESTREAM_PLAY_TYPE: 'livestream_play_type',

  //RESPONSE TIME
  ID_ADDRESS: 'ip',
  CDN_HOSTNAME: 'sn',
  LATENCY: 'latency',

  //SEARCH
  KEYWORD: 'keyword',
  KEYWORD_POSITION: 'keyword_position',
  KEYWORD_NAME: 'keyword_name',
  KEYWORD_INPUTTED: 'keyword_inputted',

  // PAYMENT
  STEP: 'step',
  STEP_NAME: 'step_name',
  PRODUCT: 'product',
  PRICE: 'price',
  PAID_PRICE: 'paid_price',
  PROMOTION: 'promotion',
  PAYMENT_METHOD: 'payment_method',
  BANK: 'bank',
  RECURRING: 'recurring',
  CAUSE_FOR_FAILURE: 'cause_for_failure',
  FROM_PRODUCT: 'from_product',
  TO_PRODUCT: 'to_product',
  CONTACT_METHOD: 'contact_method',
  PACKAGE_ID_SURVEY: 'properties_package_id',
  PACKAGE_PRICE: 'paid_price',

  // NEW UI
  CODE: 'code',
  CURRENT_PAGE: 'current_page',
  SEARCH_ENGINE: 'search_engine',
  BANNER_ID: 'banner_id',
  BANNER_NAME: 'banner_name',
  BANNER_ORDER: 'banner_order',
  BANNER_ORDER_IN_CAROUSEL: 'banner_order_in_carousel',
  POPUP_NAME: 'popup_name',
  REFERRAL: 'referal',
  PACKAGE_NAME: 'package_name',
  PACKAGE_ID: 'package_id',
  PACKAGE_DURATION: 'package_duration',
  TRANSACTION_ID: 'transaction_id',
  CHECKOUT_RESULT: 'checkout_result',
  CONTENT_NAME: 'content_name',
  CONTENT_ORDER: 'content_order',
  RIBBON_NAME: 'ribbon_name',
  PLAY_NOW: 'play_now',
  CATEGORY_NAME: 'category_name',
  SUB_MENU_ID: 'submenu_id',
  SUB_MENU_NAME: 'submenu_name',
  SUB_MENU_ORDER: 'submenu_order',
  CLICK_TYPE: 'click_type',
  GENRE: 'genre',
  RIBBON_ID: 'ribbon_id',
  RIBBON_ORDER: 'ribbon_order',
  VOUCHER_CODE: 'voucher_code',
  RESULT: 'result',
  ITEM_POSITION: 'item_position',
  ITEM_NAME: 'item_name',
  RESULT_NO: 'results_no',
  CONTENT_POSITION: 'content_position',
  LIVETV_NAME: 'livetv_name',
  LIVESTREAM_STATUS: 'livestream_status',
  IS_PREMIERE: 'is_premiere',
  GERNE_NAME_SUBMITTED: 'genre_name_submitted',
  HAS_GAME: 'has_game',
  LIVE_PLAY_TYPE: 'live_play_type',
  MENU_ID: 'menu_id',
  MENU_NAME: 'menu_name',
  MENU_ORDER: 'menu_order',
  MENU_SLUG: 'menu_slug',

  USER_TYPE: 'user_type',
  USER_ID: 'user_id',
  FULLSCREEN_PAGE_BUTTON_STATUS: 'fullscreen_page_button_status',
  BANNER_BUTTON_STATUS: 'banner_button_status',
  BUTTON_STATUS: 'button_status',
  TRIGGER_FROM: 'trigger_from',
  SVOD_TRIAL_CANCEL: 'svod_trial_cancel',
  BANNER_POSITION: 'banner_position',

  //MWeb To App
  FAB_ID: 'fab_item_id',
  TRIGGER_BY_BENEFIT_NEWS: 'trigger_by_benefit_news',

  // Multi Profile
  PROFILE_AGE: 'profile_age',
  PROFILE_ID: 'profile_id',
  PROFILE_NAME: 'profile_name',
  PROFILE_TYPE: 'profile_type',
  PROFILE_GENDER: 'profile_gender',
  PROFILE_ORDER: 'profile_order',
  PROFILE_TYPE_UX: 'profile_type_ux',
  PROFILE_NAME_UX: 'profile_name_ux',
  AVATAR_ID: 'avatar_id',
  AVATAR_NAME: 'avatar_name',
  AVATAR_ORDER: 'avatar_order',

  //Player error
  PLAYER_ERROR_CODE: 'player_error_code',
  PLAYER_PROFILE: 'player_profile',
  PLAYER_SUBTITLE: 'player_subtitle',
  PLAYER_AUDIO: 'player_audio',
  PLAYER_CODEC: 'player_codec',
  PLAYER_STREAMING_PROTOCOL: 'player_streaming_protocol',
  WEB_VIEW: 'web_view',

  //Survey payment
  PROPERTIES_LEAVING_REASONS: 'properties_leaving_reasons',
  PROPERTIES_OTHERS_LEAVING_REASONS: 'properties_others_leaving_reasons',
  SCREEN_NAME: 'screen_name',

  // First pay
  PACKAGE_GROUP_NAME: 'package_group_name',
  CAMPAIGN_NAME: 'campaign_name',
  CAMPAIGN_ID: 'campaign_id',

  // Request Ads Tracking
  ADS_TYPE: 'ads_type',
  INSTREAM_ADS: 'instream_ads',
  OUTSTREAM_ADS: 'outstream_ads',
  OVERLAY_ADS: 'overlay_ads',
  INVENTORY_ID: 'inventory_id',
  TYPE: 'type',
  SLOT_NUMBER: 'slot_number',
  STATUS: 'status',
  ERROR_MSG: 'error_msg',
  VOUCHER_TYPE: 'voucher_type',
  TEXT_ERROR: 'text_error',
  METHOD: 'method',
  IS_BLOCK_VIP: 'is_block_vip',
  MODEL: 'model',
  BUTTON_TYPE: 'button_type'
};

export const VALUE:any = {
  FACEBOOK: 'facebook',
  GOOGLE: 'google',
  PHONE: 'phone',
  SOCIAL: 'social',
  QR_CODE: 'qr_code',
  PROGRESS_0: '0%',
  PROGRESS_6s: '6s',
  PROGRESS_30s: '30s',
  PROGRESS_60s: '60s',
  PROGRESS_300s: '300s',
  PROGRESS_600s: '600s',
  PROGRESS_1200s: '1200s',
  PROGRESS_1800s: '1800s',
  PROGRESS_3600s: '3600s',
  PROGRESS_7200s: '7200s',
  PROGRESS_10: '10%',
  PROGRESS_25: '25%',
  PROGRESS_50: '50%',
  PROGRESS_90: '90%',
  PROGRESS_100: '100%',
  RESOLUTION: 'resolution',
  AUDIO: 'audio',
  SUBTITLE: 'subtitle',
  VIP_CONTENT: 'vip_content',
  ADS_FREE: 'ads_free',
  DOWNLOAD: 'download',
  ADVANCED_AUDIO: 'advanced_audio',
  ADVANCED_SUBTITLE: 'advanced_subtitle',
  BANNER: 'banner',
  AVOD: 'AVOD',
  SVOD: 'SVOD',
  TVOD: 'TVOD',
  SVOD_TRIAL: 'svod_trial',
  DIRECT: 'direct',
  HOVER_CLICK: 'hover click',
  CLICK_DETAIL: 'click detail',
  CLICK_WATCH_NOW: 'click Xem ngay',
  CLICK_RENTED: 'click Thuê ngay',
  CLICK_ICON_TRIGGER: 'user bấm icon x ở Web',
  CLICK_ADD_MY_LIST: 'add my list',
  FAIL: 'fail',
  SUCCESS: 'success',
  NEARLY_EXPIRE_BANNER: 'nearly_expire_banner',
  NEARLY_EXPIRE_FULLSCREEN: 'nearly_expire_fullscreen',
  EXPIRE_BANNER: 'expire_banner',
  EXPIRE_FULLSCREEN: 'expire_fullscreen',
  PACKAGE_RENEWAL: 'Gia hạn gói',

  /// POPUP
  POPUP_NAME: {
    TRIAL: 'Dùng thử',
    FORGET_PASSWORD: 'Quên mật khẩu',
    VOD_PLAYER: 'VOD Player',
    LIVETV_PLAYER: 'Live TV Player',
    LIVESTREAM: 'Trực tiếp',
    REGISTER: 'Đăng ký',
    REGISTER_OTP: 'Đăng ký OTP',
    CHANGE_PASSWORD_OTP: 'Đổi mật khẩu OTP',
    SELECT_REPORT: 'Bấm chọn Báo lỗi',
    REGISTER_FROM_LOGIN: 'Gợi ý đăng ký từ đăng nhập',
    GUEST_VOD_LIVESTREAM_VIP: 'Bấm chọn VOD/Livestream VIP',
    GUEST_EPISODE_VIP: 'Bấm chọn / chuyển qua tập phim VIP',
    LIVETV_VIP: 'Bấm chọn Kênh/chương trình truyền hình VIP',
    LIVETV_K_PLUS: 'Bấm chọn Kênh/chương trình truyền hình K+',
    GUEST_VOD_MY_LIST: 'Thêm VOD vào Danh sách của tôi',
    GUEST_LIVETV_MY_LIST: 'Thêm LIVETV vào Danh sách của tôi',
    GUEST_RATING: 'Bấm Rating',
    GUEST_COMMENT: 'Bấm field Thêm bình luận',
    GUEST_BUY_VIP: 'Bấm button Mua gói ở header',
    GUEST_REMAIN: 'Bấm button Nhắc tôi',
    GUEST_VOD_COMING_SOON: 'Bấm vào button Nhắc tôi của VOD sắp phát sóng',
    GUEST_LIVESTREAM_COMING_SOON: 'Bấm vào button Nhắc tôi của livestream sắp phát sóng',
    GUEST_LIVETV_COMING_SOON: 'Bấm icon Notification ở chương trình truyền hình',
    QUALITY_VIP: 'Bấm chọn chất lượng VIP',
    AUDIO_SUB_VIP: 'Bấm chọn sub-audio VIP',
    REQUEST_LOGIN_CONTENT: 'Yêu cầu đăng nhập từ content',
    REQUEST_LOGIN_BUY: 'Yêu cầu đăng nhập mua gói',
    REQUEST_LOGIN_VOUCHER: 'Yêu cầu đăng nhập Voucher',
    BUTTON_LOGIN_PROFILE: 'Button Login Menu Profile',
    INPUT_PHONE: 'input_phone',
    INPUT_OTP: 'input_OTP',
    INPUT_PASS: 'input_pass'
  }, // Bind Account
  FLOW_NAME_BIND_ACCOUNT_LOGIN: 'Bind account - LogIn',
  FLOW_NAME_BIND_ACCOUNT_BANNER: 'Bind account - Banner',
  FLOW_NAME_REGISTER_RECOMMENDATION:
    'Từ luồng bấm vào item kêu gọi Đăng ký ở sau item thứ 8 hoặc item cuối cùng nếu số lượng item < 8 của ribbon Có thể bạn sẽ thích',
  FLOW_NAME_BANNER_COMMENT: 'Từ luồng bấm banner kêu gọi đăng ký ở bình luận',
  FLOW_NAME_TEXT_BUTTON_COMMENT: 'Từ luồng bấm text-button kêu gọi đăng ký ở bình luận',
  CANCEL_BUTTON_X_REGISTER: 'user bấm icon x ở dialog Đăng ký tài khoản ở Web',
  BIND_ACCOUNT_BANNER: 'bind_account_banner',
  LATER: 'Để sau',
  UPDATE_NOW: 'Cập nhật ngay',
  SVOD_TRIAL_BANNER: 'svod_trial_banner',
  SVOD_TRIAL_INFO_BOX: 'svod_trial_inforbox',
  QUALITY_INFO_BOX: 'quality_inforbox',
  SUB_AUDIO_INFO_BOX: 'sub_audio_inforbox',
  REMOVE_ADS_INFO_BOX: 'remove_ads_inforbox',
  QUALITY_DIALOG: 'quality_dialog',
  SUB_AUDIO_DIALOG: 'sub_audio_dialog',
  SVOD_TRIAL_DIALOG: 'svod_trial_dialog',
  EXCLUSIVE_HOT_CONTENT_BANNER: 'exculsive_hot_content_banner',
  EXCLUSIVE_HOT_CONTENT_INFO: 'exculsive_hot_content_infor',
  SVOD_TRIAL_CANCEL_DIALOG: 'Khi bấm vào icon X trên dialog Đã hết thời gian xem thử',
  REFERRAL_CODE: {
    1: 'svod_trial_subcribe_package',
    2: 'remove_ads_subscribe_package',
    3: 'quality_sub_subscribe_package',
    4: 'exclusive_hot_content_subscribe_package',
    5: 'first_month_trial_offer',
    6: 'nearly_expire_banner',
    7: 'expire_banner',
    8: 'nearly_expire_fullscreen',
    9: 'expire_fullscreen'
  },
  RELATED_RIBBON: {
    ID: 'video-lien-quan',
    NAME: 'Video liên quan'
  },
  RECOMMEND_RIBBON: {
    ID: 'de-xuat-cho-ban',
    NAME: 'Đề xuất cho bạn'
  },
  END_SCREEN_SUGGESTION: {
    ID: 'end_screen_suggestion',
    NAME: 'end_screen_suggestion'
  },
  EPISODE: {
    ID: 'danh-sach-tap',
    NAME: 'Danh sách tập'
  },
  MASTER_BANNER: {
    ID: 'master_banner_id',
    NAME: 'master_banner'
  },
  PROFILE_RENTED: {
    ID: 'profile_rented_id',
    NAME: 'Nội dung đang thuê',
    MENU_NAME: 'Trang cá nhân'
  },
  PROFILE_WATCH_MORE: {
    ID: 'profile_watch_more_id',
    NAME: 'Đang xem',
    MENU_NAME: 'Trang cá nhân'
  },

  //Account Deletion
  RESTORE_ACCOUNT_SOCIAL_LOGIN_PHONE: 'restore_account_social_login_phone',
  RESTORE_ACCOUNT_SOCIAL_BIND_ACCOUNT: 'restore_account_social_bind_account',
  RESTORE_ACCOUNT_LOGIN_PHONE: 'restore_account_login_phone',
  RESTORE_ACCOUNT_SIGN_UP: 'restore_account_sign_up',
  RESTORE_ACCOUNT_FORGOT_PASSWORD: 'restore_account_forgot_password',

  //profile
  HOME_PAGE: 'home',
  VOD_INTRO_PAGE: 'view_video_detail',
  LIVETV_PAGE: 'livetv',
  TRIAL_CONTENT: 'trial_content',
  PROFILE_PAGE: 'Trang cá nhân',

  // MWeb To App
  TRIGGER_DIRECTLY_SMART_BANNER_OS: 'trigger_directly_smart_banner_os',
  TRGGIER_DIRECTLY_FOOTER: 'trigger_directly_footer',
  TRGGIER_DIRECTLY_FAB: 'trigger_directly_fab',
  TRGGIER_BY_CONTENT_RIBBON_ONLY_APP: 'trigger_by_content_ribbon_only_app',
  TRIGGER_BY_CONTENT_MOVIE_TRIAL_15P: 'trigger_by_content_movie_trial_15p',
  TRIGGER_BY_CONTENT_MOVIE_TVSERIES_ONLY_IN_APP: 'trigger_by_content_movie_tvseries_only_in_app',
  TRIGGER_BY_CONTENT_MOVIE_TVSERIES_TRIAL_EPISODES: 'trigger_by_content_tvseries_trial_episodes',
  TRIGGER_BY_BENEFIT: 'trigger_by_benifit',

  // Multi Profile
  MULTI_PROFILE_CONFIG: 'multi_profile_config',
  MULTI_PROFILE_CONFIRM: 'multi_profile_confirm',
  MULTI_PROFILE_LOBBY: 'multi_profile_lobby',
  REGISTRATION_MULTI_PROFILE: 'registration_multi_profile',
  MULTI_PROFILE_ADD_PROFILE: 'multi_profile_add_profile',
  MULTI_PROFILE_ADD_PROFILE_NON_KID: 'multi_profile_add_profile_non_kid',
  MULTI_PROFILE_ADD_PROFILE_KID: 'multi_profile_add_profile_kid',

  //Dialog / popup
  DIALOG_REGISTRATION_CONVERSION: 'Dialog Đăng nhập để xem nội dung',
  REGISTRATION_TRIAL: 'registration_trial',
  REGISTRATION_FOR_CONTENT: 'registration_for_content',
  REGISTRATION_FOR_LIVETV: 'registration_for_livetv',
  REGISTRATION_FOR_LIVESTREAM: 'registration_for_livestream',
  REGISTRATION_FOR_VOD: 'registration_for_vod',
  FORCE_LOGIN_INFOBOX: 'force_login_inforbox',
  FORCE_LOGIN_NOTIFICATION: 'force_login_notification',
  FORCE_LOGIN_BUTTON: 'force_login_button',
  FORCE_LOGIN: 'force_login',
  REGISTRATION_TRIGGER_LOADED: 'registration_trigger_loaded',

  //PLAYER ERROR
  URL_EMPTY: 'URL_EMPTY',
  DASH: 'DASH',
  HLS: 'HLS',

  //segment user
  OFFER_FREE_TO_SUB: 'offer_free_to_sub',

  // fast_track
  FAST_TRACK: 'fast_track',

  // login_button_selected updated
  SVOD_TRIAL_TRIGGER: 'svod_trial_trigger',
  SVOD_TRIGGER: 'svod_trigger',
  VIP_FEATURE_TRIGGER: 'vip_feature_trigger'
};
