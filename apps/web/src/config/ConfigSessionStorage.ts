export const setSession = (key: any, value: any) => {
  if (typeof window === 'undefined' || !window.sessionStorage) return '';
  try {
    return window.sessionStorage.setItem(key, value);
  } catch (e) {
    return { error: e };
  }
};

export const getSession = (key: any) => {
  if (typeof window === 'undefined' || !window.sessionStorage) return '';
  try {
    return window.sessionStorage.getItem(key);
  } catch (e) {
    return { error: e };
  }
};

export const removeSession = (key: any) => {
  if (typeof window === 'undefined' || !window.sessionStorage) return '';
  try {
    return window.sessionStorage.removeItem(key);
  } catch (e) {
    return { error: e };
  }
};

export const clearSession = () => {
  if (typeof window === 'undefined' || !window.sessionStorage) return '';
  try {
    return window.sessionStorage.clear();
  } catch (e) {
    return { error: e };
  }
};
