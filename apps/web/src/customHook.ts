import { useState, useEffect } from 'react';
import isEmpty from 'lodash/isEmpty';
import { useRouter } from 'next/router';
import { isMobile } from 'react-device-detect';
import { EL_ID } from '@constants/constants';
import { formatQueryParams } from '@helpers/common';
export const useVieRouter: any = () => {
  const router = useRouter();
  const { query } = router || {};

  const push = (url: any, as: any, options: any) => {
    if (isEmpty(url)) return;
    const urlRemake = formatQueryParams(url, query);
    const asRemake = formatQueryParams(as, query);
    return router.push(urlRemake, asRemake, options);
  };

  const replace = (url: any, as: any, options: any) => {
    if (isEmpty(url)) return;
    const urlRemake = formatQueryParams(url, query);
    const asRemake = formatQueryParams(as, query);
    return router.replace(urlRemake, asRemake, options);
  };

  const prefetch = (url: any, as: any, options: any) => {
    if (isEmpty(url)) return;
    const urlRemake = formatQueryParams(url, query);
    const asRemake = formatQueryParams(as, query);
    return router.prefetch(urlRemake, asRemake, options);
  };

  return { ...router, push, replace, prefetch };
};

export const useMoveOutSideElement = (ref: any, callback: any) => {
  useEffect(() => {
    function handleMoveOutside(e?: any) {
      if (ref.current) {
        const isContain = !!ref.current.contains(e.target);
        if (!isContain && typeof callback === 'function') {
          callback();
        }
      }
    }
    // Bind the event listener
    document.addEventListener('mousemove', handleMoveOutside);
    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener('mousemove', handleMoveOutside);
    };
  }, [ref]);
};

export const useOutsideEvent = (ref: any, clickOutsideCallback: any) => {
  useEffect(() => {
    function handleClickOutside(event?: any) {
      const modalOverlay = document.getElementById(EL_ID.MODAL);
      const moengageNotify = document.getElementById('moe-push-div');
      if (
        (modalOverlay && modalOverlay.contains(event.target)) ||
        (moengageNotify && moengageNotify.contains(event.target))
      ) {
        return;
      }
      if (ref.current && !ref.current.contains(event.target)) {
        if (
          event.offsetX + 15 > event.target.clientWidth ||
          event.offsetY > event.target.clientHeight
        ) {
          return null;
        }
        if (
          event?.target?.id === EL_ID.MODAL_WELCOME_ADS ||
          event?.target?.id === EL_ID.BANNER_INTRO_BACKGROUND ||
          event?.target?.id === EL_ID.INPAGE_INTRO
        ) {
          return;
        }
        if (typeof clickOutsideCallback === 'function') clickOutsideCallback();
      }
      return;
    }
    // Bind the event listener
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref]);
};

export const useViewport = () => {
  const [width, setWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1920);

  useEffect(() => {
    const handleWindowResize = () => setWidth(window.innerWidth);
    window.addEventListener('resize', handleWindowResize);
    return () => window.removeEventListener('resize', handleWindowResize);
  }, []);

  return {
    width
  };
};

export const usePorTrait = () => {
  const [porTrait, setPorTrait] = useState(
    (typeof window !== 'undefined' && window.matchMedia('(orientation: portrait)').matches) || false
  );
  useEffect(() => {
    if (!isMobile) return;
    const handleOrientationchange = (e: any) => {
      if (e?.target?.screen?.orientation?.angle > 0) setPorTrait(false);
      else setPorTrait(true);
    };

    // handle for ios
    const handleResize = () => {
      const isPortrait = window.innerHeight > window.innerWidth;
      setPorTrait(isPortrait);
    };

    window.addEventListener('orientationchange', handleOrientationchange);
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('orientationchange', handleOrientationchange);
      window.removeEventListener('resize', handleResize);
    };
  }, [isMobile]);
  return {
    porTrait
  };
};

export const useOnClickOutside = (ref: any, handler: any, exceptions?: any) => {
  useEffect(() => {
    const listener = (event?: any) => {
      if (exceptions?.current && exceptions.current.contains(event.target)) {
        return;
      }
      // Do nothing if clicking ref's element or descendent elements
      if (!ref.current || ref.current.contains(event.target)) {
        return;
      }
      handler(event);
    };
    document.addEventListener('mousedown', listener);
    document.addEventListener('touchstart', listener);
    return () => {
      document.removeEventListener('mousedown', listener);
      document.removeEventListener('touchstart', listener);
    };
  }, [ref, handler]);
};

export const useFormInput = (initialValue: any) => {
  const [value, setValue] = useState(initialValue);
  const handleChange = (event?: any) => {
    setValue(event?.target?.value);
  };
  return { value, onChange: handleChange };
};

export const useCountdown = ({ time }: any) => {
  const [countDown, setCountDown] = useState(time);
  useEffect(() => {
    const interval = setInterval(() => {
      setCountDown(countDown - 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [countDown]);

  return countDown;
};

export const groupPackageTerms = (pkg: any) => {
  const { terms = [], durationGroup = true } = pkg;

  if (isEmpty(terms)) {
    return { ...pkg, terms: [] };
  }

  if (durationGroup) {
    const groupedData = terms.reduce((acc: any, item: any) => {
      const key = `${item.duration}_${item.durationType}`;

      if (!acc[key]) {
        acc[key] = { ...item };
      } else {
        if (item.recurring && !acc[key].recurring) {
          acc[key] = { ...item, noneRecurring: acc[key] };
        } else if (!item.recurring && acc[key].recurring) {
          acc[key].noneRecurring = item;
        }
      }

      return acc;
    }, {});

    return {
      ...pkg,
      terms: Object.values(groupedData)
    };
  }

  return pkg;
};
