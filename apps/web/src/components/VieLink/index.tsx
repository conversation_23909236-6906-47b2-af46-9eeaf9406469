import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { formatQueryParams } from '@helpers/common';

const VieLink = (props: any) => {
  const { children, href, as, ...rest } = props || {};
  const router = useRouter();
  const { query } = router || {};
  const hrefRemake = formatQueryParams(href, query);
  const asRemake = formatQueryParams(as, query);
  if (!href) return children;
  return (
    <Link {...rest} href={hrefRemake} as={asRemake}>
      {children}
    </Link>
  );
};

export default VieLink;
