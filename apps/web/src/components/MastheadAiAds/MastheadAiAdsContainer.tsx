import React, { useEffect } from 'react';
import classNames from 'classnames';
import Backdrops from '@components/basic/Backdrops/Backdrops';
import ButtonClose from '@components/MastheadAiAds/ButtonClose';
import MastheadAdsPlayer from '@components/basic/Player/MastheadAdsPlayer';
import Countdown from '@components/MastheadAiAds/Countdown';
import { aiActiveThirdTracking } from '@apis/aiactiv-third-tracking';
import Styles from './Styles.module.scss';

const MastheadAiAdsContainer = ({
  backdropUrl,
  videoSrc,
  videoType,
  bannerUrl,
  videoDuration,
  handleClickBanner,
  handleSetAdsDuration,
  handleClose,
  isOpen,
  hideFullScreen,
  vade,
  events,
  impressionURLTemplates
}: any) => {
  useEffect(() => {
    aiActiveThirdTracking({
      vadeUrl: vade,
      events,
      impressionURLTemplates
    });
  }, [backdropUrl]);

  if (hideFullScreen) {
    return (
      <div className={classNames(Styles.MastheadBanner, hideFullScreen && 'animate-fade-in')}>
        <div className={Styles.MastheadBannerContainer} onClick={handleClickBanner}>
          <Backdrops
            className={Styles.MastheadBackdrop}
            imgDesktop={bannerUrl}
            alt="alt Back drop"
          />
          {/* <TagAds className={classNames(Styles.MastheadTagAds, Styles.MastheadTagAdsBanner)} /> */}
        </div>
      </div>
    );
  }

  return (
    <dialog
      className={classNames(
        Styles.MastheadFullscreen,
        isOpen && 'animate-fade-in',
        !isOpen && 'animate-fade-out'
      )}
      open={isOpen}
    >
      <div className={Styles.MastheadFullscreenContainer}>
        <Backdrops
          className={Styles.MastheadBackdrop}
          imgDesktop={backdropUrl}
          alt="alt Back drop"
        />
        <div className={Styles.MastheadFullscreenBlock} onClick={handleClickBanner}>
          <Countdown className={Styles.MastheadCountdown} time={videoDuration} />
          <div className={Styles.MastheadFullscreenMainContent}>
            {videoSrc && (
              <MastheadAdsPlayer
                type={videoType}
                mediaUrl={videoSrc}
                setAdsDuration={handleSetAdsDuration}
              />
            )}
          </div>
          {/* <TagAds className={classNames(Styles.MastheadTagAds, Styles.MastheadTagAdsFullScreen)} /> */}
        </div>
        {isOpen && <ButtonClose className={Styles.MastheadButtonClose} onClick={handleClose} />}
      </div>
    </dialog>
  );
};

export default MastheadAiAdsContainer;
