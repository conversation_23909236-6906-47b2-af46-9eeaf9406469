.<PERSON><PERSON><PERSON> {
  &Backdrop {
    @apply absolute w-full h-full top-0 left-0 z-0;
  }

  &<PERSON><PERSON> {
    &Close {
      @apply flex w-9 h-9 absolute top-5 right-5 items-center justify-center rounded-full bg-[#111]/70 text-white text-[1.125rem] z-20;
    }
  }

  &Countdown {
    @apply flex w-10 h-10 absolute bottom-5 md:bottom-10 xl:bottom-12 left-4 md:left-9 xl:left-12 items-center justify-center rounded-full bg-[#646464] text-white text-[1.25rem] z-20;
  }

  //&TagAds {
  //  @apply flex items-center justify-center bg-black/50 text-white z-20 leading-none;
  //
  //  &<PERSON> {
  //    @apply absolute bottom-4 md:bottom-[.625rem] left-[.625rem] p-[.25rem] text-[.75rem];
  //  }
  //
  //  &FullScreen {
  //    @apply absolute bottom-4 md:bottom-10 left-4 md:left-9 p-[.375rem] text-[.875rem];
  //  }
  //}

  &Fullscreen {
    @apply fixed w-full h-full top-0 left-0 z-[10000] bg-[#111] cursor-pointer;

    &Container {
      @apply block relative w-full h-full z-10 box-border;
    }

    &Block {
      @apply relative w-full h-full p-4 md:p-9 lg:p-[3.75rem];
    }

    &MainContent {
      @apply relative max-w-[60%] max-h-[100%] aspect-[1152/649] ml-auto;

      & > video {
        @apply w-full h-full;
      }
    }
  }

  &Banner {
    @apply w-full fixed top-0 left-0 z-[1000] bg-[#111] aspect-[1920/192];

    &Container {
      @apply block relative w-full h-full z-10 box-border cursor-pointer;
      @apply before:w-full before:h-[calc(100%+2px)] before:absolute before:top-0 before:left-1/2 before:-translate-x-1/2 before:bg-[#111];
    }

    &Section {
      & + header,
      & ~ header {
        @apply top-[10vw];

        & + main,
        & ~ main {
          @apply pt-[calc(10vw+3.5rem)];
        }

        &[data-sub='true'],
        &[data-sub-menu='true'] {
          & + main,
          & ~ main {
            @apply lg:pt-[calc(10vw+7rem)];
          }
        }

        &[data-sub='true'][data-sub-menu='true'] {
          & + main,
          & ~ main {
            @apply lg:pt-[calc(10vw+10.5rem)];
          }
        }
      }
    }
  }
}
