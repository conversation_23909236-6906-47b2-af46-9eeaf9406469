import React from 'react';
import { TEXT } from '@constants/text';
import EmptyWatchLater from '../empty/EmptyWatchLater';
import RibbonLoading from '../basic/Loading/RibbonLoading';
import Card from '../basic/Card/Card';

const Watching = ({ items, metadata, loadMoreData, profile }: any) => {
  let isLoadMore = false;
  let pageLoadMore = (metadata && metadata.page) || 0;
  const totalpage = metadata ? Math.floor(metadata.total / 30) : 0;
  pageLoadMore += 1;

  if (items && metadata && items.length < metadata.total - 1 && pageLoadMore <= totalpage) {
    isLoadMore = true;
  }
  if (!items) return <RibbonLoading width={300} />;
  if (items && items.length === 0) {
    return <EmptyWatchLater />;
  }
  return (
    <section className="section section--episode-list overflow">
      <div className="container">
        <div className="section-body">
          <div className="ribbon ribbon-list ribbon-list--episode">
            <div className="ribbon-header canal-v">
              <div className="grid-x align-middle">
                <div className="cell medium-auto">
                  <h1 className="title">{TEXT.WATCHING}</h1>
                </div>
              </div>
            </div>
            <div className="ribbon-body canal-v">
              <div className="slides flex-box slick-initialized flex-box-margin-x" data-item="6">
                {items?.length > 0 &&
                  items.map((item: any, i: any) => (
                    <Card
                      profile={profile}
                      lazyImg
                      className="item"
                      cardData={item}
                      key={item.id || i}
                    />
                  ))}
              </div>
              {isLoadMore && (
                <div className="load-more text-center">
                  <button className="button secondary" onClick={() => loadMoreData(pageLoadMore)}>
                    Hiển thị thêm
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Watching;
