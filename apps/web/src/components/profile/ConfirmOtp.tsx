import React from 'react';
import Button from '@components/basic/Buttons/Button';
import CountdownButton from '@components/basic/Buttons/CountdownButton';
import InputCustomOtpCode from '@components/basic/Input/InputCustomOtpCode';
import { TEXT } from '@constants/text';
import { TIME_RESEND_OTP } from '@constants/constants';

const ConfirmOtp = ({
  onChangeOtp,
  onReSendOtpCode,
  forceDisabled,
  error,
  otpCode,
  timeResend,
  title,
  subTitle,
  onEnter,
  isUpdatePassword,
  titleBtn,
  isVipUser,
  isHideClickLater,
  closeX
}: any) => {
  const disabled = !(!error && otpCode.length === 4);
  const classForm = 'form form-default form-for-dark form--update';
  const rootClass = isUpdatePassword ? `${classForm} m-t` : classForm;

  return (
    <div className="block block--for-dark">
      {title && <h2 className="title text-center text-white f-medium">{title}</h2>}
      {subTitle && <p className="text text-white font-size-16 m-t2 m-b4 text-center">{subTitle}</p>}
      <form className={rootClass} data-abide="true" noValidate name="password otp">
        <InputCustomOtpCode id="otpCode" onChangeOtp={onChangeOtp} onEnter={onEnter} />
      </form>
      <div className="text-center relative p-b4">
        <CountdownButton
          buttonName={TEXT.RE_SEND_OTP}
          time={timeResend || TIME_RESEND_OTP}
          onResend={onReSendOtpCode}
          forceDisabled={forceDisabled}
        />
        {error && (
          <label className="form-error is-visible absolute text-center middle-v top m-b m-t2">
            {error}
          </label>
        )}
      </div>
      <div className="button-group child-auto">
        {isVipUser && !isHideClickLater && (
          <Button
            className="button hollow button--dark-glass button--large"
            onClick={closeX}
            title={TEXT.LATER}
          />
        )}
        <Button
          id="autoClick"
          className={`button button--light button--large${otpCode.length === 4 ? '' : ' disabled'}`}
          title={titleBtn || TEXT.CONFIRM}
          disabled={disabled}
        />
      </div>
    </div>
  );
};

export default React.memo(ConfirmOtp);
