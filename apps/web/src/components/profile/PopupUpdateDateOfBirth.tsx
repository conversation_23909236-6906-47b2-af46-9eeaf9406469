import React, { useMemo, useState } from 'react';
import moment from 'moment';
import { useDispatch, useSelector } from 'react-redux';
import { setToast } from '@actions/app';
import CustomButtonGroup from '../basic/Buttons/CustomButtonGroup';
import { DATE_FORMAT, ID } from '@constants/constants';
import DatePickerCustom from '../DatePicker';
import { TEXT } from '@constants/text';
import ConfigImage from '@config/ConfigImage';
import Image from '../basic/Image/Image';

const PopupUpdateDateOfBirth = ({ onClosePopupProfile, onUpdateDateOfBirth, profile }: any) => {
  const [selectedDate, setSelectedDate] = useState(profile?.dob || null);
  const dispatch = useDispatch();
  const { deviceModel, deviceName, deviceType } = useSelector((state: any) => state?.App || {});

  const formattedDate = useMemo(() => {
    if (selectedDate) {
      return moment(selectedDate).format(DATE_FORMAT.YYMMDD.toUpperCase());
    }
    return '';
  }, [selectedDate]);

  const handleChange = (date: any) => {
    setSelectedDate(date);
  };

  const handleUpdateDateOfBirth = () => {
    onUpdateDateOfBirth({ dob: formattedDate, deviceModel, deviceName, deviceType }).then(
      (resp: any) => {
        if (resp?.success) {
          dispatch(setToast({ message: TEXT.UPDATE_DOB_SUCCESS }));
          if (onClosePopupProfile) {
            onClosePopupProfile();
          }
        } else if (resp?.message) {
          dispatch(setToast({ message: resp?.message }));
        } else {
          dispatch(setToast({ message: TEXT.MSG_ERROR }));
        }
      }
    );
  };

  return (
    <form
      data-abide="true"
      className="form form-default form-for-light form--member form--member-modal form--member-sign"
      autoComplete="off"
      id={ID.UPDATE_DATE_OF_BIRTH}
    >
      <DatePickerCustom
        label={TEXT.USER_LABEL.DATE_OF_BIRTH}
        id={ID.UPDATE_DATE_OF_BIRTH}
        value={selectedDate}
        icon={<Image src={ConfigImage.calendar} alt="calendar" />}
        isDisabledKeyboardNavigation
        maxYear={18}
        onChange={handleChange}
      />
      <CustomButtonGroup
        classNameLeft="button hollow button--dark-glass button--large"
        classNameRight="button button--light button--large"
        onClick={handleUpdateDateOfBirth}
        isDisabled={!selectedDate}
        onClose={onClosePopupProfile}
      />
    </form>
  );
};

export default PopupUpdateDateOfBirth;
