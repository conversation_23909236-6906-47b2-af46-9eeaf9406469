import React, { useState } from 'react';
import ConfigImage from '@config/ConfigImage';
import { parsePayDuration, trans } from '@helpers/common';
import VieLink from '@components/VieLink';
import moment from 'moment';
import { CURRENCY, PAGE } from '@constants/constants';
import { TEXT } from '@constants/text';

const TransactionHistory = ({
  dataTransactionSuccess,
  dataTransaction,
  isLoadMoreDataTransaction,
  isLoadMoreDataTransactionSuccess,
  loadMore
}: any) => {
  const [state, setState] = useState({ currentPage: 'historyTransactionSuccess' });

  const setActiveTab = (id: any) => {
    if (state.currentPage !== id) {
      setState((prevState) => ({
        ...prevState,
        currentPage: id
      }));
    }
  };

  const handleLoadMoreData = () => {
    const { currentPage } = state;
    loadMore(currentPage);
  };

  return (
    <section className="section section--user section--user-history section--user-history-transaction overflow">
      <div className="container">
        <div className="grid-x align-center">
          <div className="cell medium-11">
            <div className="section__header line">
              <h2 className="title">{TEXT.USER_ACTION.BILLING_DETAILS}</h2>
            </div>
            <div className="section__body">
              <ul className="tabs tabs--history">
                <li
                  className={`tabs-title ${
                    state.currentPage === 'historyTransactionSuccess' ? 'is-active' : ''
                  }`}
                >
                  <a
                    title="Gói Dịch vụ đã mua"
                    onClick={() => setActiveTab('historyTransactionSuccess')}
                  >
                    Gói Dịch vụ đã mua
                  </a>
                </li>
                <li
                  className={`tabs-title ${
                    state.currentPage === 'historyTransaction' ? 'is-active' : ''
                  }`}
                >
                  <a title="Lịch sử giao dịch" onClick={() => setActiveTab('historyTransaction')}>
                    Lịch sử giao dịch
                  </a>
                </li>
              </ul>
              <div
                className="tabs-content tabs-content-history"
                data-tabs-content="historyTransaction"
              >
                <div
                  className={`tabs-panel ${
                    state.currentPage === 'historyTransactionSuccess' ? 'is-active' : ''
                  }`}
                  id="historyTransactionSuccess"
                >
                  <HistoryTransactionSuccess
                    dataTransactionSuccess={dataTransactionSuccess}
                    isLoadMore={isLoadMoreDataTransactionSuccess}
                    loadMore={handleLoadMoreData}
                  />
                </div>
                <div
                  className={`tabs-panel ${
                    state.currentPage === 'historyTransaction' ? 'is-active' : ''
                  }`}
                  id="historyTransaction"
                >
                  <HistoryTransaction
                    dataTransaction={dataTransaction}
                    isLoadMore={isLoadMoreDataTransaction}
                    loadMore={handleLoadMoreData}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

const HistoryTransactionSuccess = ({ dataTransactionSuccess, isLoadMore, loadMore }: any) => {
  const { items } = dataTransactionSuccess?.[0] || {};
  if (items && items.length === 0) {
    return (
      <div className="empty empty-transaction-history">
        <div className="content text-center">
          <img
            className="inline-block"
            src={ConfigImage.emptyTransactionHistory}
            alt="Empty content"
          />
          <p className="text p-y4">Bạn chưa đăng ký gói dịch vụ nào</p>
          <a className="button rounded linear-gradient" href="/goi-dich-vu" aria-label="Mua ngay">
            Mua ngay
          </a>
        </div>
      </div>
    );
  }
  return (
    dataTransactionSuccess && (
      <>
        <div className="card-group margin-x1" data-item="3">
          {Object.keys(dataTransactionSuccess).map(
            (key) =>
              (dataTransactionSuccess?.[key]?.items &&
                dataTransactionSuccess[key].items.map((item: any, i: any) => {
                  const { name, expired_date, image, id } = item;

                  const isExpired = moment(expired_date).unix() < moment().unix();
                  const displayDateTime = isExpired
                    ? 'đã hết hạn'
                    : moment(expired_date).format('HH:mm - DD/MM/Y');

                  return (
                    <div className="card card--services-purchased" key={i}>
                      <div className="thumb-nail">
                        <img src={image} className="thumb-img" alt="" />
                      </div>

                      <div className="card-section">
                        <div className="grid-x">
                          <div className="cell auto">
                            <h4 className="p-r1">{name}</h4>
                            <p className="p-r1">
                              Ngày hết hạn: <span>{displayDateTime}</span>
                            </p>
                          </div>
                          {isExpired && (
                            <div className="cell shrink align-self-bottom">
                              <VieLink
                                href={{
                                  pathname: PAGE.PAYMENT_METHOD,
                                  query: { package_id: id }
                                }}
                              >
                                <a
                                  className="button primary rounded linear-gradient width-140 height-40"
                                  title="Gia hạn"
                                  tabIndex={0}
                                >
                                  Gia hạn
                                </a>
                              </VieLink>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })) ||
              null
          )}
        </div>
        {isLoadMore && (
          <div className="load-more text-center">
            <button
              className="button linear-gradient rounded"
              type="button"
              value="Submit"
              onClick={loadMore}
            >
              Hiển thị thêm
            </button>
          </div>
        )}
      </>
    )
  );
};

const HistoryTransaction = ({ dataTransaction, isLoadMore, loadMore }: any) => {
  const { items } = dataTransaction?.[0] || {};
  if (items && items.length === 0) {
    return (
      <div className="empty empty-transaction-history">
        <div className="content text-center">
          <img
            className="inline-block"
            src={ConfigImage.emptyTransactionHistory}
            alt="Empty content"
          />
          <p className="text p-y4">Bạn chưa có lịch sử đăng ký gói dịch vụ nào</p>
          <a className="button rounded linear-gradient" href="/goi-dich-vu" aria-label="Mua ngay">
            Mua ngay
          </a>
        </div>
      </div>
    );
  }
  let stt = 0;
  return (
    dataTransaction && (
      <>
        <table className="table table--transaction-history">
          <thead>
            <tr>
              <th style={{ width: 54 }}>STT</th>
              <th style={{ width: 350 }}>Tên gói</th>
              <th style={{ width: 150 }}>Thời hạn</th>
              <th style={{ width: 150 }}>Giá trị</th>
              <th style={{ width: 150 }}>Khuyến mãi</th>
              <th style={{ width: 280 }}>Tổng tiền thanh toán</th>
              <th style={{ width: 350 }}>Thời gian giao dịch</th>
              <th style={{ width: 250 }}>Trạng thái</th>
              <th style={{ width: 172 }}>Mã giao dịch</th>
              <th style={{ width: 300 }}>Hình thức thanh toán</th>
            </tr>
          </thead>
          <tbody>
            {Object.keys(dataTransaction).map(
              (key) =>
                (dataTransaction?.[key]?.items &&
                  dataTransaction[key].items.map((item: any) => {
                    const {
                      txn_ref,
                      duration,
                      name,
                      package_value,
                      promotion,
                      service_name,
                      status,
                      created_date
                    } = item;
                    const displayDateTime = moment(created_date).format('HH:mm:ss - DD/MM/Y');

                    return (
                      <tr key={stt}>
                        <td>{++stt}</td>
                        <td>{name}</td>
                        <td>{parsePayDuration(duration)}</td>
                        <td>
                          {`${new Intl.NumberFormat(['ban', 'id']).format(package_value || 0)} ${
                            CURRENCY.VND
                          }`}
                        </td>
                        <td>
                          {`${new Intl.NumberFormat(['ban', 'id']).format(promotion || 0)} ${
                            CURRENCY.VND
                          }`}
                        </td>
                        <td>
                          {`${new Intl.NumberFormat(['ban', 'id']).format(package_value || 0)} ${
                            CURRENCY.VND
                          }`}
                        </td>
                        <td>{displayDateTime}</td>
                        <td>{trans(status)}</td>
                        <td>{txn_ref}</td>
                        <td>{service_name}</td>
                      </tr>
                    );
                  })) ||
                null
            )}
          </tbody>
        </table>
        {isLoadMore && (
          <div className="load-more text-center">
            <button
              className="button linear-gradient rounded"
              type="button"
              value="Submit"
              onClick={loadMore}
            >
              Hiển thị thêm
            </button>
          </div>
        )}
      </>
    )
  );
};

export default TransactionHistory;
