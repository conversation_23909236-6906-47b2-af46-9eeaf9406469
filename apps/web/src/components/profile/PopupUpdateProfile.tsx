/* eslint-disable no-useless-escape */
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Button from '@components/basic/Buttons/Button';
import { TEXT } from '@constants/text';
import { ID, LOBBY, ERROR_CODE } from '@constants/constants';
import { INVOICE_INPUT_TYPE } from '@constants/types';
import { setToast } from '@actions/app';
import { confirmOtpUpdatePasswordSuccess } from '@actions/user';
import { useFormInput, useVieRouter } from '@customHook';
import { exportActivityEmail } from '@actions/multiProfile';
import TrackingLoyalty from '@tracking/functions/TrackingLoyalty';
import UserApi from '@apis/userApi';
import CustomButtonGroup from '../basic/Buttons/CustomButtonGroup';
import Modal from '../basic/Modal';
import InputCustom from '../basic/Input/InputCustom';
import PopupConfirmOtpUpdatePassword from './PopupConfirmOtpUpdatePassword';
import { validateEmail } from '@helpers/common';
import PopupUpdateDateOfBirth from './PopupUpdateDateOfBirth';

const PopupUpdateProfile = ({
  closePopupProfile,
  openPopupConfirmUpdatePhone,
  onDeleteHistory,
  dataProfile,
  updateGivenName,
  updateGender,
  updatePassword,
  updateEmail,
  updateDob,
  updateMobile,
  updateInvoiceInfo,
  actionName
}: any) => {
  const dispatch = useDispatch();
  const onClosePopupProfile = () => {
    closePopupProfile();
    dispatch(confirmOtpUpdatePasswordSuccess({}));
  };
  const { confirmedOtpUpdatePassword } = useSelector((state: any) => state?.User || {});
  const sessionId = confirmedOtpUpdatePassword?.session_id || {};
  // display otp
  const [isHideOtpUpdatePassword, setHideOtpUpdatePassword] = useState(false);
  const phoneNumber = useSelector((state: any) => state?.Profile?.profile?.mobile);
  const { deviceModel, deviceName, deviceType } = useSelector((state: any) => state?.App || {});
  // data form input
  const givenNameTemp = useFormInput(dataProfile?.givenName || '');
  // data error
  const [errorGivenName, setErrorGivenName] = useState<any>('');
  const [genderList, setGenderList] = useState([]);
  // const [selectedGender, setSelectedGender] = useState<any>(null);
  const router = useVieRouter();
  // get data toggle receive email in kids activity report
  const { isExportEmail } = useSelector((state: any) => state?.MultiProfile?.kidsActivity) || false;
  const { isOnLoyalty } = useSelector((state: any) => state?.App?.webConfig?.featureFlag || false);
  // data form input email
  const emailTemp = useFormInput(dataProfile?.email || '');
  // data error state
  const [errorEmail, setErrorEmail] = useState<any>('');

  useEffect(
    () => () => {
      clearTimeout(oldPasswordTimerRef.current);
      clearTimeout(passwordTimerRef.current);
      clearTimeout(confirmPasswordTimerRef.current);
    },
    []
  );

  useEffect(() => {
    if (actionName === ID.UPDATE_GENDER && dataProfile) {
      UserApi.getConfig({ key: LOBBY.GENDERS }).then((res) => {
        setGenderList(res);
      });
    }
  }, [actionName, dataProfile]);

  useEffect(() => {
    if (errorEmail && emailTemp?.value === '') {
      setErrorEmail('');
    }
  }, [emailTemp?.value, errorEmail]);

  const onUpdateGivenName = (e: any) => {
    e.preventDefault();
    const givenName = givenNameTemp.value;
    const check = /^\s*\s*$/.test(givenName);
    if (givenName === '' || check) {
      setErrorGivenName(TEXT.NAME_EMPTY);
      return;
    }
    if (givenName.length > 50) {
      setErrorGivenName(TEXT.NAME_LENGTH);
      return;
    }
    const spaceGivenName = givenName.indexOf('  ');
    if (spaceGivenName !== -1) {
      setErrorGivenName(TEXT.ERROR_SPACE_NAME);
      return;
    }
    const SPECIAL_CHAR_REGEX = /[`~!@#$%^&*()-+=+:;"'<>?/,.{}[]|\]/;
    if (SPECIAL_CHAR_REGEX.test(givenName || '')) {
      setErrorGivenName(TEXT.ERROR_SPEC_CHAR_NAME);
      return;
    }
    if (givenName === dataProfile?.givenName || !givenName) {
      return;
    }
    updateGivenName(givenName).then((resp: any) => {
      if (resp?.success) {
        dispatch(setToast({ message: TEXT.UPDATE_INFO_SUCCESS }));
        closePopupProfile();
      } else if (resp?.message) {
        setErrorGivenName(resp?.message);
      } else {
        dispatch(setToast({ message: TEXT.MSG_ERROR }));
      }
    });
  };

  const renderUpdateGivenName = () => (
    <form
      className="form form-default form-for-light form--member form--member-modal form--member-sign"
      data-abide="true"
      noValidate
    >
      <InputCustom
        label="Họ và tên"
        id="givenNameTemp"
        type="text"
        onEnter={onUpdateGivenName}
        error={errorGivenName}
        valueOutput={givenNameTemp}
        className="input-for-dark"
        icon="vie vie-user-o-c-medium"
      />
      <CustomButtonGroup
        classNameLeft="button hollow button--dark-glass button--large"
        classNameRight="button button--light button--large"
        onClick={(event: any) => {
          onUpdateGivenName(event);
        }}
        isDisabled={
          !!(givenNameTemp.value === dataProfile?.givenName || givenNameTemp.value === '')
        }
        onClose={onClosePopupProfile}
      />
    </form>
  );

  const onUpdateEmail = (e: any) => {
    e.preventDefault();
    const email = emailTemp.value;

    if (email === '' || !validateEmail(email)) {
      setErrorEmail(TEXT.EMAIL_WRONG);
      return;
    }
    if (
      (emailTemp.value === dataProfile?.email && dataProfile.emailVerified) ||
      emailTemp.value === ''
    ) {
      return;
    }
    updateEmail({ email, deviceModel, deviceName, deviceType }).then((resp: any) => {
      const error = resp?.message;
      if (error) {
        setErrorEmail(error);
        return;
      }
      closePopupProfile();
      dispatch(
        setToast({
          message: TEXT.NOTIFY_VERIFY_EMAIL
        })
      );
      if (router?.query?.slug === ID.KIDS_MANAGEMENT) {
        dispatch(
          exportActivityEmail({
            isExportEmail: !isExportEmail,
            email: emailTemp.value || ''
          })
        );
      }
    });
  };

  const renderUpdateEmail = () => (
    <form
      data-abide="true"
      className="form form-default form-for-light form--member form--member-modal form--member-sign"
    >
      <InputCustom
        className="input-for-dark"
        label={TEXT.USER_LABEL.EMAIL}
        id="email"
        type="email"
        error={errorEmail}
        onEnter={onUpdateEmail}
        valueOutput={emailTemp}
      />
      <CustomButtonGroup
        classNameLeft="button hollow button--dark-glass button--large"
        classNameRight="button button--light button--large"
        onClick={(event: any) => {
          onUpdateEmail(event);
        }}
        isDisabled={
          !!(
            (emailTemp.value === dataProfile?.email && dataProfile.emailVerified) ||
            emailTemp.value === ''
          )
        }
        onClose={onClosePopupProfile}
      />
    </form>
  );

  // data form input

  let phoneNumberTemp = useFormInput(dataProfile?.mobile);
  if (phoneNumberTemp.value == null) phoneNumberTemp = useFormInput('');
  // data error
  const [errorPhoneNumber, setErrorPhoneNumber] = useState<any>('');

  const onUpdateMobile = (e: any) => {
    e.preventDefault();
    const phoneNumber = phoneNumberTemp.value;
    const REGEX_PHONE_NUMBER = /^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$/;
    if (
      !REGEX_PHONE_NUMBER.test(phoneNumber) ||
      phoneNumber.length <= 9 ||
      phoneNumber.length >= 12
    ) {
      setErrorPhoneNumber(TEXT.PHONE_WRONG);
      return;
    }
    if (dataProfile?.mobile === phoneNumberTemp.value || phoneNumberTemp.value === '') {
      return;
    }
    updateMobile(phoneNumber).then((resp: any) => {
      const error = resp?.data?.message;
      if (error) {
        setErrorPhoneNumber(error);
        return;
      }
      openPopupConfirmUpdatePhone({ phoneNumber });
    });
  };

  const renderUpdateMobile = () => (
    <form
      data-abide="true"
      className="form form-default form-for-light form--member form--member-modal form--member-sign"
    >
      <InputCustom
        className="input-for-dark"
        label={TEXT.USER_LABEL.PHONE_NUMBER}
        id="phoneNumber"
        type="tel"
        error={errorPhoneNumber}
        onEnter={onUpdateMobile}
        valueOutput={phoneNumberTemp}
        icon="vie vie-hand-phone-o-rc"
      />
      <CustomButtonGroup
        classNameLeft="button hollow button--dark-glass button--large"
        classNameRight="button button--light button--large"
        onClick={(event: any) => {
          onUpdateMobile(event);
        }}
        isDisabled={
          !!(dataProfile?.mobile === phoneNumberTemp.value || phoneNumberTemp.value === '')
        }
        onClose={onClosePopupProfile}
      />
    </form>
  );

  // data form input
  const oldPasswordTemp = useFormInput('');
  const passwordTemp = useFormInput('');
  const confirmPasswordTemp = useFormInput('');
  const oldPasswordTimerRef = useRef<any>(null);
  const passwordTimerRef = useRef<any>(null);
  const confirmPasswordTimerRef = useRef<any>(null);

  const [oldPassword, setOldPassword] = useState<any>('');
  const [password, setPassword] = useState<any>('');
  const [confirmPassword, setConfirmPassword] = useState<any>('');

  const [error, setError] = useState<any>('');

  const onUpdatePassword = (e: any) => {
    e.preventDefault();
    const oldPassword = oldPasswordTemp.value;
    const password = passwordTemp.value;
    const confirmPassword = confirmPasswordTemp.value;
    updatePassword(sessionId, oldPassword, password, confirmPassword).then((resp: any) => {
      const success = resp?.data?.success;
      const errMessage = resp?.data?.message;
      if (success) {
        onClosePopupProfile();
        dispatch(
          setToast({
            message: TEXT.TOAST_UPDATE_PASSWORD_SUCCESS
          })
        );
      } else {
        setError(errMessage || TEXT.MSG_ERROR);
      }
    });
  };

  const validateLengthOldPassword = () => {
    clearTimeout(oldPasswordTimerRef.current);
    oldPasswordTimerRef.current = setTimeout(() => {
      setError('');
      if (oldPasswordTemp.value.length && oldPasswordTemp.value.length < 6) {
        setOldPassword(TEXT.PASSWORD_REQUIRED_VALUE);
      } else {
        setOldPassword('');
      }
    }, 500);
  };

  const validateLengthPassword = () => {
    clearTimeout(passwordTimerRef.current);
    passwordTimerRef.current = setTimeout(() => {
      setError('');
      if (passwordTemp.value.length && passwordTemp.value.length < 6) {
        setPassword(TEXT.PASSWORD_REQUIRED_VALUE);
      } else if (
        confirmPasswordTemp.value.length &&
        passwordTemp.value.length &&
        passwordTemp.value !== confirmPasswordTemp.value
      ) {
        setConfirmPassword(TEXT.PASSWORD_NOT_MATCHING);
        setPassword('');
      } else {
        setConfirmPassword('');
        setPassword('');
      }
    }, 500);
  };

  const validateLengthConfirmPassword = () => {
    clearTimeout(confirmPasswordTimerRef.current);
    confirmPasswordTimerRef.current = setTimeout(() => {
      setError('');
      if (confirmPasswordTemp.value.length && confirmPasswordTemp.value.length < 6) {
        setConfirmPassword(TEXT.PASSWORD_REQUIRED_VALUE);
      } else if (
        confirmPasswordTemp.value.length &&
        passwordTemp.value.length &&
        passwordTemp.value !== confirmPasswordTemp.value
      ) {
        setConfirmPassword(TEXT.PASSWORD_NOT_MATCHING);
      } else {
        setConfirmPassword('');
      }
    }, 500);
  };

  const renderUpdatePassword = () =>
    !isHideOtpUpdatePassword ? (
      <PopupConfirmOtpUpdatePassword
        onClosePopupProfile={onClosePopupProfile}
        setHideOtpUpdatePassword={setHideOtpUpdatePassword}
      />
    ) : (
      <form
        data-abide="true"
        className="form form-default form-for-light form--member form--member-modal relative"
      >
        <InputCustom
          className="input-for-dark"
          label={TEXT.CURRENT_PASS}
          id="oldPassword"
          type="password"
          error={oldPassword}
          onKeyUp={validateLengthOldPassword}
          valueOutput={oldPasswordTemp}
          placeholder={TEXT.MIN_6_CHARACTERS}
          icon="vie vie-key-skeleton-o"
          isErrClassAbsolute
        />

        <InputCustom
          className="input-for-dark"
          label={TEXT.NEW_PASS}
          id="password"
          type="password"
          error={password}
          valueOutput={passwordTemp}
          icon="vie vie-key-skeleton-o"
          onKeyUp={validateLengthPassword}
          placeholder={TEXT.MIN_6_CHARACTERS}
          isErrClassAbsolute
        />
        <InputCustom
          className="input-for-dark"
          label={TEXT.RETYPE_NEW_PASS}
          id="confirmPassword"
          type="password"
          error={confirmPassword}
          valueOutput={confirmPasswordTemp}
          onEnter={onUpdatePassword}
          icon="vie vie-key-skeleton-o"
          onKeyUp={validateLengthConfirmPassword}
          placeholder={TEXT.MIN_6_CHARACTERS}
          isErrClassAbsolute
        />
        {error && (
          <label className="form-error is-visible absolute bottom-3 midd text-center m-b4 p-b2">
            {error}
          </label>
        )}

        <div className="button-group child-auto m-t2">
          <Button
            className="button button--light button--large"
            title={TEXT.SAVE}
            onClick={(event: any) => {
              onUpdatePassword(event);
            }}
            disabled={
              !!(
                oldPasswordTemp.value === '' ||
                passwordTemp.value === '' ||
                confirmPasswordTemp.value === '' ||
                passwordTemp.value !== confirmPasswordTemp.value ||
                oldPassword ||
                password ||
                confirmPassword
              )
            }
          />
        </div>
      </form>
    );

  // data form input
  const [gender, setGender] = useState(dataProfile?.gender);
  const onUpdateGender = (e: any) => {
    e.preventDefault();
    if (isOnLoyalty) {
      TrackingLoyalty.trackingLoyaltyUpdateGender();
    }
    updateGender(gender).then(() => {
      closePopupProfile();
      dispatch(setToast({ message: 'Cập nhật thông tin giới tính thành công' }));
    });
  };

  const renderUpdateGender = () => (
    <form
      data-abide="true"
      className="form form-default form-for-light form--member form--member-modal form--member-sign max-md:mx-6"
      autoComplete="off"
      id="PROFILE_FORM_ID"
    >
      <div className="radio-group radio-group-flex align-center flex-box m-b3 gap-4 max-md:flex-col">
        {genderList.map((item: any, index: number) => (
          <div className="radio radio-custom" key={index}>
            <input
              defaultChecked={dataProfile?.gender === item.id}
              type="radio"
              id={item.id}
              name="MULTI_RADIO_BTN"
              onClick={() => {
                setGender(item.id);
              }}
            />
            <label className="text-white" htmlFor={item.id}>
              {item.name}
            </label>
          </div>
        ))}
      </div>
      <CustomButtonGroup
        classNameLeft="button hollow button--dark-glass button--large"
        classNameRight="button button--light button--large"
        onClick={(event: any) => {
          onUpdateGender(event);
        }}
        isDisabled={gender === dataProfile.gender}
        onClose={onClosePopupProfile}
      />
    </form>
  );

  const renderUpdateDob = () => (
    <PopupUpdateDateOfBirth
      onClosePopupProfile={onClosePopupProfile}
      onUpdateDateOfBirth={updateDob}
      profile={dataProfile}
    />
  );

  // Update Invoice Info
  const invoiceInfoUpdateTemp = useFormInput(
    dataProfile?.invoiceInfo[`${INVOICE_INPUT_TYPE?.[actionName]}`] || ''
  );
  const [invoiceUpdateError, setInvoiceUpdateError] = useState<any>('');

  const onUpdateInputInvoiceInfo = (e: any) => {
    e.preventDefault();
    const invoiceInfoUpdate = invoiceInfoUpdateTemp.value;
    if (
      invoiceInfoUpdate === dataProfile?.invoiceInfo[`${INVOICE_INPUT_TYPE?.[actionName]}`] ||
      invoiceInfoUpdate === ''
    ) {
      return;
    }

    if (actionName === ID.UPDATE_INVOICE_EMAIL) {
      if (invoiceInfoUpdate === '' || !validateEmail(invoiceInfoUpdate)) {
        setInvoiceUpdateError(TEXT.EMAIL_WRONG);
        return;
      }
    }

    updateInvoiceInfo({
      [`${INVOICE_INPUT_TYPE?.[actionName]}`]: invoiceInfoUpdate,
      deviceModel,
      deviceName,
      deviceType
    }).then((resp: any) => {
      if (resp?.code === ERROR_CODE.CODE_0) {
        dispatch(setToast({ message: TEXT.ACCOUNT_INVOICE.UPDATE_SUCCESS?.[actionName] }));
        closePopupProfile();
      } else if (resp?.message) {
        setInvoiceUpdateError(resp?.message);
      } else {
        dispatch(setToast({ message: TEXT.MSG_ERROR }));
      }
    });
  };

  const renderUpdateInvoiceInfo = () => (
    <form
      className="form form-default form-for-light form--member form--member-modal form--member-sign"
      data-abide="true"
      noValidate
    >
      <InputCustom
        label={TEXT.ACCOUNT_INVOICE[actionName.replace('UPDATE_', '')]}
        id={`input_${INVOICE_INPUT_TYPE[actionName]}`}
        type="text"
        onEnter={onUpdateInputInvoiceInfo}
        error={invoiceUpdateError}
        valueOutput={invoiceInfoUpdateTemp}
        className="input-for-dark"
      />
      <CustomButtonGroup
        classNameLeft="button hollow button--dark-glass button--large"
        classNameRight="button button--light button--large"
        onClick={(event: any) => {
          onUpdateInputInvoiceInfo(event);
        }}
        isDisabled={
          !!(
            invoiceInfoUpdateTemp.value ===
              dataProfile?.invoiceInfo[`${INVOICE_INPUT_TYPE?.[actionName]}`] ||
            invoiceInfoUpdateTemp.value === ''
          )
        }
        onClose={onClosePopupProfile}
      />
    </form>
  );

  const renderPopup = () => {
    switch (actionName) {
      case ID.UPDATE_NAME:
        return renderUpdateGivenName();
      case ID.UPDATE_EMAIL:
        return renderUpdateEmail();
      case ID.UPDATE_PHONE:
        return renderUpdateMobile();
      case ID.UPDATE_PASSWORD:
        return renderUpdatePassword();
      case ID.UPDATE_GENDER:
        return renderUpdateGender();
      case ID.UPDATE_DATE_OF_BIRTH:
        return renderUpdateDob();
      case ID.UPDATE_COMPANY_NAME:
      case ID.UPDATE_COMPANY_TIN:
      case ID.UPDATE_COMPANY_ADDRESS:
      case ID.UPDATE_INVOICE_EMAIL:
        return renderUpdateInvoiceInfo();
      default:
        return null;
    }
  };

  let footerData: any = {
    className: 'button-group child-auto p-y4',
    leftButton: {
      className: 'button hollow button--dark-glass button--large',
      name: TEXT.SKIP,
      onClick: closePopupProfile
    },
    rightButton: {
      className: 'button button--light button--large',
      name: TEXT.ACCEPT,
      onClick: () => onDeleteHistory(actionName)
    }
  };

  if (actionName !== ID.CLEAR_SEARCH && actionName !== ID.CLEAR_HISTORY) {
    footerData = null;
  }

  const getPopupTitle = () => {
    const emailVerified = dataProfile?.emailVerified;
    const email = dataProfile?.email;
    switch (actionName) {
      case ID.UPDATE_NAME:
        return TEXT.USER_ACTION.UPDATE_NAME;
      case ID.UPDATE_EMAIL: {
        let title = TEXT.USER_ACTION.UPDATE_EMAIL;
        if (email && !emailVerified) title = TEXT.USER_ACTION.CONFIRM_EMAIL;
        return title;
      }
      case ID.UPDATE_PHONE:
        return TEXT.USER_ACTION.UPDATE_PHONE_NUMBER;
      case ID.CONFIRM_PHONE:
        return TEXT.CONFIRM_PHONE;
      case ID.CONFIRM_PHONE_PASSWORD:
        return TEXT.CONFIRM_PHONE_PASSWORD;
      case ID.UPDATE_PASSWORD:
        return TEXT.USER_ACTION.UPDATE_PASSWORD;
      case ID.UPDATE_GENDER:
        return TEXT.USER_ACTION.UPDATE_GENDER;
      case ID.CLEAR_HISTORY:
        return TEXT.CLEAR_HISTORY;
      case ID.CLEAR_SEARCH:
        return TEXT.CLEAR_SEARCH;
      case ID.UPDATE_DATE_OF_BIRTH:
        return TEXT.USER_ACTION.UPDATE_DATE_OF_BIRTH;
      case ID.UPDATE_COMPANY_NAME:
        return TEXT.ACCOUNT_INVOICE.ACTION_NAME.UPDATE_COMPANY_NAME;
      case ID.UPDATE_COMPANY_TIN:
        return TEXT.ACCOUNT_INVOICE.ACTION_NAME.UPDATE_COMPANY_TIN;
      case ID.UPDATE_COMPANY_ADDRESS:
        return TEXT.ACCOUNT_INVOICE.ACTION_NAME.UPDATE_COMPANY_ADDRESS;
      case ID.UPDATE_INVOICE_EMAIL:
        return TEXT.ACCOUNT_INVOICE.ACTION_NAME.UPDATE_INVOICE_EMAIL;
      default:
        break;
    }
    return '';
  };

  const modalTitle = getPopupTitle();
  const subTitle = `${TEXT.OTP_REQUIRED} ${phoneNumber}`;
  const classCustom = 'text-white m-t2 m-b size-mw-280 m-x-auto';

  return (
    <Modal
      className="green-line"
      title={modalTitle}
      renderBody={renderPopup}
      footerData={footerData}
      onClosed={onClosePopupProfile}
      subTitle={!isHideOtpUpdatePassword && actionName === ID.UPDATE_PASSWORD ? subTitle : false}
      classCustom={
        !isHideOtpUpdatePassword && actionName === ID.UPDATE_PASSWORD ? classCustom : false
      }
      notClosedButton={actionName !== ID.UPDATE_PASSWORD}
    />
  );
};

export default PopupUpdateProfile;
