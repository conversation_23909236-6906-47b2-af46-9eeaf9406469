import React from 'react';
import { isEmpty } from 'lodash';
import { isMobile } from 'react-device-detect';
import { useSelector } from 'react-redux';
import { TEXT } from '@constants/text';
import Button from '../basic/Buttons/Button';
import Image from '../basic/Image/Image';
import { getAttrLink } from '@helpers/common';
import { useVieRouter } from '@customHook';

const PromoteVoting = ({ data }: any) => {
  const { items } = data || {};
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const router = useVieRouter();

  // const { currentProfile } = useSelector((state: any) => state?.MultiProfile || {});

  const handleVoteClick = () => {
    let externalUrl = items?.[0]?.externalUrl;
    const type = items?.[0]?.type;
    const seo = items?.[0]?.seo;
    if (externalUrl) {
      externalUrl = externalUrl.match(/^https?:/) ? externalUrl : `//${externalUrl}`;
      // interceptor click to voting
      if (externalUrl.includes('{accesstoken}') && profile?.token) {
        const newLink = externalUrl.replace('{accesstoken}', profile?.token);
        window.open(newLink, '_blank');
      } else {
        window.open(externalUrl, '_blank');
      }
    } else {
      // handle VOD redirect
      const attrLink = getAttrLink({ as: '', href: '', type });
      router.push(attrLink.href, seo?.url);
    }
  };
  return (
    <div className="flex flex-col 2xl:flex-row space-y-[8px] md:space-y-[16px] lg:space-x-0 justify-start lg:space-y-[16px] lg:pr-[60px] 2xl:space-x-[16px] 2xl:pr-0 md:pr-[32px]">
      <div className="w-full lg:w-full lg:min-w-[434px] 2xl:min-w-[440px]">
        <Image
          src={isMobile ? items?.[0]?.images?.thumbnail : items?.[0]?.images?.thumbnailBig}
          alt={items?.[0]?.altSEOImg}
          className="min-w-[343px] 2xl:min-w-[440px] w-full min-h-[213px] object-contain md:w-full aspect-[640/400] md:h-full"
        />
      </div>
      <div className="flex flex-col justify-center text-white space-y-[8px] md:space-y-[16px] md:pr-[40px] md:min-w-[550px] lg:justify-start lg:min-w-[434px] 4xl:min-w-[626px]">
        <div>
          {!isEmpty(profile) && (
            <div className="!text-[20px] font-[700] leading-[24px] md:!text-[28px] md:leading-[39.2px] lg:!text-[32px] lg:leading-[38.4px]">
              Wazzup,{' '}
              <span className="text-[#0AD418]">
                {`${profile?.givenName || profile?.mobile || profile?.email}!`}
              </span>
            </div>
          )}
          <div className="!text-[24px] font-[700] leading-[28.8px] tracking-[-0.2%] 4xl:!text-[40px] 4xl:leading-[48px] lg:!text-[32px] lg:leading-[38.4px]">
            {items?.[0]?.title}
          </div>
        </div>

        <p className="!text-[14px] leading-[21px] font-[400] text-[#9B9B9B]">
          {items?.[0]?.shortDescription}
        </p>
        <Button
          onClick={handleVoteClick}
          className="bg-[#2FB138] rounded-full !text-[16px] md:!text-[18px] px-[18px] leading-[24px] font-[500] py-[8px] md:px-[30px] md:py-[10px] space-x-[8px] w-[200px] md:w-[220px] !mt-[16px] md:!mt-[32px] !mb-[32px] md:h-[48px]"
          title={TEXT.VOTING_NOW}
          imgSrcRight="/arrow-icon.svg"
        />
      </div>
    </div>
  );
};
export default PromoteVoting;
