import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getDataRibbonsId } from '@actions/page';
import CustomTab from '../basic/CustomTab/CustomTab';
import ListContents from './ListContents';

const TabRibbon = ({ data, idRibbon, isRapVietRanking }: any) => {
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState(data?.items?.[0] || null);
  const ribbonData = useSelector((state: any) => state?.Page?.ribbonData);
  const tabData: any = Object.values(ribbonData).find((item: any) => item.id === activeTab.id);
  const tabs =
    data?.items?.map((item: any) => ({
      name: item.title,
      active: item.id === activeTab?.id,
      id: item.id
    })) || [];

  useEffect(() => {
    if (activeTab?.id) {
      dispatch(getDataRibbonsId({ id: activeTab.id }));
    }
  }, [activeTab, dispatch]);

  const handleTabClick = (item: any) => {
    setActiveTab(item);
  };

  return (
    <div className="text-white" key={idRibbon}>
      {/* <div className="overflow-x-auto max-w-full no-scrollbar min-h-max"> */}
      <CustomTab
        data={tabs}
        className="tabs tabs--profile md:!whitespace-nowrap md:!overflow-x-auto md:no-scrollbar md:max-w-full md:inline-flex md:flex-nowrap w-full"
        // className="whitespace-nowrap inline-flex flex-nowrap"
        onClickTab={handleTabClick}
      />
      {/* </div> */}
      <div className="tab-content py-4">
        {tabData && (
          <ListContents key={activeTab.id} data={tabData} isRapVietRanking={isRapVietRanking} />
        )}
      </div>
    </div>
  );
};

export default TabRibbon;
