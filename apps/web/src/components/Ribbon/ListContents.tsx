import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import classNames from 'classnames';
import SwiperCore, { Lazy } from 'swiper';
import { Swiper, SwiperSlide } from 'swiper/react';
import { NextArrow, PrevArrow } from '@components/basic/Buttons/ButtonArrow';
import BannerRegistration from '@components/home/<USER>';
import Card from '@components/basic/Card/Card';
import { LIMIT_ITEMS_RIBBON_FOR_SHOW, RIBBON_TYPE } from '@constants/constants';
import isEmpty from 'lodash/isEmpty';

SwiperCore.use([Lazy]);

const ListContents = ({ data, onLoadMore, isRankingBoard, notLazy, isRapVietRanking }: any) => {
  const { id, items, metadata } = data || {};
  const { page, total, limit } = metadata || {};

  const notLoopSlider = useMemo(() => {
    if (
      data &&
      (data.type === RIBBON_TYPE.FAVORITE_VOD ||
        data.type === RIBBON_TYPE.WATCH_MORE ||
        data.type === RIBBON_TYPE.TOP_VIEWS)
    ) {
      return true;
    }
    return false;
  }, [data?.type]);
  const nextTimerRef = useRef<any>(null);
  const ref = useRef([]);
  const { registrationTrigger, isMobile, isTablet } = useSelector((state: any) => state?.App || {});
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const autoSlideView = id === registrationTrigger?.idCheck && registrationTrigger?.active;
  const [listItems, setListItems] = useState(items || []);
  const [swiper, setSwiper] = useState<any>({});
  const [nextEnable, setNextEnable] = useState(false);
  const [prevEnable, setPrevEnable] = useState(false);
  const [slidePerView, setSlidePerView] = useState(6);
  const totalItems = total > LIMIT_ITEMS_RIBBON_FOR_SHOW ? LIMIT_ITEMS_RIBBON_FOR_SHOW : total;
  const [waiting, setWaiting] = useState(false);

  useEffect(() => {
    setListItems(items || []);
    if (isMobile) {
      setSlidePerView(2);
    } else if (isTablet || isRankingBoard || isRapVietRanking) {
      setSlidePerView(4);
    }
  }, [items.length, isRankingBoard, isRapVietRanking]);

  useEffect(() => {
    if (
      !!registrationTrigger?.active &&
      !profile?.id &&
      items?.length > 1 &&
      id === registrationTrigger?.idCheck
    ) {
      const dataItems = {
        id: registrationTrigger?.idCheck,
        image: isMobile ? registrationTrigger?.imageMobile : registrationTrigger?.image,
        title: registrationTrigger?.title,
        isTriggerBanner: true
      };
      const listExistTrigger = items.filter((item: any) => item?.isTriggerBanner);
      if (!listExistTrigger?.length) {
        const listItemsTemp = [...items];
        if (items?.length >= 8) {
          listItemsTemp.splice(8, 0, dataItems);
        } else if (items?.length >= 3 && items?.length <= 5) {
          if (isMobile) listItemsTemp.splice(2, 0, dataItems);
          else listItemsTemp.splice(3, 0, dataItems);
        } else listItemsTemp.push(dataItems);
        setListItems(listItemsTemp);
      }
    }
  }, [items.length, id, profile?.id]);

  useEffect(() => {
    setNextEnable(!swiper?.isEnd);
    setPrevEnable(!(!swiper?.activeIndex || swiper?.isBeginning));
  }, [swiper?.activeIndex, swiper?.isEnd, swiper?.isBeginning]);

  const handleSlideChange = () => {
    setPrevEnable(!!swiper?.activeIndex);
    setNextEnable(swiper?.activeIndex + slidePerView < totalItems);
    if (
      swiper?.activeIndex + slidePerView < totalItems &&
      (page + 1) * limit >= swiper?.activeIndex + slidePerView &&
      (page + 1) * limit < swiper?.activeIndex + slidePerView * 2
    ) {
      setWaiting(true);
      handleLoadMoreItems();
    }
  };

  const onNext = () => {
    clearTimeout(nextTimerRef.current);
    nextTimerRef.current = setTimeout(() => {
      if (!isEmpty(swiper) && listItems.length > 1) {
        if (swiper.isEnd && listItems.length > 6) {
          if (!waiting) {
            swiper.slideTo(0, 300);
          }
        } else {
          swiper.slideNext();
        }
      }
    }, 300);
  };

  const onPrev = () => {
    swiper?.slidePrev();
  };

  const handleLoadMoreItems = async () => {
    if ((page + 1) * limit >= totalItems || typeof onLoadMore !== 'function') return;
    Promise.race([await onLoadMore({ page: page + 1, limit, isLoadmore: true })]).then((res) => {
      const { loading, status } = res;
      if (status === 'success' && !loading) {
        setWaiting(loading);
      }
    });
  };

  const renderSliderItem = (item: any, index: any) => (
    <Card
      cardData={item}
      ref={ref}
      index={index + 1}
      randomID={item?.randomID || `${item?.id}_${index}`}
      notLazy={notLazy}
    />
  );

  return (
    <Swiper
      className="slider relative !group"
      onSlideChange={handleSlideChange}
      allowTouchMove={!!isMobile}
      spaceBetween={8}
      slidesPerView={autoSlideView ? 'auto' : slidePerView}
      slidesPerGroup={slidePerView}
      onSwiper={(swiper) => setSwiper(swiper)}
      data-item-view={slidePerView}
      id={id}
      speed={300}
      lazy={{
        //  tell swiper to load images before they appear
        loadPrevNext: true,
        // amount of images to load
        loadPrevNextAmount: 2
      }}
    >
      {listItems.map((item: any, index: any) => {
        if (item?.isTriggerBanner && !!registrationTrigger?.active && !profile?.id) {
          return (
            <SwiperSlide
              key={`${item?.id}-${index}`}
              className="slider__item"
              style={{ width: isMobile && !isTablet ? '100%' : '50%' }}
            >
              <BannerRegistration {...item} />
            </SwiperSlide>
          );
        }
        return (
          <SwiperSlide key={`${item?.id}-${index}`} className="slider__item">
            {renderSliderItem(item, index)}
            <div className="swiper-lazy-preloader" />
          </SwiperSlide>
        );
      })}
      {!isMobile && (
        <NextArrow
          onClickNext={onNext}
          nextClass={classNames(
            'w-4 xl:w-[3.02083vw] !absolute !top-1/2 -right-4 xl:!-right-[3.02083vw] -translate-y-1/2 z-[50]',
            {
              'swiper-button-disabled':
                (!nextEnable && notLoopSlider) ||
                listItems?.length <= (isRapVietRanking || isRankingBoard ? 4 : 6)
            }
          )}
        />
      )}
      {!isMobile && (
        <PrevArrow
          onClickPrev={onPrev}
          prevClass={classNames(
            'w-4 xl:w-[3.02083vw] !absolute !top-1/2 -translate-y-1/2 z-[50]',
            isRapVietRanking ? '!left-0' : '-left-4 xl:-left-[3.02083vw]',
            {
              '!invisible': !prevEnable,
              inside: isRankingBoard
            }
          )}
        />
      )}
    </Swiper>
  );
};

export default ListContents;
