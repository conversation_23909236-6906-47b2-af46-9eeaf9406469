import React, { CSSProperties } from 'react';
import { RIBBON_TYPE } from '@constants/constants';
import { useSelector } from 'react-redux';

const Loading = ({ type, quantity = 6 }: any) => {
  const { isMobile, isTablet } = useSelector((state: any) => state?.App || {});
  let numberOfItems = quantity || 6;
  if (isMobile) numberOfItems = 2;
  if (isTablet) numberOfItems = 4;
  let ratioClass = ' ratio-16-9';
  if (type === RIBBON_TYPE.ORIGINAL) ratioClass = ' ratio-1-2';
  const dataArray = new Array(numberOfItems).fill(1);

  return (
    <div className="rocopa-raw">
      <div className="rocopa-raw__container">
        <div
          className={`slider-raw col-x-small-2 col-small-3 col-medium-3 col-large-${numberOfItems}`}
        >
          {(dataArray || []).map((_, index) => (
            <div
              key={index}
              className="slider-raw__item"
              style={
                isMobile
                  ? ({
                      '--col-large-6': '50.00001%',
                      width: 'calc(var(--col-large-6) - .25rem)'
                    } as CSSProperties)
                  : {}
              }
            >
              <div className={ratioClass} />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Loading;
