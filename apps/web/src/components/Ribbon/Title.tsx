import React, { useEffect, useState } from 'react';
import { PAGE } from '@constants/constants';
import { useVieRouter } from '@customHook';
import ButtonViewAll from '@components/basic/Buttons/ButtonViewAll';

const Title = ({ title, isViewAll, directURL, redirectUrl, as, onSelect }: any) => {
  const router = useVieRouter();
  const [isClient, setClient] = useState(false);
  const newTitle = (title || '').replace('"{movie_name}"', '') || title;

  useEffect(() => {
    setClient(true);
  }, []);

  const handleViewAll = () => {
    router.push(directURL || PAGE.COLLECTION, directURL ? `${directURL}/` : redirectUrl || as);
    if (typeof onSelect === 'function') onSelect();
  };

  const handleClick = (e: any) => {
    e.preventDefault();
    const href = directURL || PAGE.COLLECTION;
    const url = directURL ? `${directURL}/` : redirectUrl || as;
    if (url && href) {
      router.push(href, url);
    }
    if (typeof onSelect === 'function') onSelect(e);
  };

  if (isViewAll) {
    return (
      <>
        {isClient && (
          <ButtonViewAll
            title="Xem tất cả"
            iconName="vie-chevron-right-r-medium"
            onClick={handleViewAll}
          />
        )}
        <h2 className="rocopa__title">
          <a
            href={directURL ? `${directURL}/` : redirectUrl || as}
            className="title--link"
            onClick={handleClick}
            tabIndex={-1}
          >
            {newTitle}
          </a>
        </h2>
      </>
    );
  }
  return <h2 className="rocopa__title">{newTitle}</h2>;
};
export default Title;
