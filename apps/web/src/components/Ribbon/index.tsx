import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import classnames from 'classnames';
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import { useVieRouter } from '@customHook';
import RapVietResultVoting from '@components/rap-viet-result-voting';
import BannerUserPackageInfo from '@components/home/<USER>';
import PromotedRibbonAds from '@components/OutstreamAds/PromotedRibbonAds';
import { EL_ID, LIMIT_ITEMS_RIBBON_CALL_API, PAGE, RIBBON_TYPE } from '@constants/constants';
import TrackingApp from '@tracking/functions/TrackingApp';
import dynamic from 'next/dynamic';
import RibbonBody from './Body';
import Title from './Title';
import styles from './Styles.module.scss';
import { clearRibbonData, getDataRibbonsId } from '@actions/page';

const BannerRibbonAds = dynamic(import('@components/OutstreamAds/BannerRibbonAds'), {
  ssr: false
});

const Wrapper = ({ data = {}, order, isRankingBoard, isRapVietRanking }: any) => {
  const { id, type } = data;
  const router = useVieRouter();
  const dispatch = useDispatch();
  const { pathname } = router || {};
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { isMobile, deviceId, outStreamAds, webConfig } = useSelector(
    (state: any) => state?.App || {}
  );
  const { bannerRibbonAds, promotedRibbon } = outStreamAds || {};
  const { featureFlag, mwebToApp } = webConfig || {};
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const { USER_PACKAGE_INFO, dataTvod } = useSelector((state: any) => state?.User || {});
  const subscriptions = USER_PACKAGE_INFO?.subscriptions;
  const { ribbonData } = useSelector((state: any) => state?.Page || {});
  const { listContentsWatchMore, listContentsWatchLater } = useSelector(
    (state: any) => state?.Detail || {}
  );
  const [isEmptyData, setIsEmptyData] = useState(false);
  const isPreventClick =
    type === RIBBON_TYPE.FAVORITE_VOD ||
    type === RIBBON_TYPE.WATCH_MORE ||
    (type === RIBBON_TYPE.TVOD_RENTING && profile?.hadTvod);
  let idRibbon = id;
  let finalData: any = {};
  if (type === RIBBON_TYPE.FAVORITE_VOD) {
    idRibbon = EL_ID.FAVORITE_VOD;
    finalData = { ...data, ...listContentsWatchLater };
  } else if (type === RIBBON_TYPE.WATCH_MORE) {
    idRibbon = EL_ID.WATCHING_RIBBON;
    finalData = { ...data, ...listContentsWatchMore };
  } else if (type === RIBBON_TYPE.TVOD_RENTING && profile?.hadTvod) {
    idRibbon = EL_ID.TVOD_RENTING_RIBBON;
    finalData = { ...data, ...dataTvod };
  } else {
    finalData = { ...data, ...get(ribbonData, data?.seo?.url, {}) };
  }
  const { loadedData, items, name, directURL, isViewAll, seo } = finalData;
  const isNotLoad = useMemo(
    () =>
      ((type === RIBBON_TYPE.BANNER_RIBBON_ADS || type === RIBBON_TYPE.PROMOTED_RIBBON_ADS) &&
        (isMobile || profile?.isPremium || isKid)) ||
      (!profile?.id && type === RIBBON_TYPE.FAVORITE_VOD) ||
      (type === RIBBON_TYPE.CONVERT_MOBILE_WEB_TO_APP && (!isMobile || !featureFlag?.mwebToApp)) ||
      (type === RIBBON_TYPE.TVOD_RENTING && !profile?.hadTvod) ||
      (type === RIBBON_TYPE.WATCH_MORE && pathname !== PAGE.HOME),
    [
      isMobile,
      type,
      profile?.id,
      profile?.isPremium,
      profile?.hadTvod,
      featureFlag?.mwebToApp,
      pathname,
      isKid
    ]
  );
  const isHideTitle =
    type === RIBBON_TYPE.PROMOTION_RIBBON ||
    type === RIBBON_TYPE.PROMOTION_BANNER ||
    type === RIBBON_TYPE.BANNER_RIBBON_ADS ||
    type === RIBBON_TYPE.PROMOTED_RIBBON_ADS ||
    type === RIBBON_TYPE.PROMOTE_VOTING;
  const classRibbon = classnames('rocopa', {
    'rocopa--original': isRapVietRanking,
    'rocopa--minh-2':
      type === RIBBON_TYPE.PROMOTION_RIBBON && (pathname === PAGE.SPORT || isRapVietRanking),
    'rocopa--minh-1': type === RIBBON_TYPE.PROMOTION_RIBBON && pathname !== PAGE.SPORT
  });

  const onSelect = async () => {
    TrackingApp.ribbonSelected({ data: finalData, ribbonOrder: order, title: name });
  };

  const onLoadedData = (status: any) => {
    setIsEmptyData(!status);
  };
  useEffect(() => {
    if (type === RIBBON_TYPE.RAP_RANKING_BOARD || type === RIBBON_TYPE.EXSH_BOARD) {
      dispatch(clearRibbonData());

      const timeout = setTimeout(() => {
        dispatch(
          getDataRibbonsId({
            id,
            page: 0,
            limit: LIMIT_ITEMS_RIBBON_CALL_API,
            ribbonSlug: seo?.url,
            ribbonOrder: order,
            isMWebToApp: featureFlag?.mwebToApp,
            imageMWebToApp: isMobile
              ? mwebToApp?.onlyAppImageMWeb || mwebToApp?.onlyAppImage
              : mwebToApp?.onlyAppImage,
            isMobile,
            isLoadmore: false,
            isGlobal
          })
        );
      }, 50);

      return () => clearTimeout(timeout);
    }

    return undefined;
  }, [type, id, seo?.url, order, featureFlag?.mwebToApp, mwebToApp, isMobile, isGlobal]);

  const handleRenderBody = useCallback(() => {
    switch (type) {
      case RIBBON_TYPE.PROMOTED_RIBBON_ADS:
        if (isGlobal) return null;
        return promotedRibbon ? (
          <PromotedRibbonAds
            dataRibbon={finalData}
            data={promotedRibbon}
            onLoadedAds={onLoadedData}
          />
        ) : null;
      case RIBBON_TYPE.BANNER_RIBBON_ADS:
        if (isGlobal) return null;
        return bannerRibbonAds ? (
          <BannerRibbonAds
            dataRibbon={finalData}
            data={bannerRibbonAds}
            onLoadedAds={onLoadedData}
          />
        ) : null;
      default:
        return (
          <>
            <RibbonBody
              data={finalData}
              order={order}
              isRankingBoard={isRankingBoard}
              isRapVietRanking={isRapVietRanking}
            />
          </>
        );
    }
  }, [bannerRibbonAds, finalData, promotedRibbon, isGlobal, isRapVietRanking]);

  if (type === RIBBON_TYPE.RAP_VIET_SS2) return <RapVietResultVoting />;
  if (type === RIBBON_TYPE.RAP_RANKING_BOARD || type === RIBBON_TYPE.EXSH_BOARD) return null;
  if ((loadedData && isEmpty(items)) || isEmptyData || isNotLoad) return null;
  return (
    <>
      {handleRenderBody() !== null && (
        <div
          className={classnames(
            classRibbon,
            !isHideTitle ? 'z-[1]' : 'z-0',
            isRapVietRanking ? '!mb-[56px] md:!mb-[80px]' : ''
          )}
          id={idRibbon}
        >
          {!isHideTitle && (
            <div className="rocopa__header">
              <div className={classnames('rocopa__header-left hovered', styles.titleHover)}>
                {isHideTitle ? null : (
                  <Title
                    title={name}
                    directURL={directURL}
                    isViewAll={isViewAll && !isPreventClick}
                    onSelect={onSelect}
                    redirectUrl={seo?.url_redirect}
                    as={seo?.url}
                  />
                )}
              </div>
            </div>
          )}
          {handleRenderBody()}
        </div>
      )}
      {type === RIBBON_TYPE.WATCH_MORE && deviceId && subscriptions && <BannerUserPackageInfo />}
    </>
  );
};

export default Wrapper;
