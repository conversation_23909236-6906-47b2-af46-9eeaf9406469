import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useInView } from 'react-intersection-observer';
import PromotionBanner from '@components/home/<USER>';
import CardAiring from '@components/basic/Card/CardAiring';
import { getContentWatchlater, getContentWatchmore } from '@actions/detail';
import { getUserTvod } from '@actions/user';
import { getDataRibbonsId } from '@actions/page';
import { LIMIT_ITEMS_RIBBON_CALL_API, RIBBON_TYPE } from '@constants/constants';
import classNames from 'classnames';
import ListContents from './ListContents';
import Loading from './Loading';
import TabRibbon from './TabRibbon';
import PromoteVoting from './PromoteVoting';

const Body = ({ data, order, isRankingBoard, isRapVietRanking }: any) => {
  const { id, type, loadedData, seo } = data || {};
  const dispatch = useDispatch();
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { isMobile, webConfig } = useSelector((state: any) => state?.App || {});
  const { featureFlag, mwebToApp } = webConfig || {};
  const [isLeftBoard, setLeftBoard] = useState(false);

  const [ref, inView] = useInView({
    delay: 100,
    triggerOnce: true,
    threshold: 0.5
  });

  useEffect(() => {
    if (id && !loadedData && inView && type) {
      handleLoadContents({ page: 0 });
    }
  }, [inView, id, loadedData, type]);

  const getListContentWatchLater = ({ page, limit }: any) => {
    if (profile?.id) {
      dispatch(getContentWatchlater({ page, limit, ribbonOrder: order, ribbonId: id, isGlobal }));
    }
  };

  const getListContentWatchMore = ({ page, limit, isGlobal }: any) => {
    dispatch(
      getContentWatchmore({ page, limit, ribbonOrder: order, ribbonId: id, isGlobal }) // remove ribbonName: name
    );
  };

  const getListContentTVod = () => {
    if (profile?.hadTvod) {
      dispatch(getUserTvod({ ribbonId: id, ribbonOrder: order, isGlobal })); // remove ribbonName: name
    }
  };

  const getListContentOfRibbon = ({ page, limit, isLoadmore }: any) => {
    // limit the ribbon logic
    let limitRibBanner;
    if (type === RIBBON_TYPE.MULTI_TABS_RIBBON) {
      limitRibBanner = undefined; // Set to undefined for MULTI_TABS_RIBBON
    } else {
      limitRibBanner =
        limit ||
        (type === RIBBON_TYPE.PROMOTION_BANNER || type === RIBBON_TYPE.PROMOTION_BANNER_FUNC
          ? 8
          : LIMIT_ITEMS_RIBBON_CALL_API);
    }

    return Promise.race([
      dispatch(
        getDataRibbonsId({
          id,
          page: page || 0,
          limit: limitRibBanner,
          ribbonSlug: seo?.url,
          ribbonOrder: order,
          isMWebToApp: featureFlag?.mwebToApp,
          imageMWebToApp: isMobile
            ? mwebToApp?.onlyAppImageMWeb || mwebToApp?.onlyAppImage
            : mwebToApp?.onlyAppImage,
          isMobile,
          isLoadmore,
          isGlobal
        })
      )
    ])
      .then((res) => {
        return {
          loading: false,
          status: 'success',
          result: res
        };
      })
      .catch((err) => {
        return {
          loading: false,
          status: 'error',
          error: err
        };
      });
  };

  useEffect(() => {
    const hasLeftBoardRv = document.getElementById('rv');
    const hasLeftBoardRank = document.getElementById('rankingBoard');
    if (hasLeftBoardRv || hasLeftBoardRank) {
      setLeftBoard(true);
    }
  }, []);

  const handleLoadContents = ({ page, limit, isLoadmore }: any) => {
    switch (type) {
      case RIBBON_TYPE.WATCH_MORE:
        return getListContentWatchMore({ page, limit, isGlobal });
      case RIBBON_TYPE.FAVORITE_VOD:
        return getListContentWatchLater({ page, limit });
      case RIBBON_TYPE.TVOD_RENTING:
        return getListContentTVod();
      default:
        return getListContentOfRibbon({ page, limit, isLoadmore });
    }
  };

  const handleRender = () => {
    if (!loadedData) return <Loading />;

    switch (type) {
      case RIBBON_TYPE.PROMOTION_BANNER_FUNC:
      case RIBBON_TYPE.PROMOTION_BANNER:
        return <PromotionBanner isRankingBoard={isRankingBoard} data={data} />;
      case RIBBON_TYPE.PROMOTION_RIBBON:
        return <CardAiring data={data?.items?.[0]} idRibbon={id} />;
      case RIBBON_TYPE.MULTI_TABS_RIBBON:
        return (
          <TabRibbon
            data={data}
            key={`${id}-${order}`}
            idRibbon={id}
            isRapVietRanking={isRapVietRanking}
          />
        );
      case RIBBON_TYPE.PROMOTE_VOTING:
        return <PromoteVoting data={data} />;
      default:
        return (
          <ListContents
            data={data}
            isRankingBoard={isRankingBoard || isLeftBoard}
            onLoadMore={handleLoadContents}
            notLazy={order === 0}
            isRapVietRanking={isRapVietRanking || isLeftBoard}
          />
        );
    }
  };

  return (
    <div className={classNames('rocopa__body')} ref={ref}>
      {handleRender()}
    </div>
  );
};

export default Body;
