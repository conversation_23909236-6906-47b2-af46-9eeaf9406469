import React, { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { openPopup } from '@actions/popup';
import { useVieRouter } from '@customHook';
import { PAGE, POPUP } from '@constants/constants';
import { replaceKey } from '@helpers/common';
import isEmpty from 'lodash/isEmpty';
import { DRM } from '@constants/player';
import Button from '../basic/Buttons/Button';
import Modal from '../basic/Modal';
import styles from './Modal.module.scss';

const PopupConcurrentScreenAndLimitCUU = ({
  title,
  description,
  image,
  btnPrimary,
  mobileDescription,
  sessionList,
  contentConcurrentGroup,
  popupName,
  drmServiceName
}: any) => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);

  const parseDescription = useMemo(() => {
    let textTypeContent = dataType[contentConcurrentGroup.toUpperCase()];
    if (drmServiceName && popupName === POPUP.NAME.LIMIT_CCU) {
      if (drmServiceName.toUpperCase() === DRM.K_PLUS) textTypeContent = dataType.KPLUS;
      else if (drmServiceName.toUpperCase() === DRM.HBO) textTypeContent = dataType.HBO;
    }
    if (isMobile && mobileDescription) {
      return replaceKey(mobileDescription, 'type_vip', textTypeContent || '');
    }
    return replaceKey(description, 'type_vip', textTypeContent || '');
  }, [mobileDescription, description, contentConcurrentGroup]);

  const onHandleDeviceManager = () => {
    onClosed();
    router.push(PAGE.DEVICE_MANAGEMENT);
  };

  const onClosed = () => {
    dispatch(openPopup());
  };

  const renderContent = () => (
    <div className="block block--for-dark block--notify">
      <div className="title font-bold text-center mx-8">{title}</div>
      {parseDescription && (
        <div
          className="text-[#cccccc] text-base font-normal text-center !mb-3"
          dangerouslySetInnerHTML={{
            __html: parseDescription
          }}
        />
      )}
      {!isEmpty(sessionList) &&
        sessionList.slice(0, 2)?.map((item: any, index: any) => (
          <div className={styles.concurrentScreen} key={index}>
            <div className={styles.concurrentScreenPoint}>•</div>
            <div className={styles.concurrentScreenText}>{item?.device_name}</div>
          </div>
        ))}
    </div>
  );

  const renderCustom = () => {
    if (!image) return null;
    return (
      <div className="mask mask--dark-backdrop m-b-neg-1">
        <div className="mask__inner ratio-variant-1d8">
          <div className="mask__img absolute">
            <img src={image} alt="popup-notification" />
          </div>
        </div>
      </div>
    );
  };

  const renderFooter = () => (
    <div className="button-group child-auto p-b4">
      <Button
        className="button button--light button--large m-b"
        onClick={onHandleDeviceManager}
        title={btnPrimary}
      />
    </div>
  );

  return (
    <Modal
      className="modal--notify"
      renderCustom={renderCustom}
      renderBody={renderContent}
      renderFooter={!isKid && renderFooter}
      onClosed={onClosed}
    />
  );
};

const dataType: any = {
  VCAB: 'VTVCab',
  HBO: 'HBO GO',
  KPLUS: 'K+'
};
export default React.memo(PopupConcurrentScreenAndLimitCUU);
