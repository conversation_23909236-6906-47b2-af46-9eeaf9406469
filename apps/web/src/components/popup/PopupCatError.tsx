import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { openPopup } from '@actions/popup';
import { POPUP } from '@constants/constants';
import TrackingPlayer from '@tracking/functions/TrackingPlayer';
import { pingGoogle } from '@functions/functions';
import { TEXT } from '@constants/text';
import modalStyleModule from './Modal.module.scss';
import Modal from '../basic/Modal';

const NETWORK_STATUS = {
  OFFLINE: -1,
  CHECKING: 0,
  ONLINE: 1
};

const PopupCatError = ({
  popupName,
  image,
  title,
  subTitle,
  description,
  mobileDescription,
  btnRetry,
  notHasCloseBtn,
  retryAction,
  segmentParams,
  btnClose
}: any) => {
  const dispatch = useDispatch();
  const { ggTimeout } = useSelector((state: any) => state?.App?.webConfig) || {};
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const [networkStatus, setNetworkStatus] = useState(NETWORK_STATUS.CHECKING);

  // Check network when show error player popup
  useEffect(() => {
    pingGoogle(ggTimeout).then((res) => {
      setNetworkStatus(res ? NETWORK_STATUS.ONLINE : NETWORK_STATUS.OFFLINE);
    });
  }, [popupName]);

  // hide old PLAYER_ERROR_NETWORK
  // useEffect(() => {
  //   if (networkStatus === NETWORK_STATUS.OFFLINE) {
  //     dispatch(
  //       openPopup({
  //         name: POPUP.NAME.PLAYER_ERROR_NETWORK,
  //         imageDefault: ConfigImage.networkCat,
  //         retryAction: () => window.location.reload()
  //       })
  //     );
  //   } else if (networkStatus === NETWORK_STATUS.ONLINE) {
  //     // Tracking Sentry Cat Error
  //     sentryCatError(errorData);
  //   }
  // }, [networkStatus]);

  const onRetry = () => {
    dispatch(openPopup());
    if (typeof retryAction === 'function') {
      retryAction();
      if (segmentParams) TrackingPlayer.playerErrorRetry(segmentParams);
    }
  };

  const closePopup = () => {
    dispatch(openPopup({ backToPlay: true }));
  };

  const renderCustom = () => {
    if (!image) return null;
    return (
      <div className="mask mask--dark-backdrop m-b-neg-1">
        <div className="mask__inner ratio-variant-1d8">
          <div className="mask__img absolute">
            <img src={image} alt="popup-notification" />
          </div>
        </div>
      </div>
    );
  };

  const renderBody = () => (
    <div className="block block--for-dark block--notify">
      <h2 className="title text-center f-medium">
        {title || (popupName === POPUP.NAME.PLAYER_ERROR_NETWORK ? TEXT.NETWORK_ERROR : '')}
      </h2>
      {subTitle && <h4 className="text text-center text-white">{subTitle}</h4>}
      {description && (
        <p
          className="text text-center margin-small-up-bottom-12 margin-large-up-bottom-32 text-large-up-16 text-14 text-muted"
          dangerouslySetInnerHTML={{
            __html: isMobile ? mobileDescription || description : description
          }}
        />
      )}
      <div className="button-group child-auto">
        {(btnRetry || networkStatus === NETWORK_STATUS.OFFLINE) && (
          <button
            className="button button--light medium button--large"
            type="button"
            onClick={onRetry}
          >
            <span className="text text-center">{btnRetry || TEXT.RETRY}</span>
          </button>
        )}

        {btnClose && (
          <button
            className={`${modalStyleModule['gray-button']} button hollow button--dark-glass medium button--large`}
            type="button"
            onClick={closePopup}
          >
            <span className="text text-center">{btnClose}</span>
          </button>
        )}
      </div>
    </div>
  );

  return (
    <Modal
      className="modal--notify modal--notify-login"
      renderCustom={renderCustom}
      renderBody={renderBody}
      notClosedButton={networkStatus === NETWORK_STATUS.OFFLINE || notHasCloseBtn}
      onClosed={closePopup}
      modalMobile={isMobile}
      classModal={isMobile ? 'p-l3 p-r3' : ''}
    />
  );
};

export default PopupCatError;
