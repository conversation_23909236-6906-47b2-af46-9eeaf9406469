import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { openPopup } from '@actions/popup';
import { TEXT } from '@constants/text';
import { setReferralValue, resetValueReferralCode } from '@actions/payment';
import PaymentApi from '@apis/Payment';
import InputLabelSlideUp from '@components/basic/Input/InputLabelSlideUp';
import { EL_ID } from '@constants/constants';
import Button from '../basic/Buttons/Button';
import Modal from '../basic/Modal';

const PopupReferralCode = ({ valueReferral }: any) => {
  const dispatch = useDispatch();
  const { valueReferralCode } = useSelector((state: any) => state?.Payment) || {};
  const [checkValid, setCheckValid] = useState(false);
  const [inputError, setInputError] = useState<any>('');
  const [valueCode, setValueCode] = useState(valueReferral || '');
  const [disabledButton, setDisabledButton] = useState(true);

  useEffect(() => {
    if (valueReferral || !valueCode) {
      setDisabledButton(true);
    }
  }, []);
  const onChange = (e: any) => {
    const value = ((e?.target?.value || '').trim() || '').toUpperCase();
    const inValid = /\s/;
    const isSpace = inValid.test(value);
    if (!isSpace) {
      setValueCode(value);
    }
    if (value !== valueReferralCode) setCheckValid(false);
    if (value) {
      setDisabledButton(false);
    } else setDisabledButton(true);
  };

  const onClick = () => {
    if (valueCode && valueCode === valueReferral) {
      onClosed();
    } else if (checkValid) {
      setCheckValid(false);
      dispatch(resetValueReferralCode());
    } else {
      PaymentApi.checkReferralValid(valueCode).then((res) => {
        if (res?.isValid) {
          setCheckValid(true);
          setInputError('');
          dispatch(setReferralValue(valueCode));
          onClosed();
        } else {
          setInputError(TEXT.REFERRAL_CODE_ERROR);
          setCheckValid(false);
        }
      });
    }
  };

  const onFocus = () => {
    setInputError('');
  };
  const onClosed = () => {
    dispatch(openPopup());
  };

  const renderContent = () => (
    <div className="block block--for-dark block--notify p-t2">
      <h2 className="title text-center p-b2">{TEXT.PLACE_HOLDER_REFERRAL_CODE}</h2>
      <div className="form round-1 formReferralCode">
        <p className="text">{TEXT.REFERRAL_CODE_OPTIONAL}</p>
        <InputLabelSlideUp
          value={valueCode || ''}
          onChange={onChange}
          id={EL_ID.REFERRAL_CODE_INPUT}
          autoComplete="cc-number"
          title={TEXT.REFERRAL_CODE}
          placeholder={TEXT.SEVEN_CHARACTER}
          error={inputError || ''}
          titleBtn={!checkValid ? TEXT.CONFIRM : TEXT.CANCEL}
          onSubmit={onClick}
          onFocus={onFocus}
          onEnter={onClick}
          keyLimit={7}
        />
      </div>
    </div>
  );

  const renderFooter = () => (
    <div className="button-group child-auto p-b4">
      <Button
        className="button hollow button--medium"
        textClass="text-gray117"
        onClick={onClosed}
        title={TEXT.SKIP}
      />
      <Button
        className="button button--green button--medium"
        onClick={onClick}
        title={TEXT.CONFIRM}
        disabled={disabledButton}
      />
    </div>
  );

  return (
    <Modal
      className="modal--light"
      renderBody={renderContent}
      renderFooter={renderFooter}
      notClosedButton
      light
    />
  );
};

export default React.memo(PopupReferralCode);
