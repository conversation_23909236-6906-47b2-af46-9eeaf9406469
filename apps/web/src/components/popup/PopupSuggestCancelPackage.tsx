import React from 'react';
import { useDispatch } from 'react-redux';
import { openPopup } from '@actions/popup';
import { TEXT } from '@constants/text';
import { mobileCheck } from '@helpers/common';
import { useVieRouter } from '@customHook';
import { PAGE } from '@constants/constants';
import Button from '../basic/Buttons/Button';
import Modal from '../basic/Modal';

const PopupSuggestCancelPackage = ({ paidMessage, primaryButtonMessage }: any) => {
  const dispatch = useDispatch();
  const router = useVieRouter();

  const onConfirm = () => {
    router.push({ pathname: PAGE.PROFILE_SLUG }, { pathname: '/ca-nhan/dich-vu-da-mua' });
  };

  const closePopup = () => {
    dispatch(openPopup());
  };

  const renderBody = () => (
    <div className="block block--for-dark block--notify">
      <p
        className="text text-center"
        style={{
          color: 'white',
          fontSize: mobileCheck() ? '24px' : '18px',
          margin: '0px'
        }}
      >
        {paidMessage}
      </p>
      <br />
      <div className="button-group child-auto">
        <Button
          className="button hollow button--dark-glass medium button--large"
          title={TEXT.LATER}
          onClick={closePopup}
        />
        <Button
          className="button button--light medium button--large"
          onClick={onConfirm}
          title={primaryButtonMessage}
        />
      </div>
    </div>
  );

  return (
    <Modal className=" modal--notify modal--notify-login" renderBody={renderBody} notClosedButton />
  );
};

export default PopupSuggestCancelPackage;
