import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import cn from 'classnames';
import { openPopup } from '@actions/popup';
import { TEXT } from '@constants/text';
import { useVieRouter } from '@customHook';
import { isMobile } from 'react-device-detect';
import Button from '../basic/Buttons/Button';
import Image from '../basic/Image/Image';
import Modal from '../basic/Modal';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import TrackingPayment from '@tracking/functions/payment';
import { PAGE } from '@constants/constants';
import { ENABLE_SDK_MIDESK_CHAT } from '@config/ConfigEnv';
import MideskChat from '@script/MideskChat';
import { destinationLogin } from '@services/multiProfileServices';
import ConfigCookie from '@config/ConfigCookie';
import { encodeParamDestination } from '@helpers/common';

const PopupSurveyPayment = () => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const trackingPayment = new TrackingPayment();
  const surveyFeedbackContent = useSelector(
    (state: any) => state?.Popup?.configs?.payment_survey_dialog
  );
  const urlFromFooter = useSelector((state: any) => state?.Popup?.params?.intendedUrl);
  const { currentProfile } = useSelector((state: any) => state?.MultiProfile || {});
  const { profile } = useSelector((state: any) => state?.Profile) || {};
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [countdown, setCountdown] = useState(3);
  const timerRef = useRef<any>(null);

  const randomItem =
    surveyFeedbackContent?.header?.imageSrc[
      Math.floor(Math.random() * surveyFeedbackContent?.header?.imageSrc.length)
    ];

  const [formValue, setFormValue] = useState<any>({
    options: [],
    other: ''
  });

  const isDisabled = formValue.options.length === 0 && !formValue.other.length;

  const handleChooseOption = (event: any) => {
    const selectedOption = event.target.innerText;
    setFormValue((prevValue: any) => ({
      ...prevValue,
      options: prevValue.options.includes(selectedOption)
        ? prevValue.options.filter((item: any) => item !== selectedOption)
        : [...prevValue.options, selectedOption]
    }));
  };

  const handleFeedback = () => {
    trackingPayment.sendLeavingSurvey({
      options: formValue.options.join(','),
      other: formValue.other
    });
    setShowConfirmation(true);
  };

  const handleHotlineSupport = (e: any) => {
    e.preventDefault();
    window.location.href = `tel:1800599920`;
    trackingPayment.helpButtonSelectedSurvey();
    dispatch(openPopup({}));
  };

  const handleChatSupport = () => {
    if (!ENABLE_SDK_MIDESK_CHAT || ENABLE_SDK_MIDESK_CHAT === 'false') {
      dispatch(openPopup({}));
      return;
    }
    MideskChat.init();
    setTimeout(() => {
      MideskChat.show();
    }, 1000);
  };

  const startCountdown = () => {
    let count = 3;
    timerRef.current = setInterval(() => {
      count -= 1;
      setCountdown(count);

      if (count === 0) {
        clearInterval(timerRef.current);
        timerRef.current = null;

        if (showConfirmation && !isDisabled) {
          handleClosePopup();
        }
      }
    }, 1000);
  };

  const handleChangeOther = (e: any) => {
    setFormValue((prevValue: any) => ({
      ...prevValue,
      other: e.target.value
    }));
  };

  const handleClosePopup = async () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    setShowConfirmation(false);
    trackingPayment.closeSurvey();
    dispatch(openPopup({}));

    // Determine destination from original closePopup logic
    let destination =
      (ConfigLocalStorage.get('PREVIOUS_URL') as string)?.replace(/["]+/g, '') || '/';
    const currentDomain = window.location.origin;
    if (urlFromFooter) {
      destination = urlFromFooter;
    }
    if (destination?.startsWith(currentDomain)) {
      destination = destination.slice(currentDomain.length);
    }

    const isHomeClick = ConfigLocalStorage.get('isHomeClick');

    // Use destinationLogin for navigation
    const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};

    if (!currentProfile?.id) {
      await destinationLogin({
        dataLogin: { accessToken, profile },
        destination: isHomeClick ? PAGE.HOME : encodeParamDestination(destination),
        router,
        dispatch
      });

      // Clean up local storage
      if (isHomeClick) {
        ConfigLocalStorage.remove('isHomeClick');
      }
      if (destination !== PAGE.HOME && destination && !urlFromFooter) {
        ConfigLocalStorage.remove('PREVIOUS_URL');
      }
    } else {
      // Fallback navigation from original closePopup
      if (urlFromFooter && !urlFromFooter.includes(currentDomain)) {
        window.location.href = urlFromFooter;
      } else if (isHomeClick && destination === PAGE.HOME) {
        ConfigLocalStorage.remove('isHomeClick');
        router.push(PAGE.HOME);
      } else if (destination !== PAGE.HOME && !!destination) {
        const isVODPath = destination?.includes('.html') ? PAGE.VOD : '';
        if (!isVODPath) {
          router.push(destination);
        } else {
          router.push(PAGE.VOD, destination);
        }
        ConfigLocalStorage.remove('PREVIOUS_URL');
      } else {
        router.push(PAGE.HOME);
      }
    }
  };

  const renderCustom = () => (
    <button
      title="close"
      className="button close absolute top-right-4 size-square-24"
      onClick={handleClosePopup}
      aria-label="close"
      type="button"
    >
      <span className="icon icon--variant icon--small">
        <i className="vie vie-times-medium text-black" />
      </span>
    </button>
  );

  const renderBody = () => (
    <div className="bg-white flex flex-col items-center space-y-[24px] relative">
      {showConfirmation ? (
        <>
          <div className="text-center">
            {randomItem && (
              <Image
                src={randomItem}
                alt="survey-image"
                className="w-[120px] h-[120px] mx-auto mb-2"
                notWebp
              />
            )}
            <p className="!text-[24px] font-[500] leading-[33.6px]">Cảm ơn đóng góp của bạn</p>
            <p className="!text-[14px] md:!text-[16px] leading-[20px] md:leading-[22px]">
              Bạn sẽ thoát khỏi đây sau
            </p>
          </div>
          <p className="text-[#9B9B9B] text-[24px] bg-white border-solid border-[1px] border-black p-[10px] rounded-[100px] md:absolute flex size-[56px] text-center justify-center justify-self-center md:translate-y-[150px]">
            {countdown}s
          </p>
        </>
      ) : (
        <>
          <div className="text-center px-[16px]">
            {randomItem && (
              <Image
                src={randomItem}
                alt="survey-image"
                className="w-[120px] h-[120px] mx-auto mb-2"
                notWebp
              />
            )}
            <p className="!text-[24px] font-[500] mb-2">{surveyFeedbackContent?.header?.title}</p>
            <p className="!text-[14px] md:!text-[16px] font-[400] leading-[22.4px]">
              {surveyFeedbackContent?.header?.desc}
            </p>
          </div>
          <hr className="border-dashed border-[#CCCCCC] border-[1px] w-full" />
          <div className="flex flex-wrap gap-[4px] text-[16px] leading-[24px] font-[500] mt-[-8px]">
            {surveyFeedbackContent?.option.map((option: any, index: any) => (
              <button
                key={index}
                className={cn(
                  'text-sm py-2 px-3 rounded-full border-solid border-[1px] mt-[8px]',
                  formValue.options.includes(option)
                    ? 'text-[#2FB138] border-[#2FB138] hover:bg-green-200'
                    : 'text-[#171717] hover:bg-gray-200 bg-[#F3F3F3] border-[#c7c7c7]',
                  'transition-colors duration-200'
                )}
                onClick={handleChooseOption}
              >
                {option}
              </button>
            ))}
          </div>
          <div className="w-full">
            <p className="mb-[8px] !text-black/[91] !font-[500] !text-[16px] leading-[24px]">
              {TEXT.REASON_TITLE}
            </p>
            <textarea
              className="w-full !h-[80px] p-2 border border-gray-300 rounded-md"
              placeholder={TEXT.INPUT_REASON}
              rows={3}
              onChange={handleChangeOther}
            />
          </div>
          <Button
            title={TEXT.SEND}
            className={cn(
              'w-full mt-4 py-2 rounded-full',
              isDisabled
                ? 'bg-[#F3F3F3] text-[#00000017]'
                : 'text-white bg-[#2FB138] hover:bg-green-600'
            )}
            disabled={isDisabled}
            onClick={handleFeedback}
          />
          <hr className="border-dashed border-[#CCCCCC] border-[1px] w-full" />
          <div className="w-full space-y-[8px]">
            <p className="text-center !text-[18px] leading-[140%] font-[500]">
              {TEXT.MORE_SUPPORT}
            </p>
            <div className="flex flex-2 flex-col md:flex-row justify-between w-full space-y-[8px] md:space-y-0 md:space-x-[8px] items-center">
              <Button
                title={TEXT.HOTLINE_SUPPORT}
                customizeClass="h-[40px]"
                className="flex w-full md:flex-1 justify-center items-center space-x-[8px] rounded-full text-[#2FB138] bg-[#e9f9ea] border-solid border-[1px] border-[#ccebce] font-[500] text-[16px]"
                imgSrc={'/assets/img/icon/call_icon.svg'}
                onClick={handleHotlineSupport}
              />
              <Button
                title={TEXT.CHAT_SUPPORT}
                customizeClass="h-[40px]"
                className="flex w-full md:flex-1 justify-center items-center space-x-[8px] rounded-full text-[#2FB138] bg-[#e9f9ea] border-solid border-[1px] border-[#ccebce] font-[500] text-[16px]"
                imgSrc={'/assets/img/icon/chat_icon.svg'}
                onClick={handleChatSupport}
              />
            </div>
          </div>
        </>
      )}
    </div>
  );

  useEffect(() => {
    trackingPayment.diaglogSurveyLoad();
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (showConfirmation && !isDisabled) {
      startCountdown();
    }
  }, [showConfirmation, isDisabled]);

  return (
    <Modal
      className={cn(
        '!bg-white !max-w-full md:!max-w-[600px] rounded-[10px]',
        isMobile && (!showConfirmation ? '!translate-y-[-35%]' : '!translate-y-[85%]')
      )}
      bodyClass="w-full md:w-[600px] p-[24px] rounded-[8px]"
      wrapperClassname="!bg-white !pt-0 relative rounded-[8px] md:!max-w-[600px]"
      renderBody={renderBody}
      modalMobile={isMobile}
      buttonIconClassname="!text-black !shadow-none"
      notClosedButton
      renderCustom={renderCustom}
    />
  );
};

export default React.memo(PopupSurveyPayment);
