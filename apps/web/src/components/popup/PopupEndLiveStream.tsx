import React, { useEffect, useMemo, useState } from 'react';
import { stringReplaceKeys } from '@helpers/utils';
import DetailApi from '@apis/detailApi';
import Button from '@components/basic/Buttons/Button';
import { useDispatch, useSelector } from 'react-redux';
import { setLoading, setToast } from '@actions/app';
import { TEXT } from '@constants/text';
import { useVieRouter } from '@customHook';
import PaymentApi from '@apis/Payment';
import { CONTENT_TYPE, TVOD } from '@constants/constants';
import {
  dialogLivestreamEndLoaded,
  dialogLivestreamEndHomePage,
  dialogLivestreamEndWatch,
  dialogLivestreamEndClose
} from '@tracking/functions/TrackingTVodDialog';
import { isMobile } from 'react-device-detect';
import Modal from '../basic/Modal';

const PopupEndLiveStream = ({
  closeModal,
  title,
  image,
  btnPrimary,
  remindNote,
  contentId,
  description,
  descriptionNotHaveVod,
  descriptionHaveVod,
  profile,
  btnBackHome
}: any) => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const [dataTVodMovie, setDataTVodMovie] = useState<any>('');
  const [content, setContent] = useState<any>('');
  const { waitingDurMsg, consumingDurMsg } = dataTVodMovie?.bizInfo || {};
  const { type } = dataTVodMovie?.benefitInfo || {};
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const remainTimeText = useMemo(() => {
    if (dataTVodMovie) {
      return stringReplaceKeys(remindNote, {
        consumingDurMsg,
        waitingDurMsg
      });
    }
  }, [remindNote, waitingDurMsg, consumingDurMsg]);

  useEffect(() => {
    if (profile?.id && contentId) {
      PaymentApi.getTVodInfo({
        contentId,
        contentType: CONTENT_TYPE.MOVIE
      }).then((res) => {
        let flowName = TVOD.LIVE_STREAM_END;
        if (res?.success) {
          if (type > 0) flowName = TVOD.LIVE_STREAM_END_HAVE_VOD;
          dialogLivestreamEndLoaded({ flowName });
          setDataTVodMovie(res);
        } else dialogLivestreamEndLoaded({ flowName });
      });
    }
  }, [contentId, profile?.id]);

  useEffect(() => {
    if (profile?.id && contentId && dataTVodMovie && type > 0) {
      DetailApi.getContentById({ contentId, isGlobal }).then((res) => {
        if (res?.success) setContent(res?.data);
      });
    }
  }, [contentId, dataTVodMovie, type, profile?.id]);

  const onBackHome = () => {
    if (profile?.id) {
      if (contentId) {
        let flowName = TVOD.LIVE_STREAM_END;
        if (dataTVodMovie && type > 0) flowName = TVOD.LIVE_STREAM_END_HAVE_VOD;
        dialogLivestreamEndHomePage({ flowName });
      } else {
        dialogLivestreamEndHomePage({ flowName: TVOD.LIVE_STREAM_END });
      }
    }
    if (closeModal) closeModal();
    router.push('/');
  };
  const onCloseModal = () => {
    if (contentId && profile?.id) {
      let flowName = TVOD.LIVE_STREAM_END;
      if (dataTVodMovie && type > 0) flowName = TVOD.LIVE_STREAM_END_HAVE_VOD;
      dialogLivestreamEndClose({ flowName });
    }
    if (closeModal) closeModal();
  };

  const onReview = () => {
    dialogLivestreamEndWatch({ flowName: TVOD.LIVE_STREAM_END_HAVE_VOD });
    dispatch(setLoading(true));
    if (content?.seo?.url && content?.href) {
      dispatch(setLoading(false));
      if (closeModal) closeModal();
      router.push(content?.href, content?.seo?.url);
    } else {
      dispatch(setLoading(false));
      dispatch(setToast({ message: TEXT.MSG_ERROR }));
    }
  };
  const renderDescription = () => {
    let text = description;
    if (profile?.id && contentId && type > 0 && dataTVodMovie) {
      if (content?.seo?.url && content?.href) {
        if (descriptionHaveVod) text = descriptionHaveVod;
      } else if (descriptionNotHaveVod) text = descriptionNotHaveVod;
    }
    return (
      <p
        className="text text-center text-muted p-y m-b"
        dangerouslySetInnerHTML={{ __html: text }}
      />
    );
  };
  const isLiveEndToVod =
    profile?.id && contentId && dataTVodMovie && type > 0 && content?.seo?.url && content?.href;

  const renderContent = () => (
    <div className="block block--for-dark p-x2">
      <h2 className="title text-center m-b2">{title}</h2>
      {renderDescription && renderDescription()}
      {isLiveEndToVod && remainTimeText && (
        <p
          className="text-center text-muted p-y m-b m-t2"
          dangerouslySetInnerHTML={{ __html: remainTimeText }}
        />
      )}
    </div>
  );

  const renderCustom = () => (
    <div className="mask mask--overlay">
      <div className="mask-inner text-center">
        <img src={image} alt="livestream-image" />
      </div>
    </div>
  );

  const renderFooter = () => (
    <div className={`button-group child-auto ${isMobile ? 'p-b2' : 'p-b4'}`}>
      <Button
        className={`button ${
          isLiveEndToVod ? 'button--dark-glass hollow' : 'button--light'
        } medium button--large`}
        onClick={onBackHome}
        title={btnBackHome}
      />
      {isLiveEndToVod && (
        <Button
          className="button button--light button--large m-b"
          onClick={onReview}
          title={btnPrimary}
        />
      )}
    </div>
  );

  return (
    <Modal
      className="modal--notify modal--small"
      renderCustom={renderCustom}
      renderBody={renderContent}
      renderFooter={renderFooter}
      onClosed={onCloseModal}
      small
    />
  );
};

export default PopupEndLiveStream;
