import React, { useEffect } from 'react';
import classNames from 'classnames';
import { useDispatch, useSelector } from 'react-redux';
import { openPopup } from '@actions/popup';
import { useVieRouter } from '@customHook';
import { PAGE } from '@constants/constants';
import MultiProfileApi from '@apis/MultiProfile';
import styles from './Modal.module.scss';
import Image from '../basic/Image/Image';

const PopupLobbyGuide = () => {
  const router = useVieRouter();
  const { pathname } = router || {};

  const dispatch = useDispatch();
  const {
    imgDesktop,
    imgLandscape,
    imgPorstrait: imgPortrait
  } = useSelector((state: any) => state?.App?.webConfig?.multiProfile?.dialog?.lobbyGuide || {});

  useEffect(() => {
    MultiProfileApi.insertLobbyTutorial();
  }, []);

  useEffect(() => {
    if ((!imgDesktop && !imgLandscape && !imgPortrait) || !pathname.includes(PAGE.LOBBY_PROFILES)) {
      dispatch(openPopup());
    }
  }, [imgDesktop, imgLandscape, imgPortrait, pathname]);

  const handleClickPopup = () => {
    dispatch(openPopup());
  };

  return (
    <article
      className={classNames(
        styles['modal-lobby-guide'],
        'fixed top left size-square-full layer-max'
      )}
      onClick={handleClickPopup}
    >
      <picture>
        <source media="(min-width: 768px)" srcSet={imgDesktop} />
        <source media="(orientation: landscape)" srcSet={imgLandscape} />
        <source media="(orientation: portrait)" srcSet={imgPortrait} />
        <Image alt="lobby-guide" src={imgDesktop} notWebp className="size-square-full" />
      </picture>
    </article>
  );
};

export default React.memo(PopupLobbyGuide);
