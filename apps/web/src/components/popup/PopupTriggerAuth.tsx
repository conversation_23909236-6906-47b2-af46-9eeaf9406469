import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { openPopup } from '@actions/popup';
import Modal from '../basic/Modal';
import Authentication from '@components/Authentication';
import { POPUP } from '@/constants/constants';
import { useVieRouter } from '@/customHook';
import {
  segmentLoginPopupLoaded,
  segmentLoginPopupClosed
} from '@tracking/functions/TrackingContentForceLoginPopupAuth';
import styles from './Modal.module.scss';

const PopupTriggerAuth = ({ notHasCloseBtn }: any) => {
  const dispatch = useDispatch();
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const router = useVieRouter();
  const popupName = useSelector((state: any) => state?.Popup?.popupName);
  const { contentDetail, data, isClickInfoBox, episodeData } = useSelector(
    (state: any) => state?.Popup
  );

  const trackingDataContent = useMemo(() => {
    return episodeData || contentDetail || data || {};
  }, [episodeData, contentDetail, data]);

  const titlePopupTriggerAuthDisplay = useMemo(() => {
    if (popupName === POPUP.NAME.PLAYER_TRIGGER_AUTH) {
      if (isClickInfoBox) {
        return 'POPUP_LOGIN_CLICK_INFORBOX';
      }
      if (trackingDataContent?.triggerLoginDuration) {
        return 'FORCE_LOGIN_BY_X_MINUTE';
      }
      if (trackingDataContent?.forceLogin) {
        return 'FORCE_LOGIN';
      }
    }
    return 'CONTENT';
  }, [popupName, trackingDataContent, isClickInfoBox]);

  const closePopup = () => {
    segmentLoginPopupClosed({ content: trackingDataContent });
    dispatch(openPopup());
  };

  useEffect(() => {
    const handleRouteChange = () => {
      if (popupName === POPUP.NAME.PLAYER_TRIGGER_AUTH) {
        dispatch(openPopup());
      }
    };

    router.events.on('routeChangeStart', handleRouteChange);

    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, [dispatch, popupName]);

  useEffect(() => {
    segmentLoginPopupLoaded({
      content: trackingDataContent
    });
  }, [trackingDataContent]);

  return (
    <Modal
      modalUi={styles.triggerAuthPopup}
      modalUiContainer={styles.triggerAuthPopupContainer}
      modalUiWrapper="z-[1]"
      modalUiCloseButtonClass={styles.buttonClose}
      renderBody={() => (
        <Authentication
          titlePopupTriggerAuth={titlePopupTriggerAuthDisplay}
          customClassName={styles.triggerAuthPopupContent}
        />
      )}
      notClosedButton={notHasCloseBtn}
      onClosed={closePopup}
      modalMobile={isMobile}
      classModal={isMobile ? 'px-3' : ''}
      modalNewUi
      bodyClass="p-0"
    />
  );
};

export default PopupTriggerAuth;
