import React from 'react';
import { useDispatch } from 'react-redux';
import { openPopup } from '@actions/popup';
import { POPUP } from '@constants/constants';
import { TEXT } from '@constants/text';
import MultiProfileApi from '@apis/MultiProfile';
import Modal from '../basic/Modal';

const PopupPinCodeOverInput = ({ status, data }: any) => {
  const dispatch = useDispatch();

  // for got pin code
  const handleForgotPinCode = () => {
    MultiProfileApi.forgotPinCode({ profileId: data?.id }).then((res) => {
      if (res?.success) {
        dispatch(
          openPopup({
            name: POPUP.NAME.RECREATE_PIN_CODE,
            data,
            status,
            confirmNo: res?.data?.result?.confirmNo
          })
        );
      }
    });
  };
  const renderBody = () => (
    <p
      className="text-white"
      style={{ textAlign: 'center', marginBottom: 0, whiteSpace: 'pre-line' }}
    >
      {TEXT.PIN_CODE_WRONG_OVER}
      <b>{data?.name}</b>
    </p>
  );

  const renderFooter = () => (
    <div className="button-group child-auto padding-medium-down-bottom-16 padding-medium-up-bottom-32">
      <button
        className="button button--light button--large"
        title={TEXT.TITLE_RE_TYPE_PIN_CODE}
        onClick={handleForgotPinCode}
      >
        <span className="text">{TEXT.TITLE_RE_TYPE_PIN_CODE}</span>
      </button>
    </div>
  );

  const handleClosePopup = () => {
    dispatch(openPopup());
  };

  return (
    <Modal
      className="yellow-line"
      bodyClass="modal-body pt-0 relative"
      renderBody={renderBody}
      renderFooter={renderFooter}
      onClosed={handleClosePopup}
      title={TEXT.INPUT_PIN_CODE_WRONG}
    />
  );
};

export default PopupPinCodeOverInput;
