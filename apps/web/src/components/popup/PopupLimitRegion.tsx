import { openPopup } from '@actions/popup';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import cn from 'classnames';
import GlobalRetrictBody from '../basic/GlobalRetrictBody';
import Modal from '../basic/Modal';
import styles from './Modal.module.scss';

const PopupLimitRegion = () => {
  const dispatch = useDispatch();
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const [shouldShowPopup, setShouldShowPopup] = useState(false);

  useEffect(() => {
    const lastShownTime = localStorage.getItem('popupLimitRegionLastShown');
    const currentTime = new Date().getTime();
    const oneDayInMilliseconds = 24 * 60 * 60 * 1000;

    if (!lastShownTime || currentTime - parseInt(lastShownTime) > oneDayInMilliseconds) {
      setShouldShowPopup(true);
      localStorage.setItem('popupLimitRegionLastShown', currentTime.toString());
    }
  }, []);
  useEffect(() => {
    if (!isMobile) {
      const preventScroll = (e: any) => e.preventDefault();
      document.body.style.overflow = 'hidden';
      document.addEventListener('wheel', preventScroll, { passive: false });
      document.addEventListener('touchmove', preventScroll, { passive: false });

      return () => {
        document.body.style.overflow = 'auto';
        document.removeEventListener('wheel', preventScroll);
        document.removeEventListener('touchmove', preventScroll);
      };
    }
    return;
  }, [isMobile]);

  const closePopup = () => {
    dispatch(openPopup());
  };

  const renderBody = () => <GlobalRetrictBody closePopup={closePopup} />;

  return shouldShowPopup ? (
    <Modal
      className="!max-w-full m-[16px]"
      bodyClass="w-full md:w-[520px] rounded-[8px] max-h-[560px] md:max-h-full"
      wrapperClassname={cn(
        'max-w-[520px] py-[40px] rounded-[8px] overflow-y-scroll',
        isMobile ? 'custom-scrollbar' : ''
      )}
      renderBody={renderBody}
      onClosed={closePopup}
      modalMobile={isMobile}
      notClosedButton
      classModel={styles.globalRestrict}
    />
  ) : null;
};

export default PopupLimitRegion;
