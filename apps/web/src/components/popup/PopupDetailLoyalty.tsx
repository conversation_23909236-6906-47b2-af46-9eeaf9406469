import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import moment from 'moment';
import { openPopup } from '@actions/popup';
import classNames from 'classnames';
import { TEXT } from '@constants/text';
import Button from '@components/basic/Buttons/Button';
import { LOYALTY_TYPE, LOYALTY_EXPIRY_TYPE, POSITION, POPUP } from '@constants/constants';
import { setLoading, clearToast } from '@actions/app';
import { redeemVoucher } from '@actions/user';
import modalStyleModule from './Modal.module.scss';
import Modal from '../basic/Modal';

const PopupDetailLoyalty = ({ greenLineNotInImage, yellowLine, data, type }: any) => {
  const dispatch = useDispatch();
  const isMobile = useSelector((state: any) => state.App?.isMobile || false);
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const info = useSelector((state: any) => state?.User?.loyalty?.info || {});
  const redeemVoucherData = useSelector((state: any) => state?.User?.loyalty?.redeemVoucher || {});
  const { availablePoints } = !isEmpty(redeemVoucherData) ? redeemVoucherData : info;
  const isDisabled = data?.pointsToRedeem > (availablePoints || 0);

  const [isPromotion, setIsPromotion] = useState(false);
  const timerRef = useRef<any>(null);

  useEffect(
    () => () => {
      clearTimeout(timerRef.current);
    },
    []
  );

  const formatUTCTime = (utcTime: any, format?: any) =>
    moment
      .utc(utcTime)
      .local()
      .format(format || 'DD/MM/YYYY');

  // Classnames
  const modalClass = classNames(
    'modal--notify modal--notify-login',
    greenLineNotInImage && 'green-line p-t1',
    yellowLine && 'yellow-line p-t1',
    !isPromotion && modalStyleModule['modal-xlarge']
  );

  const handleCloseToast = () => {
    dispatch(
      clearToast({
        position: POSITION.BOTTOM_RIGHT
      })
    );
  };

  // Functions
  const handleExchangePoint = () => {
    if (profile?.mobile || (profile?.email && profile?.emailVerified)) {
      const replaceArray = {
        '{user_type}': profile?.mobile
          ? TEXT.USER_LABEL.PHONE_NUMBER.toLowerCase()
          : TEXT.USER_LABEL.EMAIL.toLowerCase(),
        '{user_value}': profile?.mobile ? profile?.mobile : profile?.email
      };

      dispatch(setLoading(true));

      dispatch(
        redeemVoucher({
          userId: profile?.id,
          voucherId: data?.voucherRefId,
          data: replaceArray || {},
          onCloseLoyalty: handleCloseToast,
          codeDeliveryChannel: profile?.mobile ? 1 : 2
        })
      );
      clearTimeout(timerRef.current);
      timerRef.current = setTimeout(() => {
        dispatch(setLoading(false));
        closePopup();
      }, 2000);
      return;
    }
    dispatch(openPopup({ name: POPUP.NAME.REDEEM_VOUCHER, voucherId: data?.voucherRefId }));
  };

  const closePopup = () => {
    dispatch(openPopup());
  };

  const renderBody = () => (
    <div className="block block--for-dark block--notify">
      <h2 className="title text-center f-medium">
        {isPromotion ? TEXT.LOYALTY_PROMOTION_TITLE : TEXT.LOYALTY_POINT_TITLE}
      </h2>

      <div
        className={classNames(
          'margin-small-up-bottom-16 margin-large-up-bottom-24 text-14 text-white',
          modalStyleModule.loyalty__content
        )}
      >
        <div className="margin-bottom-4">
          <span className="text-muted padding-small-up-right-8">
            {type === LOYALTY_TYPE.PROMOTION ? TEXT.VOUCHER_NAME : TEXT.ACTIVITY_NAME}
          </span>
          {data?.name || data?.voucherName}
        </div>
        <div className="margin-bottom-4">
          <span className="text-muted padding-small-up-right-8">
            {type === LOYALTY_TYPE.PROMOTION ? TEXT.LOYALTY_EXCHANGE_POINT : TEXT.POINT_COLLECT}
          </span>
          {data?.pointsToRedeem || data?.basePoint} {TEXT.VIECOIN}
        </div>
        {type !== LOYALTY_TYPE.PROMOTION && (
          <div className="margin-bottom-4">
            <span className="text-muted padding-small-up-right-8">{TEXT.CONDITION}</span>
            {TEXT.REGISTER_BY_PHONE}
          </div>
        )}
        {type === LOYALTY_TYPE.PROMOTION && (
          <div className="margin-bottom-4">
            <span className="text-muted padding-small-up-right-8">{TEXT.LOYALTY_METHOD}</span>
            {data?.voucherForm}
          </div>
        )}
        {type === LOYALTY_TYPE.PROMOTION && (
          <div className="margin-bottom-4">
            <span className="text-muted padding-small-up-right-8">{TEXT.LOYALTY_EXPIRED}</span>
            {data?.voucherExpirationType === LOYALTY_EXPIRY_TYPE.NUMBER_OF_DAY
              ? `${data?.daysToExpiry} ${TEXT.DAY_TO_EXPIRY}`
              : formatUTCTime(data?.specificExpiryDate)}
          </div>
        )}
        <div
          className={classNames(
            'padding-right-8',
            modalStyleModule.loyalty__content__info,
            !isPromotion && modalStyleModule.content
          )}
          dangerouslySetInnerHTML={{
            __html: data?.description || data?.voucherInfo
          }}
        />
      </div>

      <div className="button-group child-auto">
        <Button
          className={`${modalStyleModule.button} ${modalStyleModule['gray-button']} button hollow button--dark-glass medium button--large`}
          title={TEXT.CLOSED}
          onClick={closePopup}
        />
        {isPromotion && (
          <Button
            className={`${modalStyleModule.button} button button--light medium button--large`}
            title={TEXT.EXCHANGE_POINT}
            onClick={handleExchangePoint}
            disabled={isDisabled}
          />
        )}
      </div>
    </div>
  );

  // Hooks
  useEffect(() => {
    if (type === LOYALTY_TYPE.PROMOTION) return setIsPromotion(true);
    setIsPromotion(false);
  }, [isPromotion]);

  return (
    <Modal
      className={modalClass}
      renderBody={renderBody}
      notClosedButton
      onClosed={closePopup}
      modalMobile={isMobile}
      classModal={isMobile ? 'p-l3 p-r3' : ''}
    />
  );
};

export default PopupDetailLoyalty;
