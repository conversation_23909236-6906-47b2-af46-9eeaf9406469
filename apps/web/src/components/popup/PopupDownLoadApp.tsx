import React from 'react';
import { useDispatch } from 'react-redux';
import { openPopup } from '@actions/popup';
import DownloadApp from '@components/DownloadApp';
import Modal from '../basic/Modal';

const PopupDownLoadApp = () => {
  const dispatch = useDispatch();
  const onClosed = () => {
    dispatch(openPopup());
  };

  const renderContent = () => (
    <div className="block p-b3 p-x3">
      <DownloadApp />
    </div>
  );

  return <Modal className="modal--small" renderBody={renderContent} onClosed={onClosed} small />;
};

export default React.memo(PopupDownLoadApp);
