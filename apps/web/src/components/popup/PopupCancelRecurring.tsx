import React from 'react';
import { TEXT } from '@constants/text';
import Modal from '../basic/Modal';

const PopupCancelRecurring = ({ closePopup, onAccept }: any) => {
  const renderBody = () => (
    <div className="block block--for-dark block--notify block--notify-cancel">
      <h2 className="title text-center">{TEXT.CANCEL_RECURRING}</h2>
      <p className="text text-center text-muted margin-small-up-bottom-12 margin-large-up-bottom-32 text-large-up-16">
        {TEXT.CANCEL_RECURRING_SUB}
      </p>
      <div className="button-group child-auto">
        <button
          className="button button--dark-glass hollow button--large"
          title="Dùng thử"
          onClick={() => closePopup(false)}
        >
          <span className="text">{TEXT.GO_BACK}</span>
        </button>
        <button
          className="button button--light button--large"
          title="Đăng nhập/ Đăng ký"
          onClick={() => onAccept()}
        >
          <span className="text">{TEXT.CANCEL_RENEWAL}</span>
        </button>
      </div>
    </div>
  );

  return (
    <Modal
      className="green-line modal--notify"
      renderBody={renderBody}
      onClosed={closePopup}
      notClosedButton
    />
  );
};

export default PopupCancelRecurring;
