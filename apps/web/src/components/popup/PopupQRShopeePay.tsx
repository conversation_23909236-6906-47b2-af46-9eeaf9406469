/* eslint-disable react/no-unused-state */
/* eslint-disable react/destructuring-assignment */
import React, { useEffect } from 'react';
import { TEXT } from '@constants/text';
import ConfigImage from '@config/ConfigImage';
import { numberWithCommas } from '@helpers/common';
import { CURRENCY } from '@constants/constants';
import Button from '../basic/Buttons/Button';
import Modal from '../basic/Modal';

const PopupQRShopeePay = ({
  countDoneAction,
  cancelTransaction,
  qrDisplayTime,
  qrCodeImg,
  totalAmount,
  spPayTransaction,
  handle5Sec,
  recurring
}: any) => {
  useEffect(
    () => () => {
      handleCancelTransaction();
    },
    []
  );

  const renderHeader = () => (
    <div className="badges badges--payment-qrcode padding-medium-up-left-32 padding-medium-up-right-32">
      <div className="badges__container grid-x">
        <div className="badges__item small-4">
          <img src={ConfigImage.shopeePayHand} alt="shopeepay step01" />
          <span className="text text-12 padding-medium-up-left-6 padding-medium-up-right-6">
            Đăng nhập ứng dụng ShopeePay
          </span>
        </div>
        <div className="badges__item small-4">
          <img src={ConfigImage.sspQrPhoneHand2} alt="Shopeepay step01" />
          <span className="text text-12 padding-medium-up-left-6 padding-medium-up-right-6">
            Chọn tính năng quét mã
          </span>
        </div>
        <div className="badges__item small-4">
          <img src={ConfigImage.sspQrPhoneHand3} alt="shopeepay step01" />
          <span className="text text-12 padding-medium-up-left-6 padding-medium-up-right-6">
            Di chuyển camera và quét mã trên
          </span>
        </div>
      </div>
    </div>
  );

  let titleSpp = TEXT.CHECK_OUT_SHOPEE_PAY;
  if (recurring) titleSpp = TEXT.CHECK_OUT_WALLET_SHOPEE_PAY;

  const renderBody = () => (
    <>
      <div className="block block--for-dark block--qrcode m-x-auto">
        <h2 className="title text-center">{titleSpp}</h2>
        <p className="text">
          {`Thời gian quét mã thanh toán còn `}
          <DisplayTime
            time={qrDisplayTime || 300}
            countDoneAction={countDoneAction}
            handle5Sec={handle5Sec}
            spPayTransaction={spPayTransaction}
          />
          {` giây`}
        </p>
        <p className="text">
          {`Số tiền: `}
          <span className="total-price">{`${numberWithCommas(totalAmount)} ${CURRENCY.VND}`}</span>
        </p>
        <div className="text-center">
          <div className="thumb-img m-t2">
            <img src={qrCodeImg} alt="Mã QRCode" />
          </div>
        </div>
      </div>
      <div className="button-group child-auto">
        <Button
          className="button hollow white button--large"
          onClick={handleCancelTransaction}
          title={TEXT.CHOOSE_ANOTHER_METHOD}
        />
      </div>
    </>
  );

  const handleCancelTransaction = () => {
    cancelTransaction(spPayTransaction);
  };

  return (
    <Modal
      className="modal--large middle modal--notify"
      orange
      renderHeader={renderHeader}
      renderBody={renderBody}
      notClosedButton
    />
  );
};

class DisplayTime extends React.PureComponent<any, any> {
  displayTimer: any;
  constructor(props: any) {
    super(props);
    this.state = {
      displayTime: props?.time
    };
  }

  componentDidMount() {
    this.setDisplayTime();
  }

  componentWillUnmount() {
    clearTimeout(this.displayTimer);
  }

  setDisplayTime = () => {
    const { handle5Sec, countDoneAction } = this.props;
    if (this.displayTimer) clearInterval(this.displayTimer);
    this.displayTimer = setInterval(() => {
      const displayTime = this.state.displayTime - 1;
      if (displayTime >= 0) {
        this.setState({ displayTime });

        if (displayTime % 5 === 0) {
          if (handle5Sec) handle5Sec();
        }
      } else if (displayTime < 0) {
        this.setState({ disable: false });
        clearInterval(this.displayTimer);
        if (countDoneAction) countDoneAction(this.props.spPayTransaction);
      }
    }, 1000);
  };

  render() {
    return <span className="text highlight">{this.state.displayTime}</span>;
  }
}

export default PopupQRShopeePay;
