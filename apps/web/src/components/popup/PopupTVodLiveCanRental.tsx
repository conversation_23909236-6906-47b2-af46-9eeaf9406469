import React, { useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { useVieRouter } from '@customHook';
import get from 'lodash/get';
import IconTextItem from '@components/basic/items/IconTextItem';
import { openPopup } from '@actions/popup';
import { getTVodOffer } from '@actions/payment';
import { CURRENCY, PAGE } from '@constants/constants';
import Button from '../basic/Buttons/Button';
import Modal from '../basic/Modal';
import Image from '../basic/Image/Image';

const PopupTVodLiveCanRental = ({
  content,
  tvodInfo,
  isChangeToVOD,
  isNeedReloadOffer,
  isFromPriceNotSameCurPrice,
  notClosedButton,
  isNotCheckPrice,
  onContinue,
  callbackCreateTransaction
}: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const { images, title } = content || {};
  const priceTVodLive = get(tvodInfo, 'bizInfo.price', 0);
  const titlePopup = useMemo(() => {
    let strTitle = '';
    if (isChangeToVOD) {
      const priceFormat = new Intl.NumberFormat('vi-VN').format(priceTVodLive);
      strTitle = `Xem "${title}" với ${priceFormat} `;
    } else {
      strTitle = `Xem "${title}"`;
    }
    return strTitle;
  }, [title, isChangeToVOD, priceTVodLive]);

  const onClosed = () => {
    dispatch(openPopup());
  };

  const onRentalTVod = () => {
    if (isNotCheckPrice && typeof onContinue === 'function') {
      onContinue();
    } else if (get(tvodInfo, 'bizInfo.tvodProductId', '')) {
      if (isFromPriceNotSameCurPrice) {
        const queryParams = { ...router?.query, fromPrice: priceTVodLive };
        router.replace(
          { pathname: PAGE.RENTAL_CONTENT, query: queryParams },
          { pathname: PAGE.RENTAL_CONTENT, query: queryParams }
        );
      }
      if (isNeedReloadOffer) {
        dispatch(
          getTVodOffer({
            tvodProductId: get(tvodInfo, 'bizInfo.tvodProductId', '')
          })
        );
        if (typeof callbackCreateTransaction === 'function') callbackCreateTransaction();
      }
    }
    onClosed();
  };

  const renderBody = () => (
    <div className="block block--for-dark block--notify">
      <h2 className="title text-center">
        {`${titlePopup} ${titlePopup && isChangeToVOD ? CURRENCY.VND : ''}`}
      </h2>
      {isChangeToVOD && (
        <div className="list divide-y-1 padding-medium-up-top-16 padding-medium-up-bottom-12 padding-medium-down-top-8 padding-medium-down-bottom-4 margin-large-up-bottom-32 margin-large-down-bottom-12">
          <div className="list__item flex-box align-middle margin-small-up-bottom-4">
            <span className="text text-gray239 text-medium-up-16 text-medium-down-14 padding-small-up-top-2 p-b">
              Có thể xem lại sau khi chương trình kết thúc
            </span>
          </div>
          <div className="list__item flex-box align-middle margin-small-up-bottom-4">
            <IconTextItem text={get(tvodInfo, 'bizInfo.waitingDurDesc', '')} />
          </div>
          <div className="list__item flex-box align-middle margin-small-up-bottom-4">
            <IconTextItem text={get(tvodInfo, 'bizInfo.consumingDurDesc', '')} />
          </div>
        </div>
      )}
      {renderFooter()}
    </div>
  );

  const renderCustom = () => (
    <div className="mask mask--overlay">
      <div className="mask-inner text-center">
        <Image
          src={images?.thumbnailHotNTC || images?.thumbnail}
          style={{ width: '100%', backgroundColor: '#111' }}
        />
      </div>
    </div>
  );

  const renderFooter = () => {
    const priceTVodContent = new Intl.NumberFormat('vi-VN').format(priceTVodLive || 0);
    return (
      <div className="button-group child-auto">
        <Button
          className="button button--light medium button--large"
          onClick={onRentalTVod}
          title={`Thuê ngay ${priceTVodContent} <span>${CURRENCY.VND}</span>`}
        />
      </div>
    );
  };

  return (
    <Modal
      className="modal modal--dark size-w-full modal--large middle modal--notify"
      renderCustom={renderCustom}
      renderBody={renderBody}
      onClosed={onClosed}
      notClosedButton={!!notClosedButton}
    />
  );
};

export default React.memo(PopupTVodLiveCanRental);
