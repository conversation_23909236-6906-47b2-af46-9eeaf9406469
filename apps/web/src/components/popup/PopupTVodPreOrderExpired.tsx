import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import get from 'lodash/get';
import { openPopup } from '@actions/popup';
import { getTVodOffer } from '@actions/payment';
import { TEXT } from '@constants/text';
import { CURRENCY, PAGE, POPUP, TVOD } from '@constants/constants';
import {
  dialogMissedEventLoaded,
  dialogMissedEventHomePage
} from '@tracking/functions/TrackingTVodDialog';
import {
  dialogTimeOutSaleLoaded,
  dialogTimeOutSaleHomePage,
  dialogTimeOutSaleSelect
} from '@tracking/functions/TrackingTVodDialogTimeOut';
import { isMobile } from 'react-device-detect';
import Image from '../basic/Image/Image';
import Modal from '../basic/Modal';
import Button from '../basic/Buttons/Button';

const PopupTVodPreOrderExpired = ({
  name,
  content,
  tvodInfo,
  tvodOffer,
  isNeedReloadOffer,
  isFromPriceNotSameCurPrice,
  notClosedButton,
  callbackCreateTransaction
}: any) => {
  const dispatch = useDispatch();
  const userType = useSelector((state: any) => state?.User?.USER_TYPE);
  const { images, title, contentId } = content || {};
  const preOrder = get(tvodInfo, 'bizInfo.preOrder', {});
  const router = useVieRouter();

  const isExpiredPromotionalPrice = useMemo(
    () => name === POPUP.NAME.TVOD_PRE_ORDER_PROMOTIONAL_PRICE_EXPIRED,
    [name]
  );
  const isLiveTVodExpired = useMemo(() => name === POPUP.NAME.TVOD_LIVE_EXPIRED, [name]);
  const titlePopup = useMemo(() => {
    let strTitle = '';
    if (isExpiredPromotionalPrice) {
      strTitle = `Hết hạn đặt trước "${title}" với ${new Intl.NumberFormat('vi-VN').format(
        preOrder?.price || 0
      )} `;
    } else if (isLiveTVodExpired) {
      strTitle = `Hết hạn thuê "${title}"`;
    } else {
      strTitle = `Hết hạn đặt trước "${title}"`;
    }
    return strTitle;
  }, [preOrder?.price, title, isExpiredPromotionalPrice, isLiveTVodExpired, contentId]);

  useEffect(() => {
    if (!isExpiredPromotionalPrice && isLiveTVodExpired) {
      let flowName = TVOD.MISSED_EVENT;
      if (contentId) flowName = TVOD.MISSED_EVENT_HAVE_VOD;
      dialogMissedEventLoaded({
        userType: userType?.userType,
        data: content,
        flowName
      });
    }
  }, [contentId, content]);

  useEffect(() => {
    if (isExpiredPromotionalPrice) {
      dialogTimeOutSaleLoaded({
        flowName: contentId
          ? TVOD.POPUP.TIME_OUT_SALE_PRE_ORDER_HAVE_VOD
          : TVOD.POPUP.TIME_OUT_SALE_PRE_ORDER
      });
    }
  }, []);

  const descriptionPopup = useMemo(() => {
    let strDes = '';
    if (isLiveTVodExpired && !contentId) {
      strDes = 'Trở về trang chủ để khám phá thêm những nội dung hấp dẫn khác';
    }
    return strDes;
  }, [isLiveTVodExpired, contentId]);

  const onContinue = () => {
    if (tvodInfo?.bizInfo?.tvodProductId) {
      if (isFromPriceNotSameCurPrice) {
        const queryParams = { ...router?.query, fromPrice: tvodOffer?.price };
        router.replace(
          { pathname: PAGE.RENTAL_CONTENT, query: queryParams },
          { pathname: PAGE.RENTAL_CONTENT, query: queryParams }
        );
      }
      if (isNeedReloadOffer) {
        dispatch(
          getTVodOffer({
            tvodProductId: tvodInfo.bizInfo.tvodProductId
          })
        );
        if (typeof callbackCreateTransaction === 'function') {
          callbackCreateTransaction();
        }
      }
    }
    if (isExpiredPromotionalPrice) {
      dialogTimeOutSaleSelect({
        flowName: contentId
          ? TVOD.POPUP.TIME_OUT_SALE_PRE_ORDER_HAVE_VOD
          : TVOD.POPUP.TIME_OUT_SALE_PRE_ORDER
      });
    }
    onClosed();
  };

  const onBackHome = () => {
    if (!isExpiredPromotionalPrice && isLiveTVodExpired) {
      let flowName = TVOD.MISSED_EVENT;
      if (contentId) flowName = TVOD.MISSED_EVENT_HAVE_VOD;
      dialogMissedEventHomePage({
        userType: userType?.userType,
        data: content,
        flowName
      });
    }

    if (isExpiredPromotionalPrice) {
      dialogTimeOutSaleHomePage({
        flowName: contentId
          ? TVOD.POPUP.TIME_OUT_SALE_PRE_ORDER_HAVE_VOD
          : TVOD.POPUP.TIME_OUT_SALE_PRE_ORDER
      });
    }

    onClosed();
    router.push('/');
  };

  const onClosed = () => {
    dispatch(openPopup());
  };

  const renderBody = () => (
    <div className="block block--for-dark block--notify">
      <h2 className="title text-center m-b3">
        {`${titlePopup} ${titlePopup && isExpiredPromotionalPrice ? CURRENCY.VND : ''}`}
      </h2>
      {descriptionPopup && (
        <p
          className={`text text-center text-muted margin-large-up-bottom-32 text-large-up-16 ${
            isMobile ? 'm-b3' : ''
          }`}
        >
          {descriptionPopup}
        </p>
      )}
      {renderFooter()}
    </div>
  );

  const renderCustom = () => (
    <div className="mask mask--overlay">
      <div className="mask-inner text-center">
        <Image
          src={images?.thumbnailHotNTC || images?.thumbnail}
          style={{ width: '100%', backgroundColor: '#111' }}
        />
      </div>
    </div>
  );

  const renderFooter = () => {
    if (isExpiredPromotionalPrice) {
      return (
        <div className="button-group child-auto">
          <Button
            className="button hollow button--dark-glass medium button--large"
            onClick={onBackHome}
            title={TEXT.BACK_HOME}
          />
          <Button
            className="button button--light medium button--large"
            onClick={onContinue}
            title={`Tiếp tục với ${new Intl.NumberFormat('vi-VN').format(
              tvodOffer?.price || 0
            )} <span>${CURRENCY.VND}</span>`}
          />
        </div>
      );
    }
    return (
      <div className="button-group child-auto">
        <Button
          className="button button--light medium button--large"
          onClick={onBackHome}
          title={TEXT.BACK_HOME}
        />
      </div>
    );
  };

  return (
    <Modal
      className="modal modal--dark size-w-full modal--large middle modal--notify"
      renderCustom={renderCustom}
      renderBody={renderBody}
      onClosed={onClosed}
      notClosedButton={notClosedButton}
    />
  );
};

export default React.memo(PopupTVodPreOrderExpired);
