import React, { useEffect, useRef } from 'react';
import { TEXT } from '@constants/text';
import { POSITION, ICON_KEY } from '@constants/constants';
import Button from '@components/basic/Buttons/Button';
import InputCustom from '@components/basic/Input/InputCustom';
import { openPopup } from '@actions/popup';
import { useDispatch, useSelector } from 'react-redux';
import useValidateEmailPhoneForm from '@components/basic/Input/useValidateEmailPhoneForm';
import Modal from '@components/basic/Modal';
import { saveInfoRedeemData, redeemVoucher } from '@actions/user';
import { setLoading } from '@actions/app';
import { clearToast } from '@actions/app';
import { validateEmail } from '@helpers/common';

const PopupRedeemVoucher = ({ voucherId }: any) => {
  const dispatch = useDispatch();
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const infoRedeemData = useSelector((state: any) => state?.User?.loyalty?.infoRedeemData || {});
  const value = useValidateEmailPhoneForm(infoRedeemData?.value || '');
  const timerRef = useRef<any>(null);

  useEffect(
    () => () => {
      clearTimeout(timerRef.current);
    },
    []
  );

  const handleCloseToast = () => {
    dispatch(
      clearToast({
        position: POSITION.BOTTOM_RIGHT
      })
    );
  };

  const handleRedeem = () => {
    dispatch(saveInfoRedeemData(value));
    const userInfo = value?.value;
    const isEmail = validateEmail(userInfo);

    const replaceArray = {
      '{user_type}': isEmail
        ? TEXT.USER_LABEL.EMAIL.toLowerCase()
        : TEXT.USER_LABEL.PHONE_NUMBER.toLowerCase(),
      '{user_value}': userInfo
    };

    dispatch(setLoading(true));

    dispatch(
      redeemVoucher({
        userId: profile?.id,
        voucherId,
        data: replaceArray,
        onCloseLoyalty: handleCloseToast,
        codeDeliveryChannel: isEmail ? 2 : 1,
        phoneNumber: isEmail ? '' : userInfo,
        email: isEmail ? userInfo : ''
      })
    );
    clearTimeout(timerRef.current);
    timerRef.current = setTimeout(() => {
      dispatch(setLoading(false));
      handleClose();
    }, 2000);
  };
  const handleClose = () => {
    dispatch(openPopup({}));
  };

  const renderBody = () => (
    <form
      className="form form-default form--for-dark form--member form--member-modal"
      data-abide="true"
    >
      <InputCustom
        label={TEXT.PHONE_OR_EMAIL}
        id="password"
        type="text"
        error={value?.error}
        valueOutput={value}
        className="input-for-dark"
        onBlur={value?.onBlur}
        isErrClassAbsolute
        iconSvg={ICON_KEY.EMAIL}
      />
    </form>
  );

  const renderFooter = () => (
    <div className="button-group child-auto padding-medium-down-bottom-16 padding-medium-up-bottom-32">
      <Button
        className="button button--dark-glass button--xlarge-up button--medium hollow"
        title={TEXT.CANCEL}
        subTitle={TEXT.CANCEL}
        onClick={handleClose}
      />
      <Button
        className="button button--light button--xlarge-up button--medium"
        title={TEXT.CONFIRM}
        subTitle={TEXT.CONFIRM}
        onClick={handleRedeem}
        disabled={!value?.value || !!value?.error}
      />
    </div>
  );
  return (
    <Modal
      className="green-line"
      title={TEXT.TITLE_POPUP_REDEEM_VOUCHER}
      subTitle={TEXT.DESC_POPUP_REDEEM_VOUCHER}
      renderBody={renderBody}
      renderFooter={renderFooter}
      classCustom="m-b m-t2 text-14 text-large-up-16"
      notClosedButton
    />
  );
};

export default PopupRedeemVoucher;
