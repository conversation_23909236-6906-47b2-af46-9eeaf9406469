import React from 'react';
import ConfigImage from '@config/ConfigImage';
import { useDispatch } from 'react-redux';
import { openPopup } from '@actions/popup';
import { TEXT } from '@constants/text';
import ProgressBar from '../basic/Progress/ProgressBar';
import Modal from '../basic/Modal';

const PopupSortingFavorite = () => {
  const dispatch = useDispatch();

  const renderCustom = () => (
    <div className="mask">
      <div className="text-center">
        <img src={ConfigImage.Illustration} alt="Illustration process" />
      </div>
    </div>
  );

  const onEnd = () => {
    dispatch(openPopup());
    window.location.reload();
  };

  const renderBody = () => (
    <div className="block block--for-dark block--notify">
      <h2 className="title text-center">{TEXT.POPUP_PERSONAL_HOME_PAGE_TITLE}</h2>
      <p className="text text-center text-muted margin-small-up-bottom-12 margin-large-up-bottom-32 text-large-up-16">
        {TEXT.SORTING_FAVORITE}
      </p>
      <ProgressBar onEnd={onEnd} time={3000} />
    </div>
  );

  return (
    <Modal
      className="dark modal--transparent"
      renderCustom={renderCustom}
      renderBody={renderBody}
      notClosedButton
      light
    />
  );
};

export default PopupSortingFavorite;
