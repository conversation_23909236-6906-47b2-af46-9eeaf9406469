import React from 'react';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';
import Modal from '../basic/Modal';

const PopupPreventBuyPackage = (props: any) => {
  const { closePopup } = props || {};

  const renderCustom = () => (
    <div className="mask mask--dark-backdrop intrinsic img-child-transparent">
      <div className="mask__inner ratio-variant-1d8">
        <div className="mask__img absolute">
          <img src={ConfigImage.popupPreventBuyPackage} alt="popup-PreventBuyPackage" />
        </div>
      </div>
    </div>
  );
  const renderBody = () => (
    <div className="block block--for-dark">
      <h2 className="title text-center">{TEXT.PREVENT_PACKAGE_TITLE || ''}</h2>
      <p className="text text-center text-muted">
        Chào người bạn phương xa, VieON hiện hỗ trợ việc đăng ký gói VIP trên điện thoại thông minh
        hoặc Android TV (Sony và TCL). Vui lòng truy cập kho ứng dụng của Apple hoặc Google để tải
        và đăng ký gói ngay bạn nhé.
      </p>
      <p className="text text-center text-muted">
        Hello friends, VieON’s payment is now available on smartphones or Android TV (Sony and TCL
        brand). You can find the VieON application from App Store or Play Store, make payment and
        enjoy our selected contents just for you.
      </p>
      <div className="button-group child-auto">
        <a className="button button--light button--large" title="OK" onClick={closePopup}>
          <span className="text"> OK</span>
        </a>
      </div>
    </div>
  );

  return (
    <Modal
      className="modal--notify modal--notify-login"
      renderBody={renderBody}
      renderCustom={renderCustom}
      onClosed={closePopup}
    />
  );
};

export default PopupPreventBuyPackage;
