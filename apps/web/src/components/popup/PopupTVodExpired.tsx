import React, { useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { openPopup } from '@actions/popup';
import { useVieRouter } from '@customHook';
import { TEXT } from '@constants/text';
import { PAGE } from '@constants/constants';
import IconTextItem from '@components/basic/items/IconTextItem';
import Image from '@components/basic/Image/Image';
import Button from '../basic/Buttons/Button';
import Modal from '../basic/Modal';

const PopupTVodExpired = (props: any) => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const { contentData, newTVodInfo, title, button } = props || {};
  const { images } = contentData || {};
  const onContinue = () => {
    const queryParams = {
      id: contentData?.id,
      type: contentData?.type
    };
    router.push(
      { pathname: PAGE.RENTAL_CONTENT, query: queryParams },
      { pathname: PAGE.RENTAL_CONTENT, query: queryParams }
    );
    onClosed();
  };

  const onClosed = () => {
    dispatch(openPopup());
  };

  const popupTitle = useMemo(() => {
    const temp = (title || '').replace('{title}', `"${contentData?.title || ''}"`);
    return (temp || '').replace('{price}', newTVodInfo?.bizInfo?.priceMsg || '');
  }, [title, contentData?.title]);

  const waitingDurMsg = useMemo(() => {
    // (props.waitingDurMsg || '').replace(
    //   '{waitingDurMsg}',
    //   newTVodInfo?.bizInfo?.waitingDurMsg || ''
    // ),
    const { waitingDurMsg } = props;
    const { bizInfo } = newTVodInfo || {};
    const { waitingDurMsg: bizWaitingDurMsg } = bizInfo || {};
    return (waitingDurMsg || '').replace('{waitingDurMsg}', bizWaitingDurMsg || '');
  }, [title, contentData?.title]);

  const consumingDurMsg = useMemo(
    () => {
      const { consumingDurMsg } = props;
      const { bizInfo } = newTVodInfo || {};
      const { consumingDurMsg: bizConsumingDurMsg } = bizInfo || {};
      return (consumingDurMsg || '').replace('{consumingDurMsg}', bizConsumingDurMsg || '');
    },
    // (props.consumingDurMsg || '').replace(
    //   '{consumingDurMsg}',
    //   newTVodInfo?.bizInfo?.consumingDurMsg || ''
    // ),
    [title, contentData?.title]
  );

  const renderContent = () => (
    <div className="block block--for-dark block--notify">
      <h2 className="title text-center">{popupTitle || ''}</h2>
      <div className="list divide-y-1 padding-medium-up-top-16 padding-medium-up-bottom-12 padding-medium-down-top-8 padding-medium-down-bottom-4 margin-large-up-bottom-32 margin-large-down-bottom-12">
        <div className="list__item">
          <IconTextItem text={waitingDurMsg} />
        </div>
        <div className="list__item">
          <IconTextItem text={consumingDurMsg} />
        </div>
      </div>
    </div>
  );

  const renderCustom = () => (
    <div className="mask mask--overlay">
      <div className="mask-inner text-center">
        <Image
          src={images?.thumbnailHotNTC || images?.thumbnail}
          style={{ width: '100%', backgroundColor: '#111' }}
        />
      </div>
    </div>
  );

  const renderFooter = () => (
    <div className="button-group child-auto p-b4">
      <Button
        className="button button--light button--large m-b"
        onClick={onContinue}
        title={button || TEXT.CONTINUE}
      />
    </div>
  );

  return (
    <Modal
      className="modal--notify size-w-full"
      renderCustom={renderCustom}
      renderBody={renderContent}
      renderFooter={renderFooter}
      onClosed={onClosed}
    />
  );
};

export default React.memo(PopupTVodExpired);
