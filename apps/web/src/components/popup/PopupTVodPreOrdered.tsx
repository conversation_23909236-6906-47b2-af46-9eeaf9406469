import React, { useEffect } from 'react';
import { useVieRouter } from '@customHook';
import { useDispatch, useSelector } from 'react-redux';
import { openPopup } from '@actions/popup';
import { TEXT } from '@constants/text';
import Tags from '@components/basic/Tags/Tags';
import { TAG_KEY } from '@constants/constants';
import {
  dialogRentLoaded,
  dialogRentLoadedWatch,
  dialogRentLoadedHomePage
} from '@tracking/functions/TrackingTVodDialog';
import { destinationLogin, pushToLobby } from '@services/multiProfileServices';
import Image from '../basic/Image/Image';
import Modal from '../basic/Modal';
import Button from '../basic/Buttons/Button';
import ConfigCookie from '@/config/ConfigCookie';

const PopupTVodPreOrdered = ({ content }: any) => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const userType = useSelector((state: any) => state?.User?.USER_TYPE);
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const { currentProfile } = useSelector((state: any) => state?.MultiProfile || {});
  const { images, title, href, seo, labelPublicDay } = content || {};

  useEffect(() => {
    dialogRentLoaded({
      userType: userType?.userType,
      data: content
    });
  }, [userType, content]);

  const handleCheckSkipLobby = ({ destination }: { destination: string }) => {
    const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};

    destinationLogin({
      dataLogin: {
        profile,
        accessToken
      },
      destination,
      router,
      dispatch
    });
  };

  const onContinue = () => {
    dialogRentLoadedWatch({
      userType: userType?.userType,
      data: content
    });
    onClosed();
    if (currentProfile?.id) return router.push(href, seo?.url);
    handleCheckSkipLobby({ destination: seo?.url });
  };

  const onBackHome = () => {
    dialogRentLoadedHomePage({
      userType: userType?.userType,
      data: content
    });
    if (!currentProfile?.id) return handleCheckSkipLobby({ destination: '/' });
  };

  const onClosed = () => {
    dispatch(openPopup());
  };

  const renderContent = () => (
    <div className="block block--for-dark block--notify">
      <h2 className="title text-center">{`Bạn đã đặt trước "${title}"`}</h2>
      {labelPublicDay && (
        <Tags
          tagKey={TAG_KEY.REMIND}
          subClass="padding-small-up-bottom-12 padding-large-up-bottom-24 align-center !mb-2 lg:!mb-3"
          description={labelPublicDay}
          txtClass="text-large-up-18 padding-small-up-left-4 padding-large-up-left-8 text-medium text-green p-b"
          iClass="icon--small icon--tiny-xs text-green"
        />
      )}
      {renderFooter()}
    </div>
  );

  const renderCustom = () => (
    <div className="mask mask--overlay">
      <div className="mask-inner text-center">
        <Image
          src={images?.thumbnailHotNTC || images?.thumbnail}
          style={{ width: '100%', backgroundColor: '#111' }}
        />
      </div>
    </div>
  );

  const renderFooter = () => (
    <div className="button-group child-auto">
      <Button
        className="button hollow button--dark-glass medium button--large"
        onClick={onBackHome}
        title={TEXT.BACK_HOME}
      />
      <Button
        className="button button--light medium button--large"
        onClick={onContinue}
        title={TEXT.WATCH_NOW}
      />
    </div>
  );

  return (
    <Modal
      className="modal modal--dark size-w-full modal--large middle modal--notify"
      renderCustom={renderCustom}
      renderBody={renderContent}
      onClosed={onClosed}
      notClosedButton
    />
  );
};

export default React.memo(PopupTVodPreOrdered);
