import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import { useVieRouter } from '@customHook';
import { openPopup } from '@/actions/popup';
import Image from '@components/basic/Image/Image';
import Modal from '../basic/Modal';
import PricePopupInfo from '../PricePopupInfo';
import PackageBenefitList from '../PackageBenefitList';
import modalStyleModule from './Modal.module.scss';
import {
  revisePaymentSelected,
  revisePaymentLoaded,
  revisePaymentClosed
} from '@tracking/functions/TrackingRevisePayment';
import { CONTENT_TYPE, PAGE } from '@/constants/constants';
import { VALUE } from '@config/ConfigSegment';
import {
  qualitySubSubscribePackageButtonSelected,
  svodTrialCancelButtonSelected,
  svodTrialSubscribePackageButtonLoaded,
  vipFeatureSubscribePackageLoaded,
  vipFeatureSubscribePackageCancelButtonSelected
} from '@tracking/functions/TrackingPaymentConversion';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import LocalStorage from '@config/LocalStorage';
import { isEmpty } from 'lodash';
import NewIcon from '@/components/basic/Icon/NewIcon';
import { getContentTypeText } from '@/helpers/utils';
import { EVENT_NAME, trackingAuth } from '@/tracking/functions/TrackingAuthentication';

const PopupTriggerPaymentSvod = ({
  title,
  data,
  popupTriggerPayment,
  btnPrimary,
  detailData,
  notHasCloseBtn,
  onClosed,
  isEndFreeTrial,
  trackingData,
  action
}: any) => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const dataPopup = useSelector((state: any) => state?.Popup);
  const { packageId, isRevisePayment } = dataPopup || {};
  const userType = useSelector((state: any) => state?.User?.USER_TYPE);
  const content = useSelector((state: any) => state?.Detail?.GET_CONTENT);

  const contentPopup = dataPopup?.data;
  const flowName = dataPopup?.detailData?.isPremiumPVod ? 'fast_track' : null;

  const dataTracking = {
    contentId:
      dataPopup?.detailData?.type === CONTENT_TYPE.EPISODE
        ? contentPopup?.id //episode data
        : contentPopup?.id,
    contentName:
      dataPopup?.detailData?.type === CONTENT_TYPE.EPISODE
        ? dataPopup?.contentData?.title //episode data
        : dataPopup?.data?.title || content?.title,
    packageName:
      (detailData?.packages && (detailData?.packages[0]?.name || '')) ||
      (contentPopup?.packages && (contentPopup?.packages[0]?.name || '')),
    contentType: getContentTypeText(dataPopup?.detailData?.type),
    packageId:
      dataPopup?.packages ||
      dataPopup?.detailData?.packages?.[0]?.id ||
      dataPopup?.data?.packages?.[0]?.id,
    campaignId: popupTriggerPayment?.campaign_id
  };

  useEffect(() => {
    if (dataPopup?.data?.trialDuration > 0 || isEndFreeTrial) return;

    if (!isEmpty(dataTracking)) {
      revisePaymentLoaded({
        flowName,
        profile,
        packageId,
        dataTracking
      });
    }
  }, []);

  useEffect(() => {
    if (trackingData?.triggerFrom) {
      vipFeatureSubscribePackageLoaded(trackingData);
    }
  }, [trackingData]);

  const onOpenPayment = () => {
    localStorage.setItem('currentAuthFlow', 'payment_trigger');

    if (trackingData?.contentId) {
      qualitySubSubscribePackageButtonSelected({
        ...trackingData
      });
    }
    if (isRevisePayment) {
      revisePaymentSelected({
        profile,
        packageId,
        dataTracking
      });
    }
    if (typeof action?.func === 'function') {
      action.func();
      closePopup();
    } else {
      router.push(PAGE.PAYMENT);
    }
  };

  const closePopup = () => {
    dispatch(openPopup());
    if (typeof action?.closed === 'function') {
      action.closed();
    }
  };

  const onCloseTracking = () => {
    if (isEndFreeTrial) {
      svodTrialCancelButtonSelected({
        triggerFrom: VALUE.SVOD_TRIAL_DIALOG,
        userType: userType?.userType,
        contentId: content?.id,
        contentName: content?.title
      });
    }
    closePopup();
  };

  const handleClosePopup = () => {
    if (packageId && dataTracking) {
      revisePaymentClosed({
        profile,
        packageId,
        dataTracking
      });
    }

    if (trackingData?.triggerFrom) {
      vipFeatureSubscribePackageCancelButtonSelected(trackingData);
    }

    if (typeof onClosed === 'function') return onClosed();
    return closePopup();
  };

  const onClickFooter = () => {
    dispatch(openPopup());
    const remakeDestination = encodeURIComponent(router?.asPath);
    router.push(
      `/auth/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${TYPE_TRIGGER_AUTH.CONTENT}`
    );
    ConfigLocalStorage.remove(LocalStorage.RE_LOGIN_PARAMS);
    localStorage.setItem('currentAuthFlow', 'payment_trigger');

    if (contentPopup) {
      const flowName =
        (contentPopup?.forceLogin === 1 && contentPopup?.triggerLoginDuration) ||
        contentPopup?.trialDuration
          ? VALUE.SVOD_TRIAL_TRIGGER
          : trackingData?.triggerFrom
          ? VALUE.VIP_FEATURE_TRIGGER
          : VALUE.SVOD_TRIGGER;
      trackingAuth({
        event: EVENT_NAME.LOGIN_BUTTON_SELECTED,
        flowName,
        content: contentPopup,
        isAuto: false,
        isUseFlowAuthenProperty: true
      });
    }
  };

  const renderButtonGroup = () => {
    if (isEndFreeTrial) {
      svodTrialSubscribePackageButtonLoaded({
        triggerFrom: VALUE.SVOD_TRIAL_DIALOG,
        userType: userType?.userType,
        contentId: content?.id,
        contentName: content?.title
      });
    }

    return (
      <div className="button-group child-auto space-y-2 md:space-y-4">
        <div className="bg-gradient-to-r from-[#F5E8B7] to-[#F4ED9D] p-[1px] mx-auto rounded-full max-w-full">
          <button
            className={modalStyleModule.triggerPaymentSvodButton}
            type="button"
            onClick={onOpenPayment}
          >
            <span className={modalStyleModule.triggerPaymentSvodButtonText}>
              {popupTriggerPayment?.subscribe_text || btnPrimary}
            </span>
            <NewIcon
              iconName={'vie-chevron-right-r-medium'}
              iSpClassName="flex w-[24px] h-[24px] items-center justify-center text-black"
            />
          </button>
        </div>
        {renderFooter()}
      </div>
    );
  };

  const renderFooter = () => {
    if (!popupTriggerPayment || profile?.id) return null;
    return (
      <div className={classNames('text-center w-full')}>
        <span
          className={`text-center text text-gray239 text-[14px] leading-[18px] font-medium ${modalStyleModule.textMuted}`}
        >
          Nếu bạn đã đăng ký gói, vui lòng{' '}
          <span onClick={onClickFooter} className="font-[700]">
            Đăng nhập
          </span>
        </span>
      </div>
    );
  };

  const renderBody = () => (
    <div className={modalStyleModule.triggerPaymentSvod}>
      <div className={classNames(modalStyleModule.triggerPaymentSvodHead)}>
        <div className={modalStyleModule.triggerPaymentSvodSubTitle}>
          {popupTriggerPayment?.title || title}
        </div>
        <div className={modalStyleModule.triggerPaymentSvodTitle}>
          {data?.seasonName || data?.titleSeries || data?.title}
        </div>
      </div>
      <div className={modalStyleModule.triggerPaymentSvodBody}>
        <div className="flex justify-center">
          <div className={modalStyleModule.image}>
            <Image
              src={
                data?.images?.thumbnailBig ||
                data?.images?.thumbnail ||
                data?.imgSrc ||
                data?.imgThumb
              }
              alt={data?.title}
              className="w-full h-auto rounded-[10px]"
            />
          </div>
        </div>
        <PackageBenefitList data={popupTriggerPayment} />
        <PricePopupInfo data={popupTriggerPayment} />
        {renderButtonGroup()}
        {/* {renderFooter()} */}
        {popupTriggerPayment?.note && (
          <p className={`${modalStyleModule.note} overflow-hidden text-ellipsis line-clamp-2`}>
            {popupTriggerPayment?.note}
          </p>
        )}
      </div>
    </div>
  );

  if (!popupTriggerPayment || isEmpty(popupTriggerPayment)) return null;

  return (
    <Modal
      classModal="z-[99999]"
      className={classNames(modalStyleModule.triggerPaymentPopupSvod)}
      alignment={!isMobile && 'top-[56px] relative'}
      renderBody={renderBody}
      notClosedButton={notHasCloseBtn}
      onClosed={handleClosePopup}
      onCloseTracking={onCloseTracking}
      modalMobile={isMobile}
      bodyClass={modalStyleModule.body}
      wrapperClassname={
        isMobile
          ? ' !bg-[#171717]'
          : '!pt-0 !border-[1px] !border-[#A8860C] !border-solid !bg-[#171717]'
      }
    />
  );
};

export default PopupTriggerPaymentSvod;
