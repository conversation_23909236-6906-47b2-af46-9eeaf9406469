import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { POPUP, CONFIG_KEY } from '@constants/constants';
import UserApi from '@apis/userApi';
import { handleMasterPlayer } from '@helpers/common';
import LocalStorage from '@config/LocalStorage';
import { setToast } from '@actions/app';
import { openPopup } from '@actions/popup';
import { getProfile } from '@actions/profile';
import { segmentEvent } from '@tracking/TrackingSegment';
import { NAME, PROPERTY } from '@config/ConfigSegment';
import { TEXT } from '@constants/text';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import Modal from '../basic/Modal';

const PopupFavoriteList = () => {
  const dispatch = useDispatch();
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const { deviceModel, deviceName, deviceType } = useSelector((state: any) => state?.App || {});
  const [itemActive, setItemActive] = useState([]);
  const [state, setState] = useState<any>({
    data: [],
    isSelected: false
  });

  useEffect(() => {
    UserApi.getConfig({ key: CONFIG_KEY.PERSONAL_GENRE }).then((res) => {
      setState({ data: res });
    });
  }, []);

  const onOpenSortFavoriteList = (params: any) => {
    dispatch(openPopup({ name: POPUP.NAME.SORT_FAVORITE_LIST, ...params }));
  };

  const handleSelect = (index: any, itemData: any) => {
    const newActiveArray: any = [...(itemActive || [])];
    const idx = (itemActive || []).findIndex((it: any) => it?.id === itemData?.id);
    const isExist = (itemActive || []).findIndex((it: any) => it?.id === itemData?.id) > -1;
    if (isExist) {
      newActiveArray.splice(idx, 1);
    } else {
      newActiveArray.push(itemData);
      if (newActiveArray.length > 3) newActiveArray.shift();
    }
    setItemActive(newActiveArray);
  };

  const onStartView = () => {
    const activeArray: any = (itemActive || []).map((item: any, idx: any) => ({
      ...item,
      odr: idx + 1
    }));
    segmentEvent(NAME.BUTTON.START_TRIAL_WATCHING, {
      [PROPERTY.GERNE_NAME_SUBMITTED]: (activeArray || []).map((it: any) => it.name).join(', ')
    });
    UserApi.userTrialApp(activeArray).then((res) => {
      if (res?.success && !profile && itemActive.length > 0) {
        ConfigLocalStorage.set(LocalStorage.TRIAL_APP, 'TRIAL_APP');
        ConfigLocalStorage.set(LocalStorage.USER_GUEST, 'USER_GUEST');
        onOpenSortFavoriteList({ itemActive });
        return;
      }
      dispatch(openPopup({}));
      handleMasterPlayer();
      ConfigLocalStorage.set(LocalStorage.TRIAL_APP, 'TRIAL_APP');
      dispatch(getProfile({ deviceModel, deviceName, deviceType }));
      dispatch(setToast({ message: TEXT.VIEON_WELCOME }));
    });
  };

  const renderBody = () => (
    <div className="block block--for-dark">
      <h2 className="title text-center">{TEXT.POPUP_PERSONAL_HOME_LIKE_TITLE}</h2>
      <p className="text text-center text-muted">{TEXT.PERSONAL_HOME_PAGE}</p>
      <div className="card-group col-large-3 col-x-small-2 col-small-2 col-medium-3 group-margin-x-6 group-margin-y-6">
        {state.data &&
          (state.data || []).map((item: any, index: any) => {
            let itemClass = 'card card--vod card--horizontal card--vod-favorite ';
            const isExist = (itemActive || []).findIndex((it: any) => it?.id === item?.id) > -1;
            if (isExist) itemClass += ' active';
            return (
              <div
                className={itemClass}
                id={item.id}
                key={index}
                onClick={() => handleSelect(index, item)}
              >
                <div className="card__thumbnail">
                  <div className="card__thumbnail-loader overflow ratio-1-1">
                    <img className="card__thumb-img" src={item.image} alt="Mo ta" />
                  </div>
                </div>
                <div className="card__section">
                  <div className="card__title">{item.name}</div>
                </div>
              </div>
            );
          })}
      </div>
      <div className="button-group child-auto">
        <button
          className="button button--light button--large"
          onClick={onStartView}
          title={TEXT.START_VIEW}
        >
          <span className="text">{TEXT.START_VIEW}</span>
        </button>
      </div>
    </div>
  );

  return (
    <Modal className="green-line modal--favorite-list" renderBody={renderBody} notClosedButton />
  );
};

export default PopupFavoriteList;
