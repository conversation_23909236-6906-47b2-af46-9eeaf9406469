import React, { useEffect, useState } from 'react';
import isEmpty from 'lodash/isEmpty';
import { useDispatch, useSelector } from 'react-redux';
import Modal from '@components/basic/Modal';
import Button from '@components/basic/Buttons/Button';
import Image from '@components/basic/Image/Image';
import { TEXT } from '@constants/text';
import { openPopup } from '@actions/popup';
import { setToast, setLoading } from '@actions/app';
import SvgIcon from '@components/basic/Icon/SvgIcon';
import { ERROR_CODE, HTTP_CODE, ICON_KEY, PAGE } from '@constants/constants';
import { useVieRouter } from '@customHook';
import MultiProfileApi from '@apis/MultiProfile';
import {
  clearRestrictedListOfKids,
  getRestrictedListOfKids,
  isRemoveContentInTitleRestrictionFlow
} from '@actions/multiProfile';
import styles from './Modal.module.scss';
import { ACTION_TYPE, createAction } from '@actions/actionType';

const PopupTitleRestrict = ({ contentId }: any) => {
  const [stateSelected, setStateSelected] = useState<any>([]);
  const [listOfKids, setListOfKids] = useState<any>([]);
  const [isChangedState, setChangedState] = useState(false);
  const [warningMessage, setWarningMessage] = useState<any>('');

  const router = useVieRouter();
  const dispatch = useDispatch();
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const isInRemoveContentFlowRestriction = useSelector(
    (state: any) => state?.MultiProfile?.titleRestriction?.isInRemoveContentFlow
  );
  const restrictedListOfKids =
    useSelector((state: any) => state?.MultiProfile?.titleRestriction?.restrictedListOfKids) || {};
  const { isMobile } = useSelector((state: any) => state?.App) || false;

  const handleClose = () => {
    if (!isChangedState) {
      if (!isEmpty(stateSelected)) {
        setChangedState(true);
      } else {
        dispatch(openPopup({}));
      }
    } else {
      setChangedState(false);
    }
  };

  const handleSelectProfile = (clickedItem: any) => {
    if (clickedItem.enabled) {
      if (warningMessage) {
        setWarningMessage('');
      }
      setListOfKids((prevSelected: any) =>
        prevSelected.map((item: any) => {
          if (item.id === clickedItem.id) {
            const updatedItem = { ...item, selected: !item.selected };
            return updatedItem;
          }
          return item;
        })
      );

      setStateSelected((prevSelectedItems: any) => {
        const selectedItem = restrictedListOfKids?.items?.find(
          (item: any) => item.id === clickedItem.id
        );

        if (!selectedItem) {
          return prevSelectedItems;
        }

        let updatedItems: any = prevSelectedItems ? [...prevSelectedItems] : [];
        const existingItemIndex = updatedItems.findIndex((item: any) => item.id === clickedItem.id);

        if (existingItemIndex !== -1) {
          // If the profile is already in the array, remove it
          updatedItems = updatedItems.filter((item: any) => item.id !== clickedItem.id);
        } else {
          // If the profile is not in the array, add it with the current status
          updatedItems.push(clickedItem);
        }
        return updatedItems;
      });
    }
  };

  const handleSubmit = () => {
    if (isChangedState) {
      dispatch(openPopup({}));
      return;
    }

    if (restrictedListOfKids?.caption) {
      router.push(PAGE.TITLE_RESCTRICTION);
      dispatch(isRemoveContentInTitleRestrictionFlow(true));
      dispatch(setLoading(true));
      return;
    }

    const data = stateSelected.map((selectedItem: any) => ({
      profileId: selectedItem.id,
      contents: [
        {
          id: contentId,
          status: !selectedItem.selected
        }
      ]
    }));

    MultiProfileApi.updateRestrictedContent(data).then((res) => {
      const { code, message, result } = res?.data;
      if (res?.httpCode === HTTP_CODE.OK_200) {
        if (code === ERROR_CODE.CODE_0) {
          dispatch(createAction(ACTION_TYPE.GET_RESTRICTED_LIST_OF_KIDS, result));
          dispatch(setToast({ message: TEXT.MESSAGE_RESTRICTED_SUCCESS }));
          dispatch(openPopup({}));
        } else if (code === ERROR_CODE.CODE_2201) {
          dispatch(setToast({ message: TEXT.MESSAGE_JUST_UPDATED_LIST }));
          dispatch(openPopup({}));
        } else if (code === ERROR_CODE.CODE_2202 || code === ERROR_CODE.CODE_2200) {
          dispatch(getRestrictedListOfKids({ contentId }));
          setWarningMessage(message);
        } else {
          dispatch(setToast({ message: message || TEXT.MESSAGE_RESTRICTED_FAIL }));
        }
      } else {
        dispatch(setToast({ message: TEXT.MESSAGE_RESTRICTED_FAIL }));
        dispatch(openPopup({}));
      }
    });
  };

  useEffect(() => {
    if (profile?.id && contentId) {
      setListOfKids(restrictedListOfKids?.items);
      if (restrictedListOfKids?.caption) {
        setWarningMessage(restrictedListOfKids?.caption);
      }
    }
  }, [restrictedListOfKids?.items, restrictedListOfKids?.caption, profile?.id, contentId]);

  useEffect(() => {
    if (profile?.id) {
      dispatch(getRestrictedListOfKids({ contentId }));
    }
    return () => {
      dispatch(clearRestrictedListOfKids());
    };
  }, [contentId, profile?.id, dispatch]);

  useEffect(
    () => () => {
      if (isInRemoveContentFlowRestriction) {
        dispatch(isRemoveContentInTitleRestrictionFlow(false));
      }
    },
    []
  );

  const renderHeader = () => (
    <div className="modal-header">
      <h3 className="title text-[20px] md:!text-[24px] text-center">
        {isChangedState ? TEXT.NOTI_CHANGED_DATA : TEXT.TITLE_RESTRICTION}
      </h3>
    </div>
  );

  const renderBody = () =>
    isChangedState ? (
      <div className="block block--for-dark block--notify">
        <div
          className="text text-center text-large-up-16 text-14 text-muted"
          dangerouslySetInnerHTML={{ __html: TEXT.NOTI_CHANGED_CONTENT }}
        />
      </div>
    ) : (
      <>
        <div
          className="!p-t !pb-4 md:!pb-6 text text-center text-large-up-18 text-14 text-muted text-medium"
          dangerouslySetInnerHTML={{ __html: TEXT.CHOOSE_RESTRICTION }}
        />
        <article className={styles['modal-title-restriction']}>
          {(!isEmpty(listOfKids) ? listOfKids : restrictedListOfKids?.items)?.map((item: any) => {
            const { enabled, name, avatarUrl, caption, id, selected } = item || {};
            return (
              <div
                key={id}
                className={`${styles['profile-item']} ${enabled ? '' : styles.disabled}`}
              >
                <div className={`${styles.checkbox} checkbox-custom`}>
                  <label htmlFor={`${id}_checkbox`} className={styles.label}>
                    <div className="flex items-center">
                      <div className={styles.box}>
                        <Image src={avatarUrl} alt={name} className="rounded-full relative" />
                        <span className={styles['icon-kid']}>
                          <SvgIcon isActive type={ICON_KEY.KID_SOLID} />
                        </span>
                      </div>
                      <div className={styles.info}>
                        <span className={styles.name}>{name}</span>
                        {caption && <p className={styles.note}>{caption}</p>}
                      </div>
                    </div>
                  </label>
                  <input
                    disabled={!enabled}
                    onChange={() => handleSelectProfile(item)}
                    id={`${id}_checkbox`}
                    type="checkbox"
                    checked={selected}
                    className="mb-0"
                  />
                  <span className="checkmark text-white absolute right-4 !left-[unset] !top-1/2 !-translate-y-1/2 z-[-1] md:!h-5 md:!w-5 !w-4 !h-4" />
                </div>
              </div>
            );
          })}
          {warningMessage && (
            <div className={styles['warning-text']}>
              <SvgIcon isActive={!isMobile} type={ICON_KEY.WARNING} />
              <span className="pl-2 md:pl-4 !text-[11px] md:!text-sm text-medium">
                {warningMessage}
              </span>
            </div>
          )}
        </article>
      </>
    );

  const renderFooter = () => (
    <div className="button-group child-auto padding-medium-down-bottom-16 padding-medium-up-bottom-32 pt-5 md:pt-0 lg:pt-1 md:!mt-6 lg:!mt-0">
      <Button
        className="button button--dark-glass button--xlarge-up button--medium hollow"
        title={isChangedState ? TEXT.SKIP : TEXT.CANCEL}
        subTitle={isChangedState ? TEXT.SKIP : TEXT.CANCEL}
        onClick={handleClose}
      />
      <Button
        className="button button--light button--xlarge-up button--medium"
        title={
          isChangedState
            ? TEXT.ACCEPT
            : restrictedListOfKids?.caption
            ? TEXT.DELELTE_CONTENT
            : TEXT.SAVE
        }
        subTitle={
          isChangedState
            ? TEXT.ACCEPT
            : restrictedListOfKids?.caption
            ? TEXT.DELELTE_CONTENT
            : TEXT.SAVE
        }
        onClick={handleSubmit}
        disabled={isEmpty(stateSelected) && !restrictedListOfKids?.caption}
      />
    </div>
  );
  return (
    <Modal
      className="green-line"
      renderHeader={renderHeader}
      renderBody={renderBody}
      renderFooter={renderFooter}
      classCustom="m-b m-t2 text-14 text-large-up-16"
      notClosedButton
      bodyClass={isChangedState ? '!pt-0 md:!pt-3 modal-body' : '!pt-0 modal-body'}
    />
  );
};

export default React.memo(PopupTitleRestrict);
