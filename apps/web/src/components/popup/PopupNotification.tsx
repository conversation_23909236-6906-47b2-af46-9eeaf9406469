import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import {
  encodeParamDestination,
  loadScript,
  openAppMobile,
  queryStringEncoding,
  removeAccessToken,
  replace<PERSON>ey,
  setVideoPlay
} from '@helpers/common';
import { openPopup } from '@actions/popup';
import { resetResultForm, setLobbyStep, updateAgreement } from '@actions/multiProfile';
import {
  CONTENT_TYPE,
  GG_LIBRARY,
  LOBBY_PROFILE_STEP,
  LOCATION,
  PAGE,
  PERMISSION,
  POPUP
} from '@constants/constants';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import { GG_CLIENT_ID } from '@config/ConfigEnv';
import { segmentEvent, segmentRegistrationConversionEvents } from '@tracking/TrackingSegment';
import { confirmOver18, confirmUnder18 } from '@tracking/functions/TrackingMultiProfileLobby';
import {
  dialogLogoutKidModeLoad,
  dialogLogoutKidModeAccept,
  dialogLogoutKidModeClose,
  dialogNotSpendForKidLoad,
  dialogNotSpendForKidReturnHome,
  dialogCannotAccessContentLoad,
  dialogCannotAccessContentClose
} from '@tracking/functions/TrackingMultiProfileChoose';
import TrackingPlayer from '@tracking/functions/TrackingPlayer';
import { NAME, PROPERTY, VALUE } from '@config/ConfigSegment';
import LocalStorage from '@config/LocalStorage';
import { moeDestroySession } from '@tracking/TrackingMoEngage';
import {
  svodTrialSubscribePackageButtonSelected,
  svodTrialCancelButtonSelected,
  qualitySubSubscribePackageButtonSelected
} from '@tracking/functions/TrackingPaymentConversion';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import Button from '@components/basic/Buttons/Button';
import {
  lockedAccountAccept,
  lockedAccountDialogLoaded
} from '@tracking/functions/TrackingAccountDeletion';
import TrackingMWebToApp from '@tracking/functions/TrackingMWebToApp';
import {
  revisePaymentLoaded,
  revisePaymentSelected,
  revisePaymentClosed
} from '@tracking/functions/TrackingRevisePayment';
import UserApi from '@apis/userApi';
import { usePorTrait, useVieRouter, useViewport } from '@customHook';
import { handleTrackingDialogMWebToApp } from '@services/trackingServices';
import { trackingMultiProfileClose } from '@tracking/functions/TrackingEditMultiProfile';
import { multiProfileAddProfileClose } from '@tracking/functions/TrackingAddMultiProfile';
import { TEXT } from '@constants/text';
import Image from '@components/basic/Image/Image';
import modalStyleModule from './Modal.module.scss';
import Modal from '../basic/Modal';
import { getContentTypeText } from '@/helpers/utils';

declare const window: any;

const PopupNotification = ({
  popupName,
  image,
  imageDefault,
  title,
  subTitle,
  description,
  mobileDescription,
  action,
  noControl,
  btnPrimary,
  btnSignIn,
  btnSignUp,
  btnSignUpPrimary,
  btnSkip,
  btnBack,
  buttonWatchNow,
  btnTrial,
  btnRetry,
  btnSignOut,
  btnClosePrimary,
  notHasCloseBtn,
  subDescription,
  footerActionIsSignIn,
  isEndFreeTrial,
  retryAction,
  segmentParams,
  imgTransparent,
  greenLineNotInImage,
  yellowLine,
  btnMWebToApp,
  btnBlockAccount,
  btnBackHome,
  btnConfirm,
  gotoLocal,
  gotoGlobal,
  onConfirm,
  btnOverEighteen,
  btnUnderEighteen,
  packageName,
  isEndTimeWatchTrial,
  contentType,
  lobbyType,
  isKid,
  btnClose,
  btnReturn,
  btnStartUpdate,
  pvodInfo,
  productNameMsg,
  isEndScreenVod,
  data,
  imageMobile,
  imageBenefitTriggerPayment,
  trackingData,
  detailData,
  popupTriggerPayment
}: any) => {
  const dispatch = useDispatch();
  const { appDownload } = useSelector((state: any) => state?.App?.webConfig) || {};
  const router = useVieRouter();
  const { deviceId, geoCheck, isMobile } = useSelector((state: any) => state?.App || {});
  const userType = useSelector((state: any) => state?.User?.USER_TYPE);
  const content = useSelector((state: any) => state?.Detail?.GET_CONTENT);
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const profileUserType = useSelector((state: any) => state?.User?.USER_TYPE);
  const viewPort = useViewport();
  const isMobileViewPort = viewPort.width < 768;
  const timerRef = useRef<any>(null);
  const { porTrait } = usePorTrait();
  const [newDesc, setNewDesc] = useState(description);
  const dataPopup = useSelector((state: any) => state?.Popup);
  const { packageId, isRevisePayment } = dataPopup || {};
  const [interactive, setInteractive] = useState<any>(null);
  const { trialDuration, type, isTriggerToApp, numberTrialEpisode } = content || {};
  const returnUrl = ConfigLocalStorage.get(LocalStorage.FROM_URL);
  const returnUrlFastTrack = ConfigLocalStorage.get(LocalStorage.FROM_URL_FAST_TRACK);
  const parseMobileDescription = useMemo(() => {
    let newSubstr = '';
    if (isTriggerToApp && trialDuration && isMobile && type === CONTENT_TYPE.MOVIE) {
      newSubstr = `${trialDuration} phút`;
      return mobileDescription?.replace('{trial_value}', newSubstr);
    }
    if (packageName && mobileDescription) {
      return replaceKey(mobileDescription, 'packageName', packageName);
    }
    return mobileDescription;
  }, [trialDuration, type, mobileDescription, packageName]);
  const isGeoCheckValid = geoCheck?.geo_country === LOCATION.VIETNAM && geoCheck?.geo_valid;

  const { eventNameDialogLoad, eventNameDialogTouch, eventNameDialogClose, flowName } = useMemo(
    () =>
      handleTrackingDialogMWebToApp({
        popupName,
        porTrait,
        numberTrialEpisode
      }),
    [popupName, porTrait, numberTrialEpisode]
  );

  const flowNameTracking = dataPopup?.permisssion === PERMISSION.CAN_WATCH ? 'fast_track' : null;

  useEffect(() => {
    if (packageName && description) {
      setNewDesc(replaceKey(description, 'packageName', packageName));
    }
  }, [packageName, description]);

  useEffect(() => {
    if ((pvodInfo || productNameMsg) && description) {
      setNewDesc(
        replaceKey(description, 'content', pvodInfo?.bizInfo?.productNameMsg || productNameMsg)
      );
    }
  }, [packageName, description, pvodInfo, productNameMsg]);
  useEffect(() => {
    if (popupName === POPUP.NAME.BLOCK_ACCOUNT_DELETE) {
      lockedAccountDialogLoaded();
    } else if (
      popupName === POPUP.NAME.KID_LIMITED_PAGE_DIALOG ||
      popupName === POPUP.NAME.KID_LIMITED_CONTENT_DIALOG
    ) {
      dialogNotSpendForKidLoad();
    } else if (popupName === POPUP.NAME.WARNING_KID_CHANGE_PROFILE_NON_KID) {
      dialogLogoutKidModeLoad();
    } else if (
      popupName === POPUP.NAME.KID_ACCESS_SVOD ||
      popupName === POPUP.NAME.KID_ACCESS_TVOD
    ) {
      dialogCannotAccessContentLoad();
    } else if (popupName === POPUP.NAME.REQUEST_REGISTER_CONVERSION) {
      segmentRegistrationConversionEvents({
        loaded: true,
        isEndTimeWatchTrial,
        contentType
      });
    } else if (popupName === POPUP.NAME.KICKED_DEVICE) {
      segmentEvent(NAME.LOGOUT, {}, true);
      moeDestroySession();
      removeAccessToken();
      handleRemoveStorage();
    } else if (isRevisePayment) {
      const contentPopup = dataPopup?.data;
      const dataTracking = {
        contentId: contentPopup?.id,
        contentName: contentPopup?.title,
        packageName:
          (detailData?.packages && (detailData?.packages[0]?.name || '')) ||
          (contentPopup?.packages && (contentPopup?.packages[0]?.name || '')),
        contentType: getContentTypeText(dataPopup?.contentType || contentPopup?.type),
        packageId: contentPopup?.packages && (contentPopup?.packages[0]?.id || ''),
        campaignId: popupTriggerPayment?.campaign_id
      };
      revisePaymentLoaded({
        flowName: flowNameTracking,
        profile,
        packageId,
        dataTracking
      });
    }
  }, [popupName]);

  useEffect(() => {
    if (
      (popupName === POPUP.NAME.CONVERT_MOBILE_WEB_TO_APP ||
        popupName === POPUP.NAME.MOBILE_WEB_ONLY_APP) &&
      eventNameDialogLoad &&
      flowName
    ) {
      TrackingMWebToApp.movieMWebToAppDialog({
        eventName: eventNameDialogLoad,
        flowName
      });
    }
  }, [popupName, eventNameDialogLoad, flowName]);

  useEffect(() => {
    document.addEventListener('scroll', handleInteractive);
    document.addEventListener('click', handleInteractive);
    document.addEventListener('mousedown', handleInteractive);
    document.addEventListener('touchstart', handleInteractive);
    return () => {
      document.removeEventListener('scroll', handleInteractive);
      document.removeEventListener('click', handleInteractive);
      document.removeEventListener('mousedown', handleInteractive);
      document.removeEventListener('touchstart', handleInteractive);
      if (timerRef.current) clearTimeout(timerRef.current);
      const scriptTag = document.querySelector(`script[src="${GG_LIBRARY}"]`);
      if (scriptTag) document.body.removeChild(scriptTag);
    };
  }, []);

  const handleInteractive = (e: any) => {
    if (e) setInteractive(true);
  };

  const openPopupLogin = () => {
    if (popupName === POPUP.NAME.FIRST_LOGIN) {
      segmentEvent(NAME.BUTTON.REGISTRATION, {
        [PROPERTY.CURRENT_PAGE]: window.location.href,
        [PROPERTY.POPUP_NAME]: VALUE.POPUP_NAME.TRIAL
      });
    }
    dispatch(openPopup());
    let authTrigger = '';
    if (
      popupName === POPUP.NAME.NON_LOGIN_QUALITY ||
      popupName === POPUP.NAME.NON_LOGIN_SUB_AUDIO
    ) {
      authTrigger = TYPE_TRIGGER_AUTH.SETTING;
    } else if (
      (popupName || '').includes('package') ||
      popupName === POPUP.NAME.NON_LOGIN_END_SVOD_TRIAL ||
      popupName === POPUP.NAME.NON_LOGIN_TRIAL_GLOBAL
    ) {
      authTrigger = TYPE_TRIGGER_AUTH.PAYMENT_LOGIN;
    }
    const remakeDestination = encodeParamDestination(router?.asPath);
    router.push(
      `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${authTrigger}`
    );
    closePopup({ isLogin: true });
  };

  const openPopupRequestFavoriteList = () => {
    segmentEvent(NAME.BUTTON.TRIAL);
    dispatch(openPopup({ name: POPUP.NAME.REQUEST_FAVORITE_LIST }));
  };

  const openPopupRegister = () => {
    let authTrigger = '';

    // tracking segment
    if (popupName === POPUP.NAME.REQUEST_REGISTER_CONVERSION) {
      authTrigger = TYPE_TRIGGER_AUTH.REQUEST_REGISTER_CONVERSION;
      localStorage.setItem('currentAuthFlow', 'registration_trigger');
      segmentRegistrationConversionEvents({
        isEndTimeWatchTrial,
        contentType
      });
    }
    dispatch(openPopup());
    if (
      popupName === POPUP.NAME.NON_LOGIN_QUALITY ||
      popupName === POPUP.NAME.NON_LOGIN_SUB_AUDIO
    ) {
      authTrigger = TYPE_TRIGGER_AUTH.SETTING;
    }

    const remakeDestination = encodeParamDestination(router?.asPath);
    router.push(
      `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${authTrigger}`
    );
    closePopup({ isLogin: false });
  };

  const onOpenPayment = () => {
    localStorage.setItem('currentAuthFlow', 'payment_trigger');
    if (isEndFreeTrial) {
      svodTrialSubscribePackageButtonSelected({
        triggerFrom: VALUE.SVOD_TRIAL_DIALOG,
        userType: userType?.userType,
        contentId: content?.id,
        contentName: content?.title
      });
    }
    if (trackingData?.contentId) {
      qualitySubSubscribePackageButtonSelected({
        ...trackingData
      });
    }
    if (isRevisePayment) {
      const contentPopup = dataPopup?.data;
      const dataTracking = {
        contentId: contentPopup?.id,
        contentName: contentPopup?.title,
        packageName: detailData?.packages && (detailData?.packages[0]?.name || '')
      };
      revisePaymentSelected({
        profile,
        packageId,
        dataTracking
      });
    }
    if (typeof action?.func === 'function') {
      action.func();
      closePopup({ isLogin: false });
    } else {
      router.push(PAGE.PAYMENT);
    }
  };

  const onMWebToApp = () => {
    TrackingMWebToApp.movieMWebToAppDialog({
      eventName: eventNameDialogTouch,
      flowName
    });
    if (timerRef?.current) clearTimeout(timerRef.current);
    timerRef.current = setTimeout(() => {
      openAppMobile(appDownload);
    }, 200);
  };

  const closePopup = ({ isLogin }: any) => {
    if (isEndFreeTrial) {
      svodTrialCancelButtonSelected({
        userType: userType?.userType
      });
    }
    if (isRevisePayment) {
      revisePaymentClosed({
        profile,
        packageId
      });
    }

    let isClose = true;
    switch (popupName) {
      case POPUP.NAME.BLOCK_ACCOUNT: {
        removeAccessToken();
        window.location.reload();
        break;
      }
      case POPUP.NAME.REQUEST_REGISTER_CONVERSION: {
        // tracking segment
        segmentRegistrationConversionEvents({
          cancel: true,
          isEndTimeWatchTrial,
          contentType
        });
        break;
      }
      case POPUP.NAME.CONVERT_MOBILE_WEB_TO_APP: {
        TrackingMWebToApp.movieMWebToAppDialog({
          eventName: eventNameDialogClose,
          flowName
        });
        break;
      }
      case POPUP.NAME.MOBILE_WEB_ONLY_APP: {
        TrackingMWebToApp.movieMWebToAppDialog({
          eventName: eventNameDialogClose,
          flowName
        });
        setVideoPlay({ isAll: false });
        break;
      }
      case POPUP.NAME.WARNING_KID_CHANGE_PROFILE_NON_KID: {
        dialogLogoutKidModeClose();
        break;
      }
      case POPUP.NAME.KID_ACCESS_TVOD:
      case POPUP.NAME.KID_ACCESS_SVOD: {
        dialogCannotAccessContentClose();
        break;
      }
      case POPUP.NAME.KID_LIMITED_CONTENT_DIALOG:
      case POPUP.NAME.KID_LIMITED_PAGE_DIALOG: {
        isClose = false;
        break;
      }
      case POPUP.NAME.PVOD_EXPIRED:
      case POPUP.NAME.PVOD_OWNED: {
        router.push('/');
        isClose = false;
        break;
      }

      default:
        break;
    }
    if (isClose) {
      dispatch(
        openPopup({
          noControl,
          isEndFreeTrial,
          backToPlay: true,
          isEndScreenVod
        })
      );
      if (!isLogin) {
        ConfigLocalStorage.remove(LocalStorage.RE_LOGIN_PARAMS);
      }
    }
    if (typeof action?.closed === 'function') {
      action.closed();
    }
  };

  const handleBackHome = () => {
    if (
      popupName === POPUP.NAME.KID_LIMITED_PAGE_DIALOG ||
      popupName === POPUP.NAME.KID_LIMITED_CONTENT_DIALOG
    ) {
      dialogNotSpendForKidReturnHome();
    }
    if (popupName === POPUP.NAME.KICKED_DEVICE) {
      handleSignOut();
      return;
    }
    router.push(PAGE.HOME);
    dispatch(openPopup());
  };

  const onHandleBack = () => {
    window.location = returnUrlFastTrack;
    dispatch(openPopup());
  };
  const onHandleWatchNow = () => {
    window.location = returnUrl;
    dispatch(openPopup());
  };

  const onRetry = () => {
    dispatch(openPopup());
    if (typeof retryAction === 'function') {
      retryAction();
      if (segmentParams) TrackingPlayer.playerErrorRetry(segmentParams);
    }
  };

  const onHandleBlockAccount = async () => {
    await lockedAccountAccept();
    handleSignOut();
  };

  const handleRemoveStorage = async () => {
    ConfigLocalStorage.remove(LocalStorage.LOGIN_TYPE);
    ConfigLocalStorage.remove(LocalStorage.TRIAL_APP);
    ConfigLocalStorage.remove(LocalStorage.GUEST_USER_RETRIEVER);
    ConfigLocalStorage.remove(LocalStorage.RE_LOGIN);
    ConfigLocalStorage.remove(LocalStorage.SKIP_BANNER_FIFTEEN_DAY);
    ConfigLocalStorage.remove(LocalStorage.SKIP_BANNER_ONE_DAY);
    ConfigLocalStorage.remove(LocalStorage.SKIP_BANNER_ONE);
    ConfigLocalStorage.remove(LocalStorage.OFF_BANNER);
    ConfigLocalStorage.remove(LocalStorage.OFF_BANNER_FULL_SCREEN);
    ConfigLocalStorage.remove(LocalStorage.BIND_ACCOUNT);
    ConfigLocalStorage.remove(LocalStorage.USER_JUST_LOGGED);
    ConfigLocalStorage.remove(LocalStorage.ID_TVOD_DATA);
    ConfigLocalStorage.remove(LocalStorage.ID_TVOD_PRE_ORDER_DATA);
    ConfigLocalStorage.remove(LocalStorage.TVOD_CONFIG);
    ConfigLocalStorage.remove(LocalStorage.CHECK_PROFILE_ID);
    ConfigLocalStorage.remove(LocalStorage.DAY_SEGMENT_USER_FULL_SCREEN);
    ConfigLocalStorage.remove(LocalStorage.K_PLUS_VALIDATE);
    ConfigLocalStorage.remove(LocalStorage.QN_TOKEN);
    ConfigLocalStorage.remove(LocalStorage.PROFILE_LIST);
    ConfigLocalStorage.remove(LocalStorage.FROM_URL);
    ConfigLocalStorage.remove(LocalStorage.FROM_URL_FAST_TRACK);
    ConfigLocalStorage.remove(LocalStorage.IS_BANNER_TRIGGER_PVOD);
    ConfigLocalStorage.remove(LocalStorage.LOGGED_SOCIAL_BY_PROVIDER);
    ConfigLocalStorage.remove(LocalStorage.RE_LOGIN_PARAMS);
  };

  const handleSignOut = async () => {
    ConfigLocalStorage.set(LocalStorage.HIDE_MASTHEAD, 1);
    if (profile?.email) {
      loadScript(GG_LIBRARY)
        .then(() => {
          window.google.accounts.id.initialize({
            client_id: GG_CLIENT_ID
          });
          window.google.accounts.id.disableAutoSelect();
          window.google.accounts.id.revoke(profile.email);
        })
        .catch(console.error);
    }
    UserApi.logout().then(() => {
      segmentEvent(NAME.LOGOUT, {}, true);
      moeDestroySession();
      removeAccessToken();
      handleRemoveStorage();
      setTimeout(() => {
        const { query, pathname } = router || {};
        const utmParams = {
          utm_source: query?.utm_source,
          utm_medium: query?.utm_medium,
          utm_campaign: query?.utm_campaign,
          utm_content: query?.utm_content
        };
        const queryString = queryStringEncoding(utmParams);
        if ((pathname || '').includes(PAGE.ZALOPAY)) {
          window.location.href = PAGE.ZALOPAY;
        } else {
          window.location.href = PAGE.HOME + (queryString ? `?${queryString}` : '');
        }
      }, 0);
    });
    UserApi.deleteDeviceToken({
      userId: profile?.id,
      deviceId
    });
  };

  const handleUpdateAgreement = () => {
    confirmOver18({
      flowName: VALUE.MULTI_PROFILE_CONFIRM,
      userType: userType?.userType
    });
    dispatch(updateAgreement(true));
    closePopup({ isLogin: true });
  };

  const handleUnderEighteen = () => {
    confirmUnder18({
      flowName: VALUE.MULTI_PROFILE_CONFIRM,
      userType: userType?.userType,
      isTrackImmediately: true
    });
    handleSignOut();
  };

  const onClickFooter = () => {
    if (footerActionIsSignIn) {
      openPopupRegister();
    } else {
      openPopupLogin();
    }
  };

  const handleConfirm = () => {
    dispatch(openPopup());
    if (typeof onConfirm === 'function') onConfirm({});
    if (popupName === POPUP.NAME.WARNING_KID_CHANGE_PROFILE_NON_KID) {
      dialogLogoutKidModeAccept({
        isTrackImmediately: true
      });
    }
  };

  const gotoLocalGlobal = () => {
    window.location.href = (window.location.origin || '').replace(
      gotoLocal ? '.global' : '.vn',
      gotoLocal ? '.vn' : '.global'
    );
  };

  const handleStartUpdate = () => {
    const remakeDestination = encodeParamDestination(router?.asPath);
    router?.push(
      `${PAGE.USER_UPDATE_PHONE_NUMBER}/?destination=${remakeDestination}&trigger=${TYPE_TRIGGER_AUTH.SOCIAL_UPDATE_PHONE}`
    );
    dispatch(openPopup());
  };

  const handleReturnLobbyView = () => {
    // Tracking close dialog
    if (lobbyType === LOBBY_PROFILE_STEP?.EDIT) {
      trackingMultiProfileClose({
        userType: profileUserType?.userType
      });
    } else {
      multiProfileAddProfileClose({
        flowName: isKid
          ? VALUE.MULTI_PROFILE_ADD_PROFILE_KID
          : VALUE.MULTI_PROFILE_ADD_PROFILE_NON_KID,
        userType: profileUserType?.userType
      });
    }
    dispatch(setLobbyStep());
    dispatch(resetResultForm({ status: lobbyType }));
    closePopup({ isLogin: false });
  };

  const renderCustom = () => {
    if (!image && !imageDefault) return null;
    return (
      <div
        className={`mask mask--dark-backdrop m-b-neg-1 ${
          imgTransparent ? ' img-child-transparent' : ''
        }`}
      >
        <div className="mask__inner ratio-variant-1d8">
          <div className="mask__img absolute">
            <img src={image || imageDefault} alt="popup-notification" />
          </div>
        </div>
      </div>
    );
  };

  const renderButtonGroup = () => (
    <div className="button-group child-auto">
      {btnUnderEighteen && (
        <button
          className="button hollow medium button--large"
          type="button"
          onClick={handleUnderEighteen}
        >
          <span className="text text-white">{btnUnderEighteen}</span>
        </button>
      )}
      {btnOverEighteen && (
        <button
          className="button button--light medium button--large"
          type="button"
          onClick={handleUpdateAgreement}
        >
          <span className="text">{btnOverEighteen}</span>
        </button>
      )}
      {btnBackHome && (
        <button
          className="button button--light medium button--large"
          type="button"
          onClick={handleBackHome}
        >
          <span className="text text-center">{btnBackHome}</span>
        </button>
      )}
      {btnSignOut && (
        <button
          className="button hollow button--dark-glass medium button--large"
          type="button"
          onClick={handleSignOut}
        >
          <span className="text text-center">{btnSignOut}</span>
        </button>
      )}
      {btnRetry && (
        <button
          className="button button--light medium button--large"
          type="button"
          onClick={onRetry}
        >
          <span className="text text-center">{btnRetry || TEXT.RETRY}</span>
        </button>
      )}
      {btnTrial && (
        <button
          className="button hollow button--dark-glass medium button--large "
          type="button"
          onClick={openPopupRequestFavoriteList}
        >
          <span className="text text-center">{btnTrial}</span>
        </button>
      )}
      {btnClose && (
        <button
          className={`${modalStyleModule['gray-button']} button hollow button--dark-glass medium button--large`}
          type="button"
          onClick={() => {
            closePopup({ isLogin: false });
          }}
        >
          <span className="text text-center">{btnClose}</span>
        </button>
      )}
      {btnReturn && (
        <button
          className="button button--light medium button--large"
          type="button"
          onClick={handleReturnLobbyView}
        >
          <span className="text text-center">{btnReturn}</span>
        </button>
      )}
      {btnClosePrimary && (
        <button
          className="button button--light medium button--large"
          type="button"
          onClick={() => closePopup({ isLogin: false })}
        >
          <span className="text text-center">{btnClosePrimary}</span>
        </button>
      )}
      {btnSkip && (
        <button
          className="button hollow button--dark-glass medium button--large "
          type="button"
          onClick={() => closePopup({ isLogin: false })}
        >
          <span className="text text-center">{btnSkip}</span>
        </button>
      )}
      {btnBack && (
        <button
          className="button hollow button--dark-glass medium button--large "
          type="button"
          onClick={onHandleBack}
        >
          <span className="text text-center">{btnBack}</span>
        </button>
      )}
      {btnSignUp && isGeoCheckValid && (
        <button
          className="button hollow button--dark-glass medium button--large "
          type="button"
          onClick={openPopupRegister}
        >
          <span className="text text-center">{btnSignUp}</span>
        </button>
      )}
      {btnSignUpPrimary && (
        <button
          id={popupName}
          className="button button--light medium button--large"
          type="button"
          onClick={openPopupRegister}
        >
          <span className="text text-center">{btnSignUpPrimary}</span>
        </button>
      )}
      {btnSignIn && (
        <button
          className="button button--light medium button--large"
          type="button"
          onClick={openPopupLogin}
        >
          <span className="text text-center">{btnSignIn}</span>
        </button>
      )}
      {btnPrimary && (
        <button
          className={classNames(
            'button button--light medium button--xlarge-up',
            imageBenefitTriggerPayment && 'font-bold'
          )}
          type="button"
          onClick={onOpenPayment}
        >
          <span
            className={classNames(
              'text text-center',
              imageBenefitTriggerPayment && '!text-base lg:!text-[21px]'
            )}
          >
            {btnPrimary}
          </span>
        </button>
      )}
      {buttonWatchNow && (
        <button
          className="button button--light medium button--large"
          type="button"
          onClick={onHandleWatchNow}
        >
          <span className="text text-center">{buttonWatchNow}</span>
        </button>
      )}
      {btnMWebToApp && (
        <button
          className="button button--light medium button--large"
          type="button"
          onClick={onMWebToApp}
        >
          <span className="text text-center">{btnMWebToApp}</span>
        </button>
      )}
      {btnBlockAccount && (
        <Button
          className="button button--light medium button--large"
          title={btnBlockAccount}
          onClick={onHandleBlockAccount}
          textClass="text-center"
        />
      )}
      {btnConfirm && (
        <button
          className="button button--light medium button--large"
          type="button"
          onClick={handleConfirm}
        >
          <span className="text text-center">{btnConfirm}</span>
        </button>
      )}
      {(gotoLocal || gotoGlobal) && (
        <button
          className="button button--light medium button--large"
          type="button"
          onClick={gotoLocalGlobal}
        >
          <span className="text text-center">{gotoLocal || gotoGlobal}</span>
        </button>
      )}
      {btnStartUpdate && (
        <button
          className="button button--light medium button--large"
          type="button"
          onClick={handleStartUpdate}
        >
          <span className="text text-center">{btnStartUpdate}</span>
        </button>
      )}
    </div>
  );

  const renderBody = () => (
    <div className="block block--for-dark block--notify">
      <h2 className="title text-center f-medium">
        {title || (popupName === POPUP.NAME.PLAYER_ERROR_NETWORK ? TEXT.NETWORK_ERROR : '')}
      </h2>
      {subTitle && <h4 className="text text-center text-white">{subTitle}</h4>}
      {((!isMobile && description) || (isMobile && parseMobileDescription)) && (
        <p
          className={`text text-center margin-small-up-bottom-12 margin-large-up-bottom-32 text-large-up-16 text-14 ${
            popupName === POPUP.NAME.CONVERT_MOBILE_WEB_TO_APP ||
            popupName === POPUP.NAME.MOBILE_WEB_ONLY_APP
              ? 'text-white'
              : popupName === POPUP.NAME.CONTENT_RESCTRICTED
              ? 'text-[#DEDEDE]'
              : 'text-muted'
          }`}
          dangerouslySetInnerHTML={{
            __html: isMobile ? parseMobileDescription : newDesc || description
          }}
        />
      )}
      {renderButtonGroup()}
    </div>
  );

  const renderBodyTriggerPayment = () => (
    <div className={modalStyleModule.body}>
      <div>
        <Image src={imageBenefitTriggerPayment} alt="movie-image" />
      </div>
      <div className={modalStyleModule.movieInfo}>
        <div className="block md:hidden">
          <div className={modalStyleModule.image}>
            <Image
              src={
                data?.images?.thumbnailBig ||
                data?.images?.thumbnail ||
                data?.imgSrc ||
                data?.imgThumb
              }
              alt={data?.title}
              className="w-full h-auto rounded-[10px]"
            />
          </div>
        </div>
        <div className={modalStyleModule.title}>{title}</div>
        <div className={modalStyleModule.movieTitle}>{data?.titleSeries || data?.title}</div>

        <div className="hidden md:block">
          <div className={modalStyleModule.image}>
            <Image
              src={
                data?.images?.thumbnailBig ||
                data?.images?.thumbnail ||
                data?.imgSrc ||
                data?.imgThumb
              }
              alt={data?.title}
              className="w-full h-auto rounded-[10px]"
            />
          </div>
        </div>
        {renderButtonGroup()}
        {renderFooter()}
      </div>
    </div>
  );

  const renderFooter = () => {
    if (!subDescription || (popupName === POPUP.NAME.PVOD_REGISTER_VIP && profile?.id)) return null;
    return (
      <div
        className={classNames(
          `option-link${isMobile ? ' p-y2' : ' p-y4'}`,
          imageBenefitTriggerPayment ? 'text-center !pt-4 !pb-0' : 'border-top'
        )}
      >
        <span
          className={`text-center text text-gray239 text-16 font-medium ${modalStyleModule.textMuted}`}
          onClick={onClickFooter}
          dangerouslySetInnerHTML={{
            __html: subDescription
          }}
        />
      </div>
    );
  };

  const isNotCancelESC = !!btnBlockAccount;
  const modalClass = classNames(
    'modal--notify modal--notify-login',
    greenLineNotInImage && 'green-line p-t1',
    yellowLine && 'yellow-line p-t1',
    imageBenefitTriggerPayment && modalStyleModule.triggerPaymentPopup
  );
  if (typeof window === 'undefined' || (!window.interacted && !interactive)) return null;

  return (
    <Modal
      className={modalClass}
      renderCustom={imageBenefitTriggerPayment ? null : renderCustom}
      renderBody={imageBenefitTriggerPayment ? renderBodyTriggerPayment : renderBody}
      renderFooter={imageBenefitTriggerPayment ? null : renderFooter}
      notClosedButton={notHasCloseBtn}
      onClosed={() => {
        ConfigLocalStorage.remove('currentContentId');
        closePopup({ isLogin: false });
      }}
      modalMobile={isMobile}
      classModal={
        imageBenefitTriggerPayment
          ? 'flex md:block items-center z-[99999]'
          : isMobile
          ? 'p-l3 p-r3'
          : ''
      }
      isNotCancelESC={isNotCancelESC}
      modalWrapperStyle={
        imageBenefitTriggerPayment
          ? { backgroundImage: `url(${isMobileViewPort ? imageMobile : image})` }
          : {}
      }
      wrapperClassname={classNames(imageBenefitTriggerPayment && '!pt-0')}
    />
  );
};

export default PopupNotification;
