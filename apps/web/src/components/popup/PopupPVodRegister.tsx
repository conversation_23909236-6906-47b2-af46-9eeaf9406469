import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import { GG_LIBRARY } from '@constants/constants';
import { useVieRouter } from '@customHook';
import { openPopup } from '@actions/popup';
import CardImage from '@components/basic/Card/CardImage';
import isEmpty from 'lodash/isEmpty';
import { onOpenPayment } from '@helpers/common';
import {
  revisePaymentSelected,
  revisePaymentClosed
} from '@tracking/functions/TrackingRevisePayment';
import { VALUE } from '@config/ConfigSegment';
import { fastTrackRegisterAtDialog } from '@tracking/functions/TrackingPVod';
import Modal from '../basic/Modal';

declare const window: any;

const PopupPVodRegister = ({
  data,
  title,
  description,
  notHasCloseBtn,
  greenLineNotInImage,
  yellowLine,
  btnPrimary,
  descriptionMobile,
  returnUrlFastTrack,
  returnUrl,
  action,
  pVodInfo
}: any) => {
  const { bizInfo } = pVodInfo || {};
  const { productNameMsg, priceMsg } = bizInfo || {};
  const { images, pvod, contentMsg, expirationMsg } = data || {};
  const dispatch = useDispatch();
  const router = useVieRouter();
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const timerRef = useRef<any>(null);
  const [interactive, setInteractive] = useState<any>(null);
  const parseTitle = useMemo(() => {
    if (isEmpty(contentMsg || bizInfo?.contentMsg)) return title;
    return title
      .replace('{numberEpisode}', contentMsg || bizInfo?.contentMsg)
      .replace('{price}', priceMsg);
  }, [pvod, title, bizInfo]);
  const parseDescription = useMemo(() => {
    const text = isMobile ? descriptionMobile : description;
    if (text && (expirationMsg || bizInfo?.expirationMsg)) {
      return text
        .replace('{expiredDate}', expirationMsg || bizInfo?.expirationMsg)
        .replace('{content}', data?.titleSeries || productNameMsg);
    }
    return text;
  }, [description, descriptionMobile, isMobile, bizInfo]);

  useEffect(() => {
    document.addEventListener('scroll', handleInteractive);
    document.addEventListener('click', handleInteractive);
    document.addEventListener('mousedown', handleInteractive);
    document.addEventListener('touchstart', handleInteractive);
    return () => {
      document.removeEventListener('scroll', handleInteractive);
      document.removeEventListener('click', handleInteractive);
      document.removeEventListener('mousedown', handleInteractive);
      document.removeEventListener('touchstart', handleInteractive);
      if (timerRef.current) clearTimeout(timerRef.current);
      const scriptTag = document.querySelector(`script[src="${GG_LIBRARY}"]`);
      if (scriptTag) document.body.removeChild(scriptTag);
    };
  }, []);

  const handleInteractive = (e: any) => {
    if (e) setInteractive(true);
  };

  const closePopup = () => {
    // handle tracking
    revisePaymentClosed({
      flowName: VALUE.FAST_TRACK
    });
    dispatch(openPopup());
  };
  const handleRegisterFastTrack = () => {
    // handle tracking click button
    fastTrackRegisterAtDialog({
      id: data?.id,
      title: data?.title
    });
    revisePaymentSelected({
      flowName: VALUE.FAST_TRACK
    });
    if (typeof action?.func === 'function') {
      action.func();
    } else {
      onOpenPayment(router, {
        id: data?.id,
        type: data?.type,
        returnUrl,
        returnUrlFastTrack,
        isPvod: true,
        newTriggerPaymentBuyPackage: {
          isGlobal,
          profileId: profile?.id
        }
      });
    }
  };

  const renderCustom = () => {
    if (!images) return null;

    return (
      <div className="mask mask--overlay">
        <div className="text-center">
          <CardImage images={images} className="w-full" notLazy />
        </div>
      </div>
    );
  };
  const renderBody = () => (
    <div className="block block--for-dark block--notify ddd">
      <h2
        className="title text-center f-medium"
        dangerouslySetInnerHTML={{
          __html: parseTitle
        }}
      />
      <p
        className="text text-center text-white/80 margin-small-up-bottom-12 margin-large-up-bottom-32 text-large-up-16 text-14"
        dangerouslySetInnerHTML={{
          __html: parseDescription
        }}
      />
      <div className="button-group child-auto">
        <button
          className="button button--light medium button--large"
          type="button"
          onClick={handleRegisterFastTrack}
          title={btnPrimary}
        >
          {btnPrimary}
        </button>
      </div>
    </div>
  );

  const modalClass = classNames(
    'modal--notify modal--notify-login',
    greenLineNotInImage && 'green-line p-t1',
    yellowLine && 'yellow-line p-t1'
  );
  if (typeof window === 'undefined' || (!window.interacted && !interactive)) return null;

  return (
    <Modal
      className={modalClass}
      renderCustom={renderCustom}
      renderBody={renderBody}
      notClosedButton={notHasCloseBtn}
      onClosed={closePopup}
      modalMobile={isMobile}
      classModal={isMobile ? 'p-l3 p-r3' : ''}
    />
  );
};

export default PopupPVodRegister;
