import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import { openPopup } from '@actions/popup';
import {
  deleteProfile,
  deleteProfileSuccess as clearResultDeleteProfile,
  setLobbyStep,
  setResultForm
} from '@actions/multiProfile';
import { LOBBY_PROFILE_STEP, PAGE } from '@constants/constants';
import { TEXT } from '@constants/text';
import { setToast } from '@actions/app';
import { removeAccessTokenProfile } from '@helpers/common';
import { useVieRouter } from '@customHook';
import {
  trackingMultiProfileUserDialogDeleteCancel,
  trackingMultiProfileUserDialogDeleteConfirm,
  trackingMultiProfileUserDialogDeleteLoad
} from '@tracking/functions/TrackingEditMultiProfile';
import Modal from '../basic/Modal';
import Button from '../basic/Buttons/Button';
import styles from './Modal.module.scss';

const PopupDeleteProfile = ({
  image,
  title,
  description,
  btnClosePrimary,
  btnDelete,
  btnProfileDeleted,
  yellowLine,
  notHasCloseBtn,
  mobileDescription
}: any) => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const { resultDeleteProfile, resultForm } = useSelector(
    (state: any) => state?.MultiProfile || {}
  );
  const isMobile = useSelector((state: any) => state.App?.isMobile);
  const profileUserType = useSelector((state: any) => state?.User?.USER_TYPE);

  useEffect(() => {
    if (resultDeleteProfile?.success) {
      dispatch(setLobbyStep(LOBBY_PROFILE_STEP.VIEW));
      dispatch(setResultForm({ status: LOBBY_PROFILE_STEP.EDIT }));
      handleClosePopup();
      dispatch(setToast({ message: TEXT.DELETE_PROFILE_SUCCESS, duration: 5000 }));
    }
  }, [resultDeleteProfile?.success]);

  useEffect(() => {
    // Tracking load dialog delete profile
    trackingMultiProfileUserDialogDeleteLoad({
      userType: profileUserType?.userType
    });
    return () => {
      dispatch(clearResultDeleteProfile({}));
    };
  }, []);

  const handleClosePopup = () => {
    dispatch(openPopup());
  };

  const handleCancelPopup = () => {
    // Tracking close dialog delete profile
    trackingMultiProfileUserDialogDeleteCancel({
      userType: profileUserType?.userType
    });
    handleClosePopup();
  };

  const handleDeleteProfile = () => {
    // Tracking dialog confirm delete profile
    trackingMultiProfileUserDialogDeleteConfirm({
      userType: profileUserType?.userType
    });
    if (!resultForm?.id) return;
    dispatch(deleteProfile({ id: resultForm?.id }));
  };

  const handleProfileDeleted = () => {
    handleClosePopup();
    removeAccessTokenProfile();
    router.push(PAGE.LOBBY_PROFILES);
  };

  const renderCustom = () => {
    if (!image) return null;
    return (
      <div className="mask mask--overlay">
        <div className="mask__inner ratio-variant-1d8">
          <div className="mask__img absolute">
            <img src={image} alt="popup-delete-profile" />
          </div>
        </div>
      </div>
    );
  };
  const renderBody = () => (
    <div className="block block--for-dark block--notify">
      {title && <h2 className="title text-center f-medium">{title}</h2>}
      {((!isMobile && description) || (isMobile && mobileDescription)) && (
        <p
          className="text text-center margin-small-up-bottom-12 margin-large-up-bottom-32 text-large-up-16 text-14 text-muted"
          dangerouslySetInnerHTML={{
            __html: isMobile ? mobileDescription : description
          }}
        />
      )}
      <div className="button-group child-auto">
        {btnDelete && (
          <Button
            className={`button hollow button--dark-glass medium button--large ${styles['gray-button']}`}
            onClick={handleDeleteProfile}
            title={btnDelete}
          />
        )}
        {btnClosePrimary && (
          <Button
            className="button button--light medium button--large"
            onClick={handleCancelPopup}
            title={btnClosePrimary}
          />
        )}
        {btnProfileDeleted && (
          <Button
            className="button button--light medium button--large"
            title={btnProfileDeleted}
            onClick={handleProfileDeleted}
            textClass="text-center"
          />
        )}
      </div>
    </div>
  );
  const modalClass = classNames(
    'modal--notify modal--notify-login',
    yellowLine && 'yellow-line p-t1'
  );
  return (
    <Modal
      className={modalClass}
      renderCustom={renderCustom}
      renderBody={renderBody}
      notClosedButton={notHasCloseBtn}
      onClosed={handleClosePopup}
    />
  );
};

export default React.memo(PopupDeleteProfile);
