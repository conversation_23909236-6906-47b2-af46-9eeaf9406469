/* eslint-disable no-useless-escape */
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { openPopup } from '@actions/popup';
import UserApi from '@apis/userApi';
import { POPUP } from '@constants/constants';
import { TEXT } from '@constants/text';
import { NAME, PROPERTY } from '@config/ConfigSegment';
import { segmentEvent } from '@tracking/TrackingSegment';
import Modal from '../basic/Modal';
import InputCustom from '../basic/Input/InputCustom';

// custom hook
function useFormInput(initialValue: any) {
  const [value, setValue] = useState(initialValue);
  const handleChange = (event: any) => {
    setValue(event.target.value);
  };
  return { value, onChange: handleChange };
}

const PopupForgetPassword = ({ isGuestID, noControl, value, isNeedShowLobby }: any) => {
  const dispatch = useDispatch();
  const phoneNumberTemp = useFormInput(value || '');
  const [error, setError] = useState<any>();
  const profile = useSelector((state: any) => state?.Profile?.profile);

  useEffect(() => {
    segmentEvent(NAME.BUTTON.FORGOT_PASS_STARTED, {
      [PROPERTY.REFERRAL]: window.location.href
    });
    window.addEventListener('keydown', onKeyPress);
    // Remove event listeners on cleanup
    return () => {
      window.removeEventListener('keydown', onKeyPress);
    };
  }, []); // Empty array ensures that effect is only run on mount and unmount

  const openPopupNoNumberRegister = (params: any) => {
    dispatch(
      openPopup({
        name: POPUP.NAME.REQUEST_LOGIN_NOT_NUMBER_REGISTER,
        ...params,
        noControl,
        isNeedShowLobby
      })
    );
  };

  const onSubmitSendOTP = (e: any) => {
    e.preventDefault();
    const phoneNumber = phoneNumberTemp.value || e.target.value;
    const message_phone = phoneNumber?.length === 0 ? TEXT.PHONE_REQUIRED : TEXT.PHONE_WRONG;
    const REGEX_PHONE_NUMBER = /^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$/;
    if (
      REGEX_PHONE_NUMBER.test(phoneNumber) === false ||
      phoneNumber?.length <= 9 ||
      phoneNumber?.length >= 12
    ) {
      setError(message_phone);
      return;
    }
    UserApi.forgetPassword(phoneNumber).then((resp) => {
      const { register_session_id } = resp?.data || {};
      const isNoExist = resp?.data?.data === 'No_exist';
      const error = resp?.data?.message;
      const actionName = POPUP.NAME.FORGET_PASSWORD;
      if (isNoExist) {
        openPopupNoNumberRegister({ phoneNumber, actionName });
      }
      if (error) {
        setError(error || TEXT.MSG_ERROR);
      } else {
        openConfirmOtpForgetPassword({ phoneNumber, register_session_id });
      }
    });
  };

  const openPopupLogin = (params: any) => {
    dispatch(
      openPopup({
        name: POPUP.NAME.LOGIN,
        ...params,
        isGuestID,
        noControl,
        isNeedShowLobby
      })
    );
  };

  const openConfirmOtpForgetPassword = (params: any) => {
    dispatch(
      openPopup({
        name: POPUP.NAME.CONFIRM_OTP_FORGET_PASSWORD,
        ...params,
        isGuestID,
        noControl,
        isNeedShowLobby
      })
    );
  };

  const onKeyPress = (e: any) => {
    if (e.key === 'Enter') {
      onSubmitSendOTP(e); // send new target value
      e.preventDefault();
    }
  };

  const renderBody = () => (
    <div className="block block--for-dark">
      <h2 className="title text-center">{TEXT.POPUP_FORGOT_PASS_TITLE}</h2>
      <p className="text text-muted text-center">{TEXT.FORGOT_PASS_TITLE}</p>
      <form
        className="form form-for-dark form--member form--update form--member-modal"
        data-abide="true"
        noValidate
      >
        <InputCustom
          placeholder={TEXT.PLACE_HOLDER_PASSWORD}
          label={TEXT.USER_LABEL.PHONE_NUMBER}
          id="phoneNumber"
          type="tel"
          error={error}
          valueOutput={phoneNumberTemp}
          icon="vie vie-key-skeleton-o"
          className="input-for-dark"
          onEnter={onSubmitSendOTP}
        />
        <div className="button-group child-auto">
          <button
            className={`button button--light button--large${
              phoneNumberTemp.value ? '' : ' disabled'
            }`}
            onClick={onSubmitSendOTP}
            title={TEXT.CONTINUE}
          >
            <span className="text">{TEXT.CONTINUE}</span>
          </button>
        </div>
      </form>
    </div>
  );

  const renderFooter = () => (
    <div className="option-link border-top p-y4">
      <span className="text text-14 text-medium-up-16 text-gray239">{TEXT.ALREADY_ACCOUNT}</span>
      <button className="button" type="button" data-open="loginModal" onClick={openPopupLogin}>
        {TEXT.SIGN_IN_NOW}
      </button>
    </div>
  );

  const closeX = () => {
    if (isGuestID) {
      dispatch(openPopup({ name: POPUP.NAME.FIRST_LOGIN }));
    } else if (profile?.forceBindPhone) {
      dispatch(openPopup({ name: POPUP.NAME.REQUIRE_UPDATE_PHONE }));
    } else {
      dispatch(openPopup({ noControl }));
    }
  };

  return (
    <Modal
      className="green-line modal--forgot modal--sign"
      renderBody={renderBody}
      renderFooter={renderFooter}
      onClosed={closeX}
      isNotCancelESC={profile?.forceBindPhone}
    />
  );
};

export default PopupForgetPassword;
