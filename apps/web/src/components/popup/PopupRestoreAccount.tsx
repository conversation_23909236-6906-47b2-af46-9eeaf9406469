import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { setToast } from '@actions/app';
import { openPopup } from '@actions/popup';
import { HTTP_CODE } from '@constants/constants';
import { TEXT } from '@constants/text';
import Button from '@components/basic/Buttons/Button';
import { dialogRestoreAccountLoaded } from '@tracking/functions/TrackingAccountDeletion';
import { flowNameAccountDeletion } from '@services/trackingServices';
import Modal from '../basic/Modal';

const PopupRestoreAccount = ({
  image,
  title,
  description,
  loadingPopup = true,
  btnLoginOtherAccount,
  btnLoginRestoreAccount,
  deletedAt,
  onBack,
  onSubmit,
  phoneNumber,
  isLoginSocial
}: any) => {
  const dispatch = useDispatch();
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const { dataRestoreAccount } = useSelector((state: any) => state?.GlobalAuth || {});
  const { flowName } = useMemo(
    () => flowNameAccountDeletion({ isLoginSocial, phoneNumber }),
    [isLoginSocial, phoneNumber]
  );

  useEffect(() => {
    dialogRestoreAccountLoaded({ flowName });
  }, []);

  const parseDescription = useMemo(
    () =>
      description?.replace(
        '{delete_at}',
        `<b class='text-white text-large-up-18 text-16 text-medium'>${deletedAt}</b>`
      ),
    [description, deletedAt]
  );

  const handleClosePopup = () => {
    dispatch(openPopup());
  };

  const handleBack = () => {
    handleClosePopup();
    if (typeof onBack === 'function') onBack();
  };

  const onLoginOtherAccount = () => {
    handleBack();
    //dialogRestoreAccountAnother({ flowName });
  };

  const onShowMessageError = (message: any) => {
    dispatch(
      setToast({
        message
      })
    );
  };

  const onRestoreAccount = () => {
    //dialogRestoreAccountAccept({ flowName });
    if (typeof onSubmit === 'function') onSubmit();
  };

  useEffect(() => {
    let message = '';
    if (!isEmpty(dataRestoreAccount)) {
      if (!dataRestoreAccount.success) {
        message = get(dataRestoreAccount, 'message', TEXT.ERROR_RETRY_AFTER_SOME_MINUTES_PLEASE);
      } else if (get(dataRestoreAccount, 'data.code', '') === HTTP_CODE.FAIL) {
        message = get(
          dataRestoreAccount,
          'data.message',
          TEXT.ERROR_RETRY_AFTER_SOME_MINUTES_PLEASE
        );
      } else {
        message = '';
      }
      if (message) {
        onShowMessageError(message);
      } else {
        handleClosePopup();
      }
    }
  }, [dataRestoreAccount]);

  const renderCustom = () => {
    if (!image) return null;
    return (
      <div className="mask mask--dark-backdrop intrinsic">
        <div className="mask__inner ratio-variant-1d8">
          <div className="mask__img absolute">
            <img src={image} alt="popup-notification" />
          </div>
        </div>
      </div>
    );
  };

  const renderBody = () => (
    <div className="block block--for-dark block--notify">
      {title && <h2 className="title text-center f-medium">{title}</h2>}
      {parseDescription && (
        <p
          className="text text-center margin-small-up-bottom-12 margin-large-up-bottom-32 text-large-up-16 text-14 text-muted"
          dangerouslySetInnerHTML={{
            __html: parseDescription
          }}
        />
      )}
      <div className={`${isMobile ? 'vertical ' : ''}button-group child-auto`}>
        {btnLoginOtherAccount && (
          <Button
            className={`button hollow button--large text-white${
              isMobile ? ' small-order-2 m-r' : ''
            }`}
            title={btnLoginOtherAccount}
            onClick={onLoginOtherAccount}
            textClass="text-center"
          />
        )}
        {btnLoginRestoreAccount && (
          <Button
            className={`button button--light button--large${isMobile ? ' m-l' : ''}`}
            title={btnLoginRestoreAccount}
            onClick={onRestoreAccount}
            textClass="text-center"
          />
        )}
      </div>
    </div>
  );

  if (loadingPopup) {
    return null;
  }

  return (
    <Modal
      className="modal--notify modal--notify-login yellow-line"
      renderCustom={renderCustom}
      renderBody={renderBody}
      onClosed={handleClosePopup}
      modalMobile={isMobile}
      classModal={isMobile ? 'p-l3 p-r3' : ''}
    />
  );
};

export default PopupRestoreAccount;
