import React, { useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { openPopup } from '@actions/popup';
import { TEXT } from '@constants/text';
import { replaceKey } from '@helpers/common';
import MultiProfile<PERSON><PERSON> from '@apis/MultiProfile';
import { EL_ID, HTTP_CODE, PIN_CODE, POPUP } from '@constants/constants';
import Modal from '../basic/Modal';
import InputCustom from '../basic/Input/InputCustom';

const PopupRecreatePinCode = ({ status, data, confirmNo, forgot }: any) => {
  const dispatch = useDispatch();
  const [value, setValue] = useState<any>('');
  const [error, setError] = useState<any>('');
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const disabledButton = useRef(false);

  const des = replaceKey(
    profile?.mobile ? TEXT.TEXT_PASSWORD_ACCOUNT_PIN_CODE : TEXT.TEXT_EMAIL_ACCOUNT_PIN_CODE,
    'value',
    profile?.mobile ? profile?.mobile : profile?.email
  );

  const validateInput = () => {
    if (value?.length < 6 && value?.length !== 0) {
      if (profile?.phoneVerified === false) {
        setError(TEXT.PIN_CODE_OTP_WRONG);
      } else {
        setError(TEXT.PASSWORD_REQUIRED_VALUE);
      }
      setValue('');
      disabledButton.current = false;
      return false;
    }
    return true;
  };

  const handleInputOverTime = () => {
    dispatch(
      openPopup({
        name: POPUP.NAME.PIN_CODE,
        forgot,
        status
      })
    );
  };
  // vadidate with mobile account
  const handleValidatePassword = () => {
    if (!validateInput()) return;
    MultiProfileApi.validatePassword({ password: value }).then((resp) => {
      if (!resp?.success) {
        setError(TEXT.PIN_CODE_PASSWORD_WRONG);
        setValue('');
        disabledButton.current = false;
        return;
      }

      dispatch(
        openPopup({
          name: POPUP.NAME.PIN_CODE,
          status: PIN_CODE.CREATE,
          forgot: status,
          data,
          password: value,
          isForgotPinCode: true
        })
      );
    });
  };
  // validate with social account
  const handleValidateOTP = () => {
    if (!validateInput()) return;
    MultiProfileApi.resetPinCode({
      profileId: data?.id,
      confirmNo,
      otpCode: value
    }).then((resp) => {
      if (!resp?.success) {
        // pin code input too many
        if (resp?.httpCode === HTTP_CODE.ERROR_423 || resp?.httpCode === HTTP_CODE.ERROR_404) {
          return handleInputOverTime();
        }
        // wrong pin code
        setError(TEXT.PIN_CODE_OTP_WRONG);
        setValue('');
        disabledButton.current = false;
        return;
      }
      dispatch(
        openPopup({
          name: POPUP.NAME.PIN_CODE,
          status: PIN_CODE.CREATE,
          forgot: status,
          confirmNo,
          otpCode: value,
          data,
          isForgotPinCode: true
        })
      );
    });
  };

  const handleVerifyCode = () => {
    if (profile?.phoneVerified === false) {
      handleValidateOTP();
      return;
    }
    handleValidatePassword();
  };

  const handleOnChange = (event: any) => {
    const value = event?.target?.value;
    setValue(value);
    disabledButton.current = (value?.length >= 6 || !profile.phoneVerified) && value?.length > 0;
  };

  const renderBody = () => (
    <div className="block block--for-dark relative">
      <h2 className="title text-center">{TEXT.TITLE_RE_TYPE_PIN_CODE}</h2>
      <p className="text text-muted text-center m-t2 m-b2 size-mw-70per">{des}</p>
      <InputCustom
        id={EL_ID.PIN_PASSWORD}
        type="password"
        icon="vie vie-key-skeleton-o"
        error={error}
        value={value}
        className="input-for-dark m-b2"
        onEnter={handleVerifyCode}
        onKeyDown={() => setError('')}
        onChange={(e: any) => handleOnChange(e)}
        autoFocus
        isErrClassAbsolute
      />
      <div className="button-group child-auto">
        <button
          className={`button button--light button--large ${!disabledButton.current && 'disabled'}`}
          onClick={handleVerifyCode}
          title={TEXT.CONTINUE}
        >
          <span className="text">{TEXT.CONTINUE}</span>
        </button>
      </div>
    </div>
  );

  const handleClosePopup = () => {
    dispatch(openPopup());
  };

  return (
    <Modal
      className="green-line modal--sign-confirm"
      renderBody={renderBody}
      onClosed={handleClosePopup}
    />
  );
};

export default PopupRecreatePinCode;
