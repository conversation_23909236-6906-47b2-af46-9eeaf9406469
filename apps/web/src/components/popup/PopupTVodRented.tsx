import React, { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import get from 'lodash/get';
import { openPopup } from '@actions/popup';
import { useVieRouter } from '@customHook';
import { TEXT } from '@constants/text';
import { parseTVodFromInfo, formatTimeTVodString } from '@services/contentService';
import { TAG_KEY, TVOD } from '@constants/constants';
import Tags from '@components/basic/Tags/Tags';
import { encodeParamDestination } from '@helpers/common';
import Image from '../basic/Image/Image';
import Button from '../basic/Buttons/Button';
import Modal from '../basic/Modal';
import { destinationLogin } from '@services/multiProfileServices';
import ConfigCookie from '@config/ConfigCookie';

const PopupTVodRented = ({ content, tvodInfo }: any) => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const webConfig = useSelector((state: any) => state?.App?.webConfig);
  const { currentProfile } = useSelector((state: any) => state?.MultiProfile || {});
  const { profile } = useSelector((state: any) => state?.Profile || {});

  const expiredString = get(webConfig, 'tVod.text.expiredString', '');
  const tvod = parseTVodFromInfo(tvodInfo);
  const { strTimeStandard, benefitType } = tvod || {};
  const { images, title, href, seo, slug } = content || {};

  const remainTimeText = useMemo(() => {
    if (!strTimeStandard) return '';
    return formatTimeTVodString({ strConfig: expiredString, strTime: strTimeStandard });
  }, [strTimeStandard, expiredString]);
  const onContinue = () => {
    onClosed();
    if (currentProfile?.id) return router.push(href, seo?.url);
    const remakeDestination = encodeParamDestination(seo?.url || '/' + slug + '.html');
    const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};

    destinationLogin({
      dataLogin: {
        profile,
        accessToken
      },
      destination: remakeDestination,
      router,
      dispatch
    });
  };

  const onClosed = () => {
    dispatch(openPopup());
  };

  const renderContent = () => (
    <div className="block block--for-dark block--notify">
      <h2 className="title text-center">{`Bạn đã thuê "${title}"`}</h2>
      {remainTimeText && (
        <Tags
          tagKey={TAG_KEY.REMIND}
          description={remainTimeText}
          subClass="align-center"
          txtClass="text-large-up-18 padding-small-up-left-4 padding-large-up-left-8 text-medium text-yellow91"
          iClass="icon--small icon--tiny-xs text-yellow91"
        />
      )}
    </div>
  );

  const renderCustom = () => (
    <div className="mask">
      <div className="mask-inner text-center">
        <Image
          src={images?.thumbnailHotNTC || images?.thumbnail}
          style={{ width: '100%', backgroundColor: '#111' }}
        />
      </div>
    </div>
  );

  const renderFooter = () => (
    <div className="button-group child-auto p-b4">
      <Button
        className="button button--light button--large m-b"
        onClick={onContinue}
        title={benefitType === TVOD.USER_TYPE.WATCHED ? TEXT.CONTINUE_WATCH : TEXT.WATCH_NOW}
      />
    </div>
  );

  return (
    <Modal
      className="modal--notify"
      renderCustom={renderCustom}
      renderBody={renderContent}
      renderFooter={renderFooter}
      onClosed={onClosed}
    />
  );
};

export default React.memo(PopupTVodRented);
