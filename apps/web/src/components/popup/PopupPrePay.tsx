import React from 'react';
import { useDispatch } from 'react-redux';
import { openPopup } from '@actions/popup';
import { TEXT } from '@constants/text';
import ConfigImage from '@config/ConfigImage';
import { mobileCheck } from '@helpers/common';
import Button from '../basic/Buttons/Button';
import Modal from '../basic/Modal';

const PopupPrePay = ({ onConfirm, prePayConfirmMessage, isOverlap, isPreventPayment }: any) => {
  const dispatch = useDispatch();

  const closePopup = () => {
    dispatch(openPopup());
  };

  const renderCustom = () => (
    <div className="mask">
      <div
        className="text-center"
        style={{
          backgroundColor: '#111',
          paddingTop: '30px'
        }}
      >
        <img src={ConfigImage.overlap} alt="" />
      </div>
    </div>
  );

  const renderBody = () => (
    <div className="block block--for-dark block--notify">
      <p
        className="text text-center"
        style={{
          color: 'white',
          fontSize: mobileCheck() ? '14px' : '18px',
          margin: '0px'
        }}
      >
        {prePayConfirmMessage}
      </p>
      <br />
      {isOverlap && (
        <p
          className="text text-center text-muted margin-small-up-bottom-12 margin-large-up-bottom-32"
          style={{ fontSize: mobileCheck() ? '12px' : '18px' }}
        >
          {TEXT.OVERLAP_NOTE}
        </p>
      )}
      {isPreventPayment && (
        <div className="button-group child-auto">
          <Button
            className="button button--light medium button--large"
            onClick={closePopup}
            title={TEXT.I_GOT_IT}
          />
        </div>
      )}
      {!isPreventPayment && isOverlap && (
        <div className="button-group child-auto">
          <Button
            className="button hollow button--dark-glass medium button--large"
            onClick={onConfirm}
            title={TEXT.YES}
          />
          <Button
            className="button button--light medium button--large"
            onClick={closePopup}
            title={TEXT.NO}
          />
        </div>
      )}

      {!isPreventPayment && !isOverlap && (
        <div className="button-group child-auto">
          <Button
            className="button hollow button--dark-glass medium button--large"
            onClick={closePopup}
            title={TEXT.NO}
          />
          <Button
            className="button button--light medium button--large"
            onClick={onConfirm}
            title={TEXT.YES}
          />
        </div>
      )}
    </div>
  );

  return (
    <Modal
      className=" modal--notify modal--notify-login green-line"
      renderCustom={renderCustom}
      renderBody={renderBody}
      notClosedButton
    />
  );
};

export default PopupPrePay;
