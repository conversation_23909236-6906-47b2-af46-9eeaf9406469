import React, { useEffect } from 'react';
import classNames from 'classnames';
import { useDispatch, useSelector } from 'react-redux';
import { loaded, closed, accepted } from '@tracking/functions/TrackingFirstPay';
import { cancelFirstPay, getIsFirstPay } from '@actions/user';
import { UtmParams } from '@models/subModels';
import { PAGE } from '@constants/constants';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import LocalStorage from '@config/LocalStorage';
import Modal from '../basic/Modal';
import Image from '../basic/Image/Image';
import { openPopup } from '@actions/popup';
import { useVieRouter } from '@customHook';

const PopupFirstPay = () => {
  const dispatch = useDispatch();
  const popupData = useSelector((state: any) => state?.Popup);
  const router = useVieRouter();
  const { query } = router || {};
  const {
    button_image_url: buttonImageUrl,
    popup_image_url: popupImageUrl,
    package_group_id: packageGroupId
  } = popupData?.data || {};

  const trackingValueFirstPay = {
    userType: 'free',
    packageGroupName: '',
    campaignName: ''
  };

  const handleClosePopup = () => {
    dispatch(openPopup({}));
    dispatch(cancelFirstPay());
    dispatch(getIsFirstPay(false));
  };

  const handleClose = () => {
    handleClosePopup();
    closed(trackingValueFirstPay);
  };

  const handleGoToPayment = () => {
    handleClosePopup();
    accepted(trackingValueFirstPay);
    const urmParams = UtmParams(query);
    router.push({ pathname: PAGE.PAYMENT_METHOD, query: { ...urmParams, pkg: packageGroupId } });
  };

  useEffect(() => {
    loaded(trackingValueFirstPay);
    return () => {
      ConfigLocalStorage.remove(LocalStorage.TRIGGER_AFTER_LOGIN);
    };
  }, []);

  const renderBody = () => (
    <div className="md:p-8 max-w-[576px] mx-auto">
      <div className="flex flex-col space-y-3 relative">
        <Image src={popupImageUrl} alt="trigger-first-pay" notWebp />
        <div
          className="max-h-12 cursor-pointer lg:hover:opacity-90 transition-all"
          onClick={handleGoToPayment}
        >
          <Image src={buttonImageUrl} alt="trigger-first-pay-button" notWebp />
        </div>
        <button
          className="button !mt-0 absolute 2xl:!-top-[38px] 2xl:!-right-[38px] left-[unset] -top-[14px] -right-[14px] w-7 h-7 !border border-solid rounded-full !border-white
        !bg-[rgba(0,0,0,0.45)]"
          onClick={handleClose}
          type="button"
          aria-label="Close"
        >
          <span className={classNames('icon text-white icon--tiny')}>
            <i className={classNames('vie vie-times-medium', 'text-shadow-blackColor-a50-111')} />
          </span>
        </button>
      </div>
    </div>
  );
  return <Modal renderCustom={renderBody} notClosedButton />;
};

export default React.memo(PopupFirstPay);
