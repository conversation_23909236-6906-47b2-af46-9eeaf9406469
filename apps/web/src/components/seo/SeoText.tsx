import React from 'react';

const SeoText = React.memo(
  ({ seo, custom, classH1, disableH1, disableStyle, forceShowH1, disableSeoText }: any) => {
    let seoText = seo?.seo_text || seo?.seoText || '';

    if (!seoText && !forceShowH1) return null;
    if (custom) {
      seoText += custom + seoText;
    }

    const styleH1 = classH1 ? {} : { ...styles.hideSEO };
    const styleSeoText = disableStyle ? {} : { ...styles.hideSEO };

    return (
      <>
        {!disableH1 ? (
          <h1 className={classH1 || ''} style={{ ...styleH1 }}>
            {seo?.title}
          </h1>
        ) : (
          ''
        )}
        {!disableSeoText && (
          <div
            className="seo-metadata-text"
            dangerouslySetInnerHTML={{ __html: seoText || '' }}
            style={{ ...styleSeoText }}
          />
        )}
      </>
    );
  }
);

const styles = {
  hideSEO: {
    position: 'absolute',
    color: 'transparent',
    zIndex: -9999,
    height: 0,
    overflow: 'hidden'
  }
};

export default SeoText;
