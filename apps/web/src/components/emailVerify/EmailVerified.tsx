import React, { useEffect, useState } from 'react';
import { useVieRouter } from '@customHook';
import { useDispatch, useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import UserApi from '@apis/userApi';
import ConfigImage from '@config/ConfigImage';
import Head from 'next/head';
import { setLoadedData } from '@actions/app';
import { TEXT } from '@constants/text';
import TrackingLoyalty from '@tracking/functions/TrackingLoyalty';
import Image from '../basic/Image/Image';

const THANKS = 'Xin cảm ơn!';
const OOPS = 'OOPS...';

const EmailVerified = () => {
  const { isOnLoyalty } = useSelector((state: any) => state?.App?.webConfig?.featureFlag || false);
  const dispatch = useDispatch();
  const [state, setState] = useState({
    label: '',
    content: '',
    labelButton: '',
    ready: false
  });
  const router = useVieRouter();
  useEffect(() => {
    const query = router?.query;
    dispatch(setLoadedData(true));
    if (isEmpty(query)) {
      setState({
        label: OOPS,
        content: TEXT.MSG_ERROR,
        labelButton: 'MỞ VIEON',
        ready: true
      });
      return;
    }
    UserApi.verifyEmail(query.register_session_id, query.otp).then((res) => {
      const success = res?.success;
      let message = TEXT.SUCCESS_EMAIL_VERIFIED;
      let label = THANKS;
      let labelButton = 'MỞ NGAY VIEON';
      if (!success) {
        label = OOPS;
        message = TEXT.ERROR_EMAIL_VERIFIED;
        labelButton = 'MỞ VIEON';
      } else if (isOnLoyalty) {
        TrackingLoyalty.trackingLoyaltyUpdateEmail(query.user_id);
      }
      setState({
        label,
        content: message,
        labelButton,
        ready: true
      });
    });
  }, []);

  return (
    <>
      <Head>
        <link rel="icon" type="image/png" href={ConfigImage.favicon} />
        <title>VieON - Xác thực email</title>
      </Head>
      <section className="section section--verify canal-v overflow">
        <div className="mask visible absolute full">
          <div className="mask-img text-center">
            <Image src={ConfigImage.mediaConnection} alt="VieON" notWebp />
          </div>
        </div>
        <div className="section-body">
          <div className="container">
            <div className="block block--verify">
              {state.ready && (
                <div className="align-center text-center">
                  <span className="logo logo-landing">
                    <Image src={ConfigImage.logoVerification} alt="VieON" />
                  </span>
                  <h1 className="title">{(state.label || '').toUpperCase()}</h1>
                  <p className="text">{state.content}</p>
                  <a className="button secondary" href="/" title="Trở về trang chủ" tabIndex={0}>
                    {state.labelButton}
                    {/* <span className="icon"><i className="vie vie-play-o"></i></span> */}
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default EmailVerified;
