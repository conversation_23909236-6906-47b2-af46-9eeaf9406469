import React, { useState } from 'react';
import { useVieRouter } from '@customHook';
import { PAYMENT, PAGE } from '@constants/constants';
import PackageInfo from './PackageInfo';

const PaymentMethod = ({
  dataBillingPackages,
  packageId,
  itemId,
  profileData,
  createTransaction,
  openPopupLogin
}: any) => {
  const router = useVieRouter();
  const [state, setState] = useState({
    statusSubmit: true,
    dataQRMomo: false,
    currentMethodActive: PAYMENT.METHOD_KEY.MOMO
  });

  const handleChangeStatusSubmit = (status: any, activeMethod: any) => {
    if (state.statusSubmit !== status) {
      setState((prevState) => ({
        ...prevState,
        statusSubmit: status
      }));
    }
    if (state.currentMethodActive !== activeMethod) {
      setState((prevState) => ({
        ...prevState,
        currentMethodActive: activeMethod
      }));
    }
  };

  const handleSubmitPayment = async (e: any) => {
    e.preventDefault();
    const packageId = e.target.item_id ? e.target.item_id.value : null;
    const serviceCode = e.target.service_code ? e.target.service_code.value : null;
    const methodId = e.target.method_id ? e.target.method_id.value : null;
    const dataType = e.target.item_id ? e.target.item_id.getAttribute('data-type') : null;
    if (dataType != null && dataType === 2) {
      router.push(PAGE.HOME, '/vip');
      return;
    }
    if (!profileData) {
      openPopupLogin(true);
      return;
    }
    if (typeof createTransaction === 'function') {
      const result = await createTransaction({ methodId, packageId, serviceCode });
      if (result) {
        if (methodId === PAYMENT.METHOD_KEY.MOMO) {
          const { payUrl } = result;
          window.open(payUrl, '_blank');
          setState((prevState) => ({
            ...prevState,
            dataQRMomo: result
          }));
        } else if (methodId === PAYMENT.METHOD_KEY.VNPAY) {
          const { payUrl } = result;
          window.open(payUrl, '_blank');
          setState((prevState) => ({
            ...prevState,
            dataQRMomo: result
          }));
        }
      }
    }
  };
  const onHandleChange = () => handleChangeStatusSubmit(true, PAYMENT.METHOD_KEY.MOMO);

  return (
    <section className="section section--payment section--payment-method canal-v overflow">
      <div className="container">
        <div className="body">
          <div className="grid-x align-center">
            <div className="cell medium-10">
              <div className="block block--payment block--payment-method">
                <div className="block__header text-center">
                  <h1 className="text-uppercase">Thanh toán gói dịch vụ</h1>
                </div>
                <div className="block__body">
                  <form
                    data-abide
                    noValidate
                    className="form form--payment form--payment-method"
                    onSubmit={handleSubmitPayment}
                  >
                    <div className="grid-container">
                      <PackageInfo
                        dataBillingPackages={dataBillingPackages}
                        packageId={packageId}
                        itemId={itemId}
                        profileData={profileData}
                      />
                      <hr />
                      <div className="payment-method-list">
                        <div className="grid-x grid-margin-x mb">
                          <div className="small-6 cell">
                            <label className="text-right text-uppercase">
                              <strong>Chọn phương thức thanh toán:</strong>
                            </label>
                          </div>
                          <div className="small-6 cell">
                            <div className="radio radio-custom">
                              <input
                                type="radio"
                                name="method_id"
                                value={PAYMENT.METHOD_KEY.MOMO}
                                id="MOMO"
                                checked={state.currentMethodActive === PAYMENT.METHOD_KEY.MOMO}
                                onChange={onHandleChange}
                              />
                              <label htmlFor="MOMO">{PAYMENT.METHOD_KEY.MOMO}</label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="text-center">
                      <button
                        id="btn-submit-payment"
                        className="button rounded success height-40 width-228"
                        type="submit"
                        name="payment"
                        disabled={!state.statusSubmit}
                        title="Thanh toán"
                      >
                        Thanh toán
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PaymentMethod;
