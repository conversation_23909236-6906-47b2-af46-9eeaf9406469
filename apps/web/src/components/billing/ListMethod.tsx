import React, { useState } from 'react';
import { PAYMENT } from '@constants/constants';
import ConfigImage from '@config/ConfigImage';
import { getLabelMethodPayment } from '@helpers/common';

const ListMethod = (props: any) => {
  const { dataBillingMethods, changeStatusSubmit } = props;

  const [state, setState] = useState({
    activeMethod: null,
    serviceCode: null
  });
  const listMethodNotHaveItem = [PAYMENT.METHOD_KEY.VNPAYQR, PAYMENT.METHOD_KEY.EWALLET];

  const handleChangeMethod = (e: any) => {
    const { value } = e.target || null;
    if (value && value !== state.activeMethod) {
      setState((prevState) => ({
        ...prevState,
        activeMethod: value,
        serviceCode: null
      }));
      if (typeof changeStatusSubmit === 'function') {
        changeStatusSubmit(false);
      }
    }
  };
  const handleOnBankChange = (serviceCode: any) => {
    setState((prevState) => ({
      ...prevState,
      serviceCode
    }));
    if (typeof changeStatusSubmit === 'function') {
      changeStatusSubmit(true);
    }
  };
  return (
    <>
      {Object.keys(dataBillingMethods).map((key, i) => {
        const label = getLabelMethodPayment(key);

        // nếu chưa có active method thì lấy thằng đầu tiên
        const isLoadServices =
          state.activeMethod === key && listMethodNotHaveItem.indexOf(key) === -1;

        return (
          <div key={key} className="grid-x mb-2">
            {i === 0 && (
              <div className="small-6 cell">
                <label className="text-right text-uppercase pr-1">
                  <strong>Chọn phương thức thanh toán:</strong>
                </label>
              </div>
            )}

            <div className={`${i !== 0 ? 'small-offset-6 ' : ''}small-6 cell`}>
              <div className="radio radio-custom">
                <input
                  type="radio"
                  name="method_id"
                  value={key}
                  id={key}
                  onChange={handleChangeMethod}
                />
                <label htmlFor={key}>{label}</label>
              </div>
              {isLoadServices && (
                <GatewayBanks
                  listBank={dataBillingMethods[key]}
                  onBankChange={handleOnBankChange}
                />
              )}
            </div>
          </div>
        );
      })}
    </>
  );
};
const GatewayBanks = ({ listBank, onBankChange }: any) =>
  listBank &&
  listBank.length > 0 && (
    <div className="payment-options">
      <div className="payment-options-list radio-group radio-group-flex" data-view={4}>
        {listBank.map((bank: any, i: any) => {
          const imgBank = bank.service_image || ConfigImage.imgVcbBank;
          const { short_name, service_code } = bank;
          return (
            <div key={i} className="radio radio-custom radio-custom-img top-right">
              <input
                value={service_code}
                type="radio"
                name="service_code"
                id={short_name}
                onChange={() => {
                  onBankChange(service_code);
                }}
              />
              <label htmlFor={short_name}>
                <span className="radio-custom-thumb">
                  <img src={imgBank} alt={short_name} />
                </span>
                <i className="vie vie-tick" />
              </label>
            </div>
          );
        })}
      </div>
    </div>
  );
export default ListMethod;
