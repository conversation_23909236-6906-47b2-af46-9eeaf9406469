import React, { useEffect, useRef, useState } from 'react';
import Router from 'next/router';
import { createTimeout } from '@helpers/common';
import { CURRENCY, PAGE } from '@constants/constants';
import { TEXT } from '@constants/text';

const PackageInfo = ({ dataBillingPackages, packageId, itemId, profileData }: any) => {
  const [state, setState] = useState({
    isOpenPackagePopup: false,
    isOpenPackageItemPopup: false
  });
  const { currentPackages, currentItem } = getCurrentPackage(
    dataBillingPackages,
    packageId,
    itemId
  );
  const userDisplayName = getInfoUser(profileData);
  const handleChangePackage = (value: any) => {
    // const { value } = e.target
    Router.push({
      pathname: PAGE.PAYMENT_METHOD,
      query: { package_id: value }
    });

    handleTogglePopup('package_id');
  };

  const handleChangePackageItem = (value: any) => {
    // const { value } = e.target
    const { package_id } = Router.query;
    Router.push({
      pathname: PAGE.PAYMENT_METHOD,
      query: {
        package_id: package_id || currentPackages[0].id,
        item_id: value
      }
    });
    handleTogglePopup('item_id');
  };
  const handleOnBlur = (e: any, key: any) => {
    const { currentTarget, relatedTarget } = e;
    createTimeout(() => {
      if (!currentTarget.contains(relatedTarget)) {
        handleTogglePopup(key);
      }
    }, 0);
  };
  const handleTogglePopup = (popupName: any) => {
    if (popupName === 'package_id') {
      setState((prevState) => ({
        ...prevState,
        isOpenPackagePopup: !state.isOpenPackagePopup,
        isOpenPackageItemPopup: false
      }));
      return;
    }
    if (popupName === 'item_id') {
      setState((prevState) => ({
        ...prevState,
        isOpenPackageItemPopup: !state.isOpenPackageItemPopup,
        isOpenPackagePopup: false
      }));
    }
  };
  const useOutsidePackageBox = (ref: any) => {
    const handleClickOutside = (event: any) => {
      if (ref.current && !ref.current.contains(event.target)) {
        setState((prevState) => ({
          ...prevState,
          isOpenPackagePopup: false,
          isOpenPackageItemPopup: false
        }));
      }
    };
    useEffect(() => {
      // Bind the event listener
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        // Unbind the event listener on clean up
        document.removeEventListener('mousedown', handleClickOutside);
      };
    });
  };

  const wrapperRef = useRef<any>(null);
  useOutsidePackageBox(wrapperRef);
  return (
    (dataBillingPackages && dataBillingPackages.length > 0 && (
      <div className="five-percent align-center" ref={wrapperRef}>
        <div className="grid-x m-b2">
          <div className="cell small-4">
            <label>Gói dịch vụ</label>
          </div>
          <div className="cell small-8">
            <div className="text-right" style={{ position: 'relative' }}>
              <input
                name="package_id"
                value={currentPackages ? currentPackages.id : ''}
                type="hidden"
                data-type={currentPackages.public_type}
              />
              <button
                className="button button--change"
                type="button"
                data-toggle="dataMoviePackage"
                aria-controls="dataMoviePackage"
                data-is-focus="false"
                data-yeti-box="dataMoviePackage"
                aria-haspopup="true"
                aria-expanded="false"
                onClick={() => handleTogglePopup('package_id')}
              >
                {currentPackages ? currentPackages.name : ''}
                <span className="icon">
                  <i className="vie vie-arrow-down" />
                </span>
              </button>
              <div
                className={`dropdown-pane has-position-bottom has-alignment-right ${
                  state.isOpenPackagePopup ? 'is-open' : ''
                }`}
                id="dataMoviePackage"
                style={{
                  right: '0',
                  top: '100%'
                }}
                // style={{ left: "914.033px", top: "108px" }}
                onBlur={(e) => handleOnBlur(e, 'package_id')}
              >
                <ul className="vertical menu">
                  {dataBillingPackages &&
                    dataBillingPackages.map((packageItem: any) => {
                      const { public_type } = packageItem;
                      return (
                        (public_type && public_type > 0 && (
                          <li
                            key={packageItem.id}
                            className={
                              currentPackages && currentPackages.id === packageItem.id
                                ? 'is-active'
                                : undefined
                            }
                          >
                            <a
                              tabIndex={-1}
                              className="text-left"
                              title={packageItem.name}
                              onClick={() => handleChangePackage(packageItem.id)}
                            >
                              {packageItem.name}
                            </a>
                          </li>
                        )) ||
                        null
                      );
                    })}
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div className="grid-x m-b2">
          <div className="cell small-4">
            <label>Giá trị</label>
          </div>
          <div className="cell small-8">
            {currentPackages && (
              <>
                <div className="text-right" style={{ position: 'relative' }}>
                  <input
                    name="item_id"
                    value={currentItem ? currentItem.id : ''}
                    type="hidden"
                    data-type={currentPackages.public_type}
                  />
                  <button
                    className="button button--change"
                    type="button"
                    onClick={() => handleTogglePopup('item_id')}
                  >
                    {currentItem ? currentItem.valueDuration : ''}
                    <span className="icon">
                      <i className="vie vie-arrow-down" />
                    </span>
                  </button>
                  <div
                    className={`dropdown-pane has-position-bottom has-alignment-right ${
                      state.isOpenPackageItemPopup ? 'is-open' : ''
                    }`}
                    id="dataMoviePackage"
                    style={{
                      right: '0',
                      top: '100%'
                    }}
                  >
                    <ul className="vertical menu">
                      {currentPackages.items.length > 0 &&
                        currentPackages.items.map((item: any) => (
                          <li
                            key={item.id}
                            className={
                              currentItem && currentItem.id === item.id ? 'is-active' : undefined
                            }
                            onClick={() => handleChangePackageItem(item.id)}
                          >
                            <a tabIndex={-1} className="text-left" title={item.valueDuration}>
                              {item.valueDuration}
                            </a>
                          </li>
                        ))}
                    </ul>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
        {/* <div className="grid-x align-justify m-b2">
              <div className={errorGiftCode ? "small-7 cell is-invalid-label" : "small-7 cell"}>
                  <input id="giftCodeID" type="text" placeholder="Nhập mã khuyến mãi" />
                  {errorGiftCode && <small data-form-error-for="giftCodeID" className="error-text">{errorGiftCode}</small>}
              </div>
              <div className="shrink cell">
                  <button type="button" id="btn-giftcode"
                      title={applyButtonTitle}
                      className={applyButtonClass} >
                      {applyButtonTitle}
                  </button>
              </div>
          </div>
          <div className="grid-x align-justify m-b2">
              <div className="small-7 cell">
                  <label>{'Khuyến mãi'}</label>
              </div>
              <div className="shrink cell">
                  <span className="text-view">{giftCode.type === 'cost' ? `${giftCode.value} ${CURRENCY.VND}` : `${giftCode.value}%`}</span>
              </div>
          </div> */}
        <div className="grid-x m-b2">
          <div className="cell small-4">
            <label>{TEXT.ACCOUNT}</label>
          </div>
          <div className="cell small-8 text-right">
            <span className="text-view">{userDisplayName}</span>
          </div>
        </div>
        <div className="grid-x align-justify m-b2">
          <div className="cell small-4">
            <label>Tổng tiền thanh toán</label>
          </div>
          <div className="cell small-8 text-right">
            <span className="text-view">
              <b>
                {`${new Intl.NumberFormat(['ban', 'id']).format(
                  handleDiscount(currentItem.price)
                )} ${CURRENCY.VND}`}
              </b>
            </span>
          </div>
        </div>
      </div>
    )) ||
    null
  );
};

const getCurrentPackage = (dataBillingPackages: any, packageId: any, itemId: any) => {
  const currentPackages = dataBillingPackages
    ? dataBillingPackages.find((item: any) => parseInt(item.id) === parseInt(packageId)) ||
      dataBillingPackages[0]
    : null;
  const currentItem = currentPackages
    ? currentPackages.items.find((item: any) => parseInt(item.id) === parseInt(itemId)) ||
      currentPackages.items[0]
    : null;
  return {
    currentPackages,
    currentItem
  };
};
const handleDiscount = (pricePackage: any, giftCode?: any) => {
  let priceAfterGiftCode = pricePackage;
  if (giftCode) {
    if (giftCode.type === 'cost') {
      if (giftCode.value >= pricePackage) {
        priceAfterGiftCode = 0;
      } else {
        priceAfterGiftCode = pricePackage - giftCode.value;
      }
    } else {
      priceAfterGiftCode = pricePackage - pricePackage * (giftCode.value / 100);
    }
  }
  return priceAfterGiftCode;
};

const getInfoUser = (profileData: any) => {
  if (!profileData) return null;
  return profileData.mobile !== '' ? profileData.mobile : profileData.email;
};

export default PackageInfo;
