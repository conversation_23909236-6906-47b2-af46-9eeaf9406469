import React from 'react';
import Image from '@components/basic/Image/Image';
import { useSelector } from 'react-redux';
import styles from './styles.module.scss';

function PackageBenefitList({ data }: any) {
  const { isMobile } = useSelector((state: any) => state?.App || {});
  if (!data || !data?.benefits) return null;
  const benefits = data.benefits;
  return (
    <div className={styles.benefitContainer}>
      {benefits.map((item: any, index: any) => (
        <div className={styles.benefitItem} key={`${item.text}-${index}`}>
          <Image
            src={item.icon}
            notWebp
            width={isMobile ? '24px' : '28px'}
            height={isMobile ? '24px' : '28px'}
          />
          <span className={styles.benefitText}>{item.text}</span>
        </div>
      ))}
    </div>
  );
}

export default PackageBenefitList;
