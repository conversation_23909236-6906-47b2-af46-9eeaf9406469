import React from 'react';
import Input from '@components/basic/Input/Input';
import Button from '@components/basic/Buttons/Button';
import { TEXT } from '@constants/text';
import { setToast } from '@actions/app';
import { copyText } from '@helpers/common';
import { useDispatch } from 'react-redux';
import { ICON_KEY } from '@constants/constants';
const CopyText = React.memo(({ value, title, className, onHandleClickButton }: any) => {
  const dispatch = useDispatch();
  const onHandleCopy = () => {
    const text = copyText(value);
    if (text && title) {
      dispatch(
        setToast({
          message: title,
          iconClass: 'icon--small',
          iClass: 'vie-copy-stroke-rc'
        })
      );
    }
    if (onHandleClickButton) {
      onHandleClickButton();
    }
  };
  return (
    <div className={`field outline field--copy field-l ${className}`}>
      <div className="grid-x">
        <div className="cell auto">
          <div className="field__input border-whithout-right" style={{ height: '36px' }}>
            <Input
              id="typevoucher"
              inputClass="input p-x2"
              readOnly
              placeholder={TEXT.PLACE_HOLDER_REFERRAL_CODE}
              valInput={value}
              size={10}
            />
          </div>
        </div>
        <div className="cell shrink">
          <Button
            className="button button--green button--medium hover:opacity-80"
            type="button"
            title={TEXT.COPY}
            onClick={onHandleCopy}
            iconType={ICON_KEY.COPY}
            textClass="m-l1 text-white"
          />
        </div>
      </div>
    </div>
  );
});
export default CopyText;
