.floatingDatePicker {
  @apply relative rounded-md w-full mb-3 lg:mb-4 border-none outline-none text-left;
  & > div:first-child {
    @apply block w-full;
  }

  button[aria-label='Close'] {
    @apply p-0 z-50;
    &::after {
      @apply bg-[#595959] absolute right-0.5 top-1/2 z-10 -translate-y-1/2 w-5 h-5;
      @apply text-base flex items-center justify-center;
    }
  }

  .datePicker {
    @apply block pt-2.5 pb-1.5 px-0 w-full text-sm bg-transparent relative;
    @apply appearance-none focus:outline-none focus:ring-0 z-10 relative shadow-none;
    &Active {
      @apply border-[#fff] text-[#fff];
      cursor: pointer;
    }
    &Disabled {
      @apply border-[#646464] text-[#9b9b9b];
    }
    &::placeholder {
      @apply text-[#9b9b9b];
    }
  }

  .label {
    @apply absolute md:left-10 left-8 text-sm duration-300 transform -translate-y-6 scale-75 top-[8px] origin-[0] rounded-none z-0;
    &Active {
      @apply text-[#9B9B9B] scale-75 -translate-y-2/3;
    }
    &Disabled {
      @apply text-[#9b9b9b] scale-100 translate-y-0;
    }
  }

  .message {
    @apply text-[#F00] text-xs absolute top-full right-0 line-clamp-1;
  }

  & > div:first-child {
    img {
      @apply p-0 w-6 h-6 top-1.5;
      & + input {
        @apply md:pl-10 pl-8 m-0 border-0;
      }
    }
  }

  .customPopper {
    @apply rounded shadow-[0px_18px_143px_0px_rgba(0,134,37,0.05),0px_4.021px_31.941px_0px_rgba(0,134,37,0.08),0px_1.197px_9.51px_0px_rgba(0,134,37,0.13)] bg-[#222222] text-gray-900 font-Roboto border-none;
    .react-datepicker__header {
      display: none;
    }
  }
}
.customHeader {
  @apply flex gap-2 content-center h-7 xl:h-8 items-center justify-center overflow-hidden;
}
.customSelect {
  --text-size: 1rem;
  @apply bg-[#595959] text-sm xl:text-base font-medium border-0 cursor-pointer overflow-hidden w-fit;
}

.customSelect:focus {
  @apply bg-[#595959] shadow-none border-0;
}

.customSelect:hover {
  @apply text-[#3ac882];
}

.navigateNext {
  @apply absolute right-1.5 w-7 h-7 rounded-full flex justify-center items-center;
  &:hover {
    @apply enabled:bg-[#383838] transition-all duration-300 delay-100;
  }
}
.navigatePrev {
  @apply absolute left-1.5 w-7 h-7 rounded-full flex justify-center items-center;
  &:hover {
    @apply enabled:bg-[#383838] transition-all duration-300 delay-100;
  }
}

.framerCustom {
  @apply text-[#fff] rounded border-none z-50 absolute top-0 left-0 w-full h-full bg-[#222222];
  .header {
    @apply bg-[#595959] p-2 text-base;
    @apply w-full flex items-center justify-evenly;
  }
  .body {
    @apply gap-1 p-2 grid grid-cols-4 items-center w-full 2xl:h-[calc(100%-74px)] h-[calc(100%-45px)];
    .item {
      @apply w-full 2xl:h-16 h-full rounded flex items-center justify-center text-xs xl:text-sm;
      @apply transition-all duration-300 whitespace-nowrap;
      &:hover:not(.active):not(:disabled) {
        @apply bg-[#383838] delay-100;
      }
      &.active {
        @apply bg-[#3ac882] relative font-medium;
      }
      &:disabled {
        @apply text-[#404040] cursor-not-allowed;
      }
    }
  }
}

.buttonCurrent {
  @apply transition-all duration-500 w-full ml-6 mr-6 2xl:h-8 h-7;
  &:hover {
    @apply enabled:bg-[#383838];
  }
  &:disabled {
    @apply text-[rgba(255,255,255,0.3)];
  }
}
