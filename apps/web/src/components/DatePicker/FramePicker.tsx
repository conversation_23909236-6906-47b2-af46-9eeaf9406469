import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { getMonth, getYear } from 'date-fns';
import classNames from 'classnames';
import styles from './Styles.module.scss';
import Button from '../basic/Buttons/Button';
import IconArrowLeft from '../Icons/IconArrowLeft';

const FramePicker = ({
  isExtraExtraLargeScreen,
  isGlobal,
  step,
  STEPS,
  year,
  setStep,
  months,
  date,
  changeYear,
  changeMonth
}: any) => {
  const [currentYear, setCurrentYear] = useState(year);
  const amountSpace = useMemo(() => (step === STEPS.MONTH ? 1 : 10), [step]);

  const startYear = useMemo(() => Math.max(currentYear - (currentYear % 10)), [currentYear]);
  const endYear = useMemo(() => startYear + 9, [startYear]);

  const years = useMemo(
    () => Array.from({ length: endYear - startYear + 1 }, (_, index) => startYear + index),
    [startYear, endYear]
  );

  const newData = useMemo(() => {
    if (step === STEPS.MONTH) {
      return months;
    }
    return years;
  }, [step, months, years]);

  const incrementYear = useCallback(() => {
    const newYear = currentYear + amountSpace;
    const maxYear = getYear(new Date());
    const clampedYear = Math.min(newYear, maxYear);
    setCurrentYear(clampedYear);
  }, [currentYear, amountSpace]);

  const decrementYear = useCallback(() => {
    const newYear = currentYear - amountSpace;
    const minYear = 1900;
    const clampedYear = Math.max(newYear, minYear);
    setCurrentYear(clampedYear);
  }, [currentYear, amountSpace]);

  const handleStep = useCallback(
    (e: any) => {
      e.preventDefault();
      if (step === STEPS.MONTH) {
        return setStep(STEPS.YEAR);
      }
      return setStep(STEPS.MONTH);
    },
    [step]
  );

  const handleSelect = useCallback(
    (e: any, selectedItem: any) => {
      e.preventDefault();
      if (step === STEPS.MONTH) {
        changeMonth(months?.indexOf(selectedItem));
        changeYear(currentYear);
        return setStep(null);
      }
      changeYear(selectedItem || currentYear);
      return setStep(STEPS.MONTH);
    },
    [step, currentYear]
  );

  useEffect(() => {
    if (year) {
      setCurrentYear(year);
    }
  }, [year]);

  useEffect(
    () => () => {
      setStep(null);
    },
    []
  );

  return (
    <div className={styles.framerCustom}>
      <div className={styles.header}>
        <button
          className={styles.navigatePrev}
          type="button"
          title="Previous"
          onClick={decrementYear}
          aria-label="Previous"
        >
          <span>
            <IconArrowLeft size={isExtraExtraLargeScreen ? 24 : 18} />
          </span>
        </button>
        <button
          className={styles.buttonCurrent}
          onClick={handleStep}
          type="button"
          title="button"
          aria-label="Current Year"
          disabled={step === STEPS.YEAR}
        >
          {step === STEPS.MONTH ? currentYear || year : `${startYear}-${endYear}`}
        </button>
        <button
          className={styles.navigateNext}
          type="button"
          title="Next"
          onClick={incrementYear}
          aria-label="Next"
        >
          <IconArrowLeft size={isExtraExtraLargeScreen ? 24 : 18} className="rotate-180" />
        </button>
      </div>
      <div className={styles.body}>
        {newData?.map((item: any) => (
          <Button
            subTitle={item}
            type="button"
            title={isGlobal && typeof item === 'string' ? item?.slice(0, 3) : item}
            key={item}
            className={classNames(
              styles.item,
              (months[getMonth(date)] === item || item === currentYear) && styles.active,
              typeof item === 'number' && item > new Date().getFullYear() && styles.disabled
            )}
            onClick={(e: any) => handleSelect(e, item)}
            disabled={typeof item === 'number' && item > new Date().getFullYear()}
          />
        ))}
      </div>
    </div>
  );
};

export default FramePicker;
