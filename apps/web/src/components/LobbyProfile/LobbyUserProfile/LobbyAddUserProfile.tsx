import React, { useEffect, useMemo, useState } from 'react';
import classNames from 'classnames';
import { useDispatch, useSelector } from 'react-redux';
import AvatarProfile from '@components/basic/AvatarProfile';
import {
  multiProfileAddProfileLoad,
  multiProfileAddProfileType,
  multiProfileAddProfileChooseAge,
  multiProfileAddProfileChooseName,
  multiProfileAddProfileChooseGender,
  multiProfileAddProfileChooseEditAvatar,
  multiProfileAddProfileChooseCreatePin,
  multiProfileAddProfileChooseDeletePin,
  multiProfileAddProfileAccept
} from '@tracking/functions/TrackingAddMultiProfile';
import Button from '@components/basic/Buttons/Button';
import InputCustom from '@components/basic/Input/InputCustom';
import SvgIcon from '@components/basic/Icon/SvgIcon';
import MultiProfile<PERSON><PERSON> from '@apis/MultiProfile';
import { TEXT } from '@constants/text';
import useValidateCharsInput from '@components/basic/Input/useValidateCharsInput';
import { ERROR_CODE, ICON_KEY, LOBBY_PROFILE_STEP, PIN_CODE, POPUP } from '@constants/constants';
import {
  resetResultForm,
  setDefaultForm,
  setLobbyStep,
  setResultForm,
  setStatusLobbyProfile
} from '@actions/multiProfile';
import { setToast } from '@actions/app';
import isEmpty from 'lodash/isEmpty';
import { openPopup } from '@actions/popup';
import { createUniqueProfileName } from '@services/multiProfileServices';
import { VALUE } from '@config/ConfigSegment';
import { isEqual } from 'lodash';
import styles from './LobbyUserProfile.module.scss';

const LobbyAddUserProfile = () => {
  // Redux handle
  const dispatch = useDispatch();
  const { resultForm, ageRanges, ageRangesKid, genders, avatars, multiProfile, defaultForm } =
    useSelector((state: any) => state?.MultiProfile || {});
  const profileUserType = useSelector((state: any) => state?.User?.USER_TYPE);
  const [randomAvatar, setRandomAvatar] = useState<any>({ avatarId: 1, avatarUrl: '' });
  const [userType, setUserType] = useState(TEXT.ADULT);
  const [ageRange, setAgeRange] = useState<any>();
  const [gender, setGender] = useState(resultForm?.gender);
  const [isKid, setIsKid] = useState(resultForm?.isKid || false);

  // Hook custom
  const userName = useValidateCharsInput(createUniqueProfileName(multiProfile?.items || []));

  // Default Gender
  const defaultGender = useMemo(
    () => (genders || []).find((item: any) => item?.id === 3),
    [genders]
  );

  // Get current gender
  const currentGender = useMemo(
    () =>
      genders.find(
        (item: any) =>
          item?.id === (resultForm?.gender >= 0 ? resultForm?.gender : defaultGender?.id)
      ),
    [resultForm?.gender, genders]
  );

  // Merge Array common
  const mergeDedupe = (arr: any) => {
    if (isEmpty(arr)) return;
    return [...new Set([].concat(...arr))];
  };

  // Handle change Gender
  const handleChangeGender = (value: any) => setGender(value);

  // Handle select avatar
  const handleSelectAvatar = () => {
    multiProfileAddProfileChooseEditAvatar({
      flowName: isKid
        ? VALUE.MULTI_PROFILE_ADD_PROFILE_KID
        : VALUE.MULTI_PROFILE_ADD_PROFILE_NON_KID,
      userType: profileUserType?.userType
    });
    dispatch(setLobbyStep(LOBBY_PROFILE_STEP.AVATAR_SELECT));
    dispatch(setStatusLobbyProfile(LOBBY_PROFILE_STEP.ADD));
  };

  // handle add new profile
  const handleOnSuccess = () => {
    const dataRequest = {
      name: userName?.value,
      gender: isKid ? null : currentGender?.id,
      isKid,
      avatarId: resultForm?.avatarId,
      pinCode: isKid ? null : resultForm?.newPinCode || '',
      ageRange: ageRange?.id
    };

    multiProfileAddProfileType({
      flowName: isKid
        ? VALUE.MULTI_PROFILE_ADD_PROFILE_KID
        : VALUE.MULTI_PROFILE_ADD_PROFILE_NON_KID,
      userType: profileUserType?.userType,
      profileTypeUx: userType
    });
    if (ageRange) {
      multiProfileAddProfileChooseAge({
        flowName: VALUE.MULTI_PROFILE_ADD_PROFILE_NON_KID,
        userType: profileUserType?.userType,
        profileAge: ageRange?.name
      });
    }
    if (userName?.value) {
      multiProfileAddProfileChooseName({
        flowName: isKid
          ? VALUE.MULTI_PROFILE_ADD_PROFILE_KID
          : VALUE.MULTI_PROFILE_ADD_PROFILE_NON_KID,
        userType: profileUserType?.userType,
        profileNameUx: userName?.value
      });
    }
    if (gender >= 0) {
      multiProfileAddProfileChooseGender({
        flowName: VALUE.MULTI_PROFILE_ADD_PROFILE_NON_KID,
        userType: profileUserType?.userType,
        profileGender: currentGender?.name
      });
    }
    multiProfileAddProfileAccept({
      flowName: isKid
        ? VALUE.MULTI_PROFILE_ADD_PROFILE_KID
        : VALUE.MULTI_PROFILE_ADD_PROFILE_NON_KID,
      userType: profileUserType?.userType,
      profileTypeUx: userType,
      profileAge: !isKid && ageRange?.name,
      profileGender: !isKid && currentGender?.name,
      profileNameUx: userName?.value
    });

    MultiProfileApi.createProfile(dataRequest).then((res) => {
      if (res?.success) {
        handleCallbackToLobbyView(TEXT.LOBBY_PROFILE_CREATE);
      } else if (res?.data?.code === ERROR_CODE.CODE_20) {
        handleCallbackToLobbyView(TEXT.ACCOUNT_PROFILE_OVER);
      } else if (res?.data?.code === ERROR_CODE.CODE_21) {
        dispatch(setToast({ message: TEXT.INVALID_NAME }));
      } else {
        dispatch(setToast({ message: TEXT.MSG_ERROR }));
      }
    });
  };
  const handleClose = () => {
    if (!isEqual(resultForm, defaultForm)) {
      // handle open popup confirm when lobby changed
      dispatch(
        openPopup({
          name: POPUP.NAME.CONFIRM_WHEN_RETURN,
          lobbyType: LOBBY_PROFILE_STEP?.ADD,
          isKid
        })
      );
    }
    handleCallbackToLobbyView();
  };

  // Back to lobby view
  const handleCallbackToLobbyView = (text?: any) => {
    dispatch(setLobbyStep());
    if (text) dispatch(setToast({ message: text }));
    dispatch(resetResultForm({}));
  };

  // handle open pin code popup
  const handleOpenPinCode = () => {
    if (resultForm?.hasPinCode) {
      multiProfileAddProfileChooseDeletePin({
        flowName: VALUE.MULTI_PROFILE_ADD_PROFILE_NON_KID,
        userType: profileUserType?.userType
      });
    } else {
      multiProfileAddProfileChooseCreatePin({
        flowName: VALUE.MULTI_PROFILE_ADD_PROFILE_NON_KID,
        userType: profileUserType?.userType
      });
    }
    if (resultForm?.hasPinCode) {
      dispatch(
        setResultForm({
          hasPinCode: false,
          pinCode: '',
          newPinCode: ''
        })
      );
      return;
    }
    dispatch(
      openPopup({
        name: POPUP.NAME.PIN_CODE,
        status: PIN_CODE.CREATE
      })
    );
    dispatch(setStatusLobbyProfile(LOBBY_PROFILE_STEP.ADD));
  };

  // Hooks
  useEffect(() => {
    multiProfileAddProfileLoad({
      flowName: VALUE.MULTI_PROFILE_ADD_PROFILE,
      userType: profileUserType?.userType
    });
  }, []);

  // Handle userType
  useEffect(() => {
    setUserType(isKid ? TEXT.CHILDREN : TEXT.ADULT);
  }, [isKid]);

  // Handle ageRange
  useEffect(() => {
    setAgeRange(!isKid ? ageRanges[0] : ageRangesKid[0]);
  }, [isKid, resultForm?.ageRange, ageRanges, ageRangesKid]);

  // Handle random avatars
  useEffect(() => {
    if (!isEmpty(avatars)) {
      const resultItem = avatars?.map((arrItems: any) => arrItems.items);
      const mergeArray: any = mergeDedupe(resultItem)?.filter(
        (arrItems: any) => arrItems?.id !== defaultForm?.avatarId
      );
      const randomItem = mergeArray[Math.floor(Math.random() * mergeArray?.length)] || '';
      return setRandomAvatar(randomItem);
    }
  }, [avatars]);

  useEffect(() => {
    if (!resultForm?.avatarUrl) {
      dispatch(
        setResultForm({ ...resultForm, avatarId: randomAvatar.id, avatarUrl: randomAvatar.url })
      );
    }
  }, [randomAvatar, resultForm?.avatarUrl]);

  // Get default Form
  useEffect(() => {
    dispatch(
      setDefaultForm({
        ...defaultForm,
        avatarId: defaultForm.avatarId || randomAvatar.id,
        avatarUrl: defaultForm.avatarUrl || randomAvatar.url,
        userType,
        ageRange,
        name: userName?.value,
        gender: gender >= 0 ? gender : currentGender?.id,
        hasPinCode: defaultForm?.hasPinCode || false,
        isKid
      })
    );
  }, [randomAvatar]);

  // Handle Result Form
  useEffect(() => {
    dispatch(
      setResultForm({
        ...resultForm,
        avatarId: resultForm.avatarId || randomAvatar.id,
        avatarUrl: resultForm.avatarUrl || randomAvatar.url,
        userType,
        ageRange,
        name: userName?.value,
        gender: gender >= 0 ? gender : currentGender?.id,
        hasPinCode: resultForm?.hasPinCode || false,
        isKid
      })
    );
  }, [userType, ageRange, ageRanges, userName?.value, gender, genders, resultForm?.hasPinCode]);

  // Classnames
  const disabledKidsClassName = classNames(
    'grid-x align-middle padding-small-up-top-12 padding-large-up-top-16 align-justify',
    isKid && styles.opacity
  );

  const blockClassName = classNames(
    'padding-large-up-24 padding-small-up-12 size-w-full',
    styles.block
  );
  const slideButtonCheckboxClassName = classNames(
    styles.slideButtonCheckbox,
    isKid && styles.slideButtonCheckboxActive
  );

  return (
    <div
      className={classNames(
        'flex-box align-middle m-x-auto p-x2 animate-fade-in relative',
        styles['section-lobby-profile']
      )}
    >
      <article className="padding-y-large-up-24 size-w-full">
        <h2 className="text text-center text-medium text-22 text-large-up-32 text-xlarge-up-40 text-white size-w-full padding-small-up-bottom-24 padding-large-up-bottom-36">
          {TEXT.LOBBY_ADD_USER}
        </h2>

        <section className={blockClassName}>
          <article className="grid-x align-middle border-for-dark padding-small-up-bottom-12 padding-large-up-bottom-24">
            <div className="cell xlarge-4 margin-small-up-right-12 margin-xlarge-up-right-36 m-t flex-box align-center">
              <AvatarProfile
                imgSrc={resultForm?.avatarUrl}
                editView
                active
                kids={isKid}
                onClick={handleSelectAvatar}
                iconType={ICON_KEY.EDIT}
              />
            </div>
            <div className="cell auto">
              <InputCustom
                className="input-for-dark"
                classInput={styles.input}
                label={TEXT.USER_NAME}
                id="username"
                type="text"
                valueOutput={userName}
                onBlur={userName?.onBlur}
                error={userName?.error}
                inputTagsClass="input-group-field p-x"
                placeholder={TEXT.USER_NAME}
                isErrClassAbsolute
                isRequiredLabel
                requiredLabelClassName={`padding-left-2 ${styles['text-required']}`}
              />
              <article className={classNames(isKid && styles.opacity)}>
                <p className="text text-white text-14 text-medium m-b">{TEXT.USER_LABEL.GENDER}</p>
                <div className="radio-group radio-group-flex align-middle">
                  {(genders || []).map((item: any) => (
                    <div
                      key={item.id}
                      className={classNames(
                        'radio radio-custom non-hover',
                        styles.radio,
                        styles['radio-gender']
                      )}
                    >
                      <input
                        type="radio"
                        name="gender"
                        id={item.id}
                        value={item.id}
                        checked={currentGender?.id === item.id}
                        onChange={() => handleChangeGender(item.id)}
                      />
                      <label className="text text-white text-medium text-12" htmlFor={item.id}>
                        {item.name}
                      </label>
                    </div>
                  ))}
                </div>
              </article>
            </div>
          </article>

          <article className="grid-x justify-content-between align-items-center padding-small-up-top-12 padding-large-up-top-16">
            <div className="cell auto grid-x button--vertical padding-small-up-right-24 padding-xlarge-up-right">
              <h4 className="text text-white padding-small-up-right-8">{TEXT.FOR_KID_TITLE}</h4>
              <span className="text text-muted text-14">{TEXT.FOR_KID_DESCRIPTION}</span>
            </div>
            <div className="cell shrink flex-box align-items-center">
              <label className={styles.slideButton}>
                <InputCustom
                  className={styles.slideButtonInput}
                  type="checkbox"
                  onChange={(e: any) => setIsKid(e.target.checked)}
                />
                <span className={slideButtonCheckboxClassName} />
              </label>
            </div>
          </article>

          <article className={disabledKidsClassName}>
            <div className="cell auto grid-x button--vertical padding-small-up-right-24 padding-xlarge-up-right">
              <div className="text text-white text-medium flex-box align-middle">
                <h4 className="text padding-small-up-right-8">{TEXT.PIN_CODE}</h4>
                {resultForm?.hasPinCode && <SvgIcon type={ICON_KEY.TICK_CIRCLE} isActive />}
              </div>
              <span className="text text-muted text-14">{TEXT.LOBBY_PIN_CODE_CONTENT}</span>
            </div>
            <div className="cell shrink">
              <Button
                className="button text-green button--custom-small-up-28-14 p-x"
                subTitle={resultForm?.hasPinCode ? TEXT.DELETE_PIN_CODE : TEXT.TITLE_TYPE_PIN_CODE}
                title={resultForm?.hasPinCode ? TEXT.DELETE_PIN_CODE : TEXT.TITLE_TYPE_PIN_CODE}
                onClick={handleOpenPinCode}
              />
            </div>
          </article>
        </section>

        <section
          className={classNames(
            'button-group child-auto padding-y-small-up-20 padding-large-up-bottom-36 size-w-full bg-gray20',
            styles['button-group']
          )}
        >
          <Button
            className="button button--dark button--xlarge-up button--medium hollow"
            subTitle={TEXT.CANCEL}
            title={TEXT.CANCEL}
            onClick={handleClose}
            textClass="text-white"
          />
          <Button
            className="button button--light button--xlarge-up button--medium"
            subTitle={TEXT.FINISHED}
            title={TEXT.FINISHED}
            onClick={handleOnSuccess}
          />
        </section>
      </article>
    </div>
  );
};

export default React.memo(LobbyAddUserProfile);
