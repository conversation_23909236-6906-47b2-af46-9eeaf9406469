// Modules
@use 'sass:math';

/* =============== function calculate rem =============== */
@function rem($size) {
  //
  $remSize: math.div($size, 16);
  //
  @return #{$remSize}rem;
}

/* =============== style =============== */
.section-lobby-profile {
  max-width: rem(620);
  .text-required {
    color: #e74c3c;
  }

  .radio {
    &:not(.non-hover) {
      input[type='radio'] {
        &:checked {
          & + label::before,
          & ~ label::before {
            background-color: transparent;
          }
          & + label::after,
          & ~ label::after {
            background-color: #3ac882;
          }
        }
      }
    }
    &.radio-gender {
      input[type='radio'] {
        & ~ label::before,
        & + label::before {
          width: rem(14);
          height: rem(14);
          margin-top: rem(-7);
          border-width: rem(1);
        }
        & ~ label::after,
        & + label::after {
          width: rem(10);
          height: rem(10);
          left: rem(10);
          margin-top: rem(-5);
          margin-left: rem(-8);
        }
        & ~ label,
        & + label {
          padding-left: rem(16);
          margin-right: rem(8);
        }
      }
    }
  }

  .input {
    border-bottom-color: #646464 !important;
  }

  .lineHeight {
    line-height: normal;
  }

  .block {
    background-color: #1b1b1b;
    border: 1px solid rgba(51, 51, 51, 1);
    border-radius: rem(8);
  }

  .opacity {
    opacity: 0.3;
    pointer-events: none;
  }
  .button:disabled:not(.button--custom) {
    span {
      color: #3ac882;
    }
  }
  .lobby-modal {
    padding-top: rem(60);
  }
  .font-size {
    font-size: rem(14);
  }

  @media screen and (min-width: rem(768)) {
    height: calc(100vh - #{rem(56)});
  }

  @media screen and (max-width: rem(600)) {
    &::before {
      content: '';
      background: linear-gradient(0deg, rgba(17, 17, 17, 0) 0%, #111111 100%);
      width: 100%;
      height: rem(40);
      position: fixed;
      top: 0;
      left: 0;
      z-index: 1;
    }

    //
    .button-group {
      button {
        span {
          font-size: rem(14);
        }
      }
    }

    //
    .radio {
      &.radio-gender {
        input[type='radio'] {
          & ~ label,
          & + label {
            padding-left: rem(13);
          }
        }
      }
    }

    .button-group {
      position: sticky;
      left: 0;
      bottom: 0;
      button {
        span {
          font-size: rem(14);
        }
      }
    }
  }
}
.filter {
  div[data-alignment='bottom-right'] {
    left: unset;
    margin-top: rem(8);
    width: rem(145);
  }
}

// Slide button
.slideButton {
  position: relative;
  width: rem(36);
  height: rem(19);

  // input checkbox
  &Input {
    opacity: 0;
    width: 0;
    height: 0;
    padding-bottom: 0;
  }

  // button checkbox
  &Checkbox {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(153, 153, 153, 0.6);
    border-radius: rem(50);
    transition: 0.4s;
    cursor: pointer;

    &::before {
      position: absolute;
      content: '';
      top: rem(3);
      left: rem(3);
      height: rem(13);
      width: rem(13);
      background-color: #fff;
      border-radius: rem(50);
      transition: 0.4s;
    }

    // checkbox active
    &Active {
      background-color: #3ac882;

      &::before {
        transform: translateX(rem(16));
      }
    }
  }
}
