import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import classNames from 'classnames';
import isEqual from 'lodash/isEqual';
import AvatarProfile from '@components/basic/AvatarProfile';
import Button from '@components/basic/Buttons/Button';
import InputCustom from '@components/basic/Input/InputCustom';
import SvgIcon from '@components/basic/Icon/SvgIcon';
import { openPopup } from '@actions/popup';
import { ERROR_CODE, ICON_KEY, LOBBY_PROFILE_STEP, PIN_CODE, POPUP } from '@constants/constants';
import { TEXT } from '@constants/text';
import useValidateCharsInput from '@components/basic/Input/useValidateCharsInput';
import { createUniqueProfileName } from '@services/multiProfileServices';
import {
  trackingMultiProfileAccept,
  trackingMultiProfileAvatarEdit,
  trackingMultiProfileEditLoad,
  trackingMultiProfileGenderEdit,
  trackingMultiProfileNameEdit,
  trackingMultiProfilePinCodeDelete,
  trackingMultiProfilePinCodeEdit,
  trackingMultiProfileUserDeletion
} from '@tracking/functions/TrackingEditMultiProfile';
import {
  resetResultForm,
  setDefaultForm,
  setLobbyStep,
  setResultForm,
  setStatusLobbyProfile
} from '@actions/multiProfile';
import MultiProfileApi from '@apis/MultiProfile';
import { setToast } from '@actions/app';
import styles from './LobbyUserProfile.module.scss';

const LobbyEditUserProfile = () => {
  // redux handle
  const dispatch = useDispatch();
  const { genders, resultForm, multiProfile, currentProfile, defaultForm } = useSelector(
    (state: any) => state?.MultiProfile || {}
  );
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const profileUserType = useSelector((state: any) => state?.User?.USER_TYPE);

  const [gender, setGender] = useState(resultForm?.gender);
  const isProfileONer = !isEmpty(profile) && !isEmpty(resultForm) && profile?.id === resultForm?.id;

  const userName = useValidateCharsInput(
    resultForm?.name || createUniqueProfileName(multiProfile?.items || [])
  );

  const isKid = resultForm?.isKid;

  // Handle get userType
  const handleChangeGender = (value: any) => setGender(value);

  // Back to lobby view
  const handleCallbackToLobbyView = (text?: any) => {
    dispatch(setLobbyStep());
    if (text) {
      dispatch(setToast({ message: text }));
    }
    dispatch(resetResultForm({ status: LOBBY_PROFILE_STEP.EDIT }));
  };

  // Handle select avatar
  const handleSelectAvatar = () => {
    // Tracking when click avatar edit
    trackingMultiProfileAvatarEdit({
      userType: profileUserType?.userType
    });
    dispatch(setLobbyStep(LOBBY_PROFILE_STEP.AVATAR_SELECT));
    dispatch(setStatusLobbyProfile(LOBBY_PROFILE_STEP.EDIT));
  };

  // Handle edit close
  const handleLobbyEditClose = () => {
    if (!isEqual(resultForm, defaultForm)) {
      // Show Popup confirm when resultForm changed
      dispatch(
        openPopup({
          name: POPUP.NAME.CONFIRM_WHEN_RETURN,
          lobbyType: LOBBY_PROFILE_STEP.EDIT
        })
      );
    }
    handleCallbackToLobbyView();
  };

  // Handle edit profile
  const handleOnSuccess = () => {
    const currentGender = genders.find((genderItems: any) => genderItems?.id === gender);
    if (currentProfile?.name !== userName?.value) {
      // Tracking name edit
      trackingMultiProfileNameEdit({
        profileName: userName?.value,
        userType: profileUserType?.userType
      });
    }
    if (currentProfile?.gender !== currentGender?.id) {
      // Tracking gender edit
      trackingMultiProfileGenderEdit({
        profileGender: currentGender?.name,
        userType: profileUserType?.userType
      });
    }
    // Tracking accept dialog
    trackingMultiProfileAccept({
      profileAge: resultForm?.ageRange,
      profileGender: currentGender?.name,
      userType: profileUserType?.userType
    });
    const { pinCode, newPinCode, id, avatarId, isForgotPinCode } = resultForm || {};
    const dataRequest = {
      name: userName?.value,
      gender,
      isKid,
      avatarId,
      pinCode: pinCode !== newPinCode || isForgotPinCode ? pinCode : null,
      newPinCode: !isForgotPinCode ? newPinCode : null,
      ageRange: resultForm?.ageRange,
      profileId: id
    };
    MultiProfileApi.editProfile(dataRequest).then((res) => {
      if (res?.success) {
        handleCallbackToLobbyView(TEXT.LOBBY_PROFILE_EDIT);
      } else if (res?.data?.code === ERROR_CODE.CODE_21) {
        dispatch(setToast({ message: TEXT.INVALID_NAME }));
      } else {
        dispatch(setToast({ message: TEXT.MSG_ERROR }));
      }
    });
  };

  const handleOpenPopupDeleteProfile = () => {
    // Tracking profile user deletion
    trackingMultiProfileUserDeletion({
      userType: profileUserType?.userType
    });
    dispatch(
      openPopup({
        name: POPUP.NAME.DELETE_PROFILE
      })
    );
  };

  // Handle open pin code popup
  const handleDeleteCreatePinCode = () => {
    if (resultForm?.hasPinCode) {
      // Tracking delete pinCode
      trackingMultiProfilePinCodeDelete({
        userType: profileUserType?.userType
      });

      // Delete Pin Code
      dispatch(
        setResultForm({
          hasPinCode: false,
          newPinCode: resultForm?.pinCode ? '' : null,
          deletePinCode: true
        })
      );
    } else {
      // Change PinCode
      dispatch(
        openPopup({
          name: POPUP.NAME.PIN_CODE,
          status: PIN_CODE.CREATE
        })
      );
    }
  };

  const handleOnChangePinCode = () => {
    // Tracking edit pinCode
    trackingMultiProfilePinCodeEdit({
      userType: profileUserType?.userType
    });
    dispatch(
      openPopup({
        name: POPUP.NAME.PIN_CODE,
        status: PIN_CODE.CREATE
      })
    );
  };

  useEffect(() => {
    // Tracking edit profile load
    if (resultForm?.id) {
      trackingMultiProfileEditLoad({
        userType: profileUserType?.userType
      });
    }
  }, [resultForm?.id]);

  // Handle check pinCode Default Form
  useEffect(() => {
    if (resultForm && resultForm.pinCode) {
      dispatch(
        setDefaultForm({
          ...defaultForm,
          ...(resultForm?.hasPinCode && { pinCode: resultForm?.pinCode })
        })
      );
    }
  }, [resultForm?.pinCode]);

  // Handle change Result Form
  useEffect(() => {
    dispatch(
      setResultForm({
        id: resultForm?.id,
        name: userName?.value,
        avatarId: resultForm?.avatarId,
        avatarUrl: resultForm?.avatarUrl,
        ageRange: resultForm?.ageRange || null,
        gender,
        isKid,
        hasPinCode: resultForm?.hasPinCode || false
      })
    );
  }, [
    userName?.value,
    resultForm?.avatarId,
    resultForm?.avatarUrl,
    resultForm?.ageRange,
    gender,
    resultForm?.hasPinCode
  ]);

  // ClassNames
  const lobbyProfileClassName = classNames(
    'flex-box align-middle m-x-auto p-x2 animate-fade-in relative',
    styles['section-lobby-profile']
  );
  const blockContentClassName = classNames(
    'size-w-full padding-large-up-24 padding-small-up-12 padding-bottom-0',
    styles.block
  );
  const blockContentInfoClassName = classNames(
    'grid-x align-middle',
    !isKid ? 'border-for-dark padding-y-small-up-12 padding-y-large-up-24' : 'padding-y-12'
  );
  const blockDeleteClassName = classNames(
    'padding-x-large-up-24 padding-x-small-up-12 padding-y-large-up-16 padding-y-small-up-12 size-w-full',
    styles.block
  );

  return (
    <div className={lobbyProfileClassName}>
      <article className="padding-y-large-up-24 size-w-full">
        <h2 className="text text-center text-medium text-24 text-xlarge-up-40 text-white size-w-full padding-small-up-bottom-24 padding-large-up-bottom-36">
          {TEXT.EDIT_INFORMATION}
        </h2>

        <section className={blockContentClassName}>
          <article
            className={classNames(
              'border-for-dark padding-large-up-bottom-24 padding-small-up-bottom-12'
            )}
          >
            <h4 className="text text-white text-14">
              {TEXT.USER}:
              <span className="text-bold p-l1">{isKid ? TEXT.CHILDREN : TEXT.ADULT}</span>
            </h4>
          </article>

          <article className={blockContentInfoClassName}>
            <div className="cell xlarge-4 margin-small-up-right-12 margin-xlarge-up-right-36 m-t flex-box align-center">
              <AvatarProfile
                imgSrc={resultForm?.avatarUrl}
                editView
                kids={isKid}
                active
                onClick={handleSelectAvatar}
                iconType={ICON_KEY.EDIT}
              />
            </div>
            <div className="cell auto padding-small-up-top-12 padding-large-up-top-12">
              <InputCustom
                className="input-for-dark"
                classInput={styles.input}
                label={TEXT.USER_NAME}
                id="username"
                type="text"
                valueOutput={userName}
                onBlur={userName?.onBlur}
                error={userName?.error}
                inputTagsClass="input-group-field p-x"
                placeholder={TEXT.USER_NAME}
                isErrClassAbsolute
                isRequiredLabel
                requiredLabelClassName={`padding-left-2 ${styles['text-required']}`}
              />
              {!isKid && (
                <>
                  <p className="text text-white text-14 text-medium m-b">
                    {TEXT.USER_LABEL.GENDER}
                  </p>
                  <div className="radio-group radio-group-flex align-middle">
                    {(genders || []).map((item: any) => (
                      <div
                        key={item.id}
                        className={classNames(
                          'radio radio-custom non-hover',
                          styles.radio,
                          styles['radio-gender']
                        )}
                      >
                        <input
                          type="radio"
                          name="gender"
                          id={item.id}
                          value={item.id}
                          disabled={isKid}
                          onChange={() => handleChangeGender(item.id)}
                          defaultChecked={resultForm?.gender === item.id}
                        />
                        <label className="text text-white text-medium text-12" htmlFor={item.id}>
                          {item.name}
                        </label>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </div>
          </article>

          {!isKid && (
            <>
              <article className="grid-x justify-items-between align-items-start padding-small-up-top-12 padding-large-up-top-20">
                <div className="cell auto grid-x button--vertical">
                  <div className="text text-white text-14 text-medium flex-box align-middle">
                    <h4 className="text text-14 padding-small-up-right-8">{TEXT.PIN_CODE}</h4>
                    {resultForm?.hasPinCode && <SvgIcon type={ICON_KEY.TICK_CIRCLE} isActive />}
                  </div>
                  <span className="text text-muted text-12">{TEXT.LOBBY_PIN_CODE_CONTENT}</span>
                </div>
                <div className={classNames('cell shrink', styles.lineHeight)}>
                  <Button
                    disabled={isKid}
                    className="button size-h-auto text-green button--custom-small-up-28-14"
                    subTitle={
                      resultForm?.hasPinCode ? TEXT.DELETE_PIN_CODE : TEXT.TITLE_TYPE_PIN_CODE
                    }
                    title={resultForm?.hasPinCode ? TEXT.DELETE_PIN_CODE : TEXT.TITLE_TYPE_PIN_CODE}
                    onClick={handleDeleteCreatePinCode}
                  />
                </div>
              </article>
              <article
                className={classNames(
                  'grid-x align-middle padding-y-small-up-24',
                  !resultForm?.hasPinCode && styles.opacity
                )}
              >
                <div className="cell auto">
                  <div className="text text-white text-14 text-medium flex-box align-middle">
                    <h4 className="text text-14 padding-small-up-right-8">
                      {TEXT.CHANGE_PIN_CODE}
                    </h4>
                  </div>
                </div>
                <div className="cell shrink">
                  <Button
                    disabled={!resultForm?.hasPinCode}
                    className={`button text-green button--custom-small-up-28-14 ${styles.button}`}
                    subTitle={TEXT.CHANGE}
                    title={TEXT.CHANGE}
                    onClick={handleOnChangePinCode}
                  />
                </div>
              </article>
            </>
          )}
        </section>

        {!isProfileONer && (
          <section className={classNames(blockDeleteClassName, 'grid-x align-middle margin-top-8')}>
            <div className="cell auto">
              <div className="text text-white text-14 text-medium flex-box align-middle">
                <h4 className="text text-14 padding-small-up-right-8">{TEXT.LOBBY_REMOVE_USER}</h4>
              </div>
            </div>
            <div className="cell shrink">
              <Button
                className="button text-green button--custom-small-up-28-14"
                subTitle={TEXT.REMOVE}
                title={TEXT.REMOVE}
                onClick={handleOpenPopupDeleteProfile}
              />
            </div>
          </section>
        )}

        <section
          className={classNames(
            'button-group child-auto padding-y-small-up-20 padding-large-up-bottom-36 size-w-full bg-gray20',
            styles['button-group']
          )}
        >
          <Button
            className="button button--dark button--xlarge-up button--medium hollow"
            subTitle={TEXT.CANCEL}
            title={TEXT.CANCEL}
            onClick={() => handleLobbyEditClose()}
            textClass="text-white"
          />
          <Button
            className="button button--light button--xlarge-up button--medium"
            subTitle={TEXT.FINISHED}
            title={TEXT.FINISHED}
            onClick={handleOnSuccess}
          />
        </section>
      </article>
    </div>
  );
};

export default React.memo(LobbyEditUserProfile);
