import React, { useEffect, useMemo, useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import AvatarProfile from '@components/basic/AvatarProfile';
import Button from '@components/basic/Buttons/Button';
import {
  clearProfile,
  getAvatars,
  getConfigAgeRanges,
  getConfigAgeRangesKid,
  getConfigGenders,
  getMultiProfile,
  resetResultForm,
  selectedProfile,
  setDefaultForm,
  setLobbyStep,
  setResultForm
} from '@actions/multiProfile';
import MultiProfileApi from '@apis/MultiProfile';
import { openPopup } from '@actions/popup';
import { ICON_KEY, LOBBY_PROFILE_STEP, PAGE, PIN_CODE, POPUP } from '@constants/constants';
import { useVieRouter } from '@customHook';
import LocalStorage from '@config/LocalStorage';
import { decodeParamDestination, queryStringEncoding } from '@helpers/common';
import {
  confirmLoad,
  lobbyWhoWatchingLoad,
  chooseProfile,
  config,
  configChoose,
  configComplete,
  addProfile
} from '@tracking/functions/TrackingMultiProfileLobby';
import { VALUE } from '@config/ConfigSegment';
import { UtmParams } from '@models/subModels';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import dynamic from 'next/dynamic';
import styles from './LobbyView.module.scss';
import { TEXT } from '../../../constants/text';
import { handleLoginParams } from '@services/multiProfileServices';

declare const window: any;

const LobbyAds = dynamic(() => import('@components/OutstreamAds/LobbyAds'), { ssr: false });

const LobbyView = () => {
  const router = useVieRouter();
  const { query, asPath } = router || {};
  const dispatch = useDispatch();

  // View state
  const [editing, setEditing] = useState(false);

  // Agreement flag
  const [agreementDone, setAgreementDone] = useState(false);

  // Redux selectors
  const { resultForm, multiProfile, currentProfile } = useSelector(
    (state: any) => state?.MultiProfile || {}
  );
  const isKid = currentProfile?.isKid || false;
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const userType = useSelector((state: any) => state?.User?.USER_TYPE);
  const webConfig = useSelector((state: any) => state?.App?.webConfig || {});
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const isMobile = useSelector((state: any) => state?.App.isMobile);
  const { notSaveProfile } = webConfig?.multiProfile || {};

  const profilesLobby = useMemo(() => (multiProfile?.items || []).slice(0, 5), [multiProfile]);

  // Initial load: fetch profiles & track
  useEffect(() => {
    dispatch(getMultiProfile());
    lobbyWhoWatchingLoad({
      flowName: VALUE.MULTI_PROFILE_LOBBY,
      userType: userType?.userType,
      isTrackImmediately: true
    });
    return () => {
      dispatch(clearProfile());
      return;
    };
  }, []);

  // On route change: reset and run tutorial→agreement
  useEffect(() => {
    setAgreementDone(false);
    MultiProfileApi.getLobbyTutorial().then((res) => {
      if (res.httpCode === 404) {
        dispatch(
          openPopup({
            name: POPUP.NAME.LOBBY_GUIDE,
            onClose: runAgreementCheck
          })
        );
      } else {
        runAgreementCheck();
      }
    });
  }, [asPath]);

  const runAgreementCheck = useCallback(() => {
    MultiProfileApi.getAgreement().then((res) => {
      if (res.httpCode === 404) {
        dispatch(
          openPopup({
            name: POPUP.NAME.CONFIRM_OVER_18,
            onClose: () => setAgreementDone(true)
          })
        );
        confirmLoad({ flowName: VALUE.MULTI_PROFILE_CONFIRM, userType: userType?.userType });
      } else {
        setAgreementDone(true);
      }
    });
  }, [userType]);

  // Redirect if no profiles
  useEffect(() => {
    if (multiProfile?.empty) {
      router.push({ pathname: PAGE.HOME, query: UtmParams(query) });
    }
  }, [multiProfile?.empty]);

  // Enter edit mode if needed
  useEffect(() => {
    if (resultForm?.status === LOBBY_PROFILE_STEP.EDIT) {
      setEditing(true);
    }
  }, [resultForm?.status]);

  // Kid auto-redirect
  useEffect(() => {
    if (isKid && !notSaveProfile) {
      router.push(PAGE.HOME);
    }
  }, [isKid]);

  const handleOnEditInformation = () => {
    setEditing((prev) => !prev);
    if (!editing) {
      config({ flowName: VALUE.MULTI_PROFILE_LOBBY, userType: userType?.userType });
    } else {
      configComplete({ flowName: VALUE.MULTI_PROFILE_LOBBY, userType: userType?.userType });
    }
  };

  const handleGetAvatarAgeGender = () => {
    dispatch(getAvatars());
    dispatch(getConfigGenders());
    dispatch(getConfigAgeRanges());
    dispatch(getConfigAgeRangesKid());
  };
  const handleAddNewProfile = () => {
    handleGetAvatarAgeGender();
    dispatch(setLobbyStep(LOBBY_PROFILE_STEP.ADD));
    dispatch(resetResultForm({}));
    dispatch(setDefaultForm({}));
    addProfile({ flowName: VALUE.MULTI_PROFILE_LOBBY, userType: userType?.userType });
  };
  const handleOnSubmitEdit = ({ pinCode }: any) => {
    dispatch(setResultForm({ pinCode }));
    dispatch(setLobbyStep(LOBBY_PROFILE_STEP.EDIT));
    if (pinCode) dispatch(openPopup());
  };
  const handleEditProfile = (profileItem: any) => {
    handleGetAvatarAgeGender();
    if (!editing) return;
    if (profileItem?.hasPinCode) {
      dispatch(
        openPopup({
          name: POPUP.NAME.PIN_CODE,
          data: profileItem,
          status: PIN_CODE.VERIFY_EDIT,
          onSubmitForm: handleOnSubmitEdit
        })
      );
    } else {
      dispatch(setLobbyStep(LOBBY_PROFILE_STEP.EDIT));
    }
    dispatch(setResultForm(profileItem));
    dispatch(
      setDefaultForm({
        ...profileItem,
        ...(resultForm?.status && { status: LOBBY_PROFILE_STEP.EDIT })
      })
    );
  };

  // Click avatar: check agreement, then proceedSelect
  const handleAfterSelectedProfile = (item: any, index: any) => {
    if (!agreementDone) {
      MultiProfileApi.getAgreement().then((res) => {
        if (res.httpCode === 404) {
          dispatch(
            openPopup({
              name: POPUP.NAME.CONFIRM_OVER_18,
              onClose: () => {
                setAgreementDone(true);
                proceedSelect(item, index);
              }
            })
          );
          confirmLoad({ flowName: VALUE.MULTI_PROFILE_CONFIRM, userType: userType?.userType });
        } else {
          setAgreementDone(true);
          proceedSelect(item, index);
        }
      });
    } else {
      proceedSelect(item, index);
    }
  };

  // Final selection and redirect
  const proceedSelect = (item: any, index: any) => {
    ConfigLocalStorage.set(LocalStorage.HIDE_MASTHEAD, '1');
    const { id } = item || {};
    if (!id) return;

    const reLoginParams: any = ConfigLocalStorage.get(LocalStorage.RE_LOGIN_PARAMS);
    const params: any = JSON.parse(reLoginParams || '{}');
    const urlParams = new URLSearchParams(params.url?.slice(params.url.indexOf('?')));
    const auth = Object.fromEntries(urlParams.entries());
    const { query } = router || {};
    const destination = query?.destination
      ? `${query.destination}${query?.vid ? `&vid=${query.vid}` : ''}`
      : '/';

    const utmQuery = queryStringEncoding(UtmParams(query));
    const utmQueryStr = utmQuery ? `?${utmQuery}` : '';
    const callback = () => {
      if (auth.destination) {
        window.location = auth.destination;
      } else if (currentProfile?.id && currentProfile.id !== id) {
        window.location = `${PAGE.HOME}${utmQueryStr}`;
      } else if (reLoginParams) {
        handleLoginParams({ reLoginParams, isFromLobby: true });
      } else if (destination) {
        window.location = `${decodeParamDestination(destination)}${utmQueryStr}`;
      } else {
        window.location = PAGE.HOME;
      }
    };

    dispatch(
      selectedProfile({
        currentProfile,
        data: item,
        callback,
        isReloadPage: true,
        notSaveProfile
      })
    );

    chooseProfile({
      userType: userType?.userType,
      profileOrder: index,
      menuName: TEXT.ACCOUNT,
      isTrackImmediately: true
    });
  };

  const handleControlProfile = (profileItem: any, index: any) => {
    if (editing) {
      handleEditProfile(profileItem);
      configChoose({ flowName: VALUE.MULTI_PROFILE_LOBBY, userType: userType?.userType });
    } else {
      handleAfterSelectedProfile(profileItem, index);
    }
  };

  const handleAvatarDefault = () => {
    const len = 5 - multiProfile?.items?.length || 0;
    if (len <= 0) return;
    return (
      <>
        {Array.from(Array(len), (e, i) => {
          if (i === 0) {
            return (
              <AvatarProfile
                key={i}
                name={TEXT.LOBBY_ADD_USER}
                size="small"
                onClick={handleAddNewProfile}
                className="margin-6 margin-lg-up-8"
                iconType={ICON_KEY.PLUS}
              />
            );
          }
          return (
            <AvatarProfile
              key={i}
              name={TEXT.LOBBY_ADD_USER}
              size="small"
              onClick={handleAddNewProfile}
              className="margin-6 margin-lg-up-8"
              iconType={ICON_KEY.PLUS}
            />
          );
        })}
      </>
    );
  };

  return (
    <section
      className={classNames(
        'flex items-center height-viewport-header height-viewport-header-large mx-auto px-3 md:px-[3.02083vw] animate-fade-in',
        styles['section--lobby-view']
      )}
    >
      <article className="w-full">
        <h1 className={`title title-white text-center ${styles['title-font']}`}>
          {!editing ? TEXT.LOBBY_VIEW : TEXT.EDIT_INFORMATION}
        </h1>

        <div className="flex flex-row flex-wrap justify-center margin-top-4 margin-top-lg-up-20 margin-bottom-lg-up-10 mx-auto">
          {profilesLobby.map((item: any, index: any) => (
            <AvatarProfile
              key={item.id}
              imgSrc={item.avatarUrl}
              name={item.name}
              size="small"
              active
              current={item.id === currentProfile?.id}
              onClick={() => handleControlProfile(item, index)}
              edit={editing}
              iconType={editing && ICON_KEY.EDIT}
              kids={item.isKid}
              locked={item?.hasPinCode}
              className="margin-6 margin-top-6 margin-lg-up-8"
            />
          ))}
          {handleAvatarDefault()}
        </div>

        <section
          className={classNames(
            'button-group mt-5 size-w-full bg-gray20 align-center',
            styles['button-group']
          )}
        >
          <Button
            className={`button button--xlarge-up animate-fade-in${
              editing ? ' button--light !text-vo-dark-gray-900' : ' button--dark hollow text-white'
            }`}
            title={editing ? TEXT.FINISHED : TEXT.EDIT_INFORMATION}
            onClick={handleOnEditInformation}
          />
        </section>

        {!isMobile && !profile?.isPremium && !isGlobal && <LobbyAds />}
      </article>
    </section>
  );
};

export default LobbyView;
