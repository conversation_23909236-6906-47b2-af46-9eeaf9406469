// Modules
@use 'sass:math';

/* =============== function caculate rem =============== */
@function rem($size) {
  //
  $remSize: math.div($size, 16);

  //
  @return #{$remSize}rem;
}

/* =============== style =============== */
.section--lobby-view {
  .title-font {
    font-size: rem(40);
    font-weight: 700;
  }

  @media screen and (max-width: rem(600)) {
    align-items: flex-start;
    height: auto;
    &::before {
      content: '';
      background: linear-gradient(0deg, rgba(17, 17, 17, 0) 0%, #111111 100%);
      width: 100%;
      height: rem(40);
      position: fixed;
      top: 0;
      left: 0;
      z-index: 1;
    }
    .button-group {
      position: sticky;
      left: 0;
      bottom: 0;
      button {
        span {
          font-size: rem(14);
        }
      }
    }
  }

  @media screen and (max-width: rem(480)) {
    .title-font {
      font-size: rem(22);
    }
  }
}
