@function rem($size) {
  $remSize: $size / 16;
  @return #{$remSize}rem;
}

.section--avatar-profile-option {
  max-width: rem(1198) !important;
  .text-size-28 {
    font-size: rem(28);
  }
  .text-size-32 {
    font-size: rem(32);
  }
  .text-size-40 {
    font-size: rem(40);
  }
  @media screen and (max-width: rem(600)) {
    .text-size-28 {
      font-size: rem(18);
    }
    .text-size-32 {
      font-size: rem(14);
    }
  }
}

.error_load_container {
  width: rem(400);
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100vh - rem(52));
  flex-direction: column;
}

.size_button {
  width: rem(132);
}

.text-error-load {
  padding: rem(36) 0 rem(24);
  margin: 0;
}
.size_avatar {
  width: rem(180) !important;
  height: rem(180);
}
