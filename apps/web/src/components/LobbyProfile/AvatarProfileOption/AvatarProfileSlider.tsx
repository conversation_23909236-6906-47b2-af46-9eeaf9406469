import React, { useEffect, useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { NextArrow, PrevArrow } from '@components/basic/Buttons/ButtonArrow';
import classNames from 'classnames';
import AvatarProfile from '@components/basic/AvatarProfile';
import { useDispatch, useSelector } from 'react-redux';
import { setLobbyStep, setResultForm, setStatusLobbyProfile } from '@actions/multiProfile';
import { LOBBY_PROFILE_STEP } from '@constants/constants';
import { useViewport } from '@customHook';
import { trackingMultiProfileAvatarChose } from '@tracking/functions/TrackingEditMultiProfile';
import { multiProfileAddProfileChooseAvatar } from '@tracking/functions/TrackingAddMultiProfile';
import { VALUE } from '@config/ConfigSegment';
import styles from './AvatarRibbon.module.scss';

const AvatarProfileSlider = ({ data, ribbonOrder, ribbonName, ribbonId }: any) => {
  const viewport = useViewport();
  const [swiper, setSwiper] = useState<any>({});
  const [activeIndex, setActiveIndex] = useState(0);
  const [next, setNext] = useState(false);
  const [prev, setPrev] = useState(false);

  // Mobile viewport
  const isMobileViewPort = viewport.width < 768;
  const isTabletViewPort = viewport.width < 1200 && viewport.width >= 768;

  // Redux handle
  const dispatch = useDispatch();
  const { isMobile } = useSelector((state: any) => state.App);
  const { statusLobbyProfile, resultForm } = useSelector((state: any) => state?.MultiProfile || {});
  const profileUserType = useSelector((state: any) => state?.User?.USER_TYPE);
  // check mobile viewport
  const itemView = isMobileViewPort || isTabletViewPort ? 4 : 6;
  const spaceVal = isMobileViewPort || isTabletViewPort ? 16 : 32;

  const arrowNextClassName = classNames({ [` swiper-button-disabled`]: !next });
  const arrowPrevClassName = classNames({ [` swiper-button-disabled`]: !prev });

  // Handle select avatar
  const handleSelectedAvatar = (
    avatarId: any,
    avatarUrl: any,
    avatarName: any,
    avatarOrder: any
  ) => {
    // Tracking change avatar
    if (statusLobbyProfile === LOBBY_PROFILE_STEP.ADD) {
      multiProfileAddProfileChooseAvatar({
        flowName: resultForm?.isKid
          ? VALUE.MULTI_PROFILE_ADD_PROFILE_KID
          : VALUE.MULTI_PROFILE_ADD_PROFILE_NON_KID,
        userType: profileUserType?.userType,
        data: {
          avatarId,
          avatarName,
          avatarOrder,
          ribbonId,
          ribbonName,
          ribbonOrder
        }
      });
    } else {
      trackingMultiProfileAvatarChose({
        userType: profileUserType?.userType
      });
    }

    // check avatar
    dispatch(setResultForm({ avatarId, avatarUrl }));
    dispatch(
      setLobbyStep(
        statusLobbyProfile === LOBBY_PROFILE_STEP.EDIT
          ? LOBBY_PROFILE_STEP.EDIT
          : LOBBY_PROFILE_STEP.ADD
      )
    );
    dispatch(setStatusLobbyProfile());
  };

  useEffect(() => {
    setNext(!swiper?.isEnd);
    setPrev(!(!activeIndex || swiper?.isBeginning));
  }, [swiper, activeIndex]);
  return (
    <Swiper
      className="slider relative style-1 overflow"
      onSlideChange={(swiper) => setActiveIndex(swiper.activeIndex)}
      allowTouchMove={!!isMobile}
      spaceBetween={spaceVal}
      slidesPerView={isMobile ? itemView : 'auto'}
      slidesPerGroup={itemView}
      data-item-view={itemView}
      onSwiper={(swiper) => setSwiper(swiper)}
      speed={300}
      lazy={{
        //  tell swiper to load images before they appear
        loadPrevNext: true,
        // amount of images to load
        loadPrevNextAmount: 2
      }}
    >
      {(data || []).map((item: any, index: any) => (
        <SwiperSlide key={item.id} className={`slider__item ${!isMobile && styles.size_avatar}`}>
          <AvatarProfile
            imgSrc={item?.url}
            active
            size="auto"
            onClick={() => handleSelectedAvatar(item.id, item?.url, item?.name, index + 1)}
          />
          <div className="swiper-lazy-preloader" />
        </SwiperSlide>
      ))}

      {!isMobile && (data || []).length > itemView && (
        <NextArrow onClickNext={() => swiper.slideNext()} nextClass={arrowNextClassName} />
      )}
      {!isMobile && (data || []).length > itemView && (
        <PrevArrow onClickPrev={() => swiper.slidePrev()} prevClass={arrowPrevClassName} />
      )}
    </Swiper>
  );
};

export default AvatarProfileSlider;
