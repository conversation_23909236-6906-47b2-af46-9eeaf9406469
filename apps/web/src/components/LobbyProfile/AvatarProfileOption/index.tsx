import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import isEmpty from 'lodash/isEmpty';
import { TEXT } from '@constants/text';
import Icon from '@components/basic/Icon/Icon';
import { ICON_KEY, LOBBY_PROFILE_STEP } from '@constants/constants';
import { setLobbyStep, setStatusLobbyProfile, getAvatars } from '@actions/multiProfile';
import AvatarProfileRibbon from '@components/LobbyProfile/AvatarProfileOption/AvatarProfileRibbon';
import Image from '@components/basic/Image/Image';
import ConfigImage from '@config/ConfigImage';
import Button from '@components/basic/Buttons/Button';
import { trackingMultiProfileAvatarBack } from '@tracking/functions/TrackingEditMultiProfile';
import {
  emptyAvatarLoaded,
  emptyAvatarLoadedTryAgain
} from '@tracking/functions/TrackingMultiProfileChoose';
import { multiProfileAddProfileChooseBack } from '@tracking/functions/TrackingAddMultiProfile';
import { VALUE } from '@config/ConfigSegment';
import styles from './AvatarRibbon.module.scss';

const AvatarProfileOption = () => {
  // Redux handle
  const dispatch = useDispatch();
  // Open when API ready
  const { avatars, statusLobbyProfile, resultForm } = useSelector(
    (state: any) => state?.MultiProfile || {}
  );
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const profileUserType = useSelector((state: any) => state?.User?.USER_TYPE);

  // Tracking avatar list empty load
  useEffect(() => {
    if (isEmpty(avatars)) {
      emptyAvatarLoaded();
    }
  }, [avatars]);

  // Handle go to back profile
  const gotoBackViewProfile = () => {
    // Tracking back to edit profile
    if (statusLobbyProfile === LOBBY_PROFILE_STEP.EDIT) {
      trackingMultiProfileAvatarBack({
        userType: profileUserType?.userType
      });
    }
    // Tracking back to add profile
    if (statusLobbyProfile === LOBBY_PROFILE_STEP.ADD) {
      multiProfileAddProfileChooseBack({
        flowName: resultForm?.isKid
          ? VALUE.MULTI_PROFILE_ADD_PROFILE_KID
          : VALUE.MULTI_PROFILE_ADD_PROFILE_NON_KID,
        userType: profileUserType?.userType
      });
    }
    dispatch(
      setLobbyStep(
        statusLobbyProfile === LOBBY_PROFILE_STEP.EDIT
          ? LOBBY_PROFILE_STEP.EDIT
          : LOBBY_PROFILE_STEP.ADD
      )
    );
    dispatch(setStatusLobbyProfile());
  };
  // reload list avatar when error
  const regetDataAvatarsList = () => {
    dispatch(getAvatars());
    emptyAvatarLoadedTryAgain();
  };

  return (
    <section
      className={`section section--user m-x-auto overflow ${styles['section--avatar-profile-option']}`}
    >
      {isEmpty(avatars) ? (
        <div className="container canal-v">
          <div className="section__header">
            <div className="display-flex-inline align-middle size-w-full p-y1">
              <button
                className="button button--for-dark button--large"
                type="button"
                onClick={gotoBackViewProfile}
              >
                <Icon iClass={`vie-chevron-left-r-medium ${styles['text-size-28']}`} />
              </button>
              <h1 className={`title title-white ${styles['text-size-40']}`}>
                {TEXT.LOBBY_PROFILE_AVATAR}
              </h1>
            </div>
          </div>
          <article className={styles.error_load_container}>
            <Image src={ConfigImage.errorLoad} />
            <p className={`text text-white text-center ${styles['text-error-load']}`}>
              {TEXT.ERROR_LOAD}
            </p>
            <Button
              title="Thử lại"
              className={`button button--light button--large ${styles.size_button}`}
              onClick={regetDataAvatarsList}
            />
          </article>
        </div>
      ) : (
        <div className={classNames('container', isMobile && 'canal-v')}>
          <div className="section__header">
            <div className="display-flex-inline align-middle size-w-full p-y1">
              <Button
                className="button button--for-dark button--large p-l"
                type="button"
                onClick={gotoBackViewProfile}
                iconType={ICON_KEY.BACK}
              />
              <h1 className={`title title-white ${styles['text-size-40']}`}>
                {TEXT.LOBBY_PROFILE_AVATAR}
              </h1>
            </div>
          </div>
          <div className="section__body">
            {(avatars || []).map((categoryItem: any, index: any) => (
              <AvatarProfileRibbon
                key={categoryItem.id}
                title={categoryItem.name}
                ribbonOrder={index + 1}
                ribbonId={categoryItem?.id}
                {...categoryItem}
              />
            ))}
          </div>
        </div>
      )}
    </section>
  );
};

export default AvatarProfileOption;
