import React from 'react';
import AvatarProfileSlider from './AvatarProfileSlider';
import styles from './AvatarRibbon.module.scss';

const AvatarProfileRibbon = ({ title, items, ribbonOrder, ribbonId }: any) => (
  <div className="m-b3">
    {title && (
      <div className={`text text-medium text-white m-b3 ${styles['text-size-28']}`}>{title}</div>
    )}
    <AvatarProfileSlider
      data={items}
      title={title}
      ribbonOrder={ribbonOrder}
      ribbonId={ribbonId}
      ribbonName={title}
    />
  </div>
);

export default AvatarProfileRibbon;
