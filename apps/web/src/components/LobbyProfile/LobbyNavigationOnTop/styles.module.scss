// Modules
@use 'sass:math';

/* =============== function caculate rem =============== */
@function rem($size) {
  //
  $remSize: math.div($size, 16);

  //
  @return #{$remSize}rem;
}

/* =============== style =============== */
.lobby-item {
  &-non {
    &:hover {
      // opacity: 0.5;
      img {
        @apply border-vo-green;
      }
    }
    img {
      @apply p-[.125rem] border border-solid border-white rounded-full aspect-square transition-all;
    }
  }
  &-premium {
    img {
      border-color: #da9e1c;
    }
  }
  & > a {
    display: flex !important;
  }

  .lobby-item-image {
    width: rem(24);
    height: rem(24);
    object-fit: cover;
    &-mobile {
      width: rem(30);
      height: rem(30);
      object-fit: cover;
    }
  }
  .icon-right {
    left: auto;
    right: 1.125rem;
    --pos-top: 50%;
    --pos-left: 1.125rem;
    transform: translateY(-50%);
  }
  .text-gray {
    color: #9b9b9b !important;
  }
  .break-spaces {
    white-space: break-spaces;
  }
}

.kid-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: rem(12);
  height: rem(12);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #3ac882;
  border-radius: 50%;
  svg {
    width: rem(8);
    height: rem(8);
  }
}
