import React, { useEffect, useState } from 'react';
import Image from '@components/basic/Image/Image';
import classNames from 'classnames';
import ConfigImage from '@config/ConfigImage';
import { useDispatch, useSelector } from 'react-redux';
import Button from '@components/basic/Buttons/Button';
import { getMultiProfile, selectedProfile } from '@actions/multiProfile';
import { ICON_KEY, PAGE } from '@constants/constants';
import Icon from '@components/basic/Icon/Icon';
import isEmpty from 'lodash/isEmpty';
import { useVieRouter } from '@customHook';
import { UtmParams } from '@models/subModels';
import { queryStringEncoding } from '@helpers/common';
import SvgIcon from '@components/basic/Icon/SvgIcon';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import LocalStorage from '@config/LocalStorage';
import styles from './styles.module.scss';

declare const window: any;

const LobbyNavigationOnTop = ({
  isCurrentProfile,
  mobileOnlyAvatar,
  handleToggleLobby,
  isPayment
}: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const webConfig = useSelector((state: any) => state?.App?.webConfig || {});
  const { notSaveProfile } = webConfig?.multiProfile || {};
  const { multiProfile, currentProfile } = useSelector((state: any) => state?.MultiProfile || {});
  const [toggleIsMore, setToggleIsMore] = useState(false);
  const [lobbyList, setLobbyList] = useState([]);

  const itemClassNames = classNames(
    'flex-box align-middle',
    isCurrentProfile ? 'p-y1' : 'relative padding-y-small-up-8'
  );
  const imageClassNames = classNames(
    'circle',
    !isCurrentProfile ? styles[`lobby-item-image${isMobile ? '-mobile' : ''}`] : 'size-square-32',
    isPayment && '!border-[#222]'
  );
  const spanClassNames = classNames(
    'text',
    styles['break-spaces'],
    isMobile
      ? 'padding-small-up-left-6 text-white text-bold size-mw-78per'
      : 'padding-left-5 padding-right-12'
  );
  const iconClassNames = classNames(`vie-chevron-${toggleIsMore ? 'up-r' : 'down-red'}-medium`);

  const handleSelectedProfile = ({ data }: any) => {
    ConfigLocalStorage.set(LocalStorage.HIDE_MASTHEAD, 1);
    const queryParams = queryStringEncoding(UtmParams(router.query));
    dispatch(
      selectedProfile({
        currentProfile,
        data,
        callback: () => (window.location = PAGE.HOME + (queryParams && `?${queryParams}`)),
        isReloadPage: true,
        notSaveProfile
      })
    );
  };

  const onMoreToggle = () => {
    handleToggleLobby(!toggleIsMore);
    setToggleIsMore(!toggleIsMore);
  };

  const handleImageDefault = (data: any) => {
    if (isEmpty(currentProfile) && isCurrentProfile) {
      return <Icon spClass="icon icon--medium text-white" iClass="vie-user-o-c-medium" />;
    }
    return (
      <p className="relative margin-b">
        <Image
          className={imageClassNames}
          src={data?.avatarUrl || ''}
          defaultSrc={ConfigImage.avatarProfileDefault}
          alt={data?.name}
        />
        {data?.isKid && (
          <span className={styles['kid-icon']}>
            <SvgIcon type={ICON_KEY.KID_SOLID} />
          </span>
        )}
        {profile?.isPremium && isCurrentProfile && (
          <label className="tags tags--member tags--member-premium absolute align-center middle-h bottom">
            <span className="icon">
              <i className="vie vie-vip" />
            </span>
          </label>
        )}
      </p>
    );
  };

  useEffect(() => {
    if ((!isCurrentProfile || isMobile) && profile?.id && isEmpty(multiProfile)) {
      dispatch(getMultiProfile());
    }
  }, [profile?.id, multiProfile, isCurrentProfile, isMobile]);

  useEffect(() => {
    if (!isEmpty(multiProfile)) {
      setLobbyList(
        (multiProfile?.items || []).filter((item: any) => item.id !== currentProfile?.id)
      );
    }
  }, [multiProfile]);

  const lobbyInfo = ({ data }: any) => (
    <>
      {handleImageDefault(data)}
      {(isMobile || !isCurrentProfile) && !mobileOnlyAvatar && (
        <span className={spanClassNames}>{data?.name}</span>
      )}
      {data?.hasPinCode && data?.id !== currentProfile?.id && (
        <Icon
          spClass={`icon--tiny absolute ${styles['icon-right']}`}
          iClass={`vie-lock-o-rc ${styles['text-gray']}`}
        />
      )}
    </>
  );

  const renderLobbyItem = ({ data }: any) => (
    <div
      className={classNames(
        styles['lobby-item'],
        (isMobile || isCurrentProfile) && styles['lobby-item-non'],
        profile?.isPremium && styles['lobby-item-premium']
      )}
      key={data.id}
    >
      {isCurrentProfile ? (
        <div className={itemClassNames}>
          {lobbyInfo({ data })}
          {isMobile && multiProfile?.items?.length > 0 && !mobileOnlyAvatar && (
            <Button
              className="button absolute right-2 top-2 text-white size-h-auto p-1"
              iconClass="icon--tiny"
              iconName={iconClassNames}
              subTitle={toggleIsMore ? 'Less' : 'More'}
              onClick={() => onMoreToggle()}
            />
          )}
        </div>
      ) : (
        <a
          className={itemClassNames}
          title={data?.name}
          onClick={() => handleSelectedProfile({ data })}
        >
          {lobbyInfo({ data })}
        </a>
      )}
    </div>
  );

  return isCurrentProfile
    ? renderLobbyItem({ data: currentProfile })
    : lobbyList.map((item: any) => renderLobbyItem({ data: item }));
};

export default LobbyNavigationOnTop;
