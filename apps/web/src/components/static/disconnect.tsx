import React, { useEffect, useState } from 'react';
import Image from '../basic/Image/Image';

const NoConnnect = () => {
  const lostConnectionImage = `/assets/images/lost_connect.svg`;

  const [countdown, setCountdown] = useState(0);

  const handleOnline = () => {
    setCountdown(0);
  };

  useEffect(() => {
    let timer: any;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  useEffect(() => {
    window.addEventListener('online', handleOnline);

    return () => {
      window.removeEventListener('online', handleOnline);
    };
  }, []);

  return (
    <div className="flex flex-col-reverse md:flex-row justify-between items-center pl-[104px] pr-[120px] pt-[104px] pb-[144px]">
      <div className="flex flex-col space-y-[48px]">
        <div className="flex flex-col text-white space-y-[12px]">
          <p className="!text-[58px] leading-[81.2px] font-[700]">Không có kết nối mạng</p>
          <p className="!text-[36px] font-[400] leading-[57.6px]">
            Vui lòng kiểm tra lại kết nối mạng và thử lại
          </p>
        </div>
        {/* <Button
          title={countdown > 0 ? `Thử lại sau (${countdown}s)` : 'Thử lại'}
          className={classNames(
            'bg-white text-black w-[470px] text-center h-[48px] font-[500] !text-[18px] leading-[28px] cursor-pointer',
            countdown > 0 && '!bg-[#C4C4C4] cursor-not-allowed'
          )}
          onClick={handleRetry}
          disabled={countdown > 0}
        /> */}
      </div>
      <div>
        <Image src={lostConnectionImage} className="w-[600px] h-[600px]" />
      </div>
    </div>
  );
};

export default NoConnnect;
