import Image from '@components/basic/Image/Image';
import get from 'lodash/get';
import { useSelector } from 'react-redux';
import { GOOGLE_STORE_ID, APPLE_STORE_ID } from '@config/ConfigEnv';
import ConfigImage from '@config/ConfigImage';
import { LINK_QRCODE_DOWNLOAD_APP } from '@constants/constants';
import { TEXT } from '@constants/text';
import React from 'react';

const DownloadDesktop = () => {
  const webConfig = useSelector((state: any) => state?.App?.webConfig || {});
  const appDownload = get(webConfig, 'appDownload', {});
  const linkQrCode = get(appDownload, 'qrCode', LINK_QRCODE_DOWNLOAD_APP);

  return (
    <div className="download download-footer">
      <div className="grid-x align-middle align-center">
        <div className="cell medium-shrink m-r2">
          <Image className="size-w-64" src={linkQrCode} alt="QR-Download-App-In-Footer" notWebp />
        </div>
        <div className="text-left">
          <div className="download-label p-l1 p-b1">{TEXT.QR_DOWNLOAD_APP}</div>

          <div className="grid-x align-middle">
            <div className="cell small-5 medium-auto m-r2">
              <a
                className="link link-down link-down-ios dark outline size-h-36 m-l1 p-y1 grid-x align-middle align-center"
                href={`https://itunes.apple.com/us/app/${APPLE_STORE_ID}`}
                target="_blank"
                rel="noreferrer"
              >
                <Image
                  src={ConfigImage.downLoadAppStore}
                  alt={TEXT.DOWNLOAD_APP_STORE}
                  title={TEXT.DOWNLOAD_APP_STORE}
                  className="flex-box m-x-auto"
                  notWebp
                />
              </a>
            </div>
            <div className="cell m-l1 p-y11 small-5 medium-auto">
              <a
                className="link link-down link-down-android dark outline size-h-36 m-l1 p-y1 grid-x align-middle align-center"
                target="_blank"
                rel="noreferrer"
                href={`https://play.google.com/store/apps/details?id=${GOOGLE_STORE_ID}`}
              >
                <Image
                  src={ConfigImage.downLoadAppGGPlay}
                  alt={TEXT.DOWNLOAD_APP_ANDROID}
                  title={TEXT.DOWNLOAD_APP_ANDROID}
                  className="flex-box m-x-auto"
                  notWebp
                />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DownloadDesktop;
