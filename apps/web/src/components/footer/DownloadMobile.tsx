import Image from '@components/basic/Image/Image';
import { GOOGLE_STORE_ID, APPLE_STORE_ID } from '@config/ConfigEnv';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';
import React from 'react';
import { VALUE } from '@config/ConfigSegment';
import TrackingMWebToApp from '@tracking/functions/TrackingMWebToApp';

const DownloadMobile = () => {
  const onClickDownLoadAppStore = () =>
    TrackingMWebToApp.footerAppStoreDownloadTouch({ flowName: VALUE.TRGGIER_DIRECTLY_FOOTER });
  const onClickDownLoadGooglePlay = () =>
    TrackingMWebToApp.footerGooglePlayDownloadTouch({ flowName: VALUE.TRGGIER_DIRECTLY_FOOTER });
  return (
    <div className="download download-footer border-st4-bottom p-b4 p-t3">
      <div className="grid-x grid-margin-x4 size-w-full p-x1 align-middle align-center">
        <div className="cell medium-shrink p-b2">
          <span className="download-label">{TEXT.DOWNLOAD_APP} tại</span>
        </div>
        <div className="cell small-6">
          <a
            className="link link-down link-down-ios dark outline size-h-36 m-l1 p-y1 grid-x align-middle align-center"
            href={`https://itunes.apple.com/us/app/${APPLE_STORE_ID}`}
            target="_blank"
            rel="noreferrer"
            onClick={onClickDownLoadAppStore}
          >
            <Image
              src={ConfigImage.downLoadAppStore}
              alt={TEXT.DOWNLOAD_APP_STORE}
              title={TEXT.DOWNLOAD_APP_STORE}
              className="flex-box m-x-auto"
              notWebp
            />
          </a>
        </div>
        <div className="cel small-6">
          <a
            className="link link-down link-down-android dark outline size-h-36 m-l1 p-y1 grid-x align-middle align-center"
            target="_blank"
            rel="noreferrer"
            href={`https://play.google.com/store/apps/details?id=${GOOGLE_STORE_ID}`}
            onClick={onClickDownLoadGooglePlay}
          >
            <Image
              src={ConfigImage.downLoadAppGGPlay}
              alt={TEXT.DOWNLOAD_APP_ANDROID}
              title={TEXT.DOWNLOAD_APP_ANDROID}
              className="flex-box m-x-auto"
              notWebp
            />
          </a>
        </div>
      </div>
    </div>
  );
};

export default DownloadMobile;
