import React, { useEffect, useMemo } from 'react';
import { useVieRouter } from '@customHook';
import { FOOTER, PAGE, POPUP, RIBBON_TYPE, VieON } from '@constants/constants';
import { REPORT_LINK, STATIC_DOMAIN } from '@config/ConfigEnv';
import { UtmParams } from '@models/subModels';
import Image from '@components/basic/Image/Image';
import ConfigImage from '@config/ConfigImage';
import { useDispatch, useSelector } from 'react-redux';
import { openPopup } from '@actions/popup';
import classNames from 'classnames';
import LogoVieSvg from '../basic/Logo/LogoVieSvg';
import DownloadDesktop from './DownloadDesktop';
import DownloadMobile from './DownloadMobile';
import LogoCertification from '../basic/Logo/LogoCertification';
import ConfigCookie from '@/config/ConfigCookie';
import { destinationLogin } from '@/services/multiProfileServices';

const FooterComponent = React.memo((props: any) => {
  const { isLiveSupport, emailVerified, isMobile } = props;
  const router = useVieRouter();
  const pathname = router?.pathname;
  let unmounted = false;
  const dispatch = useDispatch();
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const { isLoginPaymentSuccess } = useSelector((state: any) => state?.Payment);
  const { pageRibbon } = useSelector((state: any) => state?.Page || {});
  const { activeMenu, activeSubMenu } = useSelector((state: any) => state?.Menu || {});
  const { currentProfile } = useSelector((state: any) => state?.MultiProfile || {});
  const dataMenu = activeSubMenu || activeMenu;
  const pageSlug = useMemo(() => {
    let slugTemp = dataMenu?.dataSlug || dataMenu?.seo?.url;
    if (typeof window !== 'undefined') {
      slugTemp = slugTemp || window.location?.pathname;
    }
    return slugTemp;
  }, [dataMenu]);
  const isShowRapVietRanking =
    pageRibbon && pageRibbon[pageSlug]
      ? pageRibbon[pageSlug].some((item: any) => item.type === RIBBON_TYPE.RAP_RANKING_BOARD)
      : false;
  useEffect(() => {
    if (!unmounted) {
      const main = document.getElementById('main');
      const footer = document.getElementById('footer');
      if (main && footer) {
        const footerHeight = footer.clientHeight || 0;
        main.style.minHeight = `calc(100vh - ${footerHeight}px)`;
      }
    }
    return () => {
      unmounted = true;
    };
  }, []);

  const pushToStatic = (e: any, staticUrl: any) => {
    e.preventDefault();
    const queryParams = UtmParams(router.query);

    if (!currentProfile?.id && pathname?.includes(PAGE.PAYMENT_RESULT)) {
      const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};

      destinationLogin({
        dataLogin: {
          profile,
          accessToken
        },
        destination: router?.query?.destinaton || '/',
        router,
        dispatch
      });
    } else {
      router.push({
        pathname: staticUrl,
        query: queryParams
      });
    }
  };

  const onClickDMCA = (e: any) => {
    e.preventDefault();
    window.open(
      '//www.dmca.com/Protection/Status.aspx?ID=598b9d29-b519-4000-b70c-4c1f1dfdc677',
      '_blank'
    );
  };

  const handleClick = (e: any) => {
    if (
      window.location.href.includes(PAGE.PAYMENT) &&
      (profile?.id || isLoginPaymentSuccess) &&
      !isGlobal &&
      !window.location.href.includes('ket-qua')
    ) {
      const targetElement = e.target.closest('a') || e.target.closest('button');
      if (targetElement) {
        e.preventDefault();
        e.stopPropagation();
        const url = targetElement.href || targetElement.getAttribute('data-url');
        dispatch(
          openPopup({
            popupName: POPUP.NAME.SURVEY_PAYMENT,
            params: { intendedUrl: url }
          })
        );
      }
    }
  };
  useEffect(() => {
    const footer = document.getElementById('footer');

    if (footer) {
      const handleClickWrapper = (e: any) => {
        const targetElement = e.target.closest('a') || e.target.closest('button');
        if (targetElement) {
          handleClick(e);
        }
      };

      footer.addEventListener('click', handleClickWrapper);

      return () => {
        footer.removeEventListener('click', handleClickWrapper);
      };
    }
    return () => {
      unmounted = true;
    };
  }, []);
  const inApp = (pathname || '').includes('in-app');
  if (!!emailVerified || inApp) return null;
  return (
    <footer
      className={classNames(
        'footer border-st4-top layer-2',
        isShowRapVietRanking && 'relative bg-black'
      )}
      id="footer"
    >
      {isMobile && <DownloadMobile />}
      <div className="footer-top">
        <div className="container canal-v">
          <div className="grid-x">
            <div className="cell medium-4 large-4">
              <div className="footer-left footer-info flex flex-col gap-3">
                <a
                  onClick={(e) => pushToStatic(e, inApp ? PAGE.ZALOPAY : PAGE.HOME)}
                  className="logo w-[6.125rem]"
                >
                  <LogoVieSvg isImage />
                </a>
                <a
                  className="logo logo--mit"
                  href={FOOTER.MIT.LINK}
                  target="_blank"
                  title="Đã thông báo Bộ Công Thương"
                  rel="noreferrer"
                >
                  <img
                    src={FOOTER.MIT.IMAGE}
                    alt={FOOTER.MIT.TITLE}
                    title={FOOTER.MIT.TITLE}
                    width="..."
                    height="..."
                  />
                </a>

                <a
                  onClick={onClickDMCA}
                  title="DMCA.com Protection Status"
                  className="dmca-badge logo w-[7.5625rem]"
                  href="//www.dmca.com/Protection/Status.aspx?ID=598b9d29-b519-4000-b70c-4c1f1dfdc677"
                  target="_blank"
                  rel="noreferrer"
                >
                  <img
                    className="bg-[#e6e6e6]"
                    src="https://images.dmca.com/Badges/dmca_protected_sml_120m.png?ID=598b9d29-b519-4000-b70c-4c1f1dfdc677"
                    alt="DMCA.com Protection Status"
                    title="DMCA.com Protection Status"
                    width="..."
                    height="..."
                  />
                </a>

                <LogoCertification
                  className="w-[7.5625rem]"
                  href="https://certificate.crossbowlabs.com/b6b03752-549a-4003-9842-ec5034048980#gs.19u98b"
                  title="VALIDATION OF COMPLIANCE - PCI-SAQ"
                  target="_blank"
                  src={ConfigImage.logoPciDss}
                  altImg="VALIDATION OF COMPLIANCE - PCI-SAQ"
                />
              </div>
            </div>
            <div className="cell medium-8 large-8">
              <div className="footer-right medium-align-right flex flex-col md:flex-row gap-8 md:gap-14 md:justify-end">
                <ul className="vertical menu menu--footer">
                  <li className="title">{FOOTER.NAV.INTRO}</li>
                  <li>
                    <a
                      className="hover:!text-vo-green"
                      href={FOOTER.NAV.INTRODUCE.PATH}
                      target="_blank"
                      title={FOOTER.NAV.INTRODUCE.NAME}
                      rel="nofollow noreferrer"
                    >
                      {FOOTER.NAV.INTRODUCE.NAME}
                    </a>
                  </li>
                </ul>
                <ul className="vertical menu menu--footer">
                  <li className="title">{FOOTER.NAV.RULE}</li>
                  <li>
                    <a
                      className="hover:!text-vo-green"
                      href={FOOTER.NAV.TERMS.PATH}
                      title={FOOTER.NAV.TERMS.NAME}
                      onClick={(e) => pushToStatic(e, FOOTER.NAV.TERMS.PATH)}
                    >
                      {FOOTER.NAV.TERMS.NAME}
                    </a>
                  </li>
                  <li>
                    <a
                      className="hover:!text-vo-green"
                      href={FOOTER.NAV.AGREEMENT.PATH}
                      title={FOOTER.NAV.AGREEMENT.NAME}
                      onClick={(e) => pushToStatic(e, FOOTER.NAV.AGREEMENT.PATH)}
                    >
                      {FOOTER.NAV.AGREEMENT.NAME}
                    </a>
                  </li>
                  <li>
                    <a
                      className="hover:!text-vo-green"
                      href={FOOTER.NAV.PRIVATE_POLICY.PATH}
                      title={FOOTER.NAV.PRIVATE_POLICY.NAME}
                      onClick={(e) => pushToStatic(e, FOOTER.NAV.PRIVATE_POLICY.PATH)}
                    >
                      {FOOTER.NAV.PRIVATE_POLICY.NAME}
                    </a>
                  </li>
                </ul>
                <ul className="vertical menu menu--footer">
                  <li className="title">{FOOTER.NAV.INFO}</li>
                  <li>
                    <a
                      className="hover:!text-vo-green"
                      title={FOOTER.NAV.ANNOUNCEMENT.NAME}
                      href={FOOTER.NAV.ANNOUNCEMENT.PATH}
                      target="_blank"
                      rel="noreferrer"
                    >
                      {FOOTER.NAV.ANNOUNCEMENT.NAME}
                    </a>
                  </li>
                  <li>
                    <a
                      className="hover:!text-vo-green"
                      title={FOOTER.NAV.FAQS.NAME}
                      href={FOOTER.NAV.FAQS.PATH}
                      target="_blank"
                      rel="noreferrer"
                    >
                      {FOOTER.NAV.FAQS.NAME}
                    </a>
                  </li>
                  <li>
                    <a
                      className="hover:!text-vo-green"
                      href={`mailto:${VieON.SUPPORT_EMAIL}?Subject=Trợ%20giúp/Góp%20ý%20về%20website%20VieON`}
                      title={FOOTER.NAV.CONTACT}
                      target="_top"
                    >
                      {FOOTER.NAV.CONTACT}
                    </a>
                  </li>
                  <li>
                    <a
                      className="hover:!text-vo-green"
                      rel="noreferrer"
                      href={REPORT_LINK}
                      title={FOOTER.NAV.ERROR}
                      target="_blank"
                    >
                      {FOOTER.NAV.FEEDBACK}
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="footer-copy border-st4-top">
        <div className="container canal-v">
          <div className="grid-x align-middle">
            <div className="cell small-12 medium-shrink small-order-1 medium-order-2">
              {!isMobile && <DownloadDesktop />}
            </div>
            <div className="cell small-12 medium-auto small-order-2 medium-order-1">
              <address>
                {FOOTER.COMPANY_INFO.ADDRESS}
                <br />
                {'Email: '}
                <a
                  className="link"
                  href={`mailto:${VieON.SUPPORT_EMAIL}`}
                  title={`Email hỗ trợ: ${VieON.SUPPORT_EMAIL}`}
                  target="_parent"
                >
                  {FOOTER.COMPANY_INFO.EMAIL}
                </a>
                {' | Hotline: '}
                <a
                  className="link"
                  href="tel:(+84)**********"
                  title="Hãy gọi cho chúng tôi theo số hotline 1800.599.920 (miễn phí)"
                  target="_parent"
                >
                  {FOOTER.COMPANY_INFO.HOTLINE}
                </a>
                {' (miễn phí)'}
                <br />
                {FOOTER.COMPANY_INFO.BUSINESS_LICENSE}
                <br />
                {FOOTER.COMPANY_INFO.BUSINESS_LICENSE_2}
              </address>
            </div>
          </div>
        </div>
      </div>

      {/* liveSupport */}
      {isLiveSupport && (
        <div className="liveSupport">
          <div className="liveSupportImg">
            <a
              href="https://quickom.beowulfchain.com/call/44651581477532603"
              target="_blank"
              rel="noreferrer"
            >
              <Image
                src={`${STATIC_DOMAIN}assets/img/qrcodevieoncenter.png`}
                title="qrcode vieon center"
                width="..."
                height="..."
                alt="VieON IMAGE"
              />
            </a>
          </div>
          <div className="liveSupportTitle">
            <h3>Live Support</h3>
          </div>
        </div>
      )}

      {/* end liveSupport */}
    </footer>
  );
});

export default FooterComponent;
