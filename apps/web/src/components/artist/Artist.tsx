import React, { useRef } from 'react';
import { getListOrderArtist } from '@helpers/settings';
import { TEXT } from '@constants/text';
import { ID } from '@constants/constants';
import ArtistItem from '@components/basic/items/ArtistItem';
import FilterDropDown from '../basic/FilterDropDown';
import Image from '../basic/Image/Image';
import Card from '../basic/Card/Card';
import CardList from '../basic/Card/CardList';
import SeoText from '../seo/SeoText';
const listOrderArtist: any = getListOrderArtist();

const Artist = ({
  infoArtist,
  listArtistRelated,
  listDataAllPage,
  titlePage,
  onScrollDown,
  changeOrder,
  seoData,
  currentSortId
}: any) => {
  const ref = useRef([]);
  const onChangeOrder = (e: any, page: any, item: any) => {
    if (item) changeOrder(item.id);
  };

  const country = infoArtist?.country || {};
  const job = infoArtist?.job;
  const relatedList = listArtistRelated?.items;
  const textTeam = listArtistRelated?.text_team;
  const filterArr = (listOrderArtist || []).find(
    (el: any) => currentSortId && el.id === currentSortId
  );

  const renderCardList = () => (
    <>
      {Object.keys(listDataAllPage).map(
        (key) =>
          (listDataAllPage?.[key]?.items &&
            listDataAllPage[key].items.map((item: any, index: any) => {
              if (!item?.id || item?.id === ID.VIEW_MORE) return null;
              return (
                <Card
                  lazyImg
                  cardData={item}
                  key={item.id}
                  ref={ref}
                  index={index + 1}
                  randomID={`${item?.id}_${index}`}
                />
              );
            })) ||
          null
      )}
    </>
  );

  if (!listDataAllPage) return null;
  return (
    <section className="section section--for-dark section--artist !py-4 md:!py-6 overflow canal-v">
      <div className="section__header horizontal">
        <div className="section__header-left">
          <h3 className="section__title">{titlePage}</h3>
        </div>
        <div className="section__header-right">
          <FilterDropDown
            className="filter filter--sort filter--dark"
            buttonClass="flex items-center justify-center !border !border-solid !border-vo-gray-200 hover:!border-vo-green h-9 px-3 bg-vo-dark-gray-900/50 !text-white hover:!text-vo-green space-x-3 transition-colors"
            iconNameSlotRight="vie-align-left-rc-light"
            id="filterTypeDropdown"
            title={TEXT.ALL_EPISODE}
            filterLabel={TEXT.SHOW}
            filterList={listOrderArtist}
            changeFilter={onChangeOrder}
            filterName={filterArr?.name}
            alignRight
          />
        </div>
      </div>
      <div className="section__body">
        <div className="grid-x">
          <div className="cell medium-shrink" id="artistBoard">
            {infoArtist && (
              <div className="block block--artist block--artist-detail">
                <div className="card card--vod card--artist card--artist-info">
                  <div
                    className={`avatar avatar--box border--green overflow circle${
                      !infoArtist?.images?.avatar ? ' default light' : ''
                    }`}
                  >
                    <div className="avatar__wrap">
                      {infoArtist?.images?.avatar ? (
                        <Image
                          src={infoArtist?.images?.avatar}
                          alt={`Nghệ sĩ ${infoArtist?.name}`}
                        />
                      ) : (
                        <span className="absolute alluvion-right">{infoArtist?.name}</span>
                      )}
                    </div>
                  </div>

                  <div className="card__section">
                    <h1 className="card__title text-white">{seoData?.data?.name || ''}</h1>
                    <SeoText disableH1 seo={seoData?.data?.seo} disableSeoText />
                    <div className="card__info__artist">
                      {job && (
                        <div className="tags-group">
                          <label className="tags-muted">{TEXT.JOB}: </label>
                          <a className="tags">{job}</a>
                        </div>
                      )}
                      {country.name && (
                        <div className="tags-group">
                          <label className="tags-muted">{TEXT.COUNTRY}: </label>
                          <a className="tags">{country.name}</a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
            {relatedList && relatedList.length > 0 && (
              <div className="block block--artist block--artist-related">
                <div className="block-header">
                  <h3 className="block__title">{textTeam || TEXT.SAME_ARTIST}</h3>
                </div>
                <div className="block-body">
                  <div className="card-group">
                    {relatedList.map((artist: any) => {
                      const name = artist?.name;
                      const link = artist?.seo?.url;
                      const avatar = artist?.images?.avatar;
                      return <ArtistItem key={artist.id} name={name} link={link} avatar={avatar} />;
                    })}
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="cell medium-auto">
            <CardList
              numberItem={5}
              renderContent={renderCardList}
              heightCheckScroll={320}
              onScrollDown={onScrollDown}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Artist;
