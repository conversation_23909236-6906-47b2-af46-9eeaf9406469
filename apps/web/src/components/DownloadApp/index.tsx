import React from 'react';
import get from 'lodash/get';
import { useSelector } from 'react-redux';
import Image from '@components/basic/Image/Image';
import { LINK_QRCODE_DOWNLOAD_APP, LINK_VIEON_DOWNLOAD_APP } from '@constants/constants';
import { TEXT } from '@constants/text';
import { APPLE_STORE_ID, GOOGLE_STORE_ID } from '@config/ConfigEnv';
import ConfigImage from '@config/ConfigImage';
import CopyText from '@components/CopyText';

const DownloadApp = ({ onHandleClickButton }: any) => {
  const webConfig = useSelector((state: any) => state?.App?.webConfig || {});
  const appDownload = get(webConfig, 'appDownload', {});
  const linkQrCode = get(appDownload, 'qrCode', LINK_QRCODE_DOWNLOAD_APP);

  return (
    <div className="block__wrap">
      <div className="block__body margin-cel-nl-bottom-16">
        <div className="text text-white text-medium-up-18 text-center text-medium">
          {TEXT.DOWNLOAD_APP}
          <div className="text text-medium-up-14 text-gray119 p-t2 p-b3">
            {TEXT.SCAN_QRCODE_APP}
          </div>
        </div>
        <div className="thumb thumb-center">
          <Image src={linkQrCode} notWebp />
        </div>
        <div className="text text-white text-medium-up-14 text-center p-t2 p-b3">
          <div className="m-b2 text-gray119">{TEXT.OR}</div>
          {TEXT.COPY_LINK_SEND_MOBILE}
        </div>
        <CopyText
          value={LINK_VIEON_DOWNLOAD_APP}
          title={TEXT.COPY_LINK_SUCCESS}
          onHandleClickButton={onHandleClickButton}
        />
        <div className="grid-x grid-margin-x4 align-middle align-center">
          <div className="cell small-6">
            <a
              className="link link-down link-down-ios dark outline size-h-36 grid-x align-middle align-center"
              href={`https://itunes.apple.com/us/app/${APPLE_STORE_ID}`}
              target="_blank"
              rel="noreferrer"
            >
              <Image
                src={ConfigImage.downLoadAppStore}
                alt={TEXT.DOWNLOAD_APP_STORE}
                title={TEXT.DOWNLOAD_APP_STORE}
                notWebp
              />
            </a>
          </div>
          <div className="cell small-6">
            <a
              className="link link-down link-down-android dark outline size-h-36 grid-x align-middle align-center"
              target="_blank"
              rel="noreferrer"
              href={`https://play.google.com/store/apps/details?id=${GOOGLE_STORE_ID}`}
            >
              <Image
                src={ConfigImage.downLoadAppGGPlay}
                alt={TEXT.DOWNLOAD_APP_ANDROID}
                title={TEXT.DOWNLOAD_APP_ANDROID}
                notWebp
              />
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DownloadApp;
