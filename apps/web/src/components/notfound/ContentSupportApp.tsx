import React from 'react';
import ConfigImage from '@config/ConfigImage';
import { PAGE } from '@constants/constants';
import { TEXT } from '@constants/text';
import { useSelector } from 'react-redux';
import { GOOGLE_STORE_ID, APPLE_STORE_ID } from '@config/ConfigEnv';
import Image from '@components/basic/Image/Image';
import { useVieRouter } from '@customHook';

const ContentSupportApp = () => {
  const { isMobile } = useSelector((state: any) => state?.App);
  const router = useVieRouter();

  const textForDevice = isMobile
    ? 'vui lòng tải ứng dụng VieON tại'
    : 'Vui lòng thử lại với Ứng dụng VieON trên điện thoại hoặc khám phá các nội dung khác';

  const buttonStyle =
    'flex h-12 items-center justify-center border border-solid !border-[#4d4d4d]/[.8] rounded-[3px] text-white bg-black';
  const buttonImgStyle = 'max-w-full max-h-full';

  const onClick = () => {
    router.push(PAGE.HOME);
  };

  const ButtonDownloadAppGroup = () => (
    <div className="grid grid-cols-2 gap-2 w-full">
      <a
        className={buttonStyle}
        href={`https://itunes.apple.com/us/app/${APPLE_STORE_ID}`}
        target="_blank"
        rel="noreferrer"
      >
        <Image
          className={buttonImgStyle}
          src={ConfigImage.downLoadAppStore}
          alt={TEXT.DOWNLOAD_APP_STORE}
          title={TEXT.DOWNLOAD_APP_STORE}
          notWebp
        />
      </a>
      <a
        className={buttonStyle}
        target="_blank"
        rel="noreferrer"
        href={`https://play.google.com/store/apps/details?id=${GOOGLE_STORE_ID}`}
      >
        <Image
          src={ConfigImage.downLoadAppGGPlay}
          alt={TEXT.DOWNLOAD_APP_ANDROID}
          title={TEXT.DOWNLOAD_APP_ANDROID}
          notWebp
        />
      </a>
    </div>
  );

  return (
    <div className="flex py-12 mx-auto justify-center items-center">
      <div className="flex flex-col gap-8 justify-center items-center w-full md:max-w-[367px]">
        <img
          className="inline-block max-w-[180px] md:max-w-[232px]"
          src={ConfigImage.defaultContentApp}
          alt="Empty content"
        />
        <div className="flex flex-col w-full gap-4 items-center">
          <div className="flex flex-col text-white text-center">
            <p>{TEXT.CONTENT_ONLY_SUPPORT_IN_APP}</p>
            <p>{textForDevice}</p>
          </div>
          {isMobile ? (
            <ButtonDownloadAppGroup />
          ) : (
            <a
              className="flex w-fit h-12 rounded-sm button secondary px-8"
              aria-label={TEXT.BACK_HOME}
              onClick={onClick}
            >
              {TEXT.BACK_HOME}
            </a>
          )}
        </div>
      </div>
    </div>
  );
};
export default ContentSupportApp;
