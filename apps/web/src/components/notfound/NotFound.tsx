import React, { useEffect } from 'react';
import { useVieRouter } from '@customHook';
import ConfigImage from '@config/ConfigImage';
import { PAGE } from '@constants/constants';

const NotFound = () => {
  const router = useVieRouter();
  useEffect(() => {
    if (router.asPath.indexOf('vie-icon-search-2019') > -1 || router.asPath.indexOf('apps') > -1) {
      router.push(PAGE.HOME + (window?.location?.search || ''));
    }
  }, []);

  const onClick = () => {
    router.push(PAGE.HOME + (window?.location?.search || ''));
  };

  return (
    <section className="section">
      <div className="empty empty--error">
        <div className="content text-center">
          <img className="inline-block" src={ConfigImage.emptyState} alt="Empty content" />
          <p className="text p-y3">
            <PERSON><PERSON>ô<PERSON> tìm thấy trang
            <br />
            <PERSON><PERSON><PERSON> sử dụng chức năng tìm kiếm phía trên hoặc trở lại trang chủ
          </p>
          <a className="button secondary" aria-label="Trang chủ" onClick={onClick}>
            Trang chủ
          </a>
        </div>
      </div>
    </section>
  );
};
export default NotFound;
