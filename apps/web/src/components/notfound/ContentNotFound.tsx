import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import ListContents from '@components/Ribbon/ListContents';
import { getRibbonDetailNotFound } from '@actions/detail';
import ContentApi from '@apis/cm/ContentApi';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';
import ContentSupportApp from '@components/notfound/ContentSupportApp';

const ContentNotFound = ({ dataRibbon, pageProps, isLiveTv, isVerticalPlayer }: any) => {
  const dispatch = useDispatch();
  const [loadedData, setLoadedData] = useState(false);
  const data = isEmpty(dataRibbon) ? pageProps?.dataDefault : dataRibbon || [];
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);

  const text = isLiveTv
    ? '<PERSON><PERSON> phải bạn đang tìm kiếm các kênh truyền hình sau?'
    : '<PERSON><PERSON> phải bạn đang tìm kiếm các nội dung sau?';

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = () => {
    if (isEmpty(dataRibbon?.[0]?.items)) {
      dispatch(getRibbonDetailNotFound({ isGlobal }));
    }
    setLoadedData(true);
  };

  if (!loadedData) return null;

  return (
    <section className="section section--error overflow canal-v">
      <div className="section__body">
        {isVerticalPlayer ? (
          <ContentSupportApp />
        ) : (
          <div className="empty empty--error">
            <div className="content text-center">
              <img
                className="inline-block"
                src={ConfigImage.defaultNotFoundPage}
                alt={TEXT.PAGE_NOT_FOUND}
              />
              <p className="text text-white p-t3">{TEXT.PAGE_NOT_FOUND}</p>
            </div>
          </div>
        )}

        {!isEmpty(data?.[0]?.items) && (
          <div className="rocopa rocopa--slider m-0 p-0">
            <div className="rocopa__header">
              <h2 className="rocopa__title">{text}</h2>
            </div>
            <div className="rocopa__body">
              <ListContents data={data?.[0]} />
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

ContentNotFound.getInitialProps = async (ctx: any) => {
  const { req, store } = ctx || {};
  if (req) {
    const { App } = store.getState();
    const isGlobal = App?.geoCheck?.isGlobal;
    const res = await ContentApi.getRibbonDetailNotFound({ ssr: true, isGlobal });
    const backupDataRibbons = { 0: res?.data || [] };
    return { dataDefault: backupDataRibbons };
  }
  return { dataDefault: {} };
};

export default ContentNotFound;
