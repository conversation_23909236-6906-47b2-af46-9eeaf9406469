import React from 'react';
import IconFacebook from '@components/Icons/IconFacebook';
import { TEXT } from '@constants/text';
import Style from './SocialPlugin.module.scss';

declare const window: any;

const FacebookAuth = ({ onLogin }: any) => {
  const onLoginFacebook = (e: any) => {
    e.preventDefault();
    if (!window || !window.FB || typeof window.FB.login !== 'function') return;
    try {
      window.FB.login(
        (resp: any) => {
          if (resp?.authResponse?.accessToken) {
            onLogin({ token: resp.authResponse.accessToken, provider: 'facebook' });
          }
          // Handle case where user cancelled or closed popup
          if (resp.status === 'unknown') {
            console.log('Facebook login was cancelled or failed');
          }
        },
        { scope: 'email' }
      );
    } catch (error) {
      console.log('Facebook login error:', error);
    }
  };

  return (
    <button className={Style.buttonSocial} onClick={onLoginFacebook}>
      <span className={Style.iconButton}>
        <IconFacebook />
      </span>
      <span className={Style.textButton}>{TEXT.LOGIN_BY_FACEBOOK}</span>
    </button>
  );
};

export default FacebookAuth;
