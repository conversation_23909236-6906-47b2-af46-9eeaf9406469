import React, { useEffect, useState, useRef } from 'react';
import { GG_CLIENT_ID } from '@config/ConfigEnv';
import { loadScript } from '@helpers/common';
import { GG_LIBRARY } from '@constants/constants';
import IconGoogle from '@components/Icons/IconGoogle';
import { TEXT } from '@constants/text';
import Style from './SocialPlugin.module.scss';

declare const window: any;

const GoogleAuth = ({ onLogin, deviceId }: any) => {
  const btnSignInGG = useRef<any>(null);
  const btnContainer = useRef<any>(null);
  const [isLoadDone, setIsLoadDone] = useState(false);

  useEffect(() => {
    if (deviceId) {
      loadScript(GG_LIBRARY)
        .then(() => {
          window.google.accounts.id.initialize({
            client_id: GG_CLIENT_ID,
            callback: handleCredentialResponse
          });
          setIsLoadDone(true);
        })
        .catch(console.error);
    }
    return () => {
      const scriptTag = document.querySelector(`script[src="${GG_LIBRARY}"]`);
      if (scriptTag) document.body.removeChild(scriptTag);
      setIsLoadDone(false);
    };
  }, [deviceId]);

  useEffect(() => {
    if (typeof window.google !== 'undefined' && isLoadDone) {
      window.google.accounts?.id?.renderButton(btnSignInGG.current, {
        text: 'signin_with',
        locale: 'vi_VN',
        width: btnContainer.current ? btnContainer.current.clientWidth : undefined,
        theme: 'filled_black',
        size: 'large'
      });
    }
  }, [isLoadDone]);

  const handleCredentialResponse = (response: any) => {
    onLoginGoogle(response.credential);
  };

  const onLoginGoogle = (ggToken: any) => {
    if (ggToken && typeof onLogin === 'function') {
      onLogin({
        token: ggToken,
        provider: 'google'
      });
    }
  };

  return (
    <div className={Style.buttonSocialGG}>
      <div className={Style.buttonGG} ref={btnSignInGG} />
      <button className={Style.buttonSocial} ref={btnContainer}>
        <span className={Style.iconButton}>
          <IconGoogle />
        </span>
        <span className={Style.textButton}>{TEXT.LOGIN_BY_GOOGLE}</span>
      </button>
    </div>
  );
};

export default GoogleAuth;
