import React, { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import get from 'lodash/get';
import { globalLoginSocial } from '@actions/globalAuth';
import { ENABLE_SDK_FB, ENABLE_SDK_GG } from '@config/ConfigEnv';
import Style from './SocialPlugin.module.scss';
import FacebookAuth from './FacebookAuth';
import GoogleAuth from './GoogleAuth';

const SocialPlugin = () => {
  const vieRouter = useVieRouter();
  const dispatch = useDispatch();
  const destination = useMemo(() => get(vieRouter, 'query.destination', ''), [vieRouter?.query]);
  const { social, deviceModel, deviceName, deviceType, deviceId, bindAccount } = useSelector(
    (state: any) => state?.App || {}
  );

  const isTriggerAuthQuery = useMemo(
    () => get(vieRouter, 'query.isTriggerAuth', false),
    [vieRouter?.query]
  );

  const onLoginSocial = ({ provider, token }: any) => {
    if (token && provider) {
      dispatch(
        globalLoginSocial({
          provider,
          token,
          model: deviceModel,
          deviceName,
          deviceType,
          deviceId,
          destination,
          isTriggerAuth: isTriggerAuthQuery,
          router: vieRouter,
          bindAccount: bindAccount?.allowOpenPopup
        })
      );
    }
  };

  if (!social?.allowLoginFB && !social?.allowLoginGG) {
    return null;
  }

  return (
    <div className={Style.socialPlugin}>
      {social?.allowLoginFB && (ENABLE_SDK_FB === true || ENABLE_SDK_FB === 'true') && (
        <FacebookAuth onLogin={onLoginSocial} />
      )}
      {social?.allowLoginGG && (ENABLE_SDK_GG === true || ENABLE_SDK_GG === 'true') && (
        <GoogleAuth onLogin={onLoginSocial} deviceId={deviceId} />
      )}
    </div>
  );
};

export default SocialPlugin;
