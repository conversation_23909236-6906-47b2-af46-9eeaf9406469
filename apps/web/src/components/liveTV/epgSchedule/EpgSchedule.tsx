import React, { useEffect, useMemo, useRef, useState, useLayoutEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import { isMobile } from 'react-device-detect';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { setNotifyComingSoonEpg, getListAllChannels, getListEpgs } from '@actions/liveTV';
import { getPopupTriggerDialog, openPopup } from '@actions/popup';
import { setToast } from '@actions/app';
import {
  CONTENT_TYPE,
  CONTENT_TYPE_NOTIFY,
  EL_ID,
  PAGE,
  PERMISSION,
  POPUP,
  USER_TYPE
} from '@constants/constants';
import { MOE_NAME, MOE_PROPERTY } from '@config/ConfigMoEnage';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';
import { moEngageEvent } from '@tracking/TrackingMoEngage';
import { createTimeout, encodeParamDestination, onOpenPayment } from '@helpers/common';
import { parsePopupParams } from '@services/popupServices';
import EpgScheduleMobile from './EpgScheduleMobile';
import EpgItem from './EpgItem';
import EpgFilterChannel from './EpgFilterChannel';

const EpgSchedule = ({ isFullscreen, videoContainerHeight, onCloseTabsInFullscreen }: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { listEpgsOfChannel, listAllChannels, detailChannel, activeEpg } = useSelector(
    (state: any) => state.LiveTV || {}
  );
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const timeOutScrollActiveItem = useRef<any>(null);
  const currentRef = useRef<any>(null);
  const epgHeaderRef = useRef<any>(null);
  const heightEpgHeader = epgHeaderRef.current?.clientHeight || 0;
  const videoHeight = useMemo(() => {
    if (videoContainerHeight) return videoContainerHeight;
    if (typeof window !== 'undefined') {
      return document.getElementById(EL_ID.VIE_PLAYER)?.clientHeight || 0;
    }
  }, [videoContainerHeight]);
  const heightListEpgs: any = useMemo(() => {
    if (!isMobile) return videoHeight - heightEpgHeader;
    if (typeof window !== 'undefined') {
      return window.innerHeight - videoHeight - heightEpgHeader - 40 - 50;
    } // 50 là height của header, 40 là height của header tab livetv
    return 0;
  }, [isMobile, videoHeight, heightEpgHeader]);
  const [activeFilterDate, setActiveFilterDate] = useState<any>(null);
  const [computedHeight, setComputedHeight] = useState('auto');

  useLayoutEffect(() => {
    if (!isFullscreen) {
      const newHeight: any =
        Number.isNaN(heightListEpgs) || heightListEpgs === 0 ? 'auto' : heightListEpgs;
      setComputedHeight(newHeight);
    } else {
      setComputedHeight('');
    }
  }, [heightListEpgs, isFullscreen]);

  const listEpgsToShow = useMemo(
    () =>
      get(
        listEpgsOfChannel,
        `listFilterDate[${!isEmpty(activeFilterDate) ? activeFilterDate?.id : 3}].listEpgs`,
        []
      ),
    [listEpgsOfChannel, activeFilterDate]
  );

  useEffect(
    () => () => {
      clearTimeout(timeOutScrollActiveItem.current);
    },
    []
  );

  useEffect(() => {
    if (detailChannel?.id) {
      setActiveFilterDate(null);
    }
  }, [detailChannel?.id]);

  useEffect(() => {
    const toDay = get(listEpgsOfChannel, 'listFilterDate[3]', {});
    if (!activeFilterDate && !isEmpty(toDay)) {
      setActiveFilterDate(toDay);
    }
  }, [listEpgsOfChannel, activeFilterDate]);

  useEffect(() => {
    if (
      !isEmpty(listEpgsOfChannel) &&
      !isEmpty(activeFilterDate) &&
      detailChannel?.id &&
      detailChannel?.id === listEpgsOfChannel?.idChannel
    ) {
      const isFetchedListEpgs = get(
        listEpgsOfChannel,
        `listFilterDate[${activeFilterDate?.id}].isFetched`,
        false
      );
      if (!isFetchedListEpgs) {
        dispatch(getListEpgs({ id: detailChannel?.id, strDate: activeFilterDate?.strDate }));
      }
    }
  }, [activeFilterDate, detailChannel?.id, listEpgsOfChannel]);

  const onGetListAllChannels = () => {
    if (isEmpty(listAllChannels)) {
      dispatch(getListAllChannels({}));
    }
  };

  const handleSelectChannel = (channel: any) => {
    if (!channel) return;
    if (router && channel?.seo?.url) {
      router.push(PAGE.LIVE_TV_SLUG, channel?.seo?.url);
      if (isFullscreen && typeof onCloseTabsInFullscreen === 'function') onCloseTabsInFullscreen();
    }
  };

  const handleSelectFilterDate = (data: any) => {
    if (!isEmpty(data)) {
      setActiveFilterDate(data);
    }
  };

  const checkPermissionEpgOfChannel = (epg: any) => {
    const { permission, drmServiceName, packages, contentConcurrentGroup } = detailChannel;
    const groupPackageId = packages?.[0]?.id || 0;
    let { popupName, action, goToBuyPackage, authTrigger }: any = parsePopupParams({
      permission,
      drmServiceName,
      contentType: CONTENT_TYPE.EPG,
      isSubscribeComingSoon: epg?.isComingSoon,
      profile,
      groupPackageId,
      forceLogin: detailChannel?.forceLogin,
      contentDetail: detailChannel
    });

    if (popupName) {
      action = {
        func: () =>
          onOpenPayment(router, {
            returnUrl: window?.location?.href,
            pkg: groupPackageId,
            newTriggerPaymentBuyPackage: {
              isGlobal,
              profileId: profile?.id
            }
          })
      };
      if (goToBuyPackage) {
        if (typeof action?.func === 'function') action.func();
      } else {
        //handle trigger with contentType 5 for trigger svod dialog
        if (!isGlobal && profile?.type !== USER_TYPE.VIP && detailChannel?.isPremium) {
          dispatch(
            getPopupTriggerDialog({
              type: 'svod',
              contentId: detailChannel?.id,
              contentType: CONTENT_TYPE.LIVE_TV
            })
          );
          dispatch(
            openPopup({
              name: POPUP.NAME.SVOD_TRIGGER,
              packageName: isKid ? packages?.[0]?.name : '',
              action,
              contentType: CONTENT_TYPE.EPG,
              contentConcurrentGroup,
              drmServiceName,
              data: detailChannel
            })
          );
        } else {
          dispatch(
            openPopup({
              name: popupName,
              packageName: isKid ? packages?.[0]?.name : '',
              action,
              contentType: CONTENT_TYPE.EPG,
              contentConcurrentGroup,
              drmServiceName,
              data: detailChannel
            })
          );
        }
      }
      return;
    }
    if (authTrigger) {
      localStorage.setItem('currentAuthFlow', 'registration_for_livetv_coming_soon');
      const remakeDestination = encodeParamDestination(router?.asPath);
      router.push(
        `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${authTrigger}`
      );
      return;
    }
    if (epg?.isComingSoon) {
      dispatch(
        setNotifyComingSoonEpg(
          epg?.id,
          epg?.isNotifyComingSoon,
          epg?.start,
          CONTENT_TYPE_NOTIFY.LIVE_TV
        )
      );
      moEngageEvent(MOE_NAME.COMING_SOON_CONTENT, {
        [MOE_PROPERTY.CONTENT_ID]: epg?.id,
        [MOE_PROPERTY.CONTENT_TYPE]: CONTENT_TYPE.EPG,
        [MOE_PROPERTY.IS_SUB]: !epg?.isNotifyComingSoon
      });
      return;
    }
    if (!epg?.isCatchUp) {
      dispatch(setToast({ message: TEXT.EPG_OLD }));
      return;
    }
    if ((epg?.isLive || epg?.isCatchUp) && permission === PERMISSION.CAN_WATCH) {
      router.push(PAGE.LIVE_TV_EPG, epg?.seo?.url);
    }
  };

  const handleClickEpgItem = (epg: any) => {
    if (activeEpg?.id === epg?.id) return;
    if (epg?.isCatchUp) {
      if (isFullscreen && typeof onCloseTabsInFullscreen === 'function') onCloseTabsInFullscreen();
    }
    checkPermissionEpgOfChannel(epg);
  };

  const onScrollToActive = (activeItem: any) => {
    if (!activeItem) return;
    const containerRef = currentRef?.current;
    const containerHeight = heightListEpgs / 2;
    clearTimeout(timeOutScrollActiveItem.current);
    timeOutScrollActiveItem.current = createTimeout(() => {
      if (containerRef) {
        if (containerRef.scrollTo) {
          containerRef.scrollTo({
            left: 0,
            top: activeItem?.offsetTop - containerHeight,
            behavior: 'smooth',
            block: 'center',
            inline: 'center'
          });
        }
      } else {
        activeItem.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'center'
        });
      }
    }, 1000);
  };

  if (isMobile) {
    return (
      <EpgScheduleMobile
        listEpgs={listEpgsToShow}
        listFilterDate={get(listEpgsOfChannel, 'listFilterDate', [])}
        activeFilterDate={activeFilterDate}
        handleSelectFilterDate={handleSelectFilterDate}
        ref={currentRef}
        epgHeaderRef={epgHeaderRef}
        heightContainerEPG={heightListEpgs}
        isFullscreen={isFullscreen}
        activeEpg={activeEpg}
        handleClickEpgItem={handleClickEpgItem}
        onScrollToActive={onScrollToActive}
      />
    );
  }

  return (
    <div className="epg epg--schedule">
      <div className="epg-inner">
        <div className="epg-header" ref={epgHeaderRef}>
          {!isFullscreen && <div className="epg-title"> Lịch phát sóng </div>}
          <EpgFilterChannel
            heightContainerEPG={heightListEpgs}
            listAllChannels={listAllChannels}
            activeChannel={detailChannel}
            onGetListAllChannels={onGetListAllChannels}
            isFullscreen={isFullscreen}
            handleSelectChannel={handleSelectChannel}
            activeFilterDate={activeFilterDate}
            listFilterDate={get(listEpgsOfChannel, 'listFilterDate', [])}
            handleSelectFilterDate={handleSelectFilterDate}
          />
        </div>
        <div
          className="epg-container scrollable-y over-scroll-contain"
          ref={currentRef}
          style={{ height: computedHeight }}
        >
          <div className="card-group">
            {listEpgsToShow.map((item: any) => (
              <EpgItem
                key={item.id}
                data={item}
                isFullscreen={isFullscreen}
                activeEpg={activeEpg}
                handleClickEpgItem={handleClickEpgItem}
                onScrollToActive={onScrollToActive}
              />
            ))}
          </div>
          {isEmpty(listEpgsToShow) && (
            <div className="empty" style={{ height: heightListEpgs || '100%' }}>
              <div className="content">
                <img src={ConfigImage.commentFirst} alt="Lịch phát sóng chưa cập nhật" />
                <p className="text">Lịch phát sóng chưa cập nhật</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EpgSchedule;
