import QnetApi from '@apis/ccu/qnetApi';
import { createTimeout } from '@helpers/common';
import { HTTP_CODE } from '@constants/constants';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import LocalStorage from '@config/LocalStorage';

class CCUQnet {
  closePlayer: any;
  error: any;
  loadSource: any;
  operatorId: any;
  pingEnd: any;
  pingTimer: any;
  sessionId: any;
  startPingTimer: any;
  token: any;
  userId: any;
  constructor() {
    this.operatorId = 7;
    this.sessionId = '';
    this.token = '';
    this.userId = '';
    this.pingEnd = false;
    this.pingTimer = null;
    this.error = false;
  }

  onStart = ({ userId, sessionId, loadSource, closePlayer, operatorId }: any) => {
    this.closePlayer = closePlayer;
    this.loadSource = loadSource;
    this.userId = userId;
    this.sessionId = sessionId;
    this.error = false;
    this.pingEnd = false;
    this.operatorId = operatorId || 7;

    const oldQNetToken = ConfigLocalStorage.get(LocalStorage.QN_TOKEN);
    if (oldQNetToken) {
      QnetApi.end({ token: oldQNetToken });
    }

    if (this.token) {
      this.onEnd();
      if (this.loadSource) {
        this.loadSource();
      }
      if (this.startPingTimer) clearTimeout(this.startPingTimer);
      this.startPingTimer = createTimeout(() => {
        this.ping();
      }, PING_TIMER);
    } else {
      if (this.startPingTimer) clearTimeout(this.startPingTimer);
      this.onPing();
    }
  };

  onEnd = (httpCode?: any) => {
    if (this.closePlayer) this.closePlayer(httpCode);
    if (!this.token) return;
    this.pingEnd = true;
    QnetApi.end({ token: this.token });
    this.token = '';
  };

  onPing = async () => {
    if (this.pingEnd || this.error) return;
    const params: any = {
      operatorId: this.operatorId,
      session: this.sessionId
    };
    const pingData = await QnetApi.ping(params);
    this.handleRespond(pingData);
  };

  ping = async () => {
    const params: any = {
      // operatorId: this.operatorId,
      token: this.token
    };
    if (!this.token) return;
    const pingData = await QnetApi.ping(params);
    this.handleRespond(pingData);
  };

  onRefresh = async () => {
    const params: any = { operatorId: this.operatorId, session: this.sessionId };
    const refreshData = await QnetApi.refresh(params);
    const httpCode = refreshData?.httpCode || 200;
    const newToken = refreshData?.data?.token;
    if (httpCode === HTTP_CODE.OK_200) {
      ConfigLocalStorage.set(LocalStorage.QN_TOKEN, newToken);
      this.token = newToken;
      createTimeout(() => {
        this.ping();
      }, PING_TIMER);
    } else {
      // Closed Player
      this.onEnd();
    }
  };

  handleRespond = (response: any) => {
    const httpCode = response?.httpCode || 200;
    const newToken = response?.data?.token;
    switch (httpCode) {
      case HTTP_CODE.OK_200:
        if (this.pingTimer) clearTimeout(this.pingTimer);
        this.pingTimer = createTimeout(() => {
          if (!this.error) this.ping();
        }, PING_TIMER);
        break;
      case HTTP_CODE.OK_202:
        ConfigLocalStorage.set(LocalStorage.QN_TOKEN, newToken);
        this.token = newToken;
        if (this.loadSource) {
          this.loadSource();
        }
        createTimeout(() => {
          this.ping();
        }, PING_TIMER);

        break;
      case HTTP_CODE.EXPIRE:
        this.onRefresh();
        break;
      case HTTP_CODE.UNAUTHORIZED: {
        // Closed Player
        this.onEnd();
        break;
      }
      case HTTP_CODE.MAX_CCU:
        // Closed Player
        this.onEnd(HTTP_CODE.MAX_CCU);
        break;
      default:
        if (this.closePlayer) this.closePlayer(httpCode);
        break;
    }
  };

  onError = () => {
    this.error = true;
    if (this.pingTimer) {
      clearTimeout(this.pingTimer);
      // this.onEnd();
    }
  };
}

const PING_TIMER = 60000;

export default CCUQnet;
