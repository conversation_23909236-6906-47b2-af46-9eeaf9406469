import React, { useEffect, useMemo, useRef, useState } from 'react';
import dynamic from 'next/dynamic';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import isEmpty from 'lodash/isEmpty';
import { openPopup } from '@actions/popup';
import {
  resetValidateKPlus,
  getDetailChannelById,
  validateKPlus as validateKPlusAction
} from '@actions/liveTV';
import { setStatusFullscreen } from '@actions/player';
import LiveTVApi from '@apis/liveTVApi';
import { CONTENT_TYPE, EL_ID, PERMISSION, POPUP } from '@constants/constants';
import { DRM, ERROR_PLAYER } from '@constants/player';
import ConfigUser from '@config/ConfigUser';
import ConfigSocket from '@config/ConfigSocket';
import { VALUE } from '@config/ConfigSegment';
import { checkIsFullscreen, createTimeout, getRandomInt, getRandomText } from '@helpers/common';
import TrackingPlayer from '@tracking/functions/TrackingPlayer';
import { trackingLiveProgress } from '@tracking/video';
import UserServices from '@services/userServices';
import { calculatePlayingTime, getInfoVideoCodec } from '@services/playerServices';
import { handlePreventContent } from '@services/contentService';
import TrackingLog from '@services/trackingLog';
import { RECONNECT_SOCKET } from '@config/ConfigEnv';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import LocalStorage from '@config/LocalStorage';
import { ACTION_TYPE, createAction } from '@actions/actionType';
import SocketCluster from './socket/socketCluster';

const Player = dynamic(import('../basic/Player/Player'), { ssr: true });

const tracking = new TrackingLog();
let socketConnectedStatus = false;

let sessionId = 0;
let completedId = '';
let startId = '';
const LiveTVPlayer = ({ checkChannelPermission, handleEndSessionPlay, setToast }: any) => {
  const playingId = useRef<any>(null);
  const initId = useRef<any>(null);
  const initPlayerTime = useRef<any>(null);
  const videoRef = useRef<any>(null);
  const playerRef = useRef<any>(null);
  const playerNameRef = useRef<any>(null);
  const setCancelPlayerRef = useRef<any>(null);
  const liveTVCCURef = useRef<any>(null);
  const startTimeRef = useRef<any>(null);
  const offsetTimeRef = useRef<any>(null);
  const totalPlayedDataRef = useRef<any>(null);
  const playerUsiIdRef = useRef<any>(null);
  const fingeringTimerRef = useRef<any>(null);
  const connectSocketCountRef = useRef<any>(0);
  const sessionTokenEl = useRef('');
  const router = useVieRouter();
  const dispatch = useDispatch();
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const { preventContent } = useSelector((state: any) => state.App?.featureFlag || {});
  const { activeEpg, detailChannel, validateKPlus } = useSelector(
    (state: any) => state?.LiveTV || {}
  );
  const { concurrentScreen, dataRefreshSession } = useSelector((state: any) => state?.Detail || {});
  const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
  const { blockPlayer } = useSelector((state: any) => state?.Player || {});
  const popupName = useSelector((state: any) => state?.Popup?.popupName || {});
  const isGlobal = useSelector((state: any) => state?.geoCheck?.isGlobal);

  const validateKPlusTimer = useRef<any>(null);
  const {
    deviceId,
    isMobileOnly,
    isIOS,
    isSafari,
    isAndroid,
    isWindows,
    isMobile,
    isTablet,
    deviceName,
    isMacOs
  } = useSelector((state: any) => state?.App || {});
  const {
    id,
    assetId,
    permission,
    imgThumb,
    playerLogo,
    drmServiceName,
    linkPlay,
    ads,
    qnetDrm,
    qnetInfo,
    usi,
    warningScreen,
    drmProvider,
    linkPlaysToRetry,
    drmMerchant,
    isDrm,
    isSeekAllow
  } = detailChannel || {};
  const { title, isComingSoon, hlsLinkPlay, isLive } = activeEpg || {};
  const { epg } = router?.query || {};
  const contentId = useMemo(() => (epg ? activeEpg?.id || id : id), [activeEpg?.id, id, epg]);
  const displayControl = useMemo(
    () =>
      permission === PERMISSION.GIFT_CODE ||
      permission === PERMISSION.PAYMENT ||
      permission !== PERMISSION.CAN_WATCH,
    [permission]
  );
  const isPaused = useMemo(
    () =>
      permission === PERMISSION.GIFT_CODE ||
      permission === PERMISSION.PAYMENT ||
      permission !== PERMISSION.CAN_WATCH,
    [permission]
  );

  const linkPlayToUse = useMemo(
    () => (epg && !isComingSoon ? activeEpg?.linkPlay : linkPlay),
    [epg, isComingSoon, hlsLinkPlay, linkPlay]
  );

  const [cancelPlayerData, setCancelPlayerData] = useState<any>(null);
  const [socketConnected, setSocketConnected] = useState(false);
  const [isPrevent, setIsPrevent] = useState(false);
  const [preventNote, setPreventNote] = useState<any>('');

  useEffect(
    () => () => {
      if (detailChannel?.id && playingId.current !== detailChannel?.id) {
        TrackingPlayer.exitBeforeStarted({
          contentId: detailChannel.id,
          contentTitle: detailChannel.title,
          contentType: CONTENT_TYPE.LIVE_TV
        });
      }
      handleRecord({ action: ConfigUser.RECORD.ACTION.STOP });
      handleUnSubscribeSocket();
      if (validateKPlusTimer?.current) clearTimeout(validateKPlusTimer?.current);
      dispatch(resetValidateKPlus());
    },
    []
  );
  useEffect(() => {
    if (sessionToken) {
      sessionTokenEl.current = sessionToken;
    }
  }, [sessionToken]);

  useEffect(() => {
    if (popupName && popupName === POPUP.NAME.PLAYER_ERROR_LIVETV) {
      playingId.current = detailChannel?.id;
    }
  }, [popupName]);

  useEffect(() => {
    if (!isEmpty(detailChannel)) {
      if (detailChannel?.isConcurrentScreenLimit) return;
      if (detailChannel?.success) {
        if (!detailChannel?.linkPlay && detailChannel.permission === PERMISSION.CAN_WATCH) {
          handleOpenCatError({
            errorData: {
              errorType: ERROR_PLAYER.TYPE.EMPTY_LINK
            }
          });
        }
      } else {
        handleOpenCatError({
          errorData: {
            errorType: ERROR_PLAYER.TYPE.API_DETAIL,
            detailData: detailChannel
          }
        });
      }
    }
  }, [detailChannel]);

  useEffect(() => {
    window.addEventListener('beforeunload', onBrowserChange);
    return () => {
      window.removeEventListener('beforeunload', onBrowserChange);
    };
  }, [epg, activeEpg?.id, detailChannel?.id]);

  useEffect(
    () => () => {
      if (!epg && detailChannel?.id && startId === detailChannel?.id && completedId !== startId) {
        const playedDuration = calculatePlayingTime({
          totalPlayedData: totalPlayedDataRef.current
        });
        const segmentParams = getSegmentParams();

        handleLiveTVCompletedTrackingData({
          segmentParams,
          channelDetail: detailChannel,
          playedDuration
        });

        completedId = startId;
      }
    },
    [epg, detailChannel?.id]
  );

  useEffect(
    () => () => {
      if (epg && activeEpg?.id && startId === activeEpg?.id && completedId !== startId) {
        const playedDuration = calculatePlayingTime({
          totalPlayedData: totalPlayedDataRef.current
        });
        const segmentParams = getSegmentParams();
        handleLiveTVCompletedTrackingData({
          segmentParams,
          channelDetail: detailChannel,
          playedDuration
        });
        completedId = startId;
      }
    },
    [epg, activeEpg?.id]
  );

  useEffect(() => {
    checkPrevent();
    return () => {
      if (!isEmpty(liveTVCCURef.current)) {
        liveTVCCURef.current.onEnd();
      }
    };
  }, [contentId]);

  useEffect(() => {
    connectSocketCountRef.current = 0;
    setCancelPlayerData(null);
    return () => {
      if (id || activeEpg?.id) {
        handleRecord({ action: ConfigUser.RECORD.ACTION.STOP, data: detailChannel });
      }
    };
  }, [id, activeEpg?.id]);

  useEffect(() => {
    if (
      id &&
      permission !== PERMISSION.CAN_WATCH &&
      checkIsFullscreen() &&
      document.exitFullscreen
    ) {
      document.exitFullscreen();
    }
  }, [id, permission]);

  useEffect(() => {
    socketConnectedStatus = false;
    if (id && drmServiceName === DRM.K_PLUS && drmProvider) {
      const prevent = checkPrevent();
      if (!prevent) {
        connectSocket();
      }
      setSocketConnected(false);
    }
  }, [id, drmServiceName, drmProvider]);

  useEffect(() => {
    getValidateKPlus();
  }, [detailChannel, deviceId, profile?.id, isMobile, deviceName]);

  useEffect(() => {
    handleValidateKPlus();
  }, [validateKPlus]);

  const getValidateKPlus = () => {
    if (validateKPlusTimer?.current) clearTimeout(validateKPlusTimer?.current);
    if (id && profile?.id && deviceId && deviceName) {
      if (drmServiceName === DRM.K_PLUS && permission === PERMISSION.CAN_WATCH) {
        const localValidate = checkValidateKPlusLocal();
        if (localValidate) dispatch(createAction(ACTION_TYPE.VALIDATE_K_PLUS, localValidate));
        else {
          dispatch(
            validateKPlusAction({
              deviceId,
              isMobile,
              userId: profile?.id,
              liveTVId: id,
              browserName: deviceName
            })
          );
        }
      } else {
        dispatch(resetValidateKPlus());
      }
    }
  };

  const checkValidateKPlusLocal = () => {
    const kPlusValidateLocal: any = ConfigLocalStorage.get(LocalStorage.K_PLUS_VALIDATE);
    const kPlusValidate = kPlusValidateLocal ? JSON.parse(kPlusValidateLocal) : {};
    if (kPlusValidate?.mustRevalidateBefore > 0) {
      const duration = kPlusValidate?.mustRevalidateBefore * 1000 - new Date().getTime();
      if (duration <= 0) return null;
      return { ...kPlusValidate, validateDuration: duration };
    }
    return null;
  };

  const handleValidateKPlus = () => {
    if (validateKPlusTimer?.current) clearTimeout(validateKPlusTimer?.current);
    if (validateKPlus?.validation === 0 && permission === PERMISSION.CAN_WATCH) {
      cancelPlayer({
        data: detailChannel,
        usi
      });
      dispatch(
        openPopup({
          name: POPUP.NAME.VALIDATE_K_PLUS,
          retryAction: handleRetry
        })
      );
    } else if (validateKPlus?.validation === 1) {
      if (validateKPlus?.validateDuration > 0) {
        validateKPlusTimer.current = createTimeout(() => {
          getValidateKPlus();
        }, validateKPlus?.validateDuration);
      }
    }
  };

  const onBrowserChange = () => {
    if (!isEmpty(liveTVCCURef.current)) {
      liveTVCCURef.current.onEnd();
    }
    const playedDuration = calculatePlayingTime({
      totalPlayedData: totalPlayedDataRef.current
    });
    const segmentParams = getSegmentParams();
    handleLiveTVCompletedTrackingData({
      segmentParams,
      channelDetail: detailChannel,
      playedDuration,
      isTrackImmediately: true
    });
    handleRecord({ action: ConfigUser.RECORD.ACTION.STOP });
    handleUnSubscribeSocket();

    if (detailChannel?.id && playingId.current !== detailChannel?.id) {
      TrackingPlayer.exitBeforeStarted({
        contentId: detailChannel.id,
        contentTitle: detailChannel.title,
        contentType: CONTENT_TYPE.LIVE_TV
      });
    }
    if (sessionTokenEl.current) {
      handleEndSessionPlay(sessionTokenEl.current);
    }

    return undefined;
  };

  const connectSocket = async () => {
    handleUnSubscribeSocket();
    removePlayerUsi();
    if (!detailChannel?.usi || drmServiceName !== DRM.K_PLUS) return;
    connectSocketCountRef.current += 1;
    SocketCluster.connectSocket({
      usi: detailChannel?.usi,
      channelId: id,
      handleSocketResult
    });
  };

  const checkPrevent = () => {
    let prevent = false;
    if (id) {
      const checkStatusPrevent = handlePreventContent({
        id,
        permission,
        preventContent
      });
      prevent = !!checkStatusPrevent?.isPrevent;
      setIsPrevent(prevent);
      setPreventNote(checkStatusPrevent?.preventNote);
    }
    return prevent;
  };

  const handleSocketResult = ({ data, usi }: any) => {
    console.log('handleSocketResult', socketConnectedStatus, socketConnected, { data, usi });

    switch (data?.action) {
      case ConfigSocket.EVENTS.CONNECTED: {
        socketConnectedStatus = true;
        setSocketConnected(true);
        break;
      }
      case ConfigSocket.EVENTS.DISCONNECTED: {
        if (RECONNECT_SOCKET) {
          setSocketConnected(false);
          cancelPlayer({ disconnected: true });
        }
        break;
      }
      case ConfigSocket.EVENTS.SHOW_FINGERING:
        showFingering(data);
        break;
      case ConfigSocket.EVENTS.CANCEL_PLAYER:
        cancelPlayer({ data, usi });
        setCancelPlayerData(data);
        handlePopupCancelPlayer(data);
        break;
      case ConfigSocket.EVENTS.ERROR:
        if (RECONNECT_SOCKET) {
          if (connectSocketCountRef.current >= 3) {
            cancelPlayer({ data, usi });
            setCancelPlayerData(data);
            handlePopupCancelPlayer(data);
          } else {
            connectSocket();
          }
        } else if (!socketConnectedStatus) {
          cancelPlayer({ data, usi });
          setCancelPlayerData(data);
          handlePopupCancelPlayer(data);
        }
        break;
      default:
        break;
    }
  };

  const handlePopupCancelPlayer = (data: any) => {
    let popupName = '';
    switch (data?.type_msg) {
      case ConfigSocket.MSG_TYPE.LIMIT_STREAM:
        popupName = POPUP.NAME.LIMIT_CCU;
        break;
      case ConfigSocket.MSG_TYPE.LIMIT_EPG:
        popupName = POPUP.NAME.LIMIT_EPG;
        break;
      case ConfigSocket.MSG_TYPE.BLOCK_ACCOUNT:
        popupName = POPUP.NAME.BLOCK_ACCOUNT;
        break;
      case ConfigSocket.MSG_TYPE.ERROR:
        popupName = POPUP.NAME.PLAYER_ERROR_LIVETV;
        break;
      default:
        break;
    }
    dispatch(
      openPopup({
        name: popupName,
        retryAction: handleRetry,
        errorData: {
          socketData: data,
          errorType: ERROR_PLAYER.TYPE.SOCKET
        }
      })
    );
  };

  const handleOpenCatError = (catData: any) => {
    const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
    if (sessionToken) {
      handleEndSessionPlay(sessionToken);
    }
    if (navigator.onLine) {
      dispatch(
        openPopup({
          name: POPUP.NAME.PLAYER_ERROR_LIVETV,
          retryAction: handleRetry,
          errorData: {
            ...catData?.errorData,
            liveTVData: { detailChannel, activeEpg }
          }
        })
      );
    }
  };

  const showFingering = (data: any) => {
    const fontSize = data?.font_size || 'large';
    const displayTime = data?.display_time || ConfigSocket.SHOW_FINGERING_TIMER;
    const position = getPositionFingering(data?.position);
    addPlayerUSI({ fontSize, position, displayTime });
  };

  const getPositionFingering = (position: any) => {
    let top = '10%';
    let left = '10%';
    switch (position) {
      case ConfigSocket.POSITION.TOP_LEFT:
        break;
      case ConfigSocket.POSITION.TOP_RIGHT:
        left = '85%';
        break;
      case ConfigSocket.POSITION.BOTTOM_LEFT:
        top = '82%';
        break;
      case ConfigSocket.POSITION.BOTTOM_RIGHT:
        top = '82%';
        left = '85%';
        break;
      case ConfigSocket.POSITION.MIDDLE:
        top = '49%';
        left = '49%';
        break;
      default: {
        top = `${10 + getRandomInt(82)}%`;
        left = `${10 + getRandomInt(85)}%`;
        break;
      }
    }
    return { top, left };
  };

  const addPlayerUSI = ({ fontSize, position, displayTime }: any) => {
    const playerContainer: any = document.getElementById(EL_ID.PLAYER_CONTAINER);
    const playerUSI = document.getElementById(playerUsiIdRef.current);
    if (playerUSI) {
      removePlayerUsi();
      if (fingeringTimerRef.current) clearTimeout(fingeringTimerRef.current);
      // return
    }
    playerUsiIdRef.current = getRandomText(10);
    const usiEl: any = document.createElement('div');
    usiEl.className = `player__${Math.random() * 99999}`;
    usiEl.id = playerUsiIdRef.current;
    usiEl.name = `${Math.random() * 99999}`;
    usiEl.style.top = '0px';
    usiEl.style.right = '0px';
    usiEl.style.left = '0px';
    usiEl.style.bottom = '0px';
    usiEl.style.position = 'absolute';
    usiEl.style.zIndex = 9;
    const wrapper: any = document.createElement('div');
    wrapper.style.cursor = 'initial';
    wrapper.name = `${Math.random() * 99999}`;
    const textUsi: any = document.createElement('span');
    textUsi.className = 'text text-white absolute';
    textUsi.name = `${Math.random() * 99999}`;
    textUsi.style.backgroundColor = 'rgba(0,0,0,0.5)';
    textUsi.style.fontSize = fontSize;
    textUsi.innerText = detailChannel?.usi || '';
    textUsi.style.top = position?.top;
    textUsi.style.left = position?.left;
    textUsi.style.color = 'white';
    textUsi.style.position = 'absolute';
    wrapper.appendChild(textUsi);
    usiEl.appendChild(wrapper);
    playerContainer.appendChild(usiEl);
    if (fingeringTimerRef.current) clearTimeout(fingeringTimerRef.current);
    fingeringTimerRef.current = createTimeout(() => {
      removePlayerUsi();
    }, displayTime * 1000);
  };

  const cancelPlayer = (cancelData: any) => {
    socketConnectedStatus = false;
    if (!isEmpty(videoRef.current)) videoRef.current.pause();
    removePlayerUsi();
    handleUnSubscribeSocket();
    if (typeof setCancelPlayerRef?.current === 'function') setCancelPlayerRef.current(cancelData);
  };

  const removePlayerUsi = () => {
    if (isEmpty(playerUsiIdRef.current)) return;
    const playerUSI: any = document.getElementById(playerUsiIdRef.current);
    const playerContainer: any = document.getElementById(EL_ID.PLAYER_CONTAINER);
    playerContainer.removeChild(playerUSI);
    playerUsiIdRef.current = '';
  };

  const handleUnSubscribeSocket = () => {
    socketConnectedStatus = false;
    if (typeof SocketCluster !== 'undefined' && SocketCluster?.unsubscribe) {
      SocketCluster.unsubscribe();
    }
  };

  const handleRetry = () => {
    handleUnSubscribeSocket();
    if (detailChannel?.isConcurrentScreenLimit) return;
    dispatch(getDetailChannelById(id, isIOS));
  };

  const setupPlayer = ({ video, setCancelPlayer, liveTVCCU, player, playerName }: any) => {
    videoRef.current = video;
    setCancelPlayerRef.current = setCancelPlayer;
    liveTVCCURef.current = liveTVCCU;
    playerRef.current = player;
    playerNameRef.current = playerName;

    // Tracking Init Player
    handleInitPlayer();
  };

  const handleInitPlayer = () => {
    if (detailChannel?.id && initId.current !== detailChannel?.id) {
      initId.current = detailChannel?.id;
      initPlayerTime.current = new Date().getTime();
      TrackingPlayer.initPlayer({
        contentId: detailChannel?.id,
        contentType: detailChannel?.type,
        contentTitle: detailChannel?.title
      });
    }
  };

  const startupLoadPreroll = () => {
    TrackingPlayer.videoStartupPrerollLoad({
      contentId: detailChannel?.id,
      contentType: detailChannel?.type,
      contentTitle: detailChannel?.title,
      totalTime: initPlayerTime.current ? new Date().getTime() - initPlayerTime.current : 0
    });
  };

  const onPlaying = () => {
    if (playingId.current !== detailChannel.id) {
      playingId.current = detailChannel.id;
    }
    handleRecord({ action: ConfigUser.RECORD.ACTION.PLAY });
  };

  const handleRecord = ({ action, data }: any) => {
    const dataDetail = data || detailChannel;
    const getInfoTrack = getInfoVideoCodec({
      player: playerRef.current,
      playerName: playerNameRef.current
    });
    const params: any = {
      usi: dataDetail?.usi,
      contentId: dataDetail?.id,
      contentName: dataDetail?.title,
      contentType: dataDetail?.type,
      timeSecond: Date.now() / 1000,
      action,
      videoCodec: getInfoTrack?.videoCodec
    };
    if (action === ConfigUser.RECORD.ACTION.STOP) {
      handleTracking(ConfigUser.TRACKING.STOP);
    }
    UserServices.recordProgressOfUser(params);
  };

  const handleLiveTVCompletedTrackingData = ({
    segmentParams,
    channelDetail,
    playedDuration,
    isTrackImmediately
  }: any) => {
    const isVipChannel = !!detailChannel?.isPremium;
    const isPremiumUser = !!profile?.isPremium;
    if (!isVipChannel || (isVipChannel && isPremiumUser)) {
      TrackingPlayer.liveTVCompleted({
        ...segmentParams,
        channelDetail,
        playedDuration,
        isTrackImmediately
      });
    }
  };

  const handleTracking = ({ action, contentId, bufferData }: any) => {
    const getInfoTrack = getInfoVideoCodec({
      player: playerRef.current,
      playerName: playerNameRef.current
    });
    UserServices.handleTrackingLog({
      tracker: tracking,
      action,
      contentId,
      bufferData: {
        ...bufferData,
        videoCodec: getInfoTrack?.videoCodec
      }
    });
  };

  const onEnded = () => {
    const segmentParams = getSegmentParams();
    handleLiveTVCompletedTrackingData({ segmentParams, channelDetail: detailChannel });
    handleRecord({ action: ConfigUser.RECORD.ACTION.STOP });
  };

  const onPaused = () => {
    handleRecord({ action: ConfigUser.RECORD.ACTION.PAUSE });
  };

  const onError = ({ event, linkPlay, streamingProtocol, getInfoTrack }: any) => {
    TrackingPlayer.playerError({
      event,
      content: detailChannel,
      streamingProtocol,
      getInfoTrack,
      linkPlay
    });
  };

  const getSegmentParams = () => {
    const isLive = activeEpg?.isLive;
    const title = detailChannel?.title;
    const playType = detailChannel?.isPremium === 1 ? VALUE.SVOD : VALUE.AVOD;
    const epgTitle = activeEpg?.title || detailChannel?.epg?.title || '';
    const epgId = activeEpg?.id || detailChannel?.epg?.id || '';
    const channelId = id;
    const hasAds = detailChannel?.ads?.length > 0;
    return {
      isLive,
      title,
      epgTitle,
      epgId,
      currentEpg: activeEpg,
      hasAds,
      playType,
      channelId,
      sessionId
    };
  };

  const setInitPlay = ({ bufferTime }: any) => {
    sessionId = Date.now();
    startTimeRef.current = sessionId;

    const isVipChannel = !!detailChannel?.isPremium;
    const isPremiumUser = !!profile?.isPremium;

    const bufferData = {
      contentId: id,
      contentName: detailChannel?.title || '',
      contentType: 5,
      usi: detailChannel?.usi,
      data: [
        {
          action: ConfigUser.RECORD.ACTION.PLAY,
          duration: parseInt(videoRef?.current?.duration || 0),
          timestamp: Math.floor(Date.now() / 1000),
          buffer_time: bufferTime / 1000
        }
      ]
    };

    handleTracking({
      action: ConfigUser.TRACKING.START,
      contentId: id,
      bufferData
    });

    LiveTVApi.addWatching({ channelId: id });

    const segmentParams = getSegmentParams();
    if (!isVipChannel || (isVipChannel && isPremiumUser)) {
      TrackingPlayer.liveTVStarted(segmentParams);
    }

    startId = epg ? activeEpg?.id : id;
  };

  const onTimeUpdate = ({ currentTime }: any) => {
    // Handle segment progress
    trackingLiveProgress({
      startTime: startTimeRef.current,
      segmentLiveTVProgress
    });
    handleRecordOffset(currentTime);
  };

  const segmentLiveTVProgress = (eventName: any, progress: any) => {
    const segmentParams = getSegmentParams();
    const isVipChannel = !!detailChannel?.isPremium;
    const isPremiumUser = !!profile?.isPremium;
    if (!isVipChannel || (isVipChannel && isPremiumUser)) {
      TrackingPlayer.liveTVProgress({ ...segmentParams, progress });
    }
  };

  const handleRecordOffset = (currentTime: any) => {
    if (currentTime && currentTime - offsetTimeRef.current >= 5) {
      handleRecord({ action: ConfigUser.RECORD.ACTION.OFFSET });
      offsetTimeRef.current = currentTime;
    }
    offsetTimeRef.current =
      offsetTimeRef.current >= currentTime ? currentTime : offsetTimeRef.current;
  };

  const onControlPlay = ({ playerError }: any) => {
    if (validateKPlus?.validation === 0) {
      dispatch(
        openPopup({
          name: POPUP.NAME.VALIDATE_K_PLUS,
          retry: handleRetry
        })
      );
    } else if (playerError) {
      handleRetry();
    } else if (typeof checkChannelPermission === 'function') checkChannelPermission(detailChannel);
  };

  const updateTotalPlayedData = (totalPlayedData: any) => {
    totalPlayedDataRef.current = totalPlayedData || [];
  };

  const handleStatusFullscreenOfPlayer = (status: any) => {
    dispatch(setStatusFullscreen(status));
  };

  const handleOpenPopup = (dataRequest: any) => {
    dispatch(openPopup(dataRequest));
  };

  const handlePropsToast = (e: any) => {
    dispatch(setToast(e));
  };

  return (
    <section className={`section section--television overflow${isMobileOnly ? '' : ' canal-v'}`}>
      <div className="section-body">
        <div className="player player--live-tv overflow">
          <div className="player-stage" id="playerStage">
            <Player
              isWindows={isWindows}
              epg={epg}
              isGlobal={isGlobal}
              detailChannel={detailChannel}
              activeEpg={activeEpg}
              isIOS={isIOS}
              isAndroid={isAndroid}
              isMacOs={isMacOs}
              isSafari={isSafari}
              isMobile={isMobile}
              isTablet={isTablet}
              isLive={isLive}
              isPrevent={isPrevent}
              preventNote={preventNote}
              profile={profile}
              isLiveTV
              isSeekAllow={isSeekAllow}
              deviceId={deviceId}
              assetId={assetId}
              contentId={contentId}
              ads={ads}
              playerTitle={title}
              permission={permission}
              displayControl={displayControl}
              notSeekBar={false}
              seekValue={100}
              isPaused={isPaused}
              poster={imgThumb}
              softLogo={playerLogo}
              drmServiceName={drmServiceName}
              concurrentScreen={concurrentScreen}
              linkPlay={linkPlayToUse}
              linkPlaysToRetry={linkPlaysToRetry}
              cancelPlayer={cancelPlayerData}
              socketConnected={socketConnected}
              qnetInfo={qnetInfo}
              preventPauseLive={!epg}
              isDrm={isDrm}
              qnetDrm={qnetDrm}
              blockPlayer={blockPlayer}
              isAgeRestricted={warningScreen}
              warningScreen={warningScreen}
              drmProvider={drmProvider}
              drmMerchant={drmMerchant}
              handleRetry={handleRetry}
              setupPlayer={setupPlayer}
              onPlaying={onPlaying}
              onEnded={onEnded}
              onPaused={onPaused}
              onError={onError}
              openPopup={handleOpenPopup}
              setInitPlay={setInitPlay}
              onTimeUpdate={onTimeUpdate}
              onControlPlay={onControlPlay}
              updateTotalPlayedData={updateTotalPlayedData}
              handleStatusFullscreenOfPlayer={handleStatusFullscreenOfPlayer}
              handleOpenCatError={handleOpenCatError}
              startupLoadPreroll={startupLoadPreroll}
              handleEndSessionPlay={handleEndSessionPlay}
              setToast={handlePropsToast}
              typePlayer="livetv"
              router={router}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default React.memo(LiveTVPlayer);
