import React, { useEffect, useRef } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import SwiperCore, { Navigation } from 'swiper/core';
import { isMobileOnly } from 'react-device-detect';
import isEmpty from 'lodash/isEmpty';
import TrackingApp from '@tracking/functions/TrackingApp';
import classNames from 'classnames';

SwiperCore.use([Navigation]);

const BroadcastingList = ({
  isMobile,
  isFullscreen,
  data,
  category,
  activeSubCategory,
  onChangeActiveSubCategory
}: any) => {
  const swiperRef = useRef<any>(null);

  useEffect(() => {
    if (isFullscreen && swiperRef.current && typeof swiperRef.current.update === 'function') {
      swiperRef.current.update();
    }
  }, [data, isFullscreen]);

  const onClickTab = (tab: any, i: any) => {
    if (!isEmpty(tab)) {
      TrackingApp.ribbonSelected({
        data: tab,
        ribbonOrder: i,
        title: tab.name,
        isLiveTv: true,
        category
      });
      if (typeof onChangeActiveSubCategory === 'function') {
        onChangeActiveSubCategory(tab, category?.id);
      }
    }
  };

  if (!data || data.length === 0) return null;

  return (
    <div className="flex flex-col md:flex-row md:!py-5">
      <Swiper
        spaceBetween={isMobileOnly ? 20 : 32}
        slidesPerView="auto"
        allowTouchMove={!!isMobile}
        containerModifierClass="slider slider--menu-broadcast broadcast-category non-hover overflow "
        data-item-view="auto"
        slideToClickedSlide={!!isMobileOnly}
        centeredSlides={!!isMobileOnly}
        centeredSlidesBounds={!!isMobileOnly}
        onSwiper={(swiper) => (swiperRef.current = swiper)}
        navigation={
          !isMobile && isFullscreen
            ? {
                prevEl: '.slider-navigate.slider-navigate-prev',
                nextEl: '.slider-navigate.slider-navigate-next'
              }
            : false
        }
      >
        {data.map((item: any, i: any) => (
          <SwiperSlide
            className={`slider__item${activeSubCategory?.id === item.id ? ' active' : ''}`}
            key={item.id}
          >
            <button
              id={item.id}
              onClick={() => onClickTab(item, i)}
              title={item.name}
              className={classNames(
                'category-item h-9',
                // isFullscreen ? 'py-5' : '',
                activeSubCategory?.id === item.id ? '!text-white' : 'hover:!text-vo-green'
              )}
              type="button"
            >
              {item.name}
            </button>
          </SwiperSlide>
        ))}
        {!isMobile && isFullscreen && (
          <div className="slider-navigate-group align-right horizontal layer-1 absolute right !space-x-3">
            <div className="slider-navigate slider-navigate-prev size-16 relative" />
            <div className="slider-navigate slider-navigate-next size-16 relative" />
          </div>
        )}
      </Swiper>
    </div>
  );
};

export default BroadcastingList;
