import React, { useRef } from 'react';
import isEmpty from 'lodash/isEmpty';
import Card from '@components/basic/Card/Card';
import ItemRibbonLiveTvAds from '@components/OutstreamAds/ItemRibbonLiveTvAds';
import { CONTENT_TYPE, POSITION_TRIGGER } from '@constants/constants';
import { useSelector } from 'react-redux';
import TriggerTouchPoint from '@components/home/<USER>';

const ChannelItems = ({ data, activeCategory, activeChannelId, notLazy }: any) => {
  const { dataTriggerLiveTv } = useSelector((state: any) => state?.Trigger);
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile);
  const ref = useRef([]);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);

  const styleBanner = {
    width: 'calc(100% - 0.5rem)',
    flex: '0 0 calc(100% - 0.5rem)'
  };

  if (isEmpty(data)) return null;
  return (
    <>
      {(data || []).map((channel: any, i: any) => {
        if (channel?.type === CONTENT_TYPE.BANNER_TRIGGER && !isGlobal && !currentProfile?.isKid) {
          return (
            <TriggerTouchPoint
              dataTrigger={dataTriggerLiveTv}
              image={dataTriggerLiveTv?.image}
              imageMobile={dataTriggerLiveTv?.image}
              url={dataTriggerLiveTv?.navigateUrl}
              positionTrigger={POSITION_TRIGGER.LIVE_TV}
              styleBanner={styleBanner}
              key={i + channel?.id}
            />
          );
        }
        if (channel?.type === CONTENT_TYPE.ADS && !isGlobal) {
          return <ItemRibbonLiveTvAds key={`${channel.id}_${i}`} data={channel} />;
        }
        return (
          <Card
            key={`${channel.id}-${i}`}
            cardData={{ ...channel, categoryTracking: activeCategory }}
            ref={ref}
            index={i + 1}
            randomID={`${channel.id}_${i}`}
            activeLiveTvID={activeChannelId}
            notLazy={notLazy}
          />
        );
      })}
    </>
  );
};
export default ChannelItems;
