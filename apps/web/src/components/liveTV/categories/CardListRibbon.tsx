import React, { useRef } from 'react';
import isEmpty from 'lodash/isEmpty';
import Card from '@components/basic/Card/Card';

const CardListRibbon = ({ data, activeCategory, isFullscreen }: any) => {
  const ref = useRef([]);

  if (isEmpty(data)) return null;

  return (
    <div className="rocopa">
      <div
        className={`card-group group-margin-y-4 group-margin-x-4 col-x-small-2 col-small-2 col-medium-4 ${
          isFullscreen ? 'col-large-3' : 'col-large-6'
        } `}
        data-number-item={isFullscreen ? 3 : 6}
      >
        {data.map((item: any, i: any) => (
          <Card
            key={`${item.id}-${i}`}
            cardData={{ ...item, categoryTracking: activeCategory }}
            index={i + 1}
            ref={ref}
            randomID={`${item?.id}_${i}`}
            isLivetvBroadcasting
          />
        ))}
      </div>
    </div>
  );
};

export default CardListRibbon;
