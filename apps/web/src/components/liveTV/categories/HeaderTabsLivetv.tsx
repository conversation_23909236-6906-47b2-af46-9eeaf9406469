import React from 'react';
import isEmpty from 'lodash/isEmpty';
import { Swiper, SwiperSlide } from 'swiper/react';
import { isMobileOnly } from 'react-device-detect';
import { TAB_LISTS } from '@constants/constants';
import classNames from 'classnames';
import BroadcastingList from './BroadcastingList';
import ChannelTabs from './ChannelTabs';

const HeaderTabsLivetv = React.memo(
  ({
    isMobile,
    isFullscreen,
    activeCategory,
    activeFilterInDSK,
    activeSubCategoryDSK,
    activeSubCategoryDPS,
    listFilterChannelPage,
    dataSubCategoriesDPS,
    dataSubCategoriesDSK,
    onChangeActiveSubCategory,
    onChangeCategory,
    onChangeFilter,
    isGlobal
  }: any) => (
    <>
      <Swiper
        spaceBetween={12}
        slidesPerView="auto"
        allowTouchMove={!!isMobile}
        containerModifierClass="slider slider--channel tabs tabs--channel "
        data-item-view="auto"
        slideToClickedSlide={!!isMobileOnly}
        centeredSlides={!isGlobal && !!isMobileOnly}
        centeredSlidesBounds={!isGlobal && !!isMobileOnly}
      >
        {(TAB_LISTS || []).map((item: any) => {
          if (item.id === 'LPS' && !isMobile && !isFullscreen) return null;
          if (item.id === 'DPS' && isGlobal) return null;
          return (
            <SwiperSlide
              className={`slider__item ${activeCategory?.id === item.id ? 'active' : ''}`}
              key={item.id}
            >
              <button
                className={classNames(
                  'tabs-title',
                  activeCategory?.id !== item.id ? 'hover:!text-vo-green hover:before:!hidden' : ''
                )}
                title={item.title}
                id={item.id}
                onClick={() => onChangeCategory(item)}
                type="button"
              >
                {item.title}
              </button>
            </SwiperSlide>
          );
        })}
      </Swiper>
      {activeCategory?.id === 'DPS' && !isEmpty(dataSubCategoriesDPS) && (
        <BroadcastingList
          isMobile={isMobile}
          isFullscreen={isFullscreen}
          category={activeCategory}
          data={dataSubCategoriesDPS}
          activeSubCategory={activeSubCategoryDPS}
          onChangeActiveSubCategory={onChangeActiveSubCategory}
        />
      )}
      {activeCategory?.id === 'DSK' && !isEmpty(dataSubCategoriesDSK) && (
        <ChannelTabs
          isMobile={isMobile}
          isFullscreen={isFullscreen}
          activeFilter={activeFilterInDSK}
          activeSubCategory={activeSubCategoryDSK}
          data={dataSubCategoriesDSK}
          listFilter={listFilterChannelPage}
          category={activeCategory}
          onChangeFilter={onChangeFilter}
          onChangeActiveSubCategory={onChangeActiveSubCategory}
        />
      )}
    </>
  )
);
export default HeaderTabsLivetv;
