import React from 'react';

const IconGoogle = ({ size }: any) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size || 30}
    height={size || 30}
    viewBox="0 0 30 30"
    fill="none"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M29.4 15.3416C29.4 14.278 29.3045 13.2553 29.1273 12.2734H15V18.0757H23.0727C22.725 19.9507 21.6682 21.5393 20.0795 22.603V26.3666H24.9273C27.7636 23.7553 29.4 19.9098 29.4 15.3416Z"
      fill="#4285F4"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.0002 29.9996C19.0502 29.9996 22.4457 28.6564 24.9275 26.3655L20.0798 22.6019C18.7366 23.5019 17.0184 24.0337 15.0002 24.0337C11.0934 24.0337 7.78661 21.3951 6.60707 17.8496H1.5957V21.736C4.06388 26.6382 9.13661 29.9996 15.0002 29.9996Z"
      fill="#34A853"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.60682 17.85C6.30682 16.95 6.13636 15.9887 6.13636 15C6.13636 14.0114 6.30682 13.05 6.60682 12.15V8.26367H1.59545C0.579545 10.2887 0 12.5796 0 15C0 17.4205 0.579545 19.7114 1.59545 21.7364L6.60682 17.85Z"
      fill="#FBBC05"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.0002 5.96591C17.2025 5.96591 19.1798 6.72273 20.7343 8.20909L25.0366 3.90682C22.4389 1.48636 19.0434 0 15.0002 0C9.13661 0 4.06388 3.36136 1.5957 8.26364L6.60707 12.15C7.78661 8.60454 11.0934 5.96591 15.0002 5.96591Z"
      fill="#EA4335"
    />
  </svg>
);

export default IconGoogle;
