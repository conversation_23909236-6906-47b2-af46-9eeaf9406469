.toggle {
  @apply relative inline-flex items-center shrink-0 cursor-pointer rounded-full transition-colors duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2;

  &.checked {
    @apply bg-vo-green;
  }

  &:not(.checked) {
    @apply bg-gray-200 shadow-[0_1px_2px_0px_rgba(0,0,0,0.22)];
  }

  &.disabled {
    @apply cursor-not-allowed opacity-50;
  }

  &.sm {
    @apply w-[30px] h-4 px-0.5;
  }

  &.md {
    @apply w-11 h-6 px-0.5;
  }

  &.lg {
    @apply w-14 h-7 px-1;
  }
}

.knob {
  @apply pointer-events-none absolute transform rounded-full bg-white shadow-sm transition duration-200 ease-in-out;

  &.sm {
    @apply h-3 w-3;
  }

  &.md {
    @apply h-5 w-5;
  }

  &.lg {
    @apply h-5 w-5;
  }

  &.checked {
    @apply translate-x-full;
  }
}
