import classNames from 'classnames';
import React, { useCallback } from 'react';
import styles from './Toggle.module.scss';
import { useDispatch } from 'react-redux';
import { setRecurringStatusPayment } from '@actions/payment';

// Define the available sizes as a type to ensure type safety with styles
type ToggleSize = 'sm' | 'md' | 'lg';

interface ToggleProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  size?: ToggleSize;
  className?: string;
  'aria-label'?: string;
  [key: string]: any;
}

const Toggle = React.memo(
  ({
    checked,
    onChange,
    disabled = false,
    size = 'sm',
    className,
    'aria-label': ariaLabel
  }: ToggleProps) => {
    const dispatch = useDispatch();
    const handleClick = useCallback(() => {
      if (!disabled) {
        onChange(!checked);
        dispatch(setRecurringStatusPayment(!checked));
      }
    }, [checked, disabled, onChange]);

    return (
      <button
        role="switch"
        aria-checked={checked}
        aria-label={ariaLabel}
        disabled={disabled}
        onClick={handleClick}
        className={classNames(
          styles.toggle,
          styles[size],
          checked && styles.checked,
          disabled && styles.disabled,
          className
        )}
        tabIndex={0}
      >
        <span
          className={classNames(styles.knob, styles[size], checked && [styles.checked])}
          style={{
            top: '50%',
            transform: `translateY(-50%) ${
              checked ? 'translateX(calc(100% + 2px))' : 'translateX(0)'
            }`
          }}
        />
      </button>
    );
  }
);

export default Toggle;
