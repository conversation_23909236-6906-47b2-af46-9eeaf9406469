import React from 'react';
import styles from './styles.module.scss';
import { CURRENCY } from '@/constants/constants';

function PricePopupInfo({ data }: any) {
  const { discount_price: discountPrice, display_price: displayPrice } = data;
  if (
    (discountPrice === null || discountPrice === undefined) &&
    (displayPrice === null || displayPrice === undefined)
  )
    return;
  return (
    <div className={styles.priceContainer}>
      {discountPrice !== null && discountPrice !== undefined && (
        <p className={styles.priceOriginal}>{`${new Intl.NumberFormat('vi-VN').format(
          discountPrice || 0
        )} ${CURRENCY.VND}`}</p>
      )}
      {displayPrice !== null && displayPrice !== undefined && (
        <p className={styles.priceDiscount}>{`${new Intl.NumberFormat('vi-VN').format(
          displayPrice || 0
        )} ${CURRENCY.VND}`}</p>
      )}
    </div>
  );
}

export default PricePopupInfo;
