import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { PAGE, PAYMENT_METHOD, PAYMENT_TYPE } from '@constants/constants';
import Icon from '@components/basic/Icon/Icon';
import classNames from 'classnames';
import styles from './Style.module.scss';

const Step = ({ pathname, isVoucher, isRentalContent, isMobile }: any) => {
  const { selectedMethod, transactionResult } = useSelector((state: any) => state?.Payment);
  const isPVODContent = useMemo(() => pathname?.includes(PAGE.PVOD_CONTENT), [pathname]);
  const currentStep = useMemo(() => {
    if (
      (isPVODContent || isRentalContent) &&
      pathname.includes(PAGE.PAYMENT) &&
      pathname !== PAGE.PAYMENT_RESULT
    ) {
      return 2;
    }
    if (pathname === PAGE.PAYMENT || pathname === PAGE.ZALOPAY) return 1;
    if (pathname === PAGE.PAYMENT_METHOD || pathname === PAGE.ZALOPAY_METHOD) return 2;
    if (pathname === PAGE.PAYMENT_RESULT || pathname === PAGE.ZALOPAY_RESULT) {
      return 3;
    }
    return 1;
  }, [pathname, transactionResult, isPVODContent, isRentalContent]);

  const isRentedContent = useMemo(() => {
    if (isPVODContent || isRentalContent || transactionResult?.paymentType === PAYMENT_TYPE.TVOD) {
      return true;
    }
    if (
      selectedMethod?.id === PAYMENT_METHOD.MOBI ||
      selectedMethod?.id === PAYMENT_METHOD.VINA ||
      selectedMethod?.id === PAYMENT_METHOD.VIETTEL ||
      selectedMethod?.id === PAYMENT_METHOD.PAYOO
    ) {
      return true;
    }
    return false;
  }, [selectedMethod, isRentalContent, transactionResult]);

  const data = useMemo(() => {
    if (isPVODContent || isRentalContent || transactionResult?.paymentType === PAYMENT_TYPE.TVOD) {
      return [
        { id: 1, label: 1, title: 'Chọn nội dung' },
        { id: 2, label: 2, title: 'Chọn phương thức thanh toán' },
        { id: 3, label: 3, title: 'Kết quả' }
      ];
    }
    if (
      selectedMethod?.id === PAYMENT_METHOD.MOBI ||
      selectedMethod?.id === PAYMENT_METHOD.VINA ||
      selectedMethod?.id === PAYMENT_METHOD.VIETTEL ||
      selectedMethod?.id === PAYMENT_METHOD.PAYOO
    ) {
      return [
        { id: 1, label: 1, title: 'Chọn gói' },
        {
          id: 2,
          label: 2,
          title: 'Chọn thời hạn & phương thức thanh toán'
        }
      ];
    }
    return [
      { id: 1, label: 1, title: 'Chọn gói' },
      {
        id: 2,
        label: 2,
        title: 'Chọn thời hạn & phương thức thanh toán'
      },
      { id: 3, label: 3, title: 'Kết quả' }
    ];
  }, [selectedMethod, isMobile, transactionResult]);

  if (isVoucher) return null;

  return (
    <div
      className={classNames(
        styles.steps,
        'flex justify-center items-center w-full',
        isMobile && styles.mobile,
        isRentedContent && styles.rentedContent
      )}
    >
      <div className="step flex items-start justify-center">
        {(data || []).map((step) => (
          <div
            key={step?.id}
            className={classNames(
              currentStep > step?.id
                ? `${styles.completed} completed`
                : currentStep === step?.id
                ? `${styles.current} current`
                : '',
              styles.step
            )}
          >
            {isMobile ? (
              <div className="bg-[#111] !w-[50px] flex justify-center overflow-hidden z-10 after:!content-none !pr-0 !mr-0">
                <span className={classNames('step__counter', styles.counter)}>
                  {currentStep > step?.id ? (
                    <Icon spClass="min-w-[14px]" iClass="vie-tick" />
                  ) : (
                    step?.label
                  )}
                </span>
              </div>
            ) : (
              <span className={classNames('step__counter', styles.counter)}>
                {currentStep > step?.id ? <Icon iClass="vie-tick" /> : step?.label}
              </span>
            )}
            <span
              className={classNames(
                'text text-step flex-wrap',
                styles.text,
                currentStep >= step?.id && '!text-[#2fb138]'
              )}
            >
              {step?.title || ''}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Step;
