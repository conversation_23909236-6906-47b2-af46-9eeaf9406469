.steps {
  &.mobile {
    @apply absolute top-[calc(100%_-_2px)] left-0 right-0 bg-[#111] justify-between px-5 pb-4 pt-1 flex w-full;
    & > div {
      @apply justify-between md:mx-auto lg:w-1/2 md:w-2/3;
    }
    .step {
      &:first-child {
        &::after {
          @apply md:left-[calc(100%_-_98px)] left-[calc(100%)] top-[22%];
        }
      }
      &:nth-child(2) {
        &::after {
          @apply md:left-[36%] lg:left-[45%] left-[calc(100%_-_65px)] top-[22%] z-0;
        }
      }
      &:last-child {
        &::after {
          @apply hidden;
        }
      }
    }
  }
  .step {
    @apply flex items-center flex-col justify-center flex-nowrap w-auto;
    &:not(:last-child) {
      @apply md:pr-[111px] mr-2 md:mr-4;
    }
    &:last-child {
      &::after {
        @apply opacity-0;
        visibility: hidden;
      }
    }
    &::after {
      @apply md:w-[183px] w-[122px] top-[28%] bg-[#DEDEDE] h-[1px] md:h-0.5 rounded-lg z-10;
    }

    &:first-child {
      &::after {
        @apply md:left-[calc(100%_-_98px)] left-[calc(100%_-_27px)];
      }
    }

    &.current {
      span:first-child {
        @apply border-[#3ac882] relative;
        @apply bg-white #{!important};
        &::before {
          @apply absolute content-[""] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 md:w-3 md:h-3 w-1.5 h-1.5 rounded-full bg-[#3ac882];
        }
      }
    }
    &.completed {
      &::after {
        @apply border-[#1E7124] bg-[#2FB138] rounded-full #{!important};
      }
      span:first-child {
        @apply border-[#1E7124] bg-[#2FB138] rounded-full #{!important};
        span {
          @apply md:h-[26px] h-[14px];
          i {
            @apply md:text-sm;
          }
        }
      }
    }
  }
  .counter {
    @apply relative translate-x-0 translate-y-0 top-[unset] md:w-8 md:h-8 w-4 h-4 md:text-xs text-[10px];
    @apply border-[#DEDEDE] bg-white border-[2px] md:border-[3px] font-medium text-[#9B9B9B];
  }
  .text {
    @apply pl-0 md:pt-1.5 text-[#DEDEDE] font-medium;
    @apply md:text-xs text-[10px] #{!important};
  }
  &.rentedContent {
    .step {
      @apply md:after:w-[157px];
      &:first-child {
        @apply md:pr-[94px];
        @media screen and (max-width: 767px) {
          @apply after:left-[calc(100%_-_10px)];
        }
      }
      &:nth-child(2) {
        @apply md:after:w-[154px];
      }
    }
  }
}
