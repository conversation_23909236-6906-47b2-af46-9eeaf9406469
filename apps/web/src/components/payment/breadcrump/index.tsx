import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import classNames from 'classnames';
import { useDispatch, useSelector } from 'react-redux';
import TrackingPayment from '@tracking/functions/payment';
import { openPopup } from '@actions/popup';
import Button from '@components/basic/Buttons/Button';
import { TEXT } from '@constants/text';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import { PAGE, POPUP } from '@constants/constants';

function PaymentBreadcrumb({ isInAppZalo }: any) {
  const dispatch = useDispatch();
  const router = useRouter();
  const classIcon = 'vie-chevron-left-r-medium';
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const [title, setTitle] = useState<any>('');
  const trackingPayment = new TrackingPayment();

  const handleBack = () => {
    const previousUrlValue = ConfigLocalStorage.get('PREVIOUS_URL');
    let previousPath =
      typeof previousUrlValue === 'string' ? previousUrlValue.replace(/["]+/g, '') : '/';
    const currentDomain = window.location.origin;
    const streamId = ConfigLocalStorage.get('STREAM_ID');
    const livetvDetailValue = ConfigLocalStorage.get('livetvDetail');
    const contentDetail = JSON.parse(
      typeof livetvDetailValue === 'string' ? livetvDetailValue : '{}'
    );
    const contentId = ConfigLocalStorage.get('currentContentId');

    // remove all the slug to prevent get relate data and hide the trigger footer
    if (streamId) {
      ConfigLocalStorage.remove('STREAM_ID');
    }
    if (contentDetail) {
      ConfigLocalStorage.remove('livetvDetail');
    }
    if (contentId) {
      ConfigLocalStorage.remove('currentContentId');
    }
    if (previousPath && previousPath.includes(currentDomain)) {
      previousPath = previousPath.replace(currentDomain, '');
    }

    if (title === TEXT.BACK) {
      if (router.pathname.includes(PAGE.PAYMENT) && profile?.id) {
        dispatch(
          openPopup({
            name: POPUP.NAME.SURVEY_PAYMENT
          })
        );
        return;
      }
      trackingPayment.userBackButtonSelected();
    }
    if (router.query?.pkg) {
      trackingPayment.userSeePackageList();
      ConfigLocalStorage.remove('FROM_URL');
      if (isInAppZalo) {
        router.push('/in-app/zalopay?curPage=home');
      } else {
        router.push('/mua-goi?curPage=home');
      }
    } else if (previousPath !== PAGE.HOME && previousPath) {
      const isVODPath = previousPath?.includes('.html') ? PAGE.VOD : '';
      if (!isVODPath) {
        router.push(previousPath);
        return;
      }
      router.push(PAGE.VOD, previousPath);
      ConfigLocalStorage.remove('PREVIOUS_URL');
    } else {
      router.push(PAGE.HOME);
    }
  };

  useEffect(() => {
    if (router.query?.pkg) {
      setTitle(TEXT.VIEW_PACKAGES_LIST);
    }
    if (
      router.query?.curPage?.includes(PAGE.HOME) ||
      (router.pathname.includes(PAGE.PAYMENT) && !router.query?.pkg)
    ) {
      setTitle(TEXT.BACK);
    }
  }, [router]);

  return (
    <article className="flex items-center canal-v py-4">
      <div className={classNames('w-full !max-w-[94rem] mx-auto')}>
        <Button
          className="flex items-center text-vo-green !font-Roboto font-bold space-x-2"
          iconClass="!text-[1rem]"
          iconName={classIcon}
          textClass="!text-[14px] lg:!text-[18px]"
          title={title}
          onClick={handleBack}
        />
      </div>
    </article>
  );
}

export default React.memo(PaymentBreadcrumb);
