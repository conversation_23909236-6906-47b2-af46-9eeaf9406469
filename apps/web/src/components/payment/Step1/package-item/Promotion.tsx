import React, { useMemo } from 'react';
import isEmpty from 'lodash/isEmpty';
import { useSelector } from 'react-redux';
import BannerPaymentCake from '@components/payment/Step2/BannerPaymentCake';
import style from './Promotion.module.scss';
import { PLATFORM } from '@/constants/constants';
import classNames from 'classnames';
import { TEXT } from '@constants/text';

const ItemInfoPromotion = ({ data, className }: any) => (
  <div className={classNames(style.itemPromotion, className ? className : 'h-16')}>
    <div className={style.icon}>
      <div className="flex justify-center items-center w-12 min-w-12 h-12">
        <img src={data?.icon} alt="promotion" />
      </div>
    </div>
    <div className={style.titleItem}>
      <div
        className="px-3 py-2 !text-xs"
        dangerouslySetInnerHTML={{ __html: data?.description }}
        onClick={(e) => e.stopPropagation()}
      />
    </div>
    <div className={style.cornerBefore} />
    <div className={style.cornerAfter} />
  </div>
);

const Promotion = ({ data, id, classNameItem }: any) => {
  const { dataCampaign } = useSelector((state: any) => state?.Payment);
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const filteredCampaigns = useMemo(() => {
    if (isEmpty(dataCampaign)) return [];
    return dataCampaign.filter(({ name, packageGroupIds }: any) => {
      if (name === PLATFORM.WEB || name === PLATFORM.MOBILE_WEB)
        return !packageGroupIds?.includes(id);
      return true;
    });
  }, [dataCampaign, id, isMobile]);

  const dataPromotion = useMemo(() => {
    if (isEmpty(filteredCampaigns) || isEmpty(data?.items)) return data?.items;

    return filteredCampaigns.length === 1
      ? data.items.slice(0, 4)
      : filteredCampaigns.length === 2
      ? data.items.slice(0, 2)
      : [];
  }, [filteredCampaigns, data?.items]);

  const dataGroupsPromotion = useMemo(
    () => (isEmpty(dataPromotion) ? [] : [dataPromotion]),
    [dataPromotion]
  );
  if (isEmpty(dataGroupsPromotion) && isEmpty(filteredCampaigns)) return null;

  return (
    <div className="w-[100vw] lg:w-full pt-[16px] px-4 md:px-0 -mx-4 md:mx-0 space-y-2">
      <div className="flex-box lg:hidden align-middle">
        <h2 className={style.titlePromotion}>{TEXT.TITLE_PROMOTION}</h2>
      </div>
      <div className="flex flex-nowrap xmd:flex-none xmd:grid xmd:grid-cols-3 xl:grid-cols-4 w-auto md:w-full gap-2 snap-x overflow-x-auto md:overscroll-x-none scrollbar scrollbar-thumb-transparent scrollbar-track-transparent">
        {!isEmpty(filteredCampaigns) && (
          <div className="w-[calc(100%-1.5rem)] md:w-[calc(50%-.75rem)] xmd:w-auto flex-none snap-center">
            {filteredCampaigns.map((it: any, index: any) => (
              <BannerPaymentCake key={`${index}`} data={it} />
            ))}
          </div>
        )}
        {dataGroupsPromotion?.map((item: any) =>
          item?.map((it: any, i: any) => (
            <div
              className="w-[calc(100%-1.5rem)] md:w-[calc(50%-.75rem)] xmd:w-auto flex-none snap-center"
              key={i}
            >
              <ItemInfoPromotion className={classNameItem} data={it} />
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default Promotion;
