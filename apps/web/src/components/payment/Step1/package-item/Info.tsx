import React from 'react';
import classNames from 'classnames';
import { useSelector } from 'react-redux';
import ConfigImage from '@config/ConfigImage';
import Image from '@components/basic/Image/Image';
import styles from '../Style.module.scss';

const Info = ({ packageConfig, packages }: any) => {
  const { isMobile } = useSelector((state: any) => state?.App || {});
  if (!packageConfig || !packageConfig?.benefits) {
    return null;
  }

  return (
    <div className={styles.info}>
      {packageConfig?.benefits.map((benefit: any, index: any) => {
        const value = (
          <div
            className={classNames(
              'flex items-center space-x-1 w-full',
              packages?.length === 1 && !isMobile && 'w-full 2xl:px-6 px-4',
              isMobile && 'w-full',
              !benefit?.isEnabled ? 'opacity-20' : ''
            )}
          >
            <div
              className={classNames(
                'flex items-start space-x-1 md:space-x-2 w-full text-[#171717]'
              )}
            >
              <div className={classNames('flex items-center justify-center w-5 h-auto py-[1px]')}>
                <Image src={benefit?.icon || ConfigImage.iconBenefit} alt="benefit" notWebp />
              </div>
              <div className={styles.infoBenefit}>{benefit?.title}</div>
            </div>
          </div>
        );

        return (
          <div key={`${benefit.id}benefit-${index}`} className={classNames(styles.value)}>
            {value}
          </div>
        );
      })}
    </div>
  );
};

export default Info;
