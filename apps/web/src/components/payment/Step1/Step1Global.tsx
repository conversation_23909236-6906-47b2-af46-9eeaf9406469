import React, { useEffect, useMemo, useState } from 'react';
import Image from '@components/basic/Image/Image';
import { useDispatch, useSelector } from 'react-redux';
import Button from '@components/basic/Buttons/Button';
import { TEXT } from '@constants/text';
import { selectPackage } from '@actions/payment';
import { UtmParams } from '@models/subModels';
import { useCountdown, useVieRouter } from '@customHook';
import { LOGIN_METHOD, PAGE, PAYMENT_METHOD } from '@constants/constants';
import QRCode from 'qrcode';
import ConfigImage from '@config/ConfigImage';
import { encodeParamDestination, toPascalCase } from '@helpers/common';
import UserApi from '@apis/userApi';
import PaymentApi from '@apis/Payment';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import LocalStorage from '@config/LocalStorage';
import TrackingPayment from '@tracking/functions/payment';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import { getProfile } from '@actions/profile';
import style from './Style.module.scss';

declare const window: any;

const trackingPayment = new TrackingPayment();
const QrTime = ({ onTimeUp, onEach5s }: any) => {
  const qrCountDown = useCountdown({ time: 900 });
  useEffect(() => {
    if (qrCountDown <= 0) {
      onTimeUp();
    } else if (qrCountDown < 900 && qrCountDown % 5 === 0) {
      onEach5s();
    }
  }, [qrCountDown]);
  return (
    <p className="text-sm font-medium mt-3 text-center">{`Thời gian quét mã thanh toán còn ${qrCountDown} giây`}</p>
  );
};

const Step1Global = ({ alreadyHave }: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const { query } = router || {};
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile);
  const { isMobile, deviceModel, deviceName, deviceType } = useSelector(
    (state: any) => state?.App || {}
  );
  const { packages, paymentDeepLink, listMethodsConfig, listPackagesConfig } =
    useSelector((state: any) => state?.Payment) || {};
  const [userAccount, setUserAccount] = useState<any>('');
  const [qrcodeExpired, setQrcodeExpire] = useState(false);
  const [qrcode, setQrCode] = useState<any>('');
  const [alreadyBuyViaQr, setAlreadyBuyViaQr] = useState(false);
  const packageItem = packages?.[0];

  const returnUrl = ConfigLocalStorage.get(LocalStorage.FROM_URL);
  const packageConfig = useMemo(
    () => (listPackagesConfig || []).find((item: any) => item?.id === packageItem?.id) || {},
    [listPackagesConfig, packageItem]
  );
  const methodData = useMemo(
    () =>
      (listMethodsConfig || []).filter(
        (me: any) => (packageConfig?.methods || []).indexOf(me?.id) >= 0
      ),
    [listMethodsConfig, packageConfig]
  );
  const isOnlyQrCode =
    methodData?.[0]?.id === PAYMENT_METHOD.QRCODE && (methodData || [])?.length === 1;

  useEffect(() => {
    if (!profile?.id && isOnlyQrCode) {
      const remakeDestination = encodeParamDestination(router?.asPath);
      router.push(
        `${PAGE.AUTH}/?trigger=${TYPE_TRIGGER_AUTH.PAYMENT}&destination=${remakeDestination}&page=${router?.pathname}`
      );
    }
  }, [isOnlyQrCode, profile]);

  useEffect(() => {
    if (!isMobile && isOnlyQrCode && paymentDeepLink && profile?.id) {
      createQrCode(`${paymentDeepLink}${profile?.id}`);
    }
  }, [isMobile, isOnlyQrCode, paymentDeepLink, profile]);

  useEffect(() => {
    if (isOnlyQrCode && !isMobile && !alreadyHave && !alreadyBuyViaQr) {
      trackingPayment.globalShowQrCodePayment({ currentPage: query?.curPage });
    }
  }, [isMobile, isOnlyQrCode]);

  useEffect(() => {
    if (profile?.id) {
      UserApi.getLoginMethod().then((res) => {
        const loginMethod = res?.data?.result?.login_method;
        const accountInfo =
          loginMethod === LOGIN_METHOD.MOBILE
            ? res?.data?.result?.mobile || ''
            : loginMethod === LOGIN_METHOD.EMAIL
            ? res?.data?.result?.email || ''
            : loginMethod === LOGIN_METHOD.FACEBOOK ||
              loginMethod === LOGIN_METHOD.GOOGLE ||
              loginMethod === LOGIN_METHOD.APPLE
            ? toPascalCase(loginMethod || '')
            : '';
        setUserAccount(accountInfo);
      });
    }
  }, [isOnlyQrCode, profile?.id]);

  const createQrCode = async (text: any) => {
    try {
      const qrCodeSrc = await QRCode.toDataURL(text);
      setQrCode(qrCodeSrc);
    } catch (err) {
      setQrCode(ConfigImage.smsQrCodeDefault);
    }
  };

  const handleCheckBuyViaQr = async () => {
    const firstTerm = packageItem?.terms?.[0];
    const permission2 = await PaymentApi.checkBillingPermission2({
      termId: firstTerm?.id
    });
    const { isCallback, buyPermission, success } = permission2 || {};
    if (!isCallback && !buyPermission && success) {
      setAlreadyBuyViaQr(true);
      dispatch(
        getProfile({
          deviceModel,
          deviceName,
          deviceType
        })
      );
    }
  };

  const handleSelectPackage = () => {
    dispatch(selectPackage(packageItem));
    // trackingPayment.selectPackage({ selectedPackage: packageItem });
    if (packageItem?.id) {
      // trackingPayment.paymentPageLoaded();
      const urmParams = UtmParams(query);
      router.push({ pathname: PAGE.PAYMENT_METHOD, query: { ...urmParams, pkg: packageItem?.id } });
    }
  };

  const onSelectPackage = () => {
    if (isOnlyQrCode && isMobile) {
      window.location.href = `${paymentDeepLink}${profile?.id}`;
    } else {
      handleSelectPackage();
    }
  };

  const onEach5s = () => {
    handleCheckBuyViaQr();
  };

  const onTimeUp = () => {
    setQrcodeExpire(true);
  };

  const onCreateQrcode = () => {
    setQrcodeExpire(false);
  };

  const onBackHome = () => {
    if (profile && !currentProfile?.id) {
      router.push(PAGE.LOBBY_PROFILES);
    } else {
      router.push(PAGE.HOME);
    }
  };

  const onContinueWatching = () => {
    if (profile && !currentProfile?.id) {
      router.push(`${PAGE.LOBBY_PROFILES}/?destination=${returnUrl}`);
    } else {
      window.location = returnUrl;
    }
  };

  const renderQrCode = () => (
    <>
      <p className="text-lg font-medium text-center">
        {qrcodeExpired ? TEXT.PAYMENT_QRCODE_EXPIRED : TEXT.PAYMENT_SCAN_QRCODE}
      </p>
      {!qrcodeExpired && (
        <div className={`flex py-3 ${style.note} mt-3 space-x-1 p-3`}>
          <p className="text-sm font-medium text-center">
            <i className={`vie font-medium vie-info-o-c-script ${style.iconInfo} mr-2`} />
            Lưu ý: Đăng nhập tài khoản <strong>{userAccount}</strong> trên Ứng dụng VieON để việc
            thanh toán không bị gián đoạn
          </p>
        </div>
      )}
      {qrcode && !qrcodeExpired && (
        <div className="relative">
          <Image className={`mx-auto w-[180px] mt-6 ${style.qrcode}`} notWebp src={qrcode} />
          <Image
            className="absolute bottom-[66px] left-[66px]"
            notWebp
            src={ConfigImage.logoRounded}
          />
        </div>
      )}
      {qrcode && !qrcodeExpired && <QrTime onTimeUp={onTimeUp} onEach5s={onEach5s} />}
      {qrcodeExpired && (
        <div className="space-y-3 mt-9 w-full">
          <Button
            className="button button--green button--large w-full"
            title={TEXT.CREATE_QRCODE}
            onClick={onCreateQrcode}
          />
          <Button className={style.buttonBackHome} title={TEXT.BACK_HOME} onClick={onBackHome} />
        </div>
      )}
    </>
  );

  const renderAlreadyHave = () => (
    <>
      <p className="text-center font-bold text-base mt-10 md:mt-0">
        Bạn đã đăng ký gói tự động gia hạn hàng tháng. Bây giờ bạn có thể trải nghiệm tất cả nội
        dung trên VieON
      </p>
      <Button
        className="button button--green button--large mt-3 md:mt-9 w-full !text-white"
        title={returnUrl ? TEXT.CONTINUE_WATCH_CONTENT : TEXT.BACK_HOME}
        onClick={returnUrl ? onContinueWatching : onBackHome}
      />
    </>
  );

  if (!profile?.id || !isOnlyQrCode) return null;

  return (
    <section>
      <div className="px-4 max-w-[1140px] py-6 md:py-32 mx-auto">
        <div className="md:flex space-x-6 justify-center">
          <div className="basis-1/2">
            <Image className="w-[594px] h-[334px]" notWebp src={packageItem?.bannerUpper} />
            {!isOnlyQrCode && (
              <Button
                className="button button--green button--large mt-3 w-full !text-white hover:!text-vo-green"
                title={TEXT.SELECT}
                onClick={onSelectPackage}
              />
            )}

            {isOnlyQrCode && isMobile && !alreadyHave && (
              <>
                <p className="text-center font-bold text-base mt-10">
                  {TEXT.PAYMENT_ON_VieON_APP_DESC}
                </p>
                <Button
                  className="button button--green button--large mt-3 w-full !text-white hover:!text-vo-green"
                  title={TEXT.PAYMENT_ON_VieON_APP}
                  onClick={onSelectPackage}
                />
              </>
            )}

            {alreadyHave && isMobile && renderAlreadyHave()}
          </div>

          {!isMobile && (
            <div className="basis-1/2 flex flex-col justify-center items-center">
              {isOnlyQrCode && !alreadyHave && !alreadyBuyViaQr && renderQrCode()}
              {(alreadyBuyViaQr || alreadyHave) && renderAlreadyHave()}
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default Step1Global;
