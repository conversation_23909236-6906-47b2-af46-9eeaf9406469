import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { useMediaQuery } from 'react-responsive';
import TrackingPayment from '@tracking/functions/payment';
import { selectPackage } from '@actions/payment';
import classNames from 'classnames';
import Package from './package-item';
import IconArrowLeft from '@/components/Icons/IconArrowLeft';
import { BREAKPOINT } from '@/constants/constants';
import isEmpty from 'lodash/isEmpty';
import styles from './Style.module.scss';

const trackingPayment = new TrackingPayment();

const PackageList = ({ setFocusPackage }: any) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const swiperRef = useRef<any>(null);
  const dispatch = useDispatch();
  const { packages, listPackagesConfig } = useSelector((state: any) => state?.Payment) || {};
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const isLargeScreen = useMediaQuery({ minWidth: BREAKPOINT.LG });
  const isExtraLargeScreen = useMediaQuery({ minWidth: BREAKPOINT.XL });
  const isExtraExtraLargeScreen = useMediaQuery({ minWidth: BREAKPOINT.XXL });
  const [hoveredPackageId, setHoveredPackageId] = useState<any>(null);

  // TODO: delete this useEffect
  useEffect(() => {
    setFocusPackage(hoveredPackageId);
  }, [hoveredPackageId, setFocusPackage]);

  const slidePerViewByBreakPoints = useMemo(() => {
    if (isExtraExtraLargeScreen) return 4;
    if (isExtraLargeScreen) return 4.25;
    if (isLargeScreen) return 3.5;
    return 2;
  }, [isExtraExtraLargeScreen, isExtraLargeScreen, isLargeScreen]);

  const packagesLength = useMemo(() => {
    if (packages?.length === 1) return 1;
    if (isMobile) return 1.1;
    if (packages?.length < 4 || packages?.length > slidePerViewByBreakPoints) {
      return slidePerViewByBreakPoints;
    }
    return packages?.length;
  }, [packages, isMobile, slidePerViewByBreakPoints]);

  const handleMouseEnter = () => {
    if (packages?.length > slidePerViewByBreakPoints) setIsHovered(true);
  };

  const handleMouseLeave = () => {
    if (packages?.length > slidePerViewByBreakPoints) setIsHovered(false);
  };

  const handleClickNavigate = (typeBtn: 'next' | 'prev') => {
    if (!swiperRef.current?.swiper) return;

    const swiper = swiperRef.current.swiper;
    if (typeBtn === 'next') {
      // Move to the next slide without updating currentIndex
      swiper.slideNext();
    } else if (typeBtn === 'prev') {
      // Move to the previous slide without updating currentIndex
      swiper.slidePrev();
    }
  };

  const handleSlideChange = (swiper: any) => {
    setCurrentIndex(swiper.activeIndex);
    if (isMobile) {
      const selectedPackage = packages[swiper.activeIndex];
      handlePackageSelect(selectedPackage);
    }
  };

  const handlePackageSelect = (selectedPackage: any) => {
    dispatch(selectPackage(selectedPackage));
    trackingPayment.selectPackage({ selectedPackage });
    trackingPayment.paymentPageLoaded();
  };

  const defaultPackage = useMemo(() => {
    const packageWithTag = packages?.find((item: any) =>
      listPackagesConfig.find((config: any) => config.id === item.id)
    );
    return packageWithTag || packages?.[0];
  }, [packages, listPackagesConfig]);

  const getPackageConfig = (id: any) =>
    (listPackagesConfig || []).find((cItem: any) => cItem?.id === id) || {};

  useEffect(() => {
    if (isMobile) {
      swiperRef.current?.swiper?.disable();
    }
  }, [isMobile]);

  if (!packages?.length) return null;

  return (
    <div
      className={styles.packages}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <Swiper
        id="packagesSwiper"
        onSwiper={(swiper) => {
          swiperRef.current = { swiper };
        }}
        centeredSlides={!!isMobile}
        centeredSlidesBounds={!!isMobile}
        className={classNames(
          'w-full relative pb-4 pt-1 md:p-0 lg:mx-auto max-w-[1500px]',
          packages?.length < 4 && !isMobile && styles.swiperCentered,
          isMobile ? styles.swiperVerticalScroll : ''
        )}
        slidesPerView={isMobile ? 'auto' : packagesLength}
        slidesPerGroup={1}
        // allowTouchMove={false}
        navigation={
          !isMobile &&
          packages?.length > slidePerViewByBreakPoints && {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev'
          }
        }
        onSlideChange={handleSlideChange}
      >
        {!isEmpty(packages) &&
          (packages || []).map((item: any) => {
            const packageConfig = getPackageConfig(item?.id) || {};
            const setWidthSlide = isMobile
              ? 'w-full'
              : packageConfig?.tag
              ? 'w-[calc(93.9854vw/4)]'
              : 'w-[calc(93.9854vw/4-2.25rem)]';
            return (
              <SwiperSlide
                className={classNames(
                  'h-auto md:px-2 md:first:pl-0 md:last:pr-0 md:pt-3 md:pb-6',
                  setWidthSlide
                )}
                key={item?.id}
              >
                <Package
                  data={item}
                  defaultPackage={defaultPackage}
                  hoveredPackageId={hoveredPackageId}
                  onHover={setHoveredPackageId}
                  packageConfig={packageConfig}
                />
              </SwiperSlide>
            );
          })}
      </Swiper>
      {!isMobile && packages?.length > slidePerViewByBreakPoints && (
        <>
          <div
            onClick={() => handleClickNavigate('prev')}
            className={classNames(
              'swiper-button-prev top-1/2',
              styles.swiperPrevButton,
              isHovered ? styles.showButton : styles.hideButton
            )}
            style={{ display: currentIndex === 0 ? 'none' : 'flex' }}
          >
            <div className="w-9 h-9 bg-black !text-white rounded-full flex items-center justify-center">
              <IconArrowLeft size={26} />
            </div>
          </div>
          <div
            onClick={() => handleClickNavigate('next')}
            className={classNames(
              'swiper-button-next top-1/2',
              styles.swiperNextButton,
              isHovered ? styles.showButton : styles.hideButton
            )}
            style={{ display: swiperRef.current?.swiper?.isEnd ? 'none' : 'flex' }}
          >
            <div className="w-9 h-9 bg-black !text-white rounded-full flex items-center justify-center rotate-180">
              <IconArrowLeft size={26} />
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default PackageList;
