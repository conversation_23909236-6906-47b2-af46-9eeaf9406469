import React from 'react';
import classNames from 'classnames';
import Button from '@components/basic/Buttons/Button';
import { TEXT } from '@constants/text';
import styles from './Style.module.scss';

const ButtonSelectPackageMobile = ({ onGoToStep2, selectedPackage }: any) => (
  <div className={styles.buttonSelectPackageMobile}>
    <Button
      className={classNames(styles.button, 'text-bold')}
      title={`${TEXT.SELECT_PACKAGE} ${selectedPackage?.name}`}
      onClick={onGoToStep2}
    >
      {`${TEXT.SELECT_PACKAGE} ${selectedPackage?.name}`}
    </Button>
  </div>
);

export default React.memo(ButtonSelectPackageMobile);
