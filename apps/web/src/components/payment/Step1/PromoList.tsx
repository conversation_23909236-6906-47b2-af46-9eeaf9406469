import Promotion from '@components/payment/Step1/package-item/Promotion';
import { PAYMENT_METHOD, PLATFORM } from '@constants/constants';
import { useVieRouter } from '@customHook';
import { TEXT } from '@constants/text';
import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { AnimatePresence, motion } from 'framer-motion';
import styles from './package-item/Promotion.module.scss';
import isEmpty from 'lodash/isEmpty';

const PromoList = ({ focusPackage, classNameItem }: any) => {
  const router = useVieRouter();
  const { pathname } = router || {};
  const { listPackagesConfig, dataCampaign } = useSelector((state: any) => state?.Payment) || {};
  const inAppFromPath = pathname.includes('in-app') ? PAYMENT_METHOD.ZALO_PAY : '';

  const activePackageId = focusPackage ?? listPackagesConfig?.[0]?.id;

  const promotions = useMemo(() => {
    const packageConfig = listPackagesConfig?.find((item: any) => item.id === activePackageId);
    if (!packageConfig) return [];

    const promotionData = inAppFromPath
      ? packageConfig?.promotionProgram?.[inAppFromPath]
      : packageConfig?.promotionProgram || [];

    return [{ id: packageConfig.id, promotionData }];
  }, [activePackageId, listPackagesConfig, inAppFromPath]);

  const filteredCampaigns = useMemo(() => {
    if (isEmpty(dataCampaign)) return [];
    return dataCampaign.filter(({ name, packageGroupIds }: any) => {
      if (name === PLATFORM.WEB || name === PLATFORM.MOBILE_WEB)
        return !packageGroupIds?.includes(activePackageId);
      return true;
    });
  }, [dataCampaign, activePackageId]);

  if (promotions[0]?.id === focusPackage && !promotions[0]?.promotionData) {
    return null;
  }

  return (
    <article className="relative overflow-hidden">
      {!isEmpty(filteredCampaigns) && (
        <div className="flex-box md:flex align-middle">
          <h2 className={styles.titlePromotion}>{TEXT.TITLE_PROMOTION}</h2>
        </div>
      )}
      <AnimatePresence mode="wait">
        {promotions.map(({ id, promotionData }) => (
          <motion.div
            key={id}
            className="mb-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.25, ease: 'easeInOut', delay: 0.1 }}
          >
            <Promotion data={promotionData} classNameItem={classNameItem} id={id} />
          </motion.div>
        ))}
      </AnimatePresence>
    </article>
  );
};

export default PromoList;
