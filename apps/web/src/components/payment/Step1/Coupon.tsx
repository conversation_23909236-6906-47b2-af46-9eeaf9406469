import React, { useEffect, useMemo, useRef, useState } from 'react';
import styles from './Style.module.scss';
import Input from '@/components/basic/Input/Input';
import Button from '@/components/basic/Buttons/Button';
import classNames from 'classnames';
import { useDispatch, useSelector } from 'react-redux';
import { TEXT } from '@constants/text';
import UserApi from '@apis/userApi';
import { getProfile } from '@actions/profile';
import { PAGE } from '@constants/constants';
import { createTimeout, encodeParamDestination } from '@helpers/common';
import { updateStatusRedeemCode } from '@actions/user';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import { useVieRouter } from '@/customHook';
import { NAME, PROPERTY, VALUE } from '@config/ConfigSegment';
import { segmentEvent } from '@tracking/TrackingSegment';
import ConfigLocalStorage from '@config/ConfigLocalStorage';

const Coupon = () => {
  const [voucherCode, setVoucherCode] = useState<any>('');
  const [voucherData, setVoucherData] = useState<any>(null);
  const [isRetry, setIsRetry] = useState(false);
  const inputRef = useRef<any>(null);
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const { deviceModel, deviceName, deviceType } = useSelector((state: any) => state?.App);

  const router = useVieRouter();
  const { pathname } = router || {};
  const dispatch = useDispatch();
  const inApp = useMemo(() => (pathname || '').includes(PAGE.ZALOPAY), [pathname]);

  const onChangeCode = (event: any) => {
    const value = ((event?.target?.value || '').trim() || '').toUpperCase();
    if (voucherData?.message) {
      handleRetry();
    }
    setVoucherCode(value);
  };

  useEffect(() => {
    const voucherCode: any = ConfigLocalStorage.get('voucherCode') || '';
    setVoucherCode(voucherCode);
    if (voucherCode) {
      ConfigLocalStorage.remove('voucherCode');
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }
  }, []);

  const onActiveVoucher = async () => {
    if (!profile?.id) {
      const remakeDestination = encodeParamDestination(router?.asPath);
      ConfigLocalStorage.set('voucherCode', voucherCode);
      router.push(
        `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${TYPE_TRIGGER_AUTH.INPUT_VOUCHER}`
      );
      return;
    }

    try {
      const dataResponse = await UserApi.checkVoucherExist(voucherCode);

      if (!dataResponse?.success) {
        setVoucherData({ error: 400, message: dataResponse?.data?.message });
        setIsRetry(true);
        handleTrackingVoucherCodeInputted(dataResponse?.data?.message, VALUE.FAIL);
        return;
      }

      const resVoucherData = await UserApi.activeVoucher(voucherCode);
      setVoucherData(resVoucherData?.data);

      if (resVoucherData?.success) {
        dispatch(updateStatusRedeemCode({ userId: profile?.id, voucherCode }));
        const voucherData = {
          name: dataResponse?.data?.name,
          price: dataResponse?.data?.price,
          groupName: dataResponse?.data?.groupName,
          expiredDate: dataResponse?.data?.expiredDate,
          startDate: dataResponse?.data?.startDate
        };
        handleTrackingVoucherCodeInputted('', VALUE.SUCCESS);
        createTimeout(() => {
          router.push({
            pathname: PAGE.VOUCHER,
            query: { from: 'payment', data: JSON.stringify(voucherData), type: 'coupon_vieon' }
          });
          dispatch(getProfile({ deviceModel, deviceName, deviceType }));
        }, 1000);
      }
    } catch (error) {
      setVoucherData({ error: 500, message: 'Có lỗi xảy ra. Vui lòng thử lại!' });
      setIsRetry(true);
    }
  };

  const handleRetry = () => {
    setVoucherCode('');
    setVoucherData(null);
    setIsRetry(false);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleTrackingVoucherCodeInputted = (message: any, result: any) => {
    segmentEvent(NAME.VOUCHER_CODE_INPUTTED, {
      [PROPERTY.CURRENT_PAGE]: window.location.href,
      [PROPERTY.VOUCHER_CODE]: voucherCode || '',
      [PROPERTY.CAUSE_FOR_FAILURE]: message || '',
      [PROPERTY.RESULT]: result,
      [PROPERTY.VOUCHER_TYPE]: 'gift code'
    });
  };

  useEffect(() => {
    if (isRetry && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isRetry]);

  return (
    <div className={styles.coupon}>
      <div className={styles.textDesc}>
        *Mã VieON <div>Chỉ dành cho khách hàng có mã ưu đãi tặng gói</div>
        <div className="text-[#646464] !text-xs pt-1.5">
          Bằng việc đăng nhập và xác nhận Mã VieON, bạn xác nhận đã đọc
          <a
            className="link px-1 font-bold"
            href={inApp ? PAGE.ZALOPAY_REGULATION : PAGE.REGULATION}
            target="_blank"
            rel="noreferrer"
          >
            {TEXT.CONTRACT_POLICY}
          </a>
          của VieON.
        </div>
      </div>
      <div className={classNames(styles.form, { [styles.error as any]: voucherData?.message })}>
        <Input
          placeholder="Nhập mã"
          inputClass={styles.input}
          handleOnChange={onChangeCode}
          valInput={voucherCode}
          id="voucherCodePayment"
          ref={inputRef}
        />

        {isRetry ? (
          <Button
            title={TEXT.RE_TYPE}
            subTitle={TEXT.RE_TYPE}
            className={styles.button}
            onClick={handleRetry}
          />
        ) : (
          <Button
            title="Sử dụng"
            subTitle="Sử dụng"
            className={styles.button}
            onClick={onActiveVoucher}
            disabled={!voucherCode}
          />
        )}
        {voucherData?.message && <div className={styles.message}>{voucherData?.message}</div>}
      </div>
    </div>
  );
};

export default Coupon;
