import React from 'react';
import { useSelector } from 'react-redux';
import { isMobile } from 'react-device-detect';
import Tooltip from '@components/Tooltip';
import { EL_CLASS } from '@constants/constants';
import styles from './Style.module.scss';

const Benefits = () => {
  const { benefits, packages } = useSelector((state: any) => state?.Payment) || {};

  if (packages && packages?.length === 1) {
    return null;
  }
  if (!isMobile) {
    return (
      <div className={styles.benefits}>
        {benefits?.map((item: any) => (
          <div key={item?.id} className={styles.item}>
            {item?.title && <span className="truncate">{item?.title}</span>}
            {item.tooltip && (
              <Tooltip
                key={item.id}
                title={item.tooltip}
                placement="top"
                triggerEvent={isMobile ? 'dismiss' : 'hover'}
                className="animate-fade-in text-start p-2 max-w-[224px] min-w-[43px]"
                size={EL_CLASS.SMALL}
                arrowPosition="top-end"
                sizeX={12}
              >
                <span className="ml-1 icon icon--tiny">
                  <i className="vie vie-info-o-c-script !text-[17px]" />
                </span>
              </Tooltip>
            )}
          </div>
        ))}
      </div>
    );
  }
  return null;
};

export default React.memo(Benefits);
