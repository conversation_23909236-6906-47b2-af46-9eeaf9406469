import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import Privilege from '@components/payment/Step1/Privilege';
import SeoText from '../../seo/SeoText';
import PackageList from './PackageList';
import PromoList from './PromoList';
import Coupon from './Coupon';

const Step1 = () => {
  const { sliderBenefitVip, listPackagesConfig } =
    useSelector((state: any) => state?.Payment) || {};
  const dataSEOAllPage = useSelector((state: any) => state?.Page?.dataSEOAllPage || {});

  const [focusPackage, setFocusPackage] = useState<any>(null);

  useEffect(() => {
    if (listPackagesConfig?.length) {
      setFocusPackage(listPackagesConfig[0]?.id);
    }
  }, [listPackagesConfig]);

  return (
    <section className="section !pb-8 md:!pb-16 !mb-0 canal-v">
      <SeoText seo={dataSEOAllPage?.seo} />
      <div className="flex flex-col mx-auto !max-w-[94rem] pb-6">
        {sliderBenefitVip && <Privilege data={sliderBenefitVip} />}
        <div className="flex flex-col gap-2 md:gap-5 mt-2">
          <div className="flex w-full items-end">
            <PackageList setFocusPackage={setFocusPackage} />
          </div>
        </div>
        <div className="md:mt-2 hidden lg:block">
          <PromoList focusPackage={focusPackage} />
        </div>
        <Coupon />
      </div>
    </section>
  );
};

export default Step1;
