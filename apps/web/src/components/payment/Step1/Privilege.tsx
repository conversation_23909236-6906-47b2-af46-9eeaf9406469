import React from 'react';
import ConfigImage from '@config/ConfigImage';
import Image from '@components/basic/Image/Image';
import isArray from 'lodash/isArray';
import styles from './Style.module.scss';

const Privilege = ({ data }: any) => {
  return (
    <div className={styles.privilege}>
      <div className={styles.privilegeContainer}>
        {data?.title && <h2 className={styles.privilegeTitle}>{data?.title}</h2>}
        {isArray(data?.items) && (
          <div className="flex md:flex-auto space-x-2 md:space-x-5 whitespace-nowrap w-full md:w-auto md:mx-auto hide-scrollbar">
            {(data?.items || []).map((item: any, i: any) => (
              <div className="flex flex-shrink-0 items-center gap-1" key={i}>
                <Image
                  className="w-[16px] h-[16px]"
                  src={ConfigImage.tickSuccess}
                  alt={item?.title}
                  notWebp
                />
                <span className="!text-[14px] md:!text-base font-medium md:font-normal">
                  {item?.title}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(Privilege);
