.note {
  border-radius: 0.5rem;
  border: 1px solid #3ac882;
  max-width: 520px;
}

.iconInfo {
  @apply text-vo-green text-base;
}

.qrcode {
  border: 2px solid #3ac882;
}

.buttonBackHome {
  @apply w-full border border-solid border-[#222] h-12 text-base text-[#222] font-medium;
}

.privilege {
  @apply flex max-w-full py-0 md:space-x-5 xl:space-x-6;

  &Container {
    @apply flex flex-col xl:flex-row w-full max-w-full space-y-2 xl:space-y-0 xl:space-x-6;
  }

  &Title {
    @apply flex shrink xl:items-center text-[#171717] !text-lg xl:!text-[24px] font-bold;
  }
}

.benefits {
  @apply md:w-1/5 md:min-w-[272px] md:max-w-[calc(20%-clamp(5px,6px,7px))] relative pb-[61px] md:-mr-[clamp(5px,6px,7px)] shrink-0;
  .item {
    @apply flex items-center h-[37px] p-[clamp(.25rem,50%,.5rem)] -mt-[1px] gap-1;
    @apply text-left 2xl:text-base text-sm border border-solid border-[#DBDBDB];
  }
}

.info {
  @apply flex flex-col relative space-y-2 w-full h-auto md:h-[180px] overflow-hidden;

  &Benefit {
    @apply text-ellipsis line-clamp-2 w-full max-h-10 text-[.875rem];
  }
}

.packages {
  @apply w-full relative lg:overflow-hidden;
}

.package {
  @apply relative w-full h-auto xmd:h-full bg-white content-end;

  &Container {
    @apply flex flex-col w-full h-fit xmd:h-full max-h-full relative space-y-6;
    @apply px-2 pt-2 pb-[4.875rem] lg:pb-[5.25rem] bg-white rounded-xl lg:rounded-2xl;
    @apply before:w-full before:h-full before:absolute before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 transition-all duration-500;
    @apply before:border-2 before:border-solid before:border-[#EFEFEF] before:rounded-xl before:lg:rounded-2xl;
    @apply after:w-full after:h-full after:absolute after:top-1/2 after:left-1/2 after:-translate-x-1/2 after:-translate-y-1/2 transition-all duration-500;
    @apply after:border-2 after:border-solid after:border-vo-green after:rounded-xl after:lg:rounded-2xl after:opacity-0 after:shadow-md;
    &:hover,
    &.forceHover {
      @apply before:animate-fade-out after:animate-fade-in md:before:opacity-0 md:after:opacity-100;
      @apply shadow-[0px_4px_10px_0px_rgba(0,0,0,.25)];
      .image {
        @apply scale-110;
      }

      .button {
        @apply text-white bg-[#2FB138] hover:text-white hover:bg-[#2FB138];
      }
    }
  }
  &.oneItem {
    @apply w-full lg:w-[480px] mx-auto;
  }

  .box {
    @apply block relative md:aspect-[332/161] h-auto border-2 border-solid border-[#919191] rounded-[13px] overflow-hidden;
    .image {
      @apply transition-transform duration-500 ease-in-out origin-bottom-right;
    }
    .text {
      @apply text-white;
      @apply text-xs !important;
    }

    &Green {
      @apply border-[#0AD418];
    }
    &Blue {
      @apply border-[#3EA6FF];
    }
    &Red {
      @apply border-[#E3513B];
    }
    &Yellow {
      @apply border-[#EDC42D];
    }
  }
  &Using {
    @apply flex items-center w-full gap-1 py-1 px-2 md:px-4 absolute bottom-0 left-0;
    @apply bg-[linear-gradient(90deg,#DA9E1C_0%,rgba(219,160,32,0.94)_56%,rgba(236,189,87,0)_100%)] text-xs font-bold text-white;
  }

  &Ribbon {
    @apply before:w-8 before:h-[36px] before:absolute before:top-[2px] before:-right-2 lg:before:-right-1;
    @apply before:bg-[linear-gradient(-90deg,#DA9E1C_0%,#ECBD57_100%)] before:rounded-md before:-z-[1] before:left-[unset];
  }

  .mostBuyers {
    @apply w-[calc(100%-1.5rem)] p-0 absolute top-3 left-1/2 -translate-x-1/2 text-white font-medium text-lg;
    @apply bg-none;
  }

  .button {
    @apply w-[calc(100%-1.5rem)] mx-auto h-10 lg:h-12 z-10 rounded-[99px] absolute bottom-[.75rem] left-1/2 -translate-x-1/2;
    @apply font-medium text-[#2FB138] bg-[#0BBC1617]/10 border border-[#CCEBCE] hover:text-white hover:bg-[#2FB138] hover:border-[#2FB138];
    @apply transition-all duration-200;
  }
  .value {
    @apply flex text-left text-ellipsis;
  }

  &MostPopular {
    @apply pt-12 px-3 pb-3;
  }

  &BackDrop {
    @apply absolute top-0 left-0 w-full h-full rounded-xl lg:rounded-3xl overflow-hidden z-0;
    &Wrap {
      @apply w-full h-full relative overflow-hidden block;
    }

    &Balloon {
      @apply rounded-full blur-[36px] rotate-12;
      &1 {
        @apply w-[101%] h-[58%] block absolute -left-[46%] -top-[5%] z-0;
      }

      &2 {
        @apply w-[80%] h-[40%] block absolute left-[49%] top-[1%] z-[2];
      }

      &3 {
        @apply w-[156%] h-[97%] block absolute left-0 top-[40%] z-0;
      }
    }
    &Green {
      @apply bg-black;

      &ConicGradient {
        background-image: conic-gradient(from 90deg, #1d5b51, #2dff1a);
        @apply bg-white;
      }
    }

    &Blue {
      @apply bg-black;

      &ConicGradient {
        background-image: conic-gradient(from 90deg, #4279bb, #3561ff);
        @apply bg-white;
      }
    }

    &Red {
      @apply bg-black;

      &ConicGradient {
        background-image: conic-gradient(from 90deg, #763012, #da2028);
        @apply bg-white;
      }
    }

    &Yellow {
      @apply bg-black;

      &ConicGradient {
        background: conic-gradient(from 90deg, #9d7623, #ffd600);
      }
    }
  }

  // remainingDays
  &RemainingDays {
    @apply flex w-full items-center absolute left-0 bottom-0 bg-[linear-gradient(90deg,#ED1010_0%,#C01E1E_56%,rgba(236,87,87,0)_100%)] text-white px-[14px] pt-1 pb-[6px] space-x-1;

    & > span {
      @apply text-[.75rem] font-bold;
    }
  }
}

.swiperCentered {
  & > div {
    @apply flex justify-center;
  }
}

.swiperVerticalScroll {
  & > div {
    @apply flex-col space-y-5 md:space-y-0 !important;
  }
}

.swiperPrevButton {
  @apply w-[clamp(3.5rem,20%,12.5rem)] h-full cursor-pointer z-20 flex md:pt-[6.125%] pl-5 items-center;
  @apply absolute top-1/2 -left-4 lg:-left-[3.02083vw] -translate-y-1/2 opacity-0 transition-all duration-300 ease-in-out;
  @apply bg-[linear-gradient(90deg,#FFF_0%,rgba(255,255,255,0)_100%)];
  @apply after:hidden;
  div {
    @apply absolute right-1/2 translate-x-1/2;
  }
}
.swiperNextButton {
  @apply w-[clamp(3.5rem,20%,12.5rem)] cursor-pointer h-full z-20 flex md:pt-[6.125%] pl-5 items-center;
  @apply absolute top-1/2 -right-4 lg:-right-[3.02083vw] -translate-y-1/2 opacity-0 transition-all duration-300 ease-in-out;
  @apply bg-[linear-gradient(270deg,#FFF_0%,rgba(255,255,255,0)_100%)];
  @apply after:hidden;
  div {
    @apply absolute right-1/2 translate-x-1/2;
  }
}

.showButton {
  @apply opacity-100;
}

.hideButton {
  @apply opacity-0 invisible;
}

.buttonSelectPackageMobile {
  @apply w-full p-4 bottom-0 left-0 right-0 z-50 bg-[#F6F6F7];
  @apply transition duration-300 ease-in-out border-t-[1px_solid_rgba(196,196,196,0.20)] mt-4;
  position: sticky;
  .button {
    @apply relative w-full h-9 flex items-center justify-center border;
    @apply text-base rounded-sm bg-vo-green text-white font-medium;
  }
}

.coupon {
  @apply md:flex items-center lg:items-start lg:space-y-0 space-y-2 pt-3;
  .textDesc {
    @apply text-sm relative lg:mr-3 lg:pr-3 max-w-[314px];
  }
  .form {
    @apply flex items-center relative max-w-[480px] w-full;
    &.error {
      & > .button {
        @apply bg-[#E3513B];
      }

      & > div > .input,
      & > div > .input:focus {
        @apply border-[#E3513B] text-[#E3513B];
      }
      .message {
        @apply text-[#E3513B] text-xs absolute -bottom-6 left-0;
      }
    }
    .input {
      @apply rounded-[100px] text-black placeholder:text-[rgba(0,0,0,0.09)] mb-0 h-12 py-1 border-[rgba(0,0,0,0.05)] border-[1px] focus:border-[#CCCCCC] focus:ring-0 px-[28px] pr-[125px] w-full;
    }
    .button {
      @apply absolute top-1/2 right-[4px] -translate-y-1/2 h-[calc(100%_-_8px)] w-full max-w-[120px];
      @apply disabled:bg-[#F3F3F3] disabled:text-[#9B9B9B] disabled:cursor-not-allowed text-white bg-[#2FB138] hover:opacity-90 rounded-[100px] transition-all;
    }
  }
}
