import React, { useMemo, useEffect } from 'react';
import classNames from 'classnames';
import { PAGE, PAYMENT_METHOD } from '@constants/constants';
import ShopeePayMethod from '@components/payment/Step2/MethodGroup/ShopeePayMethod';
import { TEXT } from '@constants/text';
import { useDispatch, useSelector } from 'react-redux';
import CakeMethod from '@components/payment/Step2/MethodGroup/CakeMethod/CakeMethod';
import { selectBank } from '@actions/payment';
import TrackingPayment from '@tracking/functions/payment';
import CreditMethod from './MethodGroup/CreditMethod';
import ZaloPayMethod from './MethodGroup/ZaloPayMethod';
import NapasMethod from './MethodGroup/NapasMethod';
import VNPayMethod from './MethodGroup/VNPayMethod';
import PayooMethod from './MethodGroup/PayooMethod';
import MobiMethod from './MethodGroup/MobiMethod';
import ViettelMethod from './MethodGroup/ViettelMethod';
import VinaMethod from './MethodGroup/VinaMethod';
import Image from '../../basic/Image/Image';
import style from './Step2.module.scss';
import { useVieRouter } from '@/customHook';

const trackingPayment = new TrackingPayment();
const PAYMENT_INFO = [
  {
    icon: 'vie vie-info-o-c-script',
    text: 'Chọn phương thức thanh toán phù hợp & bấm nút Thanh toán để hoàn thành đăng ký gói bạn nhé'
  },
  {
    icon: 'vie vie-info-o-c-script',
    text: 'Huỷ gói bất cứ lúc nào bạn muốn'
  },
  {
    icon: 'vie vie-lock-o-alt-rc',
    text: 'Bạn hãy yên tâm VieON luôn đảm bảo mọi giao dịch của bạn đều được bảo mật tuyệt đối'
  }
];

const Method = ({
  data = [],
  selectedMethod,
  newTermSelected,
  onSelect,
  isRentalContent,
  isPvodContent,
  isInApp,
  isMobile
}: any) => {
  const router = useVieRouter();
  const { pathname } = router || {};

  useEffect(() => {
    if (selectedMethod?.id) {
      trackingPayment.selectMethod({ selectedMethod });
    }
  }, [selectedMethod?.id]);
  useEffect(() => {
    if (data?.length && !selectedMethod) {
      const firstMethod = data[0];
      onSelect(firstMethod);
    }
  }, [data]);

  return (
    <div className={classNames('block block--payment block--payment-method', style.method)}>
      <div className="space-y-1.5 md:space-y-2 lg:space-y-3">
        <h2 className={style.methodTitle}>{TEXT.CHOOSE_METHOD_PAYMENT}</h2>

        {PAYMENT_INFO.map((info, index) => {
          if (
            index === 1 &&
            (pathname.includes(PAGE.RENTAL_CONTENT) || pathname.includes(PAGE.PVOD_CONTENT))
          ) {
            return null;
          }
          return (
            <div key={index} className={style.methodTitleSub}>
              <span className={classNames('icon icon--tiny', style.methodTitleIcon)}>
                <i className={info.icon} />
              </span>
              <div className={style.methodTitleSubText}>{info.text}</div>
            </div>
          );
        })}
      </div>

      <div className="block__body">
        <div className="accordion accordion--method space-y-2 md:space-y-4">
          {data?.map((item: any) => (
            <ItemMethod
              key={item.id}
              data={item}
              {...{
                onSelect,
                selectedMethod,
                newTermSelected,
                isRentalContent,
                isPvodContent,
                isInApp,
                isMobile
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

const ItemMethod = ({
  data,
  onSelect,
  selectedMethod,
  newTermSelected,
  isRentalContent,
  isPvodContent,
  isInApp
}: any) => {
  const dispatch = useDispatch();
  const { vnPayList } = useSelector((state: any) => state?.Payment) || {};
  const isActive = useMemo(() => data?.id === selectedMethod?.id, [data, selectedMethod]);
  const isDisable = useMemo(() => {
    if (isRentalContent) return !data?.isRental;
    if (isPvodContent) return !data?.isOnPvod;
    if (newTermSelected?.noneRecurring?.id) return !data;
    return (
      (data?.enableTermList || []).findIndex((termId: any) => termId === newTermSelected?.id) === -1
    );
  }, [newTermSelected, data]);

  const promotionHighlight = useMemo(() => {
    const promotionHighlightData = isInApp
      ? data?.promotionHighlightInApp
      : data?.promotionHighlight;
    return promotionHighlightData?.[newTermSelected?.id];
  }, [data, newTermSelected]);

  const description = useMemo(
    () => (isInApp ? data?.descriptionInApp : data?.description),
    [data, newTermSelected]
  );

  const selectMethod = () => {
    onSelect(data);
    if (vnPayList?.length > 0 && PAYMENT_METHOD.QR_VNPAY === data?.id) {
      const firstBank = vnPayList?.[0];
      dispatch(selectBank(firstBank));
      trackingPayment.selectBank({ bank: firstBank });
    }
  };

  const renderBodyMethod = (method: any) => {
    switch (method?.id) {
      case PAYMENT_METHOD.CAKE:
        return <CakeMethod isActive={isActive} recurringNote={method.recurringNote} />;
      case PAYMENT_METHOD.ASIAPAY:
        return <CreditMethod isActive={isActive} />;
      case PAYMENT_METHOD.NAPAS:
        return (
          <NapasMethod isActive={isActive} method={method} isRentalContent={isRentalContent} />
        );
      case PAYMENT_METHOD.ZALO_PAY:
        return (
          <ZaloPayMethod
            isActive={isActive}
            isRentalContent={isRentalContent}
            selectedTerm={newTermSelected}
          />
        );
      case PAYMENT_METHOD.SHOPEE_PAY:
        return <ShopeePayMethod isActive={isActive} isRentalContent={isRentalContent} />;
      case PAYMENT_METHOD.MOMO:
      case PAYMENT_METHOD.MOCA:
      case PAYMENT_METHOD.VIETTEL_PAY:
        return null;
      case PAYMENT_METHOD.VN_PAY:
        return <VNPayMethod isActive={isActive} isRentalContent={isRentalContent} />;
      case PAYMENT_METHOD.MOBI:
        return <MobiMethod method={method} isActive={isActive} selectedTerm={newTermSelected} />;
      case PAYMENT_METHOD.VINA:
        return <VinaMethod method={method} isActive={isActive} selectedTerm={newTermSelected} />;
      case PAYMENT_METHOD.VIETTEL:
        return <ViettelMethod method={method} isActive={isActive} selectedTerm={newTermSelected} />;
      case PAYMENT_METHOD.PAYOO:
        return <PayooMethod isActive={isActive} />;
      default:
        return null;
    }
  };

  return (
    <div
      className={classNames(
        'accordion__item on-click !p-2 md:!py-4 md:!pl-4 md:!pr-6 hover:!border-[#3AC882] hover:!bg-[#EEFCF5]',
        style['margin-bottom'],
        style.term,
        isDisable && 'disabled',
        isActive && '!border-[#3AC882] !bg-[#EEFCF5] shadow-[0_4px_10px_0_#00000040]',
        data?.id === PAYMENT_METHOD.CAKE && '!pb-5'
      )}
    >
      <div className="accordion__header" onClick={selectMethod}>
        <div className="radio radio-custom non-hover">
          <input type="radio" name="payment-methods" id={data?.id} checked={isActive} readOnly />
          <label htmlFor={data?.id}>
            <span className="logo logo--method align-items justify-content-center small">
              <Image notWebp src={data?.icon || ''} alt={data?.title} />
            </span>
            <div className="method-info flex flex-col md:flex-row md:flex-wrap gap-x-3 gap-y-0.5 md:items-center">
              <h2 className="method-title leading-[1.2] text-[.75rem] md:text-[1rem] font-bold text-black">
                {data?.title}
              </h2>
              {promotionHighlight && !isRentalContent && (
                <div
                  className="method-desc leading-[1.2] text-[.75rem] md:text-[1rem] font-bold text-[#3EA6FF]"
                  dangerouslySetInnerHTML={{ __html: promotionHighlight }}
                />
              )}
              {description && !isRentalContent && (
                <div
                  className={classNames(
                    'method-desc leading-[1.2] text-[.875rem] md:text-[1rem] text-black'
                  )}
                  dangerouslySetInnerHTML={{ __html: description }}
                />
              )}
            </div>
          </label>
        </div>
      </div>
      {renderBodyMethod(data)}
    </div>
  );
};

export default Method;
