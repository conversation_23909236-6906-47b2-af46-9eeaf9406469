import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import filter from 'lodash/filter';
import { setLoading, setToast } from '@actions/app';
import * as MocaAction from '@actions/moca';
import * as ShopeePayAction from '@actions/shopeepay';
import * as NapasAction from '@actions/napas';
import { openPopup } from '@actions/popup';
import * as ViettelPayAction from '@actions/viettelPay';
import * as MomoAction from '@actions/momo';
import PaymentApi from '@apis/Payment';
import { CURRENCY, EL_ID, LOCATION, PAGE, PAYMENT_METHOD, POPUP } from '@constants/constants';
import ConfigGTM from '@config/ConfigGTM';
import LocalStorage from '@config/LocalStorage';
import { DOMAIN_WEB } from '@config/ConfigEnv';
import { TEXT } from '@constants/text';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import TrackingPayment from '@tracking/functions/payment';
import { handleGTMEvent } from '@tracking/TrackingGTM';
import { handleScrollTop, queryStringEncoding, encodeParamDestination } from '@helpers/common';
import { checkDataContentTVodToShowPopup } from '@services/tVod';
import TrackingApp from '@tracking/functions/TrackingApp';
import {
  createTransactionSuccess,
  clearTVodInfo,
  clearTVodOffer,
  setTemporaryData,
  getListTokensSaved,
  checkLinkZaloPayTransaction,
  selectBank,
  setSelectedMethod,
  selectTerm
} from '@actions/payment';
import ReferralProgPayment from '@components/payment/Step2/ReferralProgPayment';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import Bill from './bill/Bill';
import BillTVod from './bill/BillTVod';
import AsiaPayCheckout from './checkoutFunctions/asiapay';
import ZaloPayCheckout from './checkoutFunctions/zalopay';
import VNPayCheckout from './checkoutFunctions/vnpay';
import NapasCheckout from './checkoutFunctions/napas';
import SeoText from '../../seo/SeoText';
import TermPackage from './TermPackage';
import Method from './Method';

const trackingPayment = new TrackingPayment();
let totalAmount = 0;

const Step2Global = () => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const {
    listMethodsConfig,
    listPackagesConfig,
    selectedMethod,
    selectedPackage,
    promotionData,
    selectedTerm,
    bank,
    cardInfo,
    zaloPayLinked,
    tokenSelected,
    tvodInfo,
    tvodOffer,
    valueReferralCode
  } = useSelector((state: any) => state?.Payment);
  const [isNotSelectBank, setIsSelectBank] = useState(false);
  const [contentData, setContentData] = useState<any>(null);
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const { isMobile, geoCheck, isMobileViewPort } = useSelector((state: any) => state?.App || {});
  const dataSEOAllPage = useSelector((state: any) => state?.Page?.dataSEOAllPage || {});
  const { referralProg } = useSelector((state: any) => state?.App.webConfig) || {};
  const { enableReferral } = referralProg || {};
  const { checkFirstPay } = useSelector((state: any) => state?.Payment) || {};
  const { totalPrice } = tvodOffer || {};
  const isGeoCheckValid = geoCheck?.geo_country === LOCATION.VIETNAM && geoCheck?.geo_valid;
  const { pathname, query, asPath } = router || {};
  const { isSimulcast, isLiveEvent, fromPrice } = query || {};
  const isRentalContent = useMemo(() => (pathname || '').includes(PAGE.RENTAL_CONTENT), [pathname]);
  const isInApp = useMemo(() => (pathname || '').includes(PAGE.INAPP), [pathname]);
  const inAppZaloPay = useMemo(() => (pathname || '').includes(PAGE.ZALOPAY), [pathname]);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const packageConfig = useMemo(
    () => (listPackagesConfig || []).find((item: any) => item?.id === selectedPackage?.id) || {},
    [selectedPackage, listPackagesConfig]
  );

  const methodData = useMemo(
    () =>
      (listMethodsConfig || []).filter(
        (me: any) => (packageConfig?.methods || []).indexOf(me?.id) >= 0
      ),
    [listMethodsConfig, selectedPackage, packageConfig]
  );

  const isCheckFirstPay = useMemo(() => checkFirstPay?.isFirstPay, [checkFirstPay]);

  const checkPackageConfig = useMemo(
    () => referralProg?.package.findIndex((item: any) => item?.id === selectedPackage?.id) !== -1,
    [selectedPackage, referralProg?.package]
  );

  const isOnlyQrCode =
    methodData?.[0]?.id === PAYMENT_METHOD.QRCODE && (methodData || [])?.length === 1;

  useEffect(() => {
    handleScrollTop();
    return () => {
      dispatch(clearTVodInfo());
      dispatch(clearTVodOffer());
      dispatch(selectTerm(null));
    };
  }, []);

  useEffect(() => {
    if (isOnlyQrCode) {
      router.push(PAGE.PAYMENT);
    }
  }, [isOnlyQrCode]);

  useEffect(() => {
    if (typeof setSelectedMethod === 'function') {
      dispatch(setSelectedMethod(methodData?.[0]));
    }
  }, [methodData, query?.id]);

  useEffect(() => {
    if (selectedTerm?.id) {
      if ((selectedMethod?.enableTermList || []).includes(selectedTerm?.id) === false) {
        const newSelectedMethod = getFirstEnableMethod({
          methodList: methodData,
          selectedId: selectedTerm?.id
        });
        if (typeof setSelectedMethod === 'function') {
          dispatch(setSelectedMethod(newSelectedMethod));
        }
      }
    }
  }, [selectedTerm]);

  useEffect(() => {
    if (!isEmpty(selectedPackage)) {
      const listTerms = get(selectedPackage, 'terms', []);
      const listTermsDiscount = filter(listTerms, ['discount', true]);
      const listTermsRecurring = filter(listTerms, ['recurring', true]);
      const itemTermSegmentUser = filter(listTerms, ['id', parseInt(query?.termId)]);
      if (!isEmpty(itemTermSegmentUser)) dispatch(selectTerm(itemTermSegmentUser[0]));
      else if (!isEmpty(listTermsDiscount)) {
        dispatch(selectTerm(listTermsDiscount[0]));
      } else if (!isEmpty(listTermsRecurring[0])) {
        dispatch(selectTerm(listTermsRecurring[0]));
      } else {
        dispatch(selectTerm(listTerms[0]));
      }
    }
  }, [selectedPackage]);

  useEffect(() => {
    if (profile?.id) {
      handleLinkedSupport();
    }
  }, [selectedMethod, profile?.id]);

  useEffect(() => {
    const isStopSell = get(tvodInfo, 'errorCode', 0) === 106;
    if (!isEmpty(tvodInfo) && isStopSell) {
      checkAccessTVodPackage({ ...tvodInfo, fromPrice, isGlobal });
    }
  }, [tvodInfo]);

  useEffect(() => {
    const isStopSell = get(tvodInfo, 'errorCode', 0) === 106;
    const idInfoTVodProduct = get(tvodInfo, 'bizInfo.tvodProductId', '');
    const idOfferTVodProduct = get(tvodOffer, 'tvodProductId', '');
    if (
      !isEmpty(tvodInfo) &&
      !isStopSell &&
      !isEmpty(tvodOffer) &&
      idOfferTVodProduct === idInfoTVodProduct &&
      totalPrice > 0
    ) {
      checkAccessTVodPackage({ ...tvodInfo, fromPrice, isGlobal }, tvodOffer);
    }
  }, [tvodInfo, totalPrice]);

  useEffect(() => {
    dispatch(selectBank(null));
    setIsSelectBank(false);
  }, [selectedMethod]);

  useEffect(() => {
    if (!isEmpty(bank) && isNotSelectBank) {
      setIsSelectBank(false);
    }
  }, [bank, isNotSelectBank]);

  const getFirstEnableMethod = ({ methodList, selectedId }: any) => {
    let firstEnableMethod = null;
    if (methodList) {
      firstEnableMethod = methodList.find((items: any) =>
        items?.enableTermList.includes(selectedId)
      );
    }
    return firstEnableMethod;
  };

  const handleLinkedSupport = () => {
    switch (selectedMethod?.id) {
      case PAYMENT_METHOD.ZALO_PAY:
        dispatch(checkLinkZaloPayTransaction());
        break;
      case PAYMENT_METHOD.NAPAS:
      case PAYMENT_METHOD.SHOPEE_PAY: {
        let paymentMethod = 'Card';
        let paymentService = '';
        if (selectedMethod?.id === PAYMENT_METHOD.SHOPEE_PAY) {
          paymentMethod = 'WALLET';
          paymentService = PAYMENT_METHOD.SHOPEE_PAY;
        }
        dispatch(getListTokensSaved({ paymentMethod, paymentService }));
        break;
      }
      default:
        break;
    }
  };
  // Handle checkout payment when click thanhtoan button
  const onCheckOut = async (isTVOD: any) => {
    if (!profile?.id) {
      const remakeDestination = encodeParamDestination(router?.asPath);
      router.push(
        `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${
          isTVOD ? TYPE_TRIGGER_AUTH.PAYMENT_TVOD : TYPE_TRIGGER_AUTH.PAYMENT
        }`
      );
      return;
    }
    if (!isGeoCheckValid) {
      dispatch(
        openPopup({
          name: POPUP.NAME.BLOCK_BUY_PACKAGE_FOREIGN_USERS
        })
      );
      return;
    }
    if (selectedMethod?.id === PAYMENT_METHOD.VN_PAY && isEmpty(bank)) {
      setIsSelectBank(true);
      return;
    }
    // GTM PAYMENT_FORM
    handleGTMEvent({
      eventName: ConfigGTM.PAYMENT_FORM,
      userProfile: profile,
      query,
      params: {
        m_PackageId: selectedPackage?.id || 0,
        m_PackageName: selectedPackage?.name || '',
        m_Duration: selectedTerm?.name || '',
        m_Amount: selectedTerm?.price || 0,
        m_PaymentMethod: selectedMethod?.id || '',
        m_Currency: CURRENCY.VND
      }
    });

    if (isRentalContent) {
      await handleCheckoutTVod(tvodInfo, tvodOffer);
    } else {
      const { isShowPopup } = await handlePrePayOverLap();
      if (!isShowPopup) {
        await createTransaction();
      }
    }
  };

  const handleCheckoutTVod = async (infoTVod: any, offerTVod: any) => {
    const isNotAccessTVodPackage = await checkAccessTVodPackage(
      { ...infoTVod, isGlobal },
      offerTVod,
      true
    );
    if (!isNotAccessTVodPackage) {
      await createTransaction();
    }
  };

  const handlePopupAboutTVodPackage = ({
    name,
    content,
    infoTVod,
    offerTVod,
    isNeedReloadOffer,
    isChangeToVOD,
    isFromPriceNotSameCurPrice,
    callbackCreateTransaction
  }: any) => {
    dispatch(
      openPopup({
        name,
        content,
        tvodInfo: infoTVod,
        tvodOffer: offerTVod,
        isNeedReloadOffer,
        isChangeToVOD,
        isFromPriceNotSameCurPrice,
        notClosedButton: true,
        callbackCreateTransaction
      })
    );
  };

  const checkAccessTVodPackage: any = async (infoTVod: any, offerTVod: any, isCheckOut: any) => {
    await checkDataContentTVodToShowPopup({
      id: query?.id,
      infoTVod,
      offerTVod,
      isSimulcast: isSimulcast === 'true',
      isLiveEvent: isLiveEvent === 'true',
      isCheckOut,
      callback: handlePopupAboutTVodPackage,
      setContentData,
      callbackCreateTransaction: createTransaction
    });
  };

  const handlePrePayOverLap = async () => {
    const billingPermission = await PaymentApi.checkBillingPermission({ termId: selectedTerm?.id });
    let popupData: any = billingPermission;
    const { prePayConfirmNeed, buyPermission } = billingPermission || {};
    let isPreventPayment = false;
    let isOverlap = false;
    let isAccumulate = false;
    let billingPermission2 = {};
    if (!buyPermission) {
      // "DENY"
      isPreventPayment = true;
    } else if (prePayConfirmNeed) {
      // POPUP OVERLAP
      isOverlap = true;
    } else {
      const checkAccumulate = await handlePrePayAccumulate();
      isPreventPayment = checkAccumulate?.isPreventPayment;
      isAccumulate = checkAccumulate?.isAccumulate;
      billingPermission2 = checkAccumulate?.billingPermission2;
      popupData = isPreventPayment || isAccumulate ? billingPermission2 : popupData;
    }
    if (isPreventPayment || isOverlap || isAccumulate) {
      dispatch(
        openPopup({
          name: POPUP.NAME.PRE_PAY,
          ...popupData,
          isOverlap,
          isPreventPayment,
          isAccumulate,
          onConfirm: isAccumulate
            ? createTransaction
            : isOverlap
            ? () => handlePrePayAccumulate(isOverlap)
            : () => {}
        })
      );
    }
    return { isShowPopup: isPreventPayment || isOverlap || isAccumulate };
  };

  const handlePrePayAccumulate: any = async (isOverlap: any) => {
    let isPreventPayment = false;
    let isAccumulate = false;
    const billingPermission2 = await PaymentApi.checkBillingPermission2({
      termId: selectedTerm?.id
    });
    if (!billingPermission2?.isCallback) {
      if (!billingPermission2?.buyPermission) {
        // "DENY"
        isPreventPayment = true;
      } else if (billingPermission2?.prePayConfirmNeed) {
        // "ACCUMULATE"
        isAccumulate = true;
      } else if (isOverlap) {
        createTransaction();
      }
    } else if (isOverlap) {
      createTransaction();
    }
    if (isOverlap && (isPreventPayment || isAccumulate)) {
      dispatch(
        openPopup({
          name: POPUP.NAME.PRE_PAY,
          ...billingPermission2,
          isPreventPayment,
          isAccumulate,
          isOverlap: false,
          onConfirm: createTransaction
        })
      );
    }
    return { isPreventPayment, isAccumulate, billingPermission2 };
  };
  // Create transaction all services
  const createTransaction = async () => {
    dispatch(openPopup());
    const recurring = selectedTerm?.recurring;
    const termId = selectedTerm?.id;
    const isSegmentedUser = termId === +query?.termId;
    let transactionData: any = null;
    const queryString = `?${queryStringEncoding(query)}`;
    const promotion = promotionData?.valid === 1 && promotionData?.used === 0 ? promotionData : {};
    if (isRentalContent) {
      dispatch(setLoading(true));
      const { tvodProductId } = tvodInfo?.bizInfo || '';
      const queryString = `?${queryStringEncoding(query)}`;
      const promotion =
        promotionData?.valid === 1 && promotionData?.used === 0 ? promotionData : {};

      const tvodTransaction = await PaymentApi.tvodTransaction({
        tvodProductId,
        paymentService: selectedMethod?.code,
        tokenId: selectedMethod?.id === PAYMENT_METHOD.NAPAS ? tokenSelected?.id : '',
        selectedMethod
      });
      if (!tvodTransaction?.success) {
        dispatch(setToast({ message: tvodTransaction?.errorMessage || TEXT.MSG_ERROR }));
        dispatch(setLoading(false));
        return false;
      }
      let returnUrl = '';
      let cancelUrl = '';

      if (
        selectedMethod?.id === PAYMENT_METHOD.MOCA ||
        selectedMethod?.id === PAYMENT_METHOD.SHOPEE_PAY
      ) {
        returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}`;
      } else if (selectedMethod?.id === PAYMENT_METHOD.VIETTEL_PAY) {
        cancelUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}`;
        returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}`;
      } else if (
        selectedMethod?.id === PAYMENT_METHOD.VN_PAY ||
        selectedMethod?.id === PAYMENT_METHOD.MOMO
      ) {
        returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}${queryString}`;
      } else if (selectedMethod?.id === PAYMENT_METHOD.NAPAS) {
        returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}${queryString}&orderId=${tvodTransaction?.orderId}&merchantId=VIEON&method=${PAYMENT_METHOD.NAPAS}`;
      }

      dispatch(createTransactionSuccess({ data: tvodTransaction, valueReferralCode }));
      const tvodTransactionPay = await PaymentApi.tvodTransactionPay({
        orderId: tvodTransaction?.orderId,
        bankCode: bank?.code || '',
        returnUrl,
        cancelUrl
      });
      if (!tvodTransactionPay?.success) {
        dispatch(setToast({ message: tvodTransactionPay?.errorMessage || TEXT.MSG_ERROR }));
        dispatch(setLoading(false));
        return false;
      }
      switch (selectedMethod?.id) {
        case PAYMENT_METHOD.NAPAS: {
          transactionData = tvodTransactionPay?.napas || {};
          const returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}${queryString}&orderId=${transactionData?.orderId}&merchantId=VIEON&method=${PAYMENT_METHOD.NAPAS}`;
          const napasCheckout = new NapasCheckout({
            returnUrl,
            router,
            isRentalContent,
            transactionData,
            dispatch
          });
          await napasCheckout.handleNapas();
          dispatch(setLoading(false));
          break;
        }
        case PAYMENT_METHOD.ZALO_PAY: {
          transactionData = tvodTransactionPay?.zalopay;
          const checkoutZaloPay = new ZaloPayCheckout({
            dispatch,
            totalAmount: (tvodTransaction?.amountNum || 0) / 100,
            router,
            selectedMethod,
            queryString,
            inApp: inAppZaloPay,
            promotionData: promotion,
            isRentalContent,
            transactionData
          });
          setTimeout(() => {
            dispatch(setLoading(false));
            checkoutZaloPay.handleZaloPay();
          }, 2000);
          break;
        }
        case PAYMENT_METHOD.SHOPEE_PAY: {
          transactionData = tvodTransactionPay?.shopeepay;
          setTimeout(() => {
            dispatch(setLoading(false));
            dispatch(
              ShopeePayAction.handleTVod({
                transactionData: tvodTransactionPay?.shopeepay,
                totalAmount: (tvodTransaction?.amountNum || 0) / 100,
                selectedPackage,
                selectedTerm,
                isRentalContent,
                queryString,
                router
              })
            );
          }, 2000);
          break;
        }
        case PAYMENT_METHOD.MOCA: {
          transactionData = tvodTransactionPay?.moca;
          const redirectUrl = transactionData?.redirectUrl;
          if (redirectUrl) {
            setTimeout(() => {
              dispatch(setLoading(false));
              window.location = redirectUrl;
            }, 2000);
          } else {
            dispatch(setToast({ message: TEXT.MSG_ERROR }));
          }
          break;
        }
        case PAYMENT_METHOD.MOMO: {
          transactionData = tvodTransactionPay?.momo;
          const redirectUrl = transactionData?.redirectUrl;
          if (redirectUrl) {
            setTimeout(() => {
              dispatch(setLoading(false));
              window.location = redirectUrl;
            }, 2000);
          } else {
            dispatch(setToast({ message: TEXT.MSG_ERROR }));
          }
          break;
        }
        case PAYMENT_METHOD.VIETTEL_PAY: {
          transactionData = tvodTransactionPay?.viettelPay;
          const { payUrl } = transactionData;
          if (payUrl) {
            setTimeout(() => {
              dispatch(setLoading(false));
              window.location = payUrl;
            }, 2000);
          } else {
            dispatch(setToast({ message: TEXT.MSG_ERROR }));
          }

          break;
        }
        case PAYMENT_METHOD.VN_PAY:
          transactionData = tvodTransactionPay?.vnpay;
          if (transactionData?.payUrl) {
            setTimeout(() => {
              dispatch(setLoading(false));
              window.location.href = transactionData?.payUrl;
            }, 2000);
          }
          break;
        default:
          break;
      }
    } else {
      if (!termId) return;
      switch (selectedMethod?.id) {
        case PAYMENT_METHOD.NAPAS: {
          transactionData = await dispatch(
            NapasAction.createTransaction({
              tokenId: tokenSelected?.id || '',
              selectedTerm,
              selectedMethod,
              promotionData: promotion,
              queryString,
              router,
              valueReferralCode
            })
          );
          break;
        }
        case PAYMENT_METHOD.ASIAPAY: {
          const checkoutAsiaPay = new AsiaPayCheckout({
            selectedTerm,
            dispatch,
            selectedPackage,
            totalAmount,
            router,
            cardInfo,
            profile,
            selectedMethod,
            queryString,
            promotionData: promotion,
            valueReferralCode
          });
          transactionData = await checkoutAsiaPay.handleAsiaPay();
          break;
        }
        case PAYMENT_METHOD.ZALO_PAY: {
          const checkoutZaloPay = new ZaloPayCheckout({
            recurring,
            selectedTerm,
            dispatch,
            selectedPackage,
            totalAmount,
            router,
            zaloPayLinked,
            selectedMethod,
            queryString,
            inApp: inAppZaloPay,
            promotionData: promotion,
            valueReferralCode
          });
          transactionData = await checkoutZaloPay.handleZaloPay();
          break;
        }
        case PAYMENT_METHOD.SHOPEE_PAY: {
          if (recurring) {
            transactionData = await dispatch(
              ShopeePayAction.handleRecurring({
                tokenId: tokenSelected?.id || '',
                recurring,
                selectedTerm,
                dispatch,
                selectedPackage,
                totalAmount,
                router,
                selectedMethod,
                queryString,
                promotionData: promotion,
                isRentalContent,
                valueReferralCode
              })
            );
          } else {
            transactionData = await dispatch(
              ShopeePayAction.createTransaction({
                tokenId: tokenSelected?.id || '',
                recurring,
                selectedTerm,
                dispatch,
                selectedPackage,
                totalAmount,
                router,
                selectedMethod,
                queryString,
                promotionData: promotion,
                isRentalContent,
                valueReferralCode
              })
            );
          }
          break;
        }
        case PAYMENT_METHOD.MOMO: {
          transactionData = await dispatch(
            MomoAction.createTransaction({
              selectedTerm,
              selectedMethod,
              promotionData: promotion,
              valueReferralCode,
              tokenId: tokenSelected?.id || '',
              isSegmentedUser
            })
          );
          break;
        }
        case PAYMENT_METHOD.MOCA: {
          transactionData = await dispatch(
            MocaAction.createTransaction({
              tokenId: '',
              selectedTerm,
              selectedMethod,
              promotionData: promotion,
              valueReferralCode,
              isSegmentedUser
            })
          );
          break;
        }
        case PAYMENT_METHOD.VIETTEL_PAY: {
          transactionData = await dispatch(
            ViettelPayAction.createTransaction({
              selectedTerm,
              selectedMethod,
              promotionData: promotion,
              valueReferralCode,
              isSegmentedUser
            })
          );
          break;
        }
        case PAYMENT_METHOD.VN_PAY: {
          const checkoutVnPay = new VNPayCheckout({
            selectedTerm,
            dispatch,
            bankCode: bank?.code,
            promotionData: promotion,
            selectedMethod,
            queryString,
            valueReferralCode,
            isSegmentedUser
          });
          transactionData = await checkoutVnPay.handleVnPay();
          break;
        }
        default:
          break;
      }
    }

    trackingPayment.createTransaction({
      transactionData,
      selectedTerm,
      selectedMethod,
      promotionData,
      bankCode: bank?.code,
      isSegmentedUser
    });

    if (!transactionData || transactionData?.error) {
      TrackingApp.createTransactionError({
        httpCode: transactionData?.error,
        payMethod: selectedMethod.id
      });
    }

    // Save Data For Payment Result
    let orderId =
      transactionData?.order_id ||
      transactionData?.orderId ||
      transactionData?.orderID ||
      transactionData?.txnRef ||
      transactionData?.tnxID;
    if (isRentalContent) orderId = transactionData?.orderId;
    if (orderId) {
      setTemporaryData({
        id: orderId,
        partnerName: query?.utm_source || 'none',
        query: {
          ...query,
          term: selectedTerm?.id,
          method: selectedMethod?.id,
          currentUrl: { url: asPath, href: pathname }
        }
      });
    }
    saveStep();
    return true;
  };

  const saveStep = () => {
    const step2Url = asPath;
    ConfigLocalStorage.set(LocalStorage.PAYMENT_STEP2, JSON.stringify({ step2Url }));
  };

  const setTotalAmount = (total: any) => {
    totalAmount = total;
  };

  const onSelectTerm = (term: any) => {
    if (!isEmpty(term)) {
      dispatch(selectTerm(term));
    }
  };

  const onSelectMethod = (method: any) => {
    if (!isEmpty(method)) {
      if (typeof setSelectedMethod === 'function') {
        const promotionCode = router?.query?.promotionCode;
        dispatch(setSelectedMethod(method, promotionCode));
      }
    }
  };

  return (
    <section className="section section--payment section--payment-method canal-v">
      <SeoText seo={dataSEOAllPage?.seo} />
      <div className="container box-xlarge-up-width-1198">
        <div className="section__body">
          <div className="grid-x grid-margin-large-x2">
            <div className="cell large-auto margin-medium-down-bottom-16">
              <TermPackage
                data={get(selectedPackage, 'terms', [])}
                selectedPackage={selectedPackage}
                selectedTerm={selectedTerm}
                onSelect={onSelectTerm}
                router={router}
              />
              <Method
                isMobile={isMobile}
                data={methodData}
                isRentalContent={isRentalContent}
                selectedMethod={selectedMethod}
                selectedTerm={selectedTerm}
                router={router}
                isInApp={isInApp}
                onSelect={onSelectMethod}
              />
            </div>
            <div className="cell large-shrink">
              <div
                className={
                  isMobileViewPort
                    ? ''
                    : 'sticky top margin-cel-large-up-bottom-16 padding-large-up-top-58'
                }
              >
                {checkPackageConfig && !isMobile && !isRentalContent && enableReferral && (
                  <ReferralProgPayment isCheckFirstPay={isCheckFirstPay} />
                )}
                {isRentalContent ? (
                  <BillTVod
                    onCheckOut={onCheckOut}
                    isNotSelectBank={isNotSelectBank}
                    isRentalContent={isRentalContent}
                    extraError={isNotSelectBank}
                    isSimulcast={isSimulcast === 'true'}
                    isLiveEvent={isLiveEvent === 'true'}
                    content={contentData}
                    isCheckFirstPay={isCheckFirstPay}
                  />
                ) : (
                  <Bill
                    onCheckOut={onCheckOut}
                    setTotalAmount={setTotalAmount}
                    isNotSelectBank={isNotSelectBank}
                    isRentalContent={isRentalContent}
                    extraError={isNotSelectBank}
                    isCheckFirstPay={isCheckFirstPay}
                    checkPackageConfig={checkPackageConfig}
                    enableReferral={enableReferral}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
        {selectedMethod?.id === PAYMENT_METHOD.NAPAS && <div id={EL_ID.ID_ELM_NAPAS_PAYMENT} />}
      </div>
    </section>
  );
};

export default Step2Global;
