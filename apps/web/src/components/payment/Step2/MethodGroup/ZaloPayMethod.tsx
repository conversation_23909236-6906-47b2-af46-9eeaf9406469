import React, { useLayoutEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { TEXT } from '@constants/text';
import classNames from 'classnames';

const ZaloPayMethod = ({ isActive, isRentalContent, selectedTerm }: any) => {
  const { zaloPayLinked } = useSelector((state: any) => state?.Payment);
  const wrapRef = useRef<any>(null);
  const [wrapHeight, setWrapHeight] = useState(0);

  useLayoutEffect(() => {
    if (wrapRef.current) {
      setWrapHeight(wrapRef.current.clientHeight || 0);
    }
  }, [isActive]);

  if (!zaloPayLinked || !selectedTerm?.recurring || isRentalContent) return null;

  return (
    <div
      className={classNames(
        'accordion__body collapse partner-napas overflow',
        isActive && '!opacity-100 !visible'
      )}
      style={{ height: isActive ? `${wrapHeight}px` : '0' }}
    >
      <div
        className="accordion__wrap margin-small-up-top-12 padding-small-up-bottom-12 padding-large-up-bottom-16 border-st3-top"
        ref={wrapRef}
      >
        <p className="text m-b text-12 text-large-up-14 margin-small-up-top-12 margin-large-up-top-16">
          {TEXT.ZALO_PAY_LINKED}
        </p>
      </div>
    </div>
  );
};

export default ZaloPayMethod;
