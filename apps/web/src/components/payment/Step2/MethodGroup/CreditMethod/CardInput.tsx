import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { ACTION_TYPE, createAction } from '@actions/actionType';
import { EL_ID } from '@constants/constants';
import { TEXT } from '@constants/text';
import { handleAddSpacing, validateCardExpired, validateCardNumber } from '@helpers/common';
import { handleMaskedNumber } from '@services/paymentServices';
import InputGroup from '../../../../basic/Input/InputGroup';

const CardInput = () => {
  const dispatch = useDispatch();
  const {
    cardNumber,
    cardName,
    cardExpired,
    cardCVV,
    cardNumberError,
    cardExpiredError,
    cardNumberMasked,
    isFocus
  } = useSelector((state: any) => state?.Payment?.cardInfo) || {};

  const onFocusById = (id: any) => {
    const element = document.getElementById(id);
    if (element) {
      element.focus();
    }
  };
  const setCardInfo = (data: any) => {
    dispatch(createAction(ACTION_TYPE.SET_CARD_INFO, data));
  };
  const onChangeCardNumber = (e: any) => {
    const value = e.target?.value;
    const newValue = (value || '').split(' ').join('');
    const isNumber = /^\d+$/.test(newValue);
    if (!isNumber && newValue) return;

    const { validatedError, isMaster, isVisa } = validateCardNumber(newValue);
    let focus = true;
    if (`${newValue}`.length <= 16) {
      const params: any = { cardNumber: newValue, cardNumberError: '', isMaster, isVisa };
      if (`${newValue}`.length === 16) {
        params.cardNumberError = validatedError;
        focus = false;
        onFocusById(EL_ID.CARD_NAME);
      }
      params.cardNumberMasked = handleMaskedNumber(newValue, 4);
      params.isFocus = focus;
      setCardInfo(params);
    }
  };

  const onFocusCardNumber = () => {
    setCardInfo({ isFocus: true });
  };

  const onBlurCardNumber = (e: any) => {
    const value = e.target?.value;
    const newValue = (value || '').split(' ').join('');
    const { validatedError, isMaster, isVisa } = validateCardNumber(newValue);
    setCardInfo({ cardNumberError: validatedError, isMaster, isVisa, isFocus: false });
  };

  const onBlurCardExpired = (e: any) => {
    const cardExpiredError = validateCardExpired(e?.target?.value || cardExpired);
    setCardInfo({ cardExpiredError });
  };

  const onChangeCardName = (e: any) => {
    const value = `${e.target?.value}`;
    if (`${value}`.length <= 25) {
      setCardInfo({ cardName: (value || '').toUpperCase() });
    }
  };

  const onChangeCardExpired = (e: any) => {
    let value = e.target?.value || '';
    const { length } = value;
    if (value.toLowerCase().match(/[a-z]/i)) return;

    if (value.indexOf('//') !== -1) return;

    if (length <= 7) {
      if (length <= 2 && typeof parseInt(value) !== 'number') {
        return;
      }
      if (length >= 3) {
        const temp = value.substring(3, length);
        if (typeof parseInt(temp) !== 'number') return;

        if (value.indexOf('/') === -1) {
          value = `${value.substring(0, 2)}/${value.substring(2, length)}`;
        }
      }
      if (length === 2 && length > (cardExpired || '').length && value.indexOf('/') === -1) {
        value += '/';
      }

      const idxC = value.indexOf('/');
      if (idxC === length - 1) {
        const t = value.substring(0, idxC);
        if (parseInt(t) <= 9) {
          value = `0${parseInt(t)}/`;
        }
      }

      const xTemp = value.split('/');
      if (parseInt(xTemp?.[0] || 0) > 12) {
        value = `0${xTemp?.[0]?.[0] || 0}/${xTemp?.[0]?.[1] || ''}`;
      }

      setCardInfo({ cardExpired: value, cardExpiredError: '' });
    }

    if (length === 5) {
      const cardExpiredError = validateCardExpired(value);
      if (!cardExpiredError) onFocusById(EL_ID.CARD_CVV);
    }
  };

  const onChangeCVV = (e: any) => {
    const value = e.target?.value;
    const isNumber = /^\d+$/.test(value);
    if (!isNumber && value) return;
    if (`${value}`.length <= 4) {
      setCardInfo({ cardCVV: value });
    }
  };

  const newCardNumber = handleAddSpacing(cardNumber, ' ', 4);
  const inputValue = isFocus ? newCardNumber : handleAddSpacing(cardNumberMasked, ' ', 4);
  return (
    <div className="cell large-auto">
      <InputGroup
        name="CardNumber"
        placeholder={TEXT.PLACE_HOLDER_CARD_NUMBER}
        label={TEXT.CARD_NUMBER}
        id={EL_ID.CARD_NUMBER}
        className="input-for-light"
        autoComplete="cc-number"
        value={inputValue}
        onChange={onChangeCardNumber}
        onBlur={onBlurCardNumber}
        onFocus={onFocusCardNumber}
        error={cardNumberError}
        inputStyle={{ fontFamily: 'monospace' }}
      />
      <InputGroup
        label={TEXT.CARD_NAME}
        id={EL_ID.CARD_NAME}
        className="input-for-light"
        autoComplete="cc-name"
        value={cardName}
        onChange={onChangeCardName}
      />
      <div className="grid-x">
        <div className="small-7 medium-8 cell p-r3">
          <InputGroup
            name="CardExpired"
            label={TEXT.CARD_EXPIRED}
            id={EL_ID.CARD_EXPIRED}
            className="input-for-light"
            autoComplete="cc-exp"
            value={cardExpired}
            onBlur={onBlurCardExpired}
            onChange={onChangeCardExpired}
            error={cardExpiredError}
          />
        </div>
        <div className="small-5 medium-4 cell">
          <InputGroup
            type="text"
            label={TEXT.CARD_CVV}
            id={EL_ID.CARD_CVV}
            className="input-for-light "
            value={cardCVV}
            isCVV
            onChange={onChangeCVV}
            security="font-Password"
          />
        </div>
      </div>
    </div>
  );
};

export default React.memo(CardInput);
