import React from 'react';

const CardNumber = (props: any) => {
  const { count, data, reverse, defaultLabel, textStyle } = props || {};
  const tempData = `${data}`;
  const dataLength = tempData.length;
  const circleLength = count - dataLength >= 0 ? count - dataLength : 0;
  let circleArray = new Array(circleLength).fill(circleLength);

  if (reverse) circleArray = new Array(count).fill(count);

  if (reverse) {
    return (
      <>
        {dataLength <= 0 && <span className="text">{defaultLabel}</span>}
        {dataLength > 0 &&
          (circleArray || []).map((item: any, i: any) => (
            <i key={i} className="vie vie-circle-s" />
          ))}
      </>
    );
  }

  return (
    <>
      {dataLength > 0 && (
        <span className="text" style={textStyle}>
          {data}
        </span>
      )}
      {(circleArray || []).map((item: any, i: any) => (
        <i key={i} className="vie vie-circle-s" />
      ))}
    </>
  );
};

export default React.memo(CardNumber);
