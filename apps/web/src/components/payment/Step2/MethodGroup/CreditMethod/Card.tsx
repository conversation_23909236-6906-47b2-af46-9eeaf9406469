import React from 'react';
import { useSelector } from 'react-redux';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';
import Image from '@components/basic/Image/Image';
import { isMobile } from 'react-device-detect';
import SvgIcon from '@components/basic/Icon/SvgIcon';
import { ICON_KEY } from '@constants/constants';
import CardNumber from './CardNumber';

const Card = () => {
  const { cardNumber, cardName, cardExpired, cardCVV, isVisa, isMaster } =
    useSelector((state: any) => state?.Payment?.cardInfo) || {};

  const parseCardNumber = (cardNumber: any) => `${cardNumber}`.match(/.{1,4}/g);

  const tempCardNumber = parseCardNumber(cardNumber);
  const tempCardExpired = `${cardExpired}`.split('/');
  let cardClass = '';
  if (isVisa) cardClass = ' visa';
  else if (isMaster) cardClass = ' master-card';

  return (
    <div className="cell large-shrink">
      <div className={`card card--template-credit${cardClass}`}>
        <div className={`card card--template-credit-sub${cardClass}`}>
          <div className="cvv">
            <CardNumber
              defaultLabel={TEXT.CARD_CVV}
              count={(cardCVV || '').length || 0}
              data={cardCVV || ''}
              reverse
            />
          </div>
        </div>
        <div className="credit-circuit m-t1">
          <div className="grid-x align-justify">
            <div className="cell small-6">
              <Image src={ConfigImage.circuit} notWebp />
            </div>
            <div className="cell small-6">
              <div className="text text-white text-right">
                {!isMaster && !isVisa ? 'BANK' : ''}
                {isMaster && <img src={ConfigImage.logoMasterCard} alt="logo mastercard" />}
                {isVisa && <SvgIcon type={ICON_KEY.VISA} />}
              </div>
            </div>
          </div>
        </div>
        <div className="grid-container m-t2 p-l2 p-r2">
          <div className="grid-x grid-padding-x small-up-4 align-middle credit-number">
            <div className="cell">
              <div className="credit-number-item">
                <CardNumber count={4} data="" />
              </div>
            </div>
            <div className="cell">
              <div className="credit-number-item">
                <CardNumber count={4} data="" />
              </div>
            </div>
            <div className="cell">
              <div className="credit-number-item">
                <CardNumber count={4} data="" />
              </div>
            </div>
            <div className="cell">
              <div className="credit-number-item">
                <CardNumber count={4} data={tempCardNumber?.[3] || ''} />
              </div>
            </div>
          </div>
          <div className="grid-x align-bottom credit-info">
            <div className="cell auto">
              <div className="text text-white credit-name">{cardName || TEXT.CARD_NAME}</div>
            </div>
            <div className="cell shrink">
              <div className="text text-white">Hiệu lực đến</div>
              <div className="grid-x align-middle">
                <div className="cell shrink">
                  <div className="credit-number-item">
                    <CardNumber
                      textStyle={{ fontSize: isMobile ? '0.75rem' : '1rem' }}
                      count={2}
                      data={tempCardExpired?.[0] || ''}
                    />
                  </div>
                </div>
                <div className="cell shrink">
                  <span className="text text-white p-x1">/</span>
                </div>
                <div className="cell shrink">
                  <div className="credit-number-item">
                    <CardNumber
                      textStyle={{ fontSize: isMobile ? '0.75rem' : '1rem' }}
                      count={2}
                      data={tempCardExpired?.[1] || ''}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Card;
