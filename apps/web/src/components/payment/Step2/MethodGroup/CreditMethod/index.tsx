import React, { useEffect, useRef, useState } from 'react';
import BillingInfo from '@components/payment/Step2/MethodGroup/CreditMethod/BillingInfo';
import { useSelector } from 'react-redux';
import CardInput from './CardInput';
import Card from './Card';
import classNames from 'classnames';

const CreditMethod = ({ isActive }: any) => {
  const wrapRef = useRef<any>(null);
  const [wrapHeight, setWrapHeight] = useState(0);
  const { featureFlag } = useSelector((state: any) => state?.App?.webConfig || {});

  useEffect(() => {
    setWrapHeight(wrapRef?.current.clientHeight || 0);
  });

  return (
    <div
      className={classNames(
        'accordion__body collapse partner-credit',
        isActive && '!opacity-100 !visible'
      )}
      style={{ height: isActive ? `${wrapHeight}px` : 0 }}
    >
      <div className="accordion__wrap p-t2 padding-large-up-top-16 " ref={wrapRef}>
        <form className="form form--payment-card">
          <div className="pb-4 text-[#222] font-[500]">Thông tin thẻ</div>
          <div className="grid-x grid-margin-x">
            <CardInput />
            <Card />
          </div>
          {featureFlag?.isShowBillingInfo && (
            <div className="pt-10 md:pt-8">
              <BillingInfo />
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default CreditMethod;
