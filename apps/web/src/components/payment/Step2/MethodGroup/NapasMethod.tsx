import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectTokenSaved } from '@actions/payment';
import ConfigImage, { ConfigImageType } from '@config/ConfigImage';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import classNames from 'classnames';

const NapasMethod = ({ isActive, method, isRentalContent }: any) => {
  const dispatch = useDispatch();
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const { tokensSaved, tokenSelected } = useSelector((state: any) => state?.Payment);
  const dataTokensSaved = useMemo(() => {
    if (!isEmpty(tokensSaved)) return get(tokensSaved, 'data.result.tokens', []);
    return null;
  }, [tokensSaved]);

  const wrapRef = useRef<any>(null);
  const [wrapHeight, setWrapHeight] = useState(0);
  const { recurringNote, linkedSupport } = method || {};

  const isLinkedSupport = isRentalContent ? !!linkedSupport?.tvod : true;

  useEffect(() => {
    if (isLinkedSupport) {
      dispatch(selectTokenSaved(dataTokensSaved?.[0] || {}));
    }
    setWrapHeight(wrapRef?.current?.clientHeight || 0);
  }, [dataTokensSaved, isLinkedSupport]);

  const onSelectToken = (value: any) => {
    if (isLinkedSupport) {
      dispatch(selectTokenSaved(value));
    }
  };

  return (
    <div
      className={classNames(
        'accordion__body collapse partner-napas overflow',
        isActive && '!opacity-100 !visible'
      )}
      style={{ height: isActive ? `${wrapHeight}px` : '0' }}
    >
      <div
        className={`accordion__wrap margin-small-up-top-12 margin-large-up-top-16 padding-small-up-bottom-12 padding-large-up-bottom-16${
          !isEmpty(dataTokensSaved) ? ' border-st3-top' : ''
        }`}
        ref={wrapRef}
      >
        {profile?.id && (
          <>
            {(dataTokensSaved || []).map((item: any) => (
              <div key={item.id} className="radio radio-custom m-t2">
                <input
                  type="radio"
                  checked={tokenSelected?.id === item.id}
                  disabled={!isLinkedSupport}
                  onChange={() => onSelectToken(item)}
                  name="payment-methods-child"
                  id={item.id}
                />
                <label htmlFor={item.id}>
                  <img
                    src={ConfigImage[item.card_brand as keyof ConfigImageType] || ''}
                    alt={item.card_brand}
                  />
                  <div className="method-info p-l2">
                    <span className="credit-number-mask">
                      <span>
                        <i className="vie vie-circle-s" />
                        <i className="vie vie-circle-s" />
                        <i className="vie vie-circle-s" />
                        <i className="vie vie-circle-s" />
                      </span>
                      <span>
                        <i className="vie vie-circle-s" />
                        <i className="vie vie-circle-s" />
                        <i className="vie vie-circle-s" />
                        <i className="vie vie-circle-s" />
                      </span>
                      <span>
                        <i className="vie vie-circle-s" />
                        <i className="vie vie-circle-s" />
                        <i className="vie vie-circle-s" />
                        <i className="vie vie-circle-s" />
                      </span>
                      <span>{(item.card_number || '').substr(-4)}</span>
                    </span>
                  </div>
                </label>
              </div>
            ))}
            {dataTokensSaved && dataTokensSaved.length > 0 && (
              <div className="radio radio-custom">
                <input
                  type="radio"
                  name="payment-methods-child"
                  id="others"
                  checked={!tokenSelected?.id}
                  onChange={() => onSelectToken({ id: '' })}
                />
                <label htmlFor="others">
                  <div className="method-info">
                    <h2 className="method-title m-b text-14">Thanh toán bằng thẻ khác</h2>
                  </div>
                </label>
              </div>
            )}
          </>
        )}
        {recurringNote && (
          <div
            className="sub margin-small-up-top-12 margin-large-up-top-16 padding-small-up-8 padding-large-up-12 text-gray117 bg-gray191 round-2 text-12"
            dangerouslySetInnerHTML={{ __html: recurringNote }}
          />
        )}
      </div>
    </div>
  );
};

export default NapasMethod;
