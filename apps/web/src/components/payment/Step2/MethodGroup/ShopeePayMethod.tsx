import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { TEXT } from '@constants/text';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import classNames from 'classnames';

const ShopeePayMethod = ({ isActive, isRentalContent }: any) => {
  const { tokensSaved, selectedTerm } = useSelector((state: any) => state?.Payment);
  const tokenId = useMemo(() => {
    if (!isEmpty(tokensSaved)) return get(tokensSaved, 'data.result.tokens[0].id', '');
    return null;
  }, [tokensSaved]);
  const wrapRef = useRef<any>(null);
  const [wrapHeight, setWrapHeight] = useState(0);
  useEffect(() => {
    setWrapHeight(wrapRef?.current?.clientHeight || 0);
  });
  if (!tokenId || !selectedTerm?.recurring || isRentalContent) return null;

  return (
    <div
      className={classNames(
        'accordion__body collapse partner-napas overflow',
        isActive && '!opacity-100 !visible'
      )}
      style={{ height: isActive ? `${wrapHeight}px` : '0' }}
    >
      <div
        className="accordion__wrap margin-small-up-top-12 padding-small-up-bottom-12 padding-large-up-bottom-16 border-st3-top"
        ref={wrapRef}
      >
        <div className="text text-12 text-large-up-14 m-b margin-small-up-top-12 margin-large-up-top-16">
          {TEXT.SHOPEE_PAY_LINKED}
        </div>
      </div>
    </div>
  );
};

export default ShopeePayMethod;
