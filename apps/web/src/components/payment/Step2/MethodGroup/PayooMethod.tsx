import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import ConfigImage from '@config/ConfigImage';
import Image from '@components/basic/Image/Image';
import classNames from 'classnames';

const TAB = {
  TAB1: 'TAB1',
  TAB2: 'TAB2'
};

const PayooMethod = ({ isActive }: any) => {
  const { isMobileViewPort } = useSelector((state: any) => state?.App || {});
  const [tab, setTab] = useState(TAB?.TAB1);
  const wrapRef = useRef<any>(null);
  const [wrapHeight, setWrapHeight] = useState(0);

  useEffect(() => {
    setWrapHeight(wrapRef?.current?.clientHeight || 0);
  });

  return (
    <div
      className={classNames(
        'accordion__body collapse partner-payoo',
        isActive && '!opacity-100 !visible'
      )}
      style={{ height: isActive ? `${wrapHeight}px` : '0' }}
    >
      <div className="user-manual bg-white" ref={wrapRef}>
        <ul className="tabs tabs--partner-payoo flex-box justify-content-center">
          <li
            className={`tabs-title${tab === TAB.TAB1 ? ' active' : ''}`}
            onClick={() => setTab(TAB.TAB1)}
          >
            <button className="button" title="Hướng dẫn">
              Hướng dẫn
            </button>
          </li>
          <li
            className={`tabs-title${tab === TAB.TAB2 ? ' active' : ''}`}
            onClick={() => setTab(TAB.TAB2)}
          >
            <button className="button" title="Tìm kiếm cửa hàng">
              Tìm kiếm cửa hàng
            </button>
          </li>
        </ul>
        <div className={tab === TAB.TAB1 ? undefined : 'hide'}>
          <div className="user-manual-steps">
            <strong>Bước 1:</strong>
            <span className="text">
              Khách hàng hãy đến các hệ thống cửa hàng bên dưới để mua mã VieON
            </span>
            <div className="payment-store">
              <div className="grid-x grid-margin-x4 small-up-4 medium-up-6 large-up-8">
                <div className="cell">
                  <div className="card card--payment-store">
                    <div className="card-image">
                      <button className="button hollow rounded size-w-full">
                        <Image src={ConfigImage.vinMart} title="vin-mart" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="user-manual-steps">
            <strong>Bước 2:</strong>
            <span className="text" style={isMobileViewPort ? { lineHeight: 2.25 } : undefined}>
              Truy cập website
              <button className="button button--custom-small-up-24-12 button--custom-xlarge-up-28-14 hollow rounded">
                <div className="text">vieon.vn</div>
              </button>
              hoặc
              <button className="button button--custom-small-up-24-12 button--custom-xlarge-up-28-14 hollow rounded">
                <div className="text">ứng dụng VieON trên Smart TV</div>
              </button>
              và chọn <span className="highlight">Nhập mã VieON</span>
            </span>
          </div>
          <div className="user-manual-image grid-x">
            <img src={ConfigImage.tutorialPayooLeft} alt="tutorial_payoo_left" />
            <img
              className="padding-medium-down-top-20"
              src={ConfigImage.tutorialPayooRight}
              alt="tutorial_payoo_right"
            />
          </div>
          <div className="user-manual-steps">
            <strong>Bước 3:</strong>
            <span className="text">
              Nhập mã VieON đã mua ở bước 1 và bấm chọn <span className="highlight">Xác nhận</span>
            </span>
          </div>
        </div>
        <div className={`search-payment-store${tab === TAB.TAB2 ? '' : ' hide'}`}>
          <iframe
            title="Embedded Video"
            src="https://map1.payoo.vn/map"
            frameBorder="0"
            width="100%"
            height="440px"
          />
        </div>
      </div>
    </div>
  );
};

export default PayooMethod;
