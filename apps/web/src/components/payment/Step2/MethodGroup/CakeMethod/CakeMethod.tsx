import React, { useLayoutEffect, useRef, useState } from 'react';
import CardInput from './CardInput';
import Card from './Card';
import classNames from 'classnames';

const CakeMethod = ({ isActive, recurringNote }: any) => {
  const wrapRef = useRef<any>(null);
  const [wrapHeight, setWrapHeight] = useState(0);

  useLayoutEffect(() => {
    if (wrapRef.current) {
      setWrapHeight(wrapRef.current.clientHeight);
    }
  }, [isActive]);

  return (
    <div
      className={classNames('accordion__body collapse partner-credit', {
        '!opacity-100 !visible': isActive
      })}
      style={{ height: isActive ? `${wrapHeight}px` : 0 }}
    >
      <div className="accordion__wrap p-y2 padding-large-up-top-16" ref={wrapRef}>
        {recurringNote && (
          <div className="flex justify-center items-center text-left md:text-center sub mb-3 py-3 px-2 md:px-auto text-gray117 bg-gray191 rounded text-12">
            <span className="icon icon--tiny text-[#3AC882] mr-1">
              <i className="vie vie-info-o-c-script" />
            </span>
            <div className="text-[0.75rem]" dangerouslySetInnerHTML={{ __html: recurringNote }} />
          </div>
        )}
        <form className="form form--payment-card">
          <div className="grid-x grid-margin-x">
            <CardInput />
            <Card />
          </div>
        </form>
      </div>
    </div>
  );
};

export default CakeMethod;
