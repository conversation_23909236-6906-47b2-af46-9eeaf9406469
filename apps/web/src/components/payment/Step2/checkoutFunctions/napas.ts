import { EL_ID, PAGE, PLATFORM } from '@constants/constants';
import { NAPAS_CHANNEL, NAPAS_SOURCE_OF_FUNDS_TYPE, SDK_NAPAS } from '@config/ConfigEnv';
import { setToast } from '@actions/app';
import { TEXT } from '@constants/text';

class NapasCheckout {
  dispatch: any;
  isPvodContent: any;
  isRentalContent: any;
  isSmartTv: any;
  returnUrl: any;
  router: any;
  transactionData: any;
  constructor(props: any) {
    this.router = props?.router;
    this.returnUrl = props?.returnUrl;
    this.transactionData = props?.transactionData;
    this.isSmartTv = props?.isSmartTv;
    this.isRentalContent = props?.isRentalContent;
    this.isPvodContent = props?.isPvodContent;
    this.dispatch = props?.dispatch;
  }

  handleNapas = async () => {
    if (this.isRentalContent || this.isPvodContent) {
      const { flow, orderId } = this.transactionData || {};
      switch (flow) {
        case 0: {
          let resultUrl = `${PAGE.PAYMENT_RESULT}?orderId=${orderId}`;
          if (this.isSmartTv) {
            resultUrl += `&from=${PLATFORM.SMART_TV}`;
          }
          this.router.push(resultUrl);
          break;
        }
        case 1:
          this.createFormSubmit();
          break;
        case 2:
          this.verify3DSecure();
          break;
        default:
          break;
      }
    }
  };

  createFormSubmit = () => {
    const {
      merchantId,
      clientIp,
      deviceId,
      environment,
      cardScheme,
      enable3DSecure,
      apiOperation,
      orderAmount,
      orderCurrency,
      orderReference,
      orderId,
      dataKey,
      napasKey
    } = this.transactionData || {};
    const napasPaymentElm: any = document.getElementById(EL_ID.ID_ELM_NAPAS_PAYMENT);
    const formElm = document.createElement('form');
    formElm.id = 'merchant-form';
    formElm.method = 'POST';
    formElm.action = this.returnUrl;
    const napasWidget = document.createElement('div');
    napasWidget.id = 'napas-widget-container';
    const scriptElm = document.createElement('script');
    scriptElm.type = 'text/javascript';
    scriptElm.id = 'napas-widget-script';
    scriptElm.src = SDK_NAPAS;
    scriptElm.setAttribute('merchantId', merchantId);
    scriptElm.setAttribute('clientIP', clientIp);
    scriptElm.setAttribute('deviceId', deviceId);
    scriptElm.setAttribute('environment', environment);
    scriptElm.setAttribute('cardScheme', cardScheme);
    scriptElm.setAttribute('enable3DSecure', enable3DSecure);
    scriptElm.setAttribute('apiOperation', apiOperation);
    scriptElm.setAttribute('orderAmount', orderAmount);
    scriptElm.setAttribute('orderCurrency', orderCurrency);
    scriptElm.setAttribute('orderReference', orderReference);
    scriptElm.setAttribute('orderId', orderId);
    scriptElm.setAttribute('channel', NAPAS_CHANNEL);
    scriptElm.setAttribute('sourceOfFundsType', NAPAS_SOURCE_OF_FUNDS_TYPE);
    scriptElm.setAttribute('dataKey', dataKey);
    scriptElm.setAttribute('napasKey', napasKey);
    napasPaymentElm.appendChild(formElm);
    napasPaymentElm.appendChild(napasWidget);
    napasPaymentElm.appendChild(scriptElm);
  };

  verify3DSecure = () => {
    const { html } = this.transactionData || {};
    if (!html) {
      this.dispatch(setToast({ message: TEXT.MSG_ERROR }));
    } else {
      document.open();
      document.write(html);
      document.close();
    }
  };
}

export default NapasCheckout;
