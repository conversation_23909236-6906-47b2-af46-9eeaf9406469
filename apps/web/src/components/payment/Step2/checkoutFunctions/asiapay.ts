import { getCardType, mobileCheck } from '@helpers/common';
import PaymentApi from '@apis/Payment';
import { setLoading, setToast } from '@actions/app';
import { createTransactionSuccess } from '@actions/payment';
import { DOMAIN_WEB } from '@config/ConfigEnv';
import { PAGE } from '@constants/constants';
import TrackingPayment from '@tracking/functions/payment';
import BillingInfoAPI from '@apis/billing/BillingInfo';
import { TEXT } from '@/constants/text';

const ASIAN_PAY = {
  SUCCESS_URL: DOMAIN_WEB + PAGE.PAYMENT_RESULT,
  FAIL_URL: DOMAIN_WEB + PAGE.PAYMENT_RESULT,
  ERROR_URL: DOMAIN_WEB + PAGE.PAYMENT_RESULT
};

class AsiaPayCheckout {
  billingInfo: any;
  cancelTrans: any;
  cardInfo: any;
  dispatch: any;
  inApp: any;
  isMobile: any;
  profile: any;
  promotionData: any;
  queryString: any;
  router: any;
  scanQrCodeCount: any;
  scanQrCodeTimer: any;
  selectedMethod: any;
  selectedPackage: any;
  selectedTerm: any;
  totalAmount: any;
  totalPrice: any;
  trackingPayment: any;
  valueReferralCode: any;
  zaloPayLinked: any;
  constructor(params: any) {
    this.router = params?.router;
    this.selectedTerm = params?.selectedTerm;
    this.promotionData = params?.promotionData;
    this.selectedMethod = params?.selectedMethod;
    this.cardInfo = params?.cardInfo;
    this.profile = params?.profile;
    this.inApp = params?.inApp;
    this.dispatch = params?.dispatch;
    this.selectedPackage = params?.selectedPackage;
    this.totalAmount = params?.totalAmount;
    this.valueReferralCode = params?.valueReferralCode;
    this.zaloPayLinked = params?.zaloPayLinked;
    this.queryString = params?.queryString;
    this.isMobile = mobileCheck();
    this.cancelTrans = false;

    this.scanQrCodeTimer = 0;
    this.scanQrCodeCount = 0;
    this.trackingPayment = new TrackingPayment();

    this.billingInfo = params?.billingInfo;
    this.totalPrice = params?.totalPrice;
  }

  handleAsiaPay = async () => {
    // Bước 1: Gọi API để khởi tạo transaction chuẩn bị thanh toán.
    const transaction = await PaymentApi.createAsianPayTransaction({
      packageId: this.selectedTerm?.id,
      giftCode: this.promotionData?.promotionCode || this.selectedTerm?.giftCode || ''
    });

    if (transaction?.data?.error_code === 10) {
      this.dispatch(setToast({ message: transaction?.data?.error_message || TEXT.MSG_ERROR }));
      return;
    }
    // Handle post asia billing info
    if (transaction?.data?.orderID) {
      await BillingInfoAPI.postBillingInfoAsiaPay({
        ...this.billingInfo,
        order_id: transaction?.data?.orderID
      });
    }

    this.dispatch(
      createTransactionSuccess({
        data: transaction?.data,
        valueReferralCode: this.valueReferralCode
      })
    );
    // Bước 2: Gọi API để get về các config sử dụng cho thanh toán qua cổng AsiaPay
    const asianChannel = await PaymentApi.getAsianPayChannel();
    const config = asianChannel?.config;

    const billingInfoData = {
      threeDSBillingCountryCode: this.billingInfo.country_code,
      threeDSBillingState: this.billingInfo.province,
      threeDSBillingCity: this.billingInfo.district,
      threeDSBillingLine1: this.billingInfo.address,
      threeDSBillingPostalCode: this.billingInfo.postal_code
    };

    const checkoutParams = this.getParamsStep3({
      config,
      transaction,
      billingInfoData
    });
    this.createFormPost({ params: checkoutParams });
    return transaction?.data;
  };

  handleAsiaPayPvod = async (transaction: any) => {
    const asianChannel = await PaymentApi.getAsianPayChannel();
    const config = asianChannel?.config;
    const checkoutParams = this.getParamsStep3({
      config,
      transaction
    });
    this.createFormPost({ params: checkoutParams });
  };

  getParamsStep3 = ({ config, transaction, billingInfoData }: any) => {
    console.log('transaction', transaction);
    const { queryString } = this;

    const cardExpired = (this.cardInfo.cardExpired || '').split('/');
    const newCardNumber = (this.cardInfo.cardNumber || '').split(' ').join('');
    const cardNo = newCardNumber; // Số thẻ tín dụng
    const securityCode = this.cardInfo.cardCVV; // Số CVV
    const cardHolder = this.cardInfo.cardName; // Tên chủ thẻ
    const epMonth = cardExpired?.[0]; // Tháng hết hạn thẻ
    const epYear = cardExpired?.[1]?.length === 2 ? `20${cardExpired?.[1]}` : cardExpired?.[1]; // Năm hết hạn thẻ
    const orderRef = transaction?.data?.orderID || transaction?.orderId; // orderID đã lấy ở Bước 1
    const pMethod = getCardType(newCardNumber); // Tên nhà cung cấp thẻ VISA/MASTER/JCB/...
    // băm SHA1(asiapayMerchantId + "|" + orderRef + "|704|1000|N| + asiapayKeyhash)
    const secureHash = transaction?.data?.hashString3D;
    const memberPay_memberId = this.profile?.id; // user_id của vieon
    const merchantId = config?.paymentData?.['3D']?.merchantId; // config lấy ở bước 2
    const amount = this.totalPrice
      ? this.totalPrice
      : this.selectedTerm?.price - (this.promotionData?.promotionPrice || 0); // số tiền cần charge kiểu int
    const currCode = 704; // hardcode
    const payType = 'N'; // hardcode
    const successUrl = ASIAN_PAY.SUCCESS_URL + queryString; // callback link khi thành công
    const failUrl = ASIAN_PAY.FAIL_URL + queryString; // callback link khi thất bại (do thẻ hết tiền chẳng hạn)
    const errorUrl = ASIAN_PAY.ERROR_URL + queryString; // callback link khi hệ thống lỗi
    const memberPay_service = 'T'; // hardcode
    const custIPAddress = '*******'; // IP của khách đang thanh toán (nếu không biết có thể truyền bất kỳ)
    const lang = 'V'; // hardcode
    const payUrl = transaction?.data?.payLink;
    return {
      cardNo,
      securityCode,
      cardHolder,
      epMonth,
      epYear,
      orderRef,
      pMethod,
      secureHash,
      memberPay_memberId,
      merchantId,
      amount,
      currCode,
      payType,
      successUrl,
      failUrl,
      errorUrl,
      memberPay_service,
      custIPAddress,
      lang,
      payUrl,
      ...billingInfoData
    };
  };

  createFormPost = ({ params }: any) => {
    this.dispatch(setLoading(true));
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = params?.payUrl;
    // for (const key in params) {
    //   if (params.hasOwnProperty(key)) {
    //     const hiddenField = document.createElement('input');
    //     hiddenField.type = 'hidden';
    //     hiddenField.name = key;
    //     hiddenField.value = String(params[key]);

    //     form.appendChild(hiddenField);
    //   }
    // }
    Object.keys(params).forEach((key) => {
      const hiddenField = document.createElement('input');
      hiddenField.type = 'hidden';
      hiddenField.name = key;
      hiddenField.value = String(params[key]);

      form.appendChild(hiddenField);
    });
    document.body.appendChild(form);
    // make iframe to submit post form
    form.submit();
  };
}

export default AsiaPayCheckout;
