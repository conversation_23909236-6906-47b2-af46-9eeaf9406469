import PaymentApi from '@apis/Payment';
import { PAGE } from '@constants/constants';
import { setToast } from '@actions/app';
import { createTransactionSuccess } from '@actions/payment';
import TrackingPayment from '@tracking/functions/payment';

class VNPayCheckout {
  bankCode: any;
  dispatch: any;
  promotionData: any;
  queryString: any;
  selectedMethod: any;
  selectedTerm: any;
  trackingPayment: any;
  valueReferralCode: any;
  constructor(params: any) {
    this.selectedTerm = params?.selectedTerm;
    this.promotionData = params?.promotionData;
    this.bankCode = params?.bankCode;
    this.selectedMethod = params?.selectedMethod;
    this.queryString = params?.queryString;
    this.dispatch = params?.dispatch;
    this.valueReferralCode = params?.valueReferralCode;
    this.trackingPayment = new TrackingPayment();
  }

  handleVnPay = async () => {
    const url_return = window?.location?.origin + PAGE.PAYMENT_RESULT + this.queryString;
    const order_info = 'Noi dung thanh toan';
    const transaction = await PaymentApi.createVnPayTransaction({
      packageId: this.selectedTerm?.id,
      url_return,
      order_info,
      bankCode: this.bankCode,
      giftCode: this.promotionData?.promotionCode || this.selectedTerm?.giftCode || ''
    });
    this.dispatch(
      createTransactionSuccess({
        data: transaction?.data,
        valueReferralCode: this.valueReferralCode
      })
    );
    if (!transaction?.success) {
      this.dispatch(
        setToast({ message: transaction?.data?.message || transaction?.data?.error_message })
      );
    } else {
      const payUrl = transaction?.data?.payUrl;
      if (payUrl) {
        setTimeout(() => {
          window.location.href = payUrl;
        }, 1500);
      }
    }
    return transaction?.data;
  };
}

export default VNPayCheckout;
