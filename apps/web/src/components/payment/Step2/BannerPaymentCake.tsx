import Image from '@components/basic/Image/Image';
import React from 'react';
import TrackingPayment from '@tracking/functions/payment';
import isEmpty from 'lodash/isEmpty';
import { isMobile } from 'react-device-detect';

const trackingPayment = new TrackingPayment();
const BannerPaymentCake = ({ data }: any) => {
  const onHandleClick = () => {
    trackingPayment.promotionSelected();
    if (!isEmpty(data?.redirectLink)) {
      window.open(data.redirectLink, '_blank');
    }
  };
  if (isMobile) {
    return (
      <div className="cursor-pointer" onClick={onHandleClick}>
        <div className="relative">
          <div className="relative flex items-center justify-center w-full z-[1] h-full">
            <Image className="w-full" src={data?.imageUrl} alt="Campaign" notWebp />
          </div>
          <div className="flex items-center absolute top-1/2 left-1/2 transform -translate-y-1/2 -translate-x-1/2 w-full z-[2] h-full">
            <div
              className="text-white !text-[9.98px] font-medium ml-[27%] pl-[17px] pr-[7%] leading-[140%] mb-1"
              dangerouslySetInnerHTML={{ __html: data?.displayText }}
            />
          </div>
        </div>
      </div>
    );
  }
  return (
    <div className="row-span-1 p-0 !border-none cursor-pointer" onClick={onHandleClick}>
      <div className="relative h-full w-full">
        <div className="relative flex items-center justify-center w-full z-[1] h-full">
          <Image className="w-full h-auto" src={data?.imageUrl} alt="Campaign" notWebp />
        </div>
        <div className="flex items-center absolute top-1/2 left-1/2 transform -translate-y-1/2 -translate-x-1/2 w-full z-[2] h-full">
          <div
            className="text-white !text-[14px] font-medium ml-[27%] pl-[17px] pr-[7%] leading-[140%] mb-1"
            dangerouslySetInnerHTML={{ __html: data?.displayText }}
          />
        </div>
      </div>
    </div>
  );
};
export default BannerPaymentCake;
