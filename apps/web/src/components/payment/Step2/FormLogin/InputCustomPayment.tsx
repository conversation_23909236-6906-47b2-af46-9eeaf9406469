import styles from '@components/payment/Step2/Step2.module.scss';
import SvgIcon from '@components/basic/Icon/SvgIcon';
import React, { useEffect, useRef, useState } from 'react';
import { TYPE_INPUT } from '@constants/types';
import useClickOutside from '@hooks/useClickOutside';
import classNames from 'classnames';
import { TEXT } from '@constants/text';

const InputCustomPayment = ({
  id,
  type,
  placeholder,
  iconSvg,
  error,
  onHandleDataLogin,
  setError,
  ...rest
}: any) => {
  const containerRef = useRef<any>(null);
  const inputRef = useRef<any>(null);
  const [warning, setWarning] = useState<any>('');
  const [isFocusInput, setIsFocusInput] = useState(false);
  const classContainer = classNames(
    styles.loginPaymentFormInput,
    warning && isFocusInput && styles.loginPaymentHasWarning,
    error && '!border-[#E74C3C]'
  );

  const handleChange = (e: any) => {
    const value = e?.target?.value;
    if (type === 'phone') {
      if (typeof onHandleDataLogin === 'function') onHandleDataLogin({ type, valuePhone: value });
    } else if (typeof onHandleDataLogin === 'function') {
      onHandleDataLogin({ type, valuePassword: value });
    }
  };
  const onFocusInput = () => {
    setIsFocusInput(true);
    if (error) {
      setError('');
    }
  };

  const handleClickOutside = () => {
    setWarning('');
    if (inputRef.current) {
      if (!inputRef.current.value) {
        inputRef.current.blur();
      }
    }
  };

  useClickOutside(containerRef, handleClickOutside);

  useEffect(() => {
    if (type === TYPE_INPUT.PASSWORD) {
      inputRef.current?.addEventListener('click', (event: any) => {
        if (typeof event?.getModifierState === 'function' && event.getModifierState('CapsLock')) {
          setWarning(TEXT.CHECK_CAPS_LOCK);
          return;
        }
        setWarning('');
      });
      inputRef.current?.addEventListener('keyup', (event: any) => {
        if (typeof event?.getModifierState === 'function' && event.getModifierState('CapsLock')) {
          setWarning(TEXT.CHECK_CAPS_LOCK);
          return;
        }
        setWarning('');
      });
    }
  }, [type]);

  useEffect(() => {
    if (error && containerRef) {
      window.scrollTo({
        top: containerRef.current.offsetTop - 100,
        behavior: 'smooth'
      });
    }
  }, [error]);

  return (
    <>
      <div className={classContainer} ref={containerRef}>
        <div>
          <SvgIcon type={iconSvg} error={error} />
        </div>
        <input
          className={classNames(
            styles.loginPaymentInput,
            'outline-none',
            error && '!text-[#E74C3C]',
            error && styles.loginPaymentErrorInput
          )}
          id={id}
          type={type}
          placeholder={placeholder}
          autoComplete="new-password"
          {...rest}
          onFocus={onFocusInput}
          onChange={handleChange}
          ref={inputRef}
          name={id}
          role="presentation"
        />
      </div>
      {error && type === 'password' && (
        <div className="text-[#ED0000] !text-[10px] font-normal">{TEXT.ERROR_LOGIN_PAYMENT}</div>
      )}
      {warning && <div className={styles.loginPaymentHasWarning}>{warning}</div>}
    </>
  );
};
export default InputCustomPayment;
