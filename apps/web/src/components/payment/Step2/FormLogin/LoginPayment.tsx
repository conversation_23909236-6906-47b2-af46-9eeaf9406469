import { TEXT } from '@constants/text';
import React, { useEffect } from 'react';
import { ICON_KEY, PAGE, PROFILE_STATUS } from '@constants/constants';
import SvgIcon from '@components/basic/Icon/SvgIcon';
import { encodeParamDestination } from '@helpers/common';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import { useRouter } from 'next/router';
import classNames from 'classnames';
import { useSelector } from 'react-redux';
import { get } from 'lodash';
import InputCustomPayment from './InputCustomPayment';
import styles from '../Step2.module.scss';
import TrackingPayment from '@tracking/functions/payment';

const LoginPayment = ({ onHandleDataLogin, isTVOD, error, setError, loginFormRef }: any) => {
  const router = useRouter();
  const { dataLogin } = useSelector((state: any) => state?.GlobalAuth || {});
  const onHandleRegister = () => {
    localStorage.setItem('currentAuthFlow', 'register_for_payment');
    const remakeDestination = encodeParamDestination(router?.asPath);
    router.push(
      `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${
        isTVOD ? TYPE_TRIGGER_AUTH.PAYMENT_TVOD : TYPE_TRIGGER_AUTH.PAYMENT
      }`
    );
    const trackingPayment = new TrackingPayment();
    trackingPayment.signUpButtonSelectedInPaymentScreen();
  };

  useEffect(() => {
    const dataProfile = get(dataLogin, 'data.profile', null);
    const profileStatus = get(dataProfile, 'status', '');
    if (profileStatus === PROFILE_STATUS.WAIT_FOR_DELETE) {
      setError(TEXT.ERROR_LOGIN_PAYMENT);
    }
  }, [dataLogin]);

  return (
    <div className={styles.loginPayment} ref={loginFormRef}>
      <h2 className="title m-b">{TEXT.INFO_LOGIN}</h2>
      <div className={styles.loginPaymentdes}>{TEXT.LOGIN_PAYMENT_DES}</div>
      <div className="space-y-2">
        <InputCustomPayment
          id="phone"
          type="phone"
          placeholder={TEXT.USER_LABEL.PHONE_NUMBER}
          iconSvg={ICON_KEY.PHONE_PAYMENT}
          onHandleDataLogin={onHandleDataLogin}
          error={error}
          setError={setError}
        />
        <InputCustomPayment
          id="password"
          type="password"
          placeholder={TEXT.USER_LABEL.IMPORT_PASSWORD}
          iconSvg={ICON_KEY.PASSWORD_PAYMENT}
          error={error}
          onHandleDataLogin={onHandleDataLogin}
          setError={setError}
        />
      </div>
      <div className="flex items-center space-x-2 lg:space-x-4">
        <div className={styles.loginPaymentText}>{TEXT.NOT_ACCOUNT}</div>
        <div
          className={classNames(styles.loginPaymentText, styles.loginPaymentTextRegister)}
          onClick={onHandleRegister}
        >
          {TEXT.REGISTER_NOW}
          <span className="ml-2">
            <SvgIcon type={ICON_KEY.REGISTER_PAYMENT} />
          </span>
        </div>
      </div>
    </div>
  );
};
export default LoginPayment;
