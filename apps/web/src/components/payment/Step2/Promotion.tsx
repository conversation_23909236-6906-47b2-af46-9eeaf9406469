import React, { useEffect, useState } from 'react';
import { ICON_KEY, PAGE } from '@constants/constants';
import { TEXT } from '@constants/text';
import { useDispatch, useSelector } from 'react-redux';
import {
  resetPromotionData,
  getPromotionDetail,
  setPromotionCode,
  statusLoginPayment
} from '@actions/payment';
import { useVieRouter } from '@customHook';
import { encodeParamDestination } from '@helpers/common';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import Input from '@components/basic/Input/Input';
import Button from '@components/basic/Buttons/Button';
import SvgIcon from '@components/basic/Icon/SvgIcon';
import styles from './Step2.module.scss';
import classNames from 'classnames';
import { segmentEvent } from '@tracking/TrackingSegment';
import { NAME, PROPERTY, VALUE } from '@config/ConfigSegment';

const Promotion = ({ onHandleLogin, newTermSelected }: any) => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const { query } = router || {};

  const { promotionData, selectedMethod, selectedTerm, isLoginPromotionSuccess, isRecurring } =
    useSelector((state: any) => state?.Payment) || {};
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const profile = useSelector((state: any) => state?.Profile?.profile);

  const [valuePromotion, setValuePromotion] = useState<any>('');
  const [inputError, setInputError] = useState<any>('');

  const { valid, used, errorMessage, promotionError, promotionCode } = promotionData || {};
  const isInputDisabled = !selectedMethod?.isPromotionCode;

  const isPromotionApplied = valid === 1 && used === 0 && promotionCode;
  const isPromotionInvalid = valid === 0 && !used;

  useEffect(() => {
    if (profile?.id) {
      if (isPromotionInvalid) {
        setInputError(errorMessage || promotionError);
      } else if (used === 1) {
        setInputError(TEXT.GIFT_CODE_USED);
      } else if (isPromotionApplied) {
        setInputError('Mã khuyến mãi áp dụng thành công');
      } else {
        setInputError('');
      }
    }
  }, [errorMessage, promotionError, valid, used, profile?.id]);

  useEffect(() => {
    if (isInputDisabled) {
      dispatch(resetPromotionData());
    } else if (
      query?.promotionCode &&
      (newTermSelected?.id || selectedTerm?.id) === parseInt(query?.termId) &&
      profile?.id
    ) {
      onApplyPromotion();
    }
  }, [newTermSelected?.id, selectedTerm?.id, isInputDisabled]);

  useEffect(() => {
    if (query?.promotionCode && !profile?.id) {
      setValuePromotion(query?.promotionCode);
      dispatch(setPromotionCode(query?.promotionCode));
    }
    return () => {
      setInputError('');
    };
  }, [query?.promotionCode]);

  useEffect(() => {
    if (profile?.id && isLoginPromotionSuccess && !isGlobal) {
      onApplyPromotion();
    }
    return () => {
      if (isLoginPromotionSuccess) dispatch(statusLoginPayment(false));
    };
  }, [profile?.id, isLoginPromotionSuccess]);

  const handleChange = (e: any) => {
    const value = (e?.target?.value || '').toUpperCase();
    if (!/\s/.test(value)) {
      setValuePromotion(value);
      dispatch(setPromotionCode(value));
    }
  };

  useEffect(() => {
    dispatch(resetPromotionData());
  }, [isRecurring]);

  const onApplyPromotion = () => {
    if (!profile?.id) {
      if (typeof onHandleLogin === 'function' && !isGlobal) {
        onHandleLogin({ isPromotionCode: true });
        return;
      }
      const destination = encodeParamDestination(router?.asPath);
      router.push(
        `${PAGE.AUTH}/?destination=${destination}&page=${router?.pathname}&trigger=${TYPE_TRIGGER_AUTH.INPUT_PROMOTION}`
      );
      return;
    }

    if (isPromotionApplied) {
      dispatch(resetPromotionData());
      setValuePromotion('');
    } else if (isPromotionInvalid) {
      setValuePromotion('');
      setInputError('');
      dispatch(resetPromotionData());
    } else {
      if (promotionCode || valuePromotion || router?.query?.promotionCode) {
        dispatch(
          getPromotionDetail({
            packageId: newTermSelected?.id || selectedTerm?.id,
            promotionCode: promotionCode || valuePromotion || router?.query?.promotionCode,
            selectedMethod,
            userId: profile?.id
          })
        );
      }
    }
  };

  useEffect(() => {
    if (inputError) {
      segmentEvent(NAME.VOUCHER_CODE_INPUTTED, {
        [PROPERTY.CURRENT_PAGE]: window.location.href,
        [PROPERTY.VOUCHER_CODE]: promotionCode || valuePromotion || '',
        [PROPERTY.CAUSE_FOR_FAILURE]: inputError || '',
        [PROPERTY.RESULT]: isPromotionApplied ? VALUE.SUCCESS : VALUE.FAIL,
        [PROPERTY.VOUCHER_TYPE]: 'voucher'
      });
    }
  }, [inputError]);

  return (
    <form
      onSubmit={(e) => {
        // TODO: when Enter key active, that will error isValueToBeTrackedFromTrackingList not define in AIActive SDK. That is reason to block submit with Enter
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      <div
        className={classNames(
          styles.promotion,
          isPromotionApplied && styles.success,
          isPromotionInvalid && styles.error
        )}
      >
        <Input
          placeholder={TEXT.INPUT_PROMOTION_CODE}
          title={TEXT.INPUT_PROMOTION_CODE}
          inputClass={styles.input}
          handleOnChange={handleChange}
          valInput={promotionCode || valuePromotion}
          icon={
            <SvgIcon
              type={ICON_KEY.VOUCHER_PAYMENT}
              error={isPromotionInvalid}
              isActive={isPromotionApplied}
            />
          }
          id="promotion-code"
        />

        <Button
          title={
            isPromotionApplied ? TEXT.CANCEL : isPromotionInvalid ? TEXT.RE_TYPE : TEXT.CONFIRM
          }
          subTitle={
            isPromotionApplied ? TEXT.CANCEL : isPromotionInvalid ? TEXT.RE_TYPE : TEXT.CONFIRM
          }
          className={styles.button}
          onClick={onApplyPromotion}
          disabled={!(promotionCode || valuePromotion)}
        />
      </div>

      {inputError && (
        <div
          className={classNames(
            '!text-sm pt-2',
            isPromotionInvalid && 'text-[#E3513B]',
            isPromotionApplied && 'text-[#28D428]'
          )}
        >
          {inputError}
        </div>
      )}
    </form>
  );
};

export default React.memo(Promotion);
