import React, { useEffect, useRef, useState } from 'react';
import { EL_ID } from '@constants/constants';
import { TEXT } from '@constants/text';
import { useDispatch, useSelector } from 'react-redux';
import { setReferralValue, resetValueReferralCode } from '@actions/payment';
import PaymentApi from '@apis/Payment';
import InputLabelSlideUp from '../../basic/Input/InputLabelSlideUp';

const ReferralCode = () => {
  const dispatch = useDispatch();
  const ref = useRef<any>(null);
  const { valueReferralCode } = useSelector((state: any) => state?.Payment) || {};
  const [checkValid, setCheckValid] = useState(false);
  const [inputError, setInputError] = useState<any>('');
  const [width, setWidth] = useState(0);

  useEffect(() => {
    setWidth(ref?.current?.offsetWidth);
  }, [valueReferralCode]);

  const onChange = (e: any) => {
    const value = ((e?.target?.value || '').trim() || '').toUpperCase();
    const inValid = /\s/;
    const isSpace = inValid.test(value);
    if (!isSpace) {
      dispatch(setReferralValue(value));
    }
    if (value !== valueReferralCode) {
      setCheckValid(false);
    }
  };

  const onClick = () => {
    if (checkValid) {
      setCheckValid(false);
      dispatch(resetValueReferralCode());
    } else {
      PaymentApi.checkReferralValid(valueReferralCode).then((res) => {
        if (res?.isValid) {
          setCheckValid(true);
          setInputError('');
        } else {
          setInputError(TEXT.REFERRAL_CODE_ERROR);
          setCheckValid(false);
        }
      });
    }
  };

  const onFocus = () => {
    setInputError('');
  };

  return (
    <div className="form round-2 formReferralCode border-st3">
      <InputLabelSlideUp
        disabledBtn={!valueReferralCode}
        value={valueReferralCode || ''}
        onChange={onChange}
        id={EL_ID.REFERRAL_CODE_INPUT}
        autoComplete="cc-number"
        title={TEXT.REFERRAL_CODE}
        placeholder={TEXT.SEVEN_CHARACTER}
        error={inputError || ''}
        hasBtn
        titleBtn={!checkValid ? TEXT.CONFIRM : TEXT.CANCEL}
        onSubmit={onClick}
        onFocus={onFocus}
        onEnter={onClick}
        width={width}
        forwardRef={ref}
        iconTick={checkValid && valueReferralCode}
        keyLimit={7}
      />
      <div className="text text-muted small">{TEXT.REFERRAL_CODE_OPTIONAL}</div>
    </div>
  );
};

export default React.memo(ReferralCode);
