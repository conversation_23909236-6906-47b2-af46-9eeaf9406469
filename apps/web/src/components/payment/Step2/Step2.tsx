import React, { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import filter from 'lodash/filter';
import { setLoading, setToast } from '@actions/app';
import * as MocaAction from '@actions/moca';
import * as ShopeePayAction from '@actions/shopeepay';
import * as NapasAction from '@actions/napas';
import { openPopup } from '@actions/popup';
import * as ViettelPayAction from '@actions/viettelPay';
import * as MomoAction from '@actions/momo';
import PaymentApi from '@apis/Payment';
import {
  CURRENCY,
  EL_ID,
  LOCATION,
  PAGE,
  PAYMENT_METHOD,
  PAYMENT_TYPE,
  POPUP,
  TVOD,
  USER_TYPE
} from '@constants/constants';
import ConfigGTM from '@config/ConfigGTM';
import LocalStorage from '@config/LocalStorage';
import { DOMAIN_WEB } from '@config/ConfigEnv';
import { TEXT } from '@constants/text';
import TrackingPayment from '@tracking/functions/payment';
import { handleGTMEvent } from '@tracking/TrackingGTM';
import { encodeParamDestination, handleScrollTop, queryStringEncoding } from '@helpers/common';
import { checkDataContentTVodToShowPopup } from '@services/tVod';
import TrackingApp from '@tracking/functions/TrackingApp';
import {
  checkLinkZaloPayTransaction,
  clearPVodInfo,
  clearPVodOffer,
  clearTVodInfo,
  clearTVodOffer,
  createTransactionSuccess,
  getListTokensSaved,
  selectBank,
  selectTerm,
  setRecurringStatusPayment,
  setSelectedMethod,
  setTemporaryData,
  statusLoginPayment
} from '@actions/payment';
import ReferralProgPayment from '@components/payment/Step2/ReferralProgPayment';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import BillPVod from '@components/payment/Step2/bill/BillPVod';
import DetailApi from '@apis/detailApi';
import LoginPayment from '@components/payment/Step2/FormLogin/LoginPayment';
import { globalLogin, resetGlobalAuth } from '@actions/globalAuth';
import classNames from 'classnames';
import Bill from './bill/Bill';
import BillTVod from './bill/BillTVod';
import AsiaPayCheckout from './checkoutFunctions/asiapay';
import ZaloPayCheckout from './checkoutFunctions/zalopay';
import VNPayCheckout from './checkoutFunctions/vnpay';
import NapasCheckout from './checkoutFunctions/napas';
import SeoText from '../../seo/SeoText';
import TermPackage from './TermPackage';
import Method from './Method';
import debounce from 'lodash/debounce';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import { ACTION_TYPE, createAction } from '@/actions/actionType';

const trackingPayment = new TrackingPayment();
let totalAmount = 0;

const getTransformedTerm = (term: any, isRecurringFlag: any, isMethodInList = true) => {
  if (!term) return null;
  if (!term.recurring) return term;

  if (!isMethodInList) {
    return term?.noneRecurring?.id ? { ...term.noneRecurring, recurringData: term } : term;
  }

  if (isRecurringFlag) {
    return term?.noneRecurring?.id
      ? { ...term, noneRecurring: term.noneRecurring, termGrouped: true }
      : term;
  } else {
    return term?.noneRecurring?.id
      ? { ...term.noneRecurring, recurringData: term, termGrouped: true }
      : term;
  }
};

const Step2 = () => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const {
    listMethodsConfig,
    selectedMethod,
    selectedPackage,
    promotionData,
    selectedTerm,
    bank,
    cardInfo,
    cardInfoCake,
    zaloPayLinked,
    tokenSelected,
    tvodInfo,
    tvodOffer,
    pvodInfo,
    pvodOffer,
    valueReferralCode,
    dataCampaign,
    billingInfo,
    isLoginPaymentSuccess
  } = useSelector((state: any) => state?.Payment || {});
  const [isNotSelectBank, setIsSelectBank] = useState(false);
  const [contentData, setContentData] = useState<any>(null);
  const [error, setError] = useState<any>('');
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const { isMobile, geoCheck, deviceModel, deviceName, deviceType, deviceId } = useSelector(
    (state: any) => state?.App || {}
  );
  const dataSEOAllPage = useSelector((state: any) => state?.Page?.dataSEOAllPage || {});
  const { referralProg } = useSelector((state: any) => state?.App.webConfig) || {};
  const { enableReferral } = referralProg || {};
  const { checkFirstPay, isRecurring } = useSelector((state: any) => state?.Payment) || {};
  const { totalPrice } = tvodOffer || {};
  const isGeoCheckValid = geoCheck?.geo_country === LOCATION.VIETNAM && geoCheck?.geo_valid;
  const { pathname, query, asPath } = router || {};
  const { isSimulcast, isLiveEvent, fromPrice } = query || {};
  const isRentalContent = useMemo(() => (pathname || '').includes(PAGE.RENTAL_CONTENT), [pathname]);
  const isPvodContent = useMemo(() => (pathname || '').includes(PAGE.PVOD_CONTENT), [pathname]);
  const isInApp = useMemo(() => (pathname || '').includes(PAGE.INAPP), [pathname]);
  const inAppZaloPay = useMemo(() => (pathname || '').includes(PAGE.ZALOPAY), [pathname]);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const [dataLogin, setDataLogin] = useState({
    userName: '',
    password: ''
  });
  const loginRef = useRef<any>(null);
  const [isFixed, setIsFixed] = useState(false);
  const [newTermSelected, setNewTermSelected] = useState(() =>
    getTransformedTerm(selectedTerm, isRecurring, true)
  );
  const [isNotAccessPackage, setIsNotAccessPackage] = useState<any>(null);

  useLayoutEffect(() => {
    const newTerm = getTransformedTerm(selectedTerm, isRecurring, true);
    setNewTermSelected(newTerm);
  }, [selectedTerm, isRecurring]);

  const sectionRef = useRef<any>(null);

  const isShowCampaign = useMemo(() => {
    if (selectedPackage?.id && !isEmpty(dataCampaign)) {
      return dataCampaign.some(
        (item: any) =>
          Array.isArray(item.packageGroupIds) && !item.packageGroupIds.includes(selectedPackage.id)
      );
    }
    return false;
  }, [selectedPackage?.id, dataCampaign]);

  const methodData = useMemo(() => {
    let methods = listMethodsConfig || [];

    if (isEmpty(dataCampaign) || !isShowCampaign) {
      methods = methods.filter((item: any) => item?.id !== PAYMENT_METHOD.CAKE);
    }

    if (inAppZaloPay) {
      return methods.filter((item: any) => item.id === PAYMENT_METHOD.ZALO_PAY);
    }
    if (isRentalContent) {
      return methods.filter((item: any) => item?.isRental);
    }
    if (isPvodContent) {
      return methods.filter((item: any) => item?.isOnPvod);
    }

    return methods.filter(
      (item: any) => (item?.notPassUAT || []).indexOf(PAYMENT_TYPE.SVOD) === -1
    );
  }, [
    listMethodsConfig,
    dataCampaign,
    isShowCampaign,
    inAppZaloPay,
    isRentalContent,
    isPvodContent
  ]);

  const isCheckFirstPay = useMemo(() => checkFirstPay?.isFirstPay, [checkFirstPay]);

  const checkPackageConfig = useMemo(
    () => referralProg?.package.findIndex((item: any) => item?.id === selectedPackage?.id) !== -1,
    [selectedPackage, referralProg?.package]
  );

  const [checkoutClicked, setCheckoutClicked] = useState(false);

  useEffect(() => {
    // handle tracking payment not login
    if (checkoutClicked && !profile?.id && error) {
      trackingPayment.payButtonSelected({
        selectedTerm,
        promotionData,
        selectedMethod,
        bankCode: bank?.code,
        isSegmentedUser: selectedTerm?.id === parseInt(query?.termId),
        trackingError:
          error === TEXT.PASSWORD_REQUIRED_VALUE
            ? 'Short Password Error'
            : error === TEXT.ERROR_LOGIN_PAYMENT
            ? 'Incorrect Login Information'
            : 'System Error'
      });
      setCheckoutClicked(false);
    }
  }, [error, checkoutClicked]);

  useEffect(() => {
    if (isMobile || profile?.id) {
      setIsFixed(false);
      return;
    }

    const handleScroll = debounce(() => {
      const scrollPosition = window.scrollY;
      const threshold = 200;

      if (sectionRef.current) {
        const containerRect = sectionRef.current.getBoundingClientRect();
        const containerBottom = containerRect.bottom;
        const windowHeight = window.innerHeight;
        const offset = 90;

        setIsFixed(scrollPosition > threshold && containerBottom > windowHeight + offset);
      }
    }, 100);

    handleScroll();
    window.addEventListener('scroll', handleScroll);

    return () => {
      handleScroll.cancel();
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isMobile, profile?.id]);

  useEffect(() => {
    handleScrollTop();

    return () => {
      dispatch(clearTVodInfo());
      dispatch(clearTVodOffer());
      dispatch(clearPVodInfo());
      dispatch(clearPVodOffer());
      dispatch(selectTerm(null));
    };
  }, []);
  useEffect(() => {
    if (!profile?.id || !isLoginPaymentSuccess || isGlobal) return;

    if (profile?.isPremium && query?.isTSvod === 'true') {
      router.push(`${PAGE.LOBBY_PROFILES}/?destination=${query?.slugTvod || '/'}&page=${PAGE.VOD}`);
      return;
    }

    const checkAccess = async () => {
      const result = await checkAccessTVodPackage();
      setIsNotAccessPackage(result);
    };

    if (
      !(profile?.isPremium || profile?.type === USER_TYPE.VIP) ||
      (contentData?.isPremium && !contentData?.isSvodTvod) ||
      (contentData?.isSvodTvod && !(profile?.isPremium || profile?.type === USER_TYPE.VIP)) ||
      isEmpty(contentData) ||
      isRentalContent
    ) {
      checkAccess();
    }

    return () => {
      if (isLoginPaymentSuccess) {
        dispatch(statusLoginPayment(false));
      }
    };
  }, [
    profile?.id,
    profile?.isPremium,
    profile?.type,
    isLoginPaymentSuccess,
    isGlobal,
    contentData,
    dispatch,
    isRentalContent,
    router,
    query?.isTSvod,
    query?.slugTvod
  ]);

  useEffect(() => {
    if (isNotAccessPackage === false) {
      onCheckOut();
    }
  }, [isNotAccessPackage]);

  useEffect(() => {
    if (!selectedTerm?.id || !methodData?.length) return;

    const promotionCode = router?.query?.promotionCode;

    let currentMethod;
    if (
      selectedMethod &&
      Array.isArray(selectedMethod.enableTermList) &&
      selectedMethod.enableTermList.includes(selectedTerm.id)
    ) {
      currentMethod = selectedMethod;
    } else {
      currentMethod = methodData.find(
        (method: any) =>
          Array.isArray(method.enableTermList) && method.enableTermList.includes(selectedTerm.id)
      );
    }

    const supportsTerm =
      currentMethod &&
      Array.isArray(currentMethod.enableTermList) &&
      currentMethod.enableTermList.includes(selectedTerm.id);

    let forcedTerm;
    if (supportsTerm) {
      forcedTerm = getTransformedTerm(selectedTerm, true, true);
      dispatch(setRecurringStatusPayment(true));
    } else {
      forcedTerm = getTransformedTerm(selectedTerm, false, false);
      dispatch(setRecurringStatusPayment(false));
    }

    setNewTermSelected(forcedTerm);
    dispatch(setSelectedMethod(currentMethod, promotionCode));
    return () => {
      dispatch(setSelectedMethod({}, promotionCode));
    };
  }, [selectedTerm?.id, methodData, router?.query?.promotionCode, dispatch, selectedTerm]);

  const onSelectMethod = (method: any) => {
    if (!isEmpty(method)) {
      const promotionCode = router?.query?.promotionCode;
      if (selectedTerm?.id && Array.isArray(method.enableTermList)) {
        if (method.enableTermList.includes(selectedTerm.id)) {
          const forcedRecurring = getTransformedTerm(selectedTerm, true, true);
          setNewTermSelected(forcedRecurring);
          dispatch(setRecurringStatusPayment(true));
        } else {
          const forcedNonRecurring = getTransformedTerm(selectedTerm, false, false);
          setNewTermSelected(forcedNonRecurring);
          dispatch(setRecurringStatusPayment(false));
        }
      }
      dispatch(setSelectedMethod(method, promotionCode));
    }
  };

  const onSelectTerm = (term: any) => {
    if (term?.id === selectedTerm?.id) return;

    if (!isEmpty(term)) {
      dispatch(selectTerm(term));

      if (methodData && methodData.length > 0 && term.id) {
        let currentMethod;
        if (
          selectedMethod &&
          Array.isArray(selectedMethod.enableTermList) &&
          selectedMethod.enableTermList.includes(term.id)
        ) {
          currentMethod = selectedMethod;
        } else {
          currentMethod = methodData.find(
            (method: any) =>
              Array.isArray(method.enableTermList) && method.enableTermList.includes(term.id)
          );
        }

        if (currentMethod) {
          const forcedRecurring = getTransformedTerm(term, true, true);
          setNewTermSelected(forcedRecurring);
          dispatch(setRecurringStatusPayment(true));
          dispatch(setSelectedMethod(currentMethod, router?.query?.promotionCode));
        } else {
          const forcedNonRecurring = getTransformedTerm(term, false, false);
          setNewTermSelected(forcedNonRecurring);
          dispatch(setRecurringStatusPayment(false));
          dispatch(setSelectedMethod({}, router?.query?.promotionCode));
        }
      } else {
        const forcedRecurring = getTransformedTerm(term, true, true);
        setNewTermSelected(forcedRecurring);
        dispatch(setRecurringStatusPayment(true));
      }
    }
  };

  useEffect(() => {
    if (!isEmpty(selectedPackage)) {
      const listTerms = get(selectedPackage, 'terms', []);
      const listTermsDiscount = filter(listTerms, ['discount', true]);
      const listTermsRecurring = filter(listTerms, ['recurring', true]);
      const itemTermSegmentUser = filter(listTerms, ['id', parseInt(query?.termId)]);

      const selectedTerm =
        itemTermSegmentUser?.[0] ||
        listTermsDiscount?.[0] ||
        listTermsRecurring?.[0] ||
        listTerms?.[0];

      if (selectedTerm) {
        if (!isEmpty(itemTermSegmentUser)) {
          dispatch(selectTerm(selectedTerm, query?.promotionCode));
        } else {
          dispatch(selectTerm(selectedTerm));
        }
      }
    }
  }, [selectedPackage]);

  useEffect(() => {
    if (profile?.id) {
      handleLinkedSupport();
    }
  }, [selectedMethod, profile?.id]);

  /**
   * This function checks the TVOD (Transactional Video on Demand) information and initiates the purchase process if conditions are met.
   *
   * @param {Object} tvodInfo - The TVOD information object. This object contains details about the TVOD product.
   * @param {Object} tvodOffer - The TVOD offer object. This object contains details about the TVOD offer.
   * @param {number} totalPrice - The total price of the TVOD offer.
   *
   * @returns {void}
   */
  const checkTVodInfo = (tvodInfo: any, tvodOffer: any, totalPrice: any) => {
    // Check if the TVOD product is stopped from being sold
    const isStopSell = get(tvodInfo, 'errorCode', 0) === 106;
    // Get the TVOD product ID from the TVOD information
    const idInfoTVodProduct = get(tvodInfo, 'bizInfo.tvodProductId', '');
    // Get the TVOD product ID from the TVOD offer
    const idOfferTVodProduct = get(tvodOffer, 'tvodProductId', '');

    // If the TVOD information is not empty, the TVOD product is not stopped from being sold,
    // the total price is greater than 0, and the TVOD product ID from the TVOD information matches the TVOD product ID from the TVOD offer,
    // then initiate the purchase process

    if (
      !isEmpty(tvodInfo) &&
      !isStopSell &&
      totalPrice > 0 &&
      idOfferTVodProduct === idInfoTVodProduct
    ) {
      if (profile?.isPremium && query?.isTSvod === 'true') {
        return;
      }
      if (
        !(profile?.isPremium || profile?.type === USER_TYPE.VIP) ||
        (contentData?.isPremium && !contentData?.isSvodTvod) ||
        (contentData?.isSvodTvod && !(profile?.isPremium || profile?.type === USER_TYPE.VIP)) ||
        isEmpty(contentData) ||
        isRentalContent
      ) {
        checkAccessTVodPackage();
      }
    }
  };

  /**
   * This useEffect hook is responsible for checking the TVOD (Transactional Video on Demand) information.
   * It calls the `checkTVodInfo` function whenever the `tvodInfo` or `totalPrice` changes.
   *
   * @function useEffect
   * @listens {tvodInfo} - The TVOD information object. This object contains details about the TVOD product.
   * @listens {totalPrice} - The total price of the TVOD offer.
   *
   * @callback checkTVodInfo - This function is called with the current `tvodInfo`, `tvodOffer`, and `totalPrice` as parameters.
   * It checks if the TVOD product is available for purchase and if so, it initiates the purchase process.
   */
  useEffect(() => {
    checkTVodInfo(tvodInfo, tvodOffer, totalPrice);
  }, [tvodInfo, totalPrice]);

  useEffect(() => {
    dispatch(selectBank(null));
    setIsSelectBank(false);
  }, [selectedMethod]);

  useEffect(() => {
    if (!isEmpty(bank) && isNotSelectBank) {
      setIsSelectBank(false);
    }
  }, [bank, isNotSelectBank]);

  const handleLinkedSupport = () => {
    switch (selectedMethod?.id) {
      case PAYMENT_METHOD.ZALO_PAY:
        dispatch(checkLinkZaloPayTransaction());
        break;
      case PAYMENT_METHOD.NAPAS:
      case PAYMENT_METHOD.SHOPEE_PAY: {
        let paymentMethod = 'Card';
        let paymentService = '';
        if (selectedMethod?.id === PAYMENT_METHOD.SHOPEE_PAY) {
          paymentMethod = 'WALLET';
          paymentService = PAYMENT_METHOD.SHOPEE_PAY;
        }
        dispatch(
          getListTokensSaved({
            paymentMethod,
            paymentService
          })
        );
        break;
      }
      default:
        break;
    }
  };

  const onHandleLogin = ({ isLoginPayment, isPromotionCode }: any) => {
    if (error) {
      scrollToLoginForm();
    }
    if (dataLogin.password && dataLogin.password.length < 6) {
      setError(TEXT.PASSWORD_REQUIRED_VALUE);
    } else if (!dataLogin.userName || !dataLogin.password) {
      setError(TEXT.ERROR_LOGIN_PAYMENT);
    } else {
      dispatch(
        globalLogin({
          userName: dataLogin?.userName,
          password: dataLogin.password,
          model: deviceModel,
          countryCode: 'VN',
          deviceName,
          deviceType,
          deviceId,
          isLoginPayment,
          isPromotionCode,
          setError
        })
      );
    }
  };

  const onHandleDataLogin = ({ type, valuePhone, valuePassword }: any) => {
    if (type === 'phone') setDataLogin({ ...dataLogin, userName: valuePhone });
    else setDataLogin({ ...dataLogin, password: valuePassword });
  };

  // Handle checkout payment when click thanhtoan button
  const onCheckOut = async (isTVOD?: any) => {
    setCheckoutClicked(true);
    if (!profile?.id) {
      if (!isGlobal) {
        onHandleLogin({ isLoginPayment: true });
      } else {
        const remakeDestination = await encodeParamDestination(router?.asPath);
        router.push(
          `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${
            isTVOD ? TYPE_TRIGGER_AUTH.PAYMENT_TVOD : TYPE_TRIGGER_AUTH.PAYMENT
          }`
        );
      }
      return;
    }
    if (!isGeoCheckValid) {
      dispatch(
        openPopup({
          name: POPUP.NAME.BLOCK_BUY_PACKAGE_FOREIGN_USERS
        })
      );
      return;
    }
    if (
      (selectedMethod?.id === PAYMENT_METHOD.VN_PAY && isEmpty(bank)) ||
      (selectedMethod?.id === PAYMENT_METHOD.QR_VNPAY && isEmpty(bank))
    ) {
      setIsSelectBank(true);
      return;
    }
    // GTM PAYMENT_FORM
    handleGTMEvent({
      eventName: ConfigGTM.PAYMENT_FORM,
      userProfile: profile,
      query,
      params: {
        m_PackageId: selectedPackage?.id || 0,
        m_PackageName: selectedPackage?.name || '',
        m_Duration: selectedTerm?.name || '',
        m_Amount: selectedTerm?.price || 0,
        m_PaymentMethod: selectedMethod?.id || '',
        m_Currency: CURRENCY.VND
      }
    });

    if (isRentalContent) {
      if (profile?.isPremium && query?.isTSvod === 'true') {
        await router.push(
          `${PAGE.LOBBY_PROFILES}/?destination=${query?.slugTvod || '/'}&page=${PAGE.VOD}`
        );
      } else {
        await handleCheckoutTVod(tvodInfo, tvodOffer);
      }
    } else if (isPvodContent) {
      await handleCheckoutPVod(pvodInfo, pvodOffer);
    } else {
      const { isShowPopup } = await handlePrePayOverLap();
      if (!isShowPopup) {
        await createTransaction();
      }
    }
  };

  const handleCheckout = async (info: any, offer: any, checkAccessPackage: any) => {
    const isNotAccessPackage = await checkAccessPackage(
      {
        ...info,
        isGlobal
      },
      offer,
      true
    );
    if (!isNotAccessPackage) {
      await createTransaction();
    }
  };

  const handleCheckoutPVod = (infoPVod: any, offerPVod: any) =>
    handleCheckout(infoPVod, offerPVod, checkAccessPVodPackage);

  const handleCheckoutTVod = (infoTVod: any, offerTVod: any) =>
    handleCheckout(infoTVod, offerTVod, checkAccessTVodPackage);

  const handlePopupAboutTVodPackage = ({
    name,
    content,
    infoTVod,
    offerTVod,
    isNeedReloadOffer,
    isChangeToVOD,
    isFromPriceNotSameCurPrice,
    callbackCreateTransaction
  }: any) => {
    dispatch(
      openPopup({
        name,
        content,
        tvodInfo: infoTVod,
        tvodOffer: offerTVod,
        isNeedReloadOffer,
        isChangeToVOD,
        isFromPriceNotSameCurPrice,
        notClosedButton: true,
        callbackCreateTransaction
      })
    );
  };

  const checkAccessTVodPackage = async () => {
    if (!isRentalContent) return false;
    return await checkDataContentTVodToShowPopup({
      id: query?.id,
      infoTVod: tvodInfo,
      offerTVod: tvodOffer,
      isSimulcast: isSimulcast === 'true',
      isLiveEvent: isLiveEvent === 'true',
      isCheckOut: false,
      callback: handlePopupAboutTVodPackage,
      setContentData,
      callbackCreateTransaction: createTransaction
    });
  };

  const checkAccessPVodPackage = async () => {
    const pvodInfo = await PaymentApi.getPVodInfo({
      contentId: query?.id,
      contentType: query?.type
    });
    if (pvodInfo?.benefitInfo?.type === TVOD.USER_TYPE.EXPIRED || isEmpty(pvodInfo?.bizInfo)) {
      DetailApi.getContentById({ contentId: query?.id, isGlobal }).then((res: any) => {
        if (res?.success) {
          dispatch(
            openPopup({
              name: POPUP.NAME.PVOD_EXPIRED,
              pvodInfo,
              productNameMsg: res?.data?.title,
              notClosedButton: true
            })
          );
          return true;
        }
        return false;
      });
    }
    if (pvodInfo?.benefitInfo?.type === TVOD.USER_TYPE.RENTED) {
      dispatch(
        openPopup({
          name: POPUP.NAME.PVOD_OWNED,
          pvodInfo,
          notClosedButton: true
        })
      );
      return true;
    }
    return false;
  };

  const handlePrePayOverLap = async () => {
    const billingPermission = await PaymentApi.checkBillingPermission({
      termId: selectedTerm?.id
    });
    let popupData: any = billingPermission;
    const { prePayConfirmNeed, buyPermission } = billingPermission || {};
    let isPreventPayment = false;
    let isOverlap = false;
    let isAccumulate = false;
    let billingPermission2 = {};
    if (!buyPermission) {
      // "DENY"
      isPreventPayment = true;
    } else if (prePayConfirmNeed) {
      // POPUP OVERLAP
      isOverlap = true;
    } else {
      const checkAccumulate = await handlePrePayAccumulate();
      isPreventPayment = checkAccumulate?.isPreventPayment;
      isAccumulate = checkAccumulate?.isAccumulate;
      billingPermission2 = checkAccumulate?.billingPermission2;
      popupData = isPreventPayment || isAccumulate ? billingPermission2 : popupData;
    }
    if (isPreventPayment || isOverlap || isAccumulate) {
      dispatch(
        openPopup({
          name: POPUP.NAME.PRE_PAY,
          ...popupData,
          isOverlap,
          isPreventPayment,
          isAccumulate,
          onConfirm: isAccumulate
            ? createTransaction
            : isOverlap
            ? () => handlePrePayAccumulate(isOverlap)
            : () => {}
        })
      );
    }
    return { isShowPopup: isPreventPayment || isOverlap || isAccumulate };
  };

  const handlePrePayAccumulate = async (isOverlap?: any) => {
    let isPreventPayment = false;
    let isAccumulate = false;
    const billingPermission2 = await PaymentApi.checkBillingPermission2({
      termId: selectedTerm?.id
    });
    if (!billingPermission2?.isCallback) {
      if (!billingPermission2?.buyPermission) {
        // "DENY"
        isPreventPayment = true;
      } else if (billingPermission2?.prePayConfirmNeed) {
        // "ACCUMULATE"
        isAccumulate = true;
      } else if (isOverlap) {
        createTransaction();
      }
    } else if (isOverlap) {
      createTransaction();
    }
    if (isOverlap && (isPreventPayment || isAccumulate)) {
      dispatch(
        openPopup({
          name: POPUP.NAME.PRE_PAY,
          ...billingPermission2,
          isPreventPayment,
          isAccumulate,
          isOverlap: false,
          onConfirm: createTransaction
        })
      );
    }
    return {
      isPreventPayment,
      isAccumulate,
      billingPermission2
    };
  };

  // Create transaction all services
  const createTransaction = async () => {
    dispatch(openPopup());
    const recurring = newTermSelected?.recurring;
    const termId = newTermSelected?.id;
    const isSegmentedUser = termId === +query?.termId;
    let transactionData: any = null;
    const queryString = `?${queryStringEncoding(query)}`;
    const promotion = promotionData?.valid === 1 && promotionData?.used === 0 ? promotionData : {};
    if (isRentalContent) {
      dispatch(setLoading(true));
      const { tvodProductId } = tvodInfo?.bizInfo || '';
      const queryString = `?${queryStringEncoding(query)}`;
      const promotion =
        promotionData?.valid === 1 && promotionData?.used === 0 ? promotionData : {};

      const tvodTransaction = await PaymentApi.tvodTransaction({
        tvodProductId,
        paymentService: selectedMethod?.code,
        tokenId: selectedMethod?.id === PAYMENT_METHOD.NAPAS ? tokenSelected?.id : '',
        selectedMethod
      });
      if (!tvodTransaction?.success) {
        dispatch(
          setToast({
            message: tvodTransaction?.errorMessage || TEXT.MSG_ERROR
          })
        );
        dispatch(setLoading(false));
        return false;
      }
      let returnUrl = '';
      let cancelUrl = '';

      if (
        selectedMethod?.id === PAYMENT_METHOD.MOCA ||
        selectedMethod?.id === PAYMENT_METHOD.SHOPEE_PAY
      ) {
        returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}`;
      } else if (selectedMethod?.id === PAYMENT_METHOD.VIETTEL_PAY) {
        cancelUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}`;
        returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}`;
      } else if (
        selectedMethod?.id === PAYMENT_METHOD.VN_PAY ||
        selectedMethod?.id === PAYMENT_METHOD.QR_VNPAY ||
        selectedMethod?.id === PAYMENT_METHOD.MOMO
      ) {
        returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}${queryString}`;
      } else if (selectedMethod?.id === PAYMENT_METHOD.NAPAS) {
        returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}${queryString}&orderId=${tvodTransaction?.orderId}&merchantId=VIEON&method=${PAYMENT_METHOD.NAPAS}`;
      }

      dispatch(
        createTransactionSuccess({
          data: tvodTransaction,
          valueReferralCode
        })
      );
      const tvodTransactionPay = await PaymentApi.tvodTransactionPay({
        orderId: tvodTransaction?.orderId,
        bankCode: bank?.code || '',
        returnUrl,
        cancelUrl
      });
      if (!tvodTransactionPay?.success) {
        dispatch(
          setToast({
            message: tvodTransactionPay?.errorMessage || TEXT.MSG_ERROR
          })
        );
        dispatch(setLoading(false));
        return false;
      }
      switch (selectedMethod?.id) {
        case PAYMENT_METHOD.NAPAS: {
          transactionData = tvodTransactionPay?.napas || {};
          const returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}${queryString}&orderId=${transactionData?.orderId}&merchantId=VIEON&method=${PAYMENT_METHOD.NAPAS}`;
          const napasCheckout = new NapasCheckout({
            returnUrl,
            router,
            isRentalContent,
            transactionData,
            dispatch
          });
          await napasCheckout.handleNapas();
          dispatch(setLoading(false));
          break;
        }
        case PAYMENT_METHOD.ZALO_PAY: {
          transactionData = tvodTransactionPay?.zalopay;
          const checkoutZaloPay = new ZaloPayCheckout({
            dispatch,
            totalAmount: (tvodTransaction?.amountNum || 0) / 100,
            router,
            selectedMethod,
            queryString,
            inApp: inAppZaloPay,
            promotionData: promotion,
            isRentalContent,
            transactionData
          });
          setTimeout(() => {
            dispatch(setLoading(false));
            checkoutZaloPay.handleZaloPay();
          }, 2000);
          break;
        }
        case PAYMENT_METHOD.SHOPEE_PAY: {
          transactionData = tvodTransactionPay?.shopeepay;
          setTimeout(() => {
            dispatch(setLoading(false));
            dispatch(
              ShopeePayAction.handleTVod({
                transactionData: tvodTransactionPay?.shopeepay,
                totalAmount: (tvodTransaction?.amountNum || 0) / 100,
                selectedPackage,
                selectedTerm: newTermSelected,
                isRentalContent,
                queryString,
                router
              })
            );
          }, 2000);
          break;
        }
        case PAYMENT_METHOD.MOCA: {
          transactionData = tvodTransactionPay?.moca;
          const redirectUrl = transactionData?.redirectUrl;
          if (redirectUrl) {
            setTimeout(() => {
              dispatch(setLoading(false));
              window.location = redirectUrl;
            }, 2000);
          } else {
            dispatch(setToast({ message: TEXT.MSG_ERROR }));
          }
          break;
        }
        case PAYMENT_METHOD.MOMO: {
          transactionData = tvodTransactionPay?.momo;
          const redirectUrl = transactionData?.redirectUrl;
          if (redirectUrl) {
            setTimeout(() => {
              dispatch(setLoading(false));
              window.location = redirectUrl;
            }, 2000);
          } else {
            dispatch(setToast({ message: TEXT.MSG_ERROR }));
          }
          break;
        }
        case PAYMENT_METHOD.VIETTEL_PAY: {
          transactionData = tvodTransactionPay?.viettelPay;
          const { payUrl } = transactionData;
          if (payUrl) {
            setTimeout(() => {
              dispatch(setLoading(false));
              window.location = payUrl;
            }, 2000);
          } else {
            dispatch(setToast({ message: TEXT.MSG_ERROR }));
          }

          break;
        }
        case PAYMENT_METHOD.VN_PAY:
        case PAYMENT_METHOD.QR_VNPAY:
          transactionData = tvodTransactionPay?.vnpay;
          if (transactionData?.payUrl) {
            setTimeout(() => {
              dispatch(setLoading(false));
              window.location.href = transactionData?.payUrl;
            }, 2000);
          }
          break;
        default:
          break;
      }
    } else if (isPvodContent) {
      dispatch(setLoading(true));
      const { pvodProductId } = pvodInfo?.bizInfo || '';
      const queryString = `?${queryStringEncoding(query)}`;
      const promotion =
        promotionData?.valid === 1 && promotionData?.used === 0 ? promotionData : {};

      const pvodTransaction = await PaymentApi.pvodTransaction({
        pvodProductId,
        paymentService: selectedMethod?.code,
        tokenId: selectedMethod?.id === PAYMENT_METHOD.NAPAS ? tokenSelected?.id : '',
        selectedMethod
      });
      if (!pvodTransaction?.success) {
        dispatch(
          setToast({
            message: pvodTransaction?.errorMessage || TEXT.MSG_ERROR
          })
        );
        dispatch(setLoading(false));
        return false;
      }
      let returnUrl = '';
      let cancelUrl = '';

      if (
        selectedMethod?.id === PAYMENT_METHOD.MOCA ||
        selectedMethod?.id === PAYMENT_METHOD.SHOPEE_PAY
      ) {
        returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}`;
      } else if (selectedMethod?.id === PAYMENT_METHOD.VIETTEL_PAY) {
        cancelUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}`;
        returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}`;
      } else if (
        selectedMethod?.id === PAYMENT_METHOD.VN_PAY ||
        selectedMethod?.id === PAYMENT_METHOD.QR_VNPAY ||
        selectedMethod?.id === PAYMENT_METHOD.MOMO
      ) {
        returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}${queryString}`;
      } else if (selectedMethod?.id === PAYMENT_METHOD.NAPAS) {
        returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}${queryString}&orderId=${pvodTransaction?.orderId}&merchantId=VIEON&method=${PAYMENT_METHOD.NAPAS}`;
      }

      dispatch(
        createTransactionSuccess({
          data: pvodTransaction,
          valueReferralCode
        })
      );
      const pvodTransactionPay = await PaymentApi.pvodTransactionPay({
        orderId: pvodTransaction?.orderId,
        bankCode: bank?.code || '',
        returnUrl,
        cancelUrl
      });
      if (!pvodTransactionPay?.success) {
        dispatch(
          setToast({
            message: pvodTransactionPay?.errorMessage || TEXT.MSG_ERROR
          })
        );
        dispatch(setLoading(false));
        return false;
      }
      switch (selectedMethod?.id) {
        case PAYMENT_METHOD.ASIAPAY:
        case PAYMENT_METHOD.CAKE: {
          transactionData = pvodTransactionPay?.asiapay;
          const checkoutAsiaPay = new AsiaPayCheckout({
            selectedTerm: newTermSelected,
            dispatch,
            selectedPackage,
            totalAmount,
            router,
            cardInfo: selectedMethod?.id === PAYMENT_METHOD.CAKE ? cardInfoCake : cardInfo,
            profile,
            selectedMethod,
            queryString,
            promotionData: promotion,
            valueReferralCode,
            billingInfo,
            transactionData,
            totalPrice: pvodOffer?.totalPrice
          });
          await checkoutAsiaPay.handleAsiaPayPvod(transactionData);
          dispatch(setLoading(false));
          break;
        }
        case PAYMENT_METHOD.NAPAS: {
          transactionData = pvodTransactionPay?.napas || {};
          const returnUrl = `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}${queryString}&orderId=${transactionData?.orderId}&merchantId=VIEON&method=${PAYMENT_METHOD.NAPAS}`;
          const napasCheckout = new NapasCheckout({
            returnUrl,
            router,
            isPvodContent,
            transactionData,
            dispatch
          });
          await napasCheckout.handleNapas();
          dispatch(setLoading(false));
          break;
        }
        case PAYMENT_METHOD.ZALO_PAY: {
          transactionData = pvodTransactionPay?.zalopay;
          const checkoutZaloPay = new ZaloPayCheckout({
            dispatch,
            totalAmount: (pvodTransaction?.amountNum || 0) / 100,
            router,
            selectedMethod,
            queryString,
            inApp: inAppZaloPay,
            promotionData: promotion,
            isPvodContent,
            transactionData
          });
          setTimeout(() => {
            dispatch(setLoading(false));
            checkoutZaloPay.handleZaloPay();
          }, 2000);
          break;
        }
        case PAYMENT_METHOD.SHOPEE_PAY: {
          transactionData = pvodTransactionPay?.shopeepay;
          setTimeout(() => {
            dispatch(setLoading(false));
            dispatch(
              ShopeePayAction.handleTVod({
                transactionData: pvodTransactionPay?.shopeepay,
                totalAmount: (pvodTransaction?.amountNum || 0) / 100,
                selectedPackage,
                selectedTerm: newTermSelected,
                isPvodContent,
                queryString,
                router
              })
            );
          }, 2000);
          break;
        }
        case PAYMENT_METHOD.MOCA: {
          transactionData = pvodTransactionPay?.moca;
          const redirectUrl = transactionData?.redirectUrl;
          if (redirectUrl) {
            setTimeout(() => {
              dispatch(setLoading(false));
              window.location = redirectUrl;
            }, 2000);
          } else {
            dispatch(setToast({ message: TEXT.MSG_ERROR }));
          }
          break;
        }
        case PAYMENT_METHOD.MOMO: {
          transactionData = pvodTransactionPay?.momo;
          const redirectUrl = transactionData?.redirectUrl;
          if (redirectUrl) {
            setTimeout(() => {
              dispatch(setLoading(false));
              window.location = redirectUrl;
            }, 2000);
          } else {
            dispatch(setToast({ message: TEXT.MSG_ERROR }));
          }
          break;
        }
        case PAYMENT_METHOD.VIETTEL_PAY: {
          transactionData = pvodTransactionPay?.viettelPay;
          const { payUrl } = transactionData;
          if (payUrl) {
            setTimeout(() => {
              dispatch(setLoading(false));
              window.location = payUrl;
            }, 2000);
          } else {
            dispatch(setToast({ message: TEXT.MSG_ERROR }));
          }

          break;
        }
        case PAYMENT_METHOD.VN_PAY:
        case PAYMENT_METHOD.QR_VNPAY:
          transactionData = pvodTransactionPay?.vnpay;
          if (transactionData?.payUrl) {
            setTimeout(() => {
              dispatch(setLoading(false));
              window.location.href = transactionData?.payUrl;
            }, 2000);
          }
          break;
        default:
          break;
      }
    } else {
      if (!termId) return;
      switch (selectedMethod?.id) {
        case PAYMENT_METHOD.NAPAS: {
          transactionData = await dispatch(
            NapasAction.createTransaction({
              tokenId: tokenSelected?.id || '',
              selectedTerm: newTermSelected,
              selectedMethod,
              promotionData: promotion,
              queryString,
              router,
              valueReferralCode
            })
          );
          break;
        }
        case PAYMENT_METHOD.ASIAPAY:
        case PAYMENT_METHOD.CAKE: {
          const checkoutAsiaPay = new AsiaPayCheckout({
            selectedTerm: newTermSelected,
            dispatch,
            selectedPackage,
            totalAmount,
            router,
            cardInfo: selectedMethod?.id === PAYMENT_METHOD.CAKE ? cardInfoCake : cardInfo,
            profile,
            selectedMethod,
            queryString,
            promotionData: promotion,
            valueReferralCode,
            billingInfo
          });
          transactionData = await checkoutAsiaPay.handleAsiaPay();
          break;
        }
        case PAYMENT_METHOD.ZALO_PAY: {
          const checkoutZaloPay = new ZaloPayCheckout({
            recurring,
            selectedTerm: newTermSelected,
            dispatch,
            selectedPackage,
            totalAmount,
            router,
            zaloPayLinked,
            selectedMethod,
            queryString,
            inApp: inAppZaloPay,
            promotionData: promotion,
            valueReferralCode
          });
          transactionData = await checkoutZaloPay.handleZaloPay();
          break;
        }
        case PAYMENT_METHOD.SHOPEE_PAY: {
          if (recurring) {
            transactionData = await dispatch(
              ShopeePayAction.handleRecurring({
                tokenId: tokenSelected?.id || '',
                recurring,
                selectedTerm: newTermSelected,
                dispatch,
                selectedPackage,
                totalAmount,
                router,
                selectedMethod,
                queryString,
                promotionData: promotion,
                isRentalContent,
                valueReferralCode
              })
            );
          } else {
            transactionData = await dispatch(
              ShopeePayAction.createTransaction({
                tokenId: tokenSelected?.id || '',
                recurring,
                selectedTerm: newTermSelected,
                dispatch,
                selectedPackage,
                totalAmount,
                router,
                selectedMethod,
                queryString,
                promotionData: promotion,
                isRentalContent,
                valueReferralCode
              })
            );
          }
          break;
        }
        case PAYMENT_METHOD.MOMO: {
          transactionData = await dispatch(
            MomoAction.createTransaction({
              selectedTerm: newTermSelected,
              selectedMethod,
              promotionData: promotion,
              valueReferralCode,
              tokenId: tokenSelected?.id || '',
              isSegmentedUser
            })
          );
          break;
        }
        case PAYMENT_METHOD.MOCA: {
          transactionData = await dispatch(
            MocaAction.createTransaction({
              tokenId: '',
              selectedTerm: newTermSelected,
              selectedMethod,
              promotionData: promotion,
              valueReferralCode,
              isSegmentedUser
            })
          );
          break;
        }
        case PAYMENT_METHOD.VIETTEL_PAY: {
          transactionData = await dispatch(
            ViettelPayAction.createTransaction({
              selectedTerm: newTermSelected,
              selectedMethod,
              promotionData: promotion,
              valueReferralCode,
              isSegmentedUser
            })
          );
          break;
        }
        case PAYMENT_METHOD.QR_VNPAY:
        case PAYMENT_METHOD.VN_PAY: {
          const checkoutVnPay = new VNPayCheckout({
            selectedTerm: newTermSelected,
            dispatch,
            bankCode: bank?.code,
            promotionData: promotion,
            selectedMethod,
            queryString,
            valueReferralCode,
            isSegmentedUser
          });
          transactionData = await checkoutVnPay.handleVnPay();
          break;
        }
        default:
          break;
      }
    }

    trackingPayment.createTransaction({
      transactionData,
      selectedTerm: newTermSelected,
      selectedMethod,
      promotionData,
      bankCode: bank?.code,
      isSegmentedUser
    });

    if (!transactionData || transactionData?.error) {
      TrackingApp.createTransactionError({
        httpCode: transactionData?.error,
        payMethod: selectedMethod?.id
      });
    }

    // Save Data For Payment Result
    let orderId =
      transactionData?.order_id ||
      transactionData?.orderId ||
      transactionData?.orderID ||
      transactionData?.txnRef ||
      transactionData?.tnxID;
    if (isRentalContent) orderId = transactionData?.orderId;
    if (orderId) {
      setTemporaryData({
        id: orderId,
        partnerName: query?.utm_source || 'none',
        query: {
          ...query,
          term: newTermSelected?.id,
          method: selectedMethod?.id,
          currentUrl: {
            url: asPath,
            href: pathname
          }
        }
      });
    }
    saveStep();
    return true;
  };

  const saveStep = () => {
    const step2Url = asPath;
    ConfigLocalStorage.set(LocalStorage.PAYMENT_STEP2, JSON.stringify({ step2Url }));
  };

  const setTotalAmount = (total: any) => {
    totalAmount = total;
  };

  useEffect(() => {
    if (pvodInfo && isPvodContent && profile?.id) {
      if (pvodInfo?.benefitInfo?.type === TVOD.USER_TYPE.EXPIRED || isEmpty(pvodInfo?.bizInfo)) {
        DetailApi.getContentById({ contentId: query?.id, isGlobal }).then((res) => {
          if (res?.success) {
            dispatch(
              openPopup({
                name: POPUP.NAME.PVOD_EXPIRED,
                pvodInfo,
                productNameMsg: res?.data?.movie?.title,
                notClosedButton: true
              })
            );
          }
        });
      }
      if (pvodInfo?.benefitInfo?.type === TVOD.USER_TYPE.RENTED) {
        dispatch(
          openPopup({
            name: POPUP.NAME.PVOD_OWNED,
            pvodInfo,
            notClosedButton: true
          })
        );
      }
    }
  }, [profile?.id, isPvodContent, pvodInfo]);

  useEffect(
    () => () => {
      dispatch(resetGlobalAuth('dataLogin'));
    },
    []
  );

  const scrollToLoginForm = () => {
    if (loginRef.current) {
      loginRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <section
      ref={sectionRef}
      className="section section--payment section--payment-method !px-4 md:!px-[3.02083vw] !mb-0"
    >
      <SeoText seo={dataSEOAllPage?.seo} />
      <div className="container !max-w-[94rem]">
        <div className="section__body">
          <div className="lg:flex lg:space-x-3 justify-between items-start">
            <div className="cell large-auto space-y-9 md:space-y-12 z-0 lg:max-w-[912px] lg:w-[70%]">
              {!isRentalContent && !isPvodContent && (
                <TermPackage
                  data={get(selectedPackage, 'terms', [])}
                  selectedTerm={selectedTerm}
                  newTermSelected={newTermSelected}
                  onSelect={onSelectTerm}
                  router={router}
                  selectedMethod={selectedMethod}
                />
              )}
              <Method
                isMobile={isMobile}
                data={methodData}
                isRentalContent={isRentalContent}
                isPvodContent={isPvodContent}
                selectedMethod={selectedMethod}
                newTermSelected={newTermSelected}
                selectedPackage={selectedPackage}
                router={router}
                isInApp={isInApp}
                onSelect={onSelectMethod}
              />
            </div>
            <div
              className={classNames(
                'cell large-shrink space-y-4 md:space-y-12 w-full lg:max-w-[30%] 2xl:max-w-[442px] z-10',
                !isRentalContent && !isPvodContent && 'md:pt-[46px]',
                'sticky',
                !profile?.id ? 'md:top-[55px]' : 'md:top-[40px]'
              )}
            >
              {checkPackageConfig &&
                !isMobile &&
                !isRentalContent &&
                !isPvodContent &&
                enableReferral && <ReferralProgPayment isCheckFirstPay={isCheckFirstPay} />}
              {/* login form */}
              <div
                className={classNames(
                  'z-0 overflow-hidden mb-[10px]',
                  !isGlobal && profile?.id && 'hidden'
                )}
              >
                {!isGlobal && !profile?.id && (
                  <LoginPayment
                    onHandleDataLogin={onHandleDataLogin}
                    error={error}
                    setError={setError}
                    loginFormRef={loginRef}
                  />
                )}
              </div>
              {/* bill info */}
              <div
                className={classNames(
                  'z-[1] transition-all duration-500 ease-in-out',
                  isFixed ? 'absolute transform translate-y-[-295px] top-[400px]' : '',
                  isFixed ? 'transform translate-y-[-295px] top-[115px]' : '',
                  '!mt-0'
                )}
              >
                {isRentalContent ? (
                  <BillTVod
                    onCheckOut={onCheckOut}
                    isNotSelectBank={isNotSelectBank}
                    isRentalContent={isRentalContent}
                    extraError={isNotSelectBank}
                    isSimulcast={isSimulcast === 'true'}
                    isLiveEvent={isLiveEvent === 'true'}
                    content={contentData}
                    isCheckFirstPay={isCheckFirstPay}
                    onHandleLogin={onHandleLogin}
                  />
                ) : isPvodContent ? (
                  <BillPVod
                    onCheckOut={onCheckOut}
                    isNotSelectBank={isNotSelectBank}
                    extraError={isNotSelectBank}
                    content={contentData}
                    isCheckFirstPay={isCheckFirstPay}
                    onHandleLogin={onHandleLogin}
                  />
                ) : (
                  <Bill
                    onCheckOut={onCheckOut}
                    setTotalAmount={setTotalAmount}
                    isNotSelectBank={isNotSelectBank}
                    isRentalContent={isRentalContent}
                    extraError={isNotSelectBank}
                    isCheckFirstPay={isCheckFirstPay}
                    checkPackageConfig={checkPackageConfig}
                    enableReferral={enableReferral}
                    onHandleLogin={onHandleLogin}
                    newTermSelected={newTermSelected}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
        {selectedMethod?.id === PAYMENT_METHOD.NAPAS && <div id={EL_ID.ID_ELM_NAPAS_PAYMENT} />}
      </div>
    </section>
  );
};

export default Step2;
