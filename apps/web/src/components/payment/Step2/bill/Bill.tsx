import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import { TEXT } from '@constants/text';
import { isAPartElmInView, numberWithCommas } from '@helpers/common';
import { CURRENCY, PAGE, PAYMENT_METHOD } from '@constants/constants';
import ReferralProgPayment from '@components/payment/Step2/ReferralProgPayment';
import { isMobile } from 'react-device-detect';
import ConfigImage from '@config/ConfigImage';
import LogoCertificate from './LogoCertificate';
import classNames from 'classnames';
import { debounce } from 'lodash';
import Promotion from '../Promotion';
import Button from '@/components/basic/Buttons/Button';
import BillItem from './BillItem';
import Styles from './Bill.module.scss';
import Toggle from '@/components/ToggleCheckbox';
import Image from '@/components/basic/Image/Image';
import TrackingPayment from '@/tracking/functions/payment';

const trackingPayment = new TrackingPayment();

const Bill = ({
  onCheckOut,
  setTotalAmount,
  extraError,
  isNotSelectBank,
  isCheckFirstPay,
  enableReferral,
  checkPackageConfig,
  onHandleLogin,
  newTermSelected
}: any) => {
  const footerRef = useRef<any>(null);
  const router = useVieRouter();
  const { pathname } = router || {};
  const { isMobileViewPort, webConfig } = useSelector((state: any) => state?.App || {});
  const userProfile = useSelector((state: any) => state?.Profile?.profile);
  const paymentStore = useSelector((state: any) => state?.Payment) || {};
  const { selectedPackage, selectedMethod, promotionData, selectedTerm } = paymentStore;

  const { startDate, expiredDate, recurring, price, oldPrice, id, displayPrice, discount } =
    newTermSelected || {};
  const { promotionPrice, valid, used, newPrice } = promotionData || {};
  const inApp = useMemo(() => (pathname || '').includes(PAGE.ZALOPAY), [pathname]);
  const { featureFlag } = webConfig || {};

  const [paddingBill, setPaddingBill] = useState<any>(null);
  const [isFixedBill, setIsFixedBill] = useState(isMobileViewPort);
  const [isRecurring, setIsRecurring] = useState(recurring);

  const fixedResume = isMobileViewPort ? 'is-fixed-resume' : null;

  const isMethodInList = useMemo(() => {
    return (
      selectedMethod &&
      Array.isArray(selectedMethod.enableTermList) &&
      selectedTerm?.id &&
      selectedMethod.enableTermList.includes(selectedTerm.id)
    );
  }, [selectedMethod, selectedTerm]);

  const textNote =
    !newTermSelected?.termGrouped || !isMethodInList
      ? newTermSelected?.recurring
        ? TEXT.BILLING_NOTE_RECURRING
        : TEXT.BILLING_NOTE_NON_RECURRING
      : '';

  const nameLabel = useMemo(() => {
    if (userProfile?.mobile) {
      return userProfile?.mobile;
    }
    if (userProfile?.email && userProfile?.emailVerified) {
      return userProfile?.email;
    }
    return userProfile?.givenName;
  }, [userProfile]);
  const configPrice = useMemo(() => selectedMethod?.prices?.[id], [selectedMethod, id]);

  const worth = useMemo(() => {
    if (displayPrice > 0 && displayPrice > price) {
      return numberWithCommas(displayPrice);
    }
    let tempWorth = (oldPrice || 0) <= 0 ? price : oldPrice;
    if (configPrice) {
      tempWorth = configPrice;
    }
    return numberWithCommas(tempWorth);
  }, [displayPrice, price, oldPrice, configPrice]);

  const newExpireDate = promotionData?.promotionExpireDate || expiredDate;

  const totalAmount = useMemo(() => {
    if (configPrice) return numberWithCommas(configPrice);

    let tempPrice = price ?? 0;

    if (
      valid === 1 &&
      used === 0 &&
      selectedMethod?.id !== PAYMENT_METHOD.MOBI &&
      selectedMethod?.id !== PAYMENT_METHOD.VINA
    ) {
      tempPrice = newPrice;
    }

    return numberWithCommas(tempPrice);
  }, [price, valid, used, selectedMethod, configPrice, oldPrice]);

  const discountPrice = useMemo(() => {
    if (configPrice) return '';

    const promoPrice = promotionPrice ?? 0;
    const curPrice = price ?? 0;

    const basePrice =
      displayPrice && displayPrice >= price ? displayPrice : oldPrice > 0 ? oldPrice : worth;
    const finalPrice = promoPrice > 0 ? promoPrice : curPrice;

    if (promoPrice > 0) {
      const tempDiscountPrice = Number(worth - totalAmount);
      return `-${numberWithCommas(tempDiscountPrice * 1000)}`;
    }

    const tempPrice = basePrice > finalPrice ? basePrice - finalPrice : 0;
    return tempPrice > 0 ? `-${numberWithCommas(tempPrice)}` : '';
  }, [promotionPrice, displayPrice, oldPrice, price, configPrice, worth, discount, totalAmount]);

  const checkoutButton = useMemo(
    () =>
      setCheckout({
        paymentStore,
        inApp,
        featureFlag
      }),
    [paymentStore, inApp]
  );

  useEffect(() => {
    setIsRecurring(recurring);
  }, [recurring]);

  useEffect(() => {
    if (isMobileViewPort) {
      window.addEventListener('scroll', debouncedCheckFooterInViewOnMobile);
    }
    setIsFixedBill(isMobileViewPort);
    return () => {
      if (isMobileViewPort) {
        window.removeEventListener('scroll', debouncedCheckFooterInViewOnMobile);
      }
    };
  }, [isMobileViewPort]);

  useEffect(() => {
    setTotalAmount(totalAmount);
  }, [totalAmount]);

  useEffect(() => {
    if (isFixedBill) {
      setPaddingBill(footerRef?.current?.clientHeight || null);
    } else {
      setPaddingBill(null);
    }
  }, [isFixedBill]);

  const handleCheckout = () => {
    if (typeof onCheckOut === 'function') {
      onCheckOut();
    }
  };

  const handleToggleRecurring = (value: any) => {
    setIsRecurring(value);
    trackingPayment.recurringToggleSelected({
      newTermSelected,
      selectedMethodName: selectedMethod?.name,
      isRecurring: value
    });
  };

  const debouncedCheckFooterInViewOnMobile = debounce(() => {
    if (!isMobileViewPort || typeof window === 'undefined') return;

    const footerElm = document.getElementById('footer');
    const billInfo = document.getElementById('bill-info');

    if (footerElm && billInfo) {
      const footerIsInView = isAPartElmInView(footerElm);

      if (footerIsInView) {
        // Footer is in view, remove the class
        if (billInfo.classList.contains('is-fixed-resume')) {
          billInfo.classList.remove('is-fixed-resume');
          setIsFixedBill(false);
        }
      } else if (!billInfo.classList.contains('is-fixed-resume')) {
        billInfo.classList.add('is-fixed-resume');
        setIsFixedBill(true);
      }
    }
  }, 100);

  return (
    <div
      className={classNames('block block--bill !top-0', Styles.bill, fixedResume)}
      id="bill-info"
      style={isMobileViewPort && paddingBill ? { paddingBottom: `${paddingBill}px` } : undefined}
    >
      <Image
        src={ConfigImage.footerBillPayment}
        notWebp
        alt="footer bill payment"
        className="absolute w-full top-0 left-0 h-full -z-10"
      />
      <div className="block__header text-center">
        <h2 className="title m-b">{TEXT.DETAIL_INFO_PAYMENT}</h2>
      </div>

      <div className="block__body lg:px-4 px-2">
        <div className="text-center text-sm">
          {TEXT.FOR_ACCOUNT_VieON}: <span className="font-bold">{nameLabel || '-'}</span>
        </div>
        <div className="list-group">
          {[
            { label: TEXT.PACKAGE_NAME, value: selectedPackage?.name || '-' },
            { label: TEXT.TERM_NAME, value: newTermSelected?.name || '-' },
            { label: TEXT.EFFECTIVE_DATE, value: startDate || '-' },
            {
              label: TEXT.USE_UP_TO,
              value: recurring ? TEXT.WHEN_YOU_CANCEL : newExpireDate
            },
            {
              label: TEXT.NEXT_CHECK_OUT,
              value: recurring ? newExpireDate : '-'
            },
            {
              label: TEXT.AUTO_RENEWAL,
              value: (
                <Toggle
                  checked={isRecurring}
                  onChange={(value: any) => {
                    handleToggleRecurring(value);
                  }}
                  disabled={
                    !selectedTerm?.recurring ||
                    !(newTermSelected?.noneRecurring?.id || newTermSelected?.recurringData?.id) ||
                    !isMethodInList
                  }
                />
              ),
              valueClass: 'h-4',
              textNote
            },
            { label: TEXT.WORTH, value: `${worth || ''} ${CURRENCY.VND}` },
            discountPrice && {
              label: TEXT.DISCOUNT,
              value: `${discountPrice || 0} ${CURRENCY.VND}`
            }
          ]
            .filter(Boolean)
            .map((item: any, index: number) => (
              <div key={index} className="list-group__item divide--dashed py-2 lg:py-3">
                <BillItem {...item} />
                {item?.textNote && <div className="!text-[10px] -mt-3">{item?.textNote}</div>}
              </div>
            ))}
          <div className="mt-5 mb-2.5">
            <Promotion
              isNotSelectBank={isNotSelectBank}
              onHandleLogin={onHandleLogin}
              newTermSelected={newTermSelected}
            />
          </div>
        </div>
      </div>

      <div
        className={classNames(
          'block__footer',
          Styles.billFooter,
          isMobileViewPort ? 'pt-2' : '!pt-0'
        )}
        ref={footerRef}
      >
        {checkPackageConfig && isMobile && enableReferral && (
          <ReferralProgPayment isCheckFirstPay={isCheckFirstPay} />
        )}

        <div className="price temporary lg:px-4 px-2">
          <BillItem
            billItemAlign="align-middle"
            label={TEXT.TOTAL_AMOUNT}
            value={`${totalAmount || 0} ${CURRENCY.VND}`}
            valueClass="font-bold !text-[22px] lg:!text-[26px] 2xl:!text-[34px] highlight"
            labelClass="!lg:text-[18px] !text-base"
          />
        </div>

        {extraError && (
          <div className="form-error flex-box align-middle justify-center my-2">
            <span className="icon icon--tiny">
              <i className="vie vie-exclamation-tri-rc-light" />
            </span>
            <span className="text ml-1">{TEXT.SELECT_BANK_WARNING}</span>
          </div>
        )}

        {checkoutButton?.isShow && (
          <div className="button-group child-auto">
            <Button
              className="button button--green"
              title={TEXT.CONTINUE_CHECK_OUT}
              textClass={classNames(Styles.textCheckOut, 'text-white')}
              disabled={checkoutButton?.disabled}
              onClick={handleCheckout}
            />
          </div>
        )}

        <LogoCertificate />

        {/* <div className="text-gray-500 !text-xs bg-[#F3F3F3] py-3 px-6 rounded-lg mt-4">
          Khi thanh toán quý Khách Hàng đã đồng ý cho VieON tạo và lưu trữ liên thông tin cho những
          lần thanh toán sau.
          <div>
            <div>*Để yêu cầu hủy liên kết, vui lòng liên hệ qua email</div>
            <a href="mailto:<EMAIL>" className="font-bold text-vo-green px-1">
              <EMAIL>
            </a>
            , hoặc số hotline
            <a href="tel:1800599920" className="font-bold text-vo-green px-1">
              1800 599 920
            </a>
            (miễn phí)
          </div>
        </div> */}

        <div className="text-gray-500 !text-xs bg-[#F3F3F3] py-3 px-6 rounded-lg mt-4">
          {`Bằng việc thanh toán, bạn xác nhận đã đọc và đồng ý với `}
          <a
            className="link"
            href={inApp ? PAGE.ZALOPAY_REGULATION : PAGE.REGULATION}
            target="_blank"
            rel="noreferrer"
          >
            {TEXT.CONTRACT_POLICY}
          </a>
          {` của VieON`}
          {recurring ? TEXT.RECURRING_NOTE : '.'}
        </div>
      </div>
    </div>
  );
};

const setCheckout = ({ paymentStore, inApp, featureFlag }: any) => {
  let disabled = false;
  let isShow = true;
  const { cardInfo, cardInfoCake, selectedMethod, billingInfo } = paymentStore || {};
  switch (selectedMethod?.id) {
    case PAYMENT_METHOD.CAKE: {
      const { cardCVV, cardExpired, cardExpiredError, cardName, cardNumber, cardNumberError } =
        cardInfoCake || {};
      if (
        !cardCVV ||
        cardCVV?.length < 3 ||
        !cardExpired ||
        !cardName ||
        !cardNumber ||
        cardNumberError ||
        cardExpiredError
      ) {
        disabled = true;
      }
      break;
    }
    case PAYMENT_METHOD.ASIAPAY: {
      const { cardCVV, cardExpired, cardExpiredError, cardName, cardNumber, cardNumberError } =
        cardInfo || {};

      const { country_code, country, province, district, ward, address } = billingInfo || {};
      const regexAddress = /^[\w\-/\\ \u00C0-\u1EF9]{3,50}$/;

      const fullyInfo =
        country_code && country && province && district && ward && regexAddress.test(address);

      const enableBilling = featureFlag?.isShowBillingInfo;

      if (
        !cardCVV ||
        cardCVV?.length < 3 ||
        !cardExpired ||
        !cardName ||
        !cardNumber ||
        cardNumberError ||
        cardExpiredError ||
        (enableBilling && !fullyInfo)
      ) {
        disabled = true;
      }
      break;
    }
    case PAYMENT_METHOD.ZALO_PAY:
    case PAYMENT_METHOD.SHOPEE_PAY:
    case PAYMENT_METHOD.VN_PAY:
    case PAYMENT_METHOD.MOCA:
    case PAYMENT_METHOD.VIETTEL_PAY:
      break;
    case PAYMENT_METHOD.MOBI:
    case PAYMENT_METHOD.VINA:
    case PAYMENT_METHOD.VIETTEL:
    case PAYMENT_METHOD.PAYOO:
      isShow = false;
      break;
    default:
      break;
  }
  if (!selectedMethod?.id) disabled = true;
  if (inApp) disabled = false;

  return {
    disabled,
    isShow
  };
};

export default React.memo(Bill);
