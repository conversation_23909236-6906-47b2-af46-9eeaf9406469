import React from 'react';
import LogoCertificationItem from '@components/basic/Logo/LogoCertification';
import ConfigImage from '@config/ConfigImage';

const LogoCertificate = () => {
  return (
    <div className="flex items-center space-x-2">
      <LogoCertificationItem
        className="min-w-[96px] h-[36px]"
        href="https://certificate.crossbowlabs.com"
        title="VALIDATION OF COMPLIANCE - PCI-SAQ"
        target="_blank"
        src={ConfigImage.logoPciDss}
        altImg="VALIDATION OF COMPLIANCE - PCI-SAQ"
      />
      <span className="!text-[10px] text-[#9B9B9B]">
        Bảo mật SSL/TLS, mọi thông tin giao dịch đều được mã hoá an toàn
      </span>
    </div>
  );
};

export default LogoCertificate;
