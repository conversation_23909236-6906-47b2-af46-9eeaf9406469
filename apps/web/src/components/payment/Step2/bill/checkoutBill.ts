import { PAYMENT_METHOD } from '@constants/constants';

export const checkoutBill = (paymentStore: any, inApp: any) => {
  let disabled = false;
  let isShow = true;
  const { cardInfo, selectedMethod } = paymentStore || {};
  switch (selectedMethod?.id) {
    case PAYMENT_METHOD.ASIAPAY: {
      const { cardCVV, cardExpired, cardExpiredError, cardName, cardNumber, cardNumberError } =
        cardInfo || {};
      if (
        !cardCVV ||
        cardCVV?.length < 3 ||
        !cardExpired ||
        !cardName ||
        !cardNumber ||
        cardNumberError ||
        cardExpiredError
      ) {
        disabled = true;
      }
      break;
    }
    case PAYMENT_METHOD.ZALO_PAY:
    case PAYMENT_METHOD.VN_PAY:
    case PAYMENT_METHOD.MOCA:
    case PAYMENT_METHOD.MOMO:
    case PAYMENT_METHOD.VIETTEL_PAY:
      break;
    case PAYMENT_METHOD.MOBI:
    case PAYMENT_METHOD.VINA:
    case PAYMENT_METHOD.VIETTEL:
    case PAYMENT_METHOD.PAYOO:
      isShow = false;
      break;
    default:
      break;
  }
  if (!selectedMethod) disabled = true;
  if (inApp) disabled = false;

  return { disabled, isShow };
};
