import React from 'react';

const renderValue = (value: any) => {
  if (React.isValidElement(value)) {
    return value;
  } else if (typeof value === 'string') {
    return <div dangerouslySetInnerHTML={{ __html: value }} />;
  } else {
    return value;
  }
};

const BillItem = ({ label, value, valueClass, billItemAlign, labelClass }: any) => {
  if (!label && !value) return null;
  return (
    <div className={`grid-x grid-margin-x${billItemAlign ? ` ${billItemAlign}` : ''}`}>
      <div className="cell small-4 text-nowrap whitespace-nowrap">
        <span className={labelClass || '!text-sm'}>{label}</span>
      </div>
      <div className="cell small-8">
        <div className={`text-right font-bold ${valueClass || '!text-sm'}`}>
          {renderValue(value)}
        </div>
      </div>
    </div>
  );
};

export default React.memo(BillItem);
