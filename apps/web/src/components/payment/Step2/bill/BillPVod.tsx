import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import get from 'lodash/get';
import { TEXT } from '@constants/text';
import { isAPartElmInView } from '@helpers/common';
import { PAGE } from '@constants/constants';
import ConfigImage from '@config/ConfigImage';
import LogoCertification from '@components/basic/Logo/LogoCertification';
import classNames from 'classnames';
import { debounce } from 'lodash';
import Promotion from '../Promotion';
import Button from '../../../basic/Buttons/Button';
import BillItem from './BillItem';
import { checkoutBill } from './checkoutBill';
import Styles from './Bill.module.scss';

const BillPVod = ({ onCheckOut, isNotSelectBank, extraError, onHandleLogin }: any) => {
  const [paddingBill, setPaddingBill] = useState<any>(null);
  const footerRef = useRef<any>(null);
  const router = useVieRouter();
  const { pathname } = router || {};
  const { isMobileViewPort, webConfig } = useSelector((state: any) => state?.App || {});
  const userProfile = useSelector((state: any) => state?.Profile?.profile);
  const paymentStore = useSelector((state: any) => state?.Payment) || {};
  const isEnableInputPromotionCode = get(webConfig, 'pVod.payment.enableInputPromotionCode', false);
  const { pvodOffer } = paymentStore || {};
  const { productNameMsg, contentMsg, priceMsg, discountAmountMsg, totalPriceMsg, discountAmount } =
    pvodOffer || {};

  const inApp = useMemo(() => (pathname || '').includes(PAGE.ZALOPAY), [pathname]);
  const nameLabel = useMemo(() => {
    if (userProfile?.mobile) {
      return userProfile?.mobile;
    }
    if (!userProfile?.mobile && userProfile?.email && userProfile?.emailVerified) {
      return userProfile?.email;
    }
    return userProfile?.givenName;
  }, [userProfile]);
  const checkoutButton = useMemo(() => checkoutBill(paymentStore, inApp), [paymentStore, inApp]);
  // Check footer view in Mobile
  const debounceCheckFooterInViewOnMobile = debounce(() => {
    if (!isMobileViewPort || typeof window === 'undefined') return;
    const footerElm = document.getElementById('footer');
    if (footerElm) {
      const footerIsInView = isAPartElmInView(footerElm);
      const billInfo: any = document.getElementById('bill-info');
      if (footerIsInView) {
        if (billInfo.classList.contains('is-fixed-resume') === true) {
          billInfo.classList.remove('is-fixed-resume');
          setPaddingBill(null);
        }
      } else if (billInfo.classList.contains('is-fixed-resume') === false) {
        billInfo.classList.add('is-fixed-resume');
        setPaddingBill(footerRef?.current?.clientHeight || null);
      }
    }
  }, 100);

  useEffect(() => {
    if (!isMobileViewPort) return;
    window.addEventListener('scroll', debounceCheckFooterInViewOnMobile);
    return () => window.removeEventListener('scroll', debounceCheckFooterInViewOnMobile);
  }, [isMobileViewPort]);

  const fixedResume = isMobileViewPort ? 'is-fixed-resume' : null;

  return (
    <div
      className={classNames('block', Styles.bill, fixedResume)}
      id="bill-info"
      style={isMobileViewPort && paddingBill ? { paddingBottom: `${paddingBill}px` } : undefined}
    >
      <div className="block__header">
        <h2 className="title m-b">{TEXT.DETAIL_INFO}</h2>
      </div>
      <div className="block__body">
        <div className="list-group">
          <div className="list-group__item divide--dashed text-nowrap">
            <BillItem label={TEXT.ACCOUNT_VieON} value={nameLabel || '-'} />
          </div>
          <div className="list-group__item divide--dashed">
            <BillItem label={TEXT.CONTENT_NAME} value={productNameMsg || '-'} />
            <BillItem label={TEXT.EPISODE} value={contentMsg} />
          </div>
          <div className="list-group__item divide--dashed">
            <BillItem label={TEXT.WORTH} value={priceMsg} />
            {discountAmount ? <BillItem label={TEXT.DISCOUNT} value={discountAmountMsg} /> : ''}
            {isEnableInputPromotionCode && (
              <Promotion isNotSelectBank={isNotSelectBank} onHandleLogin={onHandleLogin} />
            )}
          </div>
        </div>
      </div>
      <div className={classNames('block__footer', Styles.billFooter)} ref={footerRef}>
        <div className="price temporary">
          <BillItem
            billItemAlign=" align-middle"
            label={TEXT.TOTAL_AMOUNT}
            value={totalPriceMsg}
            valueClass="text text-bold large-x highlight"
          />
        </div>
        {extraError && (
          <div className="form-error flex-box align-middle justify-content-center m-b padding-small-up-top-5 padding-small-up-bottom-12 padding-large-up-bottom-20">
            <span className="icon icon--tiny">
              <i className="vie vie-exclamation-tri-rc-light" />
            </span>
            <span className="text padding-small-up-left-4 p-t p-b">Vui lòng chọn Ngân hàng</span>
          </div>
        )}
        <div className="text text-gray117 text-12">
          {`Bằng việc thanh toán, bạn xác nhận đã đọc và đồng ý với `}
          <a
            className="link"
            href={inApp ? PAGE.ZALOPAY_REGULATION : PAGE.REGULATION}
            target="_blank"
            title={TEXT.CONTRACT_POLICY}
            rel="noreferrer"
          >
            {TEXT.CONTRACT_POLICY}
          </a>
          {` của VieON`}
        </div>
        {checkoutButton?.isShow && (
          <div className="button-group child-auto">
            <Button
              className="button button--green button--large"
              title={TEXT.CHECK_OUT}
              textClass={classNames(Styles.textCheckOut, '!text-white')}
              disabled={checkoutButton?.disabled}
              onClick={onCheckOut}
            />
          </div>
        )}
        <div className="flex space-x-[20px] text-gray117">
          <LogoCertification
            className="w-[7.5625rem]"
            href="https://certificate.crossbowlabs.com/b6b03752-549a-4003-9842-ec5034048980#gs.19u98b"
            title="VALIDATION OF COMPLIANCE - PCI-SAQ"
            target="_blank"
            src={ConfigImage.logoPciDss}
            altImg="VALIDATION OF COMPLIANCE - PCI-SAQ"
            style={{
              width: isMobileViewPort ? '62px' : '121px',
              height: isMobileViewPort ? '24px' : '47px'
            }}
          />
          <span>Bảo mật SSL/TLS, mọi thông tin giao dịch đều được mã hoá an toàn</span>
        </div>
      </div>
    </div>
  );
};

export default React.memo(BillPVod);
