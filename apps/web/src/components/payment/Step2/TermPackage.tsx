import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import TagDiscount from '@components/basic/Tags/TagDiscount';
import classNames from 'classnames';
import TagsMostBuyersV2 from '@components/basic/Tags/TagMostBuyersV2';
import { CURRENCY } from '@constants/constants';
import { useSelector } from 'react-redux';
import BadgePromoPackage from '@components/Icons/BadgePromoPackage';
import style from './Step2.module.scss';
import TrackingPayment from '@/tracking/functions/payment';
import { TEXT } from '@constants/text';

const trackingPayment = new TrackingPayment();
const TermPackage = React.memo(
  ({ data = [], selectedTerm, onSelect, newTermSelected, selectedMethod }: any) => {
    const updateCount = useRef<any>(0);
    const lastTrackedId = useRef<any>(null);

    useEffect(() => {
      if (newTermSelected?.id) {
        updateCount.current += 1;

        // TODO update tracking for duplicate force select term with recurring
        if (updateCount.current > 1 && newTermSelected.id !== lastTrackedId.current) {
          trackingPayment.selectTerm({ selectedTerm: newTermSelected });
          lastTrackedId.current = newTermSelected.id;
        }
      }
    }, [newTermSelected?.id, selectedMethod?.name]);

    return (
      <div className={classNames(style.plan)} id="blockPlan">
        <div className={classNames(style.planTitle)}>
          <h2 className={classNames(style.planTitle)}>{TEXT.SELECT_PACKAGE_DURATION}</h2>
        </div>
        <div className="flex flex-wrap gap-3 md:gap-4">
          {data?.map((item: any) => (
            <ItemTerm
              key={item.id}
              data={item}
              onSelect={onSelect}
              selectedTerm={selectedTerm}
              newTermSelected={newTermSelected}
            />
          ))}
        </div>
      </div>
    );
  }
);

const formatNameWithLineBreaks = (name: any) =>
  name?.split('-').map((part: any, index: any) => (
    <React.Fragment key={index}>
      {part}
      {index < name?.split('-')?.length - 1 && <br />}
    </React.Fragment>
  ));

const formatPrice = (price: any) =>
  price ? new Intl.NumberFormat('vi-VN').format(price) + ' ' + CURRENCY.VND : '';
const ItemTerm = React.memo(({ data, selectedTerm, onSelect, newTermSelected }: any) => {
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const isActive = useMemo(() => selectedTerm?.id === data?.id, [selectedTerm, data]);

  const getDataNewTermSelected = useMemo(() => {
    return isActive ? newTermSelected : data;
  }, [isActive, newTermSelected, data]);

  const handleClick = useCallback(() => onSelect(data), [data, onSelect]);

  const { displayPrice, price, oldPrice, promotionPrice, discount } = getDataNewTermSelected || {};
  const shouldShowDiscount = displayPrice > price || oldPrice > price;

  const getDisplayedPrice = () => {
    if (displayPrice < price && displayPrice < oldPrice) {
      if (price > displayPrice) {
        return oldPrice;
      }
    }
    if (displayPrice !== price) {
      return displayPrice;
    }
    return 0;
  };
  const finalPrice = price - (promotionPrice || 0);

  const renderTagPromoFirstPay = useMemo(() => {
    if (!discount) return null;
    return (
      <div className="absolute lg:top-[-8px] -top-1 -right-[2px] w-[42px] h-[45px] md:w-[73px] md:h-[78px]">
        <BadgePromoPackage
          text="ưu đãi"
          className="overflow-visible"
          width="100%"
          height="100%"
          position="top-right"
        />
      </div>
    );
  }, [discount]);

  const discountValue = getDataNewTermSelected?.discount
    ? getDataNewTermSelected?.discountTag
    : getDataNewTermSelected?.percentDiscount;

  const cleanedDiscountValue = discountValue?.toString().replace(/%/g, '');
  const numericDiscount = parseFloat(cleanedDiscountValue) || 0;

  const shouldShowDiscountTag = displayPrice !== price && numericDiscount > 0;
  const termContentActive = getDataNewTermSelected?.subName
    ? style.termContentMostActive
    : style.termContentActive;

  return (
    <article
      className={classNames(
        'flex-none w-[calc(50%-6px)] md:w-[calc((100%/3)-12px)] xl:w-[calc((100%/4)-12px)] flex items-end justify-center h-auto',
        getDataNewTermSelected?.subName && 'min-h-[164px]'
      )}
    >
      <div
        className={classNames(
          'on-click pt-6 md:pt-9 lg:pt-11 relative h-full w-full',
          style.term,
          // getDataNewTermSelected?.subName ? 'w-[calc(100%_-_8px)]' : 'w-full',
          getDataNewTermSelected?.subName && 'lg:px-2 px-1 pb-1 lg:pb-2',
          isActive &&
            getDataNewTermSelected?.subName &&
            'shadow-[3px_3px_10px_0px_rgba(0,0,0,.25)] rounded-xl'
        )}
        onClick={handleClick}
      >
        {getDataNewTermSelected?.subName && (
          <TagsMostBuyersV2 text={getDataNewTermSelected?.subName} />
        )}
        <div
          className={classNames(
            style.termContent,
            isActive ? termContentActive : style.termContentHover
          )}
        >
          {renderTagPromoFirstPay}

          <div className={classNames(style.termHead)}>
            {getDataNewTermSelected?.name && (
              <h4 className={classNames(style.termHeadTitle)}>
                {isMobile
                  ? getDataNewTermSelected?.name
                  : formatNameWithLineBreaks(getDataNewTermSelected?.name)}
              </h4>
            )}
            {(getDataNewTermSelected?.discountDesc || getDataNewTermSelected?.description) && (
              <span className={classNames(style.termHeadSub)}>
                {discount
                  ? getDataNewTermSelected?.discountDesc
                  : getDataNewTermSelected?.description}
              </span>
            )}
          </div>
          <div className={classNames(style.Price)}>
            {shouldShowDiscount && (
              <div className={classNames(style.PriceDiscount)}>
                <div className={classNames(style.PriceOld)}>{formatPrice(getDisplayedPrice())}</div>
                {shouldShowDiscountTag && <TagDiscount text={`-${discountValue}`} />}
              </div>
            )}

            {getDataNewTermSelected?.price && (
              <div className={classNames(style.PriceCurrent)}>{formatPrice(finalPrice)}</div>
            )}
          </div>
        </div>
      </div>
    </article>
  );
});

export default TermPackage;
