.method {
  @apply pt-0;

  &Title {
    @apply m-0;
    @apply text-[1rem] md:text-[1.25rem] xl:text-[1.5rem] font-bold text-black leading-[1.4];

    &Icon {
      @apply text-[#3ac882];
    }

    &Sub {
      @apply flex md:items-center space-x-1;
      &Text {
        @apply text-[.75rem] leading-[1.25] w-full;
      }
    }
  }
}

.plan {
  @apply flex flex-col space-y-4;
  &Head {
    @apply flex;
  }
  &Title {
    @apply m-0;
    @apply text-[1rem] md:text-[1.25rem] xl:text-[1.5rem] font-bold text-black leading-[1.4];

    &Icon {
      @apply text-[#3ac882];
    }

    &Sub {
      @apply flex md:items-center space-x-1;
      &Text {
        @apply text-[.75rem] leading-[1.25];
      }
    }
  }
}

.Price {
  @apply flex flex-col mt-3;

  &Discount {
    @apply flex flex-wrap items-center space-x-1;
  }

  &Old {
    @apply text-[11px] max-w-full md:text-base lg:text-[.75rem] xl:text-[.875rem] 2xl:text-base font-medium line-through text-[#999];
  }

  &Current {
    @apply text-[.875rem] md:text-[1.25rem] 2xl:text-[1.5rem] font-bold text-[#3AC882] leading-[1.25];
  }
}

.term {
  &Content {
    // @apply h-auto md:h-[11.5rem] lg:h-[10.5rem] xl:h-[192px] flex flex-col justify-between align-middle relative z-[1] bg-white rounded-lg;
    @apply min-h-[136px] h-full flex flex-col justify-between align-middle relative z-[1] bg-white rounded-lg;
    @apply p-3 md:pt-6 xl:pb-3 2xl:pb-4 2xl:px-4;
    @apply before:-z-[1] before:absolute before:top-0 before:right-0 before:bottom-0 before:left-0 before:w-full before:h-full before:rounded-lg before:border-[1px] before:border-solid border-[#ccc] before:transition-all;

    &Active,
    &MostActive,
    &Hover:hover {
      @apply before:border-[1px] before:bg-[#EEFCF5] before:border-[#3AC882];
    }

    &Active {
      box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.25);
    }
  }

  &Head {
    @apply flex flex-col min-h-14 md:min-h-[72px] lg:min-h-[88px] space-y-1 xl:space-y-2;

    &Title {
      @apply text-[.75rem] md:text-[1.125rem] lg:text-[1rem] 2xl:text-[1.125rem] text-black font-bold uppercase leading-[1.25] text-ellipsis line-clamp-2 pr-3 md:pr-5;
    }

    &Sub {
      @apply text-[.625rem] md:text-[.75rem] text-black font-normal leading-[1.33] text-ellipsis line-clamp-3;
    }
  }
}

.tagsMostBuyers {
  @apply max-w-[calc(100%-.5rem)] h-4 md:h-8 lg:h-5 xl:h-8 absolute top-[.5rem] right-0 z-[2];
}
.ribbon {
  @apply before:w-8 before:h-[1.25rem] before:md:h-[2.25rem] before:lg:h-[1.5rem] before:xl:h-[2.25rem] before:absolute before:top-[.25rem] before:right-0;
  @apply before:bg-[#CE8C00] before:rounded-md before:-z-[1];
}

.loginPayment {
  @apply w-full lg:p-4 2xl:p-6 rounded-lg lg:border lg:border-solid lg:border-[#ccc] space-y-[8px];
  &Title {
    @apply lg:text-2xl font-bold lg:font-medium;
  }
  &des {
    @apply text-sm font-medium;
  }
  &FormInput {
    @apply flex items-center border border-solid rounded-xl border-[#111] text-black px-4 py-[9px] lg:py-3 space-x-[10px];
  }
  &Input {
    @apply text-black w-full m-0 p-0 h-auto;
    border: none !important;
    box-shadow: 0 0 #0000 !important;
    &:-webkit-autofill,
    &:-webkit-autofill:focus,
    &:-webkit-autofill:hover {
      -webkit-text-fill-color: #000 !important;
      font-size: 14px !important;
      line-height: 1;
    }
  }
  &ErrorInput {
    &:-webkit-autofill,
    &:-webkit-autofill:focus,
    &:-webkit-autofill:hover {
      -webkit-text-fill-color: #e74c3c !important;
      font-size: 14px !important;
      line-height: 1;
    }
  }
  &Text {
    @apply text-[13px] 2xl:text-[14px] font-normal;
    &Register {
      @apply flex items-center text-[#3AC882] cursor-pointer underline;
    }
  }
  &HasError {
    @apply text-[10px] font-normal after:content-[attr(data-error)] after:text-[#E74C3C];
  }
  &HasWarning {
    @apply text-[10px] font-normal text-[#F1C21B];
  }
}

.promotion {
  @apply relative;
  .input {
    // @apply rounded-[100px] text-black placeholder:text-[rgba(0,0,0,0.09)] mb-0 h-9 py-1 border-[rgba(0,0,0,0.05)] border-[1px] focus:border-[#CCCCCC] focus:ring-0 px-[36px] text-xs lg:text-base pr-[112px];
    @apply rounded-[100px] text-black placeholder:text-[#C4C4C4] mb-0 h-9 py-1 border-[#C4C4C4] border-[1px] focus:border-[#CCCCCC] focus:ring-0 px-[36px] text-xs lg:text-base pr-[112px];
  }
  .button {
    @apply absolute top-1/2 right-[4px] -translate-y-1/2 h-[calc(100%_-_8px)] w-full max-w-[104px];
    @apply disabled:bg-[#F3F3F3] disabled:text-[#9B9B9B] disabled:cursor-not-allowed text-white bg-[#2FB138] hover:opacity-90 rounded-[100px] transition-all font-medium text-sm;
  }

  &.error {
    .input {
      @apply border-[#E3513B] text-[#E3513B];
    }
    .button {
      @apply bg-[#E3513B];
    }
  }
  &.success {
    .input {
      @apply border-[#28D428] text-[#28D428];
    }
    .button {
      @apply bg-[#28D428];
    }
  }
}
