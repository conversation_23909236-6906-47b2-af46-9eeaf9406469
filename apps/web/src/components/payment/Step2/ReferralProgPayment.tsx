import React from 'react';
import { isMobile } from 'react-device-detect';
import { openPopup } from '@actions/popup';
import { POPUP } from '@constants/constants';
import Button from '@components/basic/Buttons/Button';
import ReferralCode from '@components/payment/Step2/ReferralCode';
import { useDispatch, useSelector } from 'react-redux';
import { TEXT } from '@constants/text';
import { resetValueReferralCode } from '@actions/payment';
import Icon from '@components/basic/Icon/Icon';
const ReferralProgPayment = ({ isCheckFirstPay }: any) => {
  const dispatch = useDispatch();
  const { valueReferralCode } = useSelector((state: any) => state?.Payment) || {};

  const onHandleClickIcon = () => {
    if (valueReferralCode) {
      dispatch(resetValueReferralCode());
    } else {
      dispatch(openPopup({ name: POPUP.NAME.REFERRAL_CODE }));
    }
  };
  const onHandleClickTitle = () => {
    dispatch(
      openPopup({
        name: POPUP.NAME.REFERRAL_CODE,
        valueReferral: valueReferralCode || ''
      })
    );
  };

  if (isCheckFirstPay) {
    if (isMobile) {
      return (
        <div className="grid-x align-middle align-justify m-b1">
          <div className="cell auto" onClick={onHandleClickTitle}>
            <span className="grid-x text-12">
              <span className="text-bold p-r1">
                {valueReferralCode
                  ? `${TEXT.REFERRAL_CODE}: ${valueReferralCode}`
                  : TEXT.PLACE_HOLDER_REFERRAL_CODE}
              </span>
              {valueReferralCode ? (
                <Icon spClass="icon--tiny" iClass="vie-tick-solid-c text-green" />
              ) : (
                ' (không bắt buộc)'
              )}
            </span>
          </div>
          <div className="cell shrink">
            <Button
              className="button button--close p-x"
              iconClass="icon--tiny text-gray117"
              iconName={valueReferralCode ? 'vie-times-medium' : 'vie-chevron-right-r-medium'}
              onClick={onHandleClickIcon}
            />
          </div>
        </div>
      );
    }
    return <ReferralCode />;
  }
  return null;
};
export default React.memo(ReferralProgPayment);
