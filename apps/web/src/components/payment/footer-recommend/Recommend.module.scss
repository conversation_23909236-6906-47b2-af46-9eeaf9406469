.Card {
  @apply flex flex-col md:flex-row items-center justify-center md:justify-start px-4 lg:px-[10%] py-[20px] space-y-4 md:space-y-0 md:space-x-9;

  &Thumb {
    @apply relative w-full h-auto max-w-[342px] md:max-w-[332px] md:max-h-[148px] flex justify-center items-center;
  }
}
.Benefit {
  @apply flex items-start space-x-1 md:space-x-2 w-full text-[#171717];

  &Disabled {
    @apply opacity-20;
  }
  &Icon {
    @apply flex items-center justify-center w-5 h-auto p-0.5;
  }
  &Title {
    @apply text-ellipsis line-clamp-1 w-full text-[14px];
  }
}
