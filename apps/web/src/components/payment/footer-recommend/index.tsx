import React, { useEffect, useMemo, useRef, useState } from 'react';
import { handleGetSlugEpsFromPath } from '@services/detailServices';
import isEmpty from 'lodash/isEmpty';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import ContentApi from '@/apis/cm/ContentApi';
import DetailApi from '@/apis/detailApi';
import LiveStreamApi from '@/apis/LiveStream';
import Button from '@/components/basic/Buttons/Button';
import ConfigLocalStorage from '@/config/ConfigLocalStorage';
import CardListFooter from './card-list-footer';
import PageApi from '@/apis/cm/PageApi';
import TrackingPayment from '@/tracking/functions/payment';
import { useVieRouter } from '@/customHook';
import CardPackageSelected from './CardPackageSelected';
import { setCollapseFooterRecommend } from '@actions/payment';
import { PAGE } from '@constants/constants';
import LocalStorage from '@config/LocalStorage';

function FooterRecommend() {
  const { pathname } = useVieRouter();
  const { selectedPackage, listPackagesConfig, isLoginPaymentSuccess, isLoginPromotionSuccess } =
    useSelector((state: any) => state?.Payment || []);
  const { isCollapseFooterRecommend } = useSelector((state: any) => state?.Payment) || {};
  const dispatch = useDispatch();
  // const isPVODContent = useMemo(() => pathname?.includes(PAGE.PVOD_CONTENT), [pathname]);
  const timerRef = useRef<any>(null);
  const packageConfig = useMemo(
    () => listPackagesConfig.find((item: any) => item?.id === selectedPackage?.id) || {},
    [selectedPackage, listPackagesConfig]
  );

  const iconClass = isCollapseFooterRecommend
    ? 'vie-chevron-up-r-medium'
    : 'vie-chevron-down-red-medium';
  const [relatedData, setRelate] = useState<any>(null);
  const slugPath: any = ConfigLocalStorage.get(LocalStorage.FROM_URL) || '';
  const handleChangeRelatedVod = async () => {
    let result: any = [];
    if (slugPath.includes('livestream')) {
      const streamId = ConfigLocalStorage.get('STREAM_ID');
      if (!streamId) return;
      const slugStream = ConfigLocalStorage.get('selectedLivestreamSlug');
      const streamSelectedData = await PageApi.getLivestreamEvents({ slug: slugStream, dispatch });
      if (streamSelectedData) {
        const data = await LiveStreamApi.eventRelated({ id: streamId });
        if (data) {
          result = [streamSelectedData, ...(data?.items || [])];
        } else {
          result = [streamSelectedData];
        }
      }
    } else if (slugPath.includes('truyen-hinh-truc-tuyen')) {
      const contentDetail = JSON.parse(ConfigLocalStorage.get('livetvDetail') as string);
      if (contentDetail) {
        result = [contentDetail];
      }
    } else {
      // pvod
      const contentId = ConfigLocalStorage.get('currentContentId');
      if (contentId) {
        const contentData = await DetailApi.getContentById({ contentId });
        const dataRelate = [contentData?.data];
        if (dataRelate) {
          let slugApi = '';
          const host = window.location.hostname;
          if (slugPath.includes(host)) {
            slugApi = slugPath.replace(`${window.location.protocol}//${host}`, '');
          } else {
            slugApi = slugPath;
          }
          const { slugFromPath }: any = await handleGetSlugEpsFromPath({ path: slugApi });
          const getRelatedData = await ContentApi.contentRecommendVod({
            contentSlug: slugFromPath
          });
          if (getRelatedData?.data?.items) {
            result = [...dataRelate, ...getRelatedData.data.items];
          } else {
            result = dataRelate;
          }
        }
      }
    }
    setRelate(result);
  };

  const handleRecommendedPackageSelected = () => {
    let result: any = [];
    const pkgId = window.location.href.includes(`pkg=${selectedPackage?.id}`);
    if (pkgId) {
      result = {
        title: selectedPackage?.name,
        banner: selectedPackage?.banner,
        bannerInfoGroup: selectedPackage?.bannerInfoGroup,
        benefits: [packageConfig?.benefits]
      };
    }
    setRelate(result);
    dispatch(setCollapseFooterRecommend(!isCollapseFooterRecommend));
  };

  const handleCollapse = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    const trackingPayment = new TrackingPayment();
    if (isCollapseFooterRecommend) {
      trackingPayment.userHideAdvertisement();
      dispatch(setCollapseFooterRecommend(false));
    } else {
      trackingPayment.userOpenAdvertisement();
      dispatch(setCollapseFooterRecommend(true));
    }
  };

  useEffect(() => {
    if (!slugPath && isEmpty(selectedPackage)) {
      setRelate(null);
      return;
    }
    if (!slugPath && !isEmpty(selectedPackage)) {
      handleRecommendedPackageSelected();
      return;
    }
    handleChangeRelatedVod();
  }, [slugPath, pathname, selectedPackage]);

  useEffect(
    () => () => {
      setRelate(null);
      ConfigLocalStorage.remove('currentContentId');
    },
    []
  );

  useEffect(() => {
    if (isLoginPaymentSuccess || isLoginPromotionSuccess) {
      dispatch(setCollapseFooterRecommend(true));
    } else {
      dispatch(setCollapseFooterRecommend(false));
    }

    const TIMER = 5000;

    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    if (!isEmpty(relatedData) || !isEmpty(packageConfig)) {
      timerRef.current = setTimeout(() => {
        dispatch(setCollapseFooterRecommend(true));
      }, TIMER);
    }
    return () => clearTimeout(timerRef.current);
  }, [relatedData, packageConfig]);

  useEffect(() => {
    if (relatedData?.length === 0 || window?.location?.href.includes('curPage=home')) return;
    const trackingPayment = new TrackingPayment();
    trackingPayment.advertisementLoaded();
  }, [relatedData]);

  if (
    !relatedData ||
    relatedData?.length === 0 ||
    (window.location.href.includes('curPage=home') && isEmpty(selectedPackage)) ||
    pathname === PAGE.PAYMENT
  ) {
    return;
  }

  return (
    <>
      <div className="sticky bottom-0 z-[55] w-full">
        <div
          className={classNames(
            'relative before:w-screen before:md:hidden before:absolute before:right-0 before:bottom-0 before:left-0 before:bg-black/50 before:z-[1]',
            isCollapseFooterRecommend ? 'before:h-0' : 'before:h-screen'
          )}
        >
          <div className="relative z-[2] pb-[1px]">
            <Button
              iconName={iconClass}
              className="absolute right-4 2xl:right-[6%] top-[-2.4375rem] w-[3.25rem] h-[2.5rem] flex justify-center items-center z-[3] bg-white border border-solid border-[#c7c7c7] border-b-0 rounded-t-2xl rounded-b-0 text-black"
              onClick={handleCollapse}
            />
            <div
              className={`bg-white shadow-md drop-shadow-lg border-0 border-[#c7c7c7] border-solid transition-all duration-300 ease-in-out ${
                isCollapseFooterRecommend
                  ? 'max-h-0 overflow-hidden'
                  : 'max-h-[1000px] border-t-[1px]'
              }`}
            >
              {slugPath ? (
                <CardListFooter data={relatedData} />
              ) : (
                <CardPackageSelected data={relatedData} />
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default FooterRecommend;
