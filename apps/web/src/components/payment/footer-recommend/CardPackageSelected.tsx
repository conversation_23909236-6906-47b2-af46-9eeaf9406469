import React from 'react';
import { useRouter } from 'next/router';
import classNames from 'classnames';
import Image from '@/components/basic/Image/Image';
import Button from '@/components/basic/Buttons/Button';
import ConfigImage from '@/config/ConfigImage';
import styles from './Recommend.module.scss';
import isEmpty from 'lodash/isEmpty';

const CardPackageSelected = ({ data }: any) => {
  const { title, banner, bannerInfoGroup, benefits } = data || {};
  const router = useRouter();
  const handleBack = () => {
    router.push('/mua-goi/?curPage=home');
  };

  return (
    <div className={styles.Card}>
      <div className={styles.CardThumb}>
        <Image
          src={banner}
          alt={title}
          className="w-full h-full object-cover rounded-lg max-h-full"
          notWebp
        />
        <div className="w-[61%] max-h-full absolute top-4 left-4 space-y-2">
          <Image className="w-full" src={bannerInfoGroup} alt={title} notWebp />
        </div>
      </div>
      <div className="flex flex-col md:self-start w-full md:max-w-[calc(100%-250px)] space-y-3">
        {title && <h2 className="!text-[18px] md:!text-[24px] font-medium text-black">{title}</h2>}
        {!isEmpty(benefits) && (
          <div className="space-y-2">
            {benefits?.[0]?.map((benefit: any, index: any) => (
              <div
                key={`${benefit.id}benefit-${index}`}
                className={classNames(
                  styles.Benefit,
                  !benefit?.isEnabled && styles.BenefitDisabled
                )}
              >
                <div className={styles.BenefitIcon}>
                  <Image src={benefit?.icon || ConfigImage.iconBenefit} alt="benefit" notWebp />
                </div>
                <div className={styles.BenefitTitle}>{benefit?.title}</div>
              </div>
            ))}
          </div>
        )}
      </div>
      <div className="relative h-auto md:h-[136px] flex justify-center items-center w-full md:max-w-[214px]">
        <Button
          className="px-7 py-3 text-[#0AD418] border-[#CCEBCE] bg-[#0BBC16]/10 rounded-full hover:text-white hover:bg-[#2FB138] hover:border-vo-green"
          title="Tham khảo gói khác"
          onClick={handleBack}
        />
      </div>
    </div>
  );
};

export default CardPackageSelected;
