import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import Image from '../../../basic/Image/Image';
import ConfigImage from '@config/ConfigImage';
import ConfigLocalStorage from '@config/ConfigLocalStorage';

const CardListFooter = ({ data }: any) => {
  const packagesData = useSelector((state: any) => state?.Payment?.packages);
  const router = useRouter();
  const [selectedPackage, setSelectedPackage] = useState<any>({});

  useEffect(() => {
    if (router.query?.pkg) {
      ConfigLocalStorage.set('selectedPkg', router.query?.pkg);
    }
    const pkgRedirect = ConfigLocalStorage.get('selectedPkg');
    setSelectedPackage(
      packagesData?.filter((item: any) => item.id === Number(pkgRedirect))[0] ||
        data[0]?.packages?.[0]
    );
  }, []);

  return (
    <div className="flex flex-col md:flex-row items-center justify-center md:justify-start px-4 md:px-[10%] py-[20px] space-y-4 md:space-y-0 md:space-x-9">
      <div className="relative h-[120px] md:h-[136px] flex justify-center items-center w-full max-w-[189px] md:max-w-[214px]">
        {data?.slice(0, 3)?.map((item: any, index: any) => (
          <div
            key={index}
            className="absolute rounded-lg overflow-hidden shadow-xl transition-transform duration-300 hover:z-10 hover:scale-105 bg-[#444444]"
            style={{
              width: `${100 - index * 5}%`,
              zIndex: data?.length - index,
              bottom: `${index * 15}px`
            }}
          >
            <Image
              src={item?.images?.thumbnail_v4 || item?.images?.thumbnail}
              alt={`Image ${index}`}
              className="w-full h-full object-cover rounded-lg aspect-[16/9] max-h-[105px] md:max-h-[120px]"
            />
          </div>
        ))}
      </div>
      {/* TODO: deleted  md:space-x-9 this line  */}
      <div className="flex flex-col md:self-start w-full md:max-w-[calc(100%-250px)] space-y-3">
        <h2 className="!text-[18px] md:!text-[24px] font-medium text-black">
          Xem trọn vẹn
          {data[0]?.title && (
            <span className="text-green-500 font-bold">
              {' '}
              {data[0]?.title?.length > 80
                ? `${data[0]?.title.slice(0, 80)}...`
                : data[0]?.title}{' '}
            </span>
          )}{' '}
          với gói {selectedPackage?.name}
        </h2>
        <div className="space-y-1 text-[14px] md:text-[16px]">
          <p className="font-bold leading-[1.25]">
            Khám phá thêm hàng ngàn nội dung khác hấp dẫn khác với gói {selectedPackage?.name}
          </p>
          {data.length > 1 && (
            <ul>
              {data?.slice(1, 3)?.map((item: any, index: any) => (
                <li className="flex items-center" key={index}>
                  <div className="flex w-5 h-5 mr-1 mb-[2px]">
                    <Image
                      rootClass="w-full h-full"
                      src={ConfigImage.iconBenefit}
                      alt="benefit"
                      notWebp
                    />
                  </div>
                  {item.title && <span className="w-full truncate block">{item.title}</span>}
                </li>
              ))}
              {data.length > 3 && (
                <li className="flex items-center">
                  <div className="flex w-5 h-5 mr-1">
                    <Image
                      rootClass="w-full h-full"
                      src={ConfigImage.iconBenefit}
                      alt="benefit"
                      notWebp
                    />
                  </div>
                  Và nhiều nội dung khác nữa...
                </li>
              )}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
};

export default CardListFooter;
