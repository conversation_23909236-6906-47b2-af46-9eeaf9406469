import React, { useCallback, useMemo } from 'react';
import { useVieRouter } from '@customHook';
import { PAGE } from '@constants/constants';
import { TEXT } from '@constants/text';
import { useSelector } from 'react-redux';

const LocationNotes = React.memo(() => {
  const router = useVieRouter();
  const { pathname } = router || {};
  const { packageDiscount, discountInfo } = useSelector((state: any) => state?.Payment);

  const hrefInfo = useMemo(() => {
    if (pathname === PAGE.ZALOPAY || pathname === PAGE.ZALOPAY_METHOD) {
      return { href: PAGE.ZALOPAY_USAGE, target: '' };
    }
    return { href: PAGE.USAGE, target: '_blank' };
  }, [pathname]);

  const onClick = useCallback(() => {
    if (!hrefInfo?.target) {
      router.push({ pathname: hrefInfo?.href }, { pathname: hrefInfo?.href });
    } else {
      window.open(hrefInfo?.href, hrefInfo?.target);
    }
  }, [hrefInfo]);

  const isPackageDiscount =
    useMemo(
      () => packageDiscount?.some((item: any) => item?.type === 'PACKAGE_DISCOUNT'),
      [packageDiscount]
    ) || false;

  if (!isPackageDiscount) return null;

  return (
    <div className="notify notify--region dark">
      <div className="notify-container canal-v">
        <div className="grid-x align-middle justify-content-center">
          <span className="icon icon-16">
            <i className="vie vie-bell-o-rc" />
          </span>
          <span className="text padding-xlarge-up-left-8 padding-small-up-left-16 text-12">
            {discountInfo}
            <a className="link underline pl-1" onClick={onClick} title={TEXT.FIGURE_OUT}>
              {TEXT.FIGURE_OUT}
            </a>
          </span>
        </div>
      </div>
    </div>
  );
});

export default LocationNotes;
