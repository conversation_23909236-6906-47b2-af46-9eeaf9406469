import React from 'react';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';
import { VieON_TEL } from '@constants/constants';

const ErrorTransaction = () => (
  <div className="block block--result block--result-pm">
    <div className="mask">
      <div className="mask__inner ratio-variant-result">
        <div className="mask__img absolute">
          <img src={ConfigImage.catFailed} alt={TEXT.ERROR_TRANSACTION} />
        </div>
      </div>
    </div>
    <div className="block__header">
      <h2 className="block__title text-center">{TEXT.ERROR_TRANSACTION}</h2>
    </div>
    <div className="block__body">
      <div className="list-group">
        <div className="list-group__item divide--dashed-non-last align-middle">
          <div className="text-center">
            <PERSON><PERSON><PERSON> cần hỗ trợ, vui lòng liên hệ hotline VieON (miễn phí){' '}
            <a className="highlight" href={`tel:${VieON_TEL}`}>
              {VieON_TEL}
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default ErrorTransaction;
