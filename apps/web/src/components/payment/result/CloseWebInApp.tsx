import React from 'react';
import { TEXT } from '@constants/text';

declare const window: any;

const CloseWebInApp = ({ inApp }: any) => {
  if (!inApp) return null;

  const handleCloseInApp = () => {
    if (typeof window !== 'undefined') {
      if (window.ZaloPay) {
        window.ZaloPay.closeWindow();
      }
    }
  };

  return (
    <div
      className="button-group"
      style={{
        position: 'fixed',
        padding: '14px',
        backgroundColor: '#f5f5f5',
        width: '100%',
        left: 0,
        bottom: 0
      }}
    >
      <button
        className="button text-white auto hide-for-large-only"
        style={{ backgroundColor: '#006DFF', width: '100%' }}
        onClick={handleCloseInApp}
      >
        {TEXT.CLOSED}
      </button>
    </div>
  );
};

export default CloseWebInApp;
