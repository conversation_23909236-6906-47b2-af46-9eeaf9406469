import React, { useEffect, useMemo, useState } from 'react';
import User<PERSON>pi from '@apis/userApi';
import TrackingPayment from '@tracking/functions/payment';
import { validateEmail, encodeParamDestination } from '@helpers/common';
import InputLabelSlideUp from '@components/basic/Input/InputLabelSlideUp';
import { TEXT } from '@constants/text';
import LocalStorage from '@config/LocalStorage';
import { useDispatch, useSelector } from 'react-redux';
import { setToast } from '@actions/app';
import { CURRENCY } from '@constants/constants';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import CloseWebInApp from './CloseWebInApp';
import { destinationLogin } from '@services/multiProfileServices';
import ConfigCookie from '@config/ConfigCookie';
import { NAME, PROPERTY } from '../../../config/ConfigSegment';
import { segmentEvent } from '../../../tracking/TrackingSegment';

declare const window: any;

const SUB_TEXT_SUCCESS_SMART_TV = 'Hãy tiếp tục trải nghiệm VieON trên TV của bạn';
const TEXT_SEND_INFO_TRANSACTION_TO_PHONE_NUMBER =
  'Thông tin giao dịch chi tiết sẽ được gửi về số điện thoại';
const TEXT_SEND_INFO_TRANSACTION_TO_EMAIL = 'Thông tin giao dịch chi tiết sẽ được gửi về email';
const TEXT_REQUEST_INPUT_EMAIL = 'Nhập email để nhận thêm thông tin giao dịch chi tiết';
const titleBtn = 'Cập nhật';
const trackingPayment = new TrackingPayment();

const Success = (props: any) => {
  const {
    router,
    orderId,
    packageName,
    displayDuration,
    recurring,
    expiryDate,
    effectiveDate,
    amount,
    inAppZalo,
    profile,
    supportSmartTv,
    txnRef
  } = props || {};
  const dispatch = useDispatch();
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile || {});
  const returnUrl =
    ConfigLocalStorage.get(LocalStorage.FROM_URL_TRIGGER_TRIAL) ||
    ConfigLocalStorage.get(LocalStorage.FROM_URL);
  const { mobile, phoneVerified, email, emailVerified } = profile || {};
  const phoneNumber = useMemo(
    () => (mobile && phoneVerified ? mobile : ''),
    [mobile, phoneVerified]
  );
  const emailUser = useMemo(() => (email && emailVerified ? email : ''), [email, emailVerified]);

  const [emailValue, setEmailValue] = useState<any>('');
  const [error, setError] = useState<any>('');
  const [isUpdatedEmail, setIsUpdatedEmail] = useState(false);
  const [validatedEmail, setValidatedEmail] = useState(false);

  const onChange = (e: any) => {
    const value = e.target?.value;
    setError('');
    setEmailValue(value);
    const resultValidate = validateEmail(value);
    setValidatedEmail(resultValidate);
  };

  const onUpdate = async () => {
    if (validatedEmail) {
      await setUserTransaction(
        {
          email: emailValue,
          txn_ref: orderId
        },
        'email'
      );
    } else {
      setError(TEXT.EMAIL_WRONG);
    }
  };

  const setUserTransaction = async (params: any, contactMethod: any) => {
    const result = await UserApi.setUserTransaction(params);
    handleResult(result);
    // SEGMENT TRACKING
    trackingPayment.updateInfo(contactMethod);
    segmentEvent(NAME.VOUCHER_CODE_INPUTTED, {
      [PROPERTY.CURRENT_PAGE]: window.location.href,
      [PROPERTY.VOUCHER_CODE]: params?.voucherCode || '',
      [PROPERTY.CAUSE_FOR_FAILURE]: params?.message || '',
      [PROPERTY.RESULT]: result,
      [PROPERTY.VOUCHER_TYPE]: 'gift code'
    });
  };

  const handleResult = (result: any) => {
    let message = TEXT.UPDATE_SUCCESS;
    if (!result?.success) {
      message = result?.data?.message || TEXT.MSG_ERROR;
    } else {
      setIsUpdatedEmail(true);
    }
    dispatch(setToast({ message }));
  };

  const handleClickContinueWatching = () => {
    if (!currentProfile?.id) {
      const remakeDestination = encodeParamDestination(returnUrl);

      const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};

      destinationLogin({
        dataLogin: {
          profile,
          accessToken
        },
        destination: remakeDestination,
        router,
        dispatch
      });
    } else {
      window.location = returnUrl || '/';
    }
  };

  const handleTextSendEmail = () => {
    if (supportSmartTv) {
      return <div className="text normal text-center">{SUB_TEXT_SUCCESS_SMART_TV}</div>;
    }
    if (phoneNumber) {
      return (
        <div className="text text-gray117 normal text-center">
          {TEXT_SEND_INFO_TRANSACTION_TO_PHONE_NUMBER}{' '}
          <span className="text-bold">{phoneNumber}</span>
        </div>
      );
    }
    if (emailValue && isUpdatedEmail) {
      return (
        <div className="text text-gray117 normal text-center">
          {TEXT_SEND_INFO_TRANSACTION_TO_EMAIL} <span className="text-bold">{emailValue}</span>
        </div>
      );
    }
    return null;
  };

  useEffect(() => {
    if (emailUser) {
      setIsUpdatedEmail(true);
      setEmailValue(emailUser);
    } else {
      setIsUpdatedEmail(false);
      setEmailValue('');
    }
  }, [emailUser]);

  return (
    <div className="block block--result block--result-pm">
      <div className="icon icon--variant-80 circle-outline m-x-auto overspread-animate">
        <i className="vie vie-tick" />
      </div>
      <div className="block__header m-b">
        <h2 className="block__title text-center">{TEXT.TRANSACTION_SUCCESS}</h2>
      </div>
      <div className="block__body">
        <div className="list-group">
          {(supportSmartTv || phoneNumber || (emailValue && isUpdatedEmail)) && (
            <div className="list-group__item divide--dashed-non-last">{handleTextSendEmail()}</div>
          )}
          {!supportSmartTv && !phoneNumber && !isUpdatedEmail && (
            <div className="list-group__item divide--dashed-non-last">
              <div className="text normal">{TEXT_REQUEST_INPUT_EMAIL}</div>
              <InputLabelSlideUp
                disabledBtn={!validatedEmail}
                value={emailValue}
                onChange={onChange}
                id="email-send-transaction"
                title={TEXT.USER_LABEL.EMAIL}
                placeholder={TEXT.USER_LABEL.EMAIL}
                error={error}
                hasBtn
                titleBtn={titleBtn}
                onSubmit={onUpdate}
              />
            </div>
          )}
          <div className="list-group__item divide--dashed-non-last align-middle">
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">{TEXT.TRANSACTION_CODE}</span>
              </div>
              {(txnRef || orderId) && (
                <div className="cell small-7">
                  <div className="text-f-m text-right text-large-up-16 text-medium break-all">
                    {`#${txnRef || orderId || ''}`}
                  </div>
                </div>
              )}
            </div>
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">{TEXT.PACKAGE_NAME}</span>
              </div>
              <div className="cell small-7">
                <div className="text-f-m text-right text-large-up-16 text-medium">
                  {packageName || ''}
                </div>
              </div>
            </div>
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">{TEXT.TERM_NAME}</span>
              </div>
              <div className="cell small-7">
                <div className="text-f-m text-right text-large-up-16 text-medium">
                  {displayDuration || ''}
                </div>
              </div>
            </div>
          </div>
          <div className="list-group__item divide--dashed-non-last">
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">{TEXT.EFFECTIVE_DATE}</span>
              </div>
              <div className="cell small-7">
                <div className="text-f-m text-right text-large-up-16 text-medium">
                  {effectiveDate || ''}
                </div>
              </div>
            </div>
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">{TEXT.USE_UP_TO}</span>
              </div>
              <div className="cell small-7">
                <div className="text-f-m text-right text-large-up-16 text-medium">
                  {recurring ? TEXT.WHEN_YOU_CANCEL : expiryDate}
                </div>
              </div>
            </div>
            {recurring && (
              <div className="grid-x grid-margin-x align-justify">
                <div className="cell small-5">
                  <span className="text normal text-nowrap">{TEXT.NEXT_CHECK_OUT}</span>
                </div>
                <div className="cell small-7">
                  <div className="text-f-m text-right text-large-up-16 text-medium">
                    {expiryDate}
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="list-group__item divide--dashed-non-last">
            <div className="grid-x grid-margin-x align-justify align-middle">
              <div className="cell small-6">
                <span className="text normal">{TEXT.TOTAL_AMOUNT}</span>
              </div>
              <div className="cell small-6">
                <div className="!text-[1.25rem] md:!text-[1.75rem] text-vo-green !text-right font-bold">
                  {`${new Intl.NumberFormat('vi-VN').format(amount || 0)} ${CURRENCY.VND}`}
                </div>
              </div>
            </div>
          </div>
        </div>
        {!inAppZalo && !supportSmartTv && (
          <div className="button-group child-auto">
            <a
              className="button button--green button--large"
              title={returnUrl ? TEXT.CONTINUE_WATCH_CONTENT : TEXT.BACK}
              onClick={handleClickContinueWatching}
            >
              <span className="text !text-white">
                {returnUrl ? TEXT.CONTINUE_WATCH_CONTENT : TEXT.BACK}
              </span>
            </a>
          </div>
        )}
      </div>
      <CloseWebInApp inApp={inAppZalo} />
    </div>
  );
};

export default React.memo(Success);
