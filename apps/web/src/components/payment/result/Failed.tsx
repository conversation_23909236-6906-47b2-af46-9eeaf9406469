import React from 'react';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';
import { ERROR_CODE, PAGE, VieON_TEL } from '@constants/constants';
import { useSelector } from 'react-redux';
import CloseWebInApp from './CloseWebInApp';
import Button from '../../basic/Buttons/Button';

const Failed = ({
  orderId,
  tel,
  inAppZalo,
  supportSmartTv,
  pkgId,
  router,
  name,
  txnRef,
  isPvod
}: any) => {
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile || {});
  const dataTemporary = useSelector((state: any) => state?.Payment?.dataTemporary || {});
  const transactionResult = useSelector((state: any) => state?.Payment?.transactionResult || {});
  const { currentUrl } = dataTemporary || {};
  const { errorMessage, errorDescMsg, errorCode, errorCodeMsg, status, errorString } =
    transactionResult || {};
  const isError104 = errorCode === ERROR_CODE.CODE_104;
  const title = isError104 ? TEXT.TRANSACTION_CANT_BE_TRACED : TEXT.UNSUCCESSFULLY_BUY_TITLE;
  const contactNote = tel
    ? `Vui lòng liên hệ hotline VieON (miễn phí) <a class='highlight text-bold' href=${`tel:${VieON_TEL}`}>${VieON_TEL}</a> hoặc hotline ${name} <a class='highlight text-bold' href=${`tel:${tel}`}>${tel}</a> để được hỗ trợ.`
    : `Vui lòng liên hệ hotline VieON (miễn phí) <a class='highlight text-bold' href=${`tel:${VieON_TEL}`}>${VieON_TEL}</a> để được hỗ trợ.`;

  const onRetry = () => {
    if (currentUrl) {
      window.location.href = currentUrl?.url;
    } else {
      router.push({
        pathname: inAppZalo ? PAGE.ZALOPAY_METHOD : PAGE.PAYMENT_METHOD,
        query: { pkg: pkgId }
      });
    }
  };

  const handleClickContinueWatching = () => {
    if (!currentProfile?.id) {
      router.push(PAGE.LOBBY_PROFILES);
    } else {
      router.push(PAGE.HOME);
    }
  };

  return (
    <div className="block block--result block--result-pm">
      <div className="mask">
        <div className="mask__inner ratio-variant-result">
          <div className="mask__img absolute">
            <img src={ConfigImage.catFailed} alt={TEXT.UNSUCCESSFULLY_BUY_TITLE} />
          </div>
        </div>
      </div>
      <div className="block__header">
        <div className="text block__title text-center text-24 text-large-up-28">{title}</div>
      </div>
      <div className="block__body">
        <div className="list-group">
          <div className="list-group__item divide--dashed-non-last align-middle">
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">{TEXT.TRANSACTION_CODE}</span>
              </div>
              {(txnRef || orderId) && (
                <div className="cell small-7">
                  <div className="text-f-m text-right">{`#${txnRef || orderId || ''}`}</div>
                </div>
              )}
            </div>
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">{TEXT.ERROR_CODE}</span>
              </div>
              <div className="cell small-7">
                <div className="text-f-m text-right">{errorCodeMsg || errorCode || status}</div>
              </div>
            </div>
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">{TEXT.ERROR}</span>
              </div>
              <div className="cell small-7">
                <div className="text-f-m text-right">
                  {errorDescMsg || errorMessage || errorString || '-'}
                </div>
              </div>
            </div>
          </div>
          <div className="list-group__item divide--dashed-non-last">
            <div className="text-center" dangerouslySetInnerHTML={{ __html: contactNote }} />
          </div>
        </div>

        <div className="button-group child-auto">
          {!supportSmartTv && !inAppZalo && !isPvod && (
            <Button
              className={`button button--large !text-[1rem]${
                isError104 ? ' button--green !text-white' : ' hollow button--gray40'
              }`}
              title={TEXT.CONTINUE_WATCH_VieON}
              onClick={handleClickContinueWatching}
            />
          )}
          {!supportSmartTv && !inAppZalo && isPvod && (
            <a
              href="/"
              className={`button button--large ${
                isError104 ? 'button--green !text-white' : 'hollow button--gray40'
              }`}
              title={TEXT.BACK_HOME}
            >
              <span className="!text-[1rem]">{TEXT.BACK_HOME}</span>
            </a>
          )}

          {!supportSmartTv && !isError104 && (
            <Button
              className="button button--green button--large !text-white !text-[1rem]"
              title={TEXT.RETRY}
              onClick={onRetry}
            />
          )}
        </div>
      </div>
      <CloseWebInApp inApp={inAppZalo} />
    </div>
  );
};

export default Failed;
