import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import InputLabelSlideUp from '@components/basic/Input/InputLabelSlideUp';
import UserApi from '@apis/userApi';
import { TEXT } from '@constants/text';
import { setToast } from '@actions/app';
import { validateEmail } from '@helpers/common';
import TrackingPayment from '@tracking/functions/payment';
import LocalStorage from '@config/LocalStorage';
import ConfigLocalStorage from '@config/ConfigLocalStorage';

declare const window: any;

const SUB_TEXT_SUCCESS_SMART_TV = 'Hãy tiếp tục trải nghiệm VieON trên TV của bạn';
const TEXT_SEND_INFO_TRANSACTION_TO_PHONE_NUMBER =
  'Thông tin giao dịch chi tiết sẽ được gửi về số điện thoại';
const TEXT_SEND_INFO_TRANSACTION_TO_EMAIL = 'Thông tin giao dịch chi tiết sẽ được gửi về email';
const TEXT_REQUEST_INPUT_EMAIL = 'Nhập email để nhận thêm thông tin giao dịch chi tiết';
const titleBtn = 'Cập nhật';
const trackingPayment = new TrackingPayment();

const PVodSuccess = ({ orderId, profile, supportSmartTv, transactionResult }: any) => {
  const { mobile, phoneVerified, email, emailVerified } = profile || {};
  const dispatch = useDispatch();
  const phoneNumber = useMemo(
    () => (mobile && phoneVerified ? mobile : ''),
    [mobile, phoneVerified]
  );
  const emailUser = useMemo(() => (email && emailVerified ? email : ''), [email, emailVerified]);
  const returnUrl = ConfigLocalStorage.get(LocalStorage.FROM_URL_FAST_TRACK);
  const watchNowUrl = ConfigLocalStorage.get(LocalStorage.FROM_URL);
  const isBannerTriggerPvod = ConfigLocalStorage.get(LocalStorage.IS_BANNER_TRIGGER_PVOD);
  const [emailValue, setEmailValue] = useState<any>('');
  const [error, setError] = useState<any>('');
  const [isUpdatedEmail, setIsUpdatedEmail] = useState(false);
  const [validatedEmail, setValidatedEmail] = useState(false);
  const { txnId, productNameMsg, totalPriceMsg, contentMsg } = transactionResult || {};

  const onChange = (e: any) => {
    const value = e.target?.value;
    setError('');
    setEmailValue(value);
    const resultValidate = validateEmail(value);
    setValidatedEmail(resultValidate);
  };

  const onUpdate = async () => {
    if (validatedEmail) {
      await setUserTransaction(
        {
          email: emailValue,
          txn_ref: orderId
        },
        'email'
      );
    } else {
      setError(TEXT.EMAIL_WRONG);
    }
  };

  const setUserTransaction = async (params: any, contactMethod: any) => {
    const result = await UserApi.setUserTransaction(params);
    handleResult(result);
    // SEGMENT TRACKING
    trackingPayment.updateInfo(contactMethod);
  };

  const handleResult = (result: any) => {
    let message = TEXT.UPDATE_SUCCESS;
    if (!result?.success) {
      message = result?.data?.message || TEXT.MSG_ERROR;
    } else {
      setIsUpdatedEmail(true);
    }
    dispatch(setToast({ message }));
  };

  const handleTextSendEmail = () => {
    if (supportSmartTv) {
      return (
        <div className="text normal text-center font-size-sm-down-14 padding-small-up-top-12">
          {SUB_TEXT_SUCCESS_SMART_TV}
        </div>
      );
    }
    if (phoneNumber) {
      return (
        <div className="text text-gray117 normal text-center padding-small-up-top-12">
          {`${TEXT_SEND_INFO_TRANSACTION_TO_PHONE_NUMBER} `}
          <span className="text-bold">{phoneNumber}</span>
        </div>
      );
    }
    if (emailValue && isUpdatedEmail) {
      return (
        <div className="text text-gray117 normal text-center padding-small-up-top-12">
          {`${TEXT_SEND_INFO_TRANSACTION_TO_EMAIL} `}
          <span className="text-bold">{emailValue}</span>
        </div>
      );
    }
    return null;
  };

  const onclickWatchContent = () => {
    if (watchNowUrl) {
      if (isBannerTriggerPvod) return (window.location = returnUrl);
      return (window.location = watchNowUrl);
    }
    return (window.location = '/');
  };

  const backToIntro = () => {
    if (returnUrl) {
      window.location = returnUrl;
    } else {
      window.location = '/';
    }
  };

  useEffect(() => {
    if (emailUser) {
      setIsUpdatedEmail(true);
      setEmailValue(emailUser);
    } else {
      setIsUpdatedEmail(false);
      setEmailValue('');
    }
  }, [emailUser]);

  return (
    <div className="block block--result block--result-pm">
      <div className="icon icon--variant-80 circle-outline m-x-auto overspread-animate">
        <i className="vie vie-tick" />
      </div>
      <div className="block__header m-b">
        <h2 className="block__title text-center">{TEXT.TRANSACTION_SUCCESS}</h2>
      </div>
      <div className="block__body">
        <div className="list-group">
          {(supportSmartTv || phoneNumber || (emailValue && isUpdatedEmail)) && (
            <div className="list-group__item divide--dashed-non-last">{handleTextSendEmail()}</div>
          )}
          {!supportSmartTv && !phoneNumber && !isUpdatedEmail && (
            <div className="list-group__item divide--dashed-non-last">
              <div className="text normal">{TEXT_REQUEST_INPUT_EMAIL}</div>
              <InputLabelSlideUp
                disabledBtn={!validatedEmail}
                value={emailValue}
                onChange={onChange}
                id="email-send-transaction"
                title={TEXT.USER_LABEL.EMAIL}
                placeholder={TEXT.USER_LABEL.EMAIL}
                error={error}
                hasBtn
                titleBtn={titleBtn}
                onSubmit={onUpdate}
              />
            </div>
          )}
          <div className="list-group__item divide--dashed-non-last align-middle">
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">{TEXT.TRANSACTION_CODE}</span>
              </div>
              <div className="cell small-7">
                <div className="text-f-m text-right text-large-up-16 text-medium break-all">
                  {`#${txnId || ''}`}
                </div>
              </div>
            </div>
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">{TEXT.CONTENT_NAME}</span>
              </div>
              <div className="cell small-7">
                <div className="text-f-m text-right text-large-up-16 text-medium">
                  {productNameMsg || ''}
                </div>
              </div>
            </div>
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">{TEXT.EPISODE}</span>
              </div>
              <div className="cell small-7">
                <div className="text-f-m text-right text-large-up-16 text-medium">
                  {contentMsg || ''}
                </div>
              </div>
            </div>
          </div>

          <div className="list-group__item divide--dashed-non-last">
            <div className="grid-x grid-margin-x align-justify align-middle">
              <div className="cell small-5">
                <span className="text normal">{TEXT.TOTAL_AMOUNT}</span>
              </div>
              <div className="cell small-7">
                <div className="text text-f-m large-x highlight text-right">{totalPriceMsg}</div>
              </div>
            </div>
          </div>
        </div>
        {!supportSmartTv && (
          <div className="button-group child-auto">
            {!isBannerTriggerPvod && (
              <button
                className="button hollow button--large"
                title={TEXT.BACK}
                onClick={backToIntro}
              >
                <span className="text text-black">{TEXT.BACK}</span>
              </button>
            )}
            <a
              className="button button--green button--large"
              title={!isBannerTriggerPvod ? TEXT.WATCH_NOW : TEXT.BACK}
              onClick={onclickWatchContent}
            >
              <span className="text !text-white">
                {!isBannerTriggerPvod ? TEXT.WATCH_NOW : TEXT.BACK}
              </span>
            </a>
          </div>
        )}
      </div>
    </div>
  );
};

export default PVodSuccess;
