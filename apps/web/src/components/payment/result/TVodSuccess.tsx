import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import InputLabelSlideUp from '@components/basic/Input/InputLabelSlideUp';
import User<PERSON>pi from '@apis/userApi';
import DetailApi from '@apis/detailApi';
import LiveStreamApi from '@apis/cm/PageApi';
import { TVOD, PAGE, CONTENT_TYPE_NOTIFY } from '@constants/constants';
import { TEXT } from '@constants/text';
import { setToast } from '@actions/app';
import { NAME, PROPERTY } from '@config/ConfigSegment';
import { validateEmail, encodeParamDestination } from '@helpers/common';
import { segmentEvent } from '@tracking/TrackingSegment';
import TrackingPayment from '@tracking/functions/payment';
import { getUserSubcribeNotifyComingSoon } from '@actions/user';
import LocalStorage from '@config/LocalStorage';
import ConfigLocalStorage from '@config/ConfigLocalStorage';

const SUB_TEXT_SUCCESS_SMART_TV = 'Hãy tiếp tục trải nghiệm VieON trên TV của bạn';
const TEXT_SEND_INFO_TRANSACTION_TO_PHONE_NUMBER =
  'Thông tin giao dịch chi tiết sẽ được gửi về số điện thoại';
const TEXT_SEND_INFO_TRANSACTION_TO_EMAIL = 'Thông tin giao dịch chi tiết sẽ được gửi về email';
const TEXT_REQUEST_INPUT_EMAIL = 'Nhập email để nhận thêm thông tin giao dịch chi tiết';
const titleBtn = 'Cập nhật';
const trackingPayment = new TrackingPayment();

const TVodSuccess = ({ orderId, profile, supportSmartTv, transactionResult }: any) => {
  const { mobile, phoneVerified, email, emailVerified } = profile || {};
  const router = useVieRouter();
  const { query } = router || {};
  const dispatch = useDispatch();
  const phoneNumber = useMemo(
    () => (mobile && phoneVerified ? mobile : ''),
    [mobile, phoneVerified]
  );
  const emailUser = useMemo(() => (email && emailVerified ? email : ''), [email, emailVerified]);
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile || {});
  const webConfig = useSelector((state: any) => state?.App?.webConfig || {});
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { dataTemporary } = useSelector((state: any) => state?.Payment);
  const dataQuery = useMemo(() => {
    if (supportSmartTv) {
      return query;
    }
    return dataTemporary;
  }, [supportSmartTv, dataTemporary, query]);
  const { id, type, isLiveEvent, isSimulcast } = dataQuery || {};
  const [content, setContent] = useState<any>(null);
  const isChangeToVod = !!content?.contentId;
  const [emailValue, setEmailValue] = useState<any>('');
  const [error, setError] = useState<any>('');
  const [isUpdatedEmail, setIsUpdatedEmail] = useState(false);
  const [validatedEmail, setValidatedEmail] = useState(false);

  const {
    txnId,
    productNameMsg,
    durationMsg,
    waitingDurMsg,
    totalPriceMsg,
    startDateMsg,
    consumingDurMsg
  } = transactionResult || {};
  const textReminderTVod = useMemo(() => {
    let temp = '';
    if (isLiveEvent === 'true') {
      temp = 'Chúng tôi sẽ gửi lời nhắc tới bạn ngay khi chương trình bắt đầu';
    }
    if (isSimulcast === 'true') temp = 'Chúng tôi sẽ gửi lời nhắc tới bạn ngay khi nội dung ra mắt';
    if (isLiveEvent === 'true' && content?.isLive) temp = '';
    return temp;
  }, [isSimulcast, isLiveEvent, content?.isLive]);

  const tvodNote = useMemo(() => {
    if (!waitingDurMsg || !consumingDurMsg) return '';
    let temp = webConfig?.tVod?.text?.paymentStep2Note || '';
    if (isLiveEvent === 'true') {
      if (content?.contentId) {
        temp = webConfig?.tVod?.text?.paymentStep2NoteForLiveEvent || '';
      } else {
        temp = '';
      }
    }
    if (isSimulcast === 'true') {
      temp = webConfig?.tVod?.text?.paymentStep2NoteForSimulcast || '';
    }
    return (temp || '')
      .replace('{waitingDurMsg}', waitingDurMsg)
      .replace('{consumingDurMsg}', consumingDurMsg);
  }, [waitingDurMsg, consumingDurMsg, isLiveEvent, isSimulcast, content?.contentId]);

  const onChange = (e: any) => {
    const value = e.target?.value;
    setError('');
    setEmailValue(value);
    const resultValidate = validateEmail(value);
    setValidatedEmail(resultValidate);
  };

  const onUpdate = async () => {
    if (validatedEmail) {
      await setUserTransaction(
        {
          email: emailValue,
          txn_ref: orderId
        },
        'email'
      );
    } else {
      setError(TEXT.EMAIL_WRONG);
    }
  };

  const setUserTransaction = async (params: any, contactMethod: any) => {
    const result = await UserApi.setUserTransaction(params);
    handleResult(result);
    // SEGMENT TRACKING
    trackingPayment.updateInfo(contactMethod);
  };

  const handleResult = (result: any) => {
    let message = TEXT.UPDATE_SUCCESS;
    if (!result?.success) {
      message = result?.data?.message || TEXT.MSG_ERROR;
    } else {
      setIsUpdatedEmail(true);
    }
    dispatch(setToast({ message }));
  };

  const handleTextSendEmail = () => {
    if (supportSmartTv) {
      return (
        <div className="text normal text-center font-size-sm-down-14 padding-small-up-top-12">
          {SUB_TEXT_SUCCESS_SMART_TV}
        </div>
      );
    }
    if (phoneNumber) {
      return (
        <div className="text text-gray117 normal text-center padding-small-up-top-12">
          {`${TEXT_SEND_INFO_TRANSACTION_TO_PHONE_NUMBER} `}
          <span className="text-bold">{phoneNumber}</span>
        </div>
      );
    }
    if (emailValue && isUpdatedEmail) {
      return (
        <div className="text text-gray117 normal text-center padding-small-up-top-12">
          {`${TEXT_SEND_INFO_TRANSACTION_TO_EMAIL} `}
          <span className="text-bold">{emailValue}</span>
        </div>
      );
    }
    return null;
  };

  const onclickWatchContent = () => {
    segmentEvent(
      NAME.TVOD.PAYMENT_SUCCESS_SELECT_WATCH,
      {
        [PROPERTY.FLOW_NAME]: TVOD.TVOD_TYPE
      },
      true
    );
    ConfigLocalStorage.set(LocalStorage.TOAST_NOTIFY_LIVE_EVENT, true);
    if (!currentProfile?.id) {
      const remakeDestination = encodeParamDestination(content?.seo?.url);
      router.push(`${PAGE.LOBBY_PROFILES}/?destination=${remakeDestination}`);
    } else {
      window.location = content?.seo?.url || '/';
    }
  };

  const backHome = () => {
    segmentEvent(
      NAME.TVOD.PAYMENT_SUCCESS_SELECT_HOMEPAGE,
      {
        [PROPERTY.FLOW_NAME]: TVOD.TVOD_TYPE
      },
      true
    );
  };

  useEffect(() => {
    if (id && type) {
      if (isLiveEvent === 'true') {
        LiveStreamApi.getLivestreamEventsById({ id }).then((res) => {
          setContent(res);
          if (!res?.data?.isLive) {
            dispatch(
              getUserSubcribeNotifyComingSoon(
                id,
                res?.data?.startTime,
                CONTENT_TYPE_NOTIFY.LIVE_EVENT
              )
            );
          }
        });
      } else {
        DetailApi.getContentById({ contentId: id, isGlobal }).then((res) => {
          setContent(res?.data);
        });
      }
    }
  }, [id, type, isLiveEvent]);

  useEffect(() => {
    if (emailUser) {
      setIsUpdatedEmail(true);
      setEmailValue(emailUser);
    } else {
      setIsUpdatedEmail(false);
      setEmailValue('');
    }
  }, [emailUser]);

  return (
    <div className="block block--result block--result-pm">
      <div className="icon icon--variant-80 circle-outline m-x-auto overspread-animate">
        <i className="vie vie-tick" />
      </div>
      <div className="block__header m-b">
        <h2 className="block__title text-center">{TEXT.TRANSACTION_SUCCESS}</h2>
        {textReminderTVod && (
          <div className="text text-center text-gray117">{textReminderTVod}</div>
        )}
      </div>
      <div className="block__body">
        <div className="list-group">
          {(supportSmartTv || phoneNumber || (emailValue && isUpdatedEmail)) && (
            <div className="list-group__item divide--dashed-non-last">{handleTextSendEmail()}</div>
          )}
          {!supportSmartTv && !phoneNumber && !isUpdatedEmail && (
            <div className="list-group__item divide--dashed-non-last">
              <div className="text normal">{TEXT_REQUEST_INPUT_EMAIL}</div>
              <InputLabelSlideUp
                disabledBtn={!validatedEmail}
                value={emailValue}
                onChange={onChange}
                id="email-send-transaction"
                title={TEXT.USER_LABEL.EMAIL}
                placeholder={TEXT.USER_LABEL.EMAIL}
                error={error}
                hasBtn
                titleBtn={titleBtn}
                onSubmit={onUpdate}
              />
            </div>
          )}
          <div className="list-group__item divide--dashed-non-last align-middle">
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">{TEXT.TRANSACTION_CODE}</span>
              </div>
              <div className="cell small-7">
                <div className="text-f-m text-right text-large-up-16 text-medium break-all">
                  {`#${txnId || ''}`}
                </div>
              </div>
            </div>
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">{TEXT.CONTENT_NAME}</span>
              </div>
              <div className="cell small-7">
                <div className="text-f-m text-right text-large-up-16 text-medium">
                  {productNameMsg || ''}
                </div>
              </div>
            </div>
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">
                  {isLiveEvent === 'true' && !isChangeToVod ? `${TEXT.TERM}` : `${TEXT.TERM}*`}
                </span>
              </div>
              <div className="cell small-7">
                <div className="text-f-m text-right text-large-up-16 text-medium">
                  {isLiveEvent === 'true' && !isChangeToVod ? TEXT.LIVE_EVENT_ENDED : durationMsg}
                </div>
              </div>
            </div>
          </div>
          <div className="list-group__item divide--dashed-non-last">
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-5">
                <span className="text normal">{TEXT.TRANSACTION_DATE}</span>
              </div>
              <div className="cell small-7">
                <div className="text-f-m text-right text-large-up-16 text-medium">
                  {startDateMsg || ''}
                </div>
              </div>
            </div>
          </div>
          <div className="list-group__item divide--dashed-non-last">
            <div className="grid-x grid-margin-x align-justify align-middle">
              <div className="cell small-5">
                <span className="text normal">{TEXT.TOTAL_AMOUNT}</span>
              </div>
              <div className="cell small-7">
                <div className="text text-f-m large-x highlight text-right">{totalPriceMsg}</div>
              </div>
            </div>
          </div>
        </div>
        {!supportSmartTv && (
          <div className="button-group child-auto">
            <a
              className="button hollow button--large"
              href="/"
              title={TEXT.BACK_HOME}
              onClick={backHome}
            >
              <span className="text text-black">{TEXT.BACK_HOME}</span>
            </a>
            {content && (
              <a
                className="button button--green button--large"
                title={TEXT.WATCH_CONTENT}
                onClick={onclickWatchContent}
              >
                <span className="text !text-white">{TEXT.WATCH_CONTENT}</span>
              </a>
            )}
          </div>
        )}
        {tvodNote && (
          <div
            className="text text-center text-gray117 md:pt-1 !text-[.875rem]"
            dangerouslySetInnerHTML={{ __html: tvodNote }}
          />
        )}
      </div>
    </div>
  );
};

export default TVodSuccess;
