import React from 'react';
import ConfigImage from '@config/ConfigImage';

const EmptyWatchLater = ({ title }: any) => (
  <section className="section section--error overflow">
    <div className="container">
      <div className="section-body">
        <div className="empty empty--error">
          <div className="content text-center">
            <img className="inline-block" src={ConfigImage.emptyWatched} alt="Empty content" />
            <p className="text p-y3">
              {title || 'Thêm ngay các chương trình, nội dung hấp dẫn vào danh sách để không bỏ lỡ'}
            </p>
            <a
              className="button secondary"
              aria-label="Trở về trang chủ"
              href="/"
              style={{ backgroundColor: '#3adb76' }}
            >
              Mở trang chủ
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
);
export default EmptyWatchLater;
