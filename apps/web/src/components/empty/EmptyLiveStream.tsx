import React from 'react';
import ConfigImage from '@config/ConfigImage';
import VieLink from '@components/VieLink';

const EmptyLiveStream = () => (
  <section className="section section--stream-player overflow">
    <div className="container">
      <div className="section-body">
        <div className="empty p-t6 m-x-auto" style={{ maxWidth: '21.5rem' }}>
          <div className="content text-center">
            <img
              className="inline-block"
              src={ConfigImage.emptyLiveStream}
              alt="Hiện không có chương trình trực tiếp nào"
            />
            <p className="text p-y3">Hiện không có chương trình trực tiếp nào</p>
            <VieLink href="/">
              <a className="button secondary size-w-full " title="Trở về Trang Chủ">
                Trở về Trang Chủ
              </a>
            </VieLink>
          </div>
        </div>
      </div>
    </div>
  </section>
);
export default EmptyLiveStream;
