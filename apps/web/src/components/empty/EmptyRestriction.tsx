import React from 'react';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';
import classNames from 'classnames';
import styles from './Empty.module.scss';

const EmptyRestriction = () => (
  <div className={classNames(styles.restrictionContent)}>
    <img
      className={classNames(styles.restrictionContentImage)}
      src={ConfigImage.emptyContentRestriction}
      alt={TEXT.USER_NOT_RESTRICTION_CONTENT}
    />
    <p
      className={classNames(styles.restrictionContentText)}
      dangerouslySetInnerHTML={{ __html: TEXT.USER_NOT_RESTRICTION_CONTENT }}
    />
  </div>
);

export default React.memo(EmptyRestriction);
