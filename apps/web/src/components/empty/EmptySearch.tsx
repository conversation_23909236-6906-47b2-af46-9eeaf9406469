import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getSearchNewForyou, getTrendKeywords } from '@actions/search';
import { getDataRibbonsId } from '@actions/page';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';
import { getContentWatchmore } from '@actions/detail';
import Card from '../basic/Card/Card';
import CardList from '../basic/Card/CardList';
import Image from '../basic/Image/Image';

const EmptySearch = ({ filterId, ribbonID }: any) => {
  const [maybeYouWillLike, setDataYouLike] = useState<any>(null);
  const [mostSearched, setMostSearched] = useState<any>(null);
  const [isHasWatchingData, setIsHasWatchingData] = useState(false);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const dispatch = useDispatch();
  const ref = useRef([]);

  useEffect(() => {
    if (ribbonID) {
      dispatch(getDataRibbonsId({ id: ribbonID, page: 0, limit: 60, isGlobal })).then(
        (res: any) => {
          setMostSearched({
            title: TEXT.MOST_SEARCHED,
            data: res?.data
          });
        }
      );
    } else {
      getSearchEmptyRecommendData();
    }
  }, [ribbonID, isGlobal, dispatch]);

  const getSearchEmptyRecommendData = () => {
    dispatch(getSearchNewForyou({ page: 0, limit: 60, isGlobal })).then((res: any) => {
      if (res?.payload?.data) {
        setDataYouLike({
          title: TEXT.MAYBE_YOU_WILL_LIKE,
          data: res?.payload?.data
        });
      }
    });
    dispatch(getTrendKeywords({ page: 0, limit: 60, isGlobal })).then((res: any) => {
      if (res?.payload?.data) {
        setMostSearched({
          title: TEXT.MOST_SEARCHED,
          data: res?.payload?.data
        });
      }
    });
  };

  useEffect(() => {
    dispatch(
      getContentWatchmore({
        page: 0,
        limit: 30,
        ribbonName: TEXT.WATCH_MORE,
        isGlobal
      })
    ).then((res: any) => {
      if (res?.payload?.data?.items?.length) {
        setIsHasWatchingData(true);
      }
    });
  }, [dispatch, isGlobal]);

  const renderCardList = (dataList: any) =>
    dataList?.map((item: any, i: any) => (
      <Card
        key={item?.id || i}
        cardData={item}
        ref={ref}
        index={i + 1}
        randomID={`${item?.id}_${i}`}
      />
    ));

  const RenderSection = ({ data, title }: any) => {
    const newData = data?.data?.items || [];
    if (!newData?.length) return null;

    return (
      <div className="rocopa">
        <div className="rocopa__header">
          <h2 className="rocopa__title">{title}</h2>
        </div>
        <div className="rocopa__body">
          <CardList renderContent={() => renderCardList(newData)} heightCheckScroll={320} />
        </div>
      </div>
    );
  };

  return (
    <section className="section section--for-dark section--search overflow canal-v">
      <div className="section__body">
        <div className="empty empty--search">
          <div className="content text-center">
            <Image
              notWebp
              className="inline-block"
              src={ConfigImage.emptyCat}
              alt="Empty content"
            />
            <p className="text">
              Không có kết quả
              {filterId ? '' : ' tìm kiếm'}
            </p>
          </div>
        </div>
        {isHasWatchingData ? (
          <RenderSection data={maybeYouWillLike} title={TEXT.MAYBE_YOU_WILL_LIKE} />
        ) : (
          <RenderSection data={mostSearched} title={TEXT.MOST_SEARCHED} />
        )}
      </div>
    </section>
  );
};

export default React.memo(EmptySearch);
