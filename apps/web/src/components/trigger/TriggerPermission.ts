import { CONTENT_TYPE, PAGE, PERMISSION, POPUP } from '@constants/constants';
import {
  addParamToUrlVieON,
  encodeParamDestination,
  onOpenPayment,
  parseSegmentPopupNameFromContent
} from '@helpers/common';
import LocalStorage from '@config/LocalStorage';
import { getPopupTriggerDialog, openPopup } from '@actions/popup';
import { parsePopupParams } from '@services/popupServices';
import { checkExpiredTVOD } from '@services/contentService';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import PaymentApi from '@apis/Payment';
import CardItem from '@models/CardItem';
import DetailApi from '@apis/detailApi';

export const handlePermission = async ({
  router,
  permission,
  url,
  dispatch,
  contentData,
  profile,
  currentProfile,
  trialDuration,
  isAddMyList,
  isWatchNow,
  isSubscribeComingSoon,
  drmServiceName,
  packageId,
  packageItem,
  forceLogin,
  triggerLoginDuration,
  isVip,
  isMWebToApp,
  isTriggerToApp,
  isMobile,
  isGlobal,
  isTriggerEngagement,
  isTriggerRegister,
  content,
  cardDataEpisode,
  detail,
  isEndScreenVod,
  isCardDetail
}: any) => {
  let continueProcess = true;
  if (
    (trialDuration > 0 && permission !== PERMISSION.CAN_WATCH) ||
    contentData?.type === CONTENT_TYPE.LIVESTREAM
  ) {
    return continueProcess;
  }
  const { popupName, authTrigger } = parsePopupParams({
    permission,
    contentType: contentData?.type,
    drmServiceName,
    isAddMyList,
    isWatchNow,
    isSubscribeComingSoon,
    profile,
    currentProfile,
    forceLogin,
    isVip: contentData?.isVip || isVip,
    groupPackageId: packageId,
    isTriggerToApp: contentData?.isTriggerToApp,
    trialDuration: contentData?.trialDuration,
    numberTrialEpisode: contentData?.numberTrialEpisode,
    isPremiumPVodHaveSVod:
      detail?.isPremiumPVodHaveSVod ||
      contentData?.isPremiumPVodHaveSVod ||
      cardDataEpisode?.isPremiumPVodHaveSVod,
    isPremiumPVodNotSVod:
      detail?.isPremiumPVodNotSVod ||
      contentData?.isPremiumPVodNotSVod ||
      cardDataEpisode?.isPremiumPVodNotSVod,
    isMWebToApp,
    isMobile,
    isTrigger: true,
    isGlobal,
    isTriggerEngagement,
    isTriggerRegister,
    contentDetail: detail,
    router
  });

  let func = () =>
    onOpenPayment(router, {
      returnUrl: contentData?.seo?.url || '',
      returnUrlFastTrack: window?.location?.href,
      id: contentData?.id,
      type: contentData?.type,
      pkg: packageId,
      isPvod: popupName === POPUP.NAME.PVOD_REGISTER,
      newTriggerPaymentBuyPackage: {
        isGlobal,
        profileId: profile?.id
      }
    });

  if (contentData?.isPremiumTVod) {
    const queryParams = addParamToUrlVieON(router?.query, {
      type: contentData?.type,
      id: contentData?.id || 0
    });
    func = () =>
      router.push(
        { pathname: PAGE.RENTAL_CONTENT, query: queryParams },
        { pathname: PAGE.RENTAL_CONTENT, query: queryParams }
      );
  }

  const action = { func };

  switch (permission) {
    case PERMISSION.NON_LOGIN: {
      continueProcess = false;
      if (forceLogin === 1) {
        continueProcess = true;

        ConfigLocalStorage.set(
          LocalStorage.RE_LOGIN_PARAMS,
          JSON.stringify({
            contentData: detail,
            pathname: router.pathname,
            url: router.asPath
          })
        );
      }

      if (typeof window !== 'undefined' && url) {
        if (
          contentData?.type === CONTENT_TYPE.SEASON ||
          contentData?.type === CONTENT_TYPE.EPISODE ||
          contentData?.type === CONTENT_TYPE.MOVIE
        ) {
          if (!contentData?.isPremiumTVod) {
            ConfigLocalStorage.set(
              LocalStorage.RE_LOGIN_PARAMS,
              JSON.stringify({
                contentData: cardDataEpisode || contentData,
                pathname: router.pathname,
                url: isCardDetail ? PAGE.HOME : router.asPath
              })
            );
          }
        }
      }

      const segmentPopupName = parseSegmentPopupNameFromContent(contentData);

      if (
        triggerLoginDuration > 0 ||
        contentData?.type === CONTENT_TYPE.LIVE_TV ||
        contentData?.type === CONTENT_TYPE.EPG ||
        contentData?.type === CONTENT_TYPE.LIVESTREAM
      ) {
        continueProcess = true;
      } else if (contentData?.isPremiumTVod) {
        action.func();
      } else if (popupName) {
        let newContentData = contentData;

        if (contentData?.type === CONTENT_TYPE.EPISODE) {
          const res = await DetailApi.getContentById({
            contentId: contentData?.groupId || contentData?.id
          });
          const data = new CardItem({ ...res?.data, ribbonType: res?.data?.type, isGlobal });
          newContentData = { ...contentData, title: data?.title };
        }

        if (
          !isGlobal &&
          ((isVip && !contentData?.isPremiumPVod && !contentData?.hasPVOD) || forceLogin === 1) &&
          (detail?.isVip || contentData?.isPremium)
        ) {
          let type = 'svod';
          const contentId = cardDataEpisode?.id || detail?.id || contentData?.id;
          const contentType = detail?.type || contentData?.type;
          dispatch(getPopupTriggerDialog({ type, contentId, contentType }));
          dispatch(
            openPopup({
              name: POPUP.NAME.SVOD_TRIGGER,
              segmentPopupName,
              action,
              packageId,
              isRevisePayment: true,
              contentType: contentData?.type,
              isEndScreenVod,
              data: content || newContentData,
              detailData: detail,
              contentData
            })
          );
          return (continueProcess = false);
        } else {
          dispatch(
            openPopup({
              name: popupName,
              segmentPopupName,
              action,
              packageId,
              isRevisePayment: true,
              contentType: contentData?.type,
              isEndScreenVod,
              data: content || newContentData,
              detailData: detail
            })
          );
        }
      } else if (authTrigger) {
        const remakeDestination = encodeParamDestination(router?.asPath);
        router.push(
          `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${authTrigger}`
        );
      }
      break;
    }
    case PERMISSION.CAN_WATCH: {
      if ((contentData?.isTriggerToApp || isTriggerToApp) && isMWebToApp) {
        dispatch(openPopup({ name: popupName }));
      }
      break;
    }
    case PERMISSION.PAYMENT: {
      continueProcess = false;

      if (
        detail?.isPremiumPVodHaveSVod ||
        contentData?.isPremiumPVodHaveSVod ||
        cardDataEpisode?.isPremiumPVodHaveSVod
      ) {
        const pVodInfo = await PaymentApi.getPVodInfo({
          contentId: contentData?.id,
          contentType: contentData?.type
        });

        dispatch(
          openPopup({
            name: popupName,
            action,
            data: {
              ...contentData,
              id: detail?.id || cardDataEpisode?.id || contentData?.id,
              title: detail?.title || cardDataEpisode?.title || contentData?.title,
              contentMsg: detail?.contentMsg || content?.contentMsg || contentData?.contentMsg,
              expirationMsg:
                detail?.expirationMsg || content?.expirationMsg || contentData?.expirationMsg,
              titleSeries: detail?.title || content?.title || contentData?.title,
              images: content?.images || contentData?.images
            },
            pVodInfo,
            isEndScreenVod,
            detailData: detail
          })
        );
      } else if (contentData?.isPremiumTVod) {
        const { isExpired, newTVodInfo } = await checkExpiredTVOD({ contentData });

        if (isExpired) {
          dispatch(
            openPopup({
              name: POPUP.NAME.TVOD_EXPIRED,
              contentData,
              packageItem,
              newTVodInfo,
              packageId,
              isRevisePayment: true
            })
          );
        } else action.func();
      } else {
        if (
          !isGlobal &&
          ((isVip && !contentData?.isPremiumPVod && !contentData?.hasPVOD) || forceLogin === 1)
        ) {
          let type = 'svod';
          const contentId = cardDataEpisode?.id || detail?.id || contentData?.id;
          const contentType = detail?.type || contentData?.type;
          dispatch(getPopupTriggerDialog({ type, contentId, contentType }));
          dispatch(
            openPopup({
              name: POPUP.NAME.SVOD_TRIGGER,
              // segmentPopupName,
              action,
              packageId,
              isRevisePayment: true,
              contentType: contentData?.type,
              isEndScreenVod,
              data: content || contentData,
              detailData: detail
            })
          );
          return (continueProcess = false);
        } else {
          dispatch(
            openPopup({
              name: popupName,
              packageName: packageItem?.name,
              id: contentData?.id,
              action,
              trigger: true,
              data: contentData,
              packageId,
              isRevisePayment: true,
              isEndScreenVod,
              detailData: detail
            })
          );
        }
      }
      break;
    }
    case PERMISSION.CONTENT_RESCTRICTED: {
      continueProcess = false;
      dispatch(openPopup({ name: POPUP.NAME.CONTENT_RESCTRICTED }));
      break;
    }
    default:
      break;
  }

  return continueProcess;
};
