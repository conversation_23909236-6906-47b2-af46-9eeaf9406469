import { checkPermissionContent } from '@services/contentService';
import { CONTENT_TYPE, PAGE, POPUP, TVOD } from '@constants/constants';
import { setToast } from '@actions/app';
import {
  addParamToUrlVieON,
  checkNextEpisodeToWatch,
  getSLugTVodFromRouter,
  parseTimeStartNotify
} from '@helpers/common';
import LocalStorage from '@config/LocalStorage';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import { handlePermission } from './TriggerPermission';
import { openPopup } from '@/actions/popup';

export const watchNowBehavior = async ({
  profile,
  currentProfile,
  contentData,
  router,
  dispatch,
  expand,
  noSaveParam,
  store,
  isVodDetail,
  cardDataEpisode,
  content,
  isMobile,
  isEndScreenVod,
  isCardDetail,
  vidItem,
  isGlobal
}: any) => {
  const { seo, href, type, episode } = cardDataEpisode || contentData;
  const { isEpisodeTrialInApp, numberTrialEpisode, isTriggerToApp } = content || {};
  const isMWebToApp = checkNextEpisodeToWatch({
    isEpisodeTrialInApp,
    numberTrialEpisode,
    episode,
    type,
    isMobile
  });

  const {
    permission,
    drmServiceName,
    trialDuration,
    packageId,
    packageItem,
    forceLogin,
    triggerLoginDuration,
    isVip,
    detail
  }: any = await checkPermissionContent({
    profile,
    currentProfile,
    router,
    contentData,
    cardDataEpisode,
    store,
    isVodDetail,
    dispatch,
    isGlobal
  });

  /**
   * Check trigger for SVOD TVOD content
   * TVOD.USER_TYPE.NONE or TVOD.USER_TYPE.EXPIRED
   */
  const checkBenefit =
    contentData?.tvod?.benefitType !== TVOD.USER_TYPE.NONE &&
    contentData?.tvod?.benefitType !== TVOD.USER_TYPE.EXPIRED &&
    vidItem?.tvod?.benefitType !== TVOD.USER_TYPE.NONE &&
    vidItem?.tvod?.benefitType !== TVOD.USER_TYPE.EXPIRED;

  if (contentData?.isSvodTvod && !trialDuration && !checkBenefit) {
    const storeData = store?.getState();
    if (currentProfile?.isKid || storeData?.MultiProfile?.currentProfile?.isKid) {
      dispatch(openPopup({ name: POPUP.NAME.KID_LIMITED_VIP_DIALOG }));
    } else {
      dispatch(
        openPopup({
          name: POPUP.NAME.TVOD_VIP_UPSELL,
          params: {
            profile: profile,
            content: contentData,
            permissionContent: detail
          }
        })
      );
    }

    return false;
  }
  const handleShowNotify = () => {
    if (contentData?.type === CONTENT_TYPE.EPG) {
      if (
        contentData?.isComingSoon &&
        contentData?.seo?.url &&
        (router?.asPath || '').includes(contentData?.seo?.url)
      ) {
        const message = parseTimeStartNotify(contentData?.startTime || 0);
        dispatch(setToast({ message }));
      }
    }
  };
  const continueProcess = await handlePermission({
    router,
    trialDuration,
    permission,
    packageId,
    packageItem,
    url: seo?.url,
    dispatch,
    contentData,
    profile,
    currentProfile,
    drmServiceName,
    forceLogin,
    triggerLoginDuration,
    isVip,
    isMWebToApp,
    isTriggerToApp,
    isMobile,
    isGlobal,
    content,
    cardDataEpisode,
    isTriggerEngagement: detail?.isTriggerEngagement,
    isTriggerRegister: detail?.isTriggerRegister,
    detail,
    isEndScreenVod,
    isCardDetail
  });

  handleShowNotify();
  if (continueProcess && !isVodDetail) {
    const params: any = {
      pathname: router?.pathname,
      asPath: router?.asPath,
      queryParams: router.query,
      expand: !!expand
    };
    if (!noSaveParam && !isEndScreenVod) {
      ConfigLocalStorage.set(LocalStorage.BACK_FROM_PLAYER, JSON.stringify(params));
    }
    const { type, seo } = contentData || {};
    const detailParams = { ...contentData.trackingData };
    const queryParams = addParamToUrlVieON(router?.query, detailParams, true);
    if (type === CONTENT_TYPE.LIVESTREAM && (seo?.url || '').includes('.html')) {
      const query = getSLugTVodFromRouter(seo?.url);
      router.push({ pathname: PAGE.LIVE_TVOD_SLUG, query }, { pathname: seo?.url });
    } else {
      router.push(
        { pathname: href, query: queryParams },
        { pathname: seo?.url, query: queryParams }
      );
    }
  }
  return continueProcess;
};
