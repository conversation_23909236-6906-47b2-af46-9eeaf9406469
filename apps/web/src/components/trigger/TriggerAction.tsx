import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector, useStore } from 'react-redux';
import get from 'lodash/get';
import {
  CONTENT_TYPE,
  CONTENT_TYPE_NOTIFY,
  CURRENCY,
  ID,
  PAGE,
  PERMISSION,
  POPUP,
  TRIGGER_ICON,
  TRIGGER_KEY,
  TVOD
} from '@constants/constants';
import { TEXT } from '@constants/text';
import User<PERSON><PERSON> from '@apis/userApi';
import {
  addParamToUrlVieON,
  checkIsFullscreen,
  createTimeout,
  encodeParamDestination,
  numberWithCommas,
  onOpenPayment,
  onShareSocial,
  openAppMobile,
  parseQueryString,
  removeURLQueryParams
} from '@helpers/common';
import { setToast } from '@actions/app';
import { setFavoriteChannel } from '@actions/liveTV';
import { setSubscribeComingSoon, getTipData, setAddMyList } from '@actions/page';
import { openPopup, previewCard } from '@actions/popup';
import { getSearchHistory, postSearchHistory } from '@actions/search';
import { getUserSubcribeNotifyComingSoon, getUserUnSubcribeNotifyComingSoon } from '@actions/user';
import LocalStorage from '@config/LocalStorage';
import { NAME, PROPERTY, VALUE } from '@config/ConfigSegment';
import { segmentEvent } from '@tracking/TrackingSegment';
import { moEngageEvent } from '@tracking/TrackingMoEngage';
import { MOE_NAME, MOE_PROPERTY } from '@config/ConfigMoEnage';
import TrackingApp from '@tracking/functions/TrackingApp';
import { parsePopupParams } from '@services/popupServices';
import PaymentApi from '@apis/Payment';
import {
  viewVideoDetailButtonSelected,
  viewLiveEventDetailButtonSelected
} from '@tracking/functions/TrackingCardDetail';
import Button from '@components/basic/Buttons/Button';
import { useVieRouter } from '@customHook';
import TrackingMWebToApp from '@tracking/functions/TrackingMWebToApp';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import TrackingPayment from '@tracking/functions/payment';
import isEmpty from 'lodash/isEmpty';
import cn from 'classnames';
import { watchNowBehavior } from './triggerFunction';
import NewIcon from '../basic/Icon/NewIcon';
import {
  trackingAddContentAtComingSoonTab,
  trackingAddContentAtOnAirTab,
  trackingRemindMeAtComingSoonTab,
  trackingViewContentAtOnAirTab,
  trackingViewContentDetailAtComingSoonTab,
  trackingViewContentDetailAtOnAirTab
} from '@tracking/functions/TrackingCommingSoon';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import styles from '@components/basic/Buttons/Button.module.scss';
import ButtonIcon from '../basic/Buttons/ButtonIcon';
import { checkPermissionContent } from '@services/contentService';
import classNames from 'classnames';
import { getContentTypeText } from '@/helpers/utils';
import { trackAddToListButton } from '@tracking/functions/TrackingSegmentedUser';

let clickTimer: any = null;
let searchDataTemp: any = null;
const trackingPayment = new TrackingPayment();
const TriggerAction = React.memo(
  ({
    contentData,
    triggerItem,
    index,
    large,
    onCallBackDetail,
    cardData,
    isAiring,
    playerId,
    titleDetailPage,
    searchContents,
    isVodDetail,
    cardDataEpisode,
    triggers,
    isCardHover,
    customText,
    isSchedule,
    idTabComingSoon,
    buttonStyleCustom,
    iconCustom,
    isEndScreenVod,
    onBackToPlayer,
    isCardDetail,
    customButtonClass,
    hideTitle,
    customSpanClass,
    onAction,
    vidItem
  }: any) => {
    const dispatch = useDispatch();
    const router = useVieRouter();
    const store = useStore();
    const masterBannerData = useSelector((state: any) => {
      const dataMenu = state?.Menu?.activeSubMenu || state?.Menu?.activeMenu || {};
      return state?.Page?.pageBanner?.[dataMenu?.seo?.url];
    });
    const { appDownload } = useSelector((state: any) => state?.App?.webConfig) || {};
    const triggerData = cardDataEpisode || cardData || contentData || {};
    const { isTriggerToApp, numberTrialEpisode } = cardDataEpisode || cardData || contentData || {};
    const { seo, href, title, isLiveStream, isLiveTv, tvod, isFavorite } = triggerData || {};
    const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);

    const isViewCollection = contentData?.isViewCollection;
    const profile = useSelector((state: any) => state?.Profile?.profile);
    const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile || {});
    const tipData = useSelector((state: any) => state?.Page?.tipData);
    const dataEventDetail = useSelector((state: any) => state?.Page?.eventsData);
    const tipDataItem = cardDataEpisode?.id
      ? tipData?.[cardDataEpisode?.id]
      : tipData?.[triggerData?.id] || tipData?.[triggerData?.groupId];
    let isWatchLater = tipDataItem?.isWatchLater || cardData?.isWatchLater;
    const [favorite, setFavorite] = useState(isFavorite);
    const previewCardSelector = useSelector((state: any) => state?.Popup?.previewCard);
    const { expand } = previewCardSelector;
    isWatchLater = triggerData?.isTV ? favorite : isWatchLater;
    const isMobile = useSelector((state: any) => state?.App?.isMobile);
    const { deviceModel } = useSelector((state: any) => state?.App);
    const userType = useSelector((state: any) => state?.User?.USER_TYPE);
    let nameContentSelectButton = '';

    const isTSvod = contentData?.isSvodTvod ?? triggerData?.isSvodTvod ?? false;
    useEffect(() => {
      return () => {
        clearTimeout(clickTimer);
      };
    }, []);
    const onClick = async (e: any) => {
      e.preventDefault();
      if (onAction) {
        onAction();
      }
      if (!triggerData?.id) return;
      if (!tipDataItem) {
        dispatch(getTipData({ id: triggerData?.id }));
      }

      switch (triggerItem?.key) {
        case TRIGGER_KEY.BUY_GLOBAL: {
          if (
            triggerData?.type === CONTENT_TYPE.SEASON ||
            triggerData?.type === CONTENT_TYPE.EPISODE ||
            triggerData?.type === CONTENT_TYPE.MOVIE
          ) {
            if (!triggerData?.isPremiumTVod) {
              ConfigLocalStorage.set(
                LocalStorage.RE_LOGIN_PARAMS,
                JSON.stringify({
                  contentData: { ...triggerData },
                  pathname: router.pathname,
                  url: isCardDetail ? PAGE.HOME : router.asPath
                })
              );
            }
          }
          const returnUrl =
            triggerData.type === CONTENT_TYPE.LIVESTREAM
              ? triggerData.seo.url
              : window?.location?.href;

          const { detail: detailData } = await checkPermissionContent({
            profile,
            currentProfile,
            router,
            contentData,
            cardDataEpisode,
            store,
            isVodDetail,
            dispatch,
            isGlobal
          });
          dispatch(
            openPopup({
              name: profile?.id
                ? POPUP.NAME.USER_VOD_TRIAL_GLOBAL
                : POPUP.NAME.NON_LOGIN_TRIAL_GLOBAL,
              action: {
                func: () => {
                  onOpenPayment(router, {
                    returnUrl,
                    curPage: VALUE.VOD_INTRO_PAGE
                  });
                }
              },
              data: contentData,
              detailData: detailData,
              isRevisePayment: true
            })
          );
          break;
        }
        case TRIGGER_KEY.BUY_FULL_SEASON: {
          trackingPayment.globalPaymentButtonSelected({ currentPage: VALUE.VOD_INTRO_PAGE });
          onOpenPayment(router, {
            returnUrl: window?.location?.href,
            curPage: VALUE.VOD_INTRO_PAGE
          });
          break;
        }
        case TRIGGER_KEY.WATCH_TRIAL:
        case TRIGGER_KEY.WATCH_TRAILER:
        case TRIGGER_KEY.WATCH_NOW: {
          const isMasterBanner = triggerData?.isMasterBanner;
          const isEPG = triggerData?.type === CONTENT_TYPE.EPG;

          if (
            tipDataItem?.progress &&
            !isMasterBanner &&
            !isLiveTv &&
            !isLiveStream &&
            !isEPG &&
            !isAiring
          ) {
            nameContentSelectButton = TEXT.CONTINUE_WATCH;
          } else if (idTabComingSoon === ID.BROADCASTING) {
            trackingViewContentAtOnAirTab();
          } else nameContentSelectButton = TEXT.WATCH_NOW;
          pauseVideo();
          if (clickTimer) clearTimeout(clickTimer);
          clickTimer = createTimeout(() => {
            watchNow();
          }, 500);
          break;
        }
        case TRIGGER_KEY.REMIND_ME: {
          const trackingData = {
            contentId: triggerData?.id || '',
            contentName: triggerData?.title || '',
            contentType: getContentTypeText(triggerData?.type),
            isBlockVip:
              triggerData?.type === CONTENT_TYPE.EPISODE
                ? contentData?.isVip || contentData?.isPremium
                : triggerData?.isVip || triggerData?.isPremium || false,
            userType: userType?.userType || 'guest',
            status: tipDataItem?.isSubscribe ? 'cancel_remind' : 'reminded'
          };
          trackingPayment.remindButtonSelected(trackingData);
          nameContentSelectButton = TEXT.REMIND_ME;
          subscribeComingSoon();
          if (idTabComingSoon === ID.UP_BROADCASTING) {
            trackingRemindMeAtComingSoonTab();
          }
          break;
        }
        case TRIGGER_KEY.MY_LIST: {
          if (isTriggerToApp && !numberTrialEpisode) {
            TrackingMWebToApp.contentOnlyInAppAddList({
              flowName: VALUE.TRIGGER_BY_CONTENT_MOVIE_TVSERIES_ONLY_IN_APP
            });
          }
          if (idTabComingSoon === ID.BROADCASTING) {
            trackingAddContentAtOnAirTab();
          }
          if (idTabComingSoon === ID.UP_BROADCASTING) {
            trackingAddContentAtComingSoonTab();
          }
          nameContentSelectButton = TEXT.MY_LIST;
          TrackingApp.contentSelected({
            data: {
              ...triggerData,
              seasonGenre: triggerData?.genreText,
              seasonThumb: triggerData?.images?.thumbnail
            },
            masterBannerData,
            clickType: VALUE.CLICK_ADD_MY_LIST,
            category: triggerData?.isLiveTv ? triggerData?.categoryTracking : undefined
          });
          if (clickTimer) clearTimeout(clickTimer);
          clickTimer = createTimeout(() => {
            addMyList();
          }, 500);
          break;
        }
        case TRIGGER_KEY.DETAIL: {
          nameContentSelectButton = TEXT.DETAIL;
          TrackingApp.contentSelected({
            data: {
              ...triggerData,
              seasonGenre: triggerData?.genreText,
              seasonThumb: triggerData?.images?.thumbnail
            },
            masterBannerData,
            clickType: VALUE.CLICK_DETAIL
          });
          if (checkIsFullscreen()) {
            document.exitFullscreen();
          }
          if (typeof triggerData?.onContentSelected === 'function') {
            triggerData.onContentSelected({ cardData: triggerData });
          }
          if (typeof onCallBackDetail === 'function') {
            onCallBackDetail();
          }
          if (typeof onBackToPlayer === 'function') {
            return onBackToPlayer(contentData);
          }
          if (triggerData?.searchData) {
            searchDataTemp = triggerData?.searchData;
          }
          if (idTabComingSoon === ID.BROADCASTING) {
            trackingViewContentDetailAtOnAirTab();
          }
          if (idTabComingSoon === ID.UP_BROADCASTING) {
            trackingViewContentDetailAtComingSoonTab();
          }
          if ((!!isViewCollection && triggerData) || !!triggerItem?.isGotoDetailPage) {
            router.push(href, seo?.url);
          } else {
            const id = triggerData?.productId || triggerData?.id;
            const pathname = router?.pathname;
            let routerPath = router?.asPath;
            if ((routerPath || '').indexOf('#') > -1) {
              routerPath = removeURLQueryParams(routerPath, '#');
            }
            const isRecommendPage =
              pathname === PAGE.COLLECTION_RIB && (routerPath || '').indexOf('?id') > -1;
            const idRecommend = parseQueryString(window.location.search)?.id;
            const queryParams = addParamToUrlVieON(
              router?.query,
              isRecommendPage && idRecommend ? { id: idRecommend, vid: id } : { vid: id }
            );
            let url = (routerPath || '').split('?');
            url = url?.[0] || '/';
            router.push(
              { pathname, query: queryParams },
              { pathname: url, query: queryParams },
              { scroll: false }
            );
          }
          break;
        }
        case TRIGGER_KEY.TVOD_NOW:
        case TRIGGER_KEY.PRE_ORDER:
        case TRIGGER_KEY.TVOD_FULL_EPS: {
          if (triggerData?.isPremiumTVod) {
            nameContentSelectButton = TEXT.TVOD_NOW;
            if (triggerData?.type === CONTENT_TYPE.SEASON) {
              nameContentSelectButton = TEXT.TVOD_FULL_EPS;
            } else if (triggerData.type === CONTENT_TYPE.LIVESTREAM) {
              nameContentSelectButton = TEXT.PRE_ORDER;
            }
          }
          TrackingApp.contentSelected({
            data: {
              ...triggerData,
              seasonGenre: triggerData?.genreText,
              seasonThumb: triggerData?.images?.thumbnail
            },
            masterBannerData,
            clickType: VALUE.CLICK_RENTED
          });
          await handleTVod();
          break;
        }
        case TRIGGER_KEY.WATCH_NOW_APP_VIEON: {
          if (isTriggerToApp && !numberTrialEpisode) {
            TrackingMWebToApp.contentOnlyInAppTouch({
              flowName: VALUE.TRIGGER_BY_CONTENT_MOVIE_TVSERIES_ONLY_IN_APP
            });
          }

          if (clickTimer) clearTimeout(clickTimer);
          clickTimer = createTimeout(() => {
            openAppMobile(appDownload);
          }, 200);
          break;
        }
        case TRIGGER_KEY.SHARE: {
          if (currentProfile?.isKid) return;
          if (seo?.shareUrl) {
            onShareSocial({
              link: seo?.shareUrl,
              name: 'facebook'
            });
          }
          break;
        }
        default:
          break;
      }
      if (
        !triggerData?.isMasterBanner &&
        !expand &&
        triggerData.isPremiumTVod &&
        triggerData?.type === CONTENT_TYPE.LIVESTREAM
      ) {
        let flowName = '';
        const currentTime = new Date().getTime() / 1000;
        if (tvod?.preOrder?.isPreOrdering) {
          flowName = TVOD.SALE_PRE_ORDER;
        } else if (currentTime <= triggerData?.startTime) flowName = TVOD.PRE_ORDER;
        else {
          nameContentSelectButton = TEXT.TVOD_NOW;
          flowName = TVOD.RENT;
        }
        viewLiveEventDetailButtonSelected({
          data: triggerData,
          userType,
          trigger: nameContentSelectButton,
          flowName
        });
      }
    };
    const handleTVod = async () => {
      const isSimulcast = get(triggerData, 'tvod.isSimulcast', false);
      const isLiveEvent = get(triggerData, 'tvod.isLiveEvent', false);
      const tvodInfo = await PaymentApi.getTVodInfo({
        contentId: triggerData?.id,
        contentType: triggerData?.type,
        isSimulcast,
        isLiveEvent
      });
      if (tvodInfo?.bizInfo) {
        if (isTSvod) return;
        const queryParams = addParamToUrlVieON(router?.query, {
          type: triggerData?.type,
          id: triggerData?.id || 0,
          isSimulcast,
          isLiveEvent,
          fromPrice: get(tvod, 'preOrder.isPreOrdering', false)
            ? get(tvod, 'preOrder.price', 0)
            : get(tvod, 'price', 0)
        });
        if (currentProfile?.isKid) {
          return dispatch(openPopup({ name: POPUP.NAME.KID_ACCESS_TVOD }));
        }
        if (!profile?.id && !isGlobal) {
          localStorage.setItem('currentAuthFlow', 'registration_for_payment_tvod');
          const destinationPath = `${PAGE.RENTAL_CONTENT}/?${new URLSearchParams(
            queryParams
          ).toString()}`;
          const queryString = new URLSearchParams({
            trigger: TYPE_TRIGGER_AUTH.PAYMENT_BUY_PACKAGE_PRE_ORDER
          }).toString();

          const finalQueryString = `${queryString}&destination=${encodeURIComponent(
            destinationPath
          )}`;
          router.push(`${PAGE.AUTH}?${finalQueryString}`);
          return;
        }
        router.push(
          { pathname: PAGE.RENTAL_CONTENT, query: queryParams },
          { pathname: PAGE.RENTAL_CONTENT, query: queryParams }
        );
      }
      return;
    };

    const pauseVideo = () => {
      const player: any = document.getElementById(playerId);
      if (player) player.pause();
    };

    const watchNow = async () => {
      const dataSearch = triggerData?.searchData || searchDataTemp;
      TrackingApp.contentSelected({
        data: {
          ...triggerData,
          seasonGenre: triggerData?.genreText,
          seasonThumb: triggerData?.images?.thumbnail
        },
        masterBannerData,
        clickType: VALUE.CLICK_WATCH_NOW,
        playNow: !expand,
        isLiveTv: triggerData?.isLiveTv,
        category: triggerData?.isLiveTv ? triggerData?.categoryTracking : undefined
      });
      if (typeof triggerData?.onContentSelected === 'function') {
        triggerData.onContentSelected({ cardData: triggerData });
      }
      if (router?.pathname === PAGE.SEARCH) {
        const keyword = router?.query?.q;
        const searchItems = searchContents?.[keyword]?.[-1]?.[0]?.items || [];
        const itemPosition = (searchItems || []).findIndex((it: any) => it?.id === triggerData?.id);
        segmentEvent(NAME.SELECT_SEARCH_RESULT, {
          [PROPERTY.KEYWORD]: keyword,
          [PROPERTY.CONTENT_TITLE]: triggerData?.title,
          [PROPERTY.CONTENT_TYPE]: triggerData?.type,
          [PROPERTY.CONTENT_POSITION]: itemPosition || triggerData?.index || 0,
          [PROPERTY.CURRENT_PAGE]: window?.location?.href
        });
      }
      if (dataSearch && profile) {
        const id = dataSearch?.id || '';
        const keyword = dataSearch?.keyword || '';
        const type = dataSearch?.type || '';
        const position = dataSearch?.position || '';
        const request_id = triggerData?.trackingData?.recommendation_id || '';
        postSearchHistory({ id, keyword, type, position, request_id });
        dispatch(getSearchHistory());
      }
      const continueProcess = await watchNowBehavior({
        profile,
        currentProfile,
        contentData,
        router,
        dispatch,
        expand,
        store,
        isVodDetail,
        cardDataEpisode,
        isMobile,
        isEndScreenVod,
        isCardDetail,
        vidItem,
        isGlobal
      });

      if (continueProcess) {
        const isActionWatchNow =
          triggerItem?.key === TRIGGER_KEY.WATCH_TRIAL ||
          triggerItem?.key === TRIGGER_KEY.WATCH_TRAILER ||
          triggerItem?.key === TRIGGER_KEY.WATCH_NOW;
        if (!triggerData?.isMasterBanner && (expand || (!expand && isActionWatchNow))) {
          viewVideoDetailButtonSelected({
            data: triggerData,
            userType,
            trigger: nameContentSelectButton,
            isBlockVip: contentData?.isPremium,
            deviceModel: deviceModel
          });
        }
        dispatch(
          previewCard({
            expand: false,
            data: null,
            offset: null,
            dataEpisode: null,
            tVodInfo: null
          })
        );
      }
    };

    const subscribeComingSoon = () => {
      if (!profile?.id) {
        localStorage.setItem('currentAuthFlow', 'registration_feature');
        localStorage.setItem('currentAuthFeature', 'remind_me');
        handleGuestAction({
          isSubscribeComingSoon: true
        });
      } else {
        let message = TEXT.MSG_ERROR;
        if (triggerData?.type === CONTENT_TYPE.LIVESTREAM) {
          if (triggerData.isSubcribed) {
            message = '';
            dispatch(getUserUnSubcribeNotifyComingSoon(triggerData?.id));
          } else {
            message = TEXT.LIVE_STREAM_NOTIFY;
            dispatch(
              getUserSubcribeNotifyComingSoon(
                triggerData?.id,
                triggerData.startTime,
                CONTENT_TYPE_NOTIFY.LIVE_EVENT
              )
            );
          }
          if (dataEventDetail?.id === triggerData?.id) {
            dataEventDetail.isSubcribed = !triggerData.isSubcribed;
          }
          if (message) dispatch(setToast({ message }));
          triggerData.isSubcribed = !triggerData.isSubcribed;
        } else {
          UserApi.subscribeComingSoon({
            contentId: triggerData?.id,
            startTime: triggerData?.startTime,
            contentType: CONTENT_TYPE_NOTIFY.LIVE_TV
          }).then((res) => {
            if (res?.data?.success) {
              message = tipDataItem?.isSubscribe ? '' : TEXT.MSG_REMIND_ME_SUCCESS;
              dispatch(
                setSubscribeComingSoon({
                  contentId: triggerData?.id,
                  isSubscribe: !tipDataItem?.isSubscribe
                })
              );
            }
            if (message) dispatch(setToast({ message }));
            // subscribe moengage
            if (
              triggerData?.type !== CONTENT_TYPE.LIVE_TV &&
              triggerData?.type !== CONTENT_TYPE.EPG
            ) {
              moEngageEvent(MOE_NAME.COMING_SOON_CONTENT, {
                [MOE_PROPERTY.CONTENT_ID]: triggerData?.id,
                [MOE_PROPERTY.CONTENT_TYPE]: triggerData?.type,
                [MOE_PROPERTY.IS_SUB]: !tipDataItem?.isSubscribe
              });
            }
          });
        }
      }
    };

    const handleGuestAction = ({ isAddMyList, isWatchNow, isSubscribeComingSoon }: any) => {
      if (
        triggerData?.type === CONTENT_TYPE.SEASON ||
        triggerData?.type === CONTENT_TYPE.EPISODE ||
        triggerData?.type === CONTENT_TYPE.MOVIE
      ) {
        const paramsVod: any = ConfigLocalStorage.get(LocalStorage.BACK_FROM_PLAYER);
        let pathnameParams = router.pathname;
        let asPathParams = router.asPath;
        const { pathname, asPath } = JSON.parse(paramsVod || '{}');
        if (isEndScreenVod) {
          if (!isEmpty(paramsVod)) {
            pathnameParams = pathname;
            asPathParams = asPath;
          } else {
            pathnameParams = PAGE.HOME;
            asPathParams = PAGE.HOME;
          }
        }
        if (!triggerData?.isPremiumTVod) {
          ConfigLocalStorage.set(
            LocalStorage.RE_LOGIN_PARAMS,
            JSON.stringify({
              contentData: { ...triggerData, isAddMyList },
              pathname: pathnameParams,
              url: asPathParams
            })
          );
        }
      }
      let segmentPopupName = '';
      if (isAddMyList) {
        segmentPopupName = VALUE.POPUP_NAME.GUEST_VOD_MY_LIST;
        if (triggerData?.type === CONTENT_TYPE.LIVE_TV || triggerData?.type === CONTENT_TYPE.EPG) {
          segmentPopupName = VALUE.POPUP_NAME.GUEST_LIVETV_MY_LIST;
        }
      } else if (isWatchNow) {
        segmentPopupName = VALUE.POPUP_NAME.GUEST_VOD_LIVESTREAM_VIP;
      } else if (isSubscribeComingSoon) {
        segmentPopupName = VALUE.POPUP_NAME.GUEST_VOD_COMING_SOON;
        if (triggerData?.type === CONTENT_TYPE.LIVE_TV || triggerData?.type === CONTENT_TYPE.EPG) {
          segmentPopupName = VALUE.POPUP_NAME.GUEST_LIVETV_COMING_SOON;
        } else if (triggerData?.type === CONTENT_TYPE.LIVESTREAM) {
          segmentPopupName = VALUE.POPUP_NAME.GUEST_LIVESTREAM_COMING_SOON;
        }
      }
      const player: any = document.getElementById(playerId);
      const currentTime = player?.ended ? 0 : player?.currentTime || 0;
      ConfigLocalStorage.set(
        LocalStorage.BANNER_TRAILER_PROGRESS,
        JSON.stringify({ [triggerData?.id]: currentTime })
      );

      const { popupName, authTrigger } = parsePopupParams({
        permission: PERMISSION.NON_LOGIN,
        contentType: triggerData?.type,
        isAddMyList,
        isWatchNow,
        isSubscribeComingSoon,
        isMobile,
        router,
        contentDetail: contentData
      });
      if (popupName) {
        dispatch(
          openPopup({
            name: popupName,
            segmentPopupName,
            contentType: triggerData?.type,
            detailData: contentData
          })
        );
        return;
      }
      if (authTrigger) {
        const remakeDestination = encodeParamDestination(router?.asPath);
        router.push(
          `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${authTrigger}`
        );
      }
    };

    const addMyList = () => {
      if (!profile?.id) {
        localStorage.setItem('currentAuthFlow', 'registration_feature');
        localStorage.setItem('currentAuthFeature', 'add_to_list');
        handleGuestAction({
          isAddMyList: true
        });
      } else if (
        triggerData?.type === CONTENT_TYPE.LIVE_TV ||
        triggerData?.type === CONTENT_TYPE.EPG ||
        triggerData?.type === CONTENT_TYPE.LIVESTREAM
      ) {
        segmentEvent(NAME.ADD_LIVE_TV_TO_MY_LIST, {
          [PROPERTY.CURRENT_PAGE]: window.location.href,
          [PROPERTY.LIVETV_NAME]: triggerData?.title,
          [PROPERTY.LIVETV_ID]: triggerData?.id
        });
        onAddFavoriteLiveTV(triggerData);
      } else {
        dispatch(setAddMyList({ contentData: triggerData, isWatchLater }));
      }
      const dataTrackAddButton = {
        contentId: triggerData?.id || '',
        contentName: triggerData?.title || '',
        contentType: getContentTypeText(triggerData?.type),
        isBlockVip:
          triggerData?.type === CONTENT_TYPE.EPISODE
            ? contentData?.isVip
            : triggerData?.isVip || false,
        userType: userType?.userType,
        buttonStatus: isWatchLater ? 'off' : 'on'
      };

      trackAddToListButton(dataTrackAddButton);
    };

    const onAddFavoriteLiveTV = async (channel: any) => {
      dispatch(setFavoriteChannel(channel, false, isGlobal));
      setFavorite(!favorite);
    };

    const onClickCollection = (e: any) => {
      e.preventDefault();
      router.push(triggerData?.href || PAGE.COLLECTION, triggerData?.seo?.url || '');
    };

    // let className = 'button';
    let className = styles.button;

    let triggerLabel = TEXT?.[triggerItem?.key] || '';

    let iconName: any = TRIGGER_ICON?.[triggerItem?.key];

    if (index === 0) {
      className += ` ${styles.primarySolid}`;
    } else {
      className += ` ${styles.primaryOutlineGlass}`;
    }

    if (triggerData?.isMasterBanner || expand || large) {
      className += ` ${isMobile ? styles.medium : large ? styles.large : styles.largeVariant}`;
    } else if (isAiring) {
      className += ` ${isMobile ? styles.medium : styles.large}`;
    } else {
      className += ` ${styles.medium}`;
    }

    if (isSchedule) className = buttonStyleCustom;

    switch (triggerItem?.key) {
      case TRIGGER_KEY.DETAIL: {
        if (
          isGlobal &&
          isCardHover &&
          triggers.findIndex((t: any) => t.key === TRIGGER_KEY.BUY_GLOBAL) !== -1
        ) {
          triggerLabel = '';
        }
        break;
      }
      case TRIGGER_KEY.BUY_GLOBAL: {
        triggerLabel = TEXT.WATCH_VieON_GLOBAL;
        break;
      }
      case TRIGGER_KEY.BUY_FULL_SEASON: {
        triggerLabel =
          triggerData?.type === CONTENT_TYPE.MOVIE
            ? TEXT.WATCH_NOW_VieON_GLOBAL
            : TEXT.WATCH_FULL_VieON_GLOBAL;
        break;
      }
      case TRIGGER_KEY.MY_LIST: {
        if (isWatchLater) {
          iconName = TRIGGER_ICON.MY_LIST_ADDED;
        } else {
          iconName = TRIGGER_ICON.MY_LIST;
        }
        break;
      }
      case TRIGGER_KEY.WATCH_TRIAL:
      case TRIGGER_KEY.WATCH_NOW: {
        const isMasterBanner = triggerData?.isMasterBanner;
        const isEPG = triggerData?.type === CONTENT_TYPE.EPG;
        if (
          tipDataItem?.progress &&
          !isMasterBanner &&
          !isLiveTv &&
          !isLiveStream &&
          !isEPG &&
          !isAiring
        ) {
          triggerLabel = TEXT.CONTINUE_WATCH;
        } else if (
          (isGlobal && isAiring) ||
          (isGlobal && profile?.isPremium && triggerData?.isPremium)
        ) {
          triggerLabel = TEXT.WATCH_NOW;
        }
        break;
      }
      case TRIGGER_KEY.REMIND_ME: {
        if (!!tipDataItem?.isSubscribe || (triggerData?.isComingSoon && triggerData?.isSubcribed)) {
          triggerLabel = TEXT.REMINDED_ME;
          iconName = 'vie-tick';
          className += ' checked';
        }
        break;
      }
      case TRIGGER_KEY.TVOD_NOW:
      case TRIGGER_KEY.TVOD_FULL_EPS: {
        if (!expand && !isAiring && !triggerData?.isMasterBanner) {
          if (isLiveStream) {
            triggerLabel += ` ${numberWithCommas(tvod?.price || 0)} ${CURRENCY.VND}`;
          } else triggerLabel = TEXT.TVOD_NOW;
        } else {
          triggerLabel += ` ${numberWithCommas(tvod?.price)}${CURRENCY.D}`;
        }
        if (isTSvod) {
          triggerLabel = TEXT.WATCH_NOW;
        }
        break;
      }
      case TRIGGER_KEY.PRE_ORDER: {
        triggerLabel += ` ${numberWithCommas(
          tvod?.preOrder?.isPreOrdering ? tvod?.preOrder?.price : tvod?.price
        )} ${CURRENCY.VND}`;
        break;
      }
      case TRIGGER_KEY.WATCH_NOW_APP_VIEON:
        iconName = TRIGGER_ICON.DOWNLOAD_APP;
        break;
      default:
        break;
    }

    if (
      titleDetailPage &&
      triggerItem?.key === TRIGGER_KEY.DETAIL &&
      triggerData?.isDetailPage &&
      !isEndScreenVod
    ) {
      className += ' hide';
    }
    let spanClass = `${isMobile ? 'icon--small' : 'icon--medium'}`;

    if (!isAiring && !triggerData?.isMasterBanner && !expand) {
      spanClass = 'icon--tiny';
    }
    if (isSchedule) spanClass = iconCustom;

    if (isViewCollection && triggerData) {
      return (
        <a
          href={triggerData?.seo?.url || ''}
          className={className}
          title={TEXT.DETAIL}
          onClick={onClickCollection}
        >
          <NewIcon spClass={spanClass} iconName={iconName || ''} />
          <span className={`text ${customText}`}>{triggerLabel || ''}</span>
        </a>
      );
    }
    if (triggerItem?.key === TRIGGER_KEY.PRE_ORDER) {
      return (
        <Button
          className={className}
          alt={`${TEXT?.[triggerItem?.key]} ${title}`}
          title={triggerLabel}
          onClick={onClick}
          iconClass={spanClass}
          iconName={iconName || ''}
        />
      );
    }
    if (
      triggerItem?.key === TRIGGER_KEY.TVOD_NOW ||
      triggerItem?.key === TRIGGER_KEY.TVOD_FULL_EPS
    ) {
      if (triggerData?.isSvodTvod && triggerItem?.iconOnly) {
        return (
          <ButtonIcon
            iClass={classNames(`${customButtonClass} ${buttonStyleCustom}`)}
            onClick={onClick}
          />
        );
      }
      return (
        <button
          className={className}
          title={`${TEXT?.[triggerItem?.key]} ${title}`}
          onClick={onClick}
          type="button"
        >
          <NewIcon spClass={spanClass} iconName={iconName || ''} />
          <span
            className={`${customText}`}
            data-desc-after={`${TEXT?.[triggerItem?.key]} ${numberWithCommas(tvod?.price)} ${
              CURRENCY.VND
            }`}
            dangerouslySetInnerHTML={{
              __html: `${triggerLabel}&ensp;`
            }}
          />
        </button>
      );
    }

    return (
      <a
        href={seo?.url || ''}
        className={className}
        title={hideTitle ? '' : `${TEXT?.[triggerItem?.key]} ${title}`}
        onClick={onClick}
      >
        <NewIcon
          spClass={cn(spanClass, customSpanClass)}
          unUseBaseClass={isSchedule}
          iconName={iconName || ''}
          iCustomizeClass={customButtonClass}
        />
        {!hideTitle && triggerLabel && (
          <span
            className={`text ${customText}`}
            dangerouslySetInnerHTML={{
              __html: triggerLabel
            }}
          />
        )}
      </a>
    );
  }
);
export default TriggerAction;
