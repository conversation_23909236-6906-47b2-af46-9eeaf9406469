import React from 'react';
import classNames from 'classnames/bind';
import Style from './Style.module.scss';
import Icon from '../basic/Icon/Icon';

const cx = classNames.bind(Style);

const Button = ({
  disabled,
  title,
  icon,
  isPrimary,
  iconSrcRight,
  customClassName,
  ...rest
}: any) => {
  const classBtn = cx(
    {
      button: true,
      primary: isPrimary
    },
    customClassName
  );

  return (
    <button className={classBtn} disabled={disabled} {...rest}>
      {icon} {title}
      {iconSrcRight && <Icon imageSrc={iconSrcRight} />}
    </button>
  );
};

export default Button;
