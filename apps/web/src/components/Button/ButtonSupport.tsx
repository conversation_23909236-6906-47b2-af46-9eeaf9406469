import React from 'react';
import { useSelector } from 'react-redux';
import Button from '@/components/basic/Buttons/Button';
import { ICON_KEY } from '@/constants/constants';
import styles from './Style.module.scss';

const ButtonSupport = ({
  customizeClass,
  disabled,
  onclick,
  title,
  textClass,
  iconType,
  svgSize
}: any) => {
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const newTitle = title || 'Hỗ trợ';
  const newClassName = customizeClass || styles.buttonSupport;
  return (
    <Button
      customizeClass={newClassName}
      title={newTitle}
      disabled={disabled}
      onclick={!disabled && onclick}
      textClass={textClass || styles.buttonSupportLabel}
      iconType={iconType || ICON_KEY.HEADPHONES}
      svgSize={svgSize || isMobile ? 13 : 16}
    />
  );
};

export default ButtonSupport;
