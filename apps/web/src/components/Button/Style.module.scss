.button {
  @apply self-stretch text-[#dedede] text-base font-medium bg-[rgba(17,17,17,0.5)] leading-5 md:leading-[140%];
  @apply grid grid-flow-col md:flex items-center justify-center;
  @apply gap-[6px] md:gap-3;
  @apply rounded-sm border-[#ccc] border-solid border-[1px];
  @apply h-9 md:h-12;
  @apply py-2 px-[30px];
  & > svg {
    @apply w-5 md:w-auto h-5 md:h-auto;
  }
  &.primary {
    @apply bg-white text-[#333];
  }
  &:disabled {
    @apply border-none bg-[#454545] text-[#646464];
  }

  &Support {
    // Group Layout
    @apply flex flex-row  md:min-w-32 h-fit items-center justify-center px-3 md:px-5 py-2 space-x-1 md:space-x-2;
    // Group style
    @apply rounded-3xl border border-[#CCEBCE] bg-[#0BBC16]/10 text-[#2FB138] hover:border-[#2FB138] hover:bg-[#2FB138] hover:text-white transition-all duration-300;

    &Label {
      @apply font-medium text-[.75rem] md:text-[1rem];
    }
  }
}
