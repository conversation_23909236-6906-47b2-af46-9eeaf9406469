import React, { useEffect } from 'react';
import { handleGetSlugEpsFromPath } from '@services/detailServices';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import { getRecommendVod } from '@actions/detail';
import { VALUE } from '@config/ConfigSegment';
import TrackingApp from '@tracking/functions/TrackingApp';
import { watchNowBehavior } from '../trigger/triggerFunction';
import Ribbon from '../basic/Ribbon/Ribbon';
import CardVodIntro from '../basic/Card/CardVodIntro';

const Recommended = (props: any) => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const { content, dataViewItem, noSaveParam, isVodDetail } = props;
  const { slugFromPath } = handleGetSlugEpsFromPath({ path: content?.seo?.url });
  const slug = slugFromPath;
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile);
  const dataServer = useSelector((state: any) => state?.Detail?.ACTION_GET_RECOMMEND_VOD);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);

  useEffect(() => {
    handleChangeRecommendVod();
  }, [content?.id]);

  const handleChangeRecommendVod = () => {
    if (slug) {
      dispatch(getRecommendVod({ slug, isGlobal }));
    }
  };

  const onClickCard = async (item: any) => {
    TrackingApp.contentSelected({
      data: {
        ...item,
        ribbonId: VALUE.RECOMMEND_RIBBON.ID,
        ribbonName: VALUE.RECOMMEND_RIBBON.NAME,
        seasonThumb: item?.images?.thumbnail,
        seasonGenre: item?.genreText
      },
      clickType: VALUE.DIRECT
    });
    await watchNowBehavior({
      profile,
      currentProfile,
      contentData: item,
      router,
      dispatch,
      expand: true,
      noSaveParam,
      isMobile,
      isGlobal
    });
  };

  const renderCardItem = (item: any) => {
    const thumbClass = 'card__thumbnail-loader overflow ratio-16-9';
    return (
      <CardVodIntro
        item={item}
        onClickCard={onClickCard}
        thumbClass={thumbClass}
        isVodDetail={isVodDetail}
        titleCardGroup
        isRelated
      />
    );
  };
  const headerData = dataServer?.recommendData?.headerData || {};
  const dataRecommend = dataServer?.recommendData?.bodyData?.slickData;
  if ((dataRecommend || []).length === 0) return null;
  return (
    <Ribbon
      title={headerData.title}
      id="idRecommend"
      renderCardItem={renderCardItem}
      dataViewItem={dataViewItem || 4}
      data={dataRecommend}
      notLoop
    />
  );
};

export default Recommended;
