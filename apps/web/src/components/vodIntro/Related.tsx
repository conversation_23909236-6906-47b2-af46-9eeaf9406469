import React, { useEffect, useState } from 'react';
import DetailApi from '@apis/detailApi';
import { handleGetSlugEpsFromPath } from '@services/detailServices';
import { useVieRouter } from '@customHook';
import { useDispatch, useSelector } from 'react-redux';
import { VALUE } from '@config/ConfigSegment';
import TrackingApp from '@tracking/functions/TrackingApp';
import { watchNowBehavior } from '../trigger/triggerFunction';
import CardVodIntro from '../basic/Card/CardVodIntro';
import Ribbon from '../basic/Ribbon/Ribbon';

const Related = (props: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile);
  const { content, dataViewItem, noSaveParam, isVodDetail } = props;
  const { slugFromPath } = handleGetSlugEpsFromPath({ path: content?.seo?.url });
  const slug = slugFromPath;
  const dataServer = useSelector((state: any) => state?.Detail?.ACTION_GET_RELATED_VOD);
  const userType = useSelector((state: any) => state?.User?.USER_TYPE);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const [relatedData, setRelate] = useState<any>(null);

  useEffect(() => {
    if (slug) {
      handleChangeRelatedVod();
    }
  }, [slug, content]);
  const headerData = dataServer?.relatedData?.headerData || relatedData?.headerData || {};

  const handleChangeRelatedVod = async () => {
    const getRelatedData = await DetailApi.getRelatedVod({ slug, isGlobal });
    setRelate(getRelatedData?.relatedData || []);
  };

  const onClickCard = async (item: any) => {
    TrackingApp.contentSelected({
      data: {
        ...item,
        ribbonId: VALUE.RELATED_RIBBON.ID,
        ribbonName: VALUE.RELATED_RIBBON.NAME,
        seasonThumb: content?.images?.thumbnail,
        seasonGenre: content?.genreText
      },
      clickType: VALUE.DIRECT,
      userType: userType?.userType
    });
    await watchNowBehavior({
      profile,
      currentProfile,
      contentData: item,
      router,
      dispatch,
      expand: true,
      noSaveParam,
      isMobile
    });
  };

  const renderCardItem = (item: any) => {
    const thumbClass = 'card__thumbnail-loader overflow ratio-16-9';
    return (
      <CardVodIntro
        item={item}
        onClickCard={onClickCard}
        thumbClass={thumbClass}
        isVodDetail={isVodDetail}
        isRelated
      />
    );
  };

  const dataRelate =
    relatedData?.bodyData?.slickData || dataServer?.relatedData?.bodyData?.slickData;

  if ((dataRelate || []).length === 0) return null;

  return (
    <Ribbon
      title={headerData?.title}
      id="idRelate"
      renderCardItem={renderCardItem}
      dataViewItem={dataViewItem || 4}
      setting={{ slidesPerView: 4, slidesPerGroup: 4, width: 903 }}
      data={dataRelate}
      notLoop
      className="slider--intro"
    />
  );
};
export default Related;
