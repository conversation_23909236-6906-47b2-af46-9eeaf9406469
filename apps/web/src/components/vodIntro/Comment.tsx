import React, { forwardRef, memo, useEffect, useMemo, useState } from 'react';
import { useVieRouter } from '@customHook';
import DetailApi from '@apis/detailApi';
import { useDispatch, useSelector } from 'react-redux';
import { EL_ID, ERROR_CODE, PAGE } from '@constants/constants';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import { setToast } from '@actions/app';
import { GET_LIST_COMMENTS_SUCCESS, getListComments } from '@actions/detail';
import { TEXT } from '@constants/text';
import { createTimeout, encodeParamDestination } from '@helpers/common';
import { text_button_register_for_comment_selected } from '@tracking/functions/TrackingRegistrationTrigger';
import EmptyCommentBlocked from '@components/empty/EmptyCommentBlocked';
import Empty from '@components/Comment/Empty';
import CommentList from '@components/Comment/CommentList';
import CommentArea from '@components/Comment/CommentArea';
import classnames from 'classnames';
import { convertToDateTime } from '@helpers/utils';
import BannerRegistration from '../home/<USER>';
import Styles from '../empty/Empty.module.scss';

const Comment = (
  { content, setTotalComment, profile, windowScroll, canalClass, textAreaId, isDetail }: any,
  ref: any
) => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const contentId = useMemo(() => content.id, [content]);
  const storeDetail = useSelector((state: any) => state.Detail);
  const bannerConversionTrigger = useSelector((state: any) => state?.App?.registrationTrigger);
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const [blocked, setBlocked] = useState(false);
  const dataCommentOfContent = useMemo(
    () =>
      storeDetail &&
      storeDetail[GET_LIST_COMMENTS_SUCCESS] &&
      storeDetail[GET_LIST_COMMENTS_SUCCESS][contentId],
    [storeDetail, contentId]
  );
  let scrollBottomTimer: any = 0;
  const limit = 20;
  const [contentListState, setContentListState] = useState<any>(null);

  useEffect(() => {
    const cardDetailEl = windowScroll ? window : document.getElementById('CardDetail');
    if (contentId && cardDetailEl) {
      cardDetailEl.addEventListener('scroll', loadMoreTrackingFunc);
    }
    return () => {
      if (cardDetailEl) cardDetailEl.removeEventListener('scroll', loadMoreTrackingFunc);
    };
  }, [contentId, windowScroll, dataCommentOfContent]);

  useEffect(() => {
    if (contentId) {
      dispatch(getListComments({ contentId, page: 0, limit }));
    }
  }, [contentId]);

  useEffect(() => {
    if (dataCommentOfContent) {
      if (!dataCommentOfContent?.blocked) {
        const totalComment = dataCommentOfContent.metadata?.total;
        setTotalComment(totalComment);
      } else setBlocked(true);
    }
    if (contentListState) {
      const fakeItems = getFakeItems(contentListState) || [];

      const newItems = [...fakeItems, ...dataCommentOfContent?.items];
      setContentListState(newItems);
    }
  }, [dataCommentOfContent]);

  const loadMoreTrackingFunc = (e: any) =>
    windowScroll ? trackWindowScrolling() : checkLoadMoreComment(e);

  const handleGetMoreComments = () => {
    const metadata = dataCommentOfContent?.metadata || {};
    const total = metadata?.total;
    const page = metadata?.page + 1;
    if (page * limit >= total) {
      return;
    }
    dispatch(getListComments({ contentId, page, limit }));
  };

  const checkBottomPosition = (el: any) => {
    if (!el) return;
    return (
      el.getBoundingClientRect() && el.getBoundingClientRect().bottom <= window.innerHeight + 1
    );
  };

  const trackWindowScrolling = () => {
    if (scrollBottomTimer) clearTimeout(scrollBottomTimer);
    scrollBottomTimer = createTimeout(() => {
      const el = document.getElementById('main');
      const isBottomPage = checkBottomPosition(el);
      if (isBottomPage && dataCommentOfContent) {
        handleGetMoreComments();
      }
    }, 700);
  };

  const checkLoadMoreComment = (e: any) => {
    const element = e?.target;
    if (!element) return;
    if (scrollBottomTimer) clearTimeout(scrollBottomTimer);
    const { scrollTop, scrollHeight, clientHeight } = element || {};
    scrollBottomTimer = createTimeout(() => {
      if (scrollTop + clientHeight > scrollHeight - 50 && dataCommentOfContent) {
        handleGetMoreComments();
      }
    }, 700);
  };

  const parseHidePhone = (str: any) => (str || '').replace(/(\d{3})\d{3}/, '$1***');

  const onClickTriggerConversion = () => {
    text_button_register_for_comment_selected();
    const remakeDestination = encodeParamDestination(router?.asPath);
    router.push(
      `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${TYPE_TRIGGER_AUTH.COMMENT}`
    );
  };

  const sendComment = (message: any) => {
    if (message && contentId && /\S/.test(message)) {
      DetailApi.postComment({ contentId, message }).then((res) => {
        if (res?.data?.error === ERROR_CODE.CODE_1001 || res?.data?.error === ERROR_CODE.CODE_400) {
          setBlocked(true);
        } else if (!res?.success) {
          dispatch(setToast({ message: res?.data?.message || TEXT.MSG_ERROR }));
        } else {
          const now = new Date().getTime();
          const date = new Date(now);
          const formatDateNow = convertToDateTime(date, 'HH:mm DD/MM/YYYY');
          const fakeComment = {
            contentId,
            // createdAt: 'Vừa xong',
            createdAt: formatDateNow,
            id: `Fake-f49a0f28-afd3-5227-9d6e-f26003353592-${Math.random()}`,
            message,
            pin: 0,
            userAvatar: profile?.avatar,
            userName: profile?.givenName || parseHidePhone(profile?.mobile)
          };

          console.log(fakeComment);
          const fakeItems = contentListState ? getFakeItems(contentListState) : [];

          const newItems = [fakeComment, ...fakeItems, ...dataCommentOfContent?.items];
          setContentListState(newItems);
        }
      });
    }
  };
  const getFakeItems = (arr: any) => {
    const fakeItems: any = [];
    if (arr?.length > 0) {
      arr.map((el: any) => {
        if (el.id.includes('Fake')) {
          fakeItems.push(el);
        }
        return null; // Return statement added here
      });
    }
    return fakeItems;
  };
  const onClickCommentArea = (e: any) => {
    if (!profile?.id) {
      localStorage.setItem('currentAuthFlow', 'registration_feature');
      localStorage.setItem('currentAuthFeature', 'register_for_comment');
      const remakeDestination = encodeParamDestination(router?.asPath);
      router.push(
        `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${TYPE_TRIGGER_AUTH.COMMENT}`
      );
      if (e.target) e.target.blur();
    }
  };
  const moveArrayItemToNewIndex = (arr: any, old_index: any, new_index: any) => {
    if (new_index >= arr.length) {
      let k = new_index - arr.length + 1;
      while (k--) {
        arr.push(undefined);
      }
    }
    arr.splice(new_index, 0, arr.splice(old_index, 1)[0]);
    return arr;
  };

  let commentList = contentListState || dataCommentOfContent?.items || [];
  const ispinedHaveFakeItem = (commentList || []).filter(
    (item: any) => item?.pin !== 0 && item?.id.includes('Fake')
  );
  if (ispinedHaveFakeItem && commentList?.length > 0) {
    let pinedIndex = 0;
    if (commentList?.length) {
      (commentList || []).forEach((item: any, i: any) => {
        if (item.pin !== 0) pinedIndex = i;
      });
    }
    if (pinedIndex !== 0) {
      commentList = moveArrayItemToNewIndex(commentList, pinedIndex, 0);
    }
  }

  const isBannerRegistration =
    commentList.length > 0 && !!bannerConversionTrigger?.active && !profile?.id;

  const renderCommentBody = () => {
    if (blocked) {
      return (
        <div className={`section__body ${isDetail ? '' : Styles.commentBlocked}`}>
          <EmptyCommentBlocked isMobile={isMobile} />
        </div>
      );
    }

    return (
      <>
        {isBannerRegistration && (
          <BannerRegistration
            image={bannerConversionTrigger?.imageComment}
            imageMobile={bannerConversionTrigger?.imageCommentMobile}
            title={bannerConversionTrigger?.commentText}
            isComment
          />
        )}
        <CommentArea
          contentID={contentId}
          profile={profile}
          textAreaId={textAreaId}
          onClick={onClickCommentArea}
          onClickSend={sendComment}
          router={router}
        />
        <CommentList commentList={commentList} />
        {commentList.length === 0 && (
          <Empty userId={profile?.id} onClick={onClickTriggerConversion} />
        )}
      </>
    );
  };

  return (
    (commentList && (
      <section
        className={classnames('section section--intro section--comment', canalClass && 'canal-v')}
        ref={ref}
        id={EL_ID.INTRO_COMMENT}
      >
        <div className="space-y-4 w-full md:max-w-[876px]">
          <h4 className="text-[1rem] text-white font-medium leading-[140%]">{TEXT.COMMENT}</h4>
          {renderCommentBody()}
        </div>
      </section>
    )) ||
    null
  );
};

export default memo(forwardRef(Comment));
