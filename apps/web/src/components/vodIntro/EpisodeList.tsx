import React, { useEffect, useState } from 'react';
import Detail<PERSON>pi from '@apis/detailApi';
import { handleGetSlugEpsFromPath } from '@services/detailServices';
import { useVieRouter } from '@customHook';
import { UtmParams } from '@models/subModels';
import { getUrlFromPath } from '@helpers/common';
import { useDispatch, useSelector } from 'react-redux';
import { CONTENT_TYPE, PAGE } from '@constants/constants';
import { TEXT } from '@constants/text';
import { getEpisodeBySlug, setNextEpisode } from '@actions/episode';
import { VALUE } from '@config/ConfigSegment';
import TrackingApp from '@tracking/functions/TrackingApp';
import Slider from '@components/basic/Slider/Slider';
import classNames from 'classnames';
import { watchNowBehavior } from '../trigger/triggerFunction';
import FilterDropDown from '../basic/FilterDropDown';
import TagsOutline from '../basic/Tags/TagsOutline';
import CardVodIntro from '../basic/Card/CardVodIntro';

let mySwiper: any = null;
let mySetupSlider: any = null;
let myforceUpdateNavigation: any = null;
const EpisodeList = React.memo((props: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile);
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const isTablet = useSelector((state: any) => state?.App?.isTablet);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { content, contentDetail, dataViewItem, noSaveParam } = props;
  const [page, setPage] = useState(0);
  const dataServer = useSelector((state: any) => state?.Detail?.ACTION_GET_EPISODE_LIST_VOD);
  const [episodeData, setEpisodeData] = useState<any>({});
  const isDetailVOD = router.pathname === PAGE.VOD;
  const [episodeList, setEpisodeList] = useState<any>();
  const limit = 50;
  const [state, setState] = useState({
    episodeData: null,
    page: 0,
    activeSeason: (content?.relatedSeason || []).find((it: any) => it?.id === content?.id)
  });
  const { slugFromPath } = handleGetSlugEpsFromPath({
    path: state.activeSeason?.seoUrl || content?.seo?.url
  });
  let slug = slugFromPath;

  const isSeason = content?.type === CONTENT_TYPE.SEASON;
  const isMovie = content?.type === CONTENT_TYPE.MOVIE;

  useEffect(() => {
    if (!state.activeSeason) {
      setState({
        episodeData: null,
        page: 0,
        activeSeason: (content?.relatedSeason || []).find((it: any) => it?.id === content?.id)
      });
    }
  }, []);
  useEffect(() => {
    if (isSeason && state.activeSeason) {
      getEpisodeList();
      if (mySetupSlider) mySetupSlider();
    }
  }, [slug, state.activeSeason]);
  useEffect(() => {
    handleChangeContent();
  }, [content.id]);

  useEffect(() => {
    setNextEpisodeHandler();
  }, [contentDetail?.id, episodeData]);

  useEffect(() => {
    if (mySetupSlider) mySetupSlider();
  }, [(episodeList || dataServer?.items || []).length]);

  useEffect(() => {
    if (episodeData?.[state.activeSeason?.id]?.items) {
      setEpisodeList(episodeData?.[state.activeSeason?.id]?.items);
    }
  }, [state.activeSeason, episodeData]);

  const setupSlider = ({ slider, setupSlider, forceUpdateNavigation }: any) => {
    mySwiper = slider;
    mySetupSlider = setupSlider;
    myforceUpdateNavigation = forceUpdateNavigation;
  };

  const getEpisodeList = async () => {
    if (!slug) return;
    const episodeDataPage0 = await dispatch(
      getEpisodeBySlug({ slug, page: 0, limit, isGlobal })
    ).then((res: any) => res?.data);
    const pageChapterData = await DetailApi.getEpisodeRange({ contentId: state.activeSeason?.id });
    const { total } = episodeDataPage0?.metadata || {};
    const pageMax = Math.floor((total || 0) / 50);
    const prMiss = [];

    if (pageMax > 0 && total > limit) {
      let i = 1;
      while (i <= pageMax) {
        prMiss.push(
          dispatch(getEpisodeBySlug({ slug, page: i, limit, isGlobal })).then(
            (res: any) => res?.data
          )
        );
        i += 1;
      }
    }
    const epsDataPages = await Promise.all(prMiss);
    epsDataPages.unshift(episodeDataPage0);
    let epsData: any = [];
    (epsDataPages || []).forEach((dt) => {
      epsData = [...epsData, ...(dt?.items || [])];
    });

    setEpisodeData({
      ...episodeData,
      [state.activeSeason?.id]: { items: epsData, pageChapterData }
    });
  };
  const setNextEpisodeHandler = () => {
    const episodeList = episodeData?.[state.activeSeason?.id]?.items || [];
    if (router.pathname === PAGE.VOD) {
      if (contentDetail?.id && episodeList?.length) {
        const currentIndex = (episodeList || []).findIndex(
          (eps: any) => eps?.id === contentDetail?.id && !eps?.isEnd
        );
        if (currentIndex > -1) {
          const nextEpisode = episodeList?.[currentIndex + 1];
          dispatch(setNextEpisode(nextEpisode));
        }
      }
    }
  };

  const handleChangeContent = () => {
    const relatedSeason = (content?.relatedSeason || []).find((it: any) => it?.id === content?.id);
    if (state?.activeSeason?.id !== relatedSeason?.id) {
      setState((prevState) => ({
        ...prevState,
        activeSeason: relatedSeason
      }));
      setPage(0);
    }
    if (mySwiper && !mySwiper.destroyed) {
      mySwiper.slideTo(0, 500);
    }
  };
  const changeSeason = async (e: any, index: any, item: any) => {
    if (router.pathname === PAGE.VOD) {
      const { slugFromPath } = handleGetSlugEpsFromPath({ path: item?.seoUrl });
      slug = slugFromPath;
    } else if (item?.id !== content?.id) {
      const url = getUrlFromPath(router?.asPath);
      const utParams = UtmParams(router?.query);
      const q = router?.query?.q;
      const query = { ...utParams };
      if (q) query.q = q;
      query.vid = item?.id;
      router.push({ pathname: router?.pathname, query }, { pathname: url, query });
    }
    setState({ ...state, activeSeason: item });
    setPage(0);
  };

  const onChapterList = (pageValue: any) => {
    if (pageValue === page) return;
    setPage(pageValue);
    if (mySwiper) {
      let slideIndex = pageValue * 30;
      const dataItemsVOD = isDetailVOD ? 6 : 4;
      if (slideIndex % dataItemsVOD > 0) {
        slideIndex -= slideIndex % 4;
      }
      if (myforceUpdateNavigation) myforceUpdateNavigation({ pre: pageValue >= 1 });
      mySwiper.slideTo(slideIndex);
    }
  };

  const { relatedSeason, vodSchedule } = content || {};
  let { currentEpisode, episode } = state?.activeSeason || {};

  currentEpisode = currentEpisode ? parseInt(currentEpisode) : 0;
  episode = episode ? parseInt(episode) : 0;

  episode = episode <= 0 ? currentEpisode : episode;
  const chapterContent =
    currentEpisode === episode || currentEpisode > episode
      ? `${episode}/${episode} tập`
      : `${currentEpisode}/${episode} tập`;
  const chapterContents = episode <= 0 || currentEpisode <= 0 ? '' : chapterContent;

  const episodeListServer = dataServer?.items;
  const onClickNextEpisode = ({ activeIndex }: any) => {
    setPage(Math.floor((activeIndex + 3) / 30));
  };
  const onClickPrevEpisode = ({ activeIndex }: any) => {
    setPage(Math.floor((activeIndex + 3) / 30));
  };

  const onClickCard = async (item: any) => {
    TrackingApp.contentSelected({
      data: {
        ...item,
        ribbonId: VALUE.EPISODE.ID,
        ribbonName: VALUE.EPISODE.NAME,
        seasonThumb: content?.images?.thumbnail,
        seasonGenre: content?.genreText
      },
      clickType: VALUE.DIRECT
    });

    await watchNowBehavior({
      profile,
      currentProfile,
      contentData: item,
      router,
      dispatch,
      expand: true,
      noSaveParam,
      content: props?.content,
      isMobile,
      isGlobal
    });
  };

  const renderEpisodeItem = (item: any) => {
    const thumbClass = 'card__thumbnail-loader overflow ratio-16-9';

    return (
      <CardVodIntro
        item={item}
        category={content?.category}
        isProgressBar
        thumbClass={thumbClass}
        onClickCard={onClickCard}
        isVodDetail={isDetailVOD}
      />
    );
  };

  const { day } = vodSchedule || {};
  const chapterContentDayTag = [];
  if (chapterContents) chapterContentDayTag.push({ title: chapterContents });
  if (day) chapterContentDayTag.push({ title: day });

  const pageChapter = episodeData?.[state?.activeSeason?.id]?.pageChapterData;
  if (isMovie) return null;
  const dataRelatedSeason = relatedSeason || [];

  return (
    <div className="rocopa">
      <div className="rocopa__header vertical">
        <div className="rocopa__header-left">
          <h2 className="rocopa__title">{TEXT.EPISODE_LIST}</h2>

          <div className="flex flex-wrap space-x-1.5 lg:space-x-2">
            <TagsOutline tagArray={chapterContentDayTag} />
          </div>
        </div>
        <div className="rocopa__header-right">
          {relatedSeason && relatedSeason.length > 1 && (
            <FilterDropDown
              id="EPISODE_SEASON_LIST"
              className="filter filter--sort filter--dark"
              buttonClass="flex items-center justify-center max-w-[17.5rem] !border !border-solid !border-vo-gray-200 hover:!border-vo-green h-9 px-3 bg-vo-dark-gray-900/50 !text-white hover:!text-vo-green space-x-3 transition-colors"
              iconClass="icon--tiny icon--absolute"
              iconNameSlotRight="vie-chevron-down-red-medium"
              changeFilter={changeSeason}
              filterName={state.activeSeason?.name || content.seasonName}
              filterList={dataRelatedSeason}
              dropdownStyle={{ maxHeight: '200px' }}
              textClass="max-v-full whitespace-nowrap line-clamp-1 text-ellipsis block"
            />
          )}
          <nav className="menu space-x-4 md:space-x-6">
            {(pageChapter?.length > 0 ? pageChapter : []).map((item: any, i: any) => (
              <button
                key={item?.id || i}
                className={classNames(
                  'text-[.875rem]',
                  item?.page === page ? 'text-white' : 'text-white/70',
                  'hover:text-vo-green'
                )}
                title={`Tập ${item?.title}`}
                onClick={() => onChapterList(item?.page)}
                type="button"
              >
                {item?.title}
              </button>
            ))}
          </nav>
        </div>
      </div>
      <div className="rocopa__body">
        {(episodeList || episodeListServer || []).length > 0 && (
          <Slider
            setupSlider={setupSlider}
            onClickNextEpisode={onClickNextEpisode}
            onClickPrevEpisode={onClickPrevEpisode}
            renderSliderItem={renderEpisodeItem}
            data={episodeList || episodeListServer || []}
            itemCount={(episodeList || episodeListServer || []).length}
            dataViewItem={dataViewItem || 4}
            isMobile={isMobile}
            isTablet={isTablet}
            id={`idEpisode_${content.id}`}
            notLoop
            ribbonData={episodeList}
          />
        )}
      </div>
    </div>
  );
});

export default EpisodeList;
