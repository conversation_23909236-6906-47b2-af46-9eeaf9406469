import React from 'react';

class CommentItem extends React.PureComponent {
  render() {
    const { userName, userAvatar, createdAt, message, pin }: any = this.props;
    const className = pin === 1 ? 'media-object is-pinned' : 'media-object';
    return (
      <div className={className} style={{ position: 'relative' }}>
        <div className="media-object-section middle">
          {userAvatar ? (
            <div className="avatar overflow light circle normal gender">
              <img src={userAvatar} alt={userName} />
            </div>
          ) : (
            <a className="avatar default overflow light circle normal" title={userName}>
              {userName}
            </a>
          )}
        </div>
        <div className="media-object-section">
          <h4>{userName}</h4>
          <p className="text" style={{ whiteSpace: 'pre-line' }}>
            {message}
          </p>
        </div>
        <div className="media-object-section bottom">
          {pin === 1 && (
            <span className="icon icon--small">
              <i className="vie vie-pin-o-rc-medium" />
            </span>
          )}
          <div className="comment-time">
            <span className="relative-time">{createdAt}</span>
          </div>
        </div>
      </div>
    );
  }
}

export default CommentItem;
