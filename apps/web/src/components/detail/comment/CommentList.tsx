import React from 'react';
import CommentItem from './CommentItem';

const CommentList = ({ commentList }: any) => {
  if (!commentList || commentList.length === 0) return null;
  return (
    <div className="comment">
      {commentList.map((comment: any) => (
        <CommentItem key={comment.id} {...comment} />
      ))}
    </div>
  );
};

CommentList.propTypes = {
  // commentList: PropTypes.array
};

export default CommentList;
