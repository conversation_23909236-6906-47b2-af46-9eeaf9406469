import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import dynamic from 'next/dynamic';
import classNames from 'classnames';
import EpisodeList from '@components/vodIntro/EpisodeList';
import Related from '@components/vodIntro/Related';
import Recommended from '@components/vodIntro/Recommended';
import Comment from '@components/vodIntro/Comment';
import { getTipData } from '@actions/page';
import { CONTENT_TYPE, EL_ID, PERMISSION } from '@constants/constants';
import CompanionBanner from '@components/OutstreamAds/CompanionBanner';
import CardImage from '@components/basic/Card/CardImage';
import CardInfo from '../basic/Card/CardInfo';
import DetailPlayer from './DetailPlayer';

const BannerMwebToApp = dynamic(import('@components/home/<USER>'), { ssr: false });

const Detail = React.memo((props: any) => {
  const ref = useRef<any>(null);
  const refInfo = useRef<any>(null);
  const [totalComment, setTotalCommentState] = useState(0);
  const [playerReady, setPlayerReady] = useState(false);
  const dispatch = useDispatch();
  const tipData = useSelector((state: any) => state?.Page?.tipData);
  const popupData = useSelector((state: any) => state?.Popup);
  const { isMobile, isTablet, isSafari, isIOS, webConfig, isHasCompanionBanner } = useSelector(
    (state: any) => state?.App || {}
  );
  const { featureFlag } = webConfig || {};
  const { blockPlayer } = useSelector((state: any) => state?.Player || {});
  const enableMenu = useSelector((state: any) => state?.Menu?.enableMenu || {});
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const {
    content,
    contentDetail,
    profile,
    GET_CONTENT,
    router,
    currentEpisode,
    dataNewSEO,
    sigmaLoaded,
    handleEndSessionPlay
  } = props;

  const { permission, trialDuration, triggerLoginDuration, drmProvider, drmMerchant } =
    contentDetail || {};
  const { isMovieTrialInApp, isEpisodeTrialInApp, isTriggerToApp, hasObjectDetection } =
    contentDetail || content || {};

  const cardData = GET_CONTENT || content || {};
  const tipDataItem = tipData?.[cardData?.id];

  useEffect(() => {
    if (
      cardData?.id &&
      (cardData?.type === CONTENT_TYPE?.SEASON ||
        cardData?.type === CONTENT_TYPE?.EPISODE ||
        cardData?.type === CONTENT_TYPE?.MOVIE)
    ) {
      dispatch(getTipData({ id: cardData?.id }));
    }
  }, [cardData?.id]);

  useEffect(() => {
    window.scrollTo({ left: 0, top: 0, behavior: 'smooth' });
  }, [router.asPath]);

  const handlePlayerReady = (status: any) => {
    setPlayerReady(status);
  };

  const setTotalComment = (totalComment: any) => {
    setTotalCommentState(totalComment);
  };

  const handleScrollToComment = () => {
    if (window && ref?.current && refInfo?.current) {
      const navHeight = 36;
      const winHeight = window.innerHeight;
      const scrollTop =
        ref?.current?.offsetHeight - refInfo?.current?.offsetHeight + winHeight - navHeight;
      window.scrollTo({ left: 0, top: scrollTop, behavior: 'smooth' });
    }
  };

  const isCanWatch =
    (permission === PERMISSION.CAN_WATCH ||
      (permission !== PERMISSION.CAN_WATCH && (trialDuration > 0 || triggerLoginDuration > 0))) &&
    (!isTriggerToApp || isEpisodeTrialInApp || isMovieTrialInApp);

  return (
    <>
      <section
        className={classNames(
          'section section--vod-detail overflow',
          isMobile && '!mt-0 p-t',
          !enableMenu && 'layer-1006',
          isHasCompanionBanner &&
            '!flex space-x-2 h-full items-stretch md:max-h-[44.55vw] lg:max-h-[44.271vw]'
        )}
      >
        <aside className={classNames('w-full grow relative', isHasCompanionBanner && 'lg:h-full')}>
          <div className={classNames('section-body', isHasCompanionBanner && 'lg:px-[2.6vw]')}>
            <CardImage
              images={cardData?.images}
              className={classNames(
                'billboard__image__hero',
                contentDetail && 'absolute',
                isHasCompanionBanner && 'w-[calc(100%-5.12vw-4px)]'
              )}
              alt={cardData?.altSEOImg}
              title={cardData?.title}
              isMobile={isMobile}
              isCardDetail
              notLazy
            />
            {sigmaLoaded && (
              <DetailPlayer
                {...props}
                isSafari={isSafari}
                isIOS={isIOS}
                isMobile={isMobile}
                isTablet={isTablet}
                profile={profile}
                content={content || GET_CONTENT}
                handlePlayerReady={handlePlayerReady}
                blockPlayer={blockPlayer}
                drmProvider={drmProvider}
                drmMerchant={drmMerchant}
                handleEndSessionPlay={handleEndSessionPlay}
                isHasCompanionBanner={isHasCompanionBanner}
                popupData={popupData}
              />
            )}
          </div>
        </aside>
        {!isMobile && (!profile?.id || (profile?.id && !profile.isPremium && !isKid)) && (
          <CompanionBanner content={contentDetail} />
        )}
      </section>
      {cardData && (
        <div
          className={`intro intro--preview intro--preview-vod intro--preview-vod--modal intro--preview-vod--detail scrollable-y over-scroll-contain${
            isCanWatch ? ' intro--preview-vod--seo-detail' : ''
          } `}
          ref={ref}
          id="CardDetail"
          style={{
            zIndex: !contentDetail ? 0 : 1
          }}
        >
          <div className="intro__wrap">
            {(isMovieTrialInApp || isEpisodeTrialInApp) && featureFlag?.mwebToApp && (
              <BannerMwebToApp content={contentDetail} type={content?.type} />
            )}
            <CardInfo
              cardData={cardData}
              dataNewSEO={dataNewSEO}
              currentEpisode={currentEpisode || props.GET_CONTENT_EPISODE}
              tipDataItem={tipDataItem}
              profile={profile}
              totalComment={totalComment}
              handleScrollToComment={handleScrollToComment}
              isMobile={isMobile}
              isVodDetail
              playerReady={playerReady}
              hasObjectDetection={hasObjectDetection}
            />
            <section
              className={classNames(
                'section section--intro canal-v',
                isKid && (isMobile ? 'p-b3' : 'p-b4')
              )}
            >
              <div className="section__body">
                <EpisodeList
                  episodeDataProps={props?.episodeData}
                  content={cardData}
                  contentDetail={contentDetail}
                  dataViewItem={6}
                  noSaveParam
                />
                <Related content={cardData} dataViewItem={6} noSaveParam isVodDetail />
                <Recommended content={cardData} dataViewItem={6} noSaveParam isVodDetail />
              </div>
            </section>
            {!isKid && (
              <Comment
                ref={refInfo}
                content={cardData}
                contentDetail={contentDetail}
                setTotalComment={setTotalComment}
                profile={profile}
                canalClass
                windowScroll
                isDetail
                textAreaId={EL_ID.DETAIL_COMMENT}
              />
            )}
          </div>
        </div>
      )}
    </>
  );
});
export default Detail;
