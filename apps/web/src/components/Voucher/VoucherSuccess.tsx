import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import BillItem from '@components/payment/Step2/bill/BillItem';
import { numberWithCommas } from '@helpers/common';
import { TEXT } from '@constants/text';
import { CURRENCY, PAGE } from '@constants/constants';
import TrackingPayment from '@tracking/functions/payment';

const trackingPayment = new TrackingPayment();
const VoucherSuccess = (props: any) => {
  const router = useVieRouter();
  const userProfile = useSelector((state: any) => state?.Profile?.profile);
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile || {});

  const { voucherInfo } = props;
  const { name, price, groupName, expiredDate, startDate } = voucherInfo || {};

  let nameLabel = userProfile?.mobile;
  if (!nameLabel && userProfile?.email && userProfile?.emailVerified) {
    nameLabel = userProfile?.email;
  }
  if (!nameLabel) {
    nameLabel = userProfile?.givenName || '';
  }

  const handleContinue = () => {
    router.push(!currentProfile?.id ? PAGE.LOBBY_PROFILES : PAGE.HOME);
  };

  useEffect(() => {
    trackingPayment.voucherResultPageLoaded({ data: voucherInfo });
  }, [voucherInfo]);

  return (
    <section className="section section--payment section--payment-result !py-4 md:!py-6 overflow">
      <div className="container canal-v">
        <div className="section_body">
          <div className="block block--result block--result-pm">
            <div className="icon icon--variant-80 circle-outline m-x-auto overspread-animate">
              <i className="vie vie-tick" />
            </div>
            <div className="block__header">
              <h2 className="block__title text-center">{TEXT.VOUCHER_SUCCESS}</h2>
            </div>
            <div className="block__body">
              <div className="list-group">
                <div className="list-group__item divide--dashed-non-last align-middle">
                  <BillItem label={TEXT.ACCOUNT_VieON} value={nameLabel} />
                </div>
                <div className="list-group__item divide--dashed-non-last align-middle">
                  <BillItem label={TEXT.PACKAGE_NAME} value={groupName} />
                  <BillItem label={TEXT.TERM_NAME} value={name} />
                </div>
                <div className="list-group__item divide--dashed-non-last align-middle">
                  <BillItem label={TEXT.EFFECTIVE_DATE} value={startDate} />
                  <BillItem label={TEXT.USE_UP_TO} value={expiredDate} />
                </div>
                <div className="list-group__item divide--dashed-non-last align-middle !border-b-0">
                  <BillItem
                    billItemAlign=" align-middle"
                    label={TEXT.WORTH}
                    value={`${numberWithCommas(price) || 0} ${CURRENCY.VND}`}
                    valueClass="text text-bold large-x highlight"
                  />
                </div>
                <div className="button-group child-auto">
                  <button
                    className="button button--green button--large"
                    title="Xem tiếp VieON"
                    onClick={handleContinue}
                  >
                    <span className="text !text-white">{TEXT.CONTINUE_WATCH_VieON}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default VoucherSuccess;
