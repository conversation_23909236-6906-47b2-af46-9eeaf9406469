import React, { useEffect, useState } from 'react';
import { segmentEvent } from '@tracking/TrackingSegment';
import { NAME, PROPERTY, VALUE } from '@config/ConfigSegment';
import UserApi from '@apis/userApi';
import { TEXT } from '@constants/text';
import { EL_ID, POPUP, PAGE } from '@constants/constants';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import ConfigImage from '@config/ConfigImage';
import { useDispatch, useSelector } from 'react-redux';
import { updateStatusRedeemCode } from '@actions/user';
import { openPopup } from '@actions/popup';
import { getProfile } from '@actions/profile';
import { createTimeout, handleScrollTop, encodeParamDestination } from '@helpers/common';
import { useVieRouter } from '@customHook';
import classNames from 'classnames';
import InputGroupNew from '../basic/Input/InputGroupNew';
import Button from '../basic/Buttons/Button';
import Styles from './Voucher.module.scss';

const Voucher = (props: any) => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const profile = useSelector((state: any) => state?.Profile?.profile) || {};
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const { deviceModel, deviceName, deviceType } = useSelector((state: any) => state?.App);

  const { onSetVoucher } = props;
  const [voucherCode, setVoucherCode] = useState<any>('');
  const [dataResult, setDataResult] = useState<any>(null);

  useEffect(() => {
    if (isKid) dispatch(openPopup({ name: POPUP.NAME.KID_LIMITED_PAGE_DIALOG }));
    handleScrollTop();
  }, []);

  const onChangeCode = (event: any) => {
    const value = ((event?.target?.value || '').trim() || '').toUpperCase();
    setVoucherCode(value);
  };

  const onActiveVoucher = async () => {
    if (!profile?.id) {
      const remakeDestination = encodeParamDestination(router?.asPath);
      router.push(
        `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${TYPE_TRIGGER_AUTH.INPUT_VOUCHER}`
      );
      return;
    }
    const dataInfoVoucherRes = await UserApi.checkVoucherExist(voucherCode);
    if (!dataInfoVoucherRes?.success) {
      const result: any = { error: 400 };
      setDataResult({ ...result, message: dataInfoVoucherRes?.data?.message });
      segmentEvent(NAME.VOUCHER_CODE_INPUTTED, {
        [PROPERTY.CURRENT_PAGE]: window.location.href,
        [PROPERTY.VOUCHER_CODE]: voucherCode || '',
        [PROPERTY.CAUSE_FOR_FAILURE]: dataInfoVoucherRes?.data?.message || '',
        [PROPERTY.RESULT]: VALUE.FAIL
      });
    } else {
      UserApi.activeVoucher(voucherCode).then((res) => {
        const dataResult = res?.data;
        setDataResult({ ...dataResult });
        if (res.success) {
          onSetVoucher(dataInfoVoucherRes?.data);
        }
      });
      dispatch(updateStatusRedeemCode({ userId: profile?.id, voucherCode }));
      createTimeout(() => {
        dispatch(getProfile({ deviceModel, deviceName, deviceType }));
      }, 1000);
      segmentEvent(NAME.VOUCHER_CODE_INPUTTED, {
        [PROPERTY.CURRENT_PAGE]: window.location.href,
        [PROPERTY.VOUCHER_CODE]: voucherCode || '',
        [PROPERTY.RESULT]: VALUE.SUCCESS || ''
      });
    }
  };

  return (
    <section className="section section--payment section--payment-voucher !py-4 md:!py-6 overflow">
      <div className="container canal-v">
        <div className="section-body">
          <div className="block block--for-light block--payment block--payment-voucher">
            <div className="block-body">
              <div className="mask">
                <div className="mask-inner text-center">
                  <img src={ConfigImage.paymentVoucher} alt="payment-voucher" />
                </div>
              </div>
              <h2 className="title text-center">{TEXT.ENTER_VieON_CODE}</h2>
              <div className="form--payment-voucher">
                <InputGroupNew
                  name="CardNumber"
                  label={TEXT.VieON_CODE}
                  id={EL_ID.VOUCHER_INPUT}
                  className="input-for-light"
                  autoComplete="no-complete"
                  value={voucherCode}
                  onChange={onChangeCode}
                  error={dataResult?.message || dataResult?.errorMessage || ''}
                  style={{ '--field-last-mb': '32px' }}
                />
                <div className="button-group child-auto">
                  <Button
                    className="button button--green button--large"
                    disabled={!voucherCode}
                    title={TEXT.CONFIRM}
                    onClick={onActiveVoucher}
                    textClass={classNames(
                      '!text-[.875rem] lg:!text-[1rem]',
                      voucherCode ? '!text-white' : ''
                    )}
                  />
                </div>
              </div>
            </div>
            <div className={`text text-14 text-gray-900 ${Styles.textPolicy}`}>
              {`Bằng việc nhập và xác nhận Mã VieON, bạn xác nhận đã đọc và đồng ý với `}
              <a
                className="link"
                href={PAGE.REGULATION}
                target="_blank"
                title={TEXT.CONTRACT_POLICY}
                rel="noreferrer"
              >
                {TEXT.CONTRACT_POLICY}
              </a>
              {` của VieON`}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Voucher;
