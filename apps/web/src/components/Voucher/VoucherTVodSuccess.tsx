import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import BillItem from '@components/payment/Step2/bill/BillItem';
import { numberWithCommas } from '@helpers/common';
import { TEXT } from '@constants/text';
import { CURRENCY, PAGE } from '@constants/constants';

const VoucherTVodSuccess = (props: any) => {
  const router = useVieRouter();
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile || {});

  const { voucherInfo } = props;
  const {
    price,
    groupName,
    startDate,
    isLifeTimeTVodType,
    tVodWaitingDuration,
    tVodConsumingDuration
  } = voucherInfo || {};

  const handleContinue = () => {
    router.push(!currentProfile?.id ? PAGE.LOBBY_PROFILES : PAGE.HOME);
  };

  const parseTimeConsuming = useMemo(() => {
    if (tVodConsumingDuration / 24 > 2) return `${Math.ceil(tVodConsumingDuration / 24)} ngày`;
    return `${tVodConsumingDuration} giờ`;
  }, [tVodConsumingDuration]);

  return (
    <section className="section section--payment section--payment-result !py-4 md:!py-6 overflow">
      <div className="container canal-v">
        <div className="section_body">
          <div className="block block--result block--result-pm">
            <div className="icon icon--variant-80 circle-outline m-x-auto overspread-animate">
              <i className="vie vie-tick" />
            </div>
            <div className="block__header">
              <h2 className="block__title text-center">{TEXT.VOUCHER_SUCCESS}</h2>
            </div>
            <div className="block__body">
              <div className="list-group">
                <div className="list-group__item divide--dashed-non-last align-middle">
                  <BillItem label={TEXT.CONTENT_NAME} value={groupName} />
                  {!isLifeTimeTVodType && (
                    <BillItem label={`${TEXT.TERM}*`} value={parseTimeConsuming} />
                  )}
                </div>
                <div className="list-group__item divide--dashed-non-last align-middle">
                  <BillItem label={TEXT.EFFECTIVE_DATE} value={startDate} />
                </div>
                <div className="list-group__item divide--dashed-non-last align-middle !border-b-0">
                  <BillItem
                    billItemAlign=" align-middle"
                    label={TEXT.WORTH}
                    value={`${numberWithCommas(price) || 0} ${CURRENCY.VND}`}
                    valueClass="text text-bold large-x highlight"
                  />
                </div>
                <div className="button-group child-auto">
                  <button
                    className="button button--green button--large"
                    title="Xem tiếp VieON"
                    onClick={handleContinue}
                  >
                    <span className="text !text-white">{TEXT.CONTINUE_WATCH_VieON}</span>
                  </button>
                </div>
                {!isLifeTimeTVodType && (
                  <div className="pt-4 text-center text-[14px] text-[#646464]">
                    *Nội dung chưa xem được lưu trong{' '}
                    <span className="text-bold">{`${tVodWaitingDuration / 24} ngày`}</span>. Sau khi
                    bấm xem, bạn có <span className="text-bold">{parseTimeConsuming}</span> để xem
                    hết.
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default VoucherTVodSuccess;
