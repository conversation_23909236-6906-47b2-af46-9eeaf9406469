import React, { useState, useCallback } from 'react';
import cn from 'classnames';
import Song from './Song';
import Image from '../basic/Image/Image';
import NewIcon from '../basic/Icon/NewIcon';

function RapVietTopSong({ ribbonData }: any) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggleExpand = useCallback(() => {
    setIsExpanded((prev) => !prev);
  }, []);

  const visibleSongs =
    isExpanded && ribbonData?.length > 1 ? ribbonData.slice(0, 10) : ribbonData?.slice(0, 5);

  return (
    <div
      className="flex w-full justify-center md:w-[248px] lg:w-[24.4791667vw] flex-none z-[3] pt-12 md:pt-11 2xl:pt-12 3xl:pt-14"
      id="rv"
    >
      <div
        className={cn(
          'flex flex-col border-[2px] border-solid rounded-[12px] bg-gradient-to-r from-[#86691E] to-[#D2A940] p-0.5 w-full relative h-fit'
        )}
      >
        <Image
          src="/assets/img/rap-viet/rap-song.svg"
          notWebp
          className="w-[256px] max-w-[94%] 2xl:w-[284px] 3xl:w-[326px] self-center absolute left-1/2 -translate-x-1/2 -top-[46px] md:-top-[40px] lg:-top-[44px] 2xl:-top-[50px] 3xl:-top-[56px]"
        />
        {ribbonData.length > 0 ? (
          <div className="flex flex-col bg-[#222222] rounded-[12px] pt-[48px] pb-[24px] px-[12px] md:px-[16px] gap-[16px] md:gap-[24px] transition-all duration-300 ease-in-out">
            {visibleSongs.map((song: any, index: any) => (
              <Song
                key={index}
                songData={song}
                data={song.rawItem}
                index={index}
                isPaddingBottom={index < visibleSongs.length - 1}
              />
            ))}

            {ribbonData.length > 5 && (
              <div
                className="text-white font-medium mt-3 flex flex-col space-y-1 justify-center items-center hover:cursor-pointer hover:text-[#0AD418]"
                onClick={() => handleToggleExpand()}
              >
                {isExpanded ? 'Thu gọn bảng xếp hạng' : 'Xem toàn bộ bảng xếp hạng'}
                <NewIcon
                  iconName={!isExpanded ? 'vie-chevron-down-red-medium' : 'vie-chevron-up-r-medium'}
                />
              </div>
            )}
          </div>
        ) : (
          <div className="bg-[#222222] pt-[102px] space-y-[16px] rounded-[12px] w-full min-h-[375px] text-center ">
            <Image
              src="/assets/img/rap-viet/empty_rank.svg"
              notWebp
              className="max-w-[222px] w-[222px] h-[162px] md:max-w-[350px] md:w-[278px] md:h-[170px]"
            />
            <p className="text-[14px] font-[500] leading-[20px] text-[#DEDEDE]">
              Bảng xếp hạng đang được cập nhật
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export default RapVietTopSong;
