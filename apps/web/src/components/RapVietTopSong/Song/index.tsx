import React from 'react';
import TriggerAction from '@components/trigger/TriggerAction';
import { PLAYER_TYPE } from '@constants/player';
import cn from 'classnames';
import Image from '../../basic/Image/Image';

const Song = ({ songData, data, isPaddingBottom }: any) => {
  const { songPlace, songImage, songName, artistName, alt, index, id } = songData;

  return (
    <div
      className={cn(
        'w-full flex items-center justify-between text-white pb-[12px] space-x-2',
        isPaddingBottom && 'border-0 border-b-[1.3px] border-solid border-[#FFFFFF]/20'
      )}
    >
      <div className="flex flex-1 w-full items-center space-x-2 overflow-hidden">
        <div className="flex flex-none items-center justify-items-start text-[14px] md:text-[18px] font-[500] md:font-[700] leading-[20px] md:leading-[25.2px] w-[20px] px-[4px] relative">
          {songPlace}
        </div>
        <Image
          alt={alt ?? ''}
          src={songImage}
          className="!w-[64px] !h-[36px] md:!w-[80px] md:!h-[46px] rounded-[3px]"
        />
        <div className="flex flex-col flex-1 w-[calc(100%-96px)] md:w-[calc(100%-116px)]">
          <h3 className="font-semibold w-full truncate overflow-hidden whitespace-nowrap !text-[14px] md:!text-[18px] md:leading-[25.2px] leading-[20px] font-500">
            {songName}
          </h3>
          <p className="!text-[10px] truncate font-[400] leading-[14px] text-[#9B9B9B] md:leading-[19.6px] md:!text-[14px]">
            {artistName}
          </p>
        </div>
      </div>
      <div className="flex-none">
        <div className="rounded-full bg-black p-[4.3%] w-[40px] h-[40px] flex justify-center items-center">
          <TriggerAction
            contentData={data}
            key={`${id}-${index}-WATCH_NOW`}
            index={index}
            playerId={PLAYER_TYPE.MASTER_BANNER}
            triggerItem={{ key: 'WATCH_NOW' }}
            customButtonClass="!text-[14px] !ml-[4px]"
            isSchedule
            hideTitle
            customSpanClass="flex w-[30px] h-[30px] md:w-[40px] md:h-[40px] cursor-pointer justify-center items-center rounded-[30px] bg-black"
          />
        </div>
      </div>
    </div>
  );
};

export default Song;
