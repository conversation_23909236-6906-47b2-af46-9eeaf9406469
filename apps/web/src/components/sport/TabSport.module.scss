//
.tabs {
  border: none;
  background-color: none;
  &Title {
    background-color: transparent;
    font-family: 'Roboto Medium', sans-serif;
    font-size: 18px;
    line-height: normal;
    position: relative;
    margin: 0 2rem 10px 0;
    text-align: center;
    color: #9b9b9b;
    padding: 20px 24px;

    &::before {
      display: block;
      content: '';
      position: absolute;
      width: 100%;
      height: 1.5px;
      right: 0;
      bottom: -1px;
      left: 0;
      transform: scale(0);
      visibility: hidden;
      opacity: 0;
      transition: 0.25s all ease;
      background-color: #3ac882;
    }

    a {
      font-size: 18px;
      background-color: transparent;
      color: #9b9b9b;
    }

    &.active,
    &:hover,
    &:focus {
      a {
        color: #fff;
      }

      &::before {
        transform: scale(1);
        visibility: visible;
        opacity: 1;
      }
    }
  }

  &Sport {
    border-bottom: 1px solid;
    display: flex;
    margin: 0;

    .tabsTitle {
      width: 50%;
      margin: 0;
      padding-bottom: 12px;
      &::before {
        background-color: #646464;
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        transform: scale(1);
        visibility: visible;
        opacity: 1;
      }
      a {
        font-size: 16px;
      }
      &.active {
        &::before {
          background-color: #3ac882;
        }
      }
    }
  }
}
