import React from 'react';
import { useVieRouter } from '@customHook';
import { MATCH_STATUS, TAG_KEY } from '@constants/constants';
import Image from '../../basic/Image/Image';
import sportStyles from '../RankingSport.module.scss';
import Button from '../../basic/Buttons/Button';
import Tags from '../../basic/Tags/Tags';

const ScheduleTeam = (props: any) => {
  const router = useVieRouter();
  const { item, dayFootball } = props;

  const onClickMatch = ({ match }: any) => {
    if (!match?.isPointer) return;
    router.push(match.href, match.contentUrl);
  };

  return (
    <>
      <div className={sportStyles.scheduleTime}>{dayFootball}</div>
      {item &&
        (item[1] || []).map((it: any, index: any) => {
          const scoreAway = it?.awayTeam?.score;
          const scoreHome = it?.homeTeam?.score;
          const scoreSum = `${scoreHome} - ${scoreAway}`;
          return (
            <div
              key={index}
              className={sportStyles.sportMatch}
              style={{ cursor: it?.isPointer ? 'pointer' : '' }}
              onClick={() => onClickMatch({ match: it })}
            >
              {it?.playIconClass && (
                <Button
                  className={`button button--replay absolute ${sportStyles.button}`}
                  iconClass={sportStyles.icon}
                  iconName={it.playIconClass}
                />
              )}

              {it?.isLive && <Tags tagKey={TAG_KEY.LIVE} subClass={sportStyles.tags} />}

              <div className={sportStyles.club}>
                <div className={sportStyles.clubLogo}>
                  <Image src={it?.homeTeam?.crestUrl} alt={it?.homeTeam?.tla} notWebp />
                </div>
                <div className={sportStyles.clubName}>
                  <span>{it?.homeTeam?.tla}</span>
                </div>
              </div>
              <div className={sportStyles.matchInfo}>
                <div
                  className={`${sportStyles.time} ${it?.isLive ? sportStyles.active : ''}
                ${it?.matchStatus === MATCH_STATUS.FINISHED ? sportStyles.premiered : ''}`}
                >
                  {it?.matchStatus === MATCH_STATUS.FINISHED || it?.isLive
                    ? scoreSum
                    : it?.matchHours}
                </div>
                {it?.liveTVImage && (
                  <div className={sportStyles.channelShow}>
                    <img src={it?.liveTVImage} alt="K+TV" />
                  </div>
                )}
              </div>
              <div className={sportStyles.club}>
                <div className={sportStyles.clubLogo}>
                  <Image
                    src={it?.awayTeam?.crestUrl}
                    alt={it?.awayTeam?.tla}
                    className={sportStyles.clubLogo}
                    notWebp
                  />
                </div>
                <div className={sportStyles.clubName}>
                  <span>{it?.awayTeam?.tla}</span>
                </div>
              </div>
            </div>
          );
        })}
    </>
  );
};
export default React.memo(ScheduleTeam);
