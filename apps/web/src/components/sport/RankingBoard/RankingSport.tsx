import React from 'react';
import { RANKING_TAB } from '@constants/constants';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';
import sportStyles from '../RankingSport.module.scss';
import RankingTeam from './RankingTeam';

const RankingSport = ({ rankingList, rankingTab }: any) => {
  let className = `${sportStyles.sportRanking} ${rankingTab !== RANKING_TAB.RANKING ? 'hide' : ''}`;
  if (rankingList === null) className += ` ${sportStyles.empty} text-center`;

  return (
    <div className={className}>
      {rankingList === null ? (
        <div className="content">
          <img src={ConfigImage.emptyRank} alt="" />
          <p className="text text-white">{TEXT.RANK_UPDATING}</p>
        </div>
      ) : (
        <>
          <div className={sportStyles.sportRankingHeader}>
            <div className={sportStyles.rankingPosition}>Hạng</div>
            <div className={sportStyles.rankingClub}>Đội</div>
            <div className={sportStyles.rankingMatch}>Trận</div>
            <div className={sportStyles.rankingPts}>HS</div>
            <div className={sportStyles.rankingPoints}>Điểm</div>
          </div>
          <div className={sportStyles.sportRankingBody}>
            {(rankingList || []).map((it: any, ind: any) => (
              <RankingTeam key={ind} itemSport={it} index={ind} />
            ))}
          </div>
        </>
      )}
    </div>
  );
};
export default React.memo(RankingSport);
