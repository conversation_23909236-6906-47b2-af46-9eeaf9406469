import React, { useEffect, useState } from 'react';
import { RANKING_TAB } from '@constants/constants';
import { useSelector } from 'react-redux';
import RankingHeader from './RankingHeader';
import RankingBody from './RankingBody';
import sportStyles from '../RankingSport.module.scss';

const RankingBoard = ({ isRankingMobile, setRankingMobile }: any) => {
  const activeCompetition = useSelector((state: any) => state?.Sport?.activeCompetition);
  const [rankingTab, setRankingTab] = useState(RANKING_TAB.SCHEDULE);
  const selectActiveTab = ({ tapKey }: any) => {
    setRankingTab(tapKey);
  };

  useEffect(() => {
    setRankingTab(RANKING_TAB.SCHEDULE);
  }, [activeCompetition]);

  let rankingBoardClass = `block ${sportStyles.landingSport}`;
  if (isRankingMobile) {
    rankingBoardClass += ` ${sportStyles.isVisible} ${sportStyles.sportInfoFixed}`;
  }

  return (
    <div className={rankingBoardClass}>
      <RankingHeader
        rankingTab={rankingTab}
        selectActiveTab={selectActiveTab}
        setRankingMobile={setRankingMobile}
        isRankingMobile={isRankingMobile}
      />
      <RankingBody rankingTab={rankingTab} />
    </div>
  );
};
export default React.memo(RankingBoard);
