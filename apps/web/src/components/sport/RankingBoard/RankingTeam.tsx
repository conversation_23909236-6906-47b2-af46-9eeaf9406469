import React from 'react';
import Image from '../../basic/Image/Image';
import sportStyles from '../RankingSport.module.scss';

const RankingTeam = (props: any) => {
  const { itemSport, index } = props;
  return (
    <div className={sportStyles.sportRankingRow} key={index}>
      <div className={sportStyles.rankingPosition}>{itemSport?.position}</div>
      <div className={sportStyles.rankingClub}>
        <Image src={itemSport?.team?.crestUrl} alt={itemSport?.team?.tla} notWebp />
        <span>{itemSport?.team?.shortName}</span>
      </div>
      <div className={sportStyles.rankingMatch}>{itemSport?.playedGames}</div>
      <div className={sportStyles.rankingPts}>{itemSport?.goalDifference}</div>
      <div className={sportStyles.rankingPoints}>{itemSport?.points}</div>
    </div>
  );
};
export default RankingTeam;
