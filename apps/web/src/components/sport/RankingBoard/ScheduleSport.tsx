import React from 'react';
import { RANKING_TAB } from '@constants/constants';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';
import sportStyles from '../RankingSport.module.scss';
import ScheduleTeam from './ScheduleTeam';

const ScheduleSport = ({ matches, rankingTab }: any) => {
  let className = `${sportStyles.sportSchedule} ${
    rankingTab !== RANKING_TAB.SCHEDULE ? 'hide' : ''
  }`;
  if (matches === null) className += ` ${sportStyles.empty} text-center`;

  return (
    <div className={className}>
      {matches === null ? (
        <div className="content">
          <img src={ConfigImage.emptyMatch} alt="" />
          <p className="text text-white">{TEXT.SCHEDULE_UPDATING}</p>
        </div>
      ) : (
        <>
          {matches &&
            (matches || []).map((item: any, index: any) => {
              const dayFootball = `${item[1]?.[0].matchFirstDay} - ngày ${item[1]?.[0].matchDay} tháng ${item[1]?.[0].matchMonth}`;
              return (
                <div key={item?.[0]} className={sportStyles.sportScheduleMatch}>
                  <ScheduleTeam item={item} index={index} dayFootball={dayFootball} />
                </div>
              );
            })}
        </>
      )}
    </div>
  );
};
export default React.memo(ScheduleSport);
