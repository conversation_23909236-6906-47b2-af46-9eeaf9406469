import React from 'react';
import { RANKING_TAB } from '@constants/constants';
import { TEXT } from '@constants/text';
import tabStyle from '../TabSport.module.scss';

const TabsSport = ({ selectActiveTab, rankingTab }: any) => {
  const onSchedule = () => {
    if (selectActiveTab) selectActiveTab({ tapKey: RANKING_TAB.SCHEDULE });
  };

  const onRanking = () => {
    if (selectActiveTab) selectActiveTab({ tapKey: RANKING_TAB.RANKING });
  };

  return (
    <ul className={`${tabStyle.tabs} ${tabStyle.tabsSport}`}>
      <li
        className={`${tabStyle.tabsTitle} ${
          rankingTab === RANKING_TAB.SCHEDULE ? tabStyle.active : ''
        }`}
      >
        <a title={TEXT.SCHEDULE_SPORT} onClick={onSchedule}>
          {TEXT.SCHEDULE_SPORT}
        </a>
      </li>
      <li
        className={`${tabStyle.tabsTitle} ${
          rankingTab === RANKING_TAB.RANKING ? tabStyle.active : ''
        }`}
      >
        <a title={TEXT.RANKING_SPORT} onClick={onRanking}>
          {TEXT.RANKING_SPORT}
        </a>
      </li>
    </ul>
  );
};
export default React.memo(TabsSport);
