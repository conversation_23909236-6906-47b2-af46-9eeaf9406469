import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getMatches, getRankingList } from '@actions/sport';
import ScheduleSport from './ScheduleSport';
import sportStyles from '../RankingSport.module.scss';
import RankingSport from './RankingSport';

const RankingBody = ({ rankingTab }: any) => {
  const dispatch = useDispatch();
  const { activeCompetition, competitions } = useSelector((state: any) => state?.Sport);
  const activeComps = (competitions || []).find(
    (item: any) => item.code === activeCompetition.code
  );

  useEffect(() => {
    if (activeCompetition && !activeCompetition?.matches && activeCompetition?.code) {
      dispatch(getMatches({ competitionCode: activeCompetition.code }));
    }

    if (activeCompetition && !activeCompetition?.rankingList && activeCompetition?.code) {
      dispatch(getRankingList({ competitionCode: activeCompetition.code }));
    }
  }, [activeCompetition]);

  return (
    <div className={sportStyles.sportInfoBody}>
      <ScheduleSport rankingTab={rankingTab} matches={activeComps?.matches} />
      <RankingSport rankingTab={rankingTab} rankingList={activeComps?.rankingList} />
    </div>
  );
};
export default React.memo(RankingBody);
