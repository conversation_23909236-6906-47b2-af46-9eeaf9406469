import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setActiveCompetition } from '@actions/sport';
import { createTimeout } from '@helpers/common';
import TabsSport from './TabsSport';
import sportStyles from '../RankingSport.module.scss';
import Button from '../../basic/Buttons/Button';
import SliderAuto from '../../basic/Slider/SliderAuto';

let swiperEl: any = null;
const RankingHeader = ({ selectActiveTab, rankingTab, setRankingMobile, isRankingMobile }: any) => {
  const dispatch = useDispatch();
  const { competitions, activeCompetition } = useSelector((state: any) => state?.Sport);

  useEffect(() => {
    if (isRankingMobile && swiperEl) {
      swiperEl.update();
    }
  }, [isRankingMobile]);
  const initSwiper = ({ swiper }: any) => {
    swiperEl = swiper;
    if (!swiper?.slideTo) return;
    const activeIndex = (competitions || []).findIndex(
      (comp: any) => comp?.code === activeCompetition?.code
    );
    createTimeout(() => {
      if (!swiper?.destroyed) {
        swiperEl.update();
        swiper.slideTo(activeIndex);
      }
    }, 1000);
  };

  const onSelectCompetition = (item: any) => {
    dispatch(setActiveCompetition({ competition: item }));
  };

  const renderCompetitions = (item: any) => (
    <Button
      className={`${sportStyles.button} button hollow rounded ${
        activeCompetition?.code === item?.code ? sportStyles.active : ''
      }`}
      imgSrc={item?.logo || ''}
      title={item?.name || ''}
      onClick={() => onSelectCompetition(item)}
    />
  );

  if ((competitions || []).length === 0) return null;

  return (
    <div className={sportStyles.sportInfoHeader}>
      {activeCompetition?.background && (
        <img src={activeCompetition?.background} alt="premier league" />
      )}
      {isRankingMobile && (
        <Button
          className="button close button--geometry-circle button--dark-glass absolute top-1 right-1"
          iconClass="icon--tiny"
          iconName="vie-times-medium"
          onClick={setRankingMobile}
        />
      )}
      <div className="p-y1">
        <SliderAuto
          className={`${sportStyles.sliderRankingSport} slider--ranking-sport overflow`}
          itemClass={`${sportStyles.sliderItem}`}
          dataViewItem="auto"
          itemCount={(competitions || []).length}
          spaceBetween={12}
          containerModifierClass={`slider ${sportStyles.sliderRankingSport} non-hover overflow `}
          renderSliderItem={renderCompetitions}
          data={competitions || []}
          initSwiper={initSwiper}
        />
      </div>
      <TabsSport rankingTab={rankingTab} selectActiveTab={selectActiveTab} />
    </div>
  );
};
export default React.memo(RankingHeader);
