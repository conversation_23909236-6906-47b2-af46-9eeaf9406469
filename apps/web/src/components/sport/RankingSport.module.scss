.empty {
  padding-top: 45px;
  background-color: #222222;
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;

  img {
    padding-bottom: 12px;
  }
}

.isVisible {
  display: block !important;
  animation: fadeIn 0.4s alternate ease-in-out;
}

.remove {
  display: none !important;
  animation: fadeOut 0.5s linear 0s 1 forwards;
}

.landingSport {
  @apply w-full md:w-[28.1770833333vw] 2xl:w-[23.1770833333vw];
  @apply bg-vo-dark-gray-850 rounded-xl overflow-hidden;
  @apply transition-all duration-500 ease-in;

  .sportInfoHeader {
    padding-top: 1.4375rem;
    position: relative;

    & > img {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
    }

    .sliderRankingSport {
      margin-bottom: 0.3125rem;
      padding-right: 0.75rem;
      padding-left: 0.75rem;
      margin-top: 3px;

      .sliderItem {
        width: -moz-fit-content;
        width: fit-content;
      }
    }

    .button {
      background-color: #22222280;
      height: 2.125rem;
      border: 0.8px solid #9b9b9b;
      padding-right: 18px;
      padding-left: 18px;

      span {
        margin-left: 0.25rem;
        font-size: 0.875rem;
        font-weight: 700;
        white-space: nowrap;
        line-height: 1.25;
        font-family: 'Roboto Medium', sans-serif;
        color: #9b9b9b;
      }

      img {
        max-width: 20px;
        max-height: 20px;
        transition: all 0.25s ease;
        -webkit-filter: grayscale(90%);
        filter: grayscale(80%);
        opacity: 0.6;
      }

      &.active,
      &:hover {
        border-color: #3ac882;
        color: #fff;

        img {
          -webkit-filter: grayscale(0);
          filter: grayscale(0);
          opacity: 1;
        }

        span {
          color: #3ac882;
        }
      }
    }
  }

  .sportInfoBody {
    position: relative;
    background-color: #333333ff;

    .sportSchedule {
      padding-right: 1.375rem;
      padding-bottom: 2rem;
      padding-left: 1.375rem;

      .sportScheduleMatch {
        padding-top: 8px;
        color: #fff;
        font-size: 14px;

        &:first-child {
          padding-top: 24px;
        }
      }

      .scheduleTime {
        color: #dededeff;
        margin-bottom: 0.75rem;
        font-weight: 500;
        font-size: 1.125rem;
      }

      .sportMatch {
        margin: 0 0 1rem 2.625rem;
        padding: 0 0 1rem 0.625rem;
        position: relative;
        display: flex;
        align-items: center;

        &:not(:last-child) {
          border-bottom: 0.0625rem solid #474747;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .tags {
          position: absolute;
          left: -2.625rem;
        }

        .button {
          position: absolute;
          right: -0.625rem;

          .icon {
            i {
              font-size: 16px;
              color: #fff;
            }
          }
        }

        .club {
          width: 27%;
          display: flex;
          align-items: center;

          span {
            width: 2.75rem;
            display: flex;
            font-weight: 500;
            font-size: 1rem;
            justify-content: center;
            padding-left: 0.75rem;
            text-align: right;
            white-space: nowrap;
          }

          &Logo {
            width: 2.375rem;
            height: 2.375rem;
            justify-content: center;

            img {
              width: 2.375rem;
              height: 2.375rem;
            }
          }

          &:last-child {
            display: flex;
            justify-content: flex-end;
          }
        }

        .matchInfo {
          align-items: center;
          display: flex;
          flex-flow: column wrap;

          & > * {
            &:not(:last-child) {
              margin-bottom: 4px;
            }
          }
        }

        .channelShow {
          align-items: center;
          display: flex;
          justify-content: center;
          height: 24px;
          width: 100%;

          img {
            width: auto;
            max-height: 100%;
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
          }
        }

        .time {
          align-items: center;
          background: linear-gradient(181.01deg, #111111 0.81%, rgba(17, 17, 17, 0) 100.05%);
          color: #fff;
          width: 4.5rem;
          height: 26px;
          border-radius: 0.125rem;
          border: 0.0625rem solid #9b9b9bff;
          margin-right: 0.6875rem;
          margin-left: 0.6875rem;
          font-size: 1.125rem;
          display: flex;
          justify-content: center;
          padding-top: 1px;

          &.active {
            color: #3ac882ff;
          }

          &.premiered {
            color: #9b9b9bff;
          }
        }

        &.active {
          .time {
            color: #3ac882ff;
          }
        }

        &.premiered {
          .time {
            color: #9b9b9bff;
          }
        }
      }
    }

    .sportRanking {
      color: #fff;
      font-size: 0.875rem;
      padding-right: 1.5rem;
      padding-bottom: 2rem;
      padding-left: 1.5rem;

      .rankingPosition {
        width: 23%;
      }

      .rankingClub {
        width: 38%;
        display: flex;
        align-items: center;

        img {
          width: 2.4375rem;
          height: 1.875rem;
          padding-right: 0.5625rem;
        }
      }

      .rankingMatch {
        text-align: center;
        width: 13%;
      }

      .rankingPts,
      .rankingPoints {
        text-align: center;
        width: 13%;
      }

      &Header {
        display: flex;
        padding-top: 2rem;
        padding-bottom: 1rem;
        border-bottom: 0.0625rem solid #474747;
      }

      &Body {
        .sportRankingRow {
          align-items: center;
          display: flex;
          padding-top: 0.75rem;
          padding-bottom: 0.75rem;

          &:not(:last-child) {
            border-bottom: 0.0625rem solid #474747;
          }

          .rankingPosition {
            font-weight: bold;
          }

          .rankingClub {
            span {
              font-weight: 500;
            }
          }

          .rankingPoints {
            font-weight: bold;
          }
        }
      }
    }
  }

  @media screen and (max-width: 1023px) {
    .sportInfoHeader {
      .button {
        padding-left: 12px;
        padding-right: 12px;

        .text {
          font-size: 12px;
        }
      }
    }

    .sportInfoBody {
      .sportSchedule {
        padding: 12px 18px 22px 18px;

        .scheduleTime {
          font-size: 14px;
        }

        .sportMatch {
          margin-left: 32px;
          padding-left: 0;

          .club {
            &Logo {
              width: 32px;
              height: 32px;

              img {
                width: 32px;
                height: 32px;
              }

              span {
                width: 2.5rem;
              }
            }

            &Name {
              span {
                font-size: 14px;
                padding-left: 8px;
              }
            }
          }

          .matchInfo {
            margin-left: 6px;
            margin-right: 6px;

            .time {
              font-size: 16px;
              width: 52px;
            }
          }

          .button {
            padding-left: 0;
            padding-right: 0;
            position: absolute;
            right: -16px;
          }
        }
      }

      .sportRanking {
        padding-left: 18px;
        padding-right: 18px;
        padding-bottom: 4px;

        img {
          height: 125px;
        }

        &Body {
          .sportRankingRow {
            padding-top: 8px;
            padding-bottom: 8px;
          }
        }
      }
    }
  }

  @media screen and (max-width: 736px) {
    display: none;
    transition: opacity 600ms, visibility 600ms;
    padding-bottom: 16px;
    padding-right: 12px;

    &.sportInfoFixed {
      width: 100%;
      height: 100vh;
      padding-right: 0;
      position: fixed;
      top: 0rem;
      left: 0;
      min-width: 100vw;
      z-index: 1006;
    }

    .sportInfoHeader {
      padding-top: 42px;
      border-radius: 0;
    }

    .sportInfoBody {
      height: calc(100vh - 137px);
      overflow-y: auto;
      border-radius: 0;

      .sportSchedule {
        .sportMatch {
          margin-right: 12px;
          justify-content: center;

          .tags {
            left: -26px;
          }

          .button {
            right: 0;
          }
        }
      }
    }
  }

  @media screen and (max-width: 600px) {
    .sportInfoBody {
      .sportRanking {
        padding-right: 12px;
        padding-left: 12px;
      }
    }
  }

  @media screen and (max-width: 414px) {
    .sportInfoBody {
      .sportSchedule {
        .sportMatch {
          margin-right: 8px;

          .button {
            right: -20px;
          }
        }
      }
    }
  }
}
