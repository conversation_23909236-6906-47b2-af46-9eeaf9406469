import React, { useEffect, useMemo, useRef, useState } from 'react';
import get from 'lodash/get';
import { watchNowBehavior } from '@components/trigger/triggerFunction';
import { BILLBOARD, BANNER_IMAGE_RATE } from '@constants/constants';
import { PLAYER_STATUS } from '@constants/player';
import LocalStorage from '@config/LocalStorage';
import { VALUE } from '@config/ConfigSegment';
import TrackingApp from '@tracking/functions/TrackingApp';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import Content from './Content';
import Backdrop from './Backdrop';

const ItemMasterBanner = ({
  data,
  router,
  dispatch,
  profile,
  currentProfile,
  config,
  activeSubMenu,
  activeMenu,
  isActive,
  onNextSlide,
  isMobile,
  needPausePlayer
}: any) => {
  const isPlayerMuted = useMemo(() => {
    const muted = ConfigLocalStorage.get(LocalStorage.TRAILER_MUTED);
    if (muted === 'true') {
      return true;
    }
    return false;
  }, []);

  const playerRef = useRef<any>(null);
  const timeIntervalRef = useRef<any>(null);
  const timeOutNextSlideRef = useRef<any>(null);
  const [playerStatus, setPlayerStatus] = useState(PLAYER_STATUS.ERROR);
  const [muted, setMuted] = useState(isPlayerMuted);
  const [timer, setTimer] = useState(1);
  const hlsLinkPlay = get(data, 'linkPlay.hlsLinkPlay', '');
  const canClickBanner = useMemo(
    () =>
      playerStatus === PLAYER_STATUS.ENDED ||
      playerStatus === PLAYER_STATUS.ERROR ||
      !hlsLinkPlay ||
      isMobile,
    [playerStatus, hlsLinkPlay]
  );

  useEffect(
    () => () => {
      clearInterval(timeIntervalRef.current);
      clearTimeout(timeOutNextSlideRef.current);
    },
    []
  );

  useEffect(() => {
    if (!isActive) playerRef.current = null;
    return () => {
      if (isActive && typeof playerRef.current?.pause === 'function') playerRef.current.pause();
    };
  }, [isActive]);

  useEffect(() => {
    if (playerRef.current) {
      const allowPlay = checkAllowPlay();
      if (needPausePlayer && playerRef.current.played) {
        playerRef.current.pause();
      } else if (allowPlay && !playerRef.current.ended && playerRef.current.paused) {
        playerRef.current.play();
      }
    }
  }, [needPausePlayer]);

  useEffect(() => {
    clearInterval(timeIntervalRef.current);
    setPlayerStatus(0);
    setTimer(0);
  }, [data?.id, isActive]);

  useEffect(() => {
    if (playerStatus === PLAYER_STATUS.PLAYING) {
      handleBillboardTimer();
    }
  }, [playerStatus]);

  useEffect(() => {
    if (data?.id && canClickBanner && isActive && !isMobile) {
      timeOutNextSlideRef.current = setTimeout(() => {
        onNextSlide();
      }, 3000);
    }
    return () => {
      clearTimeout(timeOutNextSlideRef.current);
    };
  }, [canClickBanner, data?.id, isActive, isMobile]);

  useEffect(() => {
    if (timer >= BILLBOARD.TITLE_CARD_TIMER) {
      clearInterval(timeIntervalRef.current);
    }
  }, [timer]);

  const checkAllowPlay = () => {
    const html = document.getElementsByTagName('html')?.[0];
    let allowPlay = false;
    if (html) {
      const scrollTop = html?.scrollTop;
      const bannerHeight = (html.clientWidth || 0) * BANNER_IMAGE_RATE;
      allowPlay = scrollTop < bannerHeight * 0.6;
    }
    return allowPlay;
  };

  const handleBillboardTimer = () => {
    clearInterval(timeIntervalRef.current);
    timeIntervalRef.current = setInterval(() => {
      setTimer((a) => a + 1);
    }, 1000);
  };

  const onSetupPlayer = ({ player }: any) => {
    playerRef.current = player;
  };

  const onPlayerEnded = () => {
    setPlayerStatus(PLAYER_STATUS.ENDED);
  };

  const onPlayerPlay = ({ isMuted }: any) => {
    setTimer(0);
    if (!playerRef.current?.paused) setMuted(isMuted);
  };

  const onPlayerPlaying = () => {
    setPlayerStatus(PLAYER_STATUS.PLAYING);
  };

  const onPlayerError = () => {
    setPlayerStatus(PLAYER_STATUS.ERROR);
  };

  const onClickControlPlayer = (e: any) => {
    e.preventDefault();
    if (!playerRef.current) return;
    if (playerStatus === PLAYER_STATUS.PLAYING) {
      playerRef.current.muted = !muted;
      setMuted(!muted);
      ConfigLocalStorage.set(LocalStorage.TRAILER_MUTED, !muted);
    } else if (playerStatus === PLAYER_STATUS.ENDED) {
      playerRef.current.play();
    }
  };

  const onSaveProgressForVideoIntro = () => {
    const currentTime = playerRef.current?.ended ? 0 : playerRef.current?.currentTime || 0;
    if (currentTime > 0) {
      ConfigLocalStorage.set(
        LocalStorage.BANNER_TRAILER_PROGRESS,
        JSON.stringify({ [data?.id]: currentTime })
      );
    }
  };

  const onClickBanner = async () => {
    await watchNowBehavior({
      profile,
      currentProfile,
      contentData: data,
      router,
      dispatch,
      isMobile
    });
    TrackingApp.contentSelected({
      data: { ...data, seasonThumb: data?.images?.thumbnail, seasonGenre: data?.genreText },
      clickType: VALUE.HOVER_CLICK,
      activeMenu,
      activeSubMenu
    });
  };

  const clickMask = () => {
    if (canClickBanner) {
      onClickBanner();
    }
  };

  return (
    <div className="master-banner">
      <div className="master-banner__container relative ratio-variant">
        <Backdrop
          isActive={isActive}
          isMobile={isMobile}
          muted={muted}
          data={data}
          onClick={clickMask}
          onSetupPlayer={onSetupPlayer}
          onPlayerEnded={onPlayerEnded}
          onPlayerPlay={onPlayerPlay}
          onPlayerPlaying={onPlayerPlaying}
          onPlayerError={onPlayerError}
        />
        <Content
          config={config}
          isMobile={isMobile}
          isActive={isActive}
          playerStatus={playerStatus}
          timer={timer}
          muted={muted}
          data={data}
          onClickControlPlayer={onClickControlPlayer}
          onSaveProgressForVideoIntro={onSaveProgressForVideoIntro}
          onClickMask={clickMask}
        />
      </div>
    </div>
  );
};

export default ItemMasterBanner;
