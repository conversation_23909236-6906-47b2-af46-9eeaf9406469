import React from 'react';
import get from 'lodash/get';
import Image from '@components/basic/Image/Image';
import MiniPlayer from '@components/basic/MiniPlayer/MiniPlayer';
import { PLAYER_TYPE } from '@constants/player';
import { setCardImage } from '@helpers/common';

const Backdrop = ({
  muted,
  data,
  onClick,
  onSetupPlayer,
  onPlayerEnded,
  onPlayerPlay,
  onPlayerPlaying,
  onPlayerError,
  isActive,
  isMobile
}: any) => {
  const id = get(data, 'id', '');
  const title = get(data, 'title', '');
  const images = get(data, 'images', {});
  const ribbonType = get(data, 'ribbonType', '');
  const altSEOImg = get(data, 'altSEOImg', 'VieON');
  const descriptionSeo = get(data, 'seo.description', '');
  const hlsLinkPlay = get(data, 'linkPlay.hlsLinkPlay', '');
  const isTV = get(data, 'isTV', false);
  const isLiveTv = get(data, 'isLiveTv', false);
  const { src, defaultSrc } = setCardImage({
    images,
    ribbonType,
    isMasterBanner: true,
    isMobile
  });

  const setupPlayer = ({ player }: any) => {
    if (typeof onSetupPlayer === 'function') onSetupPlayer({ player });
  };

  return (
    <div className="backdrop absolute enable-mask layer-1 overflow" onClick={onClick}>
      <div className="backdrop__wrap relative ratio-16-9">
        <div className="backdrop__image size-square-full absolute on-click">
          <Image
            className="backdrop__image__hero size-square-full"
            src={src || ''}
            defaultSrc={defaultSrc}
            alt={altSEOImg}
            title={title}
          />
          <h2 style={{ color: '#111' }}>{title}</h2>
          <p style={{ color: '#111' }} dangerouslySetInnerHTML={{ __html: descriptionSeo }} />
        </div>
        {hlsLinkPlay && isActive && !isMobile && (
          <div className="backdrop__video size-square-full absolute">
            <MiniPlayer
              id={PLAYER_TYPE.MASTER_BANNER}
              className="backdrop__video__hero size-square-full"
              isMasterBanner
              linkPlay={hlsLinkPlay}
              contentId={id}
              setupPlayer={setupPlayer}
              onEnded={onPlayerEnded}
              onPlaying={onPlayerPlaying}
              onPlay={onPlayerPlay}
              onError={onPlayerError}
              muted={isTV && isLiveTv ? true : muted}
              noPauseOther={isTV && isLiveTv}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default Backdrop;
