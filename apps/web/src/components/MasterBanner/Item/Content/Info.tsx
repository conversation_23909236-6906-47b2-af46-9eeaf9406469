import React, { useMemo } from 'react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { BILLBOARD, EL_SIZE_CLASS, TAG_KEY, TVOD } from '@constants/constants';
import TopRankingTitle from './TopRankingTitle';
import GroupTags from './GroupTags';
import ShortDescription from './ShortDescription';
import Tags from '@/components/basic/Tags/Tags';
import { numberWithCommas } from '@/helpers/common';
import classNames from 'classnames';

const styles = {
  scaled: {
    opacity: 0,
    transitionDuration: '1.3s',
    transformOrigin: 'bottom left',
    transform: `scale(0.35, 0.35)`
  },
  rankingScaled: {
    transform: `scale(1) translate3d(0, 77%, 0)`,
    transitionDuration: '1.3s',
    transformOrigin: 'bottom left'
  },
  default: {
    opacity: 1,
    transitionDuration: '1.3s',
    transformOrigin: 'bottom left'
  }
};

const Info = ({ data, timer, scaleAnimation }: any) => {
  const shortDescription = useMemo(() => {
    const desc = get(data, 'shortDescription', '');
    const maxLength = 150;
    if (desc.length > maxLength) {
      const lastSpaceIndex = desc.lastIndexOf(' ', maxLength);
      return `${desc.substring(0, lastSpaceIndex)}...`;
    }
    return desc;
  }, [get(data, 'shortDescription', '')]);
  const rankingText = get(data, 'rankingText', '');
  const categoryTags = get(data, 'categoryTags', []);
  const styleInfo = useMemo(
    () =>
      scaleAnimation && timer === BILLBOARD.TITLE_CARD_TIMER
        ? styles.rankingScaled
        : styles.default,
    [scaleAnimation, timer]
  );
  const styleDescription = useMemo(
    () => (scaleAnimation && timer === BILLBOARD.TITLE_CARD_TIMER ? styles.scaled : styles.default),
    [scaleAnimation, timer]
  );

  const conditionShowTag = data?.isPremiumTVod && data?.tvod?.price && !data?.isEpisode;

  return (
    <div className={classNames('master-banner__content relative layer-1')} style={styleInfo}>
      <div className={rankingText && 'flex space-x-[8px] !w-full'}>
        <div className="flex items-center space-x-[8px]">
          {(conditionShowTag || data?.isSvodTvod) &&
            data?.tvod &&
            (data?.tvod?.benefitType === TVOD.USER_TYPE.NONE ||
              data?.tvod?.benefitType === TVOD.USER_TYPE.EXPIRED) && (
              <Tags
                tagKey={TAG_KEY.PRICE}
                price={numberWithCommas(
                  data?.tvod?.preOrder?.isPreOrdering
                    ? data?.tvod?.preOrder?.price
                    : data?.tvod?.price
                )}
                size={data?.isOriginal ? EL_SIZE_CLASS.LARGE : EL_SIZE_CLASS.MEDIUM}
              />
            )}
          {data?.isPremium && !data?.isLiveTv && !data?.isLiveStream && (
            <Tags isPremiumDisplay={data?.isPremiumDisplay} />
          )}
          {!isEmpty(categoryTags) && <GroupTags data={categoryTags} />}
        </div>
        {rankingText && <TopRankingTitle data={rankingText} />}
      </div>

      {shortDescription && <ShortDescription data={shortDescription} style={styleDescription} />}
    </div>
  );
};

export default Info;
