import React from 'react';
import Tags from '@components/basic/Tags/Tags';
import TagsGroup from '@components/basic/Tags/TagsGroup';
import { EL_PROPERTY, EL_SIZE_CLASS } from '@constants/constants';

const GroupTags = ({ data, fontStyle }: any) => {
  const tagList = (data || []).map((item: any, index: any) => {
    const tagClass: any = fontStyle ? fontStyle : '';
    return (
      <Tags
        key={index}
        subClass={tagClass}
        description={item.title || item.name}
        tagKey={item.tagKey}
        spClass={item.spClass}
        iClass={item.iClass || ''}
        size={EL_SIZE_CLASS.LARGE}
      />
    );
  });

  return <TagsGroup divider={EL_PROPERTY.DIVIDER_CIRCLE_V} tagList={tagList} />;
};

export default GroupTags;
