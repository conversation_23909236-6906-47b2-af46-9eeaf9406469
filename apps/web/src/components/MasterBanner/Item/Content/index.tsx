import React, { useMemo } from 'react';
import get from 'lodash/get';
import TriggerAction from '@components/trigger/TriggerAction';
import { CONTENT_TYPE, EL_THEME_CLASS, TVOD, EL_CLASS, EL_SIZE_CLASS } from '@constants/constants';
import { PLAYER_STATUS, PLAYER_TYPE } from '@constants/player';
import { formatTimeTVodString } from '@services/contentService';
import AgeTag from '@components/basic/Tags/AgeTag';
import dynamic from 'next/dynamic';
import { setLive } from '@models/epgItem';
import { setStartTimeLiveStream } from '@services/datetimeServices';
import classNames from 'classnames';
import Tooltip from '@components/Tooltip';
import TagLiveAndViewer from './TagLiveAndViewer';
import ImageTitle from './ImageTitle';
import Info from './Info';
import Style from '../../MasterBanner.module.scss';
import NewIcon from '@components/basic/Icon/NewIcon';

const Tags = dynamic(import('@components/basic/Tags/Tags'), { ssr: false });

const Content = ({
  data,
  config,
  playerStatus,
  timer,
  muted,
  onSaveProgressForVideoIntro,
  isActive,
  onClickControlPlayer,
  onClickMask,
  isMobile
}: any) => {
  const id = get(data, 'id', '');
  const imageTitle = get(data, 'images.titleCardLight', '');
  const altImg = get(data, 'altSEOImg', '');
  const shortDescription = get(data, 'shortDescription', '');
  const preOrderRemainTimeText = get(data, 'tvod.preOrderRemainTimeText', '');
  const benefitType = get(data, 'tvod.benefitType', 0);
  const isLiveEventTVod = get(data, 'tvod.isLiveEvent', false);
  const isLive = get(data, 'isLive', false);
  const isPremiere = get(data, 'isPremiere', false);
  const isComingSoon = get(data, 'isComingSoon', false);
  const hlsLinkPlay = get(data, 'linkPlay.hlsLinkPlay', '');
  const triggers = get(data, 'triggers', []);
  const expiredString = get(config, 'tVod.text.expiredString', '');
  const startTime = get(data, 'startTime', '');
  const type = get(data, 'type', '');
  const strTimeStandard = get(data, 'tvod.strTimeStandard', '');
  const warningTag = get(data, 'warningTag', '');
  const remainTimeText = useMemo(() => {
    if (!strTimeStandard || isLiveEventTVod) return '';
    return formatTimeTVodString({ strConfig: expiredString, strTime: strTimeStandard });
  }, [strTimeStandard, expiredString, isLiveEventTVod]);
  const playerBtnClass = useMemo(() => {
    let tempClass = 'vie-volume-mute-rc';
    if (playerStatus === PLAYER_STATUS.ENDED) tempClass = 'vie-refresh';
    else if (muted) tempClass = 'vie-volume-mute-rc';
    else tempClass = 'vie-volume-up-rc animate-fade-in';
    return tempClass;
  }, [playerStatus, muted]);

  const toolTipContent = useMemo(() => {
    let tempToolTipContent = '';
    if (playerStatus === PLAYER_STATUS.ENDED) tempToolTipContent = 'Xem lại';
    else if (muted) tempToolTipContent = 'Bật tiếng';
    else tempToolTipContent = 'Tắt tiếng';
    return tempToolTipContent;
  }, [playerStatus, muted]);

  const startTextLive = useMemo(() => {
    if (startTime) {
      if (type === CONTENT_TYPE.EPG || type === CONTENT_TYPE.LIVE_TV) {
        const { startText } = setLive(startTime);
        return startText;
      }
      if (type === CONTENT_TYPE.LIVESTREAM) {
        return setStartTimeLiveStream(startTime, isLive, isPremiere);
      }
    }
    return '';
  }, [startTime, isLive, isPremiere]);

  const onCallBackDetail = () => {
    if (typeof onSaveProgressForVideoIntro === 'function') onSaveProgressForVideoIntro();
  };

  return (
    <>
      <div className="master-banner__left w-full md:w-[calc(100%-3.25rem)] layer-2">
        {!isComingSoon && (isLive || isPremiere) && <TagLiveAndViewer isPremiere={isPremiere} />}
        {imageTitle && (
          <ImageTitle
            bannerId={id}
            timer={timer}
            scaleAnimation={playerStatus === PLAYER_STATUS.PLAYING}
            emptyDescription={!shortDescription}
            onClick={onClickMask}
            imgSrc={imageTitle}
            altImg={altImg}
          />
        )}
        <Info
          config={config}
          data={data}
          timer={timer}
          scaleAnimation={playerStatus === PLAYER_STATUS.PLAYING}
        />
        {(startTextLive || remainTimeText) && (
          <div className="flex">
            {startTextLive && type === CONTENT_TYPE.LIVESTREAM && (
              <Tags
                description={startTextLive}
                txtClass="!text-[.75rem] lg:!text-[1.125rem] !font-medium"
                iconName="vie-clock-o-rc-medium !text-[.8125rem] lg:!text-[1.125rem]"
                size={EL_SIZE_CLASS.LARGE}
                theme={EL_THEME_CLASS.GREEN_SUBTLE}
                isNewIcon
              />
            )}
          </div>
        )}
        <div className={classNames('button-group relative layer-2 space-x-2')}>
          {triggers.map((item: any, index: any) => (
            <TriggerAction
              contentData={data}
              key={`${id}-${index}-${item?.key}`}
              index={index}
              playerId={PLAYER_TYPE.MASTER_BANNER}
              triggerItem={item}
              onCallBackDetail={onCallBackDetail}
              customSpanClass="block w-[30px] h-[30px]"
            />
          ))}
        </div>
        {remainTimeText && (
          <Tags
            description={remainTimeText}
            txtClass="!text-[.75rem] lg:!text-[1.125rem] !font-medium"
            size={EL_SIZE_CLASS.LARGE}
            iconName="vie-clock-o-rc-medium !text-[.8125rem] lg:!text-[1.125rem]"
            theme={EL_THEME_CLASS.YELLOW_SUBTLE}
            subClass={'!justify-start'}
            isNewIcon
          />
        )}
        {preOrderRemainTimeText &&
          benefitType !== TVOD.USER_TYPE.RENTED &&
          benefitType !== TVOD.USER_TYPE.WATCHED && (
            <div
              className="text text-white text-10 text-small-up-10 text-large-up-14"
              dangerouslySetInnerHTML={{ __html: preOrderRemainTimeText }}
            />
          )}
      </div>
      {hlsLinkPlay &&
        isActive &&
        !isMobile &&
        (playerStatus === PLAYER_STATUS.PLAYING || playerStatus === PLAYER_STATUS.ENDED) && (
          <div className={`master-banner__right layer-1 ${Style.spaceY}`}>
            {warningTag && playerStatus === PLAYER_STATUS.PLAYING && (
              <AgeTag text={warningTag} customClass="ageForIntro" />
            )}
            <Tooltip
              title={toolTipContent}
              placement="top"
              triggerEvent={isMobile ? 'dismiss' : 'hover'}
              className="animate-fade-in text-start p-2 max-w-[14rem] min-w-[2.75rem]"
              size={EL_CLASS.SMALL}
              arrowPosition="top-end"
              sizeX={12}
              isDarkBackground
            >
              <button
                className="button button--geometry-circle !bg-black/50 !text-white hover:!text-vo-green !text-[1.125rem] xl:!text-[1.375rem] relative"
                onClick={onClickControlPlayer}
                type="button"
              >
                <NewIcon iconName={playerBtnClass} />
              </button>
            </Tooltip>
          </div>
        )}
    </>
  );
};

export default Content;
