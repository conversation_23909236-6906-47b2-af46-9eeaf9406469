import React from 'react';
import Tags from '@components/basic/Tags/Tags';
import { EL_SIZE_CLASS, TAG_KEY } from '@constants/constants';

const TagLiveAndViewer = ({ isPremiere }: any) => (
  <div className="tags-group horizontal shrink">
    <Tags
      title={isPremiere ? TAG_KEY.PREMIERE : ''}
      tagKey={TAG_KEY.LIVE}
      size={EL_SIZE_CLASS.LARGE}
      subClass="uppercase"
    />
  </div>
);
export default TagLiveAndViewer;
