import React, { useState, useEffect } from 'react';
import Image from '@components/basic/Image/Image';
import { BILLBOARD } from '@constants/constants';

const styles = {
  scaledNoDes: {
    transform: 'scale3d(0.6,0.6,0.6) translate3d(0px, 30%, 0px)',
    transitionDuration: '1.3s',
    transitionDelay: '0s',
    transformOrigin: 'bottom left'
  },
  scaled: {
    transform: 'scale3d(0.6,0.6,0.6) translate3d(0px, 30%, 0px)',
    transitionDuration: '1.3s',
    transitionDelay: '0s',
    transformOrigin: 'bottom left'
  },
  default: {
    transform: ' scale(1) translate3d(0, 0, 0)',
    transitionDuration: '1.3s',
    transitionDelay: '0s',
    transformOrigin: 'bottom left'
  }
};

const ImageTitle = ({
  bannerId,
  imgSrc,
  altImg,
  onClick,
  timer,
  scaleAnimation,
  emptyDescription
}: any) => {
  const [style, setStyle] = useState<any>(null);

  useEffect(() => {
    setStyle(null);
  }, [bannerId]);

  useEffect(() => {
    if (timer === BILLBOARD.TITLE_CARD_TIMER && scaleAnimation) {
      if (emptyDescription) {
        setStyle(styles.scaledNoDes);
      } else {
        setStyle(styles.scaled);
      }
    } else {
      setStyle(styles.default);
    }
  }, [timer, scaleAnimation, emptyDescription]);

  return (
    <div className="master-banner__title-hero" onClick={onClick} style={style}>
      <Image src={imgSrc} defaultNone alt={altImg} title={altImg} />
    </div>
  );
};

export default ImageTitle;
