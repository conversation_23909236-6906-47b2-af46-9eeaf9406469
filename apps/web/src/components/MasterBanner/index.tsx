import React, { useEffect, useMemo, useRef, useState } from 'react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { useVieRouter } from '@customHook';
import { useDispatch, useSelector } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import SwiperCore, { Autoplay, EffectFade, Navigation, Pagination } from 'swiper';
import { openPopup } from '@actions/popup';
import { setIsMasterBanner } from '@actions/page';
import { POPUP } from '@constants/constants';
import LocalStorage from '@config/LocalStorage';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import dynamic from 'next/dynamic';
import { useInView } from 'react-intersection-observer';
import Item from './Item';
import IconArrowLeft from '../Icons/IconArrowLeft';

const AdsGamDynamic = dynamic(
  () =>
    import('@components/OutstreamAds/GamAdsMasterBanner/GamAdsMasterBanner').then(
      (module) => module.GamAdsMasterBanner
    ),
  {
    ssr: false
  }
);
SwiperCore.use([Pagination, Navigation, EffectFade, Autoplay]);

const MasterBanner = ({ data }: any) => {
  const swiperRef = useRef<any>(null);
  const router = useVieRouter();
  const dispatch = useDispatch();
  const { webConfig, isMobile, configPersonalizationFlow } = useSelector(
    (state: any) => state?.App || {}
  );
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const adsMasterBanner = useSelector((state: any) => state?.App?.outStreamAds?.masterBanner || []);
  const [ref, inView] = useInView({
    threshold: 0.5,
    triggerOnce: true
  });
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile);
  const { activeMenu, activeSubMenu } = useSelector((state: any) => state?.Menu || {});
  const { popupName, previewCard, cardHover } = useSelector((state: any) => state?.Popup || {});
  const needPausePlayer = useMemo(
    () =>
      previewCard?.expand || cardHover?.id || !!popupName || (profile?.id && !currentProfile?.id),
    [previewCard, popupName, cardHover]
  );
  const [bannerActiveIndex, setBannerActiveIndex] = useState(0);
  const listItems = get(data, 'data', []);
  const prevTitleBanner = useMemo(() => {
    if (bannerActiveIndex === 0) {
      return get(listItems, `[${listItems.length - 1}].title`, '');
    }
    return get(listItems, `[${bannerActiveIndex - 1}].title`, '') || 'Nội dung quảng cáo';
  }, [listItems, bannerActiveIndex]);
  const nextTitleBanner = useMemo(() => {
    if (bannerActiveIndex === listItems.length - 1) {
      return get(listItems, '[0].title', '');
    }
    return get(listItems, `[${bannerActiveIndex + 1}].title`, '') || 'Nội dung quảng cáo';
  }, [listItems, bannerActiveIndex]);

  useEffect(() => {
    if (
      !isMobile &&
      listItems &&
      router.pathname === '/' &&
      !profile?.isPremium &&
      !isKid &&
      !isGlobal &&
      adsMasterBanner.length > 0
    ) {
      for (const item of adsMasterBanner) {
        // console.log('adsMasterBanner', adsMasterBanner);
        const { index } = item;
        if (listItems?.findIndex((d: any) => d.id === item.id) === -1) {
          if (index && index <= listItems?.length) {
            listItems?.splice(index, 0, item);
          } else {
            listItems?.unshift({
              ...item,
              index: 0
            });
          }
        }
      }
      // console.log(listItems);
    }
  }, [listItems, adsMasterBanner]);

  useEffect(() => {
    dispatch(setIsMasterBanner(true));
    return () => {
      dispatch(setIsMasterBanner(false));
    };
  }, []);

  useEffect(() => {
    const showPopupTrial = ConfigLocalStorage.get(LocalStorage.TRIAL_APP);
    if (!profile?.id && !showPopupTrial && configPersonalizationFlow?.guestFlow) {
      dispatch(openPopup({ name: POPUP.NAME.FIRST_LOGIN }));
    }
  }, [profile, configPersonalizationFlow]);

  useEffect(() => {
    if (listItems.length > 0 && swiperRef.current) {
      if (isMobile && typeof swiperRef.current.slideToLoop === 'function') {
        swiperRef.current.slideToLoop(0, 0);
      } else if (typeof swiperRef.current.slideTo === 'function') {
        swiperRef.current.slideTo(0, 0);
        setBannerActiveIndex(0);
      }
    }
  }, [listItems, isMobile]);

  const onInitSlideBanner = (swiper: any) => {
    swiperRef.current = swiper;
  };

  const handleClickNavigate = (typeBtn: any) => {
    if (!isEmpty(swiperRef.current) && listItems.length > 1) {
      if (typeBtn === 'next') {
        if (bannerActiveIndex === listItems.length - 1) {
          setBannerActiveIndex(0);
          swiperRef.current.slideTo(0, 0);
        } else {
          setBannerActiveIndex(bannerActiveIndex + 1);
        }
      } else if (typeBtn === 'prev') {
        if (bannerActiveIndex === 0) {
          setBannerActiveIndex(listItems.length - 1);
          swiperRef.current.slideTo(listItems.length - 1, 0);
        } else {
          setBannerActiveIndex(bannerActiveIndex - 1);
        }
      }
    }
  };

  const onNextSlide = () => {
    if (!isEmpty(swiperRef.current) && listItems.length > 1) {
      if (swiperRef.current.isEnd) {
        swiperRef.current.slideTo(0, 0);
        setBannerActiveIndex(0);
      } else {
        swiperRef.current.slideNext();
        setBannerActiveIndex(swiperRef.current.activeIndex);
      }
    }
  };

  const handleStatusLoadAds = (statusLoadAds: any) => {
    if (!statusLoadAds) {
      const findIndex = listItems.findIndex((e: any) => e.index === bannerActiveIndex && e.path);
      if (findIndex > -1) {
        listItems.splice(findIndex, 1);
        handleClickNavigate('next');
      }
    }
  };
  if (listItems.length === 0) return null;

  return (
    <Swiper
      className={`slider slider--master swiper-container relative non-hover${
        isMobile ? ' overflow pb-0' : ''
      }`}
      id="sliderMasterBanner"
      slidesPerView={1}
      initialSlide={0}
      allowTouchMove={isMobile && listItems?.length > 1}
      effect={isMobile ? 'slide' : 'fade'}
      // loop={isMobile && listItems.length > 1}
      autoplay={
        isMobile
          ? {
              delay: 3000,
              disableOnInteraction: false
            }
          : false
      }
      navigation={
        isMobile
          ? false
          : {
              disabledClass: 'slider-navigate-disabled-non-enabled',
              lockClass: 'slider-navigate-lock',
              nextEl: '#sliderMasterBanner .slider-navigate.slider-nav-next',
              prevEl: '#sliderMasterBanner .slider-navigate.slider-nav-prev'
            }
      }
      pagination={{
        el: '#sliderMasterBanner .swiper-pagination',
        bulletClass: 'swiper-pagination-bullet rectangle round'
      }}
      onSwiper={onInitSlideBanner}
    >
      {listItems.map((item: any, i: any) =>
        item.path &&
        router.pathname === '/' &&
        !isMobile &&
        !profile?.isPremium &&
        !isKid &&
        !isGlobal ? (
          <SwiperSlide key={`master-banner-${item.id}-${i}`}>
            <div ref={ref}>
              {inView && (
                <AdsGamDynamic
                  onNextSlide={onNextSlide}
                  data={item}
                  isActive={i === bannerActiveIndex}
                  bannerActiveIndex={bannerActiveIndex}
                  handleStatusLoadAds={handleStatusLoadAds}
                />
              )}
            </div>
          </SwiperSlide>
        ) : (
          <SwiperSlide key={`master-banner-${item.id}-${i}`}>
            {({ isActive }: any) => (
              <Item
                needPausePlayer={needPausePlayer}
                data={item}
                isActive={isActive}
                isMobile={isMobile}
                config={webConfig}
                router={router}
                dispatch={dispatch}
                profile={profile}
                currentProfile={currentProfile}
                activeMenu={activeMenu}
                activeSubMenu={activeSubMenu}
                onNextSlide={onNextSlide}
              />
            )}
          </SwiperSlide>
        )
      )}
      <div
        className={`slider-navigate-group align-right horizontal canal-v layer-1 relative${
          listItems.length <= 1 ? ' hide' : ''
        }`}
      >
        <div className="swiper-pagination rectangle child-center swiper-pagination-horizontal" />
        {!isMobile && (
          <div
            className="slider-navigate slider-nav-prev size-10 border border-solid border-white hover:border-vo-green !text-white hover:!text-vo-green rounded-full relative text-[1.25rem]"
            onClick={() => handleClickNavigate('prev')}
          >
            <span>
              <IconArrowLeft />
            </span>
            {prevTitleBanner && (
              <div className="tooltip tooltip-arrow-available for-dark round flown-top-left animate-fade-in size-mw-280">
                <div className="tooltip__wrap flex-box relative shadow-h-v4-b4-a25">
                  <span className="text-white text-14">{prevTitleBanner}</span>
                </div>
              </div>
            )}
          </div>
        )}
        {!isMobile && (
          <div
            className="slider-navigate slider-nav-next size-10 border border-solid border-white hover:border-vo-green !text-white hover:!text-vo-green rounded-full relative text-[1.25rem]"
            onClick={() => handleClickNavigate('next')}
          >
            <span className="rotate-180">
              <IconArrowLeft />
            </span>
            {nextTitleBanner && (
              <div className="tooltip tooltip-arrow-available for-dark round flown-top-left animate-fade-in size-mw-280">
                <div className="tooltip__wrap flex-box relative shadow-h-v4-b4-a25">
                  <span className="text-white text-14">{nextTitleBanner}</span>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </Swiper>
  );
};

export default React.memo(MasterBanner);
