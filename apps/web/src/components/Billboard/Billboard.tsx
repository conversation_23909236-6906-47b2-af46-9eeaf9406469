import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import findIndex from 'lodash/findIndex';
import Image from '@components/basic/Image/Image';
import {
  BILLBOARD,
  CONTENT_TYPE,
  EL_CLASS,
  EL_SIZE_CLASS,
  EL_THEME_CLASS,
  PAGE,
  POSITION,
  TAG_KEY,
  TOAST,
  TRIGGER_KEY
} from '@constants/constants';
import { TEXT } from '@constants/text';
import { setToast } from '@actions/app';
import { PLAYER_STATUS } from '@constants/player';
import LocalStorage from '@config/LocalStorage';
import { formatTimeTVodString } from '@services/contentService';
import { numberWithCommas, parseRentType } from '@helpers/common';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import AgeTag from '@components/basic/Tags/AgeTag';
import dynamic from 'next/dynamic';
import { setStartTimeLiveStream } from '@services/datetimeServices';
import { setLive } from '@models/epgItem';
import classNames from 'classnames';
import Tooltip from '@components/Tooltip';
import CardImage from '../basic/Card/CardImage';
import Button from '../basic/Buttons/Button';
import BillboardInfoOriginal from './BillboardInfoOriginal';
import MiniPlayer from '../basic/MiniPlayer/MiniPlayer';
import BillboardTitleCard from './BillboardTitleCard';
import BillboardInfo from './BillboardInfo';
import TriggerAction from '../trigger/TriggerAction';
import Style from './Billboard.module.scss';
import groupStyles from '../../styles/Group.module.scss';
import { findItemInRibbonById } from '@/helpers/utils';

const Tags = dynamic(import('@components/basic/Tags/Tags'), { ssr: false });

const Billboard = (props: any) => {
  const timeInterval = useRef<any>(null);
  const playerRef = useRef<any>(null);
  const router = useVieRouter();
  const dispatch = useDispatch();
  const {
    billboardData,
    className,
    callbackOnDetail,
    onSetupPlayer,
    isCardDetail,
    playerId,
    onClickBanner,
    noPlayer,
    cardData,
    fadeIn,
    tipDataItem,
    isVodDetail,
    isCollectionBanner,
    canClickBillboard,
    searchContents,
    cardDataEpisode,
    isCardHover,
    isEndScreenVod,
    onBackToPlayer
  } = props || {};
  const isMuted = ConfigLocalStorage.get(LocalStorage.TRAILER_MUTED);
  const [playerStatus, setPlayerStatus] = useState(PLAYER_STATUS.ERROR);
  const [muted, setMuted] = useState(!!isMuted);
  const [timer, setTimer] = useState(1);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const previewCard = useSelector((state: any) => state?.Popup?.previewCard);
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const config = useSelector((state: any) => state?.App?.webConfig);
  const expiredString = get(config, 'tVod.text.expiredString', '');
  const { expand } = previewCard || {};
  const timerRef = useRef<any>(null);
  const [isPopupDetail, setIsPopupDetail] = useState(false);
  const { ribbonData } = useSelector((state: any) => state?.Page || {});
  //this vidItem used to tracking for all content including not active content which has api tvod detail dead
  const vidItem = findItemInRibbonById(ribbonData, billboardData?.id);

  const {
    images,
    ribbonType,
    altSEOImg,
    isMain,
    isMasterBanner,
    linkPlay,
    isOriginal,
    title,
    seo,
    isLive,
    isLiveTv,
    isTV,
    ribbonOrder,
    isViewCollection,
    startTime,
    type,
    warningTag,
    isPremiere
  } = billboardData || cardData || {};
  const triggers = useMemo(() => {
    let arrTriggers = [];
    if (!isEmpty(billboardData?.triggers)) {
      arrTriggers = billboardData?.triggers;
    }
    if (!isEmpty(cardDataEpisode?.triggers)) {
      arrTriggers = cardDataEpisode?.triggers;
    }
    if (isVodDetail && findIndex(arrTriggers, ['key', TRIGGER_KEY.MY_LIST]) === -1) {
      arrTriggers.push({ key: TRIGGER_KEY.MY_LIST });
    }
    if (isViewCollection) {
      arrTriggers = [{ key: TRIGGER_KEY.DETAIL }];
    }
    return arrTriggers;
  }, [isVodDetail, cardDataEpisode?.triggers, billboardData?.triggers, isViewCollection]);
  const tvod = billboardData?.tvod || cardData?.tvod || cardDataEpisode?.tvod;
  const noPauseOther = isTV && isLiveTv;
  const { strTimeStandard, preOrderRemainTimeText } = tvod || {};
  const remainTimeText = useMemo(() => {
    if (!tvod) return '';

    const { isRented, isExpired } = parseRentType(tvod);

    if (!isRented || isExpired) {
      return '';
    } else {
      if (!strTimeStandard) return '';
      return formatTimeTVodString({ strConfig: expiredString, strTime: strTimeStandard });
    }
  }, [strTimeStandard, expiredString, tvod]);

  const buttonGroupClass = useMemo(() => {
    let tempClass = 'button-group';
    if (!expand && !isMasterBanner) {
      if (triggers?.length === 3) {
        // if (isCardHover) {
        //   tempClass += ` ${groupStyles.childAuto}`;
        // } else tempClass += ' child-3';
        tempClass += ' space-x-2 [&>*]:!w-[calc((1/3*100%)-((16/3)*1px))]';
      } else tempClass += ` ${groupStyles.childAuto}`;
    } else tempClass += ' space-x-2';
    return tempClass;
  }, [expand, isMasterBanner, triggers]);

  const startTextLive = useMemo(() => {
    if (startTime) {
      if (type === CONTENT_TYPE.EPG || type === CONTENT_TYPE.LIVE_TV) {
        const { startText } = setLive(startTime);
        return startText;
      }
      if (type === CONTENT_TYPE.LIVESTREAM) {
        return setStartTimeLiveStream(startTime, isLive, isPremiere);
      }
    }
    return '';
  }, [startTime, type, isLive, isPremiere]);

  useEffect(() => {
    const muteTrailer = ConfigLocalStorage.get(LocalStorage.TRAILER_MUTED) || false;
    setMuted(!!muteTrailer);
    return () => {
      clearInterval(timeInterval.current);
    };
  }, []);

  useEffect(() => {
    window.addEventListener('online', handleIsOnline);
    window.addEventListener('offline', handleIsOffline);

    return () => {
      window.removeEventListener('online', handleIsOnline);
      window.removeEventListener('offline', handleIsOffline);
    };
  }, []);

  const handleIsOffline = () => {
    dispatch(
      setToast({
        message: (
          <p>
            Mất kết nối mạng!
            <br />
            Vui lòng kiểm tra đường truyền
          </p>
        ),
        kind: TOAST.KIND.WARNING,
        position: POSITION.TOP_RIGHT
      })
    );

    // main
    if (!playerRef.current) {
      const timer = setTimeout(() => {
        setIsOnline(false);
        return () => clearTimeout(timer);
      }, 1000);
      return (timerRef.current = timer);
    }
    let waitingTime = 1000;
    if (!playerRef.current?.buffered) {
      waitingTime = 1000;
    } else {
      const bufferedRanges = playerRef.current?.buffered;
      waitingTime = Math.max(bufferedRanges.end(0) - bufferedRanges.start(0), 0) * 1000;
    }

    const timer = setTimeout(() => {
      playerRef.current.pause();

      const timerToast = setTimeout(() => {
        setIsOnline(false);
      }, 500);

      return () => clearTimeout(timerToast);
    }, waitingTime);
    return (timerRef.current = timer);
  };

  const handleIsOnline = () => {
    setIsOnline(true);
    if (playerRef.current) {
      playerRef.current.play();
    }
    dispatch(
      setToast({
        message: TEXT.ONLINE,
        kind: TOAST.KIND.WARNING,
        position: POSITION.TOP_RIGHT
      })
    );
  };

  useEffect(() => {
    if (!isOnline && router?.asPath.includes('?vid=')) {
      setIsPopupDetail(true);
    }
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  }, [isOnline, router]);

  useEffect(() => {
    clearInterval(timeInterval.current);
    setPlayerStatus(PLAYER_STATUS.ERROR);
    setTimer(0);
  }, [billboardData?.id]);

  useEffect(() => {
    if (playerStatus === PLAYER_STATUS.PLAYING) {
      handleBillboardTimer();
    }
  }, [playerStatus]);

  useEffect(() => {
    if (timer >= BILLBOARD.TITLE_CARD_TIMER) {
      clearInterval(timeInterval.current);
    }
  }, [timer]);

  const handleBillboardTimer = () => {
    clearInterval(timeInterval.current);
    timeInterval.current = setInterval(() => {
      setTimer((a) => a + 1);
      if (timer >= BILLBOARD.TITLE_CARD_TIMER) {
        clearInterval(timeInterval.current);
      }
    }, 1000);
  };

  const onClickControl = (e: any) => {
    e.preventDefault();
    if (!playerRef.current) return;
    if (playerStatus === PLAYER_STATUS.PLAYING) {
      playerRef.current.muted = !muted;
      setMuted(!muted);
      ConfigLocalStorage.set(LocalStorage.TRAILER_MUTED, !muted || '');
    } else if (playerStatus === PLAYER_STATUS.ENDED) {
      playerRef.current.play();
    }
  };

  const setupPlayer = ({ player }: any) => {
    playerRef.current = player;
    if (onSetupPlayer) {
      onSetupPlayer({ player });
    }
  };
  const onEnded = () => {
    setPlayerStatus(PLAYER_STATUS.ENDED);
  };
  const onPlay = ({ isMuted }: any) => {
    setTimer(0);
    if (playerRef.current && !playerRef.current?.paused) setMuted(!!isMuted);
  };
  const onPlaying = () => {
    setPlayerStatus(PLAYER_STATUS.PLAYING);
  };
  const onError = () => {
    setPlayerStatus(PLAYER_STATUS.ERROR);
  };

  const onCallBackDetail = () => {
    if (typeof callbackOnDetail === 'function') {
      callbackOnDetail();
    }
  };

  const canClickBanner =
    playerStatus === PLAYER_STATUS.ENDED ||
    playerStatus === PLAYER_STATUS.ERROR ||
    !linkPlay?.hlsLinkPlay;

  const clickMask = () => {
    if ((canClickBanner || canClickBillboard) && typeof onClickBanner === 'function') {
      onClickBanner();
    }
  };

  let ctrIconClass = '';
  if (playerStatus === PLAYER_STATUS.ENDED) ctrIconClass = 'vie-refresh';
  else if (muted) ctrIconClass = 'vie-volume-mute-rc';
  else ctrIconClass = 'vie-volume-up-rc animate-fade-in';
  const toolTipContent = useMemo(() => {
    let tempToolTipContent = '';
    if (playerStatus === PLAYER_STATUS.ENDED) tempToolTipContent = 'Xem lại';
    else if (muted) tempToolTipContent = 'Bật tiếng';
    else tempToolTipContent = 'Tắt tiếng';
    return tempToolTipContent;
  }, [playerStatus, muted]);
  let ratioClass = ' ratio-16-9';
  if (isOriginal) ratioClass = ' ratio-1-2';
  const isHoverItems = !isMasterBanner && !expand;
  const availableClick =
    (canClickBanner || canClickBillboard) && !isCollectionBanner ? ' on-click' : '';
  const billboardBackdropClass = `billboard__backdrop absolute${
    isCollectionBanner ? '' : ratioClass
  }${availableClick}`;
  const titleDetailPage = router?.pathname === PAGE.VOD ? title : '';
  let billboardContentClass = 'billboard__content__wrap relative';
  const billboardTitleRef = useRef<any>(null);
  if (billboardTitleRef.current) {
    billboardContentClass = 'billboard__content__wrap relative';
  } else {
    billboardContentClass += ' title-not-available';
  }

  return (
    <div className={`billboard ${className}`}>
      <div className="billboard-container">
        <div
          className={`billboard__pane relative${
            isVodDetail
              ? ' ratio-16-9'
              : isMasterBanner
              ? ' ratio-variant'
              : isCollectionBanner
              ? ''
              : ratioClass
          }`}
        >
          <div role="presentation" className={billboardBackdropClass} onClick={clickMask}>
            <div
              className={classNames(
                'billboard__image absolute',
                isHoverItems ? '!bg-vo-dim-gray-900 !overflow-visible' : ''
              )}
            >
              <CardImage
                images={images}
                expand={expand}
                className="billboard__image__hero"
                ribbonType={ribbonType}
                alt={altSEOImg}
                // title={title}
                isMain={isMain}
                fadeIn={fadeIn}
                isMasterBanner={isMasterBanner}
                isMobile={isMobile}
                isCollectionBanner={isCollectionBanner}
                isCardDetail={isCardDetail}
                notLazy
              />
              {isMasterBanner && !titleDetailPage && (
                <>
                  <h2 style={{ color: '#111' }}>{title}</h2>
                  <p
                    style={{ color: '#111' }}
                    dangerouslySetInnerHTML={{ __html: seo?.description || '' }}
                  />
                </>
              )}
            </div>
            {linkPlay?.hlsLinkPlay && !noPlayer && !isMobile && (
              <div className="billboard__video absolute">
                <MiniPlayer
                  id={playerId}
                  className="billboard__video__hero"
                  notCheckScroll={!billboardData?.isMasterBanner}
                  linkPlay={isOriginal ? linkPlay?.posterLinkPlay : linkPlay?.hlsLinkPlay}
                  contentId={billboardData?.id}
                  setupPlayer={setupPlayer}
                  onEnded={onEnded}
                  onPlaying={onPlaying}
                  onPlay={onPlay}
                  isMasterBanner={!!billboardData?.isMasterBanner}
                  onError={onError}
                  muted={isTV && isLiveTv ? true : muted}
                  noPauseOther={noPauseOther}
                />
                <div
                  className={`${
                    isOnline
                      ? 'hidden z-[-3]'
                      : 'block z-50 layer-8 w-full h-full relative bg-black'
                  } `}
                >
                  <div
                    className={` flex items-center flex-col-reverse justify-center w-full h-full space-y-[12px]`}
                  >
                    <div className="flex flex-col flex-[3/2] text-white space-y-[12px]">
                      <p className="text-center !text-[20px] md:!text-[28px] leading-[10px] md:leading-[40px] font-[500]">
                        Không có kết nối mạng
                      </p>
                      <p
                        className={`text-center !text-[16px] md:!text-[24px] font-[400] md:leading-[33.6px] `}
                      >
                        Vui lòng kiểm tra lại kết nối mạng và thử lại
                      </p>
                    </div>
                    <div className="max-w-[320px] max-h-[320px] w-[27%] h-[47.5%]">
                      <Image
                        src="/assets/images/lost_connect.svg"
                        width="600"
                        height="600"
                        className="aspect-[600/600]"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div
            className={classNames(
              'billboard__content absolute',
              isHoverItems ? '!px-4 flex' : '',
              !isPopupDetail ? '' : 'hidden'
            )}
            id="billboard_Banner"
          >
            <div className={billboardContentClass}>
              <div className="billboard__info space-y-4">
                {!billboardData?.isViewCollection && !isVodDetail && (
                  <BillboardTitleCard
                    linkPlay={isOriginal ? linkPlay?.posterLinkPlay : linkPlay?.hlsLinkPlay}
                    billboardData={billboardData}
                    scaleAnimation={playerStatus === PLAYER_STATUS.PLAYING}
                    expand={expand}
                    timer={timer}
                    isMasterBanner={isMasterBanner}
                    tipDataItem={tipDataItem}
                    forwardRef={billboardTitleRef}
                    onClickBanner={clickMask}
                  />
                )}
                {!isVodDetail && (
                  <BillboardInfo
                    billboardData={billboardData}
                    scaleAnimation={playerStatus === PLAYER_STATUS.PLAYING}
                    expand={expand}
                    timer={timer}
                    titleDetailPage={titleDetailPage}
                    remainTimeText={remainTimeText}
                    isMobile={isMobile}
                  />
                )}
                <div className="group vertical space-y-2 md:space-y-4">
                  {startTextLive && isMasterBanner && type === CONTENT_TYPE.LIVESTREAM && (
                    <Tags
                      tagKey={TAG_KEY.REMIND}
                      description={startTextLive}
                      txtClass="text-large-up-18 padding-small-up-left-4 padding-large-up-left-8 text-medium text-green"
                      iClass="icon--small icon--tiny-xs text-green"
                    />
                  )}
                  {remainTimeText &&
                    isMasterBanner &&
                    !(startTextLive && isMasterBanner && type === CONTENT_TYPE.LIVESTREAM) && (
                      <Tags
                        tagKey={TAG_KEY.REMIND}
                        description={remainTimeText}
                        txtClass="text-large-up-18 padding-small-up-left-4 padding-large-up-left-8 text-medium text-yellow91"
                        iClass="icon--small icon--tiny-xs text-yellow91"
                      />
                    )}
                  {!isMobile && (
                    <div className={buttonGroupClass}>
                      {(triggers || []).map((tgg: any, index: any) => {
                        if (
                          tgg?.key === TRIGGER_KEY.DETAIL &&
                          (isVodDetail || (expand && !isMasterBanner))
                        ) {
                          return null;
                        }
                        return (
                          <TriggerAction
                            key={index}
                            index={isViewCollection ? null : index}
                            expand={expand}
                            playerId={playerId}
                            triggerItem={tgg}
                            triggers={triggers}
                            onCallBackDetail={onCallBackDetail}
                            searchContents={searchContents}
                            cardData={cardData}
                            contentData={billboardData}
                            ribbonOrder={ribbonOrder}
                            titleDetailPage={titleDetailPage}
                            isVodDetail={isVodDetail}
                            cardDataEpisode={cardDataEpisode}
                            isCardHover={isCardHover}
                            isEndScreenVod={isEndScreenVod}
                            onBackToPlayer={onBackToPlayer}
                            isCardDetail={isCardDetail}
                            customText="truncate max-w-full"
                            vidItem={vidItem}
                          />
                        );
                      })}
                    </div>
                  )}
                  {isMasterBanner && preOrderRemainTimeText && (
                    <div
                      className="text text-white text-10 text-small-up-10 text-large-up-14 padding-medium-down-top-6 padding-medium-up-top-8"
                      dangerouslySetInnerHTML={{ __html: preOrderRemainTimeText }}
                    />
                  )}
                  {remainTimeText && !isMasterBanner && expand && (
                    <div className="flex items-center">
                      <Tags
                        description={numberWithCommas(remainTimeText)}
                        theme={EL_THEME_CLASS.YELLOW_SUBTLE}
                        iconName="vie-clock-o-rc-medium"
                        size={EL_SIZE_CLASS.LARGE_SUBTLE}
                        isNewIcon
                      />
                    </div>
                  )}
                </div>
                <BillboardInfoOriginal billboardData={billboardData} tipDataItem={tipDataItem} />
              </div>
              {!noPlayer && (
                <div
                  className={classNames(
                    'billboard__controls',
                    Style.spaceY,
                    isHoverItems ? (isOriginal ? '!bottom-[112%]' : '!bottom-12') : ''
                  )}
                >
                  {playerStatus === PLAYER_STATUS.PLAYING && warningTag && !isCardHover && (
                    <AgeTag text={warningTag} customClass="ageForIntro" />
                  )}
                  {((playerStatus === PLAYER_STATUS.PLAYING && !isHoverItems) ||
                    (playerStatus === PLAYER_STATUS.ENDED && !isHoverItems) ||
                    (timer < 5 && isHoverItems) ||
                    isMasterBanner) &&
                    !isMobile &&
                    (linkPlay?.posterLinkPlay || linkPlay?.hlsLinkPlay) && (
                      <Tooltip
                        title={toolTipContent}
                        placement="top"
                        triggerEvent={isMobile ? 'dismiss' : 'hover'}
                        className="animate-fade-in text-start p-2 max-w-[14rem] min-w-[2.75rem]"
                        size={EL_CLASS.SMALL}
                        arrowPosition="top-end"
                        sizeX={12}
                        isDarkBackground
                      >
                        <Button
                          className="button button--geometry-circle !bg-black/50 !text-white hover:!text-vo-green !text-[1.125rem] xl:!text-[1.375rem]"
                          onClick={onClickControl}
                          iconName={ctrIconClass}
                        />
                      </Tooltip>
                    )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(Billboard);
