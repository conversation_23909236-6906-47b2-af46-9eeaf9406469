.container {
  @apply text-sm leading-[140%] font-normal text-[#ccc] cursor-pointer;
  @apply relative flex;
  .input {
    @apply m-0;
  }
  .input:checked ~ .checkMark {
    @apply bg-[#3AC882] border-none after:block after:opacity-100;
  }
  & > p {
    @apply w-full pl-3;
  }
}

.input {
  @apply w-[20px] h-[20px] opacity-0 cursor-pointer;
}

.checkMark {
  @apply flex items-center justify-center absolute top-0 left-0 bg-[#222222] w-[20px] h-[20px] border-[#9B9B9B] border border-solid rounded-sm after:w-[12px] after:h-[7px] after:border-white after:border-0 after:border-b-[3px] after:border-l-[3px] after:opacity-0 after:-rotate-45 after:rounded-[1px] after:-translate-y-[1px];
}
