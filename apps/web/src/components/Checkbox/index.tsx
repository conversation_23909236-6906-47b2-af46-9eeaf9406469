import Style from './Style.module.scss';
import React from 'react';

const Checkbox = ({ label, checked, onChange, ...rest }: any) => {
  const handleChange = (e: any) => {
    if (typeof onChange === 'function') onChange(e.target.checked);
  };
  return (
    <label className={Style.container}>
      <input
        checked={checked}
        onChange={handleChange}
        className={Style.input}
        type="checkbox"
        {...rest}
      />
      <span className={Style.checkMark}></span>
      {label}
    </label>
  );
};

export default Checkbox;
