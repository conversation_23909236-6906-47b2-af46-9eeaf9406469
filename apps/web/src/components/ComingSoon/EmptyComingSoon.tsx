import React from 'react';
import Image from '@components/basic/Image/Image';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';
import Button from '@components/basic/Buttons/Button';
import { useVieRouter } from '@customHook';
import { PAGE } from '@constants/constants';
import styles from './ComingSoon.module.scss';

const EmptyComingSoon = () => {
  const router = useVieRouter();
  const onHandleClick = () => {
    router.push(PAGE.HOME);
  };
  return (
    <div className="min-h-[calc(100vh-215px)] flex justify-content-center items-center animate-fade-in duration-300 text-white ">
      <div className="text-center space-y-5">
        <Image src={ConfigImage.emptySchedule} alt="empty coming soon" notWebp />
        <div
          className="max-w-[441px] text-[#ccc]"
          dangerouslySetInnerHTML={{ __html: TEXT.EMPTY_COMING_SOON }}
        />
        <Button className={styles.buttonEmpty} title={TEXT.BACK_HOME} onClick={onHandleClick} />
      </div>
    </div>
  );
};
export default EmptyComingSoon;
