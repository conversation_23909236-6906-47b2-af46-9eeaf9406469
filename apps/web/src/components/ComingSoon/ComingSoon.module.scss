.tab {
  @apply pt-1 pb-1.5 text-[#CCC] text-center border-solid border-b-2 border-transparent cursor-pointer;
  @apply hover:border-b-2 hover:border-b-[#3AC882] hover:text-white hover:rounded;
}
.active {
  @apply border-b-2 border-b-[#3AC882] text-white rounded font-medium;
}
.boxFrame {
  @apply md:w-[1260px] mx-4 md:mx-auto relative;
}
.blockCard {
  @apply z-[10] block md:flex relative md:w-[1260px] mx-4 md:mx-auto space-x-2 md:space-x-5;
  @apply relative rounded md:rounded-xl border border-solid border-[#222222];
}
.bgCard {
  @apply ml-0 #{!important};
  @apply relative z-[1] md:w-[54%] h-auto;
}
.bgCardImage {
  @apply relative w-full rounded-tr md:rounded-tr-[0] rounded-tl md:rounded-l-xl;
  @apply before:md:rounded-l-xl before:z-[2] before:absolute before:h-1/2 before:bottom-0 before:w-full;
  @apply before:bg-[linear-gradient(180deg,rgba(17,17,17,0.00)0%,rgba(17,17,17,0.70)100%)];
}
.cardImage {
  @apply relative w-full rounded-tr md:rounded-tr-[0] rounded-tl md:rounded-l-xl;
}

.sizeButton {
  @apply text-xs md:text-sm font-semibold;
}
.iConTime {
  @apply flex flex-none justify-center items-center w-[24px] h-[24px] shadow-inner shadow-[#15181C]/50;
  @apply rounded-[43px] border-solid border-[#616468] border-[1.5px];
  @apply bg-[linear-gradient(320deg,rgba(97,100,104,0.50)14.78%,rgba(21,24,28,0.50)95.73%)];
}

.bgTimeContent {
  @apply bg-[linear-gradient(90deg,rgba(51,51,51,0.00)0%,rgba(51,51,51,0.40)40%,rgba(51,51,51,0.40)50%,rgba(51,51,51,0.40)60%,rgba(51,51,51,0.00)100%)];
  @apply text-sm space-y-[6px] py-1;
}

.content {
  &List {
  }

  &Item {
    @apply flex space-x-4 text-white items-center;

    &Text {
      @apply font-medium leading-[1.4] truncate;
    }
  }
}

.Button {
  @apply flex box-border min-w-[118px] h-[2.25] md:h-[2.5rem] px-3 py-1 justify-center items-center border border-white/30 space-x-2 rounded-sm text-white hover:bg-[#333]/50 hover:text-white transition-all focus:text-white;

  &:first-child {
    @apply bg-white text-[#222] hover:text-[#222];
  }

  &Icon {
    @apply flex justify-center items-center box-border w-[1.375em] h-[1.375em] md:w-[24px] md:h-[24px] text-[1.125em];
  }
}

.buttonEmpty {
  @apply font-medium text-base bg-white text-black px-[30px] py-[7px];
}
