import Image from '@components/basic/Image/Image';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getTipData } from '@actions/page';
import {
  CONTENT_TYPE,
  EL_ID,
  EL_SIZE_CLASS,
  ID,
  ID_COMING_SOON,
  TAG_KEY,
  TVOD
} from '@constants/constants';
import { PLAYER_TYPE } from '@constants/player';
import { numberWithCommas, parseTagsData, setVideoPlay } from '@helpers/common';
import TriggerAction from '@components/trigger/TriggerAction';
import Tags from '@components/basic/Tags/Tags';
import TagsOutline from '@components/basic/Tags/TagsOutline';
import configImage from '@config/ConfigImage';
import isEmpty from 'lodash/isEmpty';
import CardImage from '@components/basic/Card/CardImage';
import { TEXT } from '@constants/text';
import styles from './ComingSoon.module.scss';
const CardBroadcasting = React.memo(({ data, idRibbon, tabId }: any) => {
  const dispatch = useDispatch();
  const cardRef = useRef<any>(null);
  const tipData = useSelector((state: any) => state?.Page?.tipData);
  const [arrayLimit, setArrayLimit] = useState(5);
  const {
    images,
    isPremium,
    altSEOImg,
    shortDescription,
    triggers,
    tvod,
    hasPVOD,
    isPremiere,
    type,
    isPremiumDisplay,
    comingSoon,
    tagGenreComingSoon,
    isUpComingSoon,
    hasObjectDetection,
    isLive
  } = data || {};
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const { broadcasting, availableFreeEps, availableVipEps, broadcastSince } = comingSoon || {};
  const { benefitType, price } = tvod || {};

  const isLiveUpdate = useMemo(() => {
    if (tabId === ID.BROADCASTING && type === CONTENT_TYPE.LIVE_TV) {
      return !isLive;
    }
    return isLive;
  }, [tabId, type, isLive]);

  const parseShortDes = useMemo(
    () =>
      shortDescription.length > 150
        ? `${shortDescription.substring(0, 150)} ...`
        : shortDescription,
    [shortDescription]
  );

  const tipDataItem = tipData?.[data?.id];

  const isVideoIndexing = useMemo(() => hasObjectDetection && !isKid, [hasObjectDetection, isKid]);
  const newTriggers = useMemo(() => {
    if (
      type &&
      (type === CONTENT_TYPE.EPG || type === CONTENT_TYPE.LIVE_TV) &&
      triggers.some((item: any) => item.key === 'SHARE')
    ) {
      return triggers.filter((item: any) => item.key !== 'SHARE');
    }
    return triggers;
  }, [triggers]);

  const cardThumbId = `CARD_AIRING_${data?.id}`;

  const checkScroll = (e: any) => {
    const scrollingEl = e.target?.scrollingElement;
    const cardEl = cardRef?.current;
    if (!scrollingEl || !cardEl) return;

    const ribbonEl = document.getElementById(idRibbon);
    const { scrollTop } = scrollingEl;
    const offsetTop = ribbonEl?.offsetTop;
    const clientHeight = cardEl?.clientHeight;
    const sectionHome = document.getElementById(EL_ID.SECTION_HOME);
    const sectionSport: any = document.getElementsByClassName('section--sport')[0];
    const offsetTopSection = sectionHome?.offsetTop || sectionSport?.offsetTop || 0;
    const screenHeight = document.documentElement.clientHeight;

    const offsetTopCard = offsetTopSection + offsetTop;
    const screenBot = scrollTop + screenHeight;
    const airingVideo: any =
      window?.[PLAYER_TYPE.CARD_AIRING + data?.id] ||
      document.getElementById(PLAYER_TYPE.CARD_AIRING + data?.id);

    if (!airingVideo) return;
    if (scrollTop > offsetTopCard || screenBot < offsetTopCard + clientHeight) {
      airingVideo.pause();
    } else if (screenBot > offsetTopCard + clientHeight) {
      if (!airingVideo.ended) {
        setVideoPlay({ playerId: PLAYER_TYPE.CARD_AIRING + data?.id });
      }
    }
  };

  const newTagData = parseTagsData({ ...data, ...tipDataItem }, true);

  const listDataContent = useMemo(() => {
    let list: any = [];
    const conditions = [
      {
        condition: isUpComingSoon ? broadcastSince : broadcasting,
        id: ID_COMING_SOON.SCHEDULE,
        name: isUpComingSoon ? broadcastSince : broadcasting,
        icon: configImage.tickTime
      },
      {
        condition: availableFreeEps,
        id: ID_COMING_SOON.NUMBER_FREE,
        name: availableFreeEps,
        icon: configImage.tickFreeEpisode
      },
      {
        condition: availableVipEps,
        id: ID_COMING_SOON.NUMBER_VIP,
        name: availableVipEps,
        icon: configImage.tickVipEpisode
      }
    ];

    conditions.forEach(({ condition, id, name, icon }) => {
      if (condition) {
        list.push({ id, name, icon });
      }
    });

    return list;
  }, [broadcasting, availableFreeEps, availableVipEps, isUpComingSoon, broadcastSince]);

  useEffect(() => {
    if (data?.id && !tipDataItem) {
      dispatch(getTipData({ id: data?.id }));
    }
  }, [data?.id]);

  useEffect(() => {
    window.addEventListener('scroll', checkScroll, false);
    return () => {
      window.removeEventListener('scroll', checkScroll, false);
    };
  }, []);

  useEffect(() => {
    if (isPremium || hasPVOD || (!!price && benefitType <= 0)) {
      if (isPremium && hasPVOD) return setArrayLimit(3);
      return setArrayLimit(4);
    }
    setArrayLimit(5);
  }, [isPremium, price, benefitType]);

  const TagGenreComingSoon = ({ tagGenreComingSoon }: any) => {
    if (isEmpty(tagGenreComingSoon)) return null;

    const slicedTagGenre = tagGenreComingSoon.slice(0, 3);

    return (
      <div className="flex">
        {slicedTagGenre.map((item: any, index: any) => (
          <div key={index} className="text-white">
            <span className="!text-sm">{item?.name}</span>
            {index < 2 && <Image className="px-2" src={configImage.dot} alt="dot" />}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={styles.blockCard} ref={cardRef}>
      <div className="absolute inset-0 opacity-[0.02] z-[0]">
        <CardImage
          className="object-cover w-full h-full md:rounded-xl"
          isSchedule
          images={images}
        />
      </div>
      <div className={styles.bgCard} id={cardThumbId}>
        <div className={styles.bgCardImage}>
          <CardImage className={styles.cardImage} isSchedule images={images} alt={altSEOImg} />
        </div>
      </div>
      <div className="z-[1] relative md:w-[45%] space-y-3 py-3 pr-5">
        {images?.titleCardDark && (
          <Image className="w-1/2 max-h-[85px]" src={images?.titleCardDark} alt={altSEOImg} />
        )}
        <div className="card__section-wrap space-y-2">
          <div className="flex flex-row flex-wrap space-x-[10px] lg:space-x-3">
            {isPremium && isPremiumDisplay && (
              <Tags isPremiumDisplay={isPremiumDisplay} size={EL_SIZE_CLASS.LARGE} />
            )}
            {hasPVOD && type === CONTENT_TYPE.SEASON && (
              <Tags
                tagKey={TAG_KEY.WATCH_SOON}
                title={TEXT.TAG_WATCH_SOON}
                size={EL_SIZE_CLASS.LARGE}
              />
            )}
            {price &&
              (benefitType === TVOD.USER_TYPE.EXPIRED || benefitType === TVOD.USER_TYPE.NONE) && (
                <Tags
                  tagKey={TAG_KEY.PRICE}
                  price={numberWithCommas(price)}
                  size={EL_SIZE_CLASS.LARGE}
                />
              )}
            {(isLiveUpdate || isPremiere) && (
              <Tags
                title={isPremiere ? TAG_KEY.PREMIERE : ''}
                tagKey={TAG_KEY.LIVE}
                size={EL_SIZE_CLASS.LARGE}
              />
            )}
            <TagsOutline
              tagArray={newTagData}
              arrayLimit={arrayLimit}
              isVideoIndexing={isVideoIndexing}
              size={EL_SIZE_CLASS.LARGE}
            />
          </div>
          {parseShortDes && (
            <div className="!text-sm text-white" title={shortDescription}>
              {parseShortDes}
            </div>
          )}
          {TagGenreComingSoon(tagGenreComingSoon)}
          {!isEmpty(listDataContent) && (
            <div className={styles.bgTimeContent}>
              {listDataContent.map((item: any, index: any) => (
                <div id={item?.id} key={index} className={styles.contentItem}>
                  <span className={styles.iConTime}>
                    <Image src={item.icon} alt="icon" />
                  </span>
                  <div className={styles.contentItemText}>{item.name}</div>
                </div>
              ))}
            </div>
          )}
        </div>
        <div className="flex space-x-4">
          {(newTriggers || []).map((tgg: any, index: any) => (
            <TriggerAction
              key={index}
              isSchedule
              isAiring
              index={index}
              triggerItem={tgg}
              cardData={data}
              contentData={data}
              customText={styles.sizeButton}
              playerId={PLAYER_TYPE.CARD_AIRING + data?.id}
              idTabComingSoon={tabId}
              buttonStyleCustom={styles.Button}
              iconCustom={styles.ButtonIcon}
            />
          ))}
        </div>
      </div>
    </div>
  );
});
export default CardBroadcasting;
