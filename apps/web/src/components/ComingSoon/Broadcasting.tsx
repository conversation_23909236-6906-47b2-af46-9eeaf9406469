import React from 'react';
import CardBroadcasting from '@components/ComingSoon/CardBroadcasting';
import isEmpty from 'lodash/isEmpty';
import EmptyComingSoon from '@components/ComingSoon/EmptyComingSoon';

const Broadcasting = ({ data = [], tabId }: any) => {
  if (isEmpty(data)) return <EmptyComingSoon />;
  return (
    <div className="animate-fade-in duration-300 space-y-5 md:space-y-6">
      {!isEmpty(data) &&
        (data || []).map((item: any, index: any) => (
          <CardBroadcasting tabId={tabId} key={`${item?.id}_${index}`} data={item} />
        ))}
    </div>
  );
};
export default Broadcasting;
