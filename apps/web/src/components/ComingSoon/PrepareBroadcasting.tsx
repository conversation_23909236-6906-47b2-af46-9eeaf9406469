import isEmpty from 'lodash/isEmpty';
import CardBroadcasting from '@components/ComingSoon/CardBroadcasting';
import React from 'react';
import EmptyComingSoon from '@components/ComingSoon/EmptyComingSoon';

const PrepareBroadcasting = ({ data = [], tabId }: any) => {
  if (isEmpty(data)) return <EmptyComingSoon />;
  return (
    <div className="animate-fade-in duration-300 space-y-5 md:space-y-6">
      {!isEmpty(data) &&
        (data || []).map((item: any, index: any) => (
          <CardBroadcasting tabId={tabId} key={`${item?.id}_${index}`} data={item} />
        ))}
    </div>
  );
};
export default PrepareBroadcasting;
