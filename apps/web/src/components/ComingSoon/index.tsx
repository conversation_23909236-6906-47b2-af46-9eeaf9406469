import { TEXT } from '@constants/text';
import React, { Fragment, memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import classnames from 'classnames';
import classNames from 'classnames';
import isEmpty from 'lodash/isEmpty';
import Broadcasting from '@components/ComingSoon/Broadcasting';
import PrepareBroadcasting from '@components/ComingSoon/PrepareBroadcasting';
import { useDispatch, useSelector } from 'react-redux';
import {
  getContentBroadcasting,
  getMoreContentBroadcasting,
  getContentUpcoming,
  getMoreContentUpcoming
} from '@actions/page';
import { ID, POSITION_TRIGGER, TYPE_TRIGGER_ALWAYS } from '@constants/constants';
import styles from './ComingSoon.module.scss';
import { debounce } from 'lodash';
import TriggerTouchPoint from '@components/home/<USER>';
import style from '@components/home/<USER>';
import { getDataTriggerPoint } from '@actions/trigger';
import { createTimeout } from '@helpers/common';

const TabData = [
  { id: ID.BROADCASTING, title: TEXT.BROADCASTING, active: true },
  { id: ID.UP_BROADCASTING, title: TEXT.PREPARE_BROADCASTING, active: false }
];

const ComingSoon = () => {
  const dispatch = useDispatch();
  const clickTimerRef = useRef<any>(null);
  const { dataContentBroadcasting, dataContentUpComingSoon, totalBroadcasting, totalUpComingSoon } =
    useSelector((state: any) => state?.Page?.comingSoon) || {};
  const { dataTriggerComingSoon } = useSelector((state: any) => state?.Trigger);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const { statusMasthead } = useSelector((state: any) => state.App) || {};
  const [listSub, setListSub] = useState(TabData);
  const [dataItem, setDataItem] = useState<any>(TabData?.[0]);
  const [page, setPage] = useState<any>(0);
  const stickyTop = useMemo(() => {
    if (statusMasthead && (!profile?.id || !profile?.isPremium)) {
      return 'top-[calc(10vw_+_54px)]';
    }
    return 'top-14';
  }, [statusMasthead, profile?.id, profile?.isPremium]);

  const stickyTopTrigger = useMemo(() => {
    if (statusMasthead && (!profile?.id || !profile?.isPremium)) {
      return 'top-[calc(10vw_+_129px)]';
    }
    return 'top-[131px]';
  }, [statusMasthead, profile?.id, profile?.isPremium]);

  const onHandleClick = (data: any) => {
    if (data.id !== dataItem.id) {
      window.scrollTo(0, 0);
      setDataItem(data);
    }
  };

  const handleScroll = useCallback(
    debounce(() => {
      const mainElement: any = document.querySelector('#main');
      const currentHeight = Math.ceil(window.scrollY + window.innerHeight);

      const loadMore = (contentLength: any, totalLength: any, action: any) => {
        if (contentLength < totalLength && currentHeight >= mainElement?.scrollHeight) {
          setPage((prevPage: any) => {
            const newPage = prevPage + 1;
            dispatch(action({ indexOfPage: newPage }));
            return newPage;
          });
        }
      };

      if (dataItem?.id === ID.BROADCASTING) {
        loadMore(
          dataContentBroadcasting?.items?.length,
          totalBroadcasting,
          getMoreContentBroadcasting
        );
      } else {
        loadMore(dataContentUpComingSoon?.items?.length, totalUpComingSoon, getMoreContentUpcoming);
      }
    }, 400),
    [
      dataContentBroadcasting?.items?.length,
      totalBroadcasting,
      totalUpComingSoon,
      dataContentUpComingSoon?.items?.length,
      dataItem?.id,
      dispatch
    ]
  );

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  useEffect(() => {
    window.scrollTo(0, 0);
    window.onbeforeunload = function () {
      window.scrollTo(0, 0);
    };
    if (clickTimerRef.current) clearTimeout(clickTimerRef.current);
    clickTimerRef.current = createTimeout(() => {
      dispatch(getDataTriggerPoint({ type: TYPE_TRIGGER_ALWAYS.COMING_SOON }));
    }, 400);

    return () => {
      window.onbeforeunload = null;
      if (clickTimerRef.current) clearTimeout(clickTimerRef.current);
    };
  }, []);

  useEffect(() => {
    if (isEmpty(dataContentBroadcasting)) {
      dispatch(getContentBroadcasting({ indexOfPage: 0 }));
    }
  }, [dataContentBroadcasting, dispatch]);

  useEffect(() => {
    if (isEmpty(dataContentUpComingSoon)) {
      dispatch(getContentUpcoming({ indexOfPage: 0 }));
    }
  }, [dataContentUpComingSoon, dispatch]);

  useEffect(() => {
    if (isEmpty(dataItem)) return;
    setListSub((prevListSub) =>
      prevListSub.map((item: any) => ({
        ...item,
        active: item.id === dataItem.id
      }))
    );
  }, [dataItem]);

  return (
    <Fragment>
      <div
        className={classNames(
          'flex !sticky bg-[#111] z-50 justify-content-center align-center pt-4 pb-3 md:pb-5 text-white space-x-9',
          stickyTop
        )}
      >
        {!isEmpty(listSub) &&
          listSub.map((item, index) => (
            <div
              key={`${item?.id}_${index}`}
              id={item?.id}
              className={classnames(styles.tab, item?.active ? styles.active : '')}
              onClick={() => onHandleClick(item)}
            >
              {item?.title}
            </div>
          ))}
      </div>
      {!isGlobal && !isKid && !isEmpty(dataTriggerComingSoon) && (
        <div
          className={classNames(
            'flex !sticky md:w-[1260px] mx-4 md:mx-auto pb-3 z-50',
            stickyTopTrigger
          )}
        >
          <TriggerTouchPoint
            image={dataTriggerComingSoon?.image}
            imageMobile={dataTriggerComingSoon?.image}
            url={dataTriggerComingSoon?.navigateUrl}
            positionTrigger={POSITION_TRIGGER.COMING_SOON}
            className={style.scroll}
          />
        </div>
      )}
      <div className={styles.boxFrame}>
        {dataItem?.id === ID.BROADCASTING && (
          <Broadcasting tabId={dataItem?.id} data={dataContentBroadcasting?.items} />
        )}
        {dataItem?.id === ID.UP_BROADCASTING && (
          <PrepareBroadcasting tabId={dataItem?.id} data={dataContentUpComingSoon?.items} />
        )}
      </div>
    </Fragment>
  );
};

export default memo(ComingSoon);
