import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import language from './language';

export default function useLanguage() {
  const tpbankSelector = useSelector((state: any) => state?.Tpbank ?? {});
  const tpbankConfig = useMemo(() => tpbankSelector?.config || {}, [tpbankSelector]);
  const getString = (key: any, date = '') => {
    let languageText = tpbankConfig?.language ?? 'VN';

    const isSupportLanguage = (language as any)?.[languageText] || null !== null;
    if (!isSupportLanguage) {
      languageText = 'VN';
    }
    if (key === 'notiVipExits') {
      return (language as any)?.[languageText].notiVipExits(date);
    }

    return (language as any)?.[languageText]?.[key] || '';
  };
  return [getString];
}
