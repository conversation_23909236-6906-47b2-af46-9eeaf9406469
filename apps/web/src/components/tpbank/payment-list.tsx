import React from 'react';
import { numberWithCommas } from '@helpers/common';
import { CURRENCY } from '@constants/constants';
import useLanguage from './useLangage';

const PaymentList = ({
  paymentList = [],
  paymentSelected,
  setPaymentSelected,
  packageName = ''
}: any) => {
  const [getString]: any = useLanguage();
  return (
    <div className="block block--payment block--plan block--plan-tpbank" id="blockPlan">
      <div className="block__header">
        <h2 className="title">
          {getString('labelChoosePackageItem')} {packageName}
        </h2>
      </div>
      <div className="block__body">
        <div className="radio-group">
          {paymentList.map((item: any) => (
            <div key={item.id} className="radio radio-custom radio-custom-right">
              <input
                type="radio"
                name={item.id}
                id={item.id}
                data-target={item.id}
                checked={paymentSelected.id === item.id}
                onChange={() => setPaymentSelected(item)}
              />
              <label className="label-medium" htmlFor={item.id}>
                {item.name} {+item?.recurring === 1 && getString('autoRecurring')}
                <span className="package-price">
                  {`${numberWithCommas(item.price)} ${CURRENCY.VND}`}
                </span>
              </label>
            </div>
          ))}
        </div>
        <label className="form-info is-visible">*{getString('autoRenewInfo')}</label>
      </div>
    </div>
  );
};
export default PaymentList;
