export default {
  VN: {
    vieonTitle: 'Ứng Dụng VieON',
    btnNext: 'Tiếp tục',
    labelPhoneNumber: 'Số điện thoại mua gói VIP',
    inputPhoneNumber: 'S<PERSON> điện thoại đăng nhập',
    inputPassword: 'Mật khẩu',
    errorPhoneNumber: 'Số điện thoại chưa đúng',
    errorPassword: 'Mật khẩu chưa đúng',
    errorLogin: 'Số điện thoại hoặc mật khẩu chưa đúng',
    errorLoginMany: '<PERSON><PERSON><PERSON> nhập quá 5 lần, vui lòng thử lại sau 5 phút.',
    errorPasswordLength: 'Mật khẩu tối thiểu có 6 ký tự',
    errorBlockAccount: 'Tài khoản của bạn đã bị khóa',
    errorUnauthorized: 'Sai mật khẩu. Vui lòng kiểm tra lại',
    errorDefault: 'Có lỗi xảy ra',
    errorManyRequest: '<PERSON>u<PERSON> nhiều yêu cầu lên máy chủ. Vui lòng thử lại sau 10 phút',
    inputCustomerName: 'Tên tài khoản VieON',
    labelChoosePackage: 'Chọn gói phù hợp với bạn',
    labelChoosePackageItem: 'Chọn thời hạn gói',
    notiNewCustomer:
      'Vie ON sẽ gửi mật khẩu tài khoản tới số điện thoại của bạn sau khi mua gói thành công',
    labelAccount: 'Tài khoản',
    labelPackage: 'Gói',
    labelTime: 'Thời hạn',
    labelPackageInfo: 'Thông tin gói cước',
    labelDay: 'Ngày',
    labelMonth: 'Tháng',
    labelYear: 'Năm',
    labelHour: 'Giờ',
    labelDays: 'Ngày',
    labelMonths: 'Tháng',
    labelYears: 'Năm',
    labelHours: 'Giờ',
    autoRecurring: '- Tự động gia hạn',
    autoRenewInfo:
      'Để hủy tự động gia hạn gói hàng tháng, vui lòng liên lạc 1800 599920 (miễn phí) để được hỗ trợ',
    loginInfo:
      'Nếu Quý khách gặp vấn đề về đăng nhập, vui lòng liên hệ tổng đài (miễn phí) 1800 599920 để được hỗ trợ',
    notiVipExits: (time: any) =>
      `Bạn có gói VIP sẽ hết hạn vào ngày ${time}. Nếu tiếp tục sử dụng, thời hạn sử dụng gói VIP mới và gói VIP cũ đang sử dụng sẽ không được cộng dồn.`
  },
  EN: {
    vieonTitle: 'VieON',
    btnNext: 'Next',
    labelPhoneNumber: 'Input phone number to purchase VIP package',
    inputPhoneNumber: 'Phone number',
    inputPassword: 'Password',
    errorPhoneNumber: 'Invalid phone number',
    errorPassword: 'Invalid password',
    errorLogin: 'Phone number or password is invalid',
    errorLoginMany:
      'Too many log in attempts from your address. Please wait 5 mins before trying again',
    errorPasswordLength: 'Password must be at least 6 characters',
    errorBlockAccount: 'The account is blocked',
    errorUnauthorized: 'Invalid password. Please recheck',
    errorDefault: 'Something went wrong',
    errorManyRequest: 'Too many request. Please try again after 10 minutes',
    inputCustomerName: 'VieON Username',
    labelChoosePackage: 'Please select a package',
    labelChoosePackageItem: 'Please select duration for',
    notiNewCustomer: 'Once payment completed, a password will be sent to your phone number',
    labelAccount: 'Account',
    labelPackage: 'Package',
    labelTime: 'Duraction',
    labelPackageInfo: 'Package Details',
    labelDay: 'Day',
    labelMonth: 'Month',
    labelYear: 'Year',
    labelHour: 'Hour',
    labelDays: 'Days',
    labelMonths: 'Months',
    labelYears: 'Years',
    labelHours: 'Hours',
    autoRecurring: '- Auto renewal',
    autoRenewInfo: 'Please contact toll-free 1800 599920 to cancel auto-renewable subscriptions',
    loginInfo:
      'Please contact toll-free 1800 599920 if you need any assistance about information related to your account',
    notiVipExits: (time: any) =>
      `You are having a VIP package which will be expired at ${time}. Once continue to have payment, the duration of current VIP package and new VIP package will not be accumulated.`
  }
};
