import React, { useEffect, useState } from 'react';
import TpbankInput from './input';
import TpbankLabel from './label';
import useLanguage from './useLangage';

const StepTwo = ({
  phoneNumber = '',
  password = '',
  setPassword = () => {},
  isErrorPassword = 0
}: any) => {
  const handleChangePassword = (event: any) => {
    const value = event?.target?.value || '';
    setPassword(value);
  };
  const [getString]: any = useLanguage();
  const [errString, setErrString] = useState<any>('');
  useEffect(() => {
    if (password.length && password.length < 6) {
      setErrString(getString('errorPasswordLength'));
    } else {
      setErrString('');
    }
  }, [password]);

  useEffect(() => {
    if (isErrorPassword === 1) {
      setErrString(getString('errorLoginMany'));
    } else if (isErrorPassword === 2) {
      setErrString(getString('errorLogin'));
    } else {
      setErrString(getString(''));
    }
  }, [isErrorPassword]);
  return (
    <div className="block block--payment block--info-tpbank" id="blockInfo">
      <div className="block__header">
        <h2 className="title text-center">{getString('labelPhoneNumber')}</h2>
      </div>
      <div className="block__body">
        <div className="form form--payment-info">
          <TpbankLabel label={getString('inputPhoneNumber')} value={phoneNumber} />
          <TpbankInput
            errorString={errString}
            value={password}
            onChange={handleChangePassword}
            label={getString('inputPassword')}
            type="password"
          />
        </div>
        <label className="form-info is-visible">*{getString('loginInfo')}</label>
      </div>
    </div>
  );
};
export default StepTwo;
