import React from 'react';
import useLanguage from './useLangage';

const PackageList = ({ list = [], packageSelected = {}, setPackageSelected = () => {} }: any) => {
  const [getString]: any = useLanguage();
  return (
    <div className="block block--payment block--package-tpbank" id="blockPackage">
      <div className="block__header">
        <h2 className="title">{getString('labelChoosePackage')}</h2>
      </div>
      <div className="block__body">
        <div className="radio-group">
          {list.map((item: any) => (
            <div key={item.id} className="radio radio-custom radio-custom-right">
              <input
                type="radio"
                name={item.name}
                id={item.id}
                data-target={item.id}
                checked={packageSelected?.id === item.id}
                onChange={() => setPackageSelected(item)}
              />
              <label className="label-medium" htmlFor={item.id}>
                {item.name}
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
export default PackageList;
