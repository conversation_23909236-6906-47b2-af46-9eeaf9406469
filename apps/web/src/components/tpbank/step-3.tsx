import React from 'react';
import PackageList from './package-list';
import TpbankLabel from './label';
import useLanguage from './useLangage';
import TpbankInput from './input';

const StepThree = ({
  phoneNumber = '',
  customerName,
  isNewUser = false,
  packageList = [],
  packageSelected = {},
  setPackageSelected = () => {}
}: any) => {
  const [getString]: any = useLanguage();
  return (
    <>
      <div className="block block--payment block--info-tpbank" id="blockInfo">
        <div className="block__header">
          <h2 className="title text-center">{getString('labelPhoneNumber')}</h2>
        </div>
        <div className="block__body">
          <div className="form form--payment-info">
            <TpbankInput disabled label={getString('inputPhoneNumber')} value={phoneNumber} />
            <TpbankLabel
              label={getString('inputCustomerName')}
              value={customerName ?? phoneNumber}
            />
            {isNewUser && (
              <label className="form-info is-visible">{getString('notiNewCustomer')}</label>
            )}
          </div>
        </div>
      </div>
      <PackageList
        list={packageList}
        packageSelected={packageSelected}
        setPackageSelected={setPackageSelected}
      />
    </>
  );
};
export default StepThree;
