import React from 'react';
import useLanguage from './useLangage';

const TpbankButton = ({ onClick, disabled }: any) => {
  const handleButtonClick = () => {
    onClick();
  };
  const [getString]: any = useLanguage();
  return (
    <div className="button-group child-auto">
      <a
        className={`button button--purple button--large child-auto ${disabled ? 'disabled' : ''}`}
        title="Tiếp tục"
        onClick={handleButtonClick}
      >
        <span className="text">{getString('btnNext')}</span>
      </a>
    </div>
  );
};
export default TpbankButton;
