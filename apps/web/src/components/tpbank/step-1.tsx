import React, { useEffect, useState } from 'react';
import TpbankInput from './input';
import useLanguage from './useLangage';

const StepOne = ({ phoneNumber = '', setPhoneNumber = () => {} }: any) => {
  const handleChangePhoneNumber = (event: any) => {
    const value = event?.target?.value || '';
    if (value.length <= 10) {
      setPhoneNumber(value);
    }
  };
  const [getString]: any = useLanguage();
  const [errString, setErrString] = useState<any>('');
  useEffect(() => {
    const reg = new RegExp('(84|0[3|5|7|8|9])+([0-9]{8})\\b', 'g');
    if (phoneNumber.length > 0 && !reg.test(phoneNumber)) {
      setErrString(getString('errorPhoneNumber'));
    } else {
      setErrString('');
    }
  }, [phoneNumber]);
  return (
    <div className="block block--payment block--info-tpbank" id="blockInfo">
      <div className="block__header">
        <h2 className="title text-center">{getString('labelPhoneNumber')}</h2>
      </div>
      <div className="block__body">
        <div className="form form--payment-info">
          <TpbankInput
            type="tel"
            errorString={errString}
            value={phoneNumber}
            onChange={handleChangePhoneNumber}
            label={getString('inputPhoneNumber')}
          />
        </div>
        <label className="form-info is-visible">*{getString('loginInfo')}</label>
      </div>
    </div>
  );
};
export default StepOne;
