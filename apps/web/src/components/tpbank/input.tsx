import React, { useEffect, useRef, useState } from 'react';

const TpbankInput = ({
  errorString,
  label,
  placeholder = '',
  id,
  type = 'text',
  value = '',
  onChange = null,
  disabled = false
}: any) => {
  const [isFocus, setIsFocus] = useState(false);
  const inputRef = useRef<any>(null);
  const onFocus = () => {
    setIsFocus(true);
  };
  const onBlur = () => {
    if (!value && !value?.length) {
      setIsFocus(false);
    }
  };
  const handleClick = () => {
    if (inputRef.current && !disabled) {
      const inputEle = inputRef.current;
      if (inputEle) {
        inputEle.focus();
      }
    }
  };
  useEffect(() => {
    if (!value && !value?.length) {
      setIsFocus(false);
    } else {
      setIsFocus(true);
    }
  }, [value]);
  return (
    <div
      className={`input-group-custom input-group-custom-inline ${isFocus ? 'focus' : ''} ${
        errorString?.length > 0 ? 'error' : ''
      }`}
      onClick={handleClick}
    >
      <div className="input-group">
        <label htmlFor={id} className="input-group-label pos-tl">
          {label}
        </label>
        <input
          ref={inputRef}
          id={id}
          className={`input-group-field ${disabled ? 'disabled' : ''}`}
          type={type}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          onFocus={onFocus}
          onBlur={onBlur}
          disabled={disabled}
        />
      </div>
      {errorString?.length > 0 && <label className="form-error is-visible">{errorString}</label>}
    </div>
  );
};
export default TpbankInput;
