import React from 'react';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';
import { VieON_TEL } from '@constants/constants';

const ProcessWalletLink = ({ tel, name, isNewCase }: any) => (
  <div className="block block--result block--result-pm block--result-pm-process text-white">
    <div className="mask">
      <div className="mask__inner ratio-variant-result">
        <div className="mask__img absolute">
          <img
            src={ConfigImage.catProcessing}
            alt={isNewCase ? 'Giao dịch tương tự đang xử lý' : TEXT.PROCESSING_WALLET_LINK_TITLE}
          />
        </div>
      </div>
    </div>
    <div className="block__header">
      <h2 className="block__title text-center">
        {isNewCase ? 'Gia<PERSON> dị<PERSON> tương tự đang xử lý' : TEXT.PROCESSING_WALLET_LINK_TITLE}
      </h2>
    </div>

    <div className="block__body">
      <div className="list-group">
        <div className="list-group__item divide--dashed-non-last">
          {!isNewCase ? (
            <div className="text-center">
              Liên kết này mất nhiều thời gian để xử lý. <br />
              Kết quả liên kết sẽ được cập nhật sau 24 giờ làm việc tại
              <span className="text-f-m normal">Tài khoản / Gói dịch vụ / Lịch sử giao dịch.</span>
            </div>
          ) : (
            <div className="text-center">
              Bạn có giao dịch tương tự đang trong quá trình xử lý. Vui lòng đợi giao dịch xử lý
              hoàn tất và thực hiện giao dịch tiếp theo sau nhé.
            </div>
          )}
        </div>
        <div className="list-group__item divide--dashed-non-last">
          <div className="text-center">
            Vui lòng liên hệ hotline VieON (miễn phí)
            <a className="highlight" href={`tel:${VieON_TEL}`} title={VieON_TEL}>
              {VieON_TEL}
            </a>
            hoặc hotline {name}
            <a className="highlight" href={`tel:${tel}`} title={tel}>
              {tel}
            </a>
            để được hỗ trợ
          </div>
        </div>
      </div>
    </div>
  </div>
);
export default ProcessWalletLink;
