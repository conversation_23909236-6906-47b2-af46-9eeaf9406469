import React from 'react';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';
import { PAGE, VieON_TEL } from '@constants/constants';
import Button from '@components/basic/Buttons/Button';
import { useVieRouter } from '@customHook';

const FailedWalletLink = ({
  errorCode,
  tel,
  name,
  pkg,
  isTransactionCantBeTraced,
  isNewCase
}: any) => {
  const router = useVieRouter();
  const onRetry = () => {
    if (pkg) {
      router.push(`${PAGE.PAYMENT_METHOD}/?pkg=${pkg}`);
    }
  };
  let contactNote = `Vui lòng liên hệ hot
line VieON (miễn phí) <a class='highlight text-bold' href=${`tel:${VieON_TEL}`}>${VieON_TEL}</a>`;
  if (tel) {
    contactNote += ` hoặc hotline ${name} <a class='highlight text-bold' href=${`tel:${tel}`}>${tel}</a>`;
  }
  contactNote += ' để được hỗ trợ.';
  let title = TEXT.UNSUCCESSFULLY_BUY_TITLE;
  let titleError = isNewCase ? 'Giao dịch tương tự đang xử lý' : TEXT.FAILED_WALLET_LINK_TITLE;
  if (isTransactionCantBeTraced) {
    title = TEXT.TRANSACTION_CANT_BE_TRACED;
    titleError = TEXT.TRANSACTION_NOT_PERMISSION_TRACED;
  }

  return (
    <div className="block block--result block--result-pm block--result-pm-process">
      <div className="mask">
        <div className="mask__inner ratio-variant-result">
          <div className="mask__img absolute">
            <img src={ConfigImage.catFailed} alt={title} />
          </div>
        </div>
      </div>
      <div className="block__header">
        <div className="text block__title text-center text-24 text-large-up-28">{title}</div>
      </div>
      <div className="block__body">
        <div className="list-group">
          <div className="list-group__item divide--dashed-non-last align-middle">
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-4">
                <span className="text normal">{TEXT.TRANSACTION_CODE}</span>
              </div>
              <div className="cell small-8">
                <div className="text-f-m text-right">-</div>
              </div>
            </div>
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-4">
                <span className="text normal">{TEXT.ERROR_CODE}</span>
              </div>
              <div className="cell small-8">
                <div className="text-f-m text-right">{errorCode}</div>
              </div>
            </div>
            <div className="grid-x grid-margin-x align-justify">
              <div className="cell small-4">
                <span className="text normal">{TEXT.ERROR}</span>
              </div>
              <div className="cell small-8">
                <div className="text-f-m text-right">{titleError}</div>
              </div>
            </div>
          </div>
          <div className="list-group__item divide--dashed-non-last">
            <div className="text-center" dangerouslySetInnerHTML={{ __html: contactNote }} />
          </div>
        </div>

        <div className="button-group child-auto">
          <Button
            className="button button--large hollow button--gray40"
            title={TEXT.CONTINUE_WATCH_VieON}
            onClick={() => router.push(PAGE.HOME)}
          />
          {pkg && (
            <Button
              className="button button--green button--large !text-white !text-[1rem]"
              title={TEXT.RETRY}
              onClick={onRetry}
            />
          )}
        </div>
      </div>
    </div>
  );
};
export default FailedWalletLink;
