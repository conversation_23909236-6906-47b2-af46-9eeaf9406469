import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setLoading } from '@actions/app';
import { PAYMENT_METHOD } from '@constants/constants';
import PaymentV2Api from '@apis/PaymentV2';
import { useVieRouter } from '@customHook';
import { createTransaction } from '@actions/shopeepay';
import ProcessWalletLink from '@components/WalletLink/ProcessWalletLink';
import FailedWalletLink from '@components/WalletLink/FailedWalletLink';
import { queryStringEncoding } from '@helpers/common';
import { getPaymentConfig } from '@actions/payment';

const WalletLink = React.memo(() => {
  const dispatch = useDispatch();
  const timeOutCheckLinked = useRef<any>(null);
  const timeIntervalCheckLinked = useRef<any>(null);
  const router = useVieRouter();
  const { listMethodsConfig } = useSelector((state: any) => state?.Payment);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const packageId = useMemo(() => router?.query?.packageId || '', [router]);
  const promotionCode = useMemo(() => router?.query?.promotionCode || '', [router]);
  const recurring = useMemo(() => router?.query?.recurring || '', [router]);
  const pkg = useMemo(() => router?.query?.pkg || '', [router]);
  const [isWalletFailed, setIsWalletFailed] = useState(false);
  const [isTransactionCantBeTraced, setIsTransactionCantBeTraced] = useState(false);
  const [errorCode, setErrorCode] = useState(0);

  const dataConfigShopeePay = useMemo(
    () =>
      (listMethodsConfig || []).filter((method: any) => method?.id === PAYMENT_METHOD.SHOPEE_PAY),
    [listMethodsConfig]
  );

  useEffect(() => {
    dispatch(setLoading(true));
    checkWallLetLinked();
    dispatch(getPaymentConfig({ isGlobal }));
    timeOutCheckLinked.current = setTimeout(() => {
      clearInterval(timeIntervalCheckLinked.current);
      dispatch(setLoading(false));
      setIsWalletFailed(true);
    }, 60000);
    return () => {
      clearInterval(timeIntervalCheckLinked.current);
      clearTimeout(timeOutCheckLinked.current);
      dispatch(setLoading(false));
    };
  }, []);
  const checkWallLetLinked = () => {
    clearInterval(timeIntervalCheckLinked.current);
    const queryString = `?${queryStringEncoding(router?.query)}`;
    timeIntervalCheckLinked.current = setInterval(() => {
      PaymentV2Api.getListTokensSaved({
        paymentMethod: 'WALLET',
        paymentService: PAYMENT_METHOD.SHOPEE_PAY
      }).then((res) => {
        if (!res?.success) {
          if (res?.httpCode === 410) {
            clearTimeout(timeOutCheckLinked.current);
            clearInterval(timeIntervalCheckLinked.current);
            setIsWalletFailed(true);
            setIsTransactionCantBeTraced(true);
          }
          const data: any = dispatch(
            createTransaction({
              tokenId: 23,
              packageId,
              promotionCode,
              recurring,
              queryString,
              router
            })
          );
          setErrorCode(data?.error_code);
        } else {
          const tokenId = res?.data?.result?.tokens?.[0]?.id;
          if (tokenId) {
            const data: any = dispatch(
              createTransaction({
                tokenId,
                packageId,
                promotionCode,
                recurring,
                queryString,
                router
              })
            );
            if (!data?.success) {
              setErrorCode(data?.error_code);
            }
            clearTimeout(timeOutCheckLinked.current);
            clearInterval(timeIntervalCheckLinked.current);
          } else setErrorCode(res?.data?.error_code);
        }
      });
    }, 5000);
  };

  return (
    <section className="section section--payment section--payment-result overflow text-white">
      <div className="container canal-v">
        <div className="section__body">
          {isWalletFailed ? (
            <FailedWalletLink
              errorCode={errorCode}
              tel={dataConfigShopeePay?.[0]?.tel}
              name={dataConfigShopeePay?.[0]?.name}
              pkg={pkg}
              isTransactionCantBeTraced={isTransactionCantBeTraced}
              isNewCase={errorCode === undefined}
            />
          ) : (
            <ProcessWalletLink
              tel={dataConfigShopeePay?.[0]?.tel}
              name={dataConfigShopeePay?.[0]?.name}
              isNewCase={errorCode === undefined}
            />
          )}
        </div>
      </div>
    </section>
  );
});
export default WalletLink;
