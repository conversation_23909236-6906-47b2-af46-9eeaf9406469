import { ENABLE_SDK_GPT } from '@config/ConfigEnv';
import { Bling as GPT } from 'react-gpt';

const MockGPT = () => null;
MockGPT.configure = () => {};
MockGPT.syncCorrelator = () => {};
MockGPT.enableSingleRequest = () => {};
MockGPT.disableInitialLoad = () => {};
MockGPT.updateCorrelator = () => {};
MockGPT.render = () => {};
MockGPT.refresh = () => {};
MockGPT.clear = () => {};
MockGPT.destroySlots = () => {};
MockGPT.pubadsService = () => ({
  refresh: () => {},
  clear: () => {},
  display: () => {},
  enableSingleRequest: () => {},
  disableInitialLoad: () => {},
  addEventListener: () => {}
});

export const GPTWrapper = ENABLE_SDK_GPT === true || ENABLE_SDK_GPT === 'true' ? GPT : MockGPT;
