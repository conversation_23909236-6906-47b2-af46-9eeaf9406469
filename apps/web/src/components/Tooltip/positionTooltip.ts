import isEmpty from 'lodash/isEmpty';

export const positionTooltip = ({ refElement, isMobile, placement }: any) => {
  if (!isMobile || isEmpty(refElement?.current) || typeof window === 'undefined') return;
  const widthLefItem = refElement?.current?.offsetLeft;
  const widthItem = refElement?.current?.offsetWidth;
  const widthScreen = window.innerWidth;
  const widthRightItem = widthScreen - widthLefItem;
  if (widthLefItem + widthItem > widthScreen / 2 && widthLefItem + widthItem < widthRightItem) {
    return placement;
  }
  if (widthLefItem > widthScreen / 2) return `${placement}-end`;
  if (widthLefItem < widthScreen / 2) return `${placement}-start`;
};
