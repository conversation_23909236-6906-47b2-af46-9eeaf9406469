import React, { useMemo } from 'react';
import ItemArtist from './item-artist';
import ItemArtistChampion from './item-artist-champion';

const ListResultFinalRound = ({ data = [] }: any) => {
  const listChampions = useMemo(
    () => data.filter((item: any) => item.final_rank < 3 && item.final_rank > 0),
    [data]
  );
  const listImpressive = useMemo(() => data.filter((item: any) => item.final_rank >= 3), [data]);

  return (
    <>
      <div className="arrow-corner-top corner-gray">
        <div className="arrow-corner-bottom corner-gray">
          <div className="vote--box">
            <div className="vote__title">Kết quả vòng chung kết</div>
            <div className="final--result">
              {listChampions &&
                listChampions.length > 0 &&
                listChampions.map((item: any) => <ItemArtistChampion data={item} key={item.id} />)}
            </div>
          </div>
        </div>
      </div>
      <div className="block block--rapviet__impressive">
        <div className="arrow-corner-top corner-gray">
          <div className="arrow-corner-bottom corner-gray">
            <div className="block__header">
              <h3 className="block__title text-center">Ấn tượng Chung kết Rap Việt</h3>
            </div>
            <div className="block__body">
              {listImpressive && listImpressive.length > 0 && (
                <div className="card-group">
                  {listImpressive.map((item: any) => (
                    <ItemArtist itemInFinalResult data={item} key={item.id} />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ListResultFinalRound;
