import React, { useEffect, useState, useMemo, memo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  getListEpisodesOfRound,
  getListRoundsVoting,
  getDetailEpisodeOfRound,
  getListRatingOfEpisode,
  getFinalResult
} from '@actions/result-voting';
import { ACTION_TYPE } from '@actions/actionType';
import { isEmptyObject } from '@helpers/common';
import { DISPLAY_TYPE_ROUND } from '@constants/constants';
import Background from './background';
import Body from './body';
import Header from './header';

const RapVietResultVoting = () => {
  const dispatch = useDispatch();
  const dataResultVoting = useSelector((state: any) => state?.ResultVoting || {});

  const [showBody, setShowBody] = useState(false);
  const [roundActive, setRoundActive] = useState<any>({});
  const [episodeActive, setEpisodeActive] = useState<any>({});
  const listRounds = useMemo(
    () => (dataResultVoting ? dataResultVoting[ACTION_TYPE.GET_LIST_ROUNDS_VOTING] : []),
    [dataResultVoting]
  );

  const fullListEpisodes = useMemo(
    () =>
      dataResultVoting && dataResultVoting?.[ACTION_TYPE.GET_LIST_EPISODES_OF_ROUND]
        ? dataResultVoting?.[ACTION_TYPE.GET_LIST_EPISODES_OF_ROUND]
        : {},
    [dataResultVoting]
  );

  const listEpisodes = useMemo(
    () =>
      roundActive && dataResultVoting?.[ACTION_TYPE.GET_LIST_EPISODES_OF_ROUND]
        ? dataResultVoting?.[ACTION_TYPE.GET_LIST_EPISODES_OF_ROUND]?.[roundActive.id]
        : [],
    [dataResultVoting, roundActive]
  );
  const listArtists = useMemo(() => {
    if (dataResultVoting && dataResultVoting[ACTION_TYPE.GET_DETAIL_EPISODE_IN_ROUND]) {
      if (
        listRounds &&
        listRounds.length > 0 &&
        roundActive &&
        listRounds[0].id === roundActive.id
      ) {
        return dataResultVoting[ACTION_TYPE.GET_DETAIL_EPISODE_IN_ROUND][episodeActive.id];
      }
      if (episodeActive) {
        return dataResultVoting[ACTION_TYPE.GET_DETAIL_EPISODE_IN_ROUND][roundActive.id];
      }
    }
  }, [dataResultVoting, episodeActive, roundActive, listRounds]);

  const listRatings = useMemo(
    () =>
      episodeActive && dataResultVoting && dataResultVoting[ACTION_TYPE.GET_LIST_RATING_OF_EPISODE]
        ? dataResultVoting[ACTION_TYPE.GET_LIST_RATING_OF_EPISODE][episodeActive.id]
        : [],
    [dataResultVoting, episodeActive]
  );

  const listFinalResult = useMemo(
    () =>
      episodeActive && dataResultVoting && dataResultVoting[ACTION_TYPE.GET_FINAL_RESULT]
        ? dataResultVoting[ACTION_TYPE.GET_FINAL_RESULT][episodeActive.id]
        : [],
    [dataResultVoting, episodeActive]
  );

  useEffect(() => {
    setShowBody(true);
    getListRounds();
    return () => {
      setRoundActive({});
      setEpisodeActive({});
    };
  }, []);

  useEffect(() => {
    if (listRounds && listRounds.length > 0 && isEmptyObject(roundActive)) {
      let checkSetSuccess = false;
      let itemCheck: any = null;
      listRounds.forEach((item: any, i: any) => {
        if (item.status_enable) {
          itemCheck = item;
          checkSetSuccess = true;
        } else if (i === listRounds.length - 1 && checkSetSuccess === false) {
          // itemCheck = listRounds[0];
          const [firstRound = {}] = listRounds;
          itemCheck = firstRound;
        }
      });
      setRoundActive(itemCheck);
      const isRunning = checkIsRunning(itemCheck.started_at);
      if (isRunning) {
        dispatch(getListEpisodesOfRound({ campaignId: itemCheck.id }));
      }
    }
  }, [listRounds, roundActive]);

  useEffect(() => {
    if (
      listRounds &&
      listRounds.length > 0 &&
      !isEmptyObject(roundActive) &&
      listEpisodes &&
      listEpisodes.length > 0 &&
      isEmptyObject(episodeActive)
    ) {
      let checkSetSuccess = false;
      let itemCheck: any = null;
      listEpisodes.forEach((item: any, i: any) => {
        if (item.status_enable) {
          itemCheck = item;
          checkSetSuccess = true;
        } else if (i === listEpisodes.length - 1 && checkSetSuccess === false) {
          // itemCheck = listEpisodes[0];
          const [firstRound = {}] = listEpisodes;
          itemCheck = firstRound;
        }
      });
      setEpisodeActive(itemCheck);
      switch (itemCheck.display_type) {
        case DISPLAY_TYPE_ROUND.LIST_ARTIST: {
          const isRunning = checkIsRunning(itemCheck.started_at);
          const isFirstRound = roundActive.id === listRounds[0].id;
          if (isRunning) {
            dispatch(
              getDetailEpisodeOfRound({
                questionId: isFirstRound ? itemCheck.id : roundActive.id,
                index: isFirstRound ? 0 : 1
              })
            );
          }
          break;
        }
        case DISPLAY_TYPE_ROUND.RATING:
          dispatch(getListRatingOfEpisode({ questionId: itemCheck.id }));
          break;
        case DISPLAY_TYPE_ROUND.FINAL_RESULT:
          dispatch(getFinalResult({ questionId: itemCheck.id }));
          break;
        default:
          break;
      }
    }
  }, [listRounds, roundActive, listEpisodes, episodeActive]);

  const getListRounds = () => {
    dispatch(getListRoundsVoting());
  };

  const checkIsRunning = (startTime: any) => {
    const timeNow = Date.now() / 1000;
    return startTime <= timeNow;
  };

  const handleSelectRound = (round: any) => {
    setRoundActive(round);
    setEpisodeActive({});
    const isRunning = checkIsRunning(round.started_at);
    if (isRunning) {
      dispatch(getListEpisodesOfRound({ campaignId: round.id }));
    }
  };

  const handleSelectEpisode = (episode: any) => {
    setEpisodeActive(episode);
    const isRunning = checkIsRunning(episode.started_at);
    if (isRunning) {
      dispatch(getDetailEpisodeOfRound({ questionId: episode.id, index: 0 }));
    }
  };

  return (
    <div className="rocopa rocopa--rapviet-ss2">
      <Header
        handleSelectRound={handleSelectRound}
        roundActive={roundActive}
        listRounds={listRounds}
        episodeActive={episodeActive}
      />
      {showBody && (
        <Body
          listRounds={listRounds}
          roundActive={roundActive}
          listEpisodes={listEpisodes}
          episodeActive={episodeActive}
          onSelectEpisode={handleSelectEpisode}
          listArtists={listArtists}
          listRatings={listRatings}
          listFinalResult={listFinalResult}
          fullListEpisodes={fullListEpisodes}
        />
      )}
      <Background />
    </div>
  );
};

export default memo(RapVietResultVoting);
