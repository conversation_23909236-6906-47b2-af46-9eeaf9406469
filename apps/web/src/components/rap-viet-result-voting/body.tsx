import React, { useMemo, useLayoutEffect } from 'react';
import { DISPLAY_TYPE_ROUND } from '@constants/constants';
import ComingSoon from './coming-soon';
import ListResultsRounds from './list-results-rounds';
import ListVotingFinalRound from './list-voting-final-round';
import GroupBtnInteractive from './group-btn-interactive';
import ListResultFinalRound from './list-result-final-round';

const Body = ({
  listRounds,
  listArtists,
  listEpisodes = [],
  roundActive,
  episodeActive,
  onSelectEpisode,
  listRatings,
  listFinalResult,
  fullListEpisodes
}: any) => {
  const isWaitingRound = useMemo(() => {
    const timeNow = Date.now() / 1000;
    if (roundActive.started_at > timeNow) {
      return true;
    }
    return false;
  }, [roundActive]);

  const isWaitingEpisode = useMemo(() => {
    const timeNow = Date.now() / 1000;
    if (episodeActive.started_at > timeNow) {
      return true;
    }
    return false;
  }, [episodeActive]);

  const episodeNewest = useMemo(() => {
    if (listRounds && listRounds.length > 0 && fullListEpisodes) {
      const roundActive = listRounds.find((rd: any) => rd.status_enable);
      if (roundActive && roundActive.id && fullListEpisodes[roundActive.id]) {
        return fullListEpisodes[roundActive.id].find((eps: any) => eps.status_enable);
      }
      return {};
    }
    return {};
  }, [listRounds, fullListEpisodes]);

  const classNameBoard = useMemo(() => {
    const displayType = episodeActive && episodeActive.display_type;
    switch (displayType) {
      case DISPLAY_TYPE_ROUND.LIST_ARTIST:
        return isWaitingEpisode ? 'rapviet__comming-soon' : '';
      case DISPLAY_TYPE_ROUND.RATING:
        return 'final--round';
      case DISPLAY_TYPE_ROUND.FINAL_RESULT:
        return 'final--round result';
      default:
        return isWaitingEpisode ? 'rapviet__comming-soon' : '';
    }
  }, [episodeActive, isWaitingEpisode]);

  useLayoutEffect(() => {
    const elmChildren: any = document.getElementsByClassName('rapviet__body')[0];
    if (elmChildren) {
      const heightToSet = elmChildren.offsetHeight;
      const elmParent = document.getElementById('rapviet-section');
      if (elmParent) {
        elmParent.style.height = `${heightToSet}px`;
      }
    }
  }, [listArtists, roundActive, episodeActive, listRatings, listFinalResult]);

  const handleUIResultVoting = (epsActive: any) => {
    const displayType = epsActive && epsActive.display_type;
    switch (displayType) {
      case DISPLAY_TYPE_ROUND.LIST_ARTIST:
        return (
          <ListResultsRounds
            roundActive={roundActive}
            listRounds={listRounds}
            listEpisodes={listEpisodes}
            listArtists={listArtists}
            onSelectEpisode={onSelectEpisode}
            episodeActive={episodeActive}
          />
        );
      case DISPLAY_TYPE_ROUND.RATING:
        return <ListVotingFinalRound data={listRatings} />;
      case DISPLAY_TYPE_ROUND.FINAL_RESULT:
        return <ListResultFinalRound data={listFinalResult} />;
      default:
        return (
          <ListResultsRounds
            roundActive={roundActive}
            listRounds={listRounds}
            listEpisodes={listEpisodes}
            listArtists={listArtists}
            onSelectEpisode={onSelectEpisode}
            episodeActive={episodeActive}
          />
        );
    }
  };

  return (
    <div className="rocopa__body" id="rapviet-section">
      {isWaitingRound ? (
        <ComingSoon data={roundActive} />
      ) : (
        <div className="rapviet__body">
          <div className={`vote--board ${classNameBoard}`}>
            {handleUIResultVoting(episodeActive)}
          </div>
          {!isWaitingRound && !isWaitingEpisode && (
            <GroupBtnInteractive episodeNewest={episodeNewest} />
          )}
        </div>
      )}
    </div>
  );
};

export default Body;
