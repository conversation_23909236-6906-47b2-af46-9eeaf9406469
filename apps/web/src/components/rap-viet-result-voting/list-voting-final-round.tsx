import React from 'react';
import ItemVotingArtist from './item-voting-artist';

const ListVotingFinalRound = ({ data = [] }) => {
  const sortData = (arr: any) => arr.sort((a: any, b: any) => a.final_rank - b.final_rank);

  return (
    <div className="arrow-corner-top corner-gray">
      <div className="arrow-corner-bottom corner-gray">
        <div className="vote--box">
          <div className="vote__title"><PERSON><PERSON>nh chọn vòng chung kết</div>
          <div className="artist--vote__list">
            {sortData(data).map((item: any) => (
              <ItemVotingArtist key={item.id} data={item} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListVotingFinalRound;
