import React from 'react';
import { useVieRouter } from '@customHook';
import { setToast } from '@actions/app';
import { TEXT } from '@constants/text';
import { useDispatch } from 'react-redux';

const ItemArtistChampion = ({ data }: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const handleClickArtist = (slug: any) => {
    if (slug) {
      router.push(slug);
    } else {
      dispatch(setToast({ message: TEXT.MSG_ARTIST_NOT_UPDATE }));
    }
  };

  return (
    <div
      onClick={() => handleClickArtist(data.partner_slug)}
      className={`card card--rapviet-custom${data.final_rank === 1 ? ` champion` : ''}`}
    >
      <div className="card__thumbnail">
        <div
          className={`card__thumbnail-loader overflow circle ratio-1-1${
            data.final_rank === 1 ? '' : ` overflow`
          }`}
          title={data.name}
        >
          <img className="card__thumb-img" src={data.image_winner} alt={data.name} />
          <img className="card__thumb-bg" src={data.image_winner} alt={data.name} />
        </div>
        <button type="button" className="button-play absolute right bottom">
          <span className="icon icon--geometry-circle">
            <i className="vie vie-play-solid-rc" />
          </span>
        </button>
        {data.final_rank === 1 && (
          <div className="icon absolute left bottom">
            <img src="/assets/img/rap-viet/champion-cup.svg" alt="rap viet cup" />
          </div>
        )}
      </div>
      <div className="card__section">
        <div className="ranking">{data.final_rank === 1 ? 'Quán Quân' : 'Á Quân'}</div>
        <div className="name">{data.name}</div>
        <div className="team">{data.content}</div>
        <span className="rating">
          {new Intl.NumberFormat().format(data.total_vote || 0)} lượt bình chọn
        </span>
      </div>
    </div>
  );
};

export default ItemArtistChampion;
