import React, { memo, useMemo } from 'react';
import { DISPLAY_TYPE_ROUND } from '@constants/constants';
import {
  LINK_QRCODE_DOWNLOAD_APP,
  LINK_QRCODE_DOWNLOAD_APP_TV,
  LINK_TUTORIAL_DOWNLOAD_APP_MOBILE,
  LINK_TUTORIAL_DOWNLOAD_APP_TV
} from '@config/ConfigEnv';

const ContentHeader = ({ activeItem, isShowBoard }: any) => (
  <div className="rapviet__header">
    <div className="rapviet__logo">
      <img src="/assets/img/sprites/icon-rapviet.svg" alt="logo Rap Việt" />
    </div>
    <div className="rapviet__header-box">
      <div className="arrow-corner-top">
        <div className="arrow-corner-bottom">
          {isShowBoard && (
            <div className="rapviet__content">
              <div className="rapviet__title text-center">
                <PERSON>ài đặt ứng dụng VieON để tham gia game tương tác và giành giải thưởng lên đến
                <span className="highlights">{activeItem.content_award}</span> mỗi tập
              </div>
              <div className="download grid-x">
                <div className="download-box grid-x">
                  <span className="icon icon--geometry-circle">
                    <i className="vie vie-hand-phone-o-rc" />
                  </span>
                  <div className="info">
                    <div className="title">Trên điện thoại</div>
                    <span className="text">
                      Quét mã QR để tải App hoặc Xem hướng dẫn
                      <a
                        target="_blank"
                        rel="noreferrer"
                        href={LINK_TUTORIAL_DOWNLOAD_APP_MOBILE}
                        title="tại đây"
                      >
                        tại đây
                      </a>
                    </span>
                  </div>
                  <div className="qr-code">
                    <img src={LINK_QRCODE_DOWNLOAD_APP} alt="qr-code" />
                  </div>
                </div>
                <div className="download-box grid-x">
                  <span className="icon icon--geometry-circle">
                    <img src="/assets/img/rap-viet/icon-tv.svg" alt="icon tv" />
                  </span>
                  <div className="info">
                    <div className="title">Trên TV</div>
                    <span className="text">
                      Quét mã QR để tải App hoặc Xem hướng dẫn
                      <a
                        target="_blank"
                        rel="noreferrer"
                        href={LINK_TUTORIAL_DOWNLOAD_APP_TV}
                        title="tại đây"
                      >
                        tại đây
                      </a>
                    </span>
                  </div>
                  <div className="qr-code">
                    <img src={LINK_QRCODE_DOWNLOAD_APP_TV} alt="qr-code" />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  </div>
);

const ListRounds = ({ data, activeItem, onSelectItem }: any) => (
  <div className="rapviet__rounds">
    <ul className="tabs tabs--rounds">
      {data.map((item: any) => (
        <li
          onClick={() => onSelectItem(item)}
          className={`tabs-title${activeItem.id === item.id ? ` active` : ''}`}
          key={item.id}
        >
          <a title={item.name}>{item.name}</a>
        </li>
      ))}
    </ul>
  </div>
);

const Header = ({ listRounds, roundActive, episodeActive, handleSelectRound }: any) => {
  const isShowBoard = useMemo(() => {
    const timeNow = Date.now() / 1000;
    if (
      roundActive &&
      roundActive.started_at <= timeNow &&
      episodeActive &&
      (episodeActive.display_type === DISPLAY_TYPE_ROUND.FINAL_RESULT ||
        episodeActive.display_type === DISPLAY_TYPE_ROUND.RATING)
    ) {
      return true;
    }
    return false;
  }, [roundActive, episodeActive]);

  return (
    <div className="rocopa__header">
      <ContentHeader activeItem={roundActive} isShowBoard={isShowBoard} />
      {listRounds && (
        <ListRounds activeItem={roundActive} data={listRounds} onSelectItem={handleSelectRound} />
      )}
    </div>
  );
};

export default memo(Header);
