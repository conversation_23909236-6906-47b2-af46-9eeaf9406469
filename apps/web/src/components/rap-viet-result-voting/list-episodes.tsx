import React from 'react';

const ListEpisodes = ({ data, onSelectEpisode, episodeActive }: any) => (
  <div className="list-episodes">
    {data.map((item: any) => (
      <button
        key={item.id}
        onClick={() => onSelectEpisode(item)}
        type="button"
        className={`button button--rapviet hollow rounded${
          episodeActive.id === item.id ? ` active` : ''
        }`}
        title={item.content}
      >
        <span className="text">{item.content}</span>
      </button>
    ))}
  </div>
);

export default ListEpisodes;
