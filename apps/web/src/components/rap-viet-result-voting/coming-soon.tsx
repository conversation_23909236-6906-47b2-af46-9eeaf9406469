import React, { useMemo } from 'react';

const optionsTime: any = {
  hour: '2-digit',
  minute: '2-digit'
};
const optionsDate: any = { year: 'numeric', month: '2-digit', day: '2-digit' };

const ComingSoon = ({ data }: any) => (
  <div className="rapviet__body rapviet__comming-soon">
    <div className="arrow-corner-top corner-gray">
      <div className="arrow-corner-bottom corner-gray">
        <ContentComingSoon data={data} />
      </div>
    </div>
  </div>
);

export default ComingSoon;

export const ContentComingSoon = ({ data }: any) => {
  const timeStart = useMemo(() => new Date(data.started_at * 1000), [data]);
  return (
    <>
      <div className="title">Chương trình sẽ diễn ra</div>
      <div className="time">
        {`vào lúc ${timeStart.toLocaleTimeString(
          'vi-VN',
          optionsTime
        )} ngày ${timeStart.toLocaleDateString('vi-VN', optionsDate)}`}
      </div>
    </>
  );
};
