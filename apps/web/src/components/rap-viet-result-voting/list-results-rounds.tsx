import React, { useMemo } from 'react';
import ItemArtist from './item-artist';
import ListEpisodes from './list-episodes';
import { ContentComingSoon } from './coming-soon';

const ListResultsRound = ({
  listRounds,
  roundActive,
  listArtists,
  listEpisodes,
  onSelectEpisode,
  episodeActive
}: any) => {
  const isWaiting = useMemo(() => {
    const timeNow = Date.now() / 1000;
    if (episodeActive.started_at > timeNow) {
      return true;
    }
    return false;
  }, [episodeActive]);

  const isFirstRound = useMemo(() => {
    if (listRounds && listRounds.length > 0 && roundActive.id === listRounds[0].id) {
      return true;
    }
    return false;
  }, [listRounds, roundActive]);

  return (
    <div className="arrow-corner-top corner-gray">
      <div className="arrow-corner-bottom corner-gray">
        {listEpisodes && listEpisodes.length > 0 && isFirstRound && (
          <ListEpisodes
            data={listEpisodes}
            onSelectEpisode={onSelectEpisode}
            episodeActive={episodeActive}
          />
        )}
        {isWaiting ? (
          <ContentComingSoon data={episodeActive} />
        ) : (
          <div className="vote--box">
            <div className="vote__title">Các Rapper vào vòng trong</div>
            {listArtists && listArtists.length > 0 && (
              <div className="card-group">
                {listArtists.map((item: any) => (
                  <ItemArtist data={item} key={item.id} />
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ListResultsRound;
