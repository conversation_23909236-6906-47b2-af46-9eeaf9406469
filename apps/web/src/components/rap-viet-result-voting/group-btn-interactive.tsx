import React from 'react';
import VieLink from '@components/VieLink';
import { useVieRouter } from '@customHook';
import { onShareSocial } from '@helpers/common';
import { DOMAIN_WEB } from '@config/ConfigEnv';

const GroupBtnInteractive = ({ episodeNewest = {} }: any) => {
  const router = useVieRouter();

  const onShare = () => {
    onShareSocial({
      link: `${DOMAIN_WEB}${router.asPath}`,
      name: 'facebook'
    });
  };

  return (
    <div className="button-group child-auto">
      <button
        onClick={onShare}
        className="button button--rapviet hollow button--large"
        title="Chia sẻ"
        aria-label="Bấm để Chia sẻ"
      >
        <span className="text">Chia sẻ</span>
      </button>
      <VieLink href={episodeNewest.partner_slug || '/'}>
        <button
          className="button button--rapviet gradient-gold button--large"
          title="Xem tập mới nhất"
          aria-label="Bấm để Xem tập mới nhất"
        >
          <span className="text">Xem tập mới nhất</span>
        </button>
      </VieLink>
    </div>
  );
};

export default GroupBtnInteractive;
