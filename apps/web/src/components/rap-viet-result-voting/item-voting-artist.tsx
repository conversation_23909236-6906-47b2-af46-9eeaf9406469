import React from 'react';
import VieLink from '@components/VieLink';

const ItemVotingArtist = ({ data }: any) => {
  const handleChangeRange = (rangeChange: any) => {
    if (rangeChange > 0) {
      return (
        <div className="ranking-up">
          <span>{rangeChange}</span>
        </div>
      );
    }
    if (rangeChange < 0) {
      return (
        <div className="ranking-down">
          <span>{-rangeChange}</span>
        </div>
      );
    }
    return <div className="ranking-draw" />;
  };

  return (
    <div className="artist__vote">
      <div className="artist-info">
        <div className="ranking">
          <div className="number-order">{data.rank}</div>
          {handleChangeRange(data.rank_change)}
        </div>
        <div className="avatar--custom">
          <div className="avatar overflow circle gender">
            <img
              src={
                data.image_charts ||
                'https://static.vieon.vn/vieplay-image/avatar/2021/01/08/hxmy8qna_lee_seung_gi.webp'
              }
              alt={data.name}
            />
          </div>
          {data.highlight === 1 && (
            <span className="icon icon--geometry-circle icon--geometry-border">
              <img src="/assets/img/rap-viet/hat-champion.svg" alt="Champion" />
            </span>
          )}
        </div>
        <div className="grid-x">
          <div className="name">{data.name}</div>
          <div className="team">{data.content}</div>
          <div className="tags-group middle-v">
            <label className="tags tags--outline tags--box tags--outline-v">
              {data.percent_vote}%
            </label>
            <span className="tags tags--outline tags--box">
              {data.total_vote > 1000 ? `${data.total_vote / 1000}k` : data.total_vote} bình chọn
            </span>
          </div>
        </div>
      </div>
      <VieLink href={data.partner_slug}>
        <a className="button hollow" title="Xem Video">
          <span className="icon icon--small">
            <i className="vie vie-play-c-light" />
          </span>
          <span className="text">Xem Video</span>
        </a>
      </VieLink>
    </div>
  );
};

export default ItemVotingArtist;
