import React, { memo } from 'react';
import { useVieRouter } from '@customHook';
import { setToast } from '@actions/app';
import { TEXT } from '@constants/text';
import { useDispatch } from 'react-redux';

const ItemArtist = ({ data, itemInFinalResult }: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();

  const handleClickArtist = (artist: any) => {
    if (artist.status_enable) {
      if (artist.partner_slug) {
        router.push(artist.partner_slug);
      } else {
        dispatch(setToast({ message: TEXT.MSG_ARTIST_NOT_UPDATE }));
      }
    }
  };

  const handleImgAvatar = (item: any, isItemInFinalRound: any) => {
    if (item.status_enable) {
      if (isItemInFinalRound) {
        return item.image_winner || '/assets/img/rap-viet/avt-default.svg';
      }
      return item.image || '/assets/img/rap-viet/avt-default.svg';
    }
    return '/assets/img/rap-viet/avt-default.svg';
  };

  return (
    <div
      onClick={() => handleClickArtist(data)}
      className={`card card--rapviet-custom${data.status_enable ? '' : ` disabled`}`}
    >
      <div className="card__thumbnail">
        <div className="card__thumbnail-loader circle ratio-1-1 overflow" title={data.name}>
          <img
            className="card__thumb-img"
            src={handleImgAvatar(data, itemInFinalResult)}
            alt={data.name}
          />
        </div>
        {data.status_enable && (
          <button type="button" className="button-play absolute right bottom">
            <span className="icon icon--geometry-circle">
              <i className="vie vie-play-solid-rc" />
            </span>
          </button>
        )}
        {data.status_enable && data.highlight === 1 && (
          <span className="icon icon--geometry-circle icon--geometry-border large">
            <img src="/assets/img/rap-viet/hat-champion.svg" alt="Champion" />
          </span>
        )}
      </div>
      <div className="card__section">
        {/* <h4 className="card__title text-center" title={data.name} tabIndex={0}> */}
        <h4 className="card__title text-center" title={data.name}>
          {data.status_enable ? data.name : 'Rapper'}
        </h4>
        <p className="card__episode text text-center">
          {data.status_enable ? data.content : data.question_content}
        </p>
      </div>
    </div>
  );
};

export default memo(ItemArtist);
