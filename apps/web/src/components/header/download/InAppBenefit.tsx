import React, { useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import LocalStorage from '@config/LocalStorage';
import isEmpty from 'lodash/isEmpty';
import { TEXT } from '@constants/text';
import { useOnClickOutside } from 'src/customHook';
import TrackingMWebToApp from '@tracking/functions/TrackingMWebToApp';
import { VALUE } from '@config/ConfigSegment';
import ConfigLocalStorage from '@config/ConfigLocalStorage';

const InAppBenefit = ({ isOpenBenefit, setStatusBenefitInApp }: any) => {
  const ref = useRef<any>(null);
  const { inAppBenefit } = useSelector((state: any) => state.App?.webConfig?.mwebToApp || {});

  const { msgList }: any = inAppBenefit;

  // Call hook passing in the ref and a function to call on outside click
  useOnClickOutside(ref, () => setStatusBenefitInApp && setStatusBenefitInApp());

  useEffect(() => {
    if (typeof window !== 'undefined' && !isEmpty(msgList) && isOpenBenefit) {
      if (getIndexBenefit < msgList?.length - 1) {
        ConfigLocalStorage.set(LocalStorage.MESSAGE_BENEFIT_INDEX, parseInt(getIndexBenefit + 1));
      } else {
        ConfigLocalStorage.set(LocalStorage.MESSAGE_BENEFIT_INDEX, 0);
      }
      TrackingMWebToApp.inAppNewsLoad({
        flowName: VALUE.TRIGGER_BY_BENEFIT,
        data: msgList[ConfigLocalStorage.get(LocalStorage.MESSAGE_BENEFIT_INDEX) as string]?.msg
      });
    }
  }, []);

  const handleClick = () => {
    if (typeof setStatusBenefitInApp === 'function') setStatusBenefitInApp();
  };
  const getIndexBenefit: any = parseInt(
    ConfigLocalStorage.get(LocalStorage.MESSAGE_BENEFIT_INDEX) as string
  );

  if (!isOpenBenefit) return null;

  return (
    <div
      className="tooltip blue tooltip-arrow-available round flown-bottom-left animate-fade-in size-mw-220 animate-fade-out-7000 p-t"
      ref={ref}
      onTouchStart={handleClick}
    >
      <div className="tooltip__wrap flex-box align-center relative line-height-1 text-white shadow-h-v4-b4-a25">
        <span className="text-14 text-bold p-b1">{TEXT.DOWNLOAD_APP}</span>
        <span
          className="text text-14 text-center"
          dangerouslySetInnerHTML={{ __html: msgList[getIndexBenefit]?.msg }}
        />
      </div>
    </div>
  );
};

export default InAppBenefit;
