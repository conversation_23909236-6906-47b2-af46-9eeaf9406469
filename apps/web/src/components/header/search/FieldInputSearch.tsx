import React from 'react';
import Icon from '@components/basic/Icon/Icon';
import CustomInput from '@components/basic/Input/CustomInput';
import { EL_ID } from '@constants/constants';

const FieldInputSearch = (props: any) => {
  const {
    valInput,
    handleOnChangeSearchInput,
    handleOnInputSearch,
    handleKeyDown,
    setCustomInput,
    handleOnFocusInput,
    onClickButtonClosed
  } = props;
  return (
    <div className="field field-for-dark field--search outline">
      <div className="grid-x">
        <div className="cell auto">
          <div className="field__input">
            <CustomInput
              id={EL_ID.SEARCH_INPUT}
              value={valInput || ''}
              placeholder="Tên phim, show, diễn viên, kênh TV"
              handleOnChangeSearchInput={handleOnChangeSearchInput}
              handleOnInputSearch={handleOnInputSearch}
              handleKeyDown={handleKeyDown}
              setCustomInput={setCustomInput}
              handleOnFocusInput={handleOnFocusInput}
              isAutoFocus
            />
          </div>
        </div>
        <div className="cell shrink">
          <button
            className="button button--custom-small-up-36-12"
            type="submit"
            title="Search"
            onClick={valInput ? onClickButtonClosed : null}
          >
            {valInput ? (
              <Icon spClass="icon--tiny" iClass="vie-times-medium animate-fade-in" />
            ) : (
              <Icon spClass="icon--small" iClass="vie-find-o-rc-medium" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default React.memo(FieldInputSearch);
