import React from 'react';
import Image from '../../basic/Image/Image';

const SearchItem = ({
  data,
  itemsRef,
  activeID,
  handleClickSearchItems,
  handleIconSearch
}: any) => (
  <div
    className={`search-suggestion__item${data.id === activeID ? ' active' : ''}`}
    id={data.id}
    ref={(el: any) => (itemsRef.current[data.id] = el)}
  >
    <a title={data?.title} onClick={() => handleClickSearchItems({ item: data })}>
      <div className="search-suggestion__thumb">
        <Image src={data?.images?.thumbnail_v4 || data?.images?.thumbnail} alt={data?.title} />
      </div>
      <div className="search-suggestion__content">{data?.title}</div>
      <div className="search-suggestion__icon">{handleIconSearch({ type: data.type })}</div>
    </a>
  </div>
);

export default SearchItem;
