import React from 'react';

const PaneIntroPackages = ({ data }: any) => (
  <div
    className="pane pane--user pane--user-privilege pane--dark absolute middle-h animate-fade-right animate-fade-in gold top-full layer-max"
    style={{ display: 'block' }}
  >
    <div className="pane-container shadow">
      <div className="pane__body">
        <div className="pane__title p-b2">
          <div className="text text-16 text-medium">{data?.title}</div>
        </div>
        {(data?.items || []).map((item: any, i: any) => (
          <div className="pane__item" key={i}>
            <img src={item?.icon} alt="icon promotion" />
            <span className="text text-14 padding-small-up-left-10 p-y1">{item?.description}</span>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export default PaneIntroPackages;
