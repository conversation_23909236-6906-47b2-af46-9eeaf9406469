import classNames from 'classnames';
import React from 'react';
import { POSITION } from '@constants/constants';

const PaneNav = ({ content, position }: any) => (
  <div
    className={classNames(
      'pane pane--dark absolute animate-fade-right animate-fade-in is-open top-full layer-max',
      POSITION_CLASS[position || POSITION.CENTER]
    )}
  >
    <div className="pane-container shadow px-3 lg:px-8 py-2 lg:py-4">
      <div className="pane__body size-w-max">
        <span className="text text-14 text-white">{content}</span>
      </div>
    </div>
  </div>
);

const POSITION_CLASS = {
  [POSITION.CENTER]: 'middle-h',
  [POSITION.BOTTOM_RIGHT]: 'right'
};

export default React.memo(PaneNav);
