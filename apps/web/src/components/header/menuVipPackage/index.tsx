import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import PaneIntroPackages from '../pane/PaneIntroPackages';
import Button from '@components/basic/Buttons/Button';

const MenuVipPackage = ({ isOffPaneNav, onClickOpenPayment, introPackages, buttonTitle }: any) => {
  const [isHoverPane, setHoverPane] = useState(false);
  const { isMobile } = useSelector((state: any) => state?.App);

  const handleMouseEnter = () => {
    setHoverPane(true);
  };

  const handleMouseLeave = () => {
    setHoverPane(false);
  };

  return (
    <div
      className="package relative size-h-full align-middle flex-box"
      onMouseEnter={!isMobile ? handleMouseEnter : undefined}
      onMouseLeave={!isMobile ? handleMouseLeave : undefined}
    >
      <Button
        title={buttonTitle}
        subTitle={buttonTitle}
        onClick={onClickOpenPayment}
        size={isMobile ? 'small' : 'medium'}
        customizeClass="bg-gradient-to-r from-[#DA9E1C] to-[#ECBD57] rounded-[.25rem]"
        textClass="!text-[.75rem] md:!text-[1rem] font-bold !text-vo-gray-800 uppercase"
      />
      {isHoverPane && !isOffPaneNav && <PaneIntroPackages data={introPackages} />}
    </div>
  );
};

export default React.memo(MenuVipPackage);
