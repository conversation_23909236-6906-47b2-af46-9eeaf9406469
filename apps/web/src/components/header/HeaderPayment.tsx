import React, { useCallback, useMemo } from 'react';
import { useVieRouter } from '@customHook';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import { PAGE, POPUP, VieON, VieON_TEL } from '@constants/constants';
import { openPopup } from '@actions/popup';
import Logo from '../basic/Logo/Logo';
import styles from './HeaderPayment.module.scss';
import Step from '../payment/step/Step';
import ConfigLocalStorage from '@config/ConfigLocalStorage';

const HeaderPayment = React.memo(() => {
  const router = useVieRouter();
  const { pathname } = useVieRouter() || {};
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const { isGlobal, restricted } = useSelector((state: any) => state?.App?.geoCheck);
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const { isLoginPaymentSuccess } = useSelector((state: any) => state?.Payment);

  const isRentalContent = useMemo(() => pathname?.includes(PAGE.RENTAL_CONTENT), [pathname]);
  const isVoucher = useMemo(() => pathname?.includes(PAGE.VOUCHER), [pathname]);
  const inApp = useMemo(() => pathname?.includes('in-app'), [pathname]);
  const dispatch = useDispatch();

  const onClickLogo = useCallback(
    (e: any) => {
      e.preventDefault();
      if (
        router.pathname.includes(PAGE.PAYMENT) &&
        (profile?.id || isLoginPaymentSuccess) &&
        !isGlobal &&
        !window.location.href.includes('ket-qua')
      ) {
        dispatch(
          openPopup({
            name: POPUP.NAME.SURVEY_PAYMENT
          })
        );
        ConfigLocalStorage.set('isHomeClick', true);
        return;
      }
      if (inApp) {
        router.push({ pathname: PAGE.ZALOPAY });
      } else {
        router.push('/');
      }
    },
    [inApp, router]
  );

  const { linkTo, linkContent } = useMemo(
    () => ({
      linkTo: isGlobal ? `mailto:${VieON.SUPPORT_EMAIL}` : `tel:(+84)${VieON_TEL}`,
      linkContent:
        isGlobal && !restricted ? VieON.SUPPORT_EMAIL : `Hotline Miễn Phí: ${VieON.HOTLINE}`
    }),
    [isGlobal]
  );

  return (
    <header className={classNames(styles.base, '!bg-[#111] header-payment')}>
      <div
        className="flex items-center justify-between px-3 md:px-[3.02083vw] h-10 md:min-h-14 md:h-auto relative my-[10px]"
        id="topBar"
      >
        <div className="flex-shrink-0 z-10">
          <Logo onClickLogo={onClickLogo} />
        </div>
        {!isGlobal && !restricted && (
          <div className="absolute left-0 right-0 flex justify-center items-center h-full">
            <Step
              pathname={pathname}
              isVoucher={isVoucher}
              isRentalContent={isRentalContent}
              isMobile={isMobile}
            />
          </div>
        )}
        <div className="flex-shrink-0 z-10" id="topBarRight">
          <a className="!text-xs md:!text-sm text-white" href={linkTo} title={linkContent}>
            {linkContent}
          </a>
        </div>
      </div>
    </header>
  );
});

export default HeaderPayment;
