import React, { useState } from 'react';
import Link from 'next/link';
import { TEXT } from '@constants/text';
import { PAGE } from '@constants/constants';
import { useSelector } from 'react-redux';
import TrackingPayment from '@tracking/functions/payment';
import PaneNav from '../pane/PaneNav';

const MenuCode = ({ isOffPaneNav }: any) => {
  const [isHoverPane, setHoverPane] = useState(false);
  const { isMobile } = useSelector((state: any) => state?.App);
  const trackingPayment = new TrackingPayment();

  const handleMouseEnter = () => {
    setHoverPane(true);
  };

  const handleMouseLeave = () => {
    setHoverPane(false);
  };

  return (
    <div
      className="relative size-h-full align-middle flex-box"
      onMouseEnter={!isMobile ? handleMouseEnter : undefined}
      onMouseLeave={!isMobile ? handleMouseLeave : undefined}
      onClick={() => {
        trackingPayment.promotionButtonSelected();
      }}
    >
      <Link href={PAGE.VOUCHER}>
        <button className="voucher text-[1rem] text-white hover:text-vo-green" type="button">
          {TEXT.VOUCHER_VIEON}
        </button>
      </Link>
      {isHoverPane && !isOffPaneNav && <PaneNav content={TEXT.CONTENT_NAVIGATION.CODE} />}
    </div>
  );
};

export default React.memo(MenuCode);
