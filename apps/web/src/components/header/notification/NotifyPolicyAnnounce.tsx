import React from 'react';
import { getLiveTime } from '@helpers/common';
import { ACTION_NOTIFY, POPUP } from '@constants/constants';
import classNames from 'classnames';
import { openPopup } from '@actions/popup';
import { useDispatch } from 'react-redux';
import { segmentEvent } from '@tracking/TrackingSegment';
import { NAME, PROPERTY } from '@config/ConfigSegment';
import { getDetailPolicyAnnounce } from '@actions/notification';
import NotificationApi from '@apis/cm/NotificationApi';
import { setToast } from '@actions/app';
import { TEXT } from '@constants/text';
import ConfigImage from '@config/ConfigImage';
import Styles from './Styles.module.scss';
import Image from '../../basic/Image/Image';

const NotifyPolicyAnnounce = ({
  item,
  closeMenu,
  handleClickItemNoti,
  readNoti,
  itemPosition,
  page
}: any) => {
  const dispatch = useDispatch();
  const {
    title,
    image,
    id,
    content,
    createdAt,
    isRead,
    layoutTypeImage,
    layoutTypeInfo,
    entityId
  } = item;
  const liveTimeStr = getLiveTime(createdAt, true);

  const onClickItem = (e: any, params: any) => {
    const { entityId, notifyId } = params;
    segmentEvent(NAME.NOTIFICATION_ITEM_SELECTED, {
      [PROPERTY.CURRENT_PAGE]: window.location.href,
      [PROPERTY.ITEM_POSITION]: itemPosition || 0,
      [PROPERTY.ITEM_NAME]: item?.title || ''
    });
    if (!isRead) {
      readNoti(e, ACTION_NOTIFY.READ, id, page);
    }
    if (typeof closeMenu === 'function') {
      closeMenu();
    }
    if (typeof handleClickItemNoti === 'function') {
      handleClickItemNoti();
    }
    if (entityId) {
      NotificationApi.checkPolicyConfirmation(entityId).then((res: any) => {
        if (res.success) {
          if (res?.isPolicyDefined) {
            dispatch(openPopup({ name: POPUP.NAME.POLICY_ANNOUNCE_SYNC, entityId }));
            return;
          }
          dispatch(getDetailPolicyAnnounce(entityId));
          return dispatch(
            openPopup({ name: POPUP.NAME.POLICY_ANNOUNCE_CONFIRM, entityId, notifyId })
          );
        }
        dispatch(setToast({ message: res.data?.error?.message || TEXT.MSG_ERROR }));
        return;
      });
    }
  };

  return (
    <div className={classNames(Styles.NotifyItemContainer, !isRead && Styles.NotifyItemUnread)}>
      <div
        className={classNames(
          Styles.NotifyItemBlock,
          layoutTypeInfo ? Styles.NotifyItemLayoutIcon : Styles.NotifyItemLayoutThumb
        )}
        onClick={(e) => onClickItem(e, { entityId, notifyId: id })}
      >
        {layoutTypeImage && (
          <div className={Styles.NotifyItemThumb}>
            <Image src={image || ConfigImage.defaultBanner16x9} alt={title} />
          </div>
        )}
        {layoutTypeInfo && (
          <span className={Styles.NotifyItemIcon}>
            <Image src={image || ConfigImage.defaultBanner16x9} alt={title} />
          </span>
        )}
        <div className={classNames(Styles.NotifyItemContent, layoutTypeImage && 'py-1')}>
          <div className={classNames(Styles.NotifyItemContentBlock)}>
            {title && (
              <h4 className={Styles.NotifyItemContentHeading} title={title}>
                {title}
              </h4>
            )}
            <p className={Styles.NotifyItemContentParagraphs} title={content}>
              {content}
            </p>
          </div>
          <div className={Styles.NotifyCreationTime}>{liveTimeStr}</div>
        </div>
      </div>
    </div>
  );
};

export default NotifyPolicyAnnounce;
