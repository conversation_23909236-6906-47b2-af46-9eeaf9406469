import React from 'react';
import { getLiveTime } from '@helpers/common';
import { trackingNotification } from '@actions/notification';
import { segmentEvent } from '@tracking/TrackingSegment';
import { NAME, PROPERTY } from '@config/ConfigSegment';
import VieLink from '@components/VieLink';
import { ACTION_NOTIFY, ID, PAGE } from '@constants/constants';
import classNames from 'classnames';
import Image from '../../basic/Image/Image';
import ConfigImage from '@config/ConfigImage';

const NotificationItem = ({
  item,
  readNoti,
  page,
  itemPosition,
  closeMenu,
  handleClickItemNoti
}: any) => {
  const {
    title,
    image,
    id,
    seo,
    content,
    createdAt,
    isRead,
    href,
    noti_type,
    gta_target_screen,
    layoutTypeImage,
    layoutTypeInfo
  } = item;
  const liveTimeStr = getLiveTime(createdAt, true);

  const onClickItem = (e: any, href: any) => {
    segmentEvent(NAME.NOTIFICATION_ITEM_SELECTED, {
      [PROPERTY.CURRENT_PAGE]: window.location.href,
      [PROPERTY.ITEM_POSITION]: itemPosition || 0,
      [PROPERTY.ITEM_NAME]: item?.title || ''
    });
    if (!item?.isRead) {
      readNoti(e, ACTION_NOTIFY.READ, id, page);
    }
    trackingNotification(id);
    if (typeof closeMenu === 'function') {
      closeMenu();
    }
    if (href && typeof handleClickItemNoti === 'function') {
      handleClickItemNoti();
    }
  };

  return (
    <div className="pane__item">
      <div
        className={classNames(
          'card card--notify card--horizontal rounded-[4px] !border border-solid border-[#9B9B9B]/30 !p-0',
          !isRead ? 'unread' : '',
          'before:!left-[calc(100%+9px)] '
        )}
      >
        <div className={classNames(layoutTypeImage ? 'card__thumbnail !mr-0' : 'pl-3 py-1')}>
          <VieLink
            href={
              noti_type === 1 && gta_target_screen === 1
                ? `${PAGE.PROFILE}/${ID.LOYALTY_POINT}`
                : href || seo?.url || ''
            }
            as={
              noti_type === 1 && gta_target_screen === 1
                ? `${PAGE.PROFILE}/${ID.LOYALTY_POINT}`
                : seo?.url || href || ''
            }
          >
            <a
              className={classNames(
                'ratio-16-9 relative overflow !h-full',
                !href && 'cursor-default'
              )}
              title={title}
              onClick={(e) => onClickItem(e, href)}
            >
              {layoutTypeImage && (
                <Image
                  className="card__thumbnail-img rounded-l-[4px]"
                  src={image || ConfigImage.defaultBanner16x9}
                  alt={title}
                />
              )}
              {layoutTypeInfo && (
                <span className="flex flex-none w-5 h-5 p-[1.5px]">
                  <Image src={image || ConfigImage.defaultBanner16x9} alt={title} notWebp />
                </span>
              )}
            </a>
          </VieLink>
        </div>
        <div className="py-1 px-3">
          <VieLink
            href={
              noti_type === 1 && gta_target_screen === 1
                ? `${PAGE.PROFILE}/${ID.LOYALTY_POINT}`
                : href || seo?.url || ''
            }
            as={
              noti_type === 1 && gta_target_screen === 1
                ? `${PAGE.PROFILE}/${ID.LOYALTY_POINT}`
                : seo?.url || ''
            }
          >
            <a
              className={classNames(!href && 'cursor-default')}
              title={title}
              onClick={(e) => onClickItem(e, href)}
            >
              <h4
                className="!leading-[125%] line-clamp-1 !text-sm md:!text-[1rem]"
                data-line-clamp="1"
                title={title}
              >
                {title}
              </h4>
              <p
                className="text-[#9B9B9B] mt-[1.5px] line-clamp-2 !text-xs md:!text-sm h-[2rem] md:h-[40px]"
                data-line-clamp="2"
                title={content}
              >
                {content}
              </p>
              <div className="!text-[11px] md:!text-sm text-[#9B9B9B] pt-2">{liveTimeStr}</div>
            </a>
          </VieLink>
        </div>
      </div>
    </div>
  );
};

export default NotificationItem;
