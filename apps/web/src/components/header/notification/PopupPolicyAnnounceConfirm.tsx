import React, { useCallback, useEffect, useRef, useState } from 'react';
import { openPopup } from '@actions/popup';
import { useDispatch, useSelector } from 'react-redux';
import Modal from '@components/basic/Modal';
import classNames from 'classnames';
import { confirmDetailPolicyAnnounce } from '@actions/notification';
import Styles from './Styles.module.scss';
import { TEXT } from '@constants/text';

const PopupPolicyAnnounceConfirm = ({
  entityId,
  btnSkip,
  btnConfirm,
  notHasCloseBtn,
  notifyId
}: any) => {
  const dispatch = useDispatch();
  const { dataContentPolicy } = useSelector((state: any) => state?.Notification) || {};
  const { content, checkbox_content: checkboxContent, title } = dataContentPolicy || {};

  const [isChecked, setIsChecked] = useState(true);
  const [scrollState, setScrollState] = useState({
    hasScrollBarUi: false,
    hasScrolledTop: false,
    hasScrolledBottom: false
  });
  const contentRef = useRef<any>(null);

  const onChangeItem = useCallback((e: any) => {
    setIsChecked(e.target.checked);
  }, []);

  const handleConfirmAnnounce = useCallback(() => {
    dispatch(
      confirmDetailPolicyAnnounce({
        entityId,
        notifyId,
        status: 1
      })
    );
    handleClosePopup();
  }, [dispatch, entityId, notifyId]);

  const handleSkipAnnounce = useCallback(() => {
    dispatch(
      confirmDetailPolicyAnnounce({
        entityId,
        notifyId,
        status: 0
      })
    );
    handleClosePopup();
  }, [dispatch, entityId, notifyId]);

  const handleScroll = useCallback((e: any) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    setScrollState({
      hasScrollBarUi: scrollHeight > clientHeight,
      hasScrolledTop: scrollTop === 0,
      hasScrolledBottom: scrollTop + clientHeight + 10 >= scrollHeight
    });
  }, []);

  const handleClosePopup = () => {
    dispatch(openPopup());
  };

  const renderHeader = () => (
    <div className={Styles.NotifyAnnounceModalTitle}>
      <h2>{title || TEXT.CONFIRM_POLICY}</h2>
    </div>
  );

  const renderBody = () => {
    const { hasScrollBarUi, hasScrolledTop, hasScrolledBottom } = scrollState;
    const contentUi = classNames(
      Styles.NotifyAnnounceModalBlock,
      hasScrollBarUi && Styles.NotifyAnnounceModalBlockScrollBar,
      !hasScrolledTop && Styles.NotifyAnnounceModalBlockOverlayTop,
      !hasScrolledBottom && Styles.NotifyAnnounceModalBlockOverlayBottom
    );
    return (
      <div className={contentUi} ref={contentRef}>
        <div dangerouslySetInnerHTML={{ __html: content }} />
      </div>
    );
  };

  const renderFooter = () => (
    <div className={Styles.NotifyAnnounceModalActionGroup}>
      <label className="checkbox-custom" htmlFor={`pa-cb-${entityId}`}>
        <input
          type="checkbox"
          id={`pa-cb-${entityId}`}
          onChange={(e) => {
            onChangeItem(e);
          }}
          checked={isChecked}
          name={`pa-cb-${entityId}`}
        />
        <span className="checkmark" />
        <span className="text text-muted">
          {checkboxContent || TEXT.READ_UNDERSTOOD_AGREED_POLICY}
        </span>
      </label>
      <div className={classNames(Styles.NotifyAnnounceModalActionBtnGroup)}>
        <button
          className={classNames(Styles.NotifyAnnounceModalActionBtnSecondary)}
          title={btnSkip}
          onClick={handleSkipAnnounce}
          type="button"
        >
          {btnSkip}
        </button>
        <button
          className={classNames(Styles.NotifyAnnounceModalActionBtnPrimary)}
          title={btnConfirm}
          onClick={handleConfirmAnnounce}
          disabled={!isChecked}
          type="button"
        >
          {btnConfirm}
        </button>
      </div>
    </div>
  );
  useEffect(() => {
    const currentRef = contentRef.current;
    if (currentRef) {
      handleScroll({ currentTarget: currentRef });
      currentRef.addEventListener('scroll', handleScroll);

      return () => {
        currentRef.removeEventListener('scroll', handleScroll);
      };
    }
    return;
  }, [handleScroll]);

  return (
    <Modal
      renderHeader={renderHeader}
      renderBody={renderBody}
      renderFooter={renderFooter}
      notClosedButton={notHasCloseBtn}
      modalNewUi
      modalUiContainer={Styles.NotifyAnnounceModalContainer}
      modalUiWrapper={Styles.NotifyAnnounceModalWrapper}
      bodyClass={Styles.NotifyAnnounceModalContent}
    />
  );
};

export default PopupPolicyAnnounceConfirm;
