import React, { useEffect, useState, useRef } from 'react';
import { segmentEvent } from '@tracking/TrackingSegment';
import { NAME, PROPERTY } from '@config/ConfigSegment';
import { POSITION } from '@constants/constants';
import { TEXT } from '@constants/text';
import isEmpty from 'lodash/isEmpty';
import { getTriggerConfig } from '@actions/appConfig';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import NewIcon from '@components/basic/Icon/NewIcon';
import PaneNav from '../pane/PaneNav';
import NotificationList from './NotificationList';

const Notification = ({
  listDataAllPage,
  readNoti,
  isMobile,
  closeNotify,
  closeMenu,
  onLoadMore,
  unread
}: any) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const triggerConfig = useSelector((state: any) => state?.AppConfig?.trigger) || {};
  const [state, setState] = useState({
    isOpen: false,
    isLoadData: false
  });
  const [isHoverPane, setHoverPane] = useState(false);
  const [isOffPaneNav, setOffPaneNav] = useState(false);
  const myRef = useRef<any>(null);
  const ref = useRef<any>(null);

  useEffect(
    () => () => {
      myRef?.current?.removeEventListener('scroll', loadMore);
    },
    []
  );

  useEffect(() => {
    if (router.asPath) {
      setState((prevState) => ({
        ...prevState,
        isOpen: false
      }));
    }
  }, [router?.asPath]);

  useEffect(() => {
    if (state.isOpen) {
      myRef?.current?.removeEventListener('scroll', loadMore);
      myRef?.current?.addEventListener('scroll', loadMore);
    }
  }, [state.isOpen]);

  const loadMore = () => {
    const temp = myRef?.current;
    if (temp && temp.clientHeight + temp.scrollTop > temp.scrollHeight - 50 && state.isOpen) {
      myRef?.current?.removeEventListener('scroll', loadMore);
      onLoadMore().then(() => {
        myRef?.current?.addEventListener('scroll', loadMore);
      });
    }
  };

  const handleClickNotify = () => {
    if (isEmpty(triggerConfig)) {
      dispatch(getTriggerConfig());
    }
    segmentEvent(NAME.NOTIFICATION_ICON_SELECTED, {
      [PROPERTY.CURRENT_PAGE]: window.location.href
    });
    setState({ ...state, isOpen: !state.isOpen });

    if (!isMobile) setOffPaneNav(!isOffPaneNav);
  };

  const handleCloseContentItem = () => {
    const notifyTooltip = document.querySelector('.tooltip-container.notify-option-list');
    if (!notifyTooltip && state.isOpen) {
      if (!isMobile) setOffPaneNav(false);
      setState((prevState) => ({
        ...prevState,
        isOpen: false
      }));
    }
  };

  const handleMouseEnter = () => {
    setHoverPane(true);
  };

  const handleMouseLeave = () => {
    setHoverPane(false);
  };

  const handleClickItemNoti = () => {
    setState({ ...state, isOpen: !state.isOpen });
    setHoverPane(false);
    setOffPaneNav(false);
  };

  const firstPage = listDataAllPage?.[1];
  const isNotify = firstPage && firstPage.length > 0;

  if (isMobile) {
    return (
      <nav className="nav nav--right nav--for-mobile animate-slide-in-right">
        <div className={`nav__wrap ${isMobile ? 'p-x' : ''}`}>
          <NotificationList
            ref={myRef}
            isNotify={isNotify}
            readNoti={readNoti}
            listDataAllPage={listDataAllPage}
            firstPage={firstPage}
            isMobile={isMobile}
            closeMenu={closeMenu}
            closeNotify={closeNotify}
          />
        </div>
      </nav>
    );
  }

  return (
    <li
      className="menu__item size-h-full align-middle flex-box"
      onMouseEnter={!isMobile ? handleMouseEnter : undefined}
      onMouseLeave={!isMobile ? handleMouseLeave : undefined}
      ref={ref}
    >
      <button
        className="button button--notify px-0 relative !text-[1.125rem] text-vo-gray-200 hover:text-vo-green"
        onClick={handleClickNotify}
        type="button"
      >
        {unread ? (
          <span className="notify__counter">
            <span>{unread > 9 ? '9+' : unread}</span>
          </span>
        ) : (
          ''
        )}
        <NewIcon iconName="vie-bell-o-rc" />
      </button>
      {state.isOpen && (
        <NotificationList
          ref={myRef}
          isNotify={isNotify}
          readNoti={readNoti}
          listDataAllPage={listDataAllPage}
          firstPage={firstPage}
          isMobile={isMobile}
          onCloseContentItem={handleCloseContentItem}
          wrapperRef={ref}
          closeMenu={closeMenu}
          handleClickItemNoti={handleClickItemNoti}
        />
      )}
      {isHoverPane && !isOffPaneNav && (
        <PaneNav position={POSITION.BOTTOM_RIGHT} content={TEXT.CONTENT_NAVIGATION.NOTIFICATION} />
      )}
    </li>
  );
};

export default Notification;
