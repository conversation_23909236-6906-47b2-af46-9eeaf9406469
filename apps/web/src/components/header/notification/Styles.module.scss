.Notify {
  &Item {
    &Container {
      @apply relative flex rounded-[.25rem] border border-solid border-[#9B9B9B]/30 p-0;
    }

    &Unread {
      @apply before:w-2.5 before:h-2.5 before:absolute before:rounded-full before:top-1/2 before:-translate-y-1/2 before:-right-5 before:bg-[#3ac882];
    }

    &Block {
      @apply flex space-x-3 hover:bg-[#222] cursor-pointer w-full;
    }

    &Layout {
      &Icon {
        @apply py-1 px-3;
      }

      &Thumb {
        @apply pr-3;
      }
    }

    &Thumb {
      @apply relative flex-none w-[10.25rem] aspect-video overflow-hidden rounded-l-[.25rem];

      img {
        @apply absolute top-0 right-0 bottom-0 left-0 max-w-full w-full;
      }
    }

    &Icon {
      @apply flex flex-none w-5 h-5 p-[1.5px];
    }

    &Content {
      @apply flex basis-auto flex-col space-y-2;

      &Block {
        @apply flex flex-col space-y-1;
      }

      &Heading {
        @apply line-clamp-1 text-sm md:text-[1rem] text-white;
      }

      &Paragraphs {
        @apply text-[#9B9B9B] line-clamp-2 text-xs md:text-sm h-[2rem] md:h-[40px];
      }

      &WithThumb {
        @apply py-1;
      }
    }
  }

  &CreationTime {
    @apply text-[.75rem] md:text-[.875rem] text-[#9b9b9b];
  }

  &Announce {
    &Modal {
      &Container {
        @apply max-w-[75rem];
      }

      &Wrapper {
        @apply py-5 md:py-9 rounded-2xl bg-[#222];
      }

      &Title {
        @apply px-4 md:px-10 pb-[.875rem] border-0 border-solid border-b border-b-[#454545]/40 text-[#dedede] font-bold text-xl md:text-2xl;
      }

      &Content {
        @apply block relative max-w-full px-4 md:px-10 py-3 md:py-5;
      }

      &Block {
        @apply w-full max-h-[calc(100vh-300px)] md:max-h-[36rem] overflow-x-hidden overflow-y-auto overscroll-contain pr-4 md:pr-10 text-white font-normal text-[.875rem];

        &ScrollBar {
          scrollbar-width: thin;
          scrollbar-color: #ffffff #333333;

          &::-webkit-scrollbar {
            @apply w-[.375rem] bg-[#333];
          }

          &::-webkit-scrollbar-track {
            @apply w-[.375rem];
          }

          &::-webkit-scrollbar-thumb {
            visibility: visible;
            @apply w-[.375rem] bg-white opacity-100;
          }
        }

        &OverlayTop {
          //@apply before:w-[calc(100%-2rem)] before:md:w-[calc(100%-7.5rem)] before:h-[calc(100%-.625rem)] before:md:h-[calc(100%-1.125rem)] before:absolute before:top-[.625rem] before:md:top-[1.125rem] before:left-4 before:md:left-10 before:bg-gradient-to-b before:from-[#222] before:to-15% before:animate-[fade-in_.4s_linear] before:z-[1];
          @apply before:w-[calc(100%-2rem)] before:md:w-[calc(100%-7.5rem)] before:h-[10%] before:absolute before:top-[.625rem] before:md:top-[1.125rem] before:left-4 before:md:left-10 before:bg-gradient-to-b before:from-[#222] before:animate-[fade-in_.4s_linear] before:z-[1];
        }

        &OverlayBottom {
          @apply after:w-[calc(100%-2rem)] after:md:w-[calc(100%-7.5rem)] after:h-[10%] after:absolute after:bottom-[.625rem] after:md:bottom-[1.125rem] after:left-4 after:md:left-10 after:bg-gradient-to-t after:from-[#222] after:animate-[fade-in_.4s_linear];
        }
      }

      &Action {
        &Group {
          @apply flex flex-col md:flex-row px-4 md:px-10 pt-3 items-center justify-between space-y-3 md:space-y-0 md:space-x-3;
        }

        &Btn {
          &Group {
            @apply flex flex-row w-full max-w-full md:max-w-96 ml-auto space-x-3;
          }

          &Primary {
            @apply flex basis-1/2 box-border w-auto md:w-[8rem] h-8 md:h-12 px-3 items-center justify-center rounded bg-white border-[1px] border-white text-[.875rem] md:text-[1rem] font-medium text-[#222] leading-none transition-all;
            @apply hover:bg-[#ccc] hover:border-[#ccc] hover:text-[#333];
            @apply disabled:bg-[#454545] disabled:text-[#646464];
          }

          &Secondary {
            @apply flex basis-1/2 box-border w-auto md:w-[8rem] h-8 md:h-12 px-3 items-center justify-center rounded bg-transparent border-solid border-[1px] border-white text-[.875rem] md:text-[1rem] font-medium text-white leading-none transition-all;
            @apply hover:bg-[#333]/50;
          }
        }
      }
    }

    &Sync {
      &Modal {
        &Wrapper {
          @apply bg-[#333] rounded;
        }

        &Body {
          @apply px-4 md:px-8 pt-4 pb-5 md:pb-8;
        }

        &Content {
          @apply space-y-3 text-sm text-[#9b9b9b];
        }

        &Title {
          @apply text-white font-bold text-xl md:text-2xl text-center;
        }

        &Action {
          &Group {
            @apply flex flex-col md:flex-row px-4 md:px-10 pb-5 md:pb-8 items-center justify-between space-y-3 md:space-y-0 md:space-x-3;
          }

          &Primary {
            @apply flex box-border w-full h-8 md:h-12 px-3 items-center justify-center rounded bg-white border-[1px] border-white text-[.875rem] md:text-[1rem] font-medium text-[#222] leading-none transition-all;
            @apply hover:bg-[#ccc] hover:border-[#ccc] hover:text-[#333];
          }
        }
      }
    }
  }
}
