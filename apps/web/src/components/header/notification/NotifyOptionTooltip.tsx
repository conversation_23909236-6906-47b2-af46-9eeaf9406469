import React from 'react';
import TooltipTrigger from 'react-popper-tooltip';
import Button from '../../basic/Buttons/Button';

const NotifyOptionTooltip = ({ renderTooltipContent, isContent, openTooltipOption }: any) => {
  function Trigger({ getTriggerProps, triggerRef }: any) {
    return (
      <button
        {...getTriggerProps({
          ref: triggerRef,
          className: 'button button--action'
        })}
      >
        <span className="icon icon--small">
          <i className="vie vie-ellipsis-dot-v-o" />
        </span>
      </button>
    );
  }

  const Tooltip = ({ getTooltipProps, getArrowProps, tooltipRef, arrowRef, placement }: any) => (
    <div
      {...getTooltipProps({
        ref: tooltipRef,
        className: 'tooltip-container notify-option-list'
      })}
    >
      <div
        {...getArrowProps({
          ref: arrowRef,
          'data-placement': placement,
          className: 'tooltip-arrow'
        })}
      />
      <div className="tooltip-body notify-option-list-inner">
        {renderTooltipContent && renderTooltipContent()}
      </div>
    </div>
  );

  if (!isContent) {
    return (
      <Button
        className="button button--action hover"
        iconClass="icon--small"
        iconName="vie-ellipsis-dot-v-o"
        onClick={openTooltipOption}
      />
    );
  }

  return (
    <TooltipTrigger placement="bottom-end" trigger="click" tooltip={Tooltip}>
      {Trigger}
    </TooltipTrigger>
  );
};

export default NotifyOptionTooltip;
