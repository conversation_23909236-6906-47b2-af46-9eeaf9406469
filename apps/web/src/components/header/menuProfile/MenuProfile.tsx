import React, { useEffect, useRef, useState } from 'react';
import { TEXT } from '@constants/text';
import { LOBBY_PROFILE_STEP, PAGE, POPUP, POSITION } from '@constants/constants';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import { openPopup } from '@actions/popup';
import { setResultForm } from '@actions/multiProfile';
import { getInfoLoyalty } from '@actions/user';
import TrackingApp from '@tracking/functions/TrackingApp';
import LobbyNavigationOnTop from '@components/LobbyProfile/LobbyNavigationOnTop';
import NewIcon from '@components/basic/Icon/NewIcon';
import PaneNav from '../pane/PaneNav';
import PaneUserProfile from './pane/PaneUserProfile';

interface MenuProfileProps {
  isPayment?: boolean;
  openPopupLogout?: () => void;
}

interface MenuProfileComponent extends React.FC<MenuProfileProps> {
  handleClickOutside: () => void;
}

const MenuProfile: MenuProfileComponent = ({ isPayment, openPopupLogout }: MenuProfileProps) => {
  const [isOpenProfile, setOpenProfile] = useState(false);
  const [isHoverPane, setHoverPane] = useState(false);
  const [isOffPaneNav, setOffPaneNav] = useState(false);

  const dispatch = useDispatch();
  const router = useVieRouter();
  const { pathname } = router || {};
  const isInApp = (pathname || '').includes(PAGE.ZALOPAY);
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const { isMobile } = useSelector((state: any) => state?.App);
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile);
  const currentTier = useSelector((state: any) => state?.User?.loyalty?.info?.currentTier || '');
  const { isOnLoyalty } = useSelector((state: any) => state?.App?.webConfig?.featureFlag || false);
  const isFailInfoLoyalty = useSelector((state: any) => state?.User?.loyalty?.isGetFailInfoLoyalty);

  const { givenName, mobile, email } = profile || {};
  const displayName = givenName || mobile || email;
  const wrapperRef = useRef<any>(null);

  const hanleOpenPopupLogout = () => {
    if (openPopupLogout) {
      openPopupLogout();
    } else {
      dispatch(openPopup({ name: POPUP.NAME.LOGOUT }));
    }
    setOpenProfile(false);
  };

  const handleToggleProfile = () => {
    if (profile?.id && !isOpenProfile && !currentTier && isOnLoyalty && !isKid) {
      dispatch(getInfoLoyalty({ userId: profile?.id }));
    }
    setOpenProfile(!isOpenProfile);
    setOffPaneNav(!isOffPaneNav);
    TrackingApp.menuSelected({
      name: TEXT.ACCOUNT_SETTING
    });
  };

  const handleClickSetting = () => {
    if (pathname !== PAGE.PROFILE) {
      setOpenProfile(false);
      setOffPaneNav(false);
    }
  };

  const handleMouseEnter = () => {
    setHoverPane(true);
  };

  const handleMouseLeave = () => {
    setHoverPane(false);
  };

  const handleCloseContentItem = () => {
    if (isOpenProfile) {
      setOffPaneNav(false);
      setOpenProfile(false);
    }
  };

  const handleOpenEditLobbyProfile = () => {
    dispatch(setResultForm({ status: LOBBY_PROFILE_STEP.EDIT }));
    router.push(PAGE.LOBBY_PROFILES);
  };

  useEffect(() => {
    if (router.asPath) setOpenProfile(false);
  }, [router.asPath]);

  if (!profile?.id) return null;

  return (
    <li className="menu__item relative size-h-full flex-box align-middle" ref={wrapperRef}>
      {(isInApp || isPayment || router?.pathname === PAGE.VOUCHER) &&
      !currentProfile?.id &&
      isMobile ? (
        <button
          className="avatar relative flexible"
          aria-label="Bấm để xem thông tin tài khoản"
          onClick={handleToggleProfile}
          type="button"
        >
          <NewIcon iconName="vie-user-o-c-medium" isFadeIn />
        </button>
      ) : (
        <button
          className="button p-r p-l relative"
          role="menuitem"
          onClick={handleToggleProfile}
          onMouseEnter={!isMobile ? handleMouseEnter : undefined}
          onMouseLeave={!isMobile ? handleMouseLeave : undefined}
          type="button"
        >
          <LobbyNavigationOnTop isCurrentProfile mobileOnlyAvatar isPayment={isPayment} />
          {isMobile && !isPayment && (
            <span className="text">{!isMobile && (profile?.givenName || profile?.mobile)}</span>
          )}
        </button>
      )}
      {isOpenProfile && (
        <PaneUserProfile
          isInApp={isInApp}
          displayName={displayName}
          isKid={isKid}
          onOpenEditLobbyProfile={handleOpenEditLobbyProfile}
          onClickSetting={handleClickSetting}
          onOpenPopupLogout={hanleOpenPopupLogout}
          isMobile={isMobile}
          wrapperRef={wrapperRef}
          onCloseContentItem={handleCloseContentItem}
          currentTier={currentTier}
          isFailInfoLoyalty={isFailInfoLoyalty}
        />
      )}
      {isHoverPane && !isOffPaneNav && (
        <PaneNav
          position={POSITION.BOTTOM_RIGHT}
          content={TEXT.CONTENT_NAVIGATION.ACCOUNT_LOGGED}
        />
      )}
    </li>
  );
};

// Define the handleClickOutside method on the component after declaration
// This is a static method that will be properly connected when the component instance is created
// The actual implementation will be set by the component that uses MenuProfile with a click-outside hook
MenuProfile.handleClickOutside = () => {
  // Default implementation that will be overridden at runtime
};

export default MenuProfile;
