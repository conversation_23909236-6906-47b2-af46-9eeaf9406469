import React from 'react';
import { useSelector } from 'react-redux';
import { TIER } from '@constants/constants';
import { TEXT } from '@constants/text';
import CurrentTier from '../../../profile/profileContent/profileLoyalty/memberPoint/banner/CurrentTier';

const MemberTier = () => {
  const currentTier = useSelector(
    (state: any) => state?.User?.loyalty?.info?.currentTier || TIER.BRONZE
  );
  const viecoin = useSelector((state: any) => state?.User?.loyalty?.info?.availablePoints || 0);
  const isMobile = useSelector((state: any) => state?.App?.isMobile || false);

  return (
    <>
      <div className="text text-10 text-bold padding-small-up-bottom-6 text-gray173">
        {TEXT.MEMBER_TIER}
      </div>
      <div className="flex-box align-middle">
        <CurrentTier
          className="text text-14 p-l1 m-l1 text-uppercase"
          currentTier={currentTier || TIER.BRONZE}
          hasIcon
        />

        {isMobile && (
          <div className="text text-bold text-green p-l4 text-14">
            <span className="p-r1">{viecoin || 0}</span>
            {TEXT.VIECOIN}
          </div>
        )}
      </div>
    </>
  );
};

export default React.memo(MemberTier);
