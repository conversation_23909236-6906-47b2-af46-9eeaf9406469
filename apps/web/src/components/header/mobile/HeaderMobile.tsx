import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { useVieRouter } from '@customHook';
import VieLink from '@components/VieLink';
import { TEXT } from '@constants/text';
import {
  EL_ID,
  HEADER_LAYER_NAME,
  PAGE,
  POPUP,
  LOBBY_PROFILE_STEP,
  ICON_KEY,
  LAYOUT_MENU
} from '@constants/constants';
import ConfigImage from '@config/ConfigImage';
import {
  createTimeout,
  onOpenPayment,
  setMutePlayer,
  encodeParamDestination
} from '@helpers/common';
import { useDispatch, useSelector } from 'react-redux';
import { setOpenTagMenu } from '@actions/menu';
import { setResultForm } from '@actions/multiProfile';
import dynamic from 'next/dynamic';
import TrackingApp from '@tracking/functions/TrackingApp';
import LobbyNavigationOnTop from '@components/LobbyProfile/LobbyNavigationOnTop';
import isEmpty from 'lodash/isEmpty';
import { getTriggerConfig } from '@actions/appConfig';
import { getInfoLoyalty } from '@actions/user';
import { VALUE } from '@config/ConfigSegment';
import TrackingPayment from '@tracking/functions/payment';
import Button from '../../basic/Buttons/Button';
import Logo from '../../basic/Logo/Logo';
import HeaderSub from '../HeaderSub';
import Menu from '../menu/Menu';
import Icon from '../../basic/Icon/Icon';
import MemberTier from '../memberTier';

const Download = dynamic(import('../download/Download'), { ssr: false });
const SearchInputContainer = dynamic(import('@containers/Header/SearchInputContainer'), {
  ssr: false
});
const NotificationContainer = dynamic(import('@containers/Header/NotificationContainer'), {
  ssr: false
});

const trackingPayment = new TrackingPayment();
const HeaderMobile = React.forwardRef(
  (
    {
      menuList,
      profile,
      activeMenu,
      activeSubMenu,
      USER_TYPE,
      isPayment,
      isMobile,
      dataCountNotification,
      onClickMenu,
      subHeader,
      openPopup,
      featureFlag,
      isOpenBenefit,
      setStatusBenefitInApp,
      isCanNotWatch,
      contentDetail
    }: any,
    ref: any
  ) => {
    const { enableSubmenuHBOGo } = featureFlag || {};
    const dispatch = useDispatch();
    const refCurrent = useRef<any>(null);
    const router = useVieRouter();
    const asPath = router?.asPath;
    const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
    const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
    const triggerConfig = useSelector((state: any) => state?.AppConfig?.trigger) || {};
    const isDataSub = useMemo(() => !!activeSubMenu?.subMenuRibbon, [activeSubMenu]);
    const isSubMenu = useMemo(() => (activeMenu?.subMenu || []).length > 1, [activeMenu]);
    const [layerName, setLayerName] = useState<any>('');
    const [showMobileMenu, setShowMobileMenu] = useState(false);
    const [showSearch, setShowSearch] = useState(false);
    const [isOpenProfile, setOpenProfile] = useState(false);
    const currentTier = useSelector((state: any) => state?.User?.loyalty?.info?.currentTier || '');
    const { isOnLoyalty } = useSelector(
      (state: any) => state?.App?.webConfig?.featureFlag || false
    );
    const isFailInfoLoyalty = useSelector(
      (state: any) => state?.User?.loyalty?.isGetFailInfoLoyalty
    );

    const unMark = useMemo(
      () =>
        dataCountNotification && dataCountNotification.unread
          ? Number.parseInt(dataCountNotification.unread)
          : 0,
      [dataCountNotification]
    );
    const isHideBuyPackage = USER_TYPE?.hide_button_buy_package;
    const buttonBuyPackageLabel = isEmpty(profile)
      ? `${TEXT.SUBSCRIBE_PACKAGE_GUESS}`
      : profile?.isPremium
      ? `${TEXT.SUBSCRIBE_PACKAGE}`
      : `${TEXT.SUBSCRIBE_PACKAGE_MEMBER}`;

    useEffect(() => {
      setShowMobileMenu(false);
    }, [asPath]);

    const openPageAuth = () => {
      closeMenu();
      const remakeDestination = encodeParamDestination(router?.asPath);
      router.push(`${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}`);
    };

    const onClickOpenPayment = () => {
      trackingPayment.globalPaymentButtonSelected({
        currentPage: VALUE.HOME_PAGE
      });
      onOpenPayment(router, { curPage: VALUE.HOME_PAGE });
    };

    const onClickOpenVoucher = () => {
      trackingPayment.promotionButtonSelected();
      router.push(PAGE.VOUCHER);
    };

    const handleShowSearch = (isShow: any) => {
      setShowSearch(isShow);
    };

    const handleOpenMenu = () => {
      if (profile?.id && !currentTier && isOnLoyalty && !isKid) {
        dispatch(getInfoLoyalty({ userId: profile?.id }));
      }
      const htmlEl: any = document.getElementsByTagName('html')[0];
      htmlEl.classList.add('overflow');
      document.body.classList.add('overflow');
      setLayerName(HEADER_LAYER_NAME.MENU);
      setMutePlayer({ playerId: EL_ID.VIE_PLAYER, isMuted: true });
      dispatch(setOpenTagMenu(true));
    };

    const closeMenu = () => {
      if (refCurrent.current) {
        refCurrent.current.classList.remove('animate-slide-in-left');
        refCurrent.current.classList.add('animate-slide-out-left');
      }
      const htmlEl: any = document.getElementsByTagName('html')[0];
      if (htmlEl.classList.contains('overflow')) {
        htmlEl.classList.remove('overflow');
        document.body.classList.remove('overflow');
      }
      createTimeout(() => {
        setLayerName('');
        setMutePlayer({ playerId: EL_ID.VIE_PLAYER, isMuted: false });
        dispatch(setOpenTagMenu(false));
      }, 500);
    };

    const openNotify = () => {
      if (isEmpty(triggerConfig)) {
        dispatch(getTriggerConfig());
      }
      setLayerName(HEADER_LAYER_NAME.NOTIFY);
    };

    const closeNotify = () => {
      setLayerName(HEADER_LAYER_NAME.MENU);
    };

    const handleClickMenu = (data: any) => {
      if (data?.layoutType !== LAYOUT_MENU.MORE) {
        closeMenu();
      } else {
        return;
      }
      if (typeof onClickMenu === 'function') onClickMenu(data);
    };

    const openProfile = () => {
      closeMenu();
      router.push(PAGE.PROFILE);
      setLayerName('');
      TrackingApp.menuSelected({
        name: TEXT.ACCOUNT_SETTING
      });
    };

    const openLogout = () => {
      openPopup({ name: POPUP.NAME.LOGOUT });
      setLayerName('');
    };

    const openEditLobbyProfile = () => {
      dispatch(setResultForm({ status: LOBBY_PROFILE_STEP.EDIT }));
      router.push(PAGE.LOBBY_PROFILES);
      closeMenu();
    };

    const handleToggleLobby = (isToggle: any) => {
      setOpenProfile(isToggle);
    };

    return (
      <header
        className={`header fixed${isDataSub || isSubMenu ? ' header-gradient' : ''}`}
        id="header"
        data-sub={isDataSub}
        data-sub-menu={isSubMenu}
        style={!isEmpty(contentDetail) && isCanNotWatch ? { zIndex: 99999 } : {}}
        ref={ref}
      >
        <div className="top-bar height-small-up-48rem" id="topBar">
          <div className="top-bar-left align-middle">
            <Button
              className="button button--for-dark button--nav-mobile size-h-full !pl-0 !mr-1.5"
              iconClass="!text-[1.125rem]"
              iconName="vie-list-bar-rc"
              onClick={handleOpenMenu}
            />
            <Logo />
            {enableSubmenuHBOGo && activeMenu?.iconText === ICON_KEY.HBO && (
              <VieLink as={activeMenu?.seo?.url} href={activeMenu?.href}>
                <a className="logo logo--hbo align-middle relative" title="HBO GO" role="menuitem">
                  <img src={ConfigImage.iconHBOGO} alt="Logo HBO-GO" />
                </a>
              </VieLink>
            )}
            {layerName === HEADER_LAYER_NAME.MENU && (
              <nav
                className="nav nav--left nav--for-mobile animate-slide-in-left overflow-hidden"
                ref={refCurrent}
              >
                <div className="nav__wrap over-scroll-contain size-h-full relative">
                  <div className="nav__header flex-box justify-content-end">
                    <Button
                      className="button button--for-dark"
                      iconClass="!text-[1.125rem]"
                      iconName="vie-times-medium"
                      onClick={closeMenu}
                    />
                  </div>
                  <div className="nav__body scrollable-y">
                    <ul className="menu menu--secondary logged vertical p-t">
                      {profile?.id && (currentTier || isFailInfoLoyalty) && (
                        <li className="padding-y-small-up-8 relative">
                          <MemberTier />
                        </li>
                      )}
                      {!profile?.id && (
                        <li className="menu__item relative" onClick={openPageAuth}>
                          <a className="avatar">
                            <span className="icon icon--small">
                              <i className="vie vie-user-o-c-medium" />
                            </span>
                            <span className="text">{TEXT.LOGIN}</span>
                          </a>
                        </li>
                      )}
                      {profile?.id && (
                        <>
                          <li className="menu__item relative">
                            <LobbyNavigationOnTop
                              isCurrentProfile
                              handleToggleLobby={handleToggleLobby}
                            />
                            {isOpenProfile && (
                              <div className="p-l3 m-t1 flex-box align-left vertical size-w-full">
                                <LobbyNavigationOnTop />
                              </div>
                            )}
                          </li>
                          {!isKid && (
                            <li className="menu__item">
                              <a
                                className="text-gray239 relative flexible height-small-up-36rem"
                                onClick={openEditLobbyProfile}
                              >
                                <Icon iClass="vie-pencil-o-rc" />
                                <span className="p-l2 text-14">{TEXT.EDIT_INFORMATION}</span>
                              </a>
                            </li>
                          )}
                          <li className="menu__item">
                            <a
                              className="text-gray239 relative flexible height-small-up-36rem"
                              onClick={openProfile}
                            >
                              <Icon iClass="vie-cog-o" />
                              <span className="p-l2 text-14">{TEXT.ACCOUNT_SETTING}</span>
                            </a>
                          </li>
                          {!isKid && (
                            <li className="menu__item">
                              <a
                                className="text-gray239 relative flexible height-small-up-36rem button button--notify"
                                onClick={openNotify}
                              >
                                {unMark > 0 && (
                                  <span className="notify__counter">
                                    <span>{unMark > 9 ? '9+' : unMark}</span>
                                  </span>
                                )}
                                <Icon iClass="vie-bell-o-rc-light" />
                                <span className="p-l2 text-14">{TEXT.NOTIFICATION}</span>
                              </a>
                            </li>
                          )}
                        </>
                      )}
                    </ul>
                    <Menu
                      menuLayout="vertical"
                      menuList={menuList}
                      showMobileMenu={showMobileMenu}
                      isMobile={isMobile}
                      activeMenu={activeMenu}
                      isPayment={isPayment}
                      onClickMenu={handleClickMenu}
                      activeSubMenu={activeSubMenu}
                    />
                    {profile?.id && !isKid && (
                      <ul className="menu">
                        <li className="menu__item relative">
                          <button
                            className="button button--logout text-gray239 p-l p-r"
                            onClick={openLogout}
                          >
                            <span className="icon icon--small">
                              <i className="vie vie-power-off-light" />
                            </span>
                            <span className="text-14 padding-small-up-left-6">{TEXT.LOGOUT}</span>
                          </button>
                        </li>
                      </ul>
                    )}
                  </div>
                </div>
              </nav>
            )}
            {profile?.id && !isKid && (
              <NotificationContainer
                profile={profile}
                isPayment={isPayment}
                closeNotify={closeNotify}
                closeMenu={closeMenu}
                isMobile
                layerName={layerName}
              />
            )}
          </div>
          <div className="top-bar-right" id="topBarRight">
            <ul className="menu menu--secondary horizontal relative logged">
              {!showSearch && !isHideBuyPackage && !isKid && (
                <li className="menu__item relative grid-x align-middle">
                  <Button
                    title={buttonBuyPackageLabel}
                    subTitle={buttonBuyPackageLabel}
                    onClick={onClickOpenPayment}
                    // theme=
                    size={isMobile ? 'small' : 'medium'}
                    customizeClass="bg-gradient-to-r from-[#DA9E1C] to-[#ECBD57] rounded-[.25rem]"
                    textClass="!text-[.75rem] md:!text-[1rem] !font-bold !text-vo-gray-800 uppercase"
                  />
                </li>
              )}
              {!showSearch && !isKid && !isGlobal && (
                <li className="menu__item">
                  <button
                    className="button p-x"
                    onClick={onClickOpenVoucher}
                    title={TEXT.ENTER_VieON_CODE}
                    type="button"
                  >
                    <span className="text text-white">{TEXT.ENTER_VieON_CODE}</span>
                  </button>
                </li>
              )}
              <li className="menu__item h-full">
                <SearchInputContainer handleShowSearch={handleShowSearch} isMobile={isMobile} />
              </li>
              <Download
                setStatusBenefitInApp={setStatusBenefitInApp}
                isOpenBenefit={isOpenBenefit}
                isMobile={isMobile}
              />
            </ul>
          </div>
        </div>
        <HeaderSub
          activeSubMenu={activeSubMenu}
          subHeader={subHeader}
          isPayment={isPayment}
          onClickMenu={onClickMenu}
          isMobile={isMobile}
        />
      </header>
    );
  }
);

export default memo(HeaderMobile);
