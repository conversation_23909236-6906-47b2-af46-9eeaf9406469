import React from 'react';
import classNames from 'classnames';
import Button from '@components/basic/Buttons/Button';
import isEmpty from 'lodash/isEmpty';
import ConfigImage from '@config/ConfigImage';
import styles from './Menu.module.scss';

const MenuBlockBuster = ({ highlights, isMobile, activeSubMenu, clickSubMenu }: any) => {
  const navBusterLayoutForDevice = isMobile ? styles.Vertical : styles.Horizontal;
  return (
    <>
      {!isEmpty(highlights) && (
        <div className={`blockbuster ${!isMobile ? '' : 'size-h-auto vertical'}`}>
          {!isMobile && (
            <Button
              className="button button--block-buster p-r p-l"
              iconClass="icon--small"
              imgSrc={ConfigImage.iconBlockBuster}
            />
          )}
          <nav
            className={classNames(
              styles.Base,
              styles.Sub,
              navBusterLayoutForDevice,
              styles.navBlockbuster
            )}
          >
            {highlights.map((item: any) => (
              <button
                className={classNames(
                  styles.navBlockbusterItem,
                  item?.id === activeSubMenu?.id ? styles.navBlockbusterActive : null
                )}
                key={item?.id}
                type="button"
                title={item?.name || ''}
                role="menuitem"
                onClick={(e) => clickSubMenu(e, item)}
              >
                <span className="text">{item?.name || ''}</span>
              </button>
            ))}
          </nav>
        </div>
      )}
    </>
  );
};

export default MenuBlockBuster;
