.Base {
  @apply flex;
}

.Menu {
  &Item {
    @apply relative space-x-2 text-[#ccc] hover:text-vo-green;

    &Icon {
      @apply flex min-w-6 h-6 items-center justify-center box-border;

      & > svg {
        @apply w-auto h-[calc(100%-.25rem)];
      }
    }

    &Text {
      @apply md:pt-[2px] text-[.875rem] lg:text-[1rem] font-medium;
    }

    &Active {
      @apply text-white hover:text-white;
      @apply before:w-12 md:before:w-full before:h-[.125rem] before:absolute md:before:top-[calc(100%-1px)] before:-left-10 before:md:left-0 before:bg-custom-radial before:rotate-90 before:md:rotate-0;
      @apply after:w-12 md:after:w-full after:h-1 after:absolute md:after:top-[calc(100%-2px)] after:-left-[38px] after:md:left-0 after:bg-custom-radial-1 after:rotate-90 after:md:rotate-0;
    }
  }
}

.Sub {
  @apply relative;

  &Item {
    @apply flex flex-row flex-wrap relative;
    @apply leading-tight text-[.75rem] md:text-[.875rem] text-vo-gray-200 hover:text-vo-green transition-colors;

    &Active {
      @apply text-white hover:text-white;
    }
  }
}

.Horizontal {
  @apply flex flex-row flex-nowrap space-x-3 md:space-x-6 items-center;

  &Divide {
    @apply flex flex-row flex-nowrap space-x-0 md:space-x-3 space-y-2 md:space-y-0 items-center;

    & > :not([hidden]) ~ :not([hidden]) {
      @apply pl-0 md:pl-3;
      @apply before:w-[1px] before:h-[.875rem] before:bg-[#9b9b9b] before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2;
    }
  }
}

.Vertical {
  @apply flex flex-col flex-nowrap space-y-4 md:space-y-5;
}

.navBlockbuster {
  @apply relative md:-ml-2 md:h-[1.75rem] md:px-6;
  @apply md:bg-gradient-to-r md:from-black/0 md:from-0% md:via-black/15 md:via-[4%] md:to-black/35 md:to-100% md:border md:border-solid md:border-vo-gray-200 md:border-l-0 md:rounded-e-3xl;

  &Item {
    @apply flex grow shrink w-max basis-0 md:basis-auto relative px-2 md:px-0 py-1 md:py-0;
    @apply leading-tight text-[.75rem] md:text-[.875rem] border border-solid border-vo-gray-200 md:border-none rounded-3xl;
    @apply text-vo-gray-200 hover:text-vo-green transition-colors;
  }

  &Active {
    @apply text-white hover:text-white;
  }
}
