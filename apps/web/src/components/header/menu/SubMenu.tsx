import React, { useRef } from 'react';
import VieLink from '@components/VieLink';
import { useVieRouter } from '@customHook';
import { useSelector } from 'react-redux';
import ConfigImage from '@config/ConfigImage';
import TrackingApp from '@tracking/functions/TrackingApp';
import { createTimeout } from '@helpers/common';
import { ICON_KEY, LAYOUT_MENU, PAGE } from '@constants/constants';
import classNames from 'classnames';
import MenuBlockBuster from './MenuBlockBuster';
import styles from './Menu.module.scss';

let clickTimer: any = null;
const SubMenu = ({
  subMenu,
  activeSubMenu,
  onClickMenu,
  activeMenu,
  isMore,
  layoutType,
  highlights,
  isMobile
}: any) => {
  const router = useVieRouter();
  const contentEl = useRef<any>(null);
  const { enableSubmenuHBOGo } = useSelector((state: any) => state.App?.featureFlag || {});
  const layoutMore = layoutType === LAYOUT_MENU.MORE;
  const clickSubMenu = (e: any, item: any) => {
    e.preventDefault();
    if (clickTimer) clearTimeout(clickTimer);
    TrackingApp.categorySelected({ data: item });
    if (typeof onClickMenu === 'function') {
      onClickMenu();
    }
    clickTimer = createTimeout(() => {
      router.push(item?.href || PAGE.HOME, item?.seo?.url);
    }, 500);
  };

  const filterDuplicateSubMenu = subMenu?.filter(
    (valSub: any) => !highlights.find((valHL: any) => valSub.id === valHL.id)
  );
  const subMenuLayout = isMobile ? styles.Vertical : styles.HorizontalDivide;

  return (
    <div
      className={
        !layoutMore
          ? `menu menu--sub height-small-up-auto height-large-up-32${
              !isMobile ? ' absolute' : ' p-l4 !pt-2 overflow'
            }`
          : ''
      }
      ref={contentEl}
      style={
        isMobile ? (isMore ? { height: contentEl?.current?.scrollHeight } : { height: '0px' }) : {}
      }
    >
      <div
        className={
          !layoutMore
            ? `container${isMobile ? ' space-y-5' : ' flex-box align-middle space-x-5'}`
            : ''
        }
      >
        {enableSubmenuHBOGo && !isMobile && activeMenu?.iconText === ICON_KEY.HBO && (
          <VieLink as={activeMenu?.seo?.url} href={activeMenu?.href}>
            <a
              title={activeMenu?.name}
              className="padding-large-up-right-12 padding-medium-up-right-6 display-flex-inline"
              role="menuitem"
            >
              <img src={ConfigImage.iconHBOGOSubMenu} alt="Logo HBO-GO" />
            </a>
          </VieLink>
        )}
        <MenuBlockBuster
          highlights={highlights}
          isMobile={isMobile}
          activeSubMenu={activeSubMenu}
          clickSubMenu={clickSubMenu}
        />
        {!layoutMore && (
          <nav className={classNames(styles.Sub, subMenuLayout)} id="SUB_MENU">
            {(filterDuplicateSubMenu || []).map((item: any, index: any) => {
              if (index === 0) return null;

              return (
                <button
                  className={classNames(
                    styles.SubItem,
                    item?.dotCode ? styles.SubItemDot : '',
                    item?.id === activeSubMenu?.id ? styles.SubItemActive : ''
                  )}
                  key={item?.id}
                  title={item?.name || ''}
                  role="menuitem"
                  type="button"
                  onClick={(e) => clickSubMenu(e, item)}
                >
                  <span>{item?.name || ''}</span>
                  {item?.dotCode && (
                    <i className="dot" style={{ backgroundColor: item?.dotCode }} />
                  )}
                </button>
              );
            })}
          </nav>
        )}
      </div>
    </div>
  );
};

export default React.memo(SubMenu);
