import React, { useEffect, useState, useMemo } from 'react';
import MenuItem from './MenuItem';

const Menu = ({
  menuList,
  activeMenu,
  activeSubMenu,
  isPayment,
  onClickMenu,
  isMobile,
  menuLayout,
  menuProperties,
  menuCustom
}: any) => {
  if (isPayment) return null;
  const [isMore, setIsMore] = useState(false);
  const menuCustomClass = useMemo(
    () =>
      (menuLayout ? ` ${menuLayout}` : '') +
      (menuProperties ? ` ${menuProperties}` : '') +
      (menuCustom ? ` ${menuCustom}` : ''),
    [menuLayout, menuProperties, menuCustom]
  );

  const onToggle = (id: any) => {
    if (isMore === id) {
      setIsMore(false);
    } else {
      setIsMore(id);
    }
  };

  useEffect(() => {
    onToggle(activeMenu?.id);
  }, []);

  return (
    <ul className={`menu${menuCustomClass}`} role="menubar" id="main-menu">
      {(menuList || []).map((item: any) => {
        if (item.id) {
          return (
            <MenuItem
              key={item.id}
              activeMenu={activeMenu}
              activeSubMenu={activeSubMenu}
              onClickMenu={() => onClickMenu(item?.urlRedirect)}
              isMobile={isMobile}
              onToggle={() => onToggle(item.id)}
              isMore={isMore === item.id}
              layoutType={item.layoutType}
              data={item}
            />
          );
        }
        return null; // Provide a default return value (null) to satisfy the ESLint rule
      })}
    </ul>
  );
};
export default React.memo(Menu);
