import React, { Fragment, useMemo } from 'react';
import { useVieRouter } from '@customHook';
import { useSelector } from 'react-redux';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import dynamic from 'next/dynamic';
import classNames from 'classnames';
import RibbonWrapper from '@components/Ribbon';
import { EL_ID, RIBBON_TYPE } from '@constants/constants';

const BindAccountRibbon = dynamic(import('@components/home/<USER>'), {
  ssr: false
});
const BannerUserPackageInfo = dynamic(import('@components/home/<USER>'), {
  ssr: false
});

const ListRibbons = ({
  id,
  isRankingBoard,
  showTrigger,
  // isLiveStream,
  customizeClassName,
  isRapVietRanking
}: any) => {
  const router = useVieRouter();
  const { asPath } = router || {};
  const { deviceId } = useSelector((state: any) => state?.App || {});
  const { pageRibbon } = useSelector((state: any) => state?.Page || {});
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const { USER_PACKAGE_INFO } = useSelector((state: any) => state?.User || {});
  const subscriptions = USER_PACKAGE_INFO?.subscriptions;
  const data = useMemo(() => {
    return get(pageRibbon, id, []);
  }, [pageRibbon, id]);

  const isPaymentTrigger = useMemo(() => {
    const basePath = asPath?.split('?')?.[0] || '';
    return (
      basePath.length === 1 && !data.some((item: any) => item?.type === RIBBON_TYPE.WATCH_MORE)
    );
  }, [asPath, data]);

  const classNameSection = classNames({
    // 'section section--home overflow': !isRankingBoard && !isLiveStream,
    // relative: !isRankingBoard,
    // 'section--channel-livestream overflow canal-v': isLiveStream,
    [customizeClassName]: customizeClassName
  });

  if (isEmpty(data) || !id) return null;

  return (
    <section id={EL_ID.SECTION_HOME} className={classNameSection}>
      {data.map((item: any, i: any) => {
        if (i === 0) {
          return (
            <Fragment key={item.id}>
              <RibbonWrapper data={item} order={i} isRankingBoard={isRankingBoard} />
              {showTrigger ? (
                <>
                  <BindAccountRibbon />
                  {profile?.id && subscriptions && deviceId && isPaymentTrigger && (
                    <BannerUserPackageInfo />
                  )}
                </>
              ) : null}
            </Fragment>
          );
        }
        return (
          <RibbonWrapper
            key={item.id}
            data={item}
            order={i}
            isRankingBoard={isRankingBoard}
            isRapVietRanking={isRapVietRanking}
          />
        );
      })}
    </section>
  );
};

export default ListRibbons;
