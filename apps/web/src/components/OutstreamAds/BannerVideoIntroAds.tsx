import React, { useEffect, useMemo, useState } from 'react';
import { isMobile } from 'react-device-detect';
import isEmpty from 'lodash/isEmpty';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import AOutstreamAds from '@components/basic/Ads/AOutstreamAds';
import { getInventoryId } from '@services/adsServices';

const BannerVideoIntroAds = ({ data }: any) => {
  const [isShowAds, setIsShowAds] = useState(false);
  const [isLoadSuccess, setLoadSuccess] = useState(false);
  const [adsSlot, setAdsSlot] = useState<any>(null);
  const { id, path, size } = data || {};
  const inventoryId = useMemo(() => getInventoryId(id), [id]);

  useEffect(
    () => () => {
      clearAds();
    },
    []
  );

  useEffect(() => {
    if (!isEmpty(data)) {
      setIsShowAds(true);
    }
  }, [data]);

  const clearAds = () => {
    setIsShowAds(false);
    setLoadSuccess(false);
    if (!isEmpty(adsSlot)) {
      GPT.clear([adsSlot]);
      setAdsSlot(null);
    }
  };

  const onSlotRenderEnded = (event: any) => {
    if (inventoryId) {
      if (event?.success) {
        setLoadSuccess(true);
      } else {
        setIsShowAds(false);
        setLoadSuccess(false);
      }
    } else {
      const slot = event?.slot;
      setAdsSlot(slot);
      if (event?.isEmpty) {
        setIsShowAds(false);
        setLoadSuccess(false);
      } else {
        setLoadSuccess(true);
      }
    }
  };

  if (isMobile || !isShowAds) return null;

  return (
    <div className="banner banner--market dynamic padding-small-up-top-8">
      <div className="banner__inner overflow">
        <div className="banner__body">
          {inventoryId ? (
            <AOutstreamAds inventoryId={inventoryId} onSlotRenderEnded={onSlotRenderEnded} />
          ) : (
            <GPT
              id={id}
              className="mask__img"
              adUnitPath={path}
              slotSize={size}
              onSlotRenderEnded={onSlotRenderEnded}
            />
          )}

          {isLoadSuccess && (
            <button
              className="button button--darken-glass close button--circle-32 absolute top-2 right-2"
              type="button"
              title="close"
              onClick={clearAds}
            >
              <span className="icon icon--tiny">
                <i className="vie vie-times-medium text-white" />
              </span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default React.memo(BannerVideoIntroAds);
