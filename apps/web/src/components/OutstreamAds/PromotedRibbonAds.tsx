import React, { useEffect, useMemo, useState } from 'react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { isMobile } from 'react-device-detect';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import { getInventoryId } from '@services/adsServices';
import AOutstreamAds from '@components/basic/Ads/AOutstreamAds';
import Image from '@components/basic/Image/Image';
import { requestAdsOutstream } from '@tracking/functions/TrackingAds';
import Style from './PromotedRibbonAds.module.scss';

const PromotedRibbonAds = ({ dataRibbon, data, onLoadedAds }: any) => {
  const [isShowAds, setIsShowAds] = useState(false);
  const [adsSlot, setAdsSlot] = useState<any>(null);
  const [dataAds, setDataAds] = useState<any>(null);
  const { size } = dataAds || {};
  const { path } = data || {};
  const inventoryId = useMemo(() => getInventoryId(path), [path]);

  useEffect(
    () => () => {
      clearAds();
    },
    []
  );

  useEffect(() => {
    if (!isEmpty(dataRibbon) && !isEmpty(data)) {
      const adsItem = get(data, `${dataRibbon.id}.items[0]`, {});
      if (!isEmpty(adsItem)) {
        setDataAds(adsItem);
        setIsShowAds(true);
        onLoadedAds(true);
      } else {
        onLoadedAds(false);
      }
    } else {
      onLoadedAds(false);
    }
  }, [dataRibbon, data]);

  const clearAds = () => {
    if (!isEmpty(adsSlot)) {
      GPT.clear([adsSlot]);
      setAdsSlot(null);
    }
    setIsShowAds(false);
    setDataAds(null);
    onLoadedAds(false);
  };

  const handleTrackingAdsRequest = (adsId: any, status: any, errorMsg: any) => {
    const data = {
      inventoryId: adsId,
      status,
      errorMsg,
      type: 'promoted_ribbon'
    };

    requestAdsOutstream(data);
  };

  const onSlotRenderEnded = (event: any) => {
    if (inventoryId) {
      if (!event?.success) {
        clearAds();
        handleTrackingAdsRequest(inventoryId, 'failed', '');
      } else {
        handleTrackingAdsRequest(inventoryId, 'success', event?.message || '');
      }
    } else {
      const slot = event?.slot;
      setAdsSlot(slot);
      if (event?.isEmpty) {
        clearAds();
        handleTrackingAdsRequest(path, 'failed', 'Ad is empty.');
      } else {
        handleTrackingAdsRequest(path, 'success', '');
      }
    }
  };

  const renderCustomAds = (adsData: any) => (
    <aside className={Style.base}>
      <div className="md:w-7/12">
        <div className="aspect-video">
          <Image
            className="cursor-pointer"
            src={adsData?.native?.previewImage?.url || ''}
            alt=""
            notWebp
          />
        </div>
      </div>
      <a
        href={adsData?.native?.link?.clickTrackers?.[0]}
        target="_blank"
        className={Style.content}
        rel="noreferrer"
      >
        <div className="aspect-auto">
          <Image className="cursor-pointer" src={adsData?.native?.icon?.url} alt="" notWebp />
        </div>
        <div className="text-white text-left">{adsData?.native?.description}</div>
        <button
          className="flex shrink items-center justify-center px-3 h-12 bg-white text-[#222] font-[Roboto]"
          title={adsData?.native?.callToAction}
        >
          {adsData?.native?.callToAction}
        </button>
      </a>
    </aside>
  );

  if (isMobile || !isShowAds || !path) return null;

  return (
    <div className="rocopa__body">
      <div className="banner banner--market">
        <div className="banner__inner align-center overflow">
          <div className="banner__body text-center relative">
            <img
              className="absolute top-0 left-0 z-10"
              src="/assets/images/sponsored.svg"
              alt="sponsoredIcon"
            />

            {inventoryId ? (
              <AOutstreamAds
                inventoryId={inventoryId}
                onSlotRenderEnded={onSlotRenderEnded}
                renderCustomAds={renderCustomAds}
              />
            ) : (
              <GPT
                adUnitPath={path}
                slotSize={size}
                onSlotRenderEnded={onSlotRenderEnded}
                renderWhenViewable
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(PromotedRibbonAds);
