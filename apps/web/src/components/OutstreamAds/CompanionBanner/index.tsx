import React, { useEffect, useMemo, useState } from 'react';
import isEmpty from 'lodash/isEmpty';
import find from 'lodash/find';
import get from 'lodash/get';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import { setStatusCompanionBanner } from '@actions/app';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import { ADS_URL } from '@constants/constants';
import Image from '@components/basic/Image/Image';
import ConfigImage from '@config/ConfigImage';
import { requestAdsOverlay } from '@tracking/functions/TrackingAds';

const COMPANION_BANNER_ADS_POSITIONS = {
  ABOVE: 'above',
  BELOW: 'below'
};

const COMPANION_BANNER_ADS_STATUS = {
  PENDING: 'pending',
  SUCCESS: 'success',
  FAILED: 'failed'
};

const CompanionBanner = ({ content }: any) => {
  const [adSlotsState, setAdSlotsState] = useState<any>({ above: null, below: null });
  const [loadStatus, setLoadStatus] = useState<any>({
    above: COMPANION_BANNER_ADS_STATUS.PENDING,
    below: COMPANION_BANNER_ADS_STATUS.PENDING
  });

  const [renderKeys, setRenderKeys] = useState<any>({
    above: 0,
    below: 0
  });
  const { isHasCompanionBanner, outStreamAds } = useSelector((state: any) => state?.App || {});
  const { isFullscreen } = useSelector((state: any) => state?.Player || {});
  const [prevIsFullscreen, setPrevIsFullscreen] = useState(false);
  const [isInit, setIsInit] = useState<any>({ above: false, below: false });

  const dispatch = useDispatch();

  const companionBannerAbove = useMemo(
    () =>
      find(get(content, 'ads', []), (ad) =>
        [
          `nonlogin-companion-banner-${COMPANION_BANNER_ADS_POSITIONS.ABOVE}`,
          `companion-banner-${COMPANION_BANNER_ADS_POSITIONS.ABOVE}`
        ].includes(ad.type)
      ),
    [content]
  );

  const companionBannerBelow = useMemo(
    () =>
      find(get(content, 'ads', []), (ad) =>
        [
          `nonlogin-companion-banner-${COMPANION_BANNER_ADS_POSITIONS.BELOW}`,
          `companion-banner-${COMPANION_BANNER_ADS_POSITIONS.BELOW}`
        ].includes(ad.type)
      ),
    [content]
  );

  const getAdUnitPath = (banner: any) => get(banner, [ADS_URL.URL1, 'url'], '');

  const reinitializeGPTAd = (position: any) => {
    setLoadStatus((prev: any) => ({
      ...prev,
      [position]: COMPANION_BANNER_ADS_STATUS.PENDING
    }));
    setAdSlotsState((prev: any) => ({
      ...prev,
      [position]: null
    }));
    setRenderKeys((prev: any) => ({
      ...prev,

      [position]: prev[position] + 1
    }));
  };

  const refreshOrReinitializeAd = (position: any) => {
    const slot = adSlotsState[position]?.slot;
    if (slot) {
      GPT.refresh([slot]);
      checkRefreshAds(position);
    } else {
      reinitializeGPTAd(position);
      checkRefreshAds(position);
    }
  };

  const refreshAllAds = () => {
    if (!isEmpty(companionBannerAbove)) {
      refreshOrReinitializeAd(COMPANION_BANNER_ADS_POSITIONS.ABOVE);
    }
    if (!isEmpty(companionBannerBelow)) {
      refreshOrReinitializeAd(COMPANION_BANNER_ADS_POSITIONS.BELOW);
    }
  };

  useEffect(() => {
    if (prevIsFullscreen && !isFullscreen) {
      refreshAllAds();
    }
    setPrevIsFullscreen(isFullscreen);
  }, [isFullscreen]);

  const updateStatusAfterClearAds = () => {
    dispatch(setStatusCompanionBanner(false));
    setLoadStatus({
      above: COMPANION_BANNER_ADS_STATUS.PENDING,
      below: COMPANION_BANNER_ADS_STATUS.PENDING
    });
  };

  useEffect(() => {
    if (!isEmpty(companionBannerAbove)) {
      setLoadStatus((prev: any) => ({ ...prev, above: COMPANION_BANNER_ADS_STATUS.PENDING }));
      dispatch(setStatusCompanionBanner(true));
    }

    return updateStatusAfterClearAds;
  }, [companionBannerAbove]);

  useEffect(() => {
    if (!isEmpty(companionBannerBelow)) {
      setLoadStatus((prev: any) => ({ ...prev, below: COMPANION_BANNER_ADS_STATUS.PENDING }));
      dispatch(setStatusCompanionBanner(true));
    }

    return updateStatusAfterClearAds;
  }, [companionBannerBelow]);

  useEffect(() => {
    if (isFullscreen) {
      return;
    }

    const refreshAbove = setInterval(() => {
      if (!isEmpty(companionBannerAbove)) {
        refreshOrReinitializeAd(COMPANION_BANNER_ADS_POSITIONS.ABOVE);
      }
    }, (companionBannerAbove?.repeat || 10) * 60 * 1000);

    return () => clearInterval(refreshAbove);
  }, [loadStatus.above, isFullscreen]);

  useEffect(() => {
    if (isFullscreen) {
      return;
    }

    const refreshBelow = setInterval(() => {
      if (!isEmpty(companionBannerBelow)) {
        refreshOrReinitializeAd(COMPANION_BANNER_ADS_POSITIONS.BELOW);
      }
    }, (companionBannerBelow?.repeat || 10) * 60 * 1000);

    return () => clearInterval(refreshBelow);
  }, [loadStatus.below, isFullscreen]);

  const checkRefreshAds = (position: any) => {
    if (isEmpty(adSlotsState[position])) {
      setLoadStatus((prev: any) => ({
        ...prev,
        [position]: COMPANION_BANNER_ADS_STATUS.FAILED
      }));
      handleTrackingAdsRequest(position, 'failed', 'Ad is empty.');
    } else {
      setLoadStatus((prev: any) => ({
        ...prev,
        [position]: COMPANION_BANNER_ADS_STATUS.SUCCESS
      }));
      handleTrackingAdsRequest(position, 'success', '');
    }
  };

  const handleTrackingAdsRequest = (position: any, status: any, errorMsg: any) => {
    const flowName = {
      inventoryId:
        position === COMPANION_BANNER_ADS_POSITIONS.ABOVE
          ? getAdUnitPath(companionBannerAbove)
          : getAdUnitPath(companionBannerBelow),
      type: `companion_${position}`,
      status,
      errorMsg
    };
    requestAdsOverlay(flowName);
  };

  const handleSlotRenderEnded = (position: any) => (event: any) => {
    const slot = event;

    if (event.isEmpty && isEmpty(adSlotsState.above) && isEmpty(adSlotsState.below)) {
      setLoadStatus((prev: any) => ({
        ...prev,
        [position]: COMPANION_BANNER_ADS_STATUS.FAILED
      }));

      if (!isInit[position]) {
        handleTrackingAdsRequest(position, 'failed', 'Ad is empty.');
      }
    } else if (!event.isEmpty) {
      setLoadStatus((prev: any) => ({
        ...prev,
        [position]: COMPANION_BANNER_ADS_STATUS.SUCCESS
      }));
      setAdSlotsState((prevState: any) => ({
        ...prevState,
        [position]: slot
      }));

      if (!isInit[position]) {
        handleTrackingAdsRequest(position, 'success', '');
      }
    }
    setIsInit({ ...isInit, [position]: true });
  };

  const renderAdSlot = (position: any, adUnitPath: any) => {
    const status = loadStatus[position];
    let baseName =
      position === COMPANION_BANNER_ADS_POSITIONS.ABOVE ? 'companionBanner1' : 'companionBanner2';
    const fieldName = baseName;
    const companionAdData = outStreamAds?.[fieldName];

    return (
      <div
        className={`${
          position === COMPANION_BANNER_ADS_POSITIONS.ABOVE ? 'h-[70%]' : 'h-[30%]'
        } flex items-center justify-center`}
      >
        <Image
          className="w-full h-full"
          style={{
            display:
              status === COMPANION_BANNER_ADS_STATUS.FAILED || isEmpty(adUnitPath)
                ? 'block'
                : 'none'
          }}
          src={
            position === COMPANION_BANNER_ADS_POSITIONS.ABOVE
              ? ConfigImage.errorADsAbove
              : ConfigImage.errorADsBelow
          }
          alt={`Error companion-banner ads ${position}`}
        />

        <div
          className="relative w-full h-full bg-[#151515]"
          style={{
            display:
              status === COMPANION_BANNER_ADS_STATUS.FAILED || isEmpty(adUnitPath)
                ? 'none'
                : 'block'
          }}
        >
          <div
            className={classNames(
              'absolute z-0 top-0 left-0 w-full h-full animate-gradient-slide',
              status !== COMPANION_BANNER_ADS_STATUS.PENDING
                ? position === COMPANION_BANNER_ADS_POSITIONS.ABOVE
                  ? 'max-h-[600px]'
                  : 'max-h-[250px]'
                : ''
            )}
            style={{
              background:
                position === COMPANION_BANNER_ADS_POSITIONS.ABOVE
                  ? 'linear-gradient(90deg, #353535, #151515, #353535)'
                  : 'linear-gradient(90deg, #151515, #353535, #151515)',
              backgroundSize: '200% 200%'
            }}
          />
          <GPT
            key={`${position}-${renderKeys[position]}`}
            id={`companion-banner-ads-${position}`}
            adUnitPath={adUnitPath}
            slotSize={companionAdData?.size || ['fluid']}
            onSlotRenderEnded={handleSlotRenderEnded(position)}
            style={{ width: '100%', height: '100%', zIndex: 1, position: 'relative' }}
          />
        </div>
      </div>
    );
  };

  if (!isHasCompanionBanner && isFullscreen) return null;

  return (
    <aside className="w-[15.7%] overflow-hidden flex-none max-w-[300px] hidden md:block">
      {!isEmpty(companionBannerAbove) &&
        renderAdSlot(COMPANION_BANNER_ADS_POSITIONS.ABOVE, getAdUnitPath(companionBannerAbove))}
      {!isEmpty(companionBannerBelow) &&
        renderAdSlot(COMPANION_BANNER_ADS_POSITIONS.BELOW, getAdUnitPath(companionBannerBelow))}
    </aside>
  );
};

export default React.memo(CompanionBanner);
