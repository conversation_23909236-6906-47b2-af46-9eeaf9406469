import React, { useEffect, useMemo, useState } from 'react';
import isEmpty from 'lodash/isEmpty';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import AOutstreamAds from '@components/basic/Ads/AOutstreamAds';
import { getInventoryId } from '@services/adsServices';
import get from 'lodash/get';
import find from 'lodash/find';
import { setStatusLoadOutStreamAds } from '@actions/app';
import { isFirefox } from 'react-device-detect';
import styles from './Ads.module.scss';
import { ADS_URL, STATUS_OUTSTREAM_ADS } from '@constants/constants';
import { requestAdsOverlay } from '../../tracking/functions/TrackingAds';

const AIACTIV_URL = 'aiactiv://';

const PausePlayerAds = ({ content, isEndScreenVod, isMobile, showController }: any) => {
  const [isLoadSuccess, setLoadSuccess] = useState(false);
  const [adsSlot, setAdsSlot] = useState<any>(null);
  const pausedAd = useMemo(() => find(get(content, 'ads', []), ['type', 'pause']), [content]);
  const adUnitPath = !isEmpty(pausedAd) ? get(pausedAd, [ADS_URL.URL1, 'url'], null) : '';
  const inventoryId = useMemo(
    () => (adUnitPath?.includes(AIACTIV_URL) ? getInventoryId(adUnitPath) : null),
    [adUnitPath]
  );
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const dispatch = useDispatch();
  const popupName = useSelector((state: any) => state?.Popup?.popupName || '');

  const clearAds = () => {
    setLoadSuccess(false);
    if (!isEmpty(adsSlot)) {
      GPT.clear([adsSlot]);
      setAdsSlot(null);
    }
  };

  const handleTrackingAdsRequest = (status: any, errorMsg: any) => {
    const inventoryIdTracking = inventoryId || adUnitPath;
    const flowName = {
      inventoryId: inventoryIdTracking,
      type: 'paused_ads',
      status,
      errorMsg
    };
    requestAdsOverlay(flowName);
  };

  const onSlotRenderEnded = (event: any) => {
    console.log('onSlotRenderEnded', event);
    if (inventoryId) {
      if (event?.success) {
        setLoadSuccess(true);
        handleTrackingAdsRequest('success', '');
        dispatch(setStatusLoadOutStreamAds(STATUS_OUTSTREAM_ADS.SUCCESS));
      } else {
        handleTrackingAdsRequest('failed', event?.message || '');
        dispatch(setStatusLoadOutStreamAds(STATUS_OUTSTREAM_ADS.FAIL));
      }
    } else {
      const slot = event?.slot;
      setAdsSlot(slot);
      if (event?.isEmpty) {
        dispatch(setStatusLoadOutStreamAds(STATUS_OUTSTREAM_ADS.FAIL));
        handleTrackingAdsRequest('failed', 'Ad serving limit exceeded.');
      } else {
        setLoadSuccess(true);
        handleTrackingAdsRequest('success', '');
        dispatch(setStatusLoadOutStreamAds(STATUS_OUTSTREAM_ADS.SUCCESS));
      }
    }
  };

  useEffect(
    () => () => {
      dispatch(setStatusLoadOutStreamAds(STATUS_OUTSTREAM_ADS.CLOSE));
      clearAds();
    },
    [dispatch]
  );

  if ((!adUnitPath && !inventoryId) || isMobile || popupName?.length > 0) {
    return null;
  }

  return (
    <div
      className={classNames(
        isEndScreenVod && showController && 'pb-[10%]',
        isLoadSuccess && styles.pausePlayer,
        isLoadSuccess && 'animate-fade-in'
      )}
    >
      {inventoryId ? (
        <AOutstreamAds inventoryId={inventoryId} onSlotRenderEnded={onSlotRenderEnded} />
      ) : (
        <GPT
          style={{
            width: `${isFirefox ? '-moz-available' : '-webkit-fill-available'}`,
            height: 'auto',
            maxWidth: isGlobal ? '300px' : '100%',
            maxHeight: isGlobal ? '250px' : '100%'
          }}
          id="pause-player-ads"
          adUnitPath={adUnitPath}
          slotSize={['fluid']}
          onSlotRenderEnded={onSlotRenderEnded}
        />
      )}
    </div>
  );
};

export default React.memo(PausePlayerAds);
