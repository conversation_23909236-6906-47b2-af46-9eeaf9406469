import React, { useEffect, useMemo, useState } from 'react';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import { useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import AOutstreamAds from '@components/basic/Ads/AOutstreamAds';
import { getInventoryId } from '@services/adsServices';
import { EL_ID } from '@constants/constants';
import { requestAdsOutstream } from '@tracking/functions/TrackingAds';

let adsSlot: any = null;

const InPageBannerAds = () => {
  const inpageId = 'div-gpt-ad-1633061292865-0';
  const router = useVieRouter();
  const { inpage } = useSelector((state: any) => state?.App?.outStreamAds) || {};
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const { path, size } = inpage || {};
  const [isHide, setHide] = useState(false);
  const [isLoadSuccess, setLoadSuccess] = useState(false);
  const [isLoadFailed, setLoadFailed] = useState(false);

  const inventoryId = useMemo(() => getInventoryId(path), [path]);

  useEffect(
    () => () => {
      adsSlot = null;
    },
    []
  );

  useEffect(() => {
    setHide(!router?.query?.vid);
    setLoadSuccess(false);
    setLoadFailed(false);
  }, [router?.query?.vid]);

  const handleTrackingAdsRequest = (adsId: any, status: any, errorMsg: any) => {
    const data = {
      inventoryId: adsId,
      status,
      errorMsg,
      type: 'inpage_banner'
    };

    requestAdsOutstream(data);
  };

  const onSlotRenderEnded = (event: any) => {
    if (inventoryId) {
      if (event?.success) {
        setLoadSuccess(true);
        handleTrackingAdsRequest(inventoryId, 'success', '');
      } else {
        setLoadFailed(true);
        handleTrackingAdsRequest(inventoryId, 'failed', event?.message || '');
      }
    } else {
      const slot = event?.slot;
      adsSlot = slot;
      const slotId = slot ? slot.getSlotElementId() : '';
      if (event?.isEmpty) {
        onLoadedFailed({ slot: event.slot, slotId });
        handleTrackingAdsRequest(path, 'failed', 'Ad is empty.');
      } else {
        onLoadedSuccess({ slot: event.slot, slotId });
        handleTrackingAdsRequest(path, 'success', '');
      }
    }
  };

  const onLoadedSuccess = ({ slot, slotId }: any) => {
    if (slotId === inpageId) {
      adsSlot = slot;
      setLoadSuccess(true);
    }
  };

  const onLoadedFailed = ({ slotId }: any) => {
    if (slotId === inpageId) {
      setLoadFailed(true);
    }
  };

  const onClose = () => {
    if (adsSlot) GPT.clear([adsSlot]);
    setHide(true);
  };

  if (isLoadFailed || !path || isHide || isMobile || profile?.isPremium) return null;

  return (
    <div className="">
      <div
        style={{
          width: '300px',
          bottom: '100px',
          height: '250px',
          overflow: 'hidden',
          position: 'fixed',
          right: '20px',
          zIndex: 9999
        }}
      >
        {inventoryId ? (
          <AOutstreamAds
            adId={EL_ID.INPAGE_INTRO}
            inventoryId={inventoryId}
            onSlotRenderEnded={onSlotRenderEnded}
          />
        ) : (
          <GPT
            id={inpageId}
            adUnitPath={path}
            slotSize={size}
            renderWhenViewable
            onSlotRenderEnded={onSlotRenderEnded}
          />
        )}
        {isLoadSuccess && (
          <button
            className="close button absolute size-square-38"
            data-close=""
            onClick={onClose}
            style={{
              top: '0.25rem',
              right: '0.25rem'
            }}
            aria-label="Close"
          >
            <span className="icon">
              <i className="vie text text-white vie-times-medium" style={{ fontSize: '1rem' }} />
            </span>
          </button>
        )}
      </div>
    </div>
  );
};

export default React.memo(InPageBannerAds);
