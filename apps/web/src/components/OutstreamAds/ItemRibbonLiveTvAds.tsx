import React, { useEffect, useMemo, useState } from 'react';
import { isMobile } from 'react-device-detect';
import isEmpty from 'lodash/isEmpty';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import AOutstreamAds from '@components/basic/Ads/AOutstreamAds';
import { getInventoryId } from '@services/adsServices';

const ItemRibbonLiveTv = ({ data }: any) => {
  const [isShowAds, setIsShowAds] = useState(false);
  const [adsSlot, setAdsSlot] = useState<any>(null);
  const { id, path, size } = data || {};
  const inventoryId = useMemo(() => getInventoryId(id), [id]);

  useEffect(
    () => () => {
      clearAds();
    },
    []
  );

  useEffect(() => {
    if (!isEmpty(data)) {
      setIsShowAds(true);
    }
  }, [data]);

  const clearAds = () => {
    setIsShowAds(false);
    if (!isEmpty(adsSlot)) {
      GPT.clear([adsSlot]);
      setAdsSlot(null);
    }
  };

  const onSlotRenderEnded = (event: any) => {
    if (inventoryId) {
      if (event?.success) {
        setIsShowAds(true);
      } else {
        setIsShowAds(false);
      }
    } else {
      const slot = event?.slot;
      setAdsSlot(slot);
      if (event?.isEmpty) {
        setIsShowAds(false);
      }
    }
  };

  if (isMobile || !isShowAds) return null;

  return (
    <div className="display-block">
      {inventoryId ? (
        <AOutstreamAds inventoryId={inventoryId} onSlotRenderEnded={onSlotRenderEnded} />
      ) : (
        <GPT
          id={id}
          className="card__thumb-img swiper-lazy"
          adUnitPath={path}
          slotSize={size}
          onSlotRenderEnded={onSlotRenderEnded}
        />
      )}
    </div>
  );
};

export default React.memo(ItemRibbonLiveTv);
