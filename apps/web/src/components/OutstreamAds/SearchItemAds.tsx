import React, { useEffect, useMemo, useState } from 'react';
import { isMobile } from 'react-device-detect';
import isEmpty from 'lodash/isEmpty';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import AOutstreamAds from '@components/basic/Ads/AOutstreamAds';
import { getInventoryId } from '@services/adsServices';
import Image from '@components/basic/Image/Image';

const SearchItemAds = ({ data }: any) => {
  const [isShowAds, setIsShowAds] = useState(false);
  const [adsSlot, setAdsSlot] = useState<any>(null);
  const { id, path, size } = data || {};
  const inventoryId = useMemo(() => getInventoryId(id), [id]);
  useEffect(
    () => () => {
      clearAds();
    },
    []
  );

  useEffect(() => {
    if (!isEmpty(data)) {
      setIsShowAds(true);
    }
  }, [data]);

  const clearAds = () => {
    setIsShowAds(false);
    if (!isEmpty(adsSlot)) {
      GPT.clear([adsSlot]);
      setAdsSlot(null);
    }
  };

  const onSlotRenderEnded = (event: any) => {
    if (inventoryId) {
      if (event?.success) {
        setIsShowAds(true);
      } else {
        setIsShowAds(false);
      }
    } else {
      const slot = event?.slot;
      setAdsSlot(slot);
      if (event?.isEmpty) {
        setIsShowAds(false);
      }
    }
  };

  const renderCustomAds = (adsData: any) => (
    <a
      title={data?.title}
      href={adsData?.native?.link?.clickTrackers?.[0]}
      target="_blank"
      rel="noreferrer"
    >
      <div className="search-suggestion__thumb">
        <Image
          className="cursor-pointer"
          src={adsData?.native?.previewImage?.url}
          alt="Search Item"
          notWebp
        />
      </div>
      <div className="search-suggestion__content">{adsData?.native?.title}</div>
      <div className="search-suggestion__icon">
        <span className="icon icon--small">
          <i className="vie vie-play-solid-rc" />
        </span>
      </div>
    </a>
  );

  if (isMobile || !isShowAds) return null;

  return (
    <div className="search-suggestion__item">
      {inventoryId ? (
        <AOutstreamAds
          inventoryId={inventoryId}
          onSlotRenderEnded={onSlotRenderEnded}
          renderCustomAds={renderCustomAds}
        />
      ) : (
        <GPT
          style={{ paddingTop: '1rem', paddingBottom: '1rem', display: 'flex' }}
          id={id}
          adUnitPath={path}
          slotSize={size}
          onSlotRenderEnded={onSlotRenderEnded}
          renderWhenViewable
        />
      )}
    </div>
  );
};

export default React.memo(SearchItemAds);
