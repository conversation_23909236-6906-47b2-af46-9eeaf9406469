import React, { useEffect, useMemo, useState } from 'react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { isMobile } from 'react-device-detect';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import { getInventoryId } from '@services/adsServices';
import dynamic from 'next/dynamic';
import { requestAdsOutstream } from '@tracking/functions/TrackingAds';

const AOutstreamAds = dynamic(import('@components/basic/Ads/AOutstreamAds'), {
  ssr: false
});

const BannerRibbonAds = ({ dataRibbon, data, onLoadedAds }: any) => {
  const [isShowAds, setIsShowAds] = useState(false);
  const [adsSlot, setAdsSlot] = useState<any>(null);
  const [dataAds, setDataAds] = useState<any>(null);
  const { size } = dataAds || {};
  const { path } = data || {};
  const inventoryId = useMemo(() => getInventoryId(path), [path]);

  useEffect(
    () => () => {
      clearAds();
    },
    []
  );

  useEffect(() => {
    if (!isEmpty(dataRibbon) && !isEmpty(data)) {
      const adsItem = get(data, `${dataRibbon.id}.items[0]`, {});
      if (!isEmpty(adsItem)) {
        setDataAds(adsItem);
        setIsShowAds(true);
        onLoadedAds(true);
      } else {
        onLoadedAds(false);
      }
    } else {
      onLoadedAds(false);
    }
  }, [dataRibbon, data]);

  const handleTrackingAdsRequest = (adsId: any, status: any, errorMsg: any) => {
    const data = {
      inventoryId: adsId,
      status,
      errorMsg,
      type: 'banner_ribbon'
    };

    requestAdsOutstream(data);
  };

  const clearAds = () => {
    if (!isEmpty(adsSlot)) {
      GPT.clear([adsSlot]);
      setAdsSlot(null);
    }
    setIsShowAds(false);
    setDataAds(null);
    onLoadedAds(false);
  };

  const onSlotRenderEnded = (event: any) => {
    if (inventoryId) {
      if (event?.success) {
        setIsShowAds(true);
        handleTrackingAdsRequest(inventoryId, 'success', '');
      } else {
        clearAds();
        handleTrackingAdsRequest(inventoryId, 'failed', event?.message || '');
      }
    } else {
      const slot = event?.slot;
      setAdsSlot(slot);
      if (event?.isEmpty) {
        clearAds();
        handleTrackingAdsRequest(path, 'failed', 'Ad is empty.');
      } else {
        handleTrackingAdsRequest(path, 'success', '');
      }
    }
  };

  if (isMobile || !isShowAds || !path) return null;

  return (
    <div className="rocopa__body text-white relative">
      <img
        className="absolute top-0 left-0 z-1000"
        src="/assets/images/sponsored.svg"
        alt="sponsoredIcon"
      />
      {inventoryId ? (
        <AOutstreamAds inventoryId={inventoryId} onSlotRenderEnded={onSlotRenderEnded} />
      ) : (
        <GPT adUnitPath={path} slotSize={size} onSlotRenderEnded={onSlotRenderEnded} />
      )}
    </div>
  );
};

export default React.memo(BannerRibbonAds);
