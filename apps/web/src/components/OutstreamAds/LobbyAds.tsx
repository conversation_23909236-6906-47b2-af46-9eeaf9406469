import React, { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import ButtonIcon from '@components/basic/Buttons/ButtonIcon';
import AOutstreamAds from '@components/basic/Ads/AOutstreamAds';
import { getInventoryId } from '@services/adsServices';
import { requestAdsOutstream } from '@tracking/functions/TrackingAds';
import Style from './LobbyAds.module.scss';

let adsSlot: any = null;

const LobbyAds = () => {
  const lobbyId = 'div-lobby-ad';
  const { lobby } = useSelector((state: any) => state?.App?.outStreamAds) || {};
  const { path, size } = lobby || {};
  const [isHide, setHide] = useState(false);
  const [isLoadSuccess, setLoadSuccess] = useState(false);
  const [isLoadFailed, setLoadFailed] = useState(false);

  const inventoryId = useMemo(() => getInventoryId(path), [path]);

  const handleTrackingAdsRequest = (adsId: any, status: any, errorMsg: any) => {
    const data = {
      inventoryId: adsId,
      status,
      errorMsg,
      type: 'lobby_ads'
    };

    requestAdsOutstream(data);
  };

  const onSlotRenderEnded = (event: any) => {
    if (inventoryId) {
      if (event?.success) {
        setLoadSuccess(true);
        setHide(false);
        handleTrackingAdsRequest(inventoryId, 'success', '');
      } else {
        setLoadFailed(true);
        handleTrackingAdsRequest(inventoryId, 'failed', event?.message || '');
      }
    } else {
      const slot = event?.slot;
      adsSlot = slot;
      const slotId = slot ? slot.getSlotElementId() : '';
      if (event?.isEmpty) {
        onLoadedFailed({ slot: event.slot, slotId });
        handleTrackingAdsRequest(path, 'failed', 'Ad is empty.');
      } else {
        onLoadedSuccess({ slot: event.slot, slotId });
        handleTrackingAdsRequest(path, 'success', '');
      }
    }
  };

  const onLoadedSuccess = ({ slot, slotId }: any) => {
    if (slotId === lobbyId) {
      adsSlot = slot;
      setHide(false);
      setLoadSuccess(true);
    }
  };

  const onLoadedFailed = ({ slotId }: any) => {
    if (slotId === lobbyId) {
      setLoadFailed(true);
    }
  };

  const onClose = () => {
    if (adsSlot) GPT.clear([adsSlot]);
    setHide(true);
  };

  if (isLoadFailed || !path || isHide) return null;

  return (
    <div className={Style.lobbyAdsContainer}>
      <div className={Style.lobbyAdsWrapper}>
        {inventoryId ? (
          <AOutstreamAds inventoryId={inventoryId} onSlotRenderEnded={onSlotRenderEnded} />
        ) : (
          <GPT
            id={lobbyId}
            adUnitPath={path}
            slotSize={size}
            renderWhenViewable
            onSlotRenderEnded={onSlotRenderEnded}
          />
        )}
        {isLoadSuccess && (
          <div className={Style.closeWrapper}>
            <ButtonIcon iClass="vie-times-medium" spClass="icon--small" onClick={onClose} />
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(LobbyAds);
