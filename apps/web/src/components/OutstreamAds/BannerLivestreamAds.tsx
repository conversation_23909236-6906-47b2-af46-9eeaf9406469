import React, { useEffect, useMemo, useState } from 'react';
import isEmpty from 'lodash/isEmpty';
import { isMobile } from 'react-device-detect';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import AOutstreamAds from '@components/basic/Ads/AOutstreamAds';
import { getInventoryId } from '@services/adsServices';

const BannerLivestreamAds = ({ data, handleStatusLoadAds, dataEventDetails }: any) => {
  const [isShowAds, setIsShowAds] = useState(false);
  const [isLoadSuccess, setLoadSuccess] = useState(false);
  const [adsSlot, setAdsSlot] = useState<any>(null);
  const { id, path, size } = data || {};
  const inventoryId = useMemo(() => getInventoryId(id), [id]);

  useEffect(
    () => () => {
      clearAds();
    },
    []
  );

  useEffect(() => {
    if (dataEventDetails?.id && !isEmpty(data)) {
      refreshAds();
      setIsShowAds(true);
    }
  }, [dataEventDetails?.id, data]);

  const clearAds = () => {
    setIsShowAds(false);
    setLoadSuccess(false);
    handleStatusLoadAds(false);
    if (!isEmpty(adsSlot)) {
      GPT.clear([adsSlot]);
      setAdsSlot(null);
    }
  };

  const refreshAds = () => {
    if (!isEmpty(adsSlot)) {
      GPT.refresh([adsSlot]);
    }
  };

  const onSlotRenderEnded = (event: any) => {
    if (inventoryId) {
      if (event?.success) {
        setLoadSuccess(true);
        handleStatusLoadAds(true);
      } else {
        setIsShowAds(false);
        setLoadSuccess(false);
        handleStatusLoadAds(false);
      }
    } else {
      const slot = event?.slot;
      setAdsSlot(slot);
      if (event?.isEmpty) {
        setIsShowAds(false);
        setLoadSuccess(false);
        handleStatusLoadAds(false);
      } else {
        setLoadSuccess(true);
        handleStatusLoadAds(true);
      }
    }
  };

  const handleClose = () => {
    clearAds();
  };

  if (isMobile || !isShowAds) return null;

  return (
    <div className="banner banner--live-stream-ads padding-small-up-left-16 padding-xlarge-up-left-32 layer-2 overflow">
      <div className="banner__inner overflow">
        <div className="banner__body">
          {inventoryId ? (
            <AOutstreamAds inventoryId={inventoryId} onSlotRenderEnded={onSlotRenderEnded} />
          ) : (
            <GPT
              id={id}
              adUnitPath={path}
              slotSize={size}
              renderWhenViewable
              onSlotRenderEnded={onSlotRenderEnded}
            />
          )}

          {isLoadSuccess && (
            <button
              className="button close button--circle-32 absolute top right layer-2"
              aria-label="Close modal"
              type="button"
              title="close"
              onClick={handleClose}
            >
              <span className="icon icon--tiny">
                <i className="vie vie-times-medium text-white" />
              </span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default React.memo(BannerLivestreamAds);
