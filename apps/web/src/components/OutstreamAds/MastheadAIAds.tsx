import React, { useEffect, useMemo, useState } from 'react';
import { useVieRouter } from '@customHook';
import { STATUS_OUTSTREAM_ADS } from '@constants/constants';
import { useDispatch, useSelector } from 'react-redux';
// import { GPTWrapper as GPT } from '@components/GPTWrapper';
import AOutstreamAds from '@components/basic/Ads/AOutstreamAds';
import { getInventoryId } from '@services/adsServices';
import parseADS from '@components/basic/Player/MastheadAdsPlayer/parse-vast-ads';
import MastheadAiAdsContainer from '@components/MastheadAiAds/MastheadAiAdsContainer';
import isEmpty from 'lodash/isEmpty';
import { setStatusLoadOutStreamAds, setStatusLoadMastheadAds } from '@actions/app';
import LocalStorage from '@config/LocalStorage';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import Styles from '../MastheadAiAds/Styles.module.scss';

const MastheadAiAds = ({ activeMenu }: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const { masthead } = useSelector((state: any) => state?.App?.outStreamAds) || {};
  const { id, isOffAds, countdown, pageShowList } = masthead || {};
  const [isHideFullScreen, setHideFullScreen] = useState(false);
  const [isLoadSuccess, setLoadSuccess] = useState(false);
  const [isLoadFailed, setLoadFailed] = useState(false);
  const inventoryId = useMemo(() => getInventoryId(id), [id]);
  const [videoDuration, setVideoDuration] = useState<any>();
  const [timeDown, setTimeDown] = useState<number | undefined>(undefined);
  const [isShowAds, setIsShowAds] = useState(false);
  const { asPath, pathname } = router || {};
  const [allowPath, setAllowPath] = useState<any>();

  useEffect(() => {
    if (asPath && pageShowList) {
      controlAds();
    }
  }, [asPath, pathname, activeMenu, pageShowList]);

  const controlAds = async () => {
    setIsShowAds(false);
    const isHideMasthead = ConfigLocalStorage.get(LocalStorage.HIDE_MASTHEAD) || '';
    const findAcceptPage = await pageShowList.findIndex((e: any) => e === asPath);
    if (findAcceptPage >= 0 && !isHideMasthead) {
      setIsShowAds(true);
      setAllowPath(asPath);
      setHideFullScreen(false);
    } else {
      setIsShowAds(false);
      if (isHideMasthead) {
        ConfigLocalStorage.remove(LocalStorage.HIDE_MASTHEAD);
      }
    }
  };

  const onSlotRenderEnded = (event: any) => {
    if (inventoryId) {
      if (event?.success) {
        setLoadSuccess(true);
        dispatch(setStatusLoadMastheadAds(true));
      } else {
        dispatch(setStatusLoadMastheadAds(false));
        setLoadFailed(true);
        dispatch(setStatusLoadOutStreamAds(STATUS_OUTSTREAM_ADS.CLOSE));
      }
    } else {
      const slot = event?.slot;
      const slotId = slot ? slot.getSlotElementId() : '';
      if (event?.isEmpty) {
        onLoadedFailed({
          slot: event.slot,
          slotId
        });
      } else {
        onLoadedSuccess({
          slot: event.slot,
          slotId
        });
      }
    }
  };

  const onLoadedSuccess = ({ slotId }: any) => {
    if (slotId === id) {
      setLoadSuccess(true);
      dispatch(setStatusLoadMastheadAds(true));
    }
  };

  const onLoadedFailed = ({ slotId }: any) => {
    if (slotId === id) {
      setLoadFailed(true);
      dispatch(setStatusLoadOutStreamAds(STATUS_OUTSTREAM_ADS.CLOSE));
    }
  };

  const handleSetAdsDuration = (event: any) => {
    setVideoDuration(event);
  };

  const handleClose = () => {
    // const htmlTag = document.getElementById('html-head');
    setHideFullScreen(true);
    // if (htmlTag && htmlTag.classList.contains('overflow') === true) {
    //   htmlTag.classList.remove('overflow');
    // }
  };
  useEffect(() => {
    if (!videoDuration && countdown) {
      setTimeDown(countdown);
      const timer = setInterval(() => {
        setTimeDown((prevTimeDown: any) => prevTimeDown - 1);
      }, 1000);
      return () => clearInterval(timer);
    }
    return;
  }, [pathname]);

  useEffect(() => {
    if (videoDuration === 0) {
      handleClose();
    }
  }, [videoDuration]);

  useEffect(() => {
    if (!videoDuration && countdown && timeDown === 0) {
      handleClose();
    }
  }, [timeDown]);

  const renderContainer = (dataAds: any) => {
    const { icon, link, previewImage, video, events } = dataAds?.native || {};
    const videoSrc = !isEmpty(video?.vast) ? parseADS(video?.vast) : null;
    const { vade } = dataAds || {};
    const handleClickBanner = () => {
      if (link?.clickTrackers?.[0]) {
        window.open(link?.clickTrackers?.[0], '_blank');
      }
    };

    return (
      <MastheadAiAdsContainer
        backdropUrl={previewImage?.url}
        videoSrc={videoSrc?.url}
        videoType={videoSrc?.type}
        bannerUrl={icon?.url}
        videoDuration={videoSrc?.url ? videoDuration : timeDown}
        handleClickBanner={handleClickBanner}
        handleSetAdsDuration={handleSetAdsDuration}
        handleClose={handleClose}
        isOpen={isLoadSuccess || !isHideFullScreen}
        hideFullScreen={isHideFullScreen}
        vade={vade}
        events={events}
        impressionURLTemplates={videoSrc?.impressionURLTemplates}
      />
    );
  };

  if (!isShowAds || isLoadFailed || !id || isOffAds || isMobile || profile?.isPremium) return null;

  return (
    allowPath &&
    isShowAds &&
    inventoryId && (
      <AOutstreamAds
        inventoryId={inventoryId}
        onSlotRenderEnded={onSlotRenderEnded}
        renderCustomAds={renderContainer}
        renderCustomAdsClass={Styles.MastheadBannerSection}
        isShowAds={isShowAds}
        allowPath={allowPath}
      />
    )
  );
};
export default React.memo(MastheadAiAds);
