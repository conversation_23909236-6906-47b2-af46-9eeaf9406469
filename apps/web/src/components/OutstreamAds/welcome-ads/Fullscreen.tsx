import React, { useEffect, useMemo, useState } from 'react';
import isNaN from 'lodash/isNaN';
import isEmpty from 'lodash/isEmpty';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import { createTimeout } from '@helpers/common';
import { useDispatch, useSelector } from 'react-redux';
import { setStatusLoadOutStreamAds } from '@actions/app';
import { EL_ID, STATUS_OUTSTREAM_ADS } from '@constants/constants';
import AOutstreamAds from '@components/basic/Ads/AOutstreamAds';
import { getInventoryId } from '@services/adsServices';
import { requestAdsOutstream } from '@tracking/functions/TrackingAds';

const POSITION = {
  HOME: 'HOME',
  MAIN_MENU: 'MAIN_MENU',
  INTRO: 'INTRO'
};
let timerDown: any = null;
let closeButtonTimer: any = null;

const WelcomeAdsFullscreen = ({ data, router, menuList, isMobile }: any) => {
  const dispatch = useDispatch();
  const { statusMasthead, geoCheck } = useSelector((state: any) => state.App) || {};
  const { isGlobal } = geoCheck || {};
  const [dataAds, setDataAds] = useState<any>(null);
  const { path, size, countDown, width, closeAfter } = dataAds || {};
  const countDownFormat = useMemo(() => (countDown ? parseInt(countDown) : 0), [countDown]);
  const closeAfterDelay = useMemo(() => (closeAfter ? parseInt(closeAfter) : 0), [closeAfter]);
  const [isLoadSuccess, setLoadSuccess] = useState(false);
  const [timeDown, setTimeDown] = useState(countDownFormat);
  const [isShowAds, setIsShowAds] = useState(false);
  const [showCloseButton, setShowCloseButton] = useState(!closeAfter);
  const [adsSlot, setAdsSlot] = useState<any>(null);
  const inventoryId = useMemo(() => getInventoryId(path), [path]);
  const positionAds = useMemo(() => {
    if (router.asPath === '/') {
      return POSITION.HOME;
    }
    if ((menuList || []).findIndex((m: any) => m.seo.url === router.asPath) > -1) {
      return POSITION.MAIN_MENU;
    }
    if (router?.query?.vid) {
      return POSITION.INTRO;
    }
    return '';
  }, [router]);
  const dataAdsWithPosition = useMemo(() => {
    switch (positionAds) {
      case POSITION.HOME:
        return data?.['welcomeHome'];
      case POSITION.MAIN_MENU:
        return data?.['welcomeMainMenu'];
      case POSITION.INTRO:
        return data?.['welcomeIntro'];
      default:
        return null;
    }
  }, [positionAds, data, isGlobal]);

  // Effect to handle close button delay
  useEffect(() => {
    if (isLoadSuccess && closeAfterDelay > 0) {
      setShowCloseButton(false);
      clearTimeout(closeButtonTimer);
      closeButtonTimer = setTimeout(() => {
        setShowCloseButton(true);
      }, closeAfterDelay * 1000);
    }

    return () => {
      clearTimeout(closeButtonTimer);
    };
  }, [isLoadSuccess, closeAfterDelay]);

  useEffect(
    () => () => {
      clearAds();
      clearTimeout(timerDown);
      clearTimeout(closeButtonTimer);
    },
    []
  );

  useEffect(() => {
    if (!isEmpty(dataAdsWithPosition)) {
      setDataAds(dataAdsWithPosition);
      setIsShowAds(true);
    }
    return () => {
      clearAds();
    };
  }, [dataAdsWithPosition]);

  useEffect(() => {
    const isMainMenu = (menuList || []).findIndex((m: any) => m.seo.url === router.asPath) > -1;
    if (isMainMenu && !isShowAds && !isEmpty(dataAdsWithPosition)) {
      setIsShowAds(true);
      setDataAds(dataAdsWithPosition);
    }
    return () => {
      clearAds();
    };
  }, [router.asPath, dataAdsWithPosition]);

  useEffect(() => {
    if (isShowAds && !isLoadSuccess) {
      setTimeDown(countDownFormat);
    }
  }, [countDownFormat, isShowAds, isLoadSuccess]);

  useEffect(() => {
    if (isLoadSuccess) {
      if (timeDown > 0) {
        clearTimeout(timerDown);
        timerDown = createTimeout(() => {
          setTimeDown((prevTime) => prevTime - 1);
        }, 1000);
      }
      if (timeDown === 0 && countDownFormat > 0) {
        handleClose();
      }
    }
  }, [timeDown, isLoadSuccess, countDownFormat]);

  const handleTrackingRequestAds = (status: any, errorMsg: any) => {
    const getFieldName = (baseName: any) => baseName;

    switch (positionAds) {
      case POSITION.HOME:
        return actionTrackingAds(data?.[getFieldName('welcomeHome')], status, errorMsg);
      case POSITION.MAIN_MENU:
        return actionTrackingAds(data?.[getFieldName('welcomeMainMenu')], status, errorMsg);
      case POSITION.INTRO:
        return actionTrackingAds(data?.[getFieldName('welcomeIntro')], status, errorMsg);
      default:
        return null;
    }
  };

  const actionTrackingAds = (data: any, status: any, errorMsg: any) => {
    const trackingData = {
      inventoryId: inventoryId || data.path,
      type: 'welcome_ads',
      status,
      errorMsg
    };
    requestAdsOutstream(trackingData);
  };

  const onSlotRenderEnded = (event: any) => {
    if (inventoryId) {
      if (event?.success) {
        const htmlTag = document.getElementById('html-head');
        if (htmlTag?.classList?.contains('overflow') === false && !statusMasthead) {
          htmlTag.classList.add('overflow');
        }
        setLoadSuccess(true);
        handleTrackingRequestAds('success', '');
      } else {
        setIsShowAds(false);
        setLoadSuccess(false);
        dispatch(setStatusLoadOutStreamAds(STATUS_OUTSTREAM_ADS.FAIL));
        handleTrackingRequestAds('failed', event?.message || '');
      }
    } else {
      const slot = event?.slot;
      setAdsSlot(slot);
      if (event?.isEmpty) {
        setIsShowAds(false);
        setLoadSuccess(false);
        dispatch(setStatusLoadOutStreamAds(STATUS_OUTSTREAM_ADS.FAIL));
        handleTrackingRequestAds('failed', 'Ad serving limit exceeded.');
      } else {
        const htmlTag = document.getElementById('html-head');
        if (htmlTag?.classList?.contains('overflow') === false && !statusMasthead) {
          htmlTag.classList.add('overflow');
        }
        setLoadSuccess(true);
        handleTrackingRequestAds('success', '');
      }
    }
  };

  const clearAds = () => {
    setIsShowAds(false);
    setLoadSuccess(false);
    if (!isEmpty(adsSlot)) {
      GPT.clear([adsSlot]);
      setAdsSlot(null);
    }
  };

  const handleClose = () => {
    const htmlTag = document.getElementById('html-head');
    dispatch(setStatusLoadOutStreamAds(STATUS_OUTSTREAM_ADS.CLOSE));
    if (htmlTag?.classList?.contains('overflow') === true) {
      htmlTag.classList.remove('overflow');
    }
    clearAds();
    clearTimeout(timerDown);
    clearTimeout(closeButtonTimer);
  };

  const onImpressionViewable = () => {
    if (countDownFormat > 0 && !isLoadSuccess) {
      setLoadSuccess(true);
      setTimeDown(countDownFormat);
    }
  };

  if (isMobile || isEmpty(dataAds) || !isShowAds || statusMasthead || !countDown) return null;

  return (
    <div
      className={`modal-overlay fixed scrollable-y over-scroll-contain${
        isLoadSuccess ? ' layer-max' : ' layer-negative'
      }`}
    >
      <div
        className="modal middle modal--welcome dynamic overflow"
        data-animation-in="fade"
        data-animation-out="fade"
      >
        <div className="modal-wrapper flex-box centering">
          <div className="modal-body">
            {inventoryId ? (
              <AOutstreamAds
                adId={EL_ID.MODAL_WELCOME_ADS}
                inventoryId={inventoryId}
                onSlotRenderEnded={onSlotRenderEnded}
              />
            ) : (
              <GPT
                adUnitPath={path}
                slotSize={size}
                onSlotRenderEnded={onSlotRenderEnded}
                onImpressionViewable={onImpressionViewable}
                style={{ width: width || '100%' }}
              />
            )}
          </div>
          {isLoadSuccess && showCloseButton && (
            <button
              className="button button--darken-glass close button--circle-32 absolute top-right-48per"
              onClick={handleClose}
              type="button"
              aria-label="Close button"
            >
              <span className="icon icon--tiny">
                <i className="vie vie-times-medium" />
              </span>
            </button>
          )}
          {isLoadSuccess && countDownFormat > 0 && (
            <div
              className="count-down size-square-40 absolute bottom-left-56per flex-box centering"
              style={{ backgroundColor: '#646464', borderRadius: '100%' }}
            >
              <span className="text text-white">
                {timeDown >= 10 ? timeDown : `0${isNaN(timeDown) ? 3 : timeDown}`}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default React.memo(WelcomeAdsFullscreen);
