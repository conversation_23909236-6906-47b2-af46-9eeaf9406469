import React, { useEffect, useState } from 'react';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import classNames from 'classnames';
import isEmpty from 'lodash/isEmpty';
import style from './GamAdsMasterBanner.module.scss';
import { requestAdsOutstream } from '@tracking/functions/TrackingAds';

GPT.enableSingleRequest();
const GamAdsMasterBanner = ({
  data,
  onNextSlide,
  bannerActiveIndex,
  handleStatusLoadAds,
  isActive
}: any) => {
  const [adsSlot, setAdsSlot] = useState<any>(null);
  const { id, path, size } = data || {};

  useEffect(() => clearAds(), []);

  useEffect(() => {
    const messageHandler = (e: any) => {
      let lastMessage = null;
      if (lastMessage !== e.data) {
        lastMessage = e.data;

        /* if (e.data === 'adsMasterInit') {
          console.log('adsMasterInit');
        } */
        /*   if (e.data === 'adsMasterPlay') {
          console.log('adsMasterPlay');
        }
        if (e.data === 'adsMasterPause') console.log('adsMasterPause'); */
        if (lastMessage === 'adsMasterEnd') {
          // console.log('adsMasterEnd');
          clearAds();
          if (!isActive) {
            onNextSlide();
          }
        }
      }
    };
    window.addEventListener('message', messageHandler, false);
    // cleanup function
    return () => {
      window.removeEventListener('message', messageHandler);
    };
  }, []);

  useEffect(() => {
    refreshAds();
  }, [bannerActiveIndex]);

  useEffect(() => {
    if (!isEmpty(data)) {
      refreshAds();
    }
  }, [data]);

  const clearAds = () => {
    if (!isEmpty(adsSlot)) {
      GPT.clear([adsSlot]);
      setAdsSlot(null);
    }
  };

  const refreshAds = () => {
    if (!isEmpty(adsSlot)) {
      GPT.refresh([adsSlot]);
    }
  };

  const handleTrackingAdsRequest = (status: any, errorMsg: any) => {
    const data = {
      inventoryId: path,
      status,
      errorMsg,
      type: 'master_banner'
    };

    requestAdsOutstream(data);
  };

  const onSlotRenderEnded = (event: any) => {
    const slot = event?.slot;
    setAdsSlot(slot);
    if (event?.isEmpty) {
      handleStatusLoadAds(false);
      handleTrackingAdsRequest('failed', 'Ad is empty.');
    } else {
      handleStatusLoadAds(true);
      handleTrackingAdsRequest('success', '');
    }
  };

  return (
    <div id="adsMasterWrapper">
      <div className={classNames(style.adsMasterVideo)} id={id}>
        <GPT
          adUnitPath={path}
          slotSize={size || ['fluid', [1, 1]]}
          renderWhenViewable
          onSlotRenderEnded={onSlotRenderEnded}
        />
        {/* <iframe src="http://localhost:8888" frameBorder="0" width="100%" /> */}
      </div>
    </div>
  );
};

export { GamAdsMasterBanner };
