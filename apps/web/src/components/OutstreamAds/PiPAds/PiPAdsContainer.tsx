import React from 'react';
import MastheadAdsPlayer from '@components/basic/Player/MastheadAdsPlayer';

const PiPAdsContainer = ({
  videoSrc,
  handleSetAdsDuration,
  onClickBanner,
  statusSkip,
  handleSkip
}: any) => (
  <div onClick={onClickBanner}>
    <MastheadAdsPlayer
      videoSrc={videoSrc}
      setAdsDuration={handleSetAdsDuration}
      countdownClass="left-0 mr-auto !bg-transparent w-max"
      statusSkip={statusSkip}
      handleSkip={handleSkip}
    />
  </div>
);

export default React.memo(PiPAdsContainer);
