import React, { useEffect } from 'react';
import Image from '../../basic/Image/Image';
import { aiActiveThirdTracking } from '@apis/aiactiv-third-tracking';

const OnstreamAdsContainer = ({ bannerUrl, onClickBanner, adsData }: any) => {
  useEffect(() => {
    if (bannerUrl) {
      aiActiveThirdTracking({
        vadeUrl: adsData?.vade,
        events: adsData?.native?.events
      });
    }
  }, [bannerUrl, adsData]);
  if (!bannerUrl) return null;

  return (
    <Image
      onClick={onClickBanner}
      src={bannerUrl}
      alt="banner preview url"
      className="w-full h-auto cursor-pointer"
    />
  );
};

export default React.memo(OnstreamAdsContainer);
