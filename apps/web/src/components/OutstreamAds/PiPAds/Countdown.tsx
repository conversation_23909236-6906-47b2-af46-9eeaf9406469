import React from 'react';
import classNames from 'classnames';
import styles from './Styles.module.scss';

const Countdown = ({ skipTimer, onClick, className }: any) => (
  <button
    className={classNames(styles.close, className)}
    onClick={onClick}
    type="button"
    disabled={!!skipTimer}
  >
    {skipTimer || (
      <span className="icon icon--tiny">
        <i className="vie vie-times-medium" />
      </span>
    )}
  </button>
);

export default Countdown;
