import React, { useEffect, useMemo, useRef, useState, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { getInventoryId } from '@services/adsServices';
import isEmpty from 'lodash/isEmpty';
import debounce from 'lodash/debounce';
import parseADS from '@components/basic/Player/MastheadAdsPlayer/parse-vast-ads';
import Countdown from './Countdown';
import PiPAdsContainer from './PiPAdsContainer';
import OnstreamAdsContainer from './OnstreamAdsContainer';
import AnimatedDiv from '@components/Animation';
import { STATUS_OUTSTREAM_ADS } from '@constants/constants';
import { aiActiveThirdTracking } from '@apis/aiactiv-third-tracking';
import { requestAdsOverlay } from '@tracking/functions/TrackingAds';

const TYPE_ADS = {
  ONSTREAM: 'ONSTREAM',
  PIP: 'PIP'
};

const PiPAds = ({
  videoCurrentTime,
  dataEventDetails,
  isAdPlay,
  isEndscreen,
  isAdsPaused,
  isPaused,
  isIntro
}: any) => {
  const { pipAds } = useSelector((state: any) => state?.App?.outStreamAds) || {};
  const { statusLoadOutStreamAds } = useSelector((state: any) => state?.App || {});
  const { isOffAds, countdown, id } = pipAds || {};
  const [adsData, setAdsData] = useState<any>(null);
  const [pipAdsList, setPipAdsList] = useState<any>([]);
  const [isAdsVisible, setAdsVisible] = useState(false);
  const [pipTimer, setPipTimer] = useState(countdown || 0);
  const [videoDuration, setVideoDuration] = useState<any>();
  const [imageAdsTimer, setImageAdsTimer] = useState(pipAds?.duration || 0);
  const [isClosing, setClosing] = useState(false);
  const [lastTimecode, setLastTimecode] = useState<any>(null);
  const timerRef = useRef<any>(null);
  const imageAdsTimerRef = useRef<any>(null);
  const closeTimeoutRef = useRef<any>(null);
  const resetLastTimecodeRef = useRef<any>(null);
  const completeTracked = useRef(false);
  // const statusSkip = useRef(false);
  const [statusSkip, setStatusSkip] = useState(false);

  const timecodes = useMemo(
    () => dataEventDetails?.timecodes?.map((timecode: any) => timecode?.start) || [],
    [dataEventDetails?.timecodes]
  );

  const inventoryId = useMemo(() => getInventoryId(id), [id]);
  const videoSrc = useMemo(() => {
    const { video } = adsData?.native || {};
    completeTracked.current = false;
    return !isEmpty(video?.vast) ? parseADS(video?.vast) : null;
  }, [adsData?.native]);

  const adsType = useMemo(() => {
    if (isAdsVisible && !isEmpty(adsData)) {
      if (adsData?.native?.video?.vast) {
        return TYPE_ADS.PIP;
      }
      if (adsData?.native?.previewImage?.url) {
        return TYPE_ADS.ONSTREAM;
      }
    }
    return null;
  }, [adsData, isAdsVisible]);

  const handleTrackingRequestAds = (status: any, errorMsg: any) => {
    const trackingData = {
      inventoryId,
      type: 'onstream_pip',
      status,
      errorMsg
    };
    requestAdsOverlay(trackingData);
  };

  const handleSetAdsDuration = useCallback((duration: any) => {
    setVideoDuration(duration);
  }, []);

  const requestAds = useCallback(
    async (timecode: any) => {
      const res = await window.AiactivSDK.requestAds([
        { inventoryId, placementId: `div-${inventoryId}`, timecode }
      ]);

      if (!isEmpty(res?.[0]?.native)) {
        setAdsData({ ...res[0], success: true });
        setPipAdsList((prevList: any) => [...prevList, res[0]]);
        console.log('Request ads successfully', res);
        setImageAdsTimer(pipAds?.duration || 0);
        setPipTimer(countdown || 0);
        handleTrackingRequestAds('success', '');
      } else {
        handleTrackingRequestAds('failed', res?.[0]?.message || '');
      }
    },
    [inventoryId, pipAds?.duration, countdown]
  );

  const handleClickBanner = useCallback(() => {
    const clickTrackerUrl = adsData?.native?.link?.clickTrackers?.[0];
    if (clickTrackerUrl) {
      window.open(clickTrackerUrl, '_blank');
    }
  }, [adsData?.native?.link?.clickTrackers?.[0]]);

  const handleClose = useCallback(() => {
    setClosing(true);
    setVideoDuration(null);
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
    }
    closeTimeoutRef.current = setTimeout(() => {
      setAdsVisible(false);
      setClosing(false);
      setAdsData(null);
    }, 500);
  }, []);

  const debouncedRequestAds = useCallback(
    debounce((timecode) => {
      requestAds(timecode);
    }, 500),
    [requestAds]
  );

  useEffect(() => {
    if (adsType === TYPE_ADS.PIP && isAdsVisible && pipTimer > 0) {
      timerRef.current = setInterval(() => {
        setPipTimer((prevTimer: any) => prevTimer - 1);
      }, 1000);
    } else if (pipTimer === 0) {
      clearInterval(timerRef.current);
    }
    return () => clearInterval(timerRef.current);
  }, [adsType, isAdsVisible, pipTimer]);

  useEffect(() => {
    if (adsType === TYPE_ADS.ONSTREAM && isAdsVisible && imageAdsTimer > 0) {
      imageAdsTimerRef.current = setInterval(() => {
        setImageAdsTimer((prevTimer: any) => prevTimer - 1);
      }, 1000);
    } else if (imageAdsTimer === 0) {
      clearInterval(imageAdsTimerRef.current);
    }
    return () => clearInterval(imageAdsTimerRef.current);
  }, [adsType, isAdsVisible, imageAdsTimer, handleClose]);

  useEffect(() => {
    if (
      isAdPlay ||
      isEndscreen ||
      isAdsPaused ||
      isOffAds ||
      isIntro ||
      statusLoadOutStreamAds === STATUS_OUTSTREAM_ADS.SUCCESS
    ) {
      handleClose();
      return;
    }
    const currentTimecode = Math.ceil(videoCurrentTime);
    const activeTimecode = timecodes.includes(currentTimecode);

    if (activeTimecode && currentTimecode !== lastTimecode) {
      setAdsVisible(true);
      if (isPaused) return;
      debouncedRequestAds(currentTimecode);
      setLastTimecode(currentTimecode);
      console.log('Request ads');
    }
  }, [
    videoCurrentTime,
    timecodes,
    debouncedRequestAds,
    isAdsPaused,
    isAdPlay,
    isEndscreen,
    isOffAds,
    lastTimecode,
    isPaused,
    isIntro,
    statusLoadOutStreamAds
  ]);

  useEffect(() => {
    if (
      (videoDuration === 0 && adsType === TYPE_ADS.PIP) ||
      (imageAdsTimer === 0 && adsType === TYPE_ADS.ONSTREAM)
    ) {
      if (!completeTracked.current) {
        videoSrc?.vastTracker?.track('complete');
        completeTracked.current = true;
      }
      handleClose();
    }
  }, [videoDuration, imageAdsTimer, adsType, handleClose]);

  useEffect(() => {
    if (lastTimecode) {
      if (resetLastTimecodeRef.current) {
        clearTimeout(resetLastTimecodeRef.current);
      }
      resetLastTimecodeRef.current = setTimeout(() => {
        setLastTimecode(null);
      }, 1000);
    }
    return () => clearTimeout(resetLastTimecodeRef.current);
  }, [lastTimecode]);
  useEffect(() => {
    if (pipAdsList?.length > 1 && adsType === TYPE_ADS.PIP) {
      const lastAd = pipAdsList[pipAdsList?.length - 1];
      const vastData = parseADS(lastAd?.native?.video?.vast);
      if (vastData?.duration) {
        handleSetAdsDuration(vastData.duration);
        completeTracked.current = false;
      }

      const timeoutId = setTimeout(() => {
        setPipAdsList((prevList: any) => prevList.slice(1));
      }, 500);

      return () => clearTimeout(timeoutId);
    }
    return;
  }, [pipAdsList, handleSetAdsDuration, adsType]);

  useEffect(() => {
    if (!isEmpty(videoSrc?.impressionURLTemplates)) {
      aiActiveThirdTracking({
        vadeUrl: adsData?.vade,
        impressionURLTemplates: videoSrc?.impressionURLTemplates
      });
    }
  }, [adsData?.vade, videoSrc?.impressionURLTemplates]);

  if (!id || isOffAds || !inventoryId || !timecodes?.length) return null;

  return (
    <>
      {adsType === TYPE_ADS.PIP &&
        pipAdsList?.length > 0 &&
        pipAdsList?.map((ad: any, index: any) => (
          <AnimatedDiv
            key={ad?.native?.video?.vast || index}
            isVisible={isAdsVisible && !isClosing}
            animationType="fadeInOut"
            className="xl:w-[384px] w-72 absolute right-[4%] transition-all overflow-hidden duration-300 z-[3] xl:bottom-[10%] min-[2560px]:bottom-[8%] bottom-[calc(12%_+_12px)] lg:bottom-[calc(11%_+_6px)]"
          >
            <PiPAdsContainer
              videoSrc={videoSrc}
              onClickBanner={handleClickBanner}
              handleSetAdsDuration={handleSetAdsDuration}
              statusSkip={statusSkip}
              handleSkip={setStatusSkip}
            />
            <Countdown
              onClick={() => {
                handleClose();
                setStatusSkip(true);
              }}
              skipTimer={pipTimer}
            />
          </AnimatedDiv>
        ))}
      {adsType === TYPE_ADS.ONSTREAM && (
        <AnimatedDiv
          isVisible={isAdsVisible && !isClosing}
          animationType="fadeInOut"
          className="xl:w-[384px] w-72 absolute right-[4%] transition-all overflow-hidden duration-300 z-[5] top-[13%]"
        >
          <OnstreamAdsContainer
            bannerUrl={adsData?.native?.previewImage?.url}
            onClickBanner={handleClickBanner}
            adsData={adsData}
          />
          <Countdown
            className="bottom-[8px] left-[8px] !top-[unset] !right-[unset]"
            onClick={handleClose}
            skipTimer={imageAdsTimer}
          />
        </AnimatedDiv>
      )}
    </>
  );
};

export default React.memo(PiPAds);
