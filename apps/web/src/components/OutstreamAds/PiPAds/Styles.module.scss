.pip-ads {
  @apply w-full h-full;
  @apply relative duration-500 delay-100 z-50;
  .countdown {
    @apply absolute bottom-5 left-[8px] text-white;
  }
}

.close {
  @apply absolute top-[8px] right-[8px] w-6 h-6 rounded-full bg-[#111] bg-opacity-70 text-white;
  @apply flex items-center justify-center z-50;
}

.show {
  @apply translate-x-0;
}

.animate-slide-in {
  animation: slide-in 0.5s forwards;
}

.animate-slide-out {
  animation: slide-out 0.5s forwards;
}

@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0.5;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}
