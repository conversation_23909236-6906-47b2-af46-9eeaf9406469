import React, { useEffect, useMemo, useState } from 'react';
import { isMobile } from 'react-device-detect';
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import { GPTWrapper as GPT } from '@components/GPTWrapper';
import AOutstreamAds from '@components/basic/Ads/AOutstreamAds';
import { getInventoryId } from '@services/adsServices';
import { EL_ID } from '@constants/constants';

const BannerVideoIntroBackgroundAds = ({ data }: any) => {
  const [isShowAds, setIsShowAds] = useState(false);
  const [adsSlot, setAdsSlot] = useState<any>(null);

  const inventoryId = useMemo(() => getInventoryId(data?.id), [data?.id]);

  useEffect(
    () => () => {
      clearAds();
    },
    []
  );

  useEffect(() => {
    if (!isEmpty(data)) {
      setIsShowAds(true);
    }
  }, [data]);

  const clearAds = () => {
    if (!isEmpty(adsSlot)) {
      GPT.clear([adsSlot]);
      setAdsSlot(null);
    }
    setIsShowAds(false);
  };

  const onSlotRenderEnded = (event: any) => {
    if (inventoryId) {
      if (event?.success) {
        setIsShowAds(true);
      } else {
        setIsShowAds(false);
      }
    } else {
      const slot = event?.slot;
      setAdsSlot(slot);
      if (event?.isEmpty) {
        setIsShowAds(false);
      }
    }
  };

  if (isMobile || !isShowAds) return null;

  return (
    <div className="banner fixed left top size-w-full">
      {inventoryId ? (
        <AOutstreamAds
          adId={EL_ID.BANNER_INTRO_BACKGROUND}
          inventoryId={inventoryId}
          onSlotRenderEnded={onSlotRenderEnded}
        />
      ) : (
        <GPT
          id={get(data, 'id', '')}
          className="mask__img"
          adUnitPath={get(data, 'path', '')}
          slotSize={get(data, 'size', [])}
          onSlotRenderEnded={onSlotRenderEnded}
        />
      )}
    </div>
  );
};

export default React.memo(BannerVideoIntroBackgroundAds);
