import React, { useRef } from 'react';
import { ID } from '@constants/constants';
import SeoText from '../seo/SeoText';
import CardList from '../basic/Card/CardList';
import Card from '../basic/Card/Card';

const SeoTemplate = (props: any) => {
  const ref = useRef([]);
  const { seoData, dataRibbon, seoTemplateConfig } = props || {};
  const { name } = dataRibbon || {};
  const { title } = seoTemplateConfig || {};

  const renderCardList = () => (
    <>
      {(dataRibbon?.items || []).map((item: any, i: any) => {
        if (!item?.id || item?.id === ID.VIEW_MORE) return null;
        return (
          <Card
            key={item?.id + i || i}
            cardData={item}
            ref={ref}
            index={i + 1}
            notLazy
            randomID={`${item?.id}_${i}`}
          />
        );
      })}
    </>
  );
  return (
    <div className="container canal-v">
      <div className="section__header m-t6 p-t3">
        <h1 className="section__title text-white text-uppercase text-32 font-bold">
          {title || ''}
        </h1>
        <SeoText seo={seoData?.data?.seo} disableH1 />
      </div>
      <div className="section__header p-y3">
        <h2 className="section__title text-white text-uppercase text-20 font-bold">{name || ''}</h2>
      </div>
      <div className="section__body">
        <CardList renderContent={renderCardList} heightCheckScroll={320} />
      </div>
    </div>
  );
};

export default SeoTemplate;
