import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import isEmpty from 'lodash/isEmpty';
import LocalStorage from '@config/LocalStorage';
import { PLAYER_TYPE } from '@constants/player';
import { setIsMasterBanner, getTipData } from '@actions/page';
import { VALUE } from '@config/ConfigSegment';
import TrackingApp from '@tracking/functions/TrackingApp';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import { watchNowBehavior } from '../trigger/triggerFunction';
import Billboard from '../Billboard/Billboard';

const BannerBillboard = ({ dataBanner, classBanner, isCollectionBanner }: any) => {
  const bannerPlayer = useRef<any>(null);
  const router = useVieRouter();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const activeMenu = useSelector((state: any) => state?.Menu?.activeMenu);
  const activeSubMenu = useSelector((state: any) => state?.Menu?.activeSubMenu);
  const { isLoadedData, isMobile } = useSelector((state: any) => state?.App || {});
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile || {});

  useEffect(() => {
    dispatch(setIsMasterBanner(true));
    setLoading(!loading);
    return () => {
      dispatch(setIsMasterBanner(false));
    };
  }, []);

  useEffect(() => {
    if (dataBanner?.id) {
      dispatch(getTipData({ id: dataBanner?.id }));
    }
  }, [dataBanner?.id]);

  const onClickBanner = async () => {
    await watchNowBehavior({
      profile,
      currentProfile,
      contentData: dataBanner,
      router,
      dispatch,
      expand: false,
      isMobile
    });
    TrackingApp.contentSelected({
      data: {
        ...dataBanner,
        seasonThumb: dataBanner?.images?.thumbnail,
        seasonGenre: dataBanner?.genreText
      },
      clickType: VALUE.HOVER_CLICK,
      activeMenu,
      activeSubMenu
    });
  };

  const onSetupPlayer = ({ player }: any) => {
    bannerPlayer.current = player;
  };

  const callbackOnDetail = () => {
    const currentTime = bannerPlayer.current?.ended ? 0 : bannerPlayer.current?.currentTime || 0;
    if (currentTime > 0) {
      ConfigLocalStorage.set(
        LocalStorage.BANNER_TRAILER_PROGRESS,
        JSON.stringify({ [dataBanner?.id]: currentTime })
      );
    }
  };

  if (isEmpty(dataBanner)) return null;

  return (
    <Billboard
      isCollectionBanner={isCollectionBanner}
      className={classBanner}
      billboardData={dataBanner}
      onSetupPlayer={onSetupPlayer}
      playerId={`${PLAYER_TYPE.BANNER_COLLECTION}-${dataBanner?.id}`}
      callbackOnDetail={callbackOnDetail}
      onClickBanner={onClickBanner}
      noPlayer={!isLoadedData}
    />
  );
};

export default BannerBillboard;
