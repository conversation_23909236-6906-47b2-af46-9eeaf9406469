.animate-slide-in {
  animation: slide-in 0.5s forwards;
}

.animate-slide-out {
  animation: slide-out 0.5s forwards;
}

.animate-fade-in {
  animation: fade-in 0.65s forwards;
}

.animate-fade-out {
  animation: fade-out 0.65s forwards;
}
@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0.5;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
