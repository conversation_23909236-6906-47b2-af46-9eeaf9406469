import React, { useEffect, useMemo, useState, useRef } from 'react';
import styles from './styles.module.scss';

const AnimatedDiv = ({ isVisible, animationType, children, className }: any) => {
  const [showChildren, setShowChildren] = useState(isVisible);
  const [isClosing, setClosing] = useState(false);
  const timeoutRef = useRef<any>(null);

  useEffect(() => {
    if (isVisible) {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      setShowChildren(true);
      setClosing(false);
    } else {
      setClosing(true);
      timeoutRef.current = setTimeout(() => {
        setShowChildren(false);
        setClosing(false);
      }, 500);
    }

    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [isVisible]);

  const animationClass = useMemo(() => {
    switch (animationType) {
      case 'slideRightToLeft':
        return isClosing ? styles['animate-slide-out'] : styles['animate-slide-in'];
      case 'slideLeftToRight':
        return isClosing ? styles['animate-slide-in'] : styles['animate-slide-out'];
      case 'fadeInOut':
        return isClosing ? styles['animate-fade-out'] : styles['animate-fade-in'];
      default:
        return isClosing ? styles['animate-fade-out'] : styles['animate-fade-in'];
    }
  }, [animationType, isClosing]);
  if (!showChildren) return null;

  return <div className={`${className} ${animationClass}`}>{children}</div>;
};

export default React.memo(AnimatedDiv);
