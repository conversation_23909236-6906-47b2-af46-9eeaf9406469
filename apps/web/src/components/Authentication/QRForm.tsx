import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { QRCode } from 'react-qrcode-logo';
import { useDispatch, useSelector } from 'react-redux';
import { getProfile } from '@actions/profile';
import get from 'lodash/get';
import classNames from 'classnames';
import UserApi from '@apis/userApi';
import { handleToastWhenAccessVieON, saveAccessToken } from '@helpers/common';
import { setToken } from '@actions/app';
import { useVieRouter, useViewport } from '@customHook';
import Button from '../basic/Buttons/Button';
import ConfigImage from '@config/ConfigImage';
import { TEXT } from '@constants/text';

const TEXT_CONTENT_QR_CODE = `Quét mã QR bằng <b class="text-white font-bold">Camera trên điện thoại</b> hoặc <b class="text-white font-bold">máy t<PERSON>h bảng</b> để đăng nhập`;

const QRForm = () => {
  const [userCode, setUserCode] = useState<any>('');
  const [hasError, setHasError] = useState(false);
  const [expiresIn, setExpiresIn] = useState<any>(null);
  const [qrCodeSize, setQrCodeSize] = useState(140);

  const isFetchingCodeRef = useRef(false);
  const codeToCheckStatusRef = useRef<any>('');
  const timeToRefetchCodeRef = useRef<any>(0);
  const timerFetchStatusRef = useRef<any>(null);
  const countdownIntervalRef = useRef<any>(null);
  const dispatch = useDispatch();
  const router = useVieRouter();

  const viewport = useViewport();
  const isSmallViewport = viewport.width < 1450;

  const { deviceModel, deviceId, deviceName, deviceType, isMobile, isTablet } = useSelector(
    (state: any) => state?.App || {}
  );
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { bg } = useSelector((state: any) => state?.App?.webConfig || {});
  const { authentication } = bg || {};
  const destination = useMemo(() => get(router, 'query.destination', '/'), [router]);

  const clearTimer = () => {
    if (timerFetchStatusRef.current) {
      clearInterval(timerFetchStatusRef.current);
      timerFetchStatusRef.current = null;
    }
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
      countdownIntervalRef.current = null;
    }
  };

  const formatTime = (seconds: any) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
  };

  const shortLinkQRCode = useMemo(() => {
    if (authentication?.shortLink || authentication?.shortLinkUS) {
      if (isGlobal) return authentication?.shortLinkUS;
      return authentication?.shortLink;
    }
    return 'vieon.vn';
  }, [isGlobal, authentication]);

  const refreshQRCode = useCallback(async () => {
    fetchCode();
  }, []);

  const fetchCode = useCallback(async () => {
    clearTimer();
    setHasError(false);

    if (isFetchingCodeRef.current) return;
    isFetchingCodeRef.current = true;

    try {
      const response = await UserApi.authenticationDeviceLogin({
        model: deviceModel,
        deviceId,
        deviceName,
        deviceType
      });

      const { code, expiresIn, interval, userCode } = response?.data || {};
      if (code && response?.success) {
        setUserCode(userCode);
        setExpiresIn(expiresIn);
        timeToRefetchCodeRef.current = Date.now() + expiresIn * 1000;
        codeToCheckStatusRef.current = code;
        countdownIntervalRef.current = setInterval(() => {
          setExpiresIn((prev: any) => {
            if (prev === 1) {
              clearTimer();
              fetchCode();
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
        timerFetchStatusRef.current = setInterval(async () => {
          await fetchStatus();
        }, interval * 1000);
      } else {
        setUserCode('');
        codeToCheckStatusRef.current = '';
        setHasError(true);
      }
    } catch (error) {
      setHasError(true);
    } finally {
      isFetchingCodeRef.current = false;
    }
  }, [deviceModel, deviceId, deviceName, deviceType]);

  const fetchStatus = useCallback(async () => {
    if (codeToCheckStatusRef.current) {
      const response = await UserApi.getAuthenticationDeviceLoginStatus(
        codeToCheckStatusRef.current
      );
      const { accessToken, refreshToken, code } = response?.data || {};
      if (code === 0) {
        if (accessToken) {
          saveAccessToken(accessToken, '', refreshToken);
          dispatch(setToken(accessToken));
          dispatch(
            getProfile({
              deviceModel,
              deviceName,
              deviceType,
              isMobile,
              isTablet,
              accessToken,
              isLoginByQRCode: true,
              destination,
              router
            })
          );
          handleToastWhenAccessVieON({
            isLogin: true,
            dispatch,
            destination
          });
        }
        clearTimer();
      }
    }
  }, [dispatch]);

  useEffect(() => {
    fetchCode();

    return () => {
      clearTimer();
      isFetchingCodeRef.current = false;
    };
  }, [fetchCode]);

  useEffect(() => {
    if (isSmallViewport) {
      setQrCodeSize(115);
    } else {
      setQrCodeSize(140);
    }
  }, [isSmallViewport]);

  return (
    <div className="text-center -mx-1.5">
      <div
        className={classNames(
          'rounded-[10px] w-max h-auto mx-auto overflow-hidden relative',
          hasError &&
            'after:bg-white after:rounded-[10px] after:w-full after:h-full after:absolute after:top-0 after:left-0 after:z-[1] after:opacity-80 transition-all'
        )}
      >
        <QRCode
          logoWidth={isSmallViewport ? 30 : 35}
          logoImage={ConfigImage.logoVieONInQRCode}
          value={`${shortLinkQRCode}?code=${userCode}`}
          size={qrCodeSize}
        />
      </div>

      <div
        className="text-[#ccc] md:text-base text-sm 3xl:pt-6 pt-4"
        dangerouslySetInnerHTML={{ __html: TEXT_CONTENT_QR_CODE }}
      />

      <div className="text-[#DEDEDE] md:text-base text-sm 3xl:pt-6 pt-4 lg:pt-2 font-medium">
        {!hasError ? (
          <>Mã sẽ hết hạn sau {formatTime(expiresIn)}</>
        ) : (
          <>
            <span>{TEXT.QR_CODE_ERROR}, </span>
            <Button className="text-[#0AD418] pl-1" title="lấy mã khác" onClick={refreshQRCode} />
          </>
        )}
      </div>
    </div>
  );
};

export default React.memo(QRForm);
