import React from 'react';
import VieLink from '@components/VieLink';
import { DOMAIN_WEB } from '@config/ConfigEnv';
import VieLogo from '@components/basic/Icon/SvgIcon/VieLogo';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import LocalStorage from '@config/LocalStorage';
import Style from './Authentication.module.scss';

const HeaderGlobalAuth = () => {
  const onHandleClick = () => {
    ConfigLocalStorage.remove(LocalStorage.RE_LOGIN_PARAMS);
  };
  return (
    <div className={Style.header}>
      <VieLink href={`${DOMAIN_WEB}/`}>
        <a className={Style.logo} onClick={onHandleClick}>
          <VieLogo />
        </a>
      </VieLink>
    </div>
  );
};

export default HeaderGlobalAuth;
