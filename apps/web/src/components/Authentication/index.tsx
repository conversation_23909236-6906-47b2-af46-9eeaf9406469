import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import moment from 'moment';
import {
  AUTH_PROVIDER,
  DATE_FORMAT,
  FLOW_GLOBAL_AUTH,
  HTTP_CODE,
  PAGE,
  POPUP,
  PROFILE_STATUS,
  TVOD
} from '@constants/constants';
import {
  globalConfirmOTP,
  globalForgotPassword,
  globalLinkPhoneNumber,
  globalLogin,
  globalRegister,
  globalRestoreAccount,
  globalUpdatePassword,
  globalValidateOTP,
  resetGlobalAuth
} from '@actions/globalAuth';
import { setOffBindAccount, setToast, setToken } from '@actions/app';
import { getProfile, getProfileSuccess } from '@actions/profile';
import { openPopup } from '@actions/popup';
import {
  LOGIN_INPUT_TYPE,
  OTP_TYPE,
  TYPE_AUTH,
  TYPE_RECEIVE_OTP,
  TYPE_TRIGGER_AUTH
} from '@constants/types';
import OptionToSelect from '@components/Modal/OptionToSelect';
import PhoneOrEmailSelected from '@components/Modal/PhoneOrEmailSelected';
import Login from '@components/Modal/Login';
import OTPVerify from '@components/Modal/OTPVerify';
import PasswordUpdate from '@components/Modal/PasswordUpdate';
import {
  checkTvodRented,
  decodeParamDestination,
  handleSaveBindAccountInfo,
  removeAccessToken
} from '@helpers/common';
import LocalStorage from '@config/LocalStorage';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import {
  EVENT_NAME,
  FLOW_AUTHEN,
  trackingAuth,
  trackingStartedAuthen
} from '@tracking/functions/TrackingAuthentication';
import { TEXT } from '@constants/text';
import HeaderGlobalAuth from './Header';
import classNames from 'classnames';
import Style from './Authentication.module.scss';
import { VALUE } from '@/config/ConfigSegment';
import { trackAuthenLoaded } from '../../tracking/functions/TrackingAuthentication';
import { destinationLogin } from '@services/multiProfileServices';
import { useReCaptcha } from '@/hooks/useRecaptcha';
import { ACTION_GLOBAL_FORGOT_PASSWORD, ACTION_GLOBAL_REGISTER } from '@/actions/actionType';

const Authentication = ({ mainFlow, titlePopupTriggerAuth, customClassName }: any) => {
  const router = useVieRouter();
  const { pathname, query } = router || {};
  const dispatch = useDispatch();
  const { getToken } = useReCaptcha();
  const refFlowBefore = useRef<any>(null);
  const refIsMustLogoutWhenCancelBindAccount = useRef<any>(null);
  const destination = useMemo(() => get(router, 'query.destination', '/'), [router?.query]);
  const pagePath = useMemo(() => get(router, 'query.page', '/'), [router?.query]);
  const trigger = useMemo(() => get(router, 'query.trigger', ''), [router?.query]);

  const isBindPhoneNumber = useMemo(
    () => get(router, 'query.isBindPhoneNumber', false),
    [router?.query]
  );
  const {
    isMobile,
    deviceModel,
    deviceName,
    deviceType,
    deviceId,
    geoCheck,
    webConfig,
    hideClickLater,
    bindAccount
  } = useSelector((state: any) => state?.App || {});
  const {
    dataRegister,
    dataLogin,
    dataValidateOTP,
    dataConfirmOTP,
    dataForgotPassword,
    dataRestoreAccount,
    confirmationNo,
    dataAccessToken,
    dataUpdatePassword,
    dataLinkPhoneNumber
  } = useSelector((state: any) => state?.GlobalAuth || {});
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const isOnlySupportVN = useMemo(
    () => get(webConfig, 'globalAuthentication.onlySupportVN', false),
    [webConfig]
  );
  const isGlobal = geoCheck?.isGlobal;
  const fieldImgBg = useMemo(() => {
    const field = geoCheck?.geo_country === 'US' ? 'US' : 'VN';
    if (isMobile) {
      return `mobile${field}`;
    }
    return field;
  }, [geoCheck, isMobile]);
  const bgPage = useMemo(() => webConfig?.bg?.authentication[fieldImgBg], [webConfig, fieldImgBg]);
  const [flow, setFlow] = useState(FLOW_GLOBAL_AUTH.PHONE);
  const [userName, setUserName] = useState<any>('');
  const [countryCode, setCountryCode] = useState<any>({ value: '' });
  const [password, setPassword] = useState<any>('');
  const [oldPassword, setOldPassword] = useState<any>('');
  const [code, setCode] = useState<any>('');
  const [typeReceiveOTP, setTypeReceiveOTP] = useState<any>('');
  const [inputType, setInputType] = useState(LOGIN_INPUT_TYPE.UNKNOWN);
  const isPhoneNumber = useMemo(() => inputType === LOGIN_INPUT_TYPE.PHONE, [inputType]);

  const dataSendOTP = useMemo(() => {
    if (
      flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_OTP ||
      flow === FLOW_GLOBAL_AUTH.BIND_PHONE_OTP
    ) {
      return dataLinkPhoneNumber;
    }
    if (
      flow === FLOW_GLOBAL_AUTH.PHONE_REGISTER_OTP ||
      flow === FLOW_GLOBAL_AUTH.EMAIL_REGISTER_OTP ||
      flow === FLOW_GLOBAL_AUTH.PHONE_REGISTER_PASSWORD ||
      flow === FLOW_GLOBAL_AUTH.EMAIL_REGISTER_PASSWORD
    ) {
      return dataRegister;
    }
    if (flow === FLOW_GLOBAL_AUTH.RESTORE_ACCOUNT_OTP) {
      return dataRestoreAccount;
    }
    if (
      flow === FLOW_GLOBAL_AUTH.UPDATE_PASSWORD_OTP ||
      flow === FLOW_GLOBAL_AUTH.UPDATE_PASSWORD_SET_PASS
    ) {
      return dataUpdatePassword;
    }
    return dataForgotPassword;
  }, [
    flow,
    dataForgotPassword,
    dataRestoreAccount,
    dataRegister,
    dataUpdatePassword,
    dataLinkPhoneNumber
  ]);
  const dataVerify = useMemo(() => {
    if (flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER || flow === FLOW_GLOBAL_AUTH.BIND_PHONE) {
      return dataLinkPhoneNumber;
    }
    return dataRegister;
  }, [dataRegister, dataLinkPhoneNumber, flow]);

  const userNameRequest = useMemo(() => {
    if (isPhoneNumber && get(countryCode, 'value', '') === 'VN' && userName.length === 9) {
      return `0${userName}`;
    }
    return userName;
  }, [userName, isPhoneNumber, countryCode]);

  const isCanSwitchTypeReceiveOTP = useMemo(
    () => profile?.emailVerified && profile?.phoneVerified,
    [profile]
  );

  const isNotGoToLobby = useMemo(() => {
    const reLoginParams: any = ConfigLocalStorage.get(LocalStorage.RE_LOGIN_PARAMS);
    const { url } = JSON.parse(reLoginParams || '{}');
    const urlResultParams = new URLSearchParams(url?.slice(url.indexOf('?')));
    const query = Object.fromEntries(urlResultParams.entries());
    if (query?.isTriggerAuth) return true;

    const decodeDestination = decodeParamDestination(destination);
    const notGoToLobbyBecauseOfPage =
      decodeDestination.includes(PAGE.PAYMENT) ||
      decodeDestination.includes(PAGE.VOUCHER) ||
      decodeDestination.includes(PAGE.PROFILE) ||
      decodeDestination.includes(PAGE.INAPP) ||
      decodeDestination.includes(PAGE.LINK);

    const isRegister =
      flow === FLOW_GLOBAL_AUTH.PHONE_REGISTER_OTP ||
      flow === FLOW_GLOBAL_AUTH.PHONE_REGISTER_PASSWORD ||
      flow === FLOW_GLOBAL_AUTH.EMAIL_REGISTER_PASSWORD;

    return notGoToLobbyBecauseOfPage || (isRegister && trigger);
  }, [destination, trigger, flow]);

  const canBack = useMemo(
    () =>
      flow === FLOW_GLOBAL_AUTH.PHONE ||
      flow === FLOW_GLOBAL_AUTH.PHONE_LOGIN ||
      flow === FLOW_GLOBAL_AUTH.PHONE_FORGOT_PASSWORD_OTP ||
      flow === FLOW_GLOBAL_AUTH.RESET_PASSWORD ||
      flow === FLOW_GLOBAL_AUTH.PHONE_REGISTER_OTP ||
      flow === FLOW_GLOBAL_AUTH.PHONE_REGISTER_PASSWORD ||
      flow === FLOW_GLOBAL_AUTH.EMAIL ||
      flow === FLOW_GLOBAL_AUTH.EMAIL_LOGIN ||
      flow === FLOW_GLOBAL_AUTH.EMAIL_FORGOT_PASSWORD_OTP ||
      flow === FLOW_GLOBAL_AUTH.EMAIL_REGISTER_OTP ||
      flow === FLOW_GLOBAL_AUTH.EMAIL_REGISTER_PASSWORD ||
      flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_OTP ||
      flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_SET_PASS ||
      flow === FLOW_GLOBAL_AUTH.BIND_PHONE ||
      flow === FLOW_GLOBAL_AUTH.BIND_PHONE_OTP ||
      flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER,
    [flow]
  );
  const isHasBtnSetupLaterBindAccount = useMemo(
    () =>
      !isGlobal &&
      profile?.isPremium &&
      !hideClickLater &&
      (flow === FLOW_GLOBAL_AUTH.BIND_PHONE ||
        (flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER && isBindPhoneNumber)),
    [flow, profile, hideClickLater, isBindPhoneNumber, isGlobal]
  );

  useEffect(() => {
    if (mainFlow) {
      setFlow(mainFlow);
    }
  }, []);

  useEffect(() => {
    const isPhoneFlow = [
      FLOW_GLOBAL_AUTH.PHONE,
      FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER,
      FLOW_GLOBAL_AUTH.BIND_PHONE
    ].includes(flow);

    if (isPhoneFlow && !isGlobal) {
      setInputType(LOGIN_INPUT_TYPE.PHONE);
    } else if (flow === FLOW_GLOBAL_AUTH.EMAIL) {
      setInputType(LOGIN_INPUT_TYPE.EMAIL);
    }
  }, [flow, userName, isGlobal]);

  const saveFlowBefore = () => {
    refFlowBefore.current = flow;
  };

  const handleChangeTypeReceiveOTP = () => {
    if (typeReceiveOTP === TYPE_RECEIVE_OTP.EMAIL) {
      setTypeReceiveOTP(TYPE_RECEIVE_OTP.PHONE);
    } else {
      setTypeReceiveOTP(TYPE_RECEIVE_OTP.EMAIL);
    }
  };

  const handleBackFirstFlow = () => {
    setFlow(FLOW_GLOBAL_AUTH.PHONE);
    setUserName('');
    setCountryCode('');
    setPassword('');
    setCode('');
    dispatch(resetGlobalAuth());
  };

  const handleRestoreAccount = ({ dataProfile, isLoginSocial }: any) => {
    const deletedAt = get(dataProfile, 'deletedAt', '');
    const dateTime = moment(deletedAt * 1000);
    const dateTimeFormat = deletedAt ? dateTime.format('HH:mm DD/MM/YYYY') : 0;
    dispatch(
      openPopup({
        name: POPUP.NAME.USER_RESTORE,
        isLoginSocial,
        phoneNumber: dataProfile?.mobile || '',
        deletedAt: dateTimeFormat,
        onBack: handleBackFirstFlow,
        onSubmit: onRestoreAccount
      })
    );
  };

  const handleChangeFlow = (data: any) => {
    setFlow(data);
  };

  const handleChangeUserName = (data: any) => {
    setUserName(data.trim());
  };

  const onSkipBindAccount = () => {
    dispatch(setOffBindAccount(true));
    handleSaveBindAccountInfo();
    if (flow === FLOW_GLOBAL_AUTH.BIND_PHONE && !isNotGoToLobby) {
      if (destination) {
        router.push(`${PAGE.LOBBY_PROFILES}/?destination=${destination}`);
      } else {
        handlePushPath({ isTvodSvod: false, urlTvodSvod: '' });
      }
    }
  };

  const onTrackingLoginSuccessfully = (isLoginBySocial: any) => {
    const typeTrigger = isLoginBySocial
      ? 'social_account'
      : isPhoneNumber
      ? 'mobile'
      : LOGIN_INPUT_TYPE.EMAIL;
    const loginType = ConfigLocalStorage.get(LocalStorage.LOGIN_TYPE);
    const { mobile, email, id } = profile || {};
    const loginAuto = !loginType;
    trackingAuth({
      event: EVENT_NAME.LOGIN_SUCCESSFULLY,
      typeTrigger,
      isUseFlowAuthenProperty: true,
      loginMethod: mobile ? VALUE.PHONE : VALUE.SOCIAL,
      isAuto: !!loginAuto,
      flowName: pathname === PAGE.RENTAL_CONTENT ? TVOD.TVOD_TYPE : '',
      userId: id,
      mobile,
      email,
      query
    });
  };

  const onTrackingSignUpSuccessfully = () => {
    trackingAuth({
      event: EVENT_NAME.SIGN_UP_SUCCESSFULLY,
      typeTrigger: trigger,
      isUseFlowAuthenProperty: false,
      typeRegister: isPhoneNumber ? 'mobile' : LOGIN_INPUT_TYPE.EMAIL
    });
  };

  const onGetProfile = () => {
    dispatch(getProfile({ deviceModel, deviceName, deviceType }));
  };

  const onVerifyToLinkPhoneNumber = () => {
    if (userNameRequest) {
      dispatch(
        globalLinkPhoneNumber({
          phoneNumber: userNameRequest,
          countryCode: get(countryCode, 'value', 'VN')
        })
      );
    }
  };

  const onVerifyUserName = async () => {
    const recaptchaToken = await getToken('signup');

    if (recaptchaToken && userNameRequest) {
      if (flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER || flow === FLOW_GLOBAL_AUTH.BIND_PHONE) {
        onVerifyToLinkPhoneNumber();
      } else {
        const res = await dispatch(
          globalRegister({
            userName: userNameRequest,
            countryCode: isPhoneNumber ? get(countryCode, 'value', 'VN') : '',
            model: deviceModel,
            deviceName,
            deviceType,
            deviceId,
            captchaToken: recaptchaToken
          })
        );

        // Handle tracking
        const resCode: any = await get(res, 'data.data.code', '');
        if (resCode === HTTP_CODE.ACCOUNT_LINKED) {
          trackingStartedAuthen(trigger, FLOW_AUTHEN.SIGN_IN);
        } else if (resCode === HTTP_CODE.ACCOUNT_NOT_LINKED || resCode === HTTP_CODE.OTP_LIMITED) {
          trackingStartedAuthen(trigger, FLOW_AUTHEN.SIGN_UP);
        }

        const resType: any = await get(res, 'type', '');
        const resStatus: any = await get(res, 'data.success', '');

        if (!resStatus && resType === ACTION_GLOBAL_REGISTER) {
          let messageError: string = await get(res, 'data.data.message', '');
          dispatch(setToast({ message: messageError || TEXT.REQUEST_LIMIT }));
        }
      }
    }
  };

  const onForgotPassword = async (type: any) => {
    const recaptchaToken = await getToken('password_reset');

    if (recaptchaToken && userNameRequest) {
      const res = await dispatch(
        globalForgotPassword({
          userName: userNameRequest,
          countryCode: isPhoneNumber ? get(countryCode, 'value', 'VN') : '',
          model: deviceModel,
          deviceName,
          deviceType,
          deviceId,
          notificationType: type,
          captchaToken: recaptchaToken
        })
      );

      const resType: any = await get(res, 'type', '');
      const resStatus: any = await get(res, 'data.success', '');

      if (!resStatus && resType === ACTION_GLOBAL_FORGOT_PASSWORD) {
        let messageError: string = await get(res, 'data.data.message', '');
        dispatch(setToast({ message: messageError || TEXT.REQUEST_LIMIT }));
      }
    }
  };

  const onRestoreAccount = () => {
    if (dataAccessToken) {
      dispatch(
        globalRestoreAccount({
          accessToken: dataAccessToken,
          model: deviceModel,
          deviceName,
          deviceType,
          deviceId,
          router
        })
      );
    }
  };

  const onUpdatePassword = (priority: any, notiType?: any) => {
    let notificationType = OTP_TYPE.SMS;
    if (!notiType) {
      if (profile?.emailVerified) {
        notificationType = OTP_TYPE.MAIL;
      }
    } else {
      notificationType = notiType;
      priority = notificationType === OTP_TYPE.MAIL ? 1 : 2;
    }
    if (priority) {
      dispatch(
        globalUpdatePassword({
          priority,
          notificationType
        })
      );
    }
  };

  const handleChangeOldPassword = (data: any) => {
    setOldPassword(data);
  };

  const handleChangePassword = (data: any) => {
    setPassword(data);
  };

  const handleChangeCountryCode = (data: any) => {
    setCountryCode(data);
  };

  const handleResendOTP = (type: any) => {
    if (
      flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_OTP ||
      flow === FLOW_GLOBAL_AUTH.BIND_PHONE_OTP
    ) {
      onVerifyToLinkPhoneNumber();
      return;
    }
    if (
      flow === FLOW_GLOBAL_AUTH.PHONE_REGISTER_OTP ||
      flow === FLOW_GLOBAL_AUTH.EMAIL_REGISTER_OTP
    ) {
      onVerifyUserName();
      return;
    }
    if (flow === FLOW_GLOBAL_AUTH.RESTORE_ACCOUNT_OTP) {
      onRestoreAccount();
      return;
    }
    if (flow === FLOW_GLOBAL_AUTH.UPDATE_PASSWORD_OTP) {
      onUpdatePassword(typeReceiveOTP, type);
      return;
    }
    onForgotPassword(type);
  };

  const handleChangeOTPCode = (data: any) => {
    setCode(data);
    if (data?.length === 4) {
      onValidateOTP(data);
    }
  };

  const handleRemoveUserLogin = () => {
    removeAccessToken();
    dispatch(setToken(''));
    dispatch(getProfileSuccess({}));
    ConfigLocalStorage.remove(LocalStorage.LOGIN_TYPE);
  };

  const handleCancelBindAccount = () => {
    if (refIsMustLogoutWhenCancelBindAccount.current) {
      dispatch(setOffBindAccount(true));
      handleRemoveUserLogin();
    }
  };

  const onConfirmOTP = (data?: any) => {
    const { registerGender, registerDOB } = data || {};
    dispatch(
      globalConfirmOTP({
        confirmationNo,
        code,
        destination,
        gender: Number.isNaN(registerGender?.id) ? 3 : registerGender?.id,
        dob: registerDOB ? moment(registerDOB).format(DATE_FORMAT.YYMMDD.toUpperCase()) : '',
        hasToSelectProfileDefault: isNotGoToLobby,
        isRegister:
          flow === FLOW_GLOBAL_AUTH.PHONE_REGISTER_PASSWORD ||
          flow === FLOW_GLOBAL_AUTH.EMAIL_REGISTER_PASSWORD,
        password: flow === FLOW_GLOBAL_AUTH.RESTORE_ACCOUNT_OTP ? '' : password,
        oldPassword: flow === FLOW_GLOBAL_AUTH.UPDATE_PASSWORD_SET_PASS ? oldPassword : '',
        accessToken: flow === FLOW_GLOBAL_AUTH.RESTORE_ACCOUNT_OTP ? dataAccessToken : '',
        router
      })
    );
  };

  const onValidateOTP = (pinCode: any) => {
    if (pinCode || code) {
      dispatch(
        globalValidateOTP({
          code: pinCode || code,
          confirmationNo
        })
      );
    }
  };

  const onLogin = () => {
    if (userNameRequest && password) {
      dispatch(
        globalLogin({
          userName: userNameRequest,
          password,
          countryCode: isPhoneNumber ? get(countryCode, 'value', 'VN') : '',
          model: deviceModel,
          deviceName,
          deviceType,
          deviceId,
          destination,
          isLoggedByEmail: !isPhoneNumber,
          router
        })
      );
    }
  };

  const onBack = () => {
    switch (flow) {
      case FLOW_GLOBAL_AUTH.PHONE:
      case FLOW_GLOBAL_AUTH.EMAIL:
        handleBackFirstFlow();
        break;
      case FLOW_GLOBAL_AUTH.EMAIL_LOGIN:
      case FLOW_GLOBAL_AUTH.EMAIL_REGISTER_OTP:
      case FLOW_GLOBAL_AUTH.EMAIL_REGISTER_PASSWORD:
        dispatch(resetGlobalAuth('dataValidateOTP'));
        dispatch(resetGlobalAuth('dataRegister'));
        setFlow(FLOW_GLOBAL_AUTH.EMAIL);
        break;
      case FLOW_GLOBAL_AUTH.PHONE_LOGIN:
      case FLOW_GLOBAL_AUTH.PHONE_REGISTER_OTP:
      case FLOW_GLOBAL_AUTH.PHONE_REGISTER_PASSWORD:
        dispatch(resetGlobalAuth('dataValidateOTP'));
        dispatch(resetGlobalAuth('dataRegister'));
        dispatch(resetGlobalAuth('dataLogin'));
        setFlow(FLOW_GLOBAL_AUTH.PHONE);
        break;
      case FLOW_GLOBAL_AUTH.PHONE_FORGOT_PASSWORD_OTP:
      case FLOW_GLOBAL_AUTH.EMAIL_FORGOT_PASSWORD_OTP:
      case FLOW_GLOBAL_AUTH.RESET_PASSWORD:
        dispatch(resetGlobalAuth('dataValidateOTP'));
        dispatch(resetGlobalAuth('dataForgotPassword'));
        if (isPhoneNumber) {
          setFlow(FLOW_GLOBAL_AUTH.PHONE_LOGIN);
        } else {
          setFlow(FLOW_GLOBAL_AUTH.EMAIL_LOGIN);
        }
        break;
      case FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_OTP:
      case FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_SET_PASS:
        dispatch(resetGlobalAuth('dataValidateOTP'));
        dispatch(resetGlobalAuth('dataLinkPhoneNumber'));
        setFlow(FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER);
        break;
      case FLOW_GLOBAL_AUTH.BIND_PHONE:
        dispatch(resetGlobalAuth('dataLogin'));
        handleRemoveUserLogin();
        setFlow(refFlowBefore.current);
        break;
      case FLOW_GLOBAL_AUTH.BIND_PHONE_OTP:
        dispatch(resetGlobalAuth('dataValidateOTP'));
        dispatch(resetGlobalAuth('dataLinkPhoneNumber'));
        setFlow(FLOW_GLOBAL_AUTH.BIND_PHONE);
        break;
      case FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER:
        if (
          trigger &&
          (trigger === TYPE_TRIGGER_AUTH.SOCIAL_UPDATE_PHONE ||
            trigger === TYPE_TRIGGER_AUTH.PROFILE_UPDATE_PHONE)
        ) {
          router.back();
        }
        break;
      default:
        break;
    }
  };

  const handleReloadPage = () => {
    handleCancelBindAccount();
    return false;
  };

  useEffect(() => {
    window.addEventListener('beforeunload', handleReloadPage);
    return () => {
      window.removeEventListener('beforeunload', handleReloadPage);
      handleCancelBindAccount();
      dispatch(resetGlobalAuth());
    };
  }, []);

  useEffect(() => {
    refIsMustLogoutWhenCancelBindAccount.current =
      !isGlobal &&
      (!profile?.isPremium || hideClickLater) &&
      (flow === FLOW_GLOBAL_AUTH.BIND_PHONE ||
        (flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER && isBindPhoneNumber) ||
        ((flow === FLOW_GLOBAL_AUTH.BIND_PHONE_OTP ||
          flow === FLOW_GLOBAL_AUTH.BIND_PHONE_SET_PASS) &&
          (isEmpty(dataConfirmOTP) ||
            (!isEmpty(dataConfirmOTP) &&
              (!dataConfirmOTP.success ||
                get(dataConfirmOTP, 'data.code', HTTP_CODE.FAIL) === HTTP_CODE.FAIL)))));
  }, [flow, profile, hideClickLater, isBindPhoneNumber, dataConfirmOTP, isGlobal]);

  useEffect(() => {
    // debugger;
    if (flow === FLOW_GLOBAL_AUTH.UPDATE_PASSWORD_OTP) {
      if (profile?.emailVerified) {
        setTypeReceiveOTP(TYPE_RECEIVE_OTP.EMAIL);
      } else {
        setTypeReceiveOTP(TYPE_RECEIVE_OTP.PHONE);
      }
    } else if (flow === FLOW_GLOBAL_AUTH.RESTORE_ACCOUNT_OTP) {
      if (
        get(dataLogin, 'data.profile.emailVerified', false) &&
        get(dataLogin, 'data.profile.phoneVerified', false)
      ) {
        if (get(dataLogin, 'data.profile.provider', '') === AUTH_PROVIDER.EMAIL) {
          setTypeReceiveOTP(TYPE_RECEIVE_OTP.EMAIL);
        } else {
          setTypeReceiveOTP(TYPE_RECEIVE_OTP.PHONE);
        }
      } else if (get(dataLogin, 'data.profile.emailVerified', false)) {
        setTypeReceiveOTP(TYPE_RECEIVE_OTP.EMAIL);
      } else if (get(dataLogin, 'data.profile.phoneVerified', false)) {
        setTypeReceiveOTP(TYPE_RECEIVE_OTP.PHONE);
      }
    }
  }, [flow, profile, dataLogin]);

  useEffect(() => {
    if (flow === FLOW_GLOBAL_AUTH.UPDATE_PASSWORD_OTP && typeReceiveOTP) {
      onUpdatePassword(typeReceiveOTP);
    }
  }, [flow, typeReceiveOTP]);

  useEffect(() => {
    if (typeReceiveOTP && profile?.id) {
      setUserName(typeReceiveOTP === TYPE_RECEIVE_OTP.PHONE ? profile?.mobile : profile?.email);
    }
  }, [typeReceiveOTP, profile]);

  useEffect(() => {
    if (typeReceiveOTP && get(dataLogin, 'data.profile.id', '')) {
      setUserName(
        typeReceiveOTP === TYPE_RECEIVE_OTP.PHONE
          ? get(dataLogin, 'data.profile.mobile', '')
          : get(dataLogin, 'data.profile.email', '')
      );
    }
  }, [typeReceiveOTP, dataLogin]);

  useEffect(() => {
    if (
      !isEmpty(dataLinkPhoneNumber) &&
      dataLinkPhoneNumber.success &&
      get(dataLinkPhoneNumber, 'data.code', HTTP_CODE.FAIL) !== HTTP_CODE.FAIL
    ) {
      if (flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER) {
        setFlow(FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_OTP);
      } else if (flow === FLOW_GLOBAL_AUTH.BIND_PHONE) {
        setFlow(FLOW_GLOBAL_AUTH.BIND_PHONE_OTP);
      }
    }
  }, [dataLinkPhoneNumber, flow]);

  useEffect(() => {
    if (!isEmpty(dataRegister) && dataRegister.success) {
      const resCode = get(dataRegister, 'data.code', '');
      if (resCode === HTTP_CODE.ACCOUNT_LINKED) {
        if (flow === FLOW_GLOBAL_AUTH.PHONE) {
          setFlow(FLOW_GLOBAL_AUTH.PHONE_LOGIN);
        } else if (flow === FLOW_GLOBAL_AUTH.EMAIL) {
          setFlow(FLOW_GLOBAL_AUTH.EMAIL_LOGIN);
        }
      } else if (resCode === HTTP_CODE.ACCOUNT_NOT_LINKED || resCode === HTTP_CODE.OTP_LIMITED) {
        if (flow === FLOW_GLOBAL_AUTH.PHONE) {
          setFlow(FLOW_GLOBAL_AUTH.PHONE_REGISTER_OTP);
        } else if (flow === FLOW_GLOBAL_AUTH.EMAIL) {
          setFlow(FLOW_GLOBAL_AUTH.EMAIL_REGISTER_OTP);
        }
      }
    }
  }, [dataRegister, flow]);

  useEffect(() => {
    if (
      !isEmpty(dataRestoreAccount) &&
      dataRestoreAccount.success &&
      get(dataRestoreAccount, 'data.code', '') !== HTTP_CODE.FAIL
    ) {
      if (!get(dataRestoreAccount, 'data.accessToken', '')) {
        setFlow(FLOW_GLOBAL_AUTH.RESTORE_ACCOUNT_OTP);
      }
    }
  }, [dataRestoreAccount]);

  useEffect(() => {
    if (
      !isEmpty(dataForgotPassword) &&
      dataForgotPassword.success &&
      get(dataForgotPassword, 'data.code', '') !== HTTP_CODE.FAIL
    ) {
      if (flow === FLOW_GLOBAL_AUTH.PHONE_LOGIN) {
        setFlow(FLOW_GLOBAL_AUTH.PHONE_FORGOT_PASSWORD_OTP);
      } else if (flow === FLOW_GLOBAL_AUTH.EMAIL_LOGIN) {
        setFlow(FLOW_GLOBAL_AUTH.EMAIL_FORGOT_PASSWORD_OTP);
      }
    }
  }, [dataForgotPassword, flow]);

  useEffect(() => {
    if (!isEmpty(dataValidateOTP) && dataValidateOTP.success) {
      if (flow === FLOW_GLOBAL_AUTH.PHONE_REGISTER_OTP) {
        setFlow(FLOW_GLOBAL_AUTH.PHONE_REGISTER_PASSWORD);
      } else if (flow === FLOW_GLOBAL_AUTH.EMAIL_REGISTER_OTP) {
        setFlow(FLOW_GLOBAL_AUTH.EMAIL_REGISTER_PASSWORD);
      } else if (
        flow === FLOW_GLOBAL_AUTH.PHONE_FORGOT_PASSWORD_OTP ||
        flow === FLOW_GLOBAL_AUTH.EMAIL_FORGOT_PASSWORD_OTP
      ) {
        setFlow(FLOW_GLOBAL_AUTH.RESET_PASSWORD);
      } else if (flow === FLOW_GLOBAL_AUTH.RESTORE_ACCOUNT_OTP) {
        onConfirmOTP();
      } else if (flow === FLOW_GLOBAL_AUTH.UPDATE_PASSWORD_OTP) {
        setFlow(FLOW_GLOBAL_AUTH.UPDATE_PASSWORD_SET_PASS);
      }
    }
  }, [dataValidateOTP, flow]);

  useEffect(() => {
    if (!isEmpty(dataValidateOTP) && dataValidateOTP.success && profile?.id) {
      if (flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_OTP) {
        if (profile?.allowPasswordChange) {
          // allowPasswordChange = true => tài khoản đã có mật khẩu (email + pass)
          onConfirmOTP();
        } else {
          // allowPasswordChange = false => social account (ko có pass)
          setFlow(FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_SET_PASS);
        }
      } else if (flow === FLOW_GLOBAL_AUTH.BIND_PHONE_OTP) {
        setFlow(FLOW_GLOBAL_AUTH.BIND_PHONE_SET_PASS);
      }
    }
  }, [flow, dataValidateOTP, profile]);

  useEffect(() => {
    const accessToken = get(dataLogin, 'data.accessToken', '');
    if (accessToken) {
      const isLoginBySocial = dataLogin?.isLoginBySocial;
      if (isLoginBySocial) {
        ConfigLocalStorage.set(LocalStorage.USER_JUST_LOGGED, true);
      }
      onTrackingLoginSuccessfully(isLoginBySocial);
    }
  }, [dataLogin]);

  useEffect(() => {
    const accessToken = get(dataConfirmOTP, 'data.accessToken', '');
    if (accessToken && dataConfirmOTP?.isRegister) {
      onTrackingSignUpSuccessfully();
    }
  }, [dataConfirmOTP]);

  useEffect(() => {
    const dataProfile =
      get(dataLogin, 'data.profile', null) || get(dataConfirmOTP, 'data.profile', null);
    const profileStatus = get(dataProfile, 'status', '');
    if (profileStatus === PROFILE_STATUS.WAIT_FOR_DELETE) {
      handleRestoreAccount({
        dataProfile,
        isLoginSocial: dataLogin?.isLoginBySocial
      });
    }
  }, [dataConfirmOTP, dataLogin]);

  useEffect(() => {
    const handleAsyncLogic = async () => {
      const accessToken =
        get(dataLogin, 'data.accessToken', '') ||
        get(dataConfirmOTP, 'data.accessToken', '') ||
        get(dataRestoreAccount, 'data.accessToken', '');
      const dataProfile =
        get(dataLogin, 'data.profile', null) ||
        get(dataConfirmOTP, 'data.profile', null) ||
        get(dataRestoreAccount, 'data.profile', null);
      const profileStatus = get(dataProfile, 'status', '');
      if (accessToken && profileStatus !== PROFILE_STATUS.WAIT_FOR_DELETE) {
        if (
          !isGlobal &&
          (get(dataLogin, 'data.profile.forceBindPhone', false) ||
            (get(dataLogin, 'data.profile.phoneRequired', false) && !!bindAccount?.allowOpenPopup))
        ) {
          saveFlowBefore();
          setFlow(FLOW_GLOBAL_AUTH.BIND_PHONE);
        } else if (destination) {
          const rentedCheck = checkTvodRented(dataLogin, router);
          if (isNotGoToLobby || rentedCheck?.status) {
            handlePushPath({ isTvodSvod: rentedCheck?.status, urlTvodSvod: rentedCheck?.redirect });
          } else {
            const reLoginParams: any = ConfigLocalStorage.get(LocalStorage.RE_LOGIN_PARAMS);
            const { url } = JSON.parse(reLoginParams || '{}');
            const urlResultParams = new URLSearchParams(url?.slice(url.indexOf('?')));
            const query = Object.fromEntries(urlResultParams.entries());

            if (query?.isTriggerAuth) return;
            if (rentedCheck?.redirect) {
              return await router.push(
                `${PAGE.LOBBY_PROFILES}/?destination=${rentedCheck?.redirect}&page=${PAGE.VOD}${
                  flow !== FLOW_GLOBAL_AUTH.PHONE_REGISTER_PASSWORD ? '&from=login' : ''
                }`
              );
            }
          }
        }
      }
    };

    handleAsyncLogic();
  }, [isGlobal, dataConfirmOTP, dataLogin, dataRestoreAccount, destination, pagePath, bindAccount]);

  const handlePushPath = ({ isTvodSvod = false, urlTvodSvod = '' }) => {
    const decodeDestination = decodeParamDestination(destination);
    const reLoginParams: any = ConfigLocalStorage.get(LocalStorage.RE_LOGIN_PARAMS);
    const { pathname, url, contentData } = JSON.parse(reLoginParams || '{}');
    const urlResultParams = new URLSearchParams(url?.slice(url.indexOf('?')));
    const query = Object.fromEntries(urlResultParams.entries());
    const { id, isAddMyList, seo } = contentData || {};

    if (query?.isTriggerAuth) {
      return;
    }
    if (isTvodSvod && urlTvodSvod) {
      destinationLogin({
        dataLogin: dataLogin?.data,
        destination: urlTvodSvod,
        router,
        dispatch
      });
    } else {
      if (
        pagePath === PAGE.VOD ||
        pagePath === PAGE.CATEGORY ||
        pagePath === PAGE.PAGE_MENU ||
        pagePath === PAGE.COLLECTION ||
        pagePath === PAGE.COLLECTION_RIB ||
        pagePath === PAGE.LIVE_TVOD_SLUG ||
        pagePath === PAGE.LIVE_STREAM_SLUG ||
        pagePath === PAGE.TAG ||
        pagePath === PAGE.ARTIST ||
        pagePath === PAGE.PROFILE_SLUG ||
        pagePath === PAGE.PAYMENT_TPBANK_SLUG ||
        pagePath === PAGE.SMART_TV_RESULT_PAYMENT ||
        pagePath === PAGE.LIVE_TV_SLUG ||
        pagePath === PAGE.LIVE_TV_EPG
      ) {
        if (id) {
          if (query?.vid) router.push(url || `${url}&vid=${query?.vid}`);
          else if (isAddMyList) router.push(`${url}?vid=${id}`);
          else router.push(PAGE.VOD, seo?.url);
        } else router.push(pagePath, decodeDestination);
      } else if (id) {
        if (pathname === PAGE.HOME) {
          if (query?.vid) router.push(`${PAGE.HOME}/?vid=${query?.vid}`);
          else if (isAddMyList) router.push(`/?vid=${id}`);
          else router.push(PAGE.VOD, seo?.url);
        } else if (pathname === PAGE.SEARCH) {
          if (query?.vid) router.push(url || `${url}&vid=${query?.vid}`);
          else if (isAddMyList) router.push(`${url}&vid=${id}`);
          else router.push(PAGE.VOD, seo?.url);
        } else if (query?.vid) router.push(url || `${url}?vid=${query?.vid}`);
        else if (isAddMyList) router.push(`${url}?vid=${id}`);
        else router.push(PAGE.VOD, seo?.url);
      } else router.push(decodeDestination);
      ConfigLocalStorage.remove(LocalStorage.RE_LOGIN_PARAMS);
    }
  };

  useEffect(() => {
    if (
      !isEmpty(dataConfirmOTP) &&
      dataConfirmOTP.success &&
      get(dataConfirmOTP, 'data.code', HTTP_CODE.FAIL) !== HTTP_CODE.FAIL
    ) {
      if (flow === FLOW_GLOBAL_AUTH.UPDATE_PASSWORD_SET_PASS) {
        ConfigLocalStorage.set(LocalStorage.UPDATE_PASSWORD_SUCCESS_TOAST, true);
      } else if (
        flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_OTP ||
        flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_SET_PASS
      ) {
        onGetProfile();
        ConfigLocalStorage.set(LocalStorage.UPDATE_PHONE_NUMBER_SUCCESS_TOAST, true);
      }
      if (destination) {
        if (
          flow === FLOW_GLOBAL_AUTH.UPDATE_PASSWORD_SET_PASS ||
          flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_OTP ||
          flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_SET_PASS ||
          flow === FLOW_GLOBAL_AUTH.RESTORE_ACCOUNT_OTP
        ) {
          if (
            trigger &&
            (trigger === TYPE_TRIGGER_AUTH.SOCIAL_UPDATE_PHONE ||
              trigger === TYPE_TRIGGER_AUTH.PROFILE_UPDATE_PHONE)
          ) {
            dispatch(setToast({ message: TEXT.UPDATE_PHONE_SUCCESS }));
          }
          handlePushPath({ isTvodSvod: false, urlTvodSvod: '' });
        } else if (flow === FLOW_GLOBAL_AUTH.BIND_PHONE_SET_PASS) {
          destinationLogin({
            dataLogin: dataLogin?.data,
            destination,
            router,
            dispatch
          });
        }
      }
    }
  }, [dataConfirmOTP, flow, destination, pagePath, isNotGoToLobby]);

  useEffect(() => {
    trackAuthenLoaded();
    return () => {
      localStorage.setItem('currentAuthFlow', '');
      localStorage.setItem('currentAuthFeature', '');
    };
  }, []);
  const renderForm = () => {
    switch (flow) {
      case FLOW_GLOBAL_AUTH.OPTIONS_TO_CHOOSE:
        return (
          <OptionToSelect
            trigger={trigger}
            onSelect={handleChangeFlow}
            notSupportEmail={!isGlobal}
          />
        );
      case FLOW_GLOBAL_AUTH.PHONE:
      case FLOW_GLOBAL_AUTH.EMAIL:
      case FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER:
      case FLOW_GLOBAL_AUTH.BIND_PHONE:
        return (
          <PhoneOrEmailSelected
            flow={flow}
            isHasBtnSetupLaterBindAccount={isHasBtnSetupLaterBindAccount}
            onSkipBindAccount={onSkipBindAccount}
            isBindPhone={flow === FLOW_GLOBAL_AUTH.BIND_PHONE}
            isUpdatePhoneNumber={flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER}
            userName={userName}
            trigger={trigger}
            isOnlySupportVN={isOnlySupportVN}
            geoCountry={get(geoCheck, 'geo_country', 'VN')}
            countryCode={get(countryCode, 'value', 'VN')}
            onChangeUserName={handleChangeUserName}
            onChangeCountryCode={handleChangeCountryCode}
            onSubmit={onVerifyUserName}
            dataVerify={dataVerify}
            isPhoneNumber={isPhoneNumber}
            canBack={canBack}
            inputType={inputType}
            setInputType={setInputType}
            titlePopupTriggerAuth={titlePopupTriggerAuth}
          />
        );
      case FLOW_GLOBAL_AUTH.EMAIL_LOGIN:
      case FLOW_GLOBAL_AUTH.PHONE_LOGIN:
        return (
          <Login
            userName={userName}
            isPhoneNumber={isPhoneNumber}
            countryKey={get(countryCode, 'key', '+84')}
            onChangePassword={handleChangePassword}
            onForgotPassword={onForgotPassword}
            onSubmit={onLogin}
            dataLogin={dataLogin}
            dataForgotPassword={dataForgotPassword}
            onBack={onBack}
            canBack={canBack}
            flow={flow}
          />
        );
      case FLOW_GLOBAL_AUTH.PHONE_FORGOT_PASSWORD_OTP:
      case FLOW_GLOBAL_AUTH.EMAIL_FORGOT_PASSWORD_OTP:
      case FLOW_GLOBAL_AUTH.PHONE_REGISTER_OTP:
      case FLOW_GLOBAL_AUTH.EMAIL_REGISTER_OTP:
      case FLOW_GLOBAL_AUTH.RESTORE_ACCOUNT_OTP:
      case FLOW_GLOBAL_AUTH.UPDATE_PASSWORD_OTP:
      case FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_OTP:
      case FLOW_GLOBAL_AUTH.BIND_PHONE_OTP:
        return (
          <OTPVerify
            isBindPhoneFlow={flow === FLOW_GLOBAL_AUTH.BIND_PHONE_OTP}
            isUpdatePhoneNumberFlow={flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_OTP}
            isUpdatePasswordFlow={flow === FLOW_GLOBAL_AUTH.UPDATE_PASSWORD_OTP}
            isRestoreAccountFlow={flow === FLOW_GLOBAL_AUTH.RESTORE_ACCOUNT_OTP}
            isCanSwitchTypeReceiveOTP={isCanSwitchTypeReceiveOTP}
            typeReceiveOTP={typeReceiveOTP}
            handleChangeTypeReceiveOTP={handleChangeTypeReceiveOTP}
            userName={userName}
            isPhoneNumber={isPhoneNumber}
            flow={
              flow === FLOW_GLOBAL_AUTH.PHONE_REGISTER_OTP ||
              flow === FLOW_GLOBAL_AUTH.EMAIL_REGISTER_OTP
                ? TYPE_AUTH.REGISTER
                : TYPE_AUTH.FORGOT_PASSWORD
            }
            countryKey={
              flow === FLOW_GLOBAL_AUTH.RESTORE_ACCOUNT_OTP
                ? get(dataLogin, 'data.profile.countryCode', '')
                : get(countryCode, 'key', '+84')
            }
            dataValidateOTP={dataValidateOTP}
            dataSendOTP={dataSendOTP}
            dataConfirmOTP={dataConfirmOTP}
            onResendOTP={handleResendOTP}
            onChangeOTP={handleChangeOTPCode}
            onBack={onBack}
            canBack={canBack}
            profile={profile}
            isGlobal={isGlobal}
            inputType={inputType}
          />
        );
      case FLOW_GLOBAL_AUTH.PHONE_REGISTER_PASSWORD:
      case FLOW_GLOBAL_AUTH.EMAIL_REGISTER_PASSWORD:
      case FLOW_GLOBAL_AUTH.RESET_PASSWORD:
      case FLOW_GLOBAL_AUTH.UPDATE_PASSWORD_SET_PASS:
      case FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_SET_PASS:
      case FLOW_GLOBAL_AUTH.BIND_PHONE_SET_PASS:
        return (
          <PasswordUpdate
            userName={userName}
            isPhoneNumber={isPhoneNumber}
            countryKey={get(countryCode, 'key', '+84')}
            isRegisterPassword={
              flow === FLOW_GLOBAL_AUTH.PHONE_REGISTER_PASSWORD ||
              flow === FLOW_GLOBAL_AUTH.EMAIL_REGISTER_PASSWORD
            }
            isBindPhone={flow === FLOW_GLOBAL_AUTH.BIND_PHONE_SET_PASS}
            isUpdatePhoneNumber={flow === FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER_SET_PASS}
            isUpdatePassword={flow === FLOW_GLOBAL_AUTH.UPDATE_PASSWORD_SET_PASS}
            isResetPassword={flow === FLOW_GLOBAL_AUTH.RESET_PASSWORD}
            onChangePassword={handleChangePassword}
            onChangeOldPassword={handleChangeOldPassword}
            onSubmit={onConfirmOTP}
            dataConfirmOTP={dataConfirmOTP}
            onBack={onBack}
            canBack={canBack}
            flow={flow}
          />
        );
      default:
        return (
          <OptionToSelect
            trigger={trigger}
            onSelect={handleChangeFlow}
            notSupportEmail={!isGlobal}
          />
        );
    }
  };

  return (
    <div
      className={classNames(Style.container, customClassName)}
      style={{
        backgroundImage: `url(${bgPage})`
      }}
    >
      {!titlePopupTriggerAuth && <HeaderGlobalAuth />}
      <section className={Style[titlePopupTriggerAuth ? 'blockAuth' : 'block']}>
        {renderForm()}
        {flow !== FLOW_GLOBAL_AUTH.PHONE_REGISTER_PASSWORD &&
          flow !== FLOW_GLOBAL_AUTH.EMAIL_REGISTER_PASSWORD &&
          flow !== FLOW_GLOBAL_AUTH.BIND_PHONE_SET_PASS && (
            <div className={Style.policyText}>
              <a
                href={PAGE.REGULATION}
                target="_blank"
                title={TEXT.CONTRACT_POLICY}
                rel="noreferrer"
              >
                {TEXT.CONTRACT_POLICY}
              </a>
            </div>
          )}
      </section>
    </div>
  );
};

export default Authentication;
