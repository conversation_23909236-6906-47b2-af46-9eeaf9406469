.header {
  @apply w-full flex before:absolute before:w-full before:bg-gradient-to-b before:from-[#111111] before:to-[#11111100] pt-4 before:left-0 before:top-0;
  @apply px-[15px] md:px-[60px];
  @apply before:h-12 md:before:h-[156px];
  @apply before:opacity-75 md:opacity-100;
}

.logo {
  @apply relative;
  & > svg {
    @apply w-14 md:w-auto h-[18px] md:h-auto;
  }
}

.container {
  @apply w-full h-screen overflow-hidden bg-no-repeat bg-[#111] relative bg-cover space-y-3 md:space-y-0;
  &::-webkit-scrollbar {
    @apply w-0;
  }
}

.block {
  @apply w-full md:max-w-[594px] flex flex-col sm:absolute sm:top-1/2 sm:right-[10.52vw] space-y-4;
  @apply translate-y-0 md:translate-y-[-50%];

  &Auth {
    @apply w-full md:max-w-[594px] flex flex-col space-y-4 overflow-y-auto max-h-full pb-6;
  }
}

.policyText {
  @apply text-center max-w-full text-white font-bold text-[14px] underline;
  a {
    @apply text-[#ccc] hover:text-[#ccc];
  }
}
