import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

function NotAvailable() {
  const { limitGlobal } = useSelector((state: any) => state?.App?.webConfig);
  const [isMobileView, setIsMobileView] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobileView(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const backgroundImage = isMobileView
    ? 'url("/assets/images/not-available-mb.jpg")'
    : 'url("/assets/images/not-available.jpg")';

  return (
    <div
      style={{
        backgroundImage,
        backgroundSize: 'cover'
      }}
      className="h-screen pt-[20%] md:pb-[25%] md:px-[64px]"
    >
      <div className="text-white max-w-[1104px] absolute md:relative bottom-[80px] px-[16px] text-center md:text-left space-y-[24px]">
        <p className="font-[700] !text-[22px] leading-[32px] md:!text-[58px] md:leading-[81.2px]">
          Tạm ngừng cung cấp gói và dịch vụ VieON ở quốc gia của bạn
        </p>
        <p className="text-[16px] leading-[20px] md:!text-[36px] font-[400] md:leading-[57.6px]">
          Để biết thêm thông tin vui lòng xem{' '}
          <a className="text-[#0AD418]" href={limitGlobal?.url || '#'}>
            tại đây
          </a>
        </p>
      </div>
    </div>
  );
}

export default NotAvailable;
