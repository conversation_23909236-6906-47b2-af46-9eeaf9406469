import React, { useEffect, useMemo, useRef, useState } from 'react';
import get from 'lodash/get';
import classNames from 'classnames/bind';
import Item from './Item';
import IconDropdown from '@components/Icons/IconDropdown';
import Style from './Dropdown.module.scss';
import useClickOutside from '@hooks/useClickOutside';

const cx = classNames.bind(Style);

const Dropdown = ({ data = [], inputRef, onSelect, hasPrefix, isFocusInput }: any) => {
  const refElm = useRef<any>(null);
  const [itemSelected, setItemSelected] = useState<any>(null);
  const [showContent, setShowContent] = useState(false);
  const isOnlyOneItem = useMemo(() => data.length === 1, [data]);
  const dropdownContent = cx({
    dropdownContent: true,
    hasPrefix: hasPrefix,
    isFocusInput: isFocusInput
  });

  const handleShowContent = () => {
    if (!isOnlyOneItem) {
      setShowContent((state: any) => !state);
    }
  };

  const handleSelectedItem = (item: any) => {
    if (inputRef.current) inputRef.current.focus();
    handleShowContent();
    setItemSelected(item);
  };

  const handleClickOutside = () => {
    setShowContent(false);
  };

  useClickOutside(refElm, handleClickOutside);

  useEffect(() => {
    setItemSelected(data.find((item: any) => item.isDefault));
  }, [data]);

  useEffect(() => {
    if (typeof onSelect === 'function') onSelect(itemSelected);
  }, [itemSelected]);

  return (
    <div className={Style.dropdownContainer} ref={refElm}>
      <span onClick={handleShowContent} className={Style.dropdownSelected}>
        {get(itemSelected, 'key', '')}
      </span>
      {!isOnlyOneItem && (
        <span onClick={handleShowContent} className={Style.iconDropdown}>
          <IconDropdown />
        </span>
      )}
      {!isOnlyOneItem && showContent && (
        <div className={dropdownContent}>
          <div className={Style.dropdownList}>
            {data.map((item: any) => (
              <Item
                key={item.key}
                data={item}
                selected={itemSelected}
                onSelected={handleSelectedItem}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Dropdown;
