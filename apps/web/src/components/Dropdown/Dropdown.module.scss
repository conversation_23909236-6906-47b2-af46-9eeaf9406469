.dropdownContainer {
  @apply flex items-center space-x-1 md:space-x-2;
}

.dropdownSelected {
  @apply text-sm text-white leading-[140%] cursor-pointer;
}

.iconDropdown {
  @apply cursor-pointer;
}

.dropdownContent {
  @apply absolute pt-[2px] w-64 z-[1] left-0 top-full;
  @apply shadow-[0px_4px_10px_0px_rgba(0,0,0,0.20),4px_0px_10px_0px_rgba(0,0,0,0.20),-4px_0px_10px_0px_rgba(0,0,0,0.20)];
  &.hasPrefix {
    &.isFocusInput {
      @apply sm:-translate-x-10 -translate-x-8;
    }
  }
}

.dropdownList {
  @apply bg-[#222222];
}

.itemDropdown {
  @apply cursor-pointer flex border-solid border border-[#333333] bg-[#222222] px-3 py-2 w-full text-white items-center text-start;
  &:hover {
    @apply bg-[#333333];
  }
}

.iconChecked {
  @apply min-w-[16px] mr-2;
}

.country {
  @apply grow;
}
