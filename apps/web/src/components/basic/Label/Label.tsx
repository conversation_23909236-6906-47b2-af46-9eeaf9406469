import React from 'react';
import Icon from '../Icon/Icon';

const Label = ({ className, spClass, iconClass, textClass, subClass, txtSubClass, label }: any) => {
  let labelClass = className || 'notify notify--warning-text flex-box align-middle';
  const spanClass = spClass || 'icon--small icon--tiny-xs';
  let txtClass =
    textClass ||
    'text text-large-up-18 text-small-up-14 padding-small-up-left-4 padding-large-up-left-8 text-small-up-medium text-large-up-bold';
  const iClass = iconClass || 'vie-clock-o-rc-medium';

  if (subClass) labelClass += ` ${subClass}`;
  if (txtSubClass) txtClass += ` ${txtSubClass}`;

  return (
    <div className={labelClass}>
      {iconClass && <Icon spClass={spanClass} iClass={iClass} />}
      {label && <span className={txtClass} dangerouslySetInnerHTML={{ __html: label }} />}
    </div>
  );
};

export default React.memo(Label);
