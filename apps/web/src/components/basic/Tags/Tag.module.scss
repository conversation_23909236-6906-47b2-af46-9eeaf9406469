.discount-tag {
  @apply md:w-10 w-9 h-[18px] md:h-[18px] flex justify-center items-center relative pt-[2px] text-center;
  @apply text-[.5rem] md:text-[.75rem] font-bold text-white leading-[1.1];
  & > svg {
    @apply w-full h-full absolute top-0 left-0 right-0 bottom-0;
  }
}

.age {
  @apply flex justify-start pl-2 items-center text-[16px];
  @apply bg-black bg-opacity-30 text-white font-bold h-[30px] w-[45px] leading-[1.2];
  border-left: 3px solid #3ac882;

  &ForIntro {
    margin-right: calc((3.02083vw) * -1);
    @apply w-[calc(3.02083vw+100%)];
  }
}

.Base {
  @apply flex shrink w-auto max-w-fit items-center justify-center text-white;
}

.Truncate {
  @apply max-w-full;
  & > span {
    @apply truncate line-clamp-1 block max-w-full;
  }
}

.Sm {
  @apply h-[15px] text-[.5rem] leading-[unset] px-1 py-[3px] space-x-[.125rem];
}

.Md {
  @apply h-[17px] text-[.75rem] leading-[unset] px-[.375rem] py-1 space-x-1;
  @apply before:w-[.375rem] before:h-[.375rem] before:hidden;
}

.Lg {
  @apply h-[22px] text-[1rem] leading-[unset] px-[.375rem] py-1 space-x-1.5;
  @apply before:w-[.5625rem] before:h-[.5625rem] before:hidden;

  &Subtle {
    @apply h-[22px] text-[1rem] leading-[unset] space-x-1.5;
  }
}

.VariantMd {
  @apply h-5 text-[.75rem] leading-[unset] px-[.375rem] py-1 space-x-1;
}

.VariantLg {
  @apply h-7 text-[1rem] leading-[unset] px-2 py-[.375rem] space-x-1;
}

.Black {
  &Gl50 {
    @apply bg-[#333]/50 text-white;
  }
}

.Blue {
  text-shadow: 0 8.972px 15.95px rgba(0, 0, 0, 0.08), 0 3.275px 5.822px rgba(0, 0, 0, 0.11),
    0 1.59px 2.827px rgba(0, 0, 0, 0.14), 0 0.779px 1.386px rgba(0, 0, 0, 0.17),
    0 0.308px 0.548px rgba(0, 0, 0, 0.25);
  @apply bg-[#3EA6FE] text-white;
}

.Gold {
  text-shadow: 0 8.972px 15.95px rgba(0, 0, 0, 0.08), 0 3.275px 5.822px rgba(0, 0, 0, 0.11),
    0 1.59px 2.827px rgba(0, 0, 0, 0.14), 0 0.779px 1.386px rgba(0, 0, 0, 0.17),
    0 0.308px 0.548px rgba(0, 0, 0, 0.25);
  @apply bg-[#DA9E1C] text-white;

  &Gra {
    text-shadow: 0 8.972px 15.95px rgba(0, 0, 0, 0.08), 0 3.275px 5.822px rgba(0, 0, 0, 0.11),
      0 1.59px 2.827px rgba(0, 0, 0, 0.14), 0 0.779px 1.386px rgba(0, 0, 0, 0.17),
      0 0.308px 0.548px rgba(0, 0, 0, 0.25);
    @apply bg-gradient-to-r from-[#da9e1c] to-[#ecbd57] text-white;
  }
}

.Gray {
  @apply bg-[#333333]/50 text-white;
}

.Green {
  text-shadow: 0 8.972px 15.95px rgba(0, 0, 0, 0.08), 0 3.275px 5.822px rgba(0, 0, 0, 0.11),
    0 1.59px 2.827px rgba(0, 0, 0, 0.14), 0 0.779px 1.386px rgba(0, 0, 0, 0.17),
    0 0.308px 0.548px rgba(0, 0, 0, 0.25);
  @apply bg-[#5BCD27] text-white;

  &Subtle {
    @apply p-0 text-vo-green;
  }
}

.Red {
  text-shadow: 0 8.972px 15.95px rgba(0, 0, 0, 0.08), 0 3.275px 5.822px rgba(0, 0, 0, 0.11),
    0 1.59px 2.827px rgba(0, 0, 0, 0.14), 0 0.779px 1.386px rgba(0, 0, 0, 0.17),
    0 0.308px 0.548px rgba(0, 0, 0, 0.25);
  @apply bg-[#EF1010] text-white;

  &Gra {
    text-shadow: 0 8.972px 15.95px rgba(0, 0, 0, 0.08), 0 3.275px 5.822px rgba(0, 0, 0, 0.11),
      0 1.59px 2.827px rgba(0, 0, 0, 0.14), 0 0.779px 1.386px rgba(0, 0, 0, 0.17),
      0 0.308px 0.548px rgba(0, 0, 0, 0.25);
    @apply bg-gradient-to-r from-[#EF1010] to-[#EF1010] text-white;
  }

  &Live {
    text-shadow: 0 8.972px 15.95px rgba(0, 0, 0, 0.08), 0 3.275px 5.822px rgba(0, 0, 0, 0.11),
      0 1.59px 2.827px rgba(0, 0, 0, 0.14), 0 0.779px 1.386px rgba(0, 0, 0, 0.17),
      0 0.308px 0.548px rgba(0, 0, 0, 0.25);
    @apply bg-[#EF1010] text-white space-x-1;
    @apply before:bg-white before:rounded-full before:mr-1 before:block;
  }
}

.White {
  @apply bg-white text-[#333] subpixel-antialiased;
}

.Yellow {
  @apply text-[#f1c21B];

  &Subtle {
    @apply p-0 text-[#f1c21B];
  }

  &Solid {
    @apply bg-[#f1c21B] text-white;
  }

  &Stroke {
    @apply bg-transparent text-[#f1c21B];
  }
}

/* // Detail tag */
.Dt {
  &Md {
    @apply h-5 text-[.75rem] leading-[unset] px-[.375rem] py-1 drop-shadow-md space-x-1;
  }

  &Lg {
    @apply h-7 text-[1rem] leading-[unset] px-2 py-[.375rem] drop-shadow-md space-x-1;
  }
}

.divide {
  @apply relative pl-0 pr-[.625rem] lg:pr-3 after:w-[1px] after:h-2/3 after:absolute after:right-0 after:top-1/2 after:-translate-y-1/2 after:bg-white/30;
  @apply last:after:w-0 last:pr-0;
}

.roundedAll {
  @apply rounded-[6px];

  &Sm {
    @apply rounded-[4px];
  }
}
.roundedTr {
  @apply rounded-tr-[6px];

  &Sm {
    @apply rounded-tr-[4px];
  }
}
.roundedTl {
  @apply rounded-tl-[6px];

  &Sm {
    @apply rounded-tl-[4px];
  }
}
.roundedBr {
  @apply rounded-br-[6px];

  &Sm {
    @apply rounded-br-[4px];
  }
}
.roundedBl {
  @apply rounded-bl-[6px];

  &Sm {
    @apply rounded-bl-[4px];
  }
}
.roundedT {
  @apply rounded-t-[6px];

  &Sm {
    @apply rounded-t-[4px];
  }
}

.tagsGroup {
  @apply w-fit overflow-hidden absolute bottom-0 left-1/2 -translate-x-1/2 rounded-tl-[.25rem] rounded-tr-[.25rem];
}

.mostBuyers {
  @apply flex w-auto items-center justify-center max-w-full h-8 px-2 md:px-3;
  @apply text-[.5625rem] md:text-[.875rem] font-bold leading-none rounded-[12px_6px_6px_12px];
  @apply text-white bg-[linear-gradient(-90deg,#CE8C00_0%,#FFB910_100%)];
}

.mostBuyersV2 {
  @apply flex items-start justify-center h-full w-full p-1 md:p-2;
  @apply text-white bg-[linear-gradient(90deg,#DA9E1C_0%,#ECBD57_100%)];
  @apply rounded-xl absolute top-0 bottom-0 left-1/2 -translate-x-1/2 font-bold text-xs md:text-sm lg:text-lg text-center;
}

.blueTvod {
  text-shadow: 0 8.972px 15.95px rgba(0, 0, 0, 0.08), 0 3.275px 5.822px rgba(0, 0, 0, 0.11),
    0 1.59px 2.827px rgba(0, 0, 0, 0.14), 0 0.779px 1.386px rgba(0, 0, 0, 0.17),
    0 0.308px 0.548px rgba(0, 0, 0, 0.25);
  @apply bg-[#2A51FF] text-white;
}
