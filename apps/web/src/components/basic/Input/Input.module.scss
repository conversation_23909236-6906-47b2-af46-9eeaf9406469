.outline {
  @apply flex flex-col;

  &Group {
    @apply flex flex-row space-x-[10px] items-center;
    @apply bg-white border border-solid border-[#111] rounded-xl;
  }

  &Field {
    @apply grow h-11 flex flex-row px-2;
  }

  &ITag {
    @apply h-auto m-0 text-[.75rem] text-[#111];
    @apply border-none shadow-none focus:border-none focus:shadow-none #{!important};
    @apply placeholder:text-[.75rem] placeholder:text-[#9B9B9B];
    @apply disabled:bg-transparent disabled:placeholder:text-[#ccc];

    &Error {
      @apply text-[#ED0000];
    }
  }

  &Btn {
    @apply flex-none text-[.75rem] text-[#3AC882] disabled:text-[#ccc] font-bold;

    &Ready {
      @apply text-[#3AC882];
    }

    &Text {
      @apply text-[.75rem] font-bold;
    }
  }
  &.success {
    .outlineBtn {
      @apply text-[#3AC882] #{!important};
    }
    .outlineGroup {
      @apply border-[#3AC882] #{!important};
    }
    label {
      @apply text-[#3AC882] #{!important};
    }
    input {
      @apply text-[#3AC882] #{!important};
    }
  }
  &.failed {
    .outlineBtn {
      @apply text-[#F44336] #{!important};
    }
    .outlineGroup {
      @apply border-[#F44336] #{!important};
    }
    label {
      @apply text-[#F44336] #{!important};
    }
  }

  &SlideUp {
    @apply h-auto m-0 text-[.75rem] text-[#111];
    //@apply border-none shadow-none focus:border-none focus:shadow-none #{!important};
    @apply disabled:bg-transparent disabled:placeholder:text-[#ccc];

    &Group {
      @apply flex flex-row space-x-[10px] items-center;
      //@apply bg-white border border-solid border-[#111] rounded-xl;
    }

    &Field {
      @apply grow h-9;

      & > input {
        @apply font-normal text-[1rem];
        @apply border-none shadow-none focus:border-none focus:shadow-none #{!important};
        @apply placeholder:text-[.75rem] placeholder:text-[#9B9B9B];
      }

      &Error {
        @apply border-b-[#E74C3C] #{!important};
      }
    }

    &Error {
      -webkit-text-fill-color: #ed0000 !important;
      @apply text-[#ED0000] autofill:text-[#ED0000] #{!important};
    }

    &Btn {
      @apply flex h-9 px-4 border-[1px] border-solid border-[#9b9b9b] rounded-[.25rem] #{!important};

      & > span {
        @apply text-[1rem] text-[#9b9b9b] font-medium #{!important};
      }
      &Disabled {
        @apply border-[#9b9b9b] #{!important};

        & > span {
          @apply text-[#9b9b9b] #{!important};
        }
      }
      &Error {
        @apply border-[#E74C3C] #{!important};

        & > span {
          @apply text-[#E74C3C] #{!important};
        }
      }
      &Ready {
        @apply border-[#3AC882] #{!important};

        & > span {
          @apply text-[#3AC882] #{!important};
        }
      }
    }

    &.success {
      .outlineBtn {
        @apply text-[#3AC882] #{!important};
      }
      .outlineGroup {
        @apply border-[#3AC882] #{!important};
      }
      label {
        @apply text-[#3AC882] #{!important};
      }

      input {
        -webkit-text-fill-color: #3ac882 !important;
        @apply text-[#3AC882] autofill:text-[#3AC882] #{!important};
      }
    }
    &.failed {
      .outlineBtn {
        @apply text-[#F44336] #{!important};
      }
      .outlineGroup {
        @apply border-[#F44336] #{!important};
      }
      label {
        @apply text-[#F44336] #{!important};
      }
      input {
        -webkit-text-fill-color: #f44336 !important;
        @apply text-[#F44336] autofill:text-[#F44336] #{!important};
      }
    }
  }
}
