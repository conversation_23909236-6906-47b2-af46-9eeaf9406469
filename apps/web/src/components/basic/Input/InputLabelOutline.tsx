import React, { memo, useMemo, useRef } from 'react';
import Icon from '@components/basic/Icon/Icon';
import { isMobile } from 'react-device-detect';
import classNames from 'classnames';
import styles from './Input.module.scss';

const KEY_CODE_ENTER = 13;

const InputGroupOutline = ({
  id,
  placeholder,
  disabled,

  // iconLeft và iconRight là class của icon
  iconLeft,

  iconRight,
  value,
  error,
  hasBtn,
  disabledBtn,
  titleBtn,
  onChange,
  onEnter,
  onSubmit,
  autoComplete,
  onFocus,
  iconTick,
  width,

  // forwardRef,
  keyLimit,

  isSuccess,
  isFailed
}: any) => {
  const ref = useRef<any>(null);
  const widthLeft = useMemo(() => {
    const widthInput = ref?.current?.offsetWidth;
    if (width < widthInput) return `${width + 8}px`;
    return '97%';
  }, [width, ref]);
  const onKeyPress = (e: any) => {
    const keyLength = e?.target?.value?.length;
    if (e.charCode === KEY_CODE_ENTER) {
      if (typeof onEnter === 'function') {
        onEnter(e);
      }
      e.preventDefault();
    }
    if (keyLength >= keyLimit) {
      e.preventDefault();
    }
  };
  const onFocusInput = () => {
    if (typeof onFocus === 'function') onFocus();
  };

  return (
    <div
      className={classNames(
        'field',
        styles.outline,
        isSuccess && styles.success,
        isFailed && styles.failed
      )}
    >
      <div className={classNames('grid-x', styles.outlineGroup)}>
        <div className={classNames(styles.outlineField)}>
          {iconLeft && (
            <span className="icon absolute-middle-left">
              <i className={`vie ${iconLeft}`} />
            </span>
          )}
          {iconRight && (
            <span className="icon absolute-middle-right">
              <i className={`vie ${iconRight}`} />
            </span>
          )}
          <input
            ref={ref}
            id={id}
            className={classNames('input', styles.outlineITag, error && styles.outlineITagError)}
            type="text"
            maxLength={keyLimit}
            value={value}
            disabled={!!disabled}
            onChange={onChange}
            onKeyPress={onKeyPress}
            placeholder={placeholder}
            autoComplete={autoComplete || 'on'}
            required
            onFocus={onFocusInput}
          />
          {iconTick && (
            <Icon
              spClass={classNames(`icon--tiny absolute right bottom m-b1`, isMobile && 'p-b1')}
              iClass="vie-tick-solid-c text-green"
              spStyle={{ left: widthLeft }}
            />
          )}
        </div>
        {hasBtn && (
          <button
            className={classNames(
              'button success',
              styles.outlineBtn,
              disabledBtn && styles.outlineBtnReady
            )}
            title={titleBtn}
            disabled={disabledBtn}
            onClick={onSubmit}
          >
            <span className={classNames(styles.outlineBtnText)}>{titleBtn}</span>
          </button>
        )}
      </div>
      {error && <label className={`form-error m-y${error ? ` is-visible` : ''}`}>{error}</label>}
    </div>
  );
};

export default memo(InputGroupOutline);
