import React, { useEffect, useState } from 'react';

const InputGroupNew = ({
  label,
  id,
  type,
  error,
  value,
  onChange,
  placeholder,
  onEnter,
  onFocus,
  onBlur,
  autoComplete,
  disabled,
  style
}: any) => {
  const [isFocus, setFocus] = useState(false);
  const onClickInput = () => {
    if (value !== '' && isFocus) {
      setFocus(true);
      return;
    }
    setFocus(true);
  };

  const onBlurInput = (e: any) => {
    if (value !== '' && isFocus) {
      if (typeof onBlur === 'function') onBlur(e);
      setFocus(true);
      return;
    }
    setFocus(false);
  };
  const onFocusInput = () => {
    if (value !== '' && isFocus) {
      if (typeof onFocus === 'function') onFocus();
      setFocus(true);
      return;
    }
    setFocus(false);
  };

  const [isToggleDisplayPW] = useState({
    display: false,
    type
  });

  const onKeyPress = (e: any) => {
    if (e.charCode === 13) {
      if (typeof onEnter === 'function') onEnter(e);
      e.preventDefault();
    }
  };

  useEffect(() => {
    if (value) {
      setFocus(true);
    } else {
      setFocus(false);
    }
  }, [value]);

  return (
    <div
      className="field field-md-1 field-line-text field-label-slide-up field-for-light relative"
      style={style}
    >
      <div className="field__input">
        <input
          id={id}
          className="input"
          type={isToggleDisplayPW.type}
          value={value}
          placeholder={placeholder}
          disabled={!!disabled}
          onChange={onChange}
          onKeyPress={onKeyPress}
          onClick={onClickInput}
          onBlur={onBlurInput}
          onFocus={onFocusInput}
          autoComplete={autoComplete || 'on'}
          required
        />
        <label htmlFor={id} className="field__label">
          {label}
        </label>
      </div>
      {error && <label className="form-error is-visible m-t1">{error}</label>}
    </div>
  );
};

export default React.memo(InputGroupNew);
