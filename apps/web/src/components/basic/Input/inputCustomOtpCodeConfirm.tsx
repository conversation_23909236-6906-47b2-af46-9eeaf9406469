import React from 'react';
import InputCustomOtpCode from '@components/basic/Input/InputCustomOtpCode';
import { TEXT } from '@constants/text';
import classNames from 'classnames';

const inputCustomOtpCodeConfirm = ({
  onChangeOtpPinCode,
  onChangeOtpPinCodeConfirm,
  otpPinCodeConfirm,
  otpPinCode,
  error,
  isMobile
}: any) => (
  <>
    <article>
      <h3 className="text text-gray239 text-center text-16 p-b2">{TEXT.TYPE_PIN_CODE}</h3>
      <InputCustomOtpCode
        id="otpPinCode"
        onChangeOtp={onChangeOtpPinCode}
        onFocusJump={!otpPinCodeConfirm && otpPinCode?.length === 4}
        autoComplete={false}
        isPinCode
      />
    </article>
    <article>
      <h3 className="text text-gray239 text-center text-16 p-b2">{TEXT.RE_TYPE_PIN_CODE}</h3>
      <InputCustomOtpCode
        id="otpPinCodeConfirm"
        onChangeOtp={onChangeOtpPinCodeConfirm}
        onFocusBack={!otpPinCodeConfirm}
        autoComplete={false}
        isPinCode
      />
    </article>
    {error && (
      <label
        className={classNames(
          'form-error is-visible absolute text-center',
          isMobile ? 'bottom m-b' : 'bottom-1'
        )}
      >
        {error}
      </label>
    )}
  </>
);

export default inputCustomOtpCodeConfirm;
