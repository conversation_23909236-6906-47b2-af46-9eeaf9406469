import React, { useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { EL_ID, PAGE } from '@constants/constants';
import { useVieRouter } from '@customHook';
import classNames from 'classnames';
import Styles from '@components/basic/Modal/Styles.module.scss';

const Modal = ({
  renderBody,
  footerData,
  className,
  onClosed,
  renderCustom,
  modalStyle,
  bodyClass,
  footerClass,
  classStyle,
  renderFooter,
  title,
  subTitle,
  renderHeader,
  notClosedButton,
  colorClass,
  isWelcome,
  classCustom,
  light,
  blue,
  small,
  orange,
  isNotCancelESC,
  modalMobile,
  classModal,
  modalNewUi,
  modalUi,
  modalUiContainer,
  modalUiWrapper,
  modalUiCloseButtonClass,
  size,
  greenLine,
  wrapperClassname,
  buttonIconClassname,
  iconShadow = true,
  modalWrapperStyle,
  alignment,
  onCloseTracking
}: any) => {
  const router = useVieRouter();
  const AppStore = useSelector((state: any) => state.App);
  const previewCard = useSelector((state: any) => state?.Popup?.previewCard);
  const isMobile = useMemo(() => AppStore.isMobile, [AppStore]);
  const leftButton = useMemo(() => (footerData ? footerData.leftButton : null), [footerData]);
  const rightButton = useMemo(() => (footerData ? footerData.rightButton : null), [footerData]);
  const classButtonRight = useMemo(() => {
    if (rightButton?.disabled) {
      return `${rightButton?.className} disabled`;
    }
    return rightButton?.className;
  }, [rightButton]);
  const modalClass = useMemo(() => {
    let elClass = 'modal modal--dark modal--large animate-fade-in';
    if (blue) {
      elClass = 'modal modal--blue modal--large animate-fade-in';
    }
    if (orange) {
      elClass = 'modal modal--orange modal--large animate-fade-in';
    }
    if (light) {
      elClass = 'modal';
    }
    if (small) {
      elClass = 'modal modal--dark animate-fade-in';
    }
    if (modalMobile) {
      elClass = 'modal modal--dark modal--tiny animate-fade-in !max-w-full';
    }
    elClass = `${elClass}${alignment ? ` ${alignment}` : ' middle'} ${className || ''} ${
      colorClass || ''
    }`;
    return elClass;
  }, [blue, light, colorClass, className]);
  const detailID: any = useMemo(() => {
    if (typeof window !== 'undefined' && router.pathname === PAGE.VOD && isMobile) {
      return 'DETAIL_PAGE';
    }
    return null;
  }, [isMobile, router.pathname]);

  useEffect(() => {
    setModalOpen({ isModalOpen: true });
    document.addEventListener('keydown', onKeyPress);
    window.addEventListener('popstate', detectHistory);
    return () => {
      setModalOpen({ isModalOpen: false });
      document.removeEventListener('keydown', onKeyPress);
    };
  }, [previewCard]);
  const detectHistory = () => {
    if (typeof onClosed === 'function') onClosed();
  };

  const onCloseTrackingClick = () => {
    if (typeof onClosed === 'function') onClosed();
    if (typeof onCloseTracking === 'function') onCloseTracking();
  };

  const setModalOpen = ({ isModalOpen }: any) => {
    if (previewCard?.expand) return;
    const htmlEl: any = document.getElementsByTagName('html')[0];
    if (typeof document === 'undefined' || !htmlEl) {
      return;
    }
    if (isModalOpen) {
      htmlEl.classList.add('overflow');
      if (router?.pathname !== PAGE.LOBBY_PROFILES) htmlEl.style.paddingRight = '0.625rem';
    } else {
      htmlEl.classList.remove('overflow');
      htmlEl.style.paddingRight = 0;
    }
  };
  const onKeyPress = (e: any) => {
    if (!isNotCancelESC) {
      switch (e.which) {
        case 27:
          if (typeof onClosed === 'function') onClosed();
          break;
        case 36:
        case 35:
        case 40:
        case 38:
        case 33:
        case 34:
          break;
        default:
          break;
      }
    }
  };

  const sizeName = size?.charAt(0).toUpperCase() + size?.slice(1);
  const sizeUi = Styles[`Modal${sizeName}`];
  const newModalUi = !modalNewUi
    ? `modal-overlay fixed layer-max scrollable-y over-scroll-contain${
        classModal ? ` ${classModal}` : ''
      }`
    : classNames(Styles.Modal, modalUi);
  const newModalUiContainer = !modalNewUi
    ? modalClass
    : classNames(Styles.ModalContainer, modalUiContainer, size && sizeUi);
  const newModalUiWrapper = !modalNewUi
    ? classNames('modal-wrapper', wrapperClassname)
    : classNames(Styles.ModalWrapper, modalUiWrapper, greenLine && Styles.ModalGreenLine);

  const bodyModal = typeof renderBody === 'function' ? renderBody() : null;

  const newModalUiCloseButton = !modalNewUi
    ? 'button close absolute top-right-4 size-square-24'
    : modalUiCloseButtonClass;

  return (
    <div className={newModalUi} id={EL_ID.MODAL}>
      <div
        className={newModalUiContainer}
        id={detailID}
        style={modalStyle}
        data-modal
        data-animation-in="fade"
        data-animation-out="fade"
        onKeyPress={(e) => onKeyPress(e)}
      >
        {renderCustom && renderCustom()}
        {!notClosedButton && (
          <button
            className={newModalUiCloseButton}
            data-close
            onClick={typeof onCloseTrackingClick === 'function' ? onCloseTrackingClick : onClosed}
            style={{ color: isWelcome ? 'white' : '#CCCCCC' }}
            aria-label="Close"
          >
            <span
              className={classNames(
                'icon icon--variant text-white icon--small',
                buttonIconClassname
              )}
            >
              <i
                className={classNames(
                  'vie vie-times-medium',
                  iconShadow && 'text-shadow-blackColor-a50-111'
                )}
              />
            </span>
          </button>
        )}
        <div className={newModalUiWrapper} style={modalWrapperStyle}>
          {renderHeader && renderHeader()}
          {title && (
            <div className="modal-header">
              <h2 className={`title text-center ${classStyle || ''}`}>{title}</h2>
              {subTitle && (
                <p className={`text text-muted text-center ${classCustom || ''}`}>{subTitle}</p>
              )}
            </div>
          )}
          {bodyModal && <div className={bodyClass || 'modal-body'}>{bodyModal}</div>}
          {footerData && (
            <div className="modal-footer">
              <div className={footerData.className || 'button-group child-auto'}>
                {leftButton && (
                  <button
                    className={leftButton.className || 'button rounded hollow basic width-120 m-b'}
                    type={leftButton.buttonType || 'button'}
                    onClick={leftButton.onClick}
                  >
                    <span className="text">{leftButton.name}</span>
                  </button>
                )}
                {rightButton && (
                  <button
                    className={classButtonRight || 'button rounded width-120 success m-b'}
                    type={rightButton.buttonType || 'button'}
                    disabled={rightButton?.disabled}
                    onClick={rightButton.onClick}
                  >
                    <span className="text">{rightButton.name}</span>
                  </button>
                )}
              </div>
            </div>
          )}
          {typeof renderFooter === 'function' && renderFooter() !== null && (
            <div className={footerClass || 'modal-footer'}>{renderFooter()}</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Modal;
