import React, { useEffect, useRef, useState, useMemo } from 'react';
import Hls from 'hls.js';
import isEmpty from 'lodash/isEmpty';
import { useSelector } from 'react-redux';
import { isIOS } from 'react-device-detect';
import { createTimeout, removeGlobalPlayer, setGlobalPlayer, setVideoPlay } from '@helpers/common';
import LocalStorage from '@config/LocalStorage';
import { useVieRouter } from '@customHook';
import ConfigLocalStorage from '@config/ConfigLocalStorage';

const MiniPlayer = React.memo((props: any) => {
  const {
    style,
    muted,
    id,
    className,
    contentId,
    linkPlay,
    notPauseScrolling,
    isMasterBanner,
    notCheckScroll,
    notAutoPlay,
    noPauseOther
  } = props || {};
  const router = useVieRouter();
  const { query } = router || {};
  const { vid } = query || {};
  const videoRef = useRef<any>(null);
  const hlsPlayerRef = useRef<any>(null);
  const playEndedRef = useRef(false);
  const playerErrorRef = useRef(false);
  const initPlayerTimerRef = useRef<any>(null);
  const { popupName, previewCard, cardHover } = useSelector((state: any) => state?.Popup || {});
  const { expand, data } = previewCard || {};
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const expanded = useMemo(
    () => expand || !isEmpty(data) || !isEmpty(cardHover) || !!popupName,
    [expand, data, popupName]
  );
  const isMP4 = useMemo(() => (linkPlay || '').includes('.mp4'), [linkPlay]);
  const [isShowPlayer, setIsShowPlayer] = useState(false);

  const isPlayerMuted = useMemo(() => {
    const muted = JSON.parse(
      (ConfigLocalStorage.get(LocalStorage.TRAILER_MUTED) as string) || 'true'
    );
    if (muted === false) {
      return false;
    }
    return true;
  }, []);

  // DidMount / Unmount
  useEffect(() => {
    // Set Player Global
    setGlobalPlayer({ id, player: videoRef.current });
    window.addEventListener('scroll', checkScroll, false);
    if (videoRef.current) {
      videoRef.current.addEventListener('ended', onEnded);
      videoRef.current.addEventListener('play', onPlay);
      videoRef.current.addEventListener('playing', onPlaying);
      videoRef.current.addEventListener('error', onError);
    }
    return () => {
      clearTimeout(initPlayerTimerRef.current);
      removeGlobalPlayer({ id });
      window.removeEventListener('scroll', checkScroll, false);
      if (videoRef.current) {
        videoRef.current.pause();
        videoRef.current.removeEventListener('ended', onEnded);
        videoRef.current.removeEventListener('play', onPlay);
        videoRef.current.removeEventListener('playing', onPlaying);
        videoRef.current.removeEventListener('error', onError);
      }
      if (hlsPlayerRef.current) {
        hlsPlayerRef.current.detachMedia(videoRef.current);
        hlsPlayerRef.current.stopLoad();
        hlsPlayerRef.current.destroy();
      }
    };
  }, []);

  useEffect(() => {
    playEndedRef.current = false;
    setupPlayer();
  }, [linkPlay, videoRef.current]);

  const checkScroll = (e?: any) => {
    if (notCheckScroll || (isMasterBanner && vid)) return;
    let scrollingEl = e?.target?.scrollingElement;
    scrollingEl = scrollingEl || document.getElementById('html-head');
    if (!scrollingEl) return;
    const screenWidth = scrollingEl.clientWidth;
    const { scrollTop } = scrollingEl;
    if (scrollTop >= 0 && screenWidth > 0) {
      if (scrollTop > (screenWidth * 8) / 16) {
        if (!notPauseScrolling && videoRef.current && videoRef.current?.played) {
          videoRef.current.pause();
        }
      } else if (!playEndedRef.current) {
        if (notPauseScrolling) return; // prevent scroll replay video detail vod

        if (videoRef.current) {
          videoRef.current.muted = isPlayerMuted;
          setVideoPlay({ playerId: id, noPauseOther });
        }
      }
    }
  };

  const setupPlayer = () => {
    setIsShowPlayer(false);
    playerErrorRef.current = false;
    playEndedRef.current = false;
    if (!videoRef.current || !linkPlay) return;
    if (typeof props.setupPlayer === 'function') props.setupPlayer({ player: videoRef.current });
    const isMP4 = (linkPlay || '').includes('.mp4');
    if (isMP4) {
      videoRef.current.load();
      if (muted) {
        videoRef.current.muted = true;
      }
      setInitPlay();
    } else if (Hls.isSupported()) {
      hlsPlayerRef.current = new Hls();
      hlsPlayerRef.current.attachMedia(videoRef.current);
      hlsPlayerRef.current.on(Hls.Events.MEDIA_ATTACHED, () => loadSource(linkPlay));
      hlsPlayerRef.current.on(Hls.Events.ERROR, onError);
    }
  };

  const loadSource = (link: any) => {
    if (!link || !hlsPlayerRef.current) return;
    hlsPlayerRef.current.loadSource(link || '');
    // Handle play initial
    clearTimeout(initPlayerTimerRef.current);
    initPlayerTimerRef.current = createTimeout(() => {
      setInitPlay();
    }, 2000);
    if (typeof props.setupPlayer === 'function') props.setupPlayer({ player: videoRef.current });
  };

  const setInitPlay = () => {
    if (!videoRef.current || notAutoPlay) return;
    continueProgress();
    if (expanded && isMasterBanner) {
      videoRef.current.pause();
    } else if (!notCheckScroll) checkScroll();
    else setVideoPlay({ playerId: id, noPauseOther });
  };

  const onEnded = () => {
    setIsShowPlayer(false);
    playerErrorRef.current = false;
    playEndedRef.current = true;
    ConfigLocalStorage.remove(LocalStorage.BANNER_TRAILER_PROGRESS);
    if (typeof props.onEnded === 'function') props.onEnded();
  };

  const onPlaying = () => {
    if (typeof props.onPlaying === 'function') props.onPlaying();
  };

  const onError = () => {
    setIsShowPlayer(false);
    playerErrorRef.current = true;
    playEndedRef.current = false;
    if (typeof props.onError === 'function') props.onError();
  };

  const onPlay = () => {
    if (playerErrorRef.current || !videoRef.current) return;
    continueProgress();
    let muteTrailer = isPlayerMuted;
    if (noPauseOther) muteTrailer = true;
    videoRef.current.muted = muteTrailer;
    setIsShowPlayer(true);
    playEndedRef.current = false;
    if (typeof props.onPlay === 'function') {
      props.onPlay({ isMuted: muteTrailer, player: videoRef.current });
    }
  };

  const continueProgress = () => {
    let progressBanner: any = ConfigLocalStorage.get(LocalStorage.BANNER_TRAILER_PROGRESS);
    let miniProgress: any = ConfigLocalStorage.get(LocalStorage.MINI_PLAYER_PROGRESS);
    progressBanner = JSON.parse(progressBanner || '{}');
    miniProgress = JSON.parse(miniProgress || '{}');
    const player: any = document.getElementById(id);

    const progressTime = progressBanner?.[contentId];
    if (contentId && progressTime > 0) {
      try {
        if (player) player.currentTime = progressTime;
      } catch (e) {
        // handle error
      }
    }

    if (typeof miniProgress?.[contentId] === 'number' && miniProgress?.[contentId] > 0) {
      try {
        if (player) player.currentTime = miniProgress?.[contentId];
      } catch (e) {
        // handle error
      }
    }
  };

  return (
    <video
      className={
        (className || '') + (isShowPlayer ? ' show animate-fade-in' : ' hide animate-fade-out')
      }
      id={id}
      ref={videoRef}
      style={{ ...style }}
      playsInline={!!(isMobile && isIOS)}
      muted
    >
      {isMP4 && <source src={linkPlay} type="video/mp4" />}
    </video>
  );
});

export default MiniPlayer;
