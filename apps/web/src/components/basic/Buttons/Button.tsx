import React, { forwardRef, useEffect } from 'react';
import { createTimeout } from '@helpers/common';
import SvgIcon from '@components/basic/Icon/SvgIcon';
import classNames from 'classnames';
import NewIcon from '../Icon/NewIcon';
import styles from './Button.module.scss';

let clickTimer: any = 0;
const Button = React.memo(
  forwardRef(
    (
      {
        title,
        subTitle,
        className,
        customizeClass,
        iconClass,
        iconName,
        iconNameSlotRight,
        iCustomizeClassSlotRight,
        iconSlotBoth, // Slot left and right
        disabled,
        type,
        onMouseEnter,
        onMouseLeave,
        onClick,
        onTouchStart,
        tagData,
        dataToggle,
        imgSrc,
        textClass,
        iconType,
        svgSize,
        isActiveIcon,
        theme,
        size,
        isFadeInForIcon,
        spImageClass,
        imgSrcRight
      }: any,
      ref: any
    ) => {
      useEffect(
        () => () => {
          clearTimeout(clickTimer);
        },
        []
      );

      const onClickButton = (e: any) => {
        if (clickTimer) clearTimeout(clickTimer);
        clickTimer = createTimeout(() => {
          if (onClick) onClick(e);
        }, 400);
      };

      const onTouchButtonStart = (e: any) => {
        if (clickTimer) clearTimeout(clickTimer);
        clickTimer = createTimeout(() => {
          if (onTouchStart) onTouchStart(e);
        }, 400);
      };

      return (
        <button
          ref={ref}
          className={classNames(className, styles[theme], styles[size], customizeClass)}
          onClick={onClickButton}
          onTouchStart={onTouchButtonStart}
          onMouseEnter={onMouseEnter || (() => {})}
          onMouseLeave={onMouseLeave || (() => {})}
          disabled={disabled}
          type={type || 'button'}
          title={subTitle || null}
          data-toggle={dataToggle || null}
        >
          {imgSrc && <img src={imgSrc} alt="VieON" />}
          {iconName && (iconSlotBoth || !iconNameSlotRight) && (
            <NewIcon
              spImageClass={spImageClass}
              iconName={iconName}
              isFadeIn={isFadeInForIcon}
              iCustomizeClass={iconClass}
            />
          )}
          {iconType && <SvgIcon size={svgSize} type={iconType} isActive={isActiveIcon} />}
          {!!title && <span className={textClass} dangerouslySetInnerHTML={{ __html: title }} />}
          {(iconNameSlotRight || imgSrcRight) && (
            <NewIcon
              spImageClass={spImageClass}
              iconName={iconNameSlotRight}
              isFadeIn={isFadeInForIcon}
              iCustomizeClass={iCustomizeClassSlotRight || iconClass}
              imageSrc={imgSrcRight}
            />
          )}
          {tagData?.label && <span className={tagData?.className}>{tagData.label}</span>}
        </button>
      );
    }
  )
);

export default Button;
