import React from 'react';
import classNames from 'classnames';

export const NextArrow = (props: any) => {
  const { onClickNext, nextClass } = props;

  return (
    <div
      className={classNames(
        `slider-navigate slider-navigate-next gradient flex-box is-hang`,
        nextClass
      )}
      onClick={onClickNext}
    />
  );
};

export const PrevArrow = (props: any) => {
  const { onClickPrev, prevClass } = props;

  return (
    <div
      className={classNames(
        `slider-navigate slider-navigate-prev gradient flex-box is-hang`,
        prevClass
      )}
      onClick={onClickPrev}
    />
  );
};
