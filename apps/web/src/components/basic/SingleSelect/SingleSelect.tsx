import React, { useRef, useState } from 'react';
import Select, { components } from 'react-select';
import classNames from 'classnames';

const { Menu<PERSON>ist, ValueContainer, Placeholder, DropdownIndicator } = components;

const cn = classNames;
const CustomMenuList = ({ selectProps, ...props }: any) => {
  const { onInputChange, inputValue, onMenuInputFocus, setInputValue } = selectProps;

  return (
    <div>
      <input
        id="inputValue"
        name="inputValue"
        style={{
          width: '97%',
          boxSizing: 'border-box',
          padding: 8,
          margin: '8px auto',
          borderRadius: '8px',
          border: '1px solid #CCC',
          fontSize: '14px',
          boxShadow: 'none'
        }}
        autoCorrect="off"
        autoComplete="off"
        spellCheck="false"
        type="text"
        value={inputValue}
        onChange={(e: any) => {
          onInputChange(e.currentTarget.value, {
            action: 'input-change'
          });
        }}
        onMouseDown={(e: any) => {
          e.stopPropagation();
          e.target.focus();
        }}
        onTouchEnd={(e: any) => {
          e.stopPropagation();
          e.target.focus();
        }}
        onFocus={onMenuInputFocus}
        placeholder="Tìm kiếm tên Tỉnh/Thành phố"
      />
      <div className="absolute top-[20px] right-[20px]">
        {inputValue.length > 0 ? (
          <div onClick={() => setInputValue('')}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 18 18"
              fill="none"
            >
              <path
                d="M17.7874 2.02499C17.9045 1.90783 17.9045 1.71788 17.7874 1.60072L16.3988 0.212132C16.2816 0.0949747 16.0917 0.0949748 15.9745 0.212132L9.21164 6.97501C9.09449 7.09217 8.90454 7.09217 8.78738 6.97501L2.0245 0.212132C1.90734 0.0949749 1.71739 0.0949747 1.60024 0.212132L0.211644 1.60072C0.0944864 1.71788 0.0944865 1.90783 0.211644 2.02499L6.97452 8.78787C7.09168 8.90503 7.09168 9.09497 6.97452 9.21213L0.211644 15.975C0.0944866 16.0922 0.0944865 16.2821 0.211644 16.3993L1.60024 17.7879C1.71739 17.905 1.90734 17.905 2.0245 17.7879L8.78738 11.025C8.90454 10.9078 9.09449 10.9078 9.21164 11.025L15.9745 17.7879C16.0917 17.905 16.2816 17.905 16.3988 17.7879L17.7874 16.3993C17.9045 16.2821 17.9045 16.0922 17.7874 15.975L11.0245 9.21213C10.9073 9.09497 10.9073 8.90503 11.0245 8.78787L17.7874 2.02499Z"
                fill="#9B9B9B"
              />
            </svg>
          </div>
        ) : (
          <svg
            width="19"
            height="18"
            viewBox="0 0 19 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g id="Group 717">
              <path
                id="Union"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13.4231 7.38461C13.4231 10.4434 10.9434 12.9231 7.88461 12.9231C4.82581 12.9231 2.34615 10.4434 2.34615 7.38461C2.34615 4.32581 4.82581 1.84615 7.88461 1.84615C10.9434 1.84615 13.4231 4.32581 13.4231 7.38461ZM13.6306 12.0235C14.6555 10.7557 15.2692 9.14182 15.2692 7.38461C15.2692 3.3062 11.963 0 7.88461 0C3.8062 0 0.5 3.3062 0.5 7.38461C0.5 11.463 3.8062 14.7692 7.88461 14.7692C9.64182 14.7692 11.2557 14.1555 12.5235 13.1306C12.561 13.2938 12.6433 13.4487 12.7704 13.5758L16.9242 17.7296C17.2847 18.0901 17.8692 18.0901 18.2296 17.7296C18.5901 17.3692 18.5901 16.7847 18.2296 16.4242L14.0758 12.2704C13.9487 12.1433 13.7938 12.061 13.6306 12.0235Z"
                fill="#9B9B9B"
              />
            </g>
          </svg>
        )}
      </div>
      <MenuList {...props} innerProps={undefined} />
    </div>
  );
};
const CustomValueContainer = ({ children, ...props }: any) => (
  <ValueContainer {...props}>
    <Placeholder {...props} isFocused={props.isFocused}>
      {props.selectProps.placeholder}
    </Placeholder>
    {React.Children.map(children, (child) => (child && child.type !== Placeholder ? child : null))}
  </ValueContainer>
);
const CustomDropdownIndicator = (props: any) => (
  <DropdownIndicator {...props}>
    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="8" viewBox="0 0 13 8">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.0754 5.30214C6.26641 5.47674 6.55908 5.47674 6.75009 5.30214L11.5939 0.874425C11.7977 0.688114 12.114 0.702308 12.3003 0.906128L12.5252 1.15216C12.7115 1.35598 12.6973 1.67225 12.4935 1.85856L7.14556 6.74709C7.14553 6.74711 7.1455 6.74714 7.14547 6.74717C7.14538 6.74725 7.1453 6.74733 7.14521 6.74741C7.04393 6.84004 6.92685 6.91071 6.80202 6.95803C6.67709 7.00538 6.54497 7.02914 6.41275 7.02914C6.28052 7.02914 6.1484 7.00538 6.02347 6.95803C5.89864 6.91071 5.78156 6.84004 5.68028 6.74741C5.6802 6.74733 5.68011 6.74725 5.68002 6.74717C5.67999 6.74714 5.67996 6.74712 5.67994 6.74709L0.332 1.85856C0.12818 1.67225 0.113987 1.35598 0.300298 1.15216L0.525196 0.906128C0.711507 0.702308 1.02777 0.688114 1.23159 0.874426L6.0754 5.30214Z"
        fill={props.isDisabled ? '#ccc' : '#9B9B9B'}
      />
    </svg>
  </DropdownIndicator>
);
const SingleSelect = ({ label, classNames, options, isDisabled, onChange, value, id }: any) => {
  const containerRef = useRef<any>(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [inputValue, setInputValue] = useState<any>('');

  const customStyles = {
    control: (styles: any, state: any) => ({
      ...styles,
      border: 'none',
      outline: 'none',
      background: 'transparent',
      borderRadius: '0',
      padding: '5px 0',
      boxShadow: 'none',
      borderBottom: state.isFocused ? '1px solid #646464FF' : '1px solid #cccccc',

      '&:hover': {
        border: 'none',
        borderBottom: state.isFocused ? '1px solid #646464' : '1px solid #cccccc',
        outline: 'none'
      },

      height: '100%',
      width: '100%'
    }),
    menu: (base: any) => ({
      ...base,
      borderRadius: '2px',
      border: '1px solid var(--Gray60, #9B9B9B)',
      background: '#FFF',
      boxShadow: 'none',
      zIndex: 10
    }),
    menuList: (base: any) => ({
      ...base,

      '::-webkit-scrollbar': {
        width: '6px',
        paddingRight: '8px'
      },

      '::-webkit-scrollbar-track': {
        background: 'none'
      },

      '::-webkit-scrollbar-thumb': {
        backgroundColor: '#9B9B9B',
        opacity: 1,
        visibility: 'visible',
        borderRadius: 0
      },

      '::-webkit-scrollbar-thumb:hover': {
        background: 'blue'
      },

      padding: 0,
      paddingRight: '8px',
      marginRight: '8px',
      maxHeight: '200px',
      background: '#fff'
    }),

    valueContainer: (provided: any) => ({
      ...provided,
      overflow: 'visible',
      padding: 0
    }),
    placeholder: (provided: any, state: any) => ({
      ...provided,
      position: 'absolute',
      top: state.hasValue || state.selectProps.inputValue || isMenuOpen ? '-.625rem' : '5px',
      transition: 'top 0.1s, font-size 0.1s',
      fontSize: state.hasValue || state.selectProps.inputValue || isMenuOpen ? '.75rem' : '1rem',
      left: '0',
      color: isDisabled ? '#ccc' : '#646464'
    }),
    option: (styles: any, { isFocused, isSelected }: any) => {
      const color = isSelected ? 'black' : isFocused ? '#646464' : '#646464';
      const backgroundColor = isSelected ? '#D9D9D9' : isFocused ? '#D9D9D9' : null;
      return { ...styles, color, backgroundColor };
    },

    singleValue: (styles: any) => ({
      ...styles,
      padding: '0',
      color: '646464'
    })
  };
  const onChangeValue = (value: any) => {
    setIsMenuOpen(false);
    onChange(value);
  };
  const openMenu = () => {
    setIsMenuOpen(true);
  };
  const closeMenu = () => {
    setIsMenuOpen(false);
  };
  return (
    <div className={cn(classNames)} ref={containerRef}>
      <Select
        id={id}
        classNamePrefix="select"
        styles={customStyles}
        name="select"
        placeholder={label}
        options={options}
        components={{
          MenuList: CustomMenuList,
          ValueContainer: CustomValueContainer,
          DropdownIndicator: CustomDropdownIndicator,
          IndicatorSeparator: () => null
        }}
        inputValue={inputValue}
        isSearchable={false}
        onMenuOpen={openMenu}
        onMenuClose={closeMenu}
        onFocus={() => setIsMenuOpen(true)}
        onChange={(value) => onChangeValue(value)}
        onInputChange={(val) => setInputValue(val)}
        menuIsOpen={isMenuOpen}
        isDisabled={isDisabled}
        value={value}
        menuPortalTarget={document.body}
      />
    </div>
  );
};

export default SingleSelect;
