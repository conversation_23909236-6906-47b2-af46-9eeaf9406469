import React from 'react';
import Image from '../../Image/Image';

const SoftLogo = ({ imgSrc, alt }: any) => {
  if (!imgSrc) return null;
  return (
    <div className="player__soft-logo absolute full layer-2">
      <div className="player__soft-logo__wrap">
        <Image id="playerBrandImg" src={imgSrc} alt={alt || 'Soft Logo'} notWebp />
      </div>
    </div>
  );
};

export default React.memo(SoftLogo);
