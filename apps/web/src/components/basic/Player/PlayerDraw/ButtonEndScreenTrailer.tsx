import { TEXT } from '@constants/text';
import React, { useEffect, useState } from 'react';
import Button from '../../Buttons/Button';

let timer: any = 0;
const ButtonEndScreenTrailer = React.memo(
  ({ viewCredits, onStartTrailer, trailerLink, onDetail }: any) => {
    const [count, setCount] = useState(10);
    useEffect(() => {
      if (!viewCredits && trailerLink) {
        timer = setInterval(() => {
          setCount((c) => {
            const temp = c - 1;
            if (temp < 1) {
              if (onStartTrailer) {
                onStartTrailer();
              }
              clearInterval(timer);
            }
            return temp;
          });
        }, 1000);
      }

      return () => {
        clearInterval(timer);
      };
    }, []);

    const onClick = () => {
      if (!trailerLink) {
        if (onDetail) onDetail();
        return;
      }
      if (count > 0) {
        clearInterval(timer);
        setCount(0);
        onStartTrailer();
      } else if (count <= 0 || !trailerLink) {
        if (onDetail) onDetail();
      }
    };

    let buttonTitle = `Trailer phát sau ${count}s`;
    if (count <= 0 || !trailerLink) buttonTitle = TEXT.DETAIL;
    return (
      <Button
        className="player__button player__button--glass-dark player__button--view-credits"
        iconName="vie-info-o-c-script"
        title={buttonTitle}
        onClick={onClick}
      />
    );
  }
);

export default ButtonEndScreenTrailer;
