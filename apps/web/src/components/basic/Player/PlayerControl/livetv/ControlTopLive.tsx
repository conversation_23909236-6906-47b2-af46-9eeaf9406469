import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { PAGE, TAG_KEY } from '@constants/constants';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import { setFavoriteChannel } from '@actions/liveTV';
import { getPlayerConfig } from '@actions/appConfig';
import { encodeParamDestination } from '@helpers/common';
import { useVieRouter } from '@customHook';
import Button from '../../../Buttons/Button';
import Tags from '../../../Tags/Tags';

const ControlTopLive = React.memo(
  ({
    timeTitle,
    mainTitle,
    isLive,
    isMobile,
    channelDetail,
    totalCCU,
    onCheckInfoDebugPlayer
  }: any) => {
    const dispatch = useDispatch();
    const router = useVieRouter();
    const profile = useSelector((state: any) => state?.Profile?.profile);
    const { title, isFavorite } = channelDetail;
    const playerConfig = useSelector((state: any) => state?.AppConfig?.playerConfig);
    const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
    const isInfoDebugPlayer = useMemo(
      () => (playerConfig?.viewInfoList || []).includes(profile?.mobile),
      [profile, playerConfig]
    );

    useEffect(() => {
      if (!playerConfig) {
        dispatch(getPlayerConfig());
      }
    }, [dispatch, playerConfig]);

    const onClickAddToFavorite = async () => {
      if (!profile?.id) {
        const remakeDestination = encodeParamDestination(router?.asPath);
        router.push(
          `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${TYPE_TRIGGER_AUTH.ADD_TO_LIST}`
        );
      } else {
        dispatch(setFavoriteChannel(channelDetail, true, isGlobal));
      }
    };

    return (
      <div className="player__controls--top mobile-device layer-4">
        <div className="container">
          <div
            className="player__controls-wrap align-middle"
            style={isMobile ? { marginBottom: '4px' } : {}}
          >
            <div className="tags-group horizontal shrink">
              {isLive ? <Tags tagKey={TAG_KEY.LIVE} /> : ''}
              {totalCCU > 0 && (
                <Tags
                  tagKey={TAG_KEY.TOTAL_CCU}
                  subClass="tags--live-count tags--dark-glass"
                  iClass="vie-eye-on-o-rc-medium"
                  content={totalCCU}
                />
              )}
            </div>
            {title && <div className="player__controls__text shrink">{title}</div>}
            {timeTitle && <div className="player__broadcast-time shrink">{timeTitle}</div>}
            {channelDetail && (
              <Button
                className="player__button player__button--add ml-auto space-x-2"
                iconClass="icon icon--small"
                iconName={`${isFavorite ? 'vie-tick' : 'vie-math-plus'}`}
                title="Danh sách"
                onClick={onClickAddToFavorite}
              />
            )}
            {isInfoDebugPlayer && (
              <Button
                className="button button--dark-glass absolute"
                customizeClass="bg-transparent right-[10%]"
                onClick={() => onCheckInfoDebugPlayer()}
                iClass="vie-info-o-c-script"
                spClass="icon--medium"
              />
            )}
          </div>
        </div>
        {mainTitle && (
          <div className="player__controls-wrap">
            <h2 className="player__title player__title-sub auto" style={{ display: 'block' }}>
              {mainTitle}
            </h2>
          </div>
        )}
      </div>
    );
  }
);

export default ControlTopLive;
