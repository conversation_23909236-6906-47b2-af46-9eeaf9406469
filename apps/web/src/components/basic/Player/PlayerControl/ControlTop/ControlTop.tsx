import React, { useEffect, useMemo } from 'react';
import { backFromPlayer } from '@functions/functions';
import { useDispatch, useSelector } from 'react-redux';
import NotifyMWebtoApp from '@components/basic/Player/PlayerControl/ControlTop/NotifyMWebtoApp';
import { usePorTrait, useVieRouter } from '@customHook';
import { getPlayerConfig } from '@actions/appConfig';
import NotifyVipFeature from './NotifyVipFeature';
import NotifySvodTrial from './NotifySvodTrial';
import NotifyTVod from './NotifyTVod';
import Button from '../../../Buttons/Button';
import { segmentRegistrationEventsPopupAuth } from '@tracking/functions/TrackingContentForceLoginPopupAuth';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import LocalStorage from '@config/LocalStorage';

const ControlTop = React.memo(
  ({
    title,
    subTitle,
    content,
    isLiveTV,
    isLiveStream,
    ads,
    showController,
    isAdsEnd,
    contentDetail,
    onCheckInfoDebugPlayer,
    playerReady,
    isShowEndScreen,
    isWarningScreen,
    hasCalledTrackingVipFeature,
    currentTime,
    isTrialContent,
    duration,
    currentEpisode
  }: any) => {
    const router = useVieRouter();
    const dispatch = useDispatch();
    const profile = useSelector((state: any) => state?.Profile?.profile);
    const playerConfig = useSelector((state: any) => state?.AppConfig?.playerConfig);
    const isInfoDebugPlayer = useMemo(
      () => (playerConfig?.viewInfoList || []).includes(profile?.mobile),
      [profile, playerConfig]
    );

    const dataContent = content;
    const dataContentById = contentDetail;
    const isMobile = useSelector((state: any) => state?.App?.isMobile);
    const featureFlag = useSelector((state: any) => state?.App?.webConfig?.featureFlag);

    const { isPremium } = profile || {};
    const isFreeTrial = dataContentById?.trialDuration > 0;
    const isVip = dataContent?.isVip || dataContentById?.isVip;
    const { porTrait } = usePorTrait();

    const onBack = () => {
      backFromPlayer({ router, contentId: content?.id });
      if (isTrialContent) {
        segmentRegistrationEventsPopupAuth({
          contentType: currentEpisode?.type || content?.type || contentDetail?.type,
          cancel: true
        });
      }
      ConfigLocalStorage.remove(LocalStorage.RE_LOGIN_PARAMS);
    };

    useEffect(() => {
      if (!playerConfig) {
        dispatch(getPlayerConfig());
      }
    }, []);
    if (isLiveTV || isLiveStream || isShowEndScreen) return null;

    let sTitle = '';
    if (content?.seasonName) sTitle = content?.seasonName;
    else {
      const activeSeason = (content?.relatedSeason || []).find(
        (item: any) => item.id === content?.id
      );
      if (activeSeason?.name) sTitle = content?.relatedSeason > 1 ? activeSeason?.name : '';
    }
    sTitle += (sTitle ? ': ' : '') + (subTitle || '');

    let wrapClass = 'player__controls-wrap flex-box align-middle';
    if (!showController) wrapClass += ' hide';
    return (
      <div className="player__controls--top layer-7 pt-2 md:pt-0">
        <div className={wrapClass}>
          <Button
            className="player__controls-button player__controls-button--back shrink !text-[1.125rem] md:!text-[22px]"
            iconName="vie-chevron-left-r-medium"
            iconAlign="middle-left"
            onClick={onBack}
          />
          {title && <div className="player__title shrink flex-box align-middle">{title}</div>}
          {sTitle && <div className="player__title player__title-sub auto">{sTitle}</div>}
          {isInfoDebugPlayer && (
            <Button
              className="button button--dark-glass absolute"
              customizeClass="bg-transparent right-[10%]"
              onClick={() => onCheckInfoDebugPlayer()}
              iconName="vie-info-o-c-script"
              spClass="icon--medium"
            />
          )}
        </div>
        <NotifyTVod
          playerReady={playerReady}
          showController={showController}
          content={content}
          contentDetail={contentDetail}
          isWarningScreen={isWarningScreen}
        />
        {featureFlag?.mwebToApp &&
          (contentDetail?.isMovieTrialInApp || contentDetail?.isEpisodeTrialInApp) &&
          !porTrait && (
            <NotifyMWebtoApp
              playerReady={playerReady}
              showController={showController}
              contentDetail={contentDetail}
              content={content}
            />
          )}
        {!isMobile && !isPremium && isVip && isFreeTrial && !isLiveStream && (
          <div className={wrapClass}>
            <NotifySvodTrial
              trialDuration={
                content?.trialDuration > 0 ? content?.trialDuration : dataContentById?.trialDuration
              }
              content={content}
              contentDetail={contentDetail}
            />
          </div>
        )}
        {!isPremium && !isFreeTrial && !isLiveStream && (
          <NotifyVipFeature
            contentDetail={contentDetail}
            currentTime={currentTime}
            contentData={content}
            showController={showController}
            ads={ads}
            isAdsEnd={isAdsEnd}
            hasCalledTrackingVipFeature={hasCalledTrackingVipFeature}
            durationVideo={duration}
            currentEpisode={currentEpisode}
          />
        )}
      </div>
    );
  }
);

export default ControlTop;
