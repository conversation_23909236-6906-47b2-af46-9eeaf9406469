import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { openPopup } from '@actions/popup';
import { PLAYER_TYPE } from '@constants/player';
import { setVideoPlay } from '@helpers/common';
import { TEXT } from '@constants/text';
import ConfigImage from '@config/ConfigImage';
import Modal from '../../../Modal';

const PopupUserReport = ({ onSkip, onSend, settingData }: any) => {
  const reports = settingData?.reports;
  const data = reports?.items || [];
  const [state, setState] = useState({
    dataUserReport: false,
    checkedItems: new Map(),
    disabled: false,
    textReport: ''
  });

  const [closePopupFinish, setClosePopupFinish] = useState(false);
  const dispatch = useDispatch();
  const profile = useSelector((state: any) => state?.Profile);
  const { statusEndScreenVod } = useSelector((state: any) => state?.Detail);
  const onChangeTextReport = (e: any) => {
    const isValue = e.target.value;
    setState((prevState) => ({
      ...prevState,
      textReport: isValue
    }));
  };

  useEffect(() => {
    if (state.dataUserReport) {
      let disable = false;
      state.checkedItems.forEach((element) => {
        if (element) {
          disable = true;
        }
      });
      setState((prevState) => ({
        ...prevState,
        disabled: disable,
        dataUserReport: false
      }));
    }
  }, [state.dataUserReport]);

  const onChangeItem = (e: any) => {
    const item = e.target.name;
    const isChecked = e.target.checked;
    setState((prevState) => ({
      ...prevState,
      checkedItems: state.checkedItems.set(item, isChecked),
      dataUserReport: true
    }));
  };

  const onClickOnSend = async (e: any) => {
    e.preventDefault();
    if (profile) {
      setClosePopupFinish(true);
      if (typeof onSend === 'function') {
        onSend(state.checkedItems, state.textReport, reports, closePopupFinish);
      }
    }
  };

  const closePopup = () => {
    dispatch(openPopup());
    if (!statusEndScreenVod) setVideoPlay({ playerId: PLAYER_TYPE.VOD });
  };

  const onClickFinish = async (dataSuccess: any) => {
    await setClosePopupFinish(false);
    if (typeof onSkip === 'function') onSkip(dataSuccess);
    closePopup();
  };

  const renderBody = () => (
    <div className="block block--for-dark block--user-report">
      <h2 className="title text-center">{TEXT.WHAT_HAPPENS}</h2>
      <p className="text text-muted text-center"> Chọn tất cả các phương án đúng.</p>
      <form className="form form--member form--member-feedback form--member-report">
        <fieldset>
          {data.map((item: any, i: any) => (
            <label key={item?.id || i} className="checkbox-custom m-b2" htmlFor={item?.id}>
              <input
                type="checkbox"
                id={item?.id}
                onChange={(e) => {
                  onChangeItem(e);
                }}
                checked={state.checkedItems.get(item)}
                name={item?.id || i}
              />
              <span className="checkmark" />
              {item?.title}
              <span className="text text-muted">{item?.description}</span>
            </label>
          ))}
        </fieldset>
        <label htmlFor="memberFeedbackUnsatisfied07">
          <span>Bạn có muốn bổ sung thêm thông tin?</span>
          <span className="text-muted"> (Không bắt buộc)</span>
        </label>
        <textarea
          className="m-t2"
          rows={3}
          onChange={onChangeTextReport}
          value={state.textReport || ''}
        />
        <div className="button-group child-auto">
          <button
            className="button button--light button--large"
            disabled={!state.disabled}
            type="submit"
            title="Gửi"
            onClick={onClickOnSend}
          >
            <span className="text">{TEXT.SEND}</span>
          </button>
        </div>
      </form>
    </div>
  );

  const renderHeaderSuccess = () => (
    <div className="mask">
      <div className="mask-inner text-center">
        <img src={ConfigImage.userReportBanner} alt="user report success" />
      </div>
    </div>
  );

  const renderBodySuccess = () => (
    <div className="block block--for-dark block--user-report block--user-report-success">
      <h2 className="title text-center">{TEXT.YOUR_IDEA_IS_VERY_IMPORTANT}</h2>
      <p className="text text-center">{TEXT.USER_REPORT_SUB}</p>
      <div className="button-group child-auto">
        <button
          className="button button--light button--large"
          title="Hoàn tất"
          aria-label="Bấm để hoàn tất"
          onClick={onClickFinish}
        >
          <span className="text">{TEXT.FINISHED}</span>
        </button>
      </div>
    </div>
  );

  if (closePopupFinish) {
    return (
      <Modal
        className=" green-line modal--user modal--user-report-success"
        renderBody={renderBodySuccess}
        renderHeader={renderHeaderSuccess}
        notClosedButton
      />
    );
  }

  return (
    <Modal
      className="green-line modal--user modal--user-report"
      renderBody={renderBody}
      onClosed={closePopup}
    />
  );
};

export default PopupUserReport;
