import React from 'react';
import { TEXT } from '@constants/text';
import styles from '@components/basic/Player/PlayerControl/ControlBottom/ControlBottom.module.scss';
import Button from '../../../../Buttons/Button';
const PlayButton = React.memo(({ isPaused, onPlayPause, playerError }: any) => (
  <Button
    className={`${styles.Button}`}
    iconClass={styles.ButtonIconI}
    textClass={styles.ButtonText}
    subTitle={isPaused ? TEXT.PLAY : TEXT.PAUSED}
    onClick={onPlayPause || (() => {})}
    iconName={
      isPaused || playerError
        ? 'vie-play-solid-rc'
        : !isPaused && !playerError
        ? 'vie-pause-rc-s'
        : ''
    }
  />
));

export default PlayButton;
