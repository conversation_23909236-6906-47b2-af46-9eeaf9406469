import React, { useEffect, useMemo, useRef, useState } from 'react';
import CustomProgress from '@components/basic/CustomProgress/CustomProgress';
import classNames from 'classnames';
import CurrentTime from '../CurrentTime/CurrentTime';

const SeekBar = ({
  seekValue,
  onControlSeeking,
  onControlSeekChange,
  notSeekBar,
  noSeeker,
  hoverTimeText,
  isDVR,
  thumbMetadata,
  thumbSrc,
  isFullscreen,
  currentTimeText,
  durationText,
  isOnEnded,
  isLoading,
  playerReady,
  showController,
  onControlThumbnail,
  onHandleOffAds,
  isHasCompanionBanner
}: any) => {
  const [isPreviewThumb, setIsPreviewThumb] = useState(false);
  const [mouseLeft, setMouseLeft] = useState<any>(0);
  const className = useMemo(
    () =>
      (isLoading && !playerReady) || !showController
        ? 'player__controls-container hide'
        : 'player__controls-container',
    [isLoading, playerReady, showController]
  );
  const onControlMouseMove = (value: any) => {
    if (typeof onControlThumbnail === 'function') onControlThumbnail(value);
    let temp = 0;
    const thumbEl = document.getElementsByClassName('progress-bar__thumbnail');
    if (thumbEl && thumbEl[0]) {
      temp = thumbEl[0].clientWidth;
    }
    setMouseLeft(`calc(${value}% - ${temp * 0.5}px)`);
  };

  const onControlMouseOver = () => {
    if (!isPreviewThumb) {
      setIsPreviewThumb(true);
    }
  };

  const onControlMouseOut = () => {
    if (isPreviewThumb) {
      setIsPreviewThumb(false);
    }
  };
  const [timeBoxLeft, setTimeBoxLeft] = useState<any>('');
  const timeBoxRef = useRef<any>(null);
  useEffect(() => {
    if (timeBoxRef.current) {
      setTimeBoxLeft(`left-[calc(50% - ${timeBoxRef.current.offsetWidth}/2]`);
    }
  }, [hoverTimeText]);

  return (
    <div className={`${className} ${isHasCompanionBanner && !isFullscreen ? '!px-0' : ''}`}>
      <CustomProgress
        style={notSeekBar ? { opacity: 0 } : {}}
        inputId={ELEMENT.INPUT_ID}
        progressId={ELEMENT.PROGRESS_ID}
        subProgressId={ELEMENT.SUB_PROGRESS_ID}
        step={0.01}
        value={seekValue}
        noSeeker={noSeeker}
        onControlSeeking={onControlSeeking}
        onControlSeekChange={onControlSeekChange}
        onControlMouseMove={onControlMouseMove}
        onControlMouseOver={onControlMouseOver}
        onControlMouseOut={onControlMouseOut}
        hoverTimeText={hoverTimeText}
        thumbMetadata={thumbMetadata}
        thumbSrc={thumbSrc}
        mouseLeft={mouseLeft}
        isPreviewThumb={isPreviewThumb}
        isFullscreen={isFullscreen}
        isOnEnded={isOnEnded}
        onHandleOffAds={onHandleOffAds}
        isDVR={isDVR}
      />
      {(!isDVR || (isDVR && currentTimeText)) && (
        <CurrentTime
          currentTimeText={currentTimeText}
          isDVR={isDVR}
          durationText={durationText}
          isOnEnded={isOnEnded}
        />
      )}
      {/* Time box for livestream */}
      {isDVR && hoverTimeText && (
        <div
          ref={timeBoxRef}
          className={classNames(
            isPreviewThumb ? 'opacity-100' : 'opacity-0',
            timeBoxLeft,
            'absolute bottom-[2.75rem] right-1/2 text-white w-fit m-0 p-0',
            isFullscreen ? '!text-[36px]' : '!text-[24px]'
          )}
        >
          -{hoverTimeText}
        </div>
      )}
      {/* End Time box for livestream */}
    </div>
  );
};

const ELEMENT = {
  INPUT_ID: 'inputSeekBar',
  PROGRESS_ID: 'progressSeekBar',
  SUB_PROGRESS_ID: 'subProgressId'
};

export default SeekBar;
