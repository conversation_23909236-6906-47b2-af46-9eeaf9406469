import React, { forwardRef } from 'react';
import Styles from './Subtitles.module.scss';

const Subtitles = (_: any, ref: any) => (
  <div className={Styles.subtitle} ref={ref}>
    <div className="container text-center m-x-auto">
      <span
        style={{
          WebkitTextStroke: '1px black',
          WebkitFontSmoothing: 'antialiased',
          MozOsxFontSmoothing: 'grayscale',
          fontSmooth: 'antialiased'
        }}
      />
    </div>
  </div>
);

export default forwardRef(Subtitles);
