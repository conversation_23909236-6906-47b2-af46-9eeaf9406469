import React from 'react';
import { EL_THEME_CLASS, ON_OFF, TAG_KEY } from '@constants/constants';
import Tags from '../../../Tags/Tags';

const TagsLiveStream = ({ classPosition, buttonLive, showCCU, totalCCU, isPremiere }: any) => {
  if (buttonLive !== ON_OFF.ON && showCCU !== ON_OFF.ON) return null;
  return (
    <div className={classPosition?.classPosition}>
      {buttonLive === ON_OFF.ON && (
        <Tags tagKey={TAG_KEY.LIVE} title={isPremiere ? 'Premiere' : 'Live'} subClass=" small" />
      )}
      {totalCCU > 0 && showCCU === ON_OFF.ON && (
        <Tags
          tagKey={TAG_KEY.TOTAL_CCU}
          iClass="vie-eye-on-o-rc-medium"
          spClass="!flex"
          content={totalCCU}
          theme={EL_THEME_CLASS.BLACK_GLASS_50}
        />
      )}
    </div>
  );
};
export default TagsLiveStream;
