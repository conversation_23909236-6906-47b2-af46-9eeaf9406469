import React, { useEffect, useMemo } from 'react';
import { onOpenPayment } from '@helpers/common';
import { useSelector, useDispatch } from 'react-redux';
import {
  exclusiveHotContentSubscribePackageButtonSelected,
  exclusiveHotContentSubscribePackageLoaded
} from '@tracking/functions/TrackingPaymentConversion';
import { VALUE } from '@config/ConfigSegment';
import { useVieRouter } from '@customHook';
import { POPUP } from '@constants/constants';
import { openPopup } from '@actions/popup';

const NotifyEpisode = ({ windowingMessage, contentData, isOpenEpisodeList }: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const userType = useSelector((state: any) => state?.User?.USER_TYPE);
  const paymentConversion = useSelector((state: any) => state?.App?.paymentConversion);
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { windowing, packageId } = paymentConversion || {};
  const { message, button } = windowing?.playerIntro || {};
  const parseMessage = useMemo(
    () => message?.replace('{windowingMessage}', windowingMessage),
    [paymentConversion, windowingMessage]
  );
  const onClickOpenPayment = () => {
    exclusiveHotContentSubscribePackageButtonSelected({
      triggerFrom: VALUE.EXCLUSIVE_HOT_CONTENT_INFO,
      userType: userType?.userType,
      contentId: contentData?.id,
      contentName: contentData?.title
    });
    if (isKid) {
      dispatch(openPopup({ name: POPUP.NAME.KID_LIMITED_VIP_DIALOG }));
    }
    onOpenPayment(router, {
      returnUrl: window?.location?.href,
      pkg: packageId,
      referralCode: 4,
      newTriggerPaymentBuyPackage: {
        isGlobal,
        profileId: profile?.id
      }
    });
  };

  useEffect(() => {
    if (windowingMessage && isOpenEpisodeList) {
      exclusiveHotContentSubscribePackageLoaded({
        triggerFrom: VALUE.EXCLUSIVE_HOT_CONTENT_INFO,
        userType: userType?.userType,
        contentId: contentData?.id,
        contentName: contentData?.title
      });
    }
  }, [contentData, windowingMessage, isOpenEpisodeList]);

  return (
    <div className="notify player__notify player__notify--appeal" onClick={onClickOpenPayment}>
      <div className="notify__inner flex-box relative">
        <span className="icon absolute left-1 middle-v">
          <i className="vie vie-info-o-c-script" />
        </span>
        <div className="notify__content">
          <button className="button">{button}</button> {parseMessage}
        </div>
      </div>
    </div>
  );
};
export default NotifyEpisode;
