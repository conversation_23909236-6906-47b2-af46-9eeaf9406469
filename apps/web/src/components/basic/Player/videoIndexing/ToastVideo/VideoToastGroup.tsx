import React, { useEffect, useMemo, useRef, useState } from 'react';
import classNames from 'classnames';
import { POSITION_ACTION, VIDEO_INDEXING } from '@constants/constants';
import { TEXT } from '@constants/text';
import Image from '@components/basic/Image/Image';
import Button from '@components/basic/Buttons/Button';
import isEmpty from 'lodash/isEmpty';
import { useDispatch, useSelector } from 'react-redux';
import { getDataProductBrand, setTypeActionVideoIndexing } from '@actions/detail';
import TrackingVieIndexing from '@tracking/functions/TrackingVieIndexing';
import animationData from './shopping-cart-animation.json';
import styles from './VideoToastGroup.module.scss';

// fix hydration error
import dynamic from 'next/dynamic';
const Lottie: any = dynamic(() => import('react-lottie'), { ssr: false });

const defaultOptions = {
  loop: 1,
  autoplay: true,
  animationData,
  rendererSettings: {
    preserveAspectRatio: 'xMidYMid slice',
    hideOnTransparent: false
  }
};

let listBrand: any = [];
const VideoToastGroup = ({
  showController,
  isShowListProduct,
  enableIconRed,
  isToastAnimation,
  handleShowIndicator,
  handleActionVideoIndexing,
  isSeekVideoShowToast,
  itemsInMarksBrand,
  itemsInMarks,
  data
}: any) => {
  const dispatch = useDispatch();
  const brandEl = useRef<any>(null);
  const timeBrandEl = useRef<any>(null);
  const { sessionId, GET_CONTENT } = useSelector((state: any) => state?.Detail) || {};
  const timeRef = useRef<any>(null);
  const elementRef = useRef<any>(null);
  const [enableEye, setEnableEye] = useState(true);
  const [enableHoverEye, setEnableHoverEye] = useState(false);
  const [indexTypeBrand, setIndexTypeBrand] = useState(0);
  const [isCollapsed, setCollapsed] = useState(false);
  const { meta } = data || {};

  const isPauseAnimation = useMemo(() => {
    if (enableIconRed) return false;
    if (!isToastAnimation) return true;
    return false;
  }, [enableIconRed, isToastAnimation]);

  const getMetaDataBrandById = useMemo(() => {
    if (!isEmpty(itemsInMarksBrand) && !isEmpty(meta?.objects)) {
      return meta?.objects.filter(
        (obj: any) =>
          itemsInMarksBrand?.some((x: any) => x.objectId === obj?.id) &&
          obj.type === VIDEO_INDEXING.BRAND
      );
    }
  }, [itemsInMarksBrand, meta?.objects]);

  const isBrandOther = !isEmpty(getMetaDataBrandById) && getMetaDataBrandById?.length > 1;

  useEffect(() => {
    if (!isEmpty(getMetaDataBrandById)) {
      const dataTracking = handleDataTracking({ listData: listBrand });
      if (!isEmpty(dataTracking)) {
        listBrand = listBrand?.concat(dataTracking);
        handleTrackingShowBrand({ dataTracking });
      }
    }
  }, [getMetaDataBrandById]);
  useEffect(() => {
    if (itemsInMarksBrand?.length > 0) {
      if (timeBrandEl.current) clearTimeout(timeBrandEl.current);
      timeBrandEl.current = setTimeout(() => {
        setCollapsed(true);
      }, 5000);
      setCollapsed(false);
    }
    return () => {
      if (timeBrandEl.current) clearTimeout(timeBrandEl.current);
    };
  }, [itemsInMarksBrand?.length]);

  useEffect(() => {
    if (timeRef.current) clearInterval(timeRef.current);
    if (getMetaDataBrandById?.length > 1) {
      timeRef.current = setInterval(() => {
        setIndexTypeBrand((currentIndex) => (currentIndex + 1) % getMetaDataBrandById?.length);
      }, 7000);
      return;
    }
    setIndexTypeBrand(0);
    return () => clearInterval(timeRef.current);
  }, [getMetaDataBrandById?.length]);

  const handleListProductBrand = (itemBrand: any) =>
    meta?.objects?.filter((item: any) => item?.props?.brand?.id === itemBrand?.id);
  const handleClickCart = ({ e, activeIcon, indexingType, itemBrand }: any) => {
    if (indexingType === VIDEO_INDEXING.ALL_BRAND) {
      e.stopPropagation();
    }
    const listProductBrand = handleListProductBrand(itemBrand);
    if (indexingType === VIDEO_INDEXING.BRAND) {
      TrackingVieIndexing.viewBrandDetailSelected({ data: GET_CONTENT, sessionId });
    }
    if (indexingType === VIDEO_INDEXING.BRAND && isEmpty(listProductBrand)) {
      TrackingVieIndexing.exploreBrandButtonSelected({
        data: GET_CONTENT,
        item: itemBrand,
        sessionId
      });
      window.open(itemBrand?.detailsUrl, '_blank');
    } else {
      if (!isEmpty(getMetaDataBrandById)) {
        dispatch(
          getDataProductBrand({
            data: itemBrand?.id ? listProductBrand : getMetaDataBrandById,
            noPrevious: true
          })
        );
      }
      dispatch(
        setTypeActionVideoIndexing({
          activeIcon,
          indexingType,
          showProduct: true
        })
      );
    }
    if (typeof handleActionVideoIndexing === 'function') {
      handleActionVideoIndexing({ positionClick: POSITION_ACTION.TOAST });
    }
  };
  const handleDataTracking = ({ listData }: any) =>
    getMetaDataBrandById?.filter((item: any) => !listData?.some((it: any) => it.id === item.id));
  const handleTrackingShowBrand = ({ dataTracking }: any) => {
    dataTracking?.map((item: any) => {
      TrackingVieIndexing.showBrand({
        data: GET_CONTENT,
        sessionId,
        brandName: item?.name
      });
    });
  };
  const handleClickIconEye = () => {
    if (!enableEye) TrackingVieIndexing.showIndicatorSelected();
    else TrackingVieIndexing.hideIndicatorSelected();
    setEnableEye(!enableEye);
    if (typeof handleShowIndicator === 'function') handleShowIndicator(!enableEye);
  };
  const handleOnMouseOver = () => {
    setEnableHoverEye(true);
  };
  const handleOnMouseOut = () => {
    setEnableHoverEye(false);
  };

  if (isShowListProduct) return false;
  if (isEmpty(getMetaDataBrandById) && isEmpty(itemsInMarks) && !isSeekVideoShowToast) return false;

  return (
    <div
      className={classNames(
        'layer-5 absolute',
        styles['safe-toast'],
        styles.transition,
        isToastAnimation && styles['animation-toast']
      )}
      style={!showController ? { bottom: '40px' } : {}}
    >
      {enableHoverEye && (
        <div
          className={classNames(
            'display-flex-inline animate-fade-in absolute',
            styles['title-eye'],
            (isCollapsed || isEmpty(itemsInMarksBrand)) && styles.eyeCollapsed
          )}
        >
          <p className="text text-white text-medium m-b line-height-default">
            {TEXT.VIDEO_INDEXING.TITLE_ICON_EYE}
          </p>
        </div>
      )}
      <div className={styles['display-flex']}>
        <div className={`${styles['bg-toast-eye']} flex-box justify-content-center align-middle`}>
          <div
            className={classNames(
              'text text-white relative align-items-center flex-box justify-content-center align-middle',
              styles['background-icon-eye'],
              styles['bg-toast-item']
            )}
            onClick={handleClickIconEye}
            onMouseOver={handleOnMouseOver}
            onMouseOut={handleOnMouseOut}
            onBlur={() => {}} // Add the onBlur event handler
            onFocus={() => {}} // Add the onFocus event handler
          >
            <i
              className={classNames(
                'vie',
                styles['icon-eye'],
                enableEye && 'vie-eye-on-o-rc-medium',
                !enableEye && 'vie-eye-off-o-rc-medium'
              )}
            />
          </div>
        </div>
        <div className={classNames('animate-fade-in', styles.groupCartBrand)} ref={elementRef}>
          {(!isEmpty(itemsInMarks) || isSeekVideoShowToast) && (
            <div
              className={classNames(
                styles.shoppingCart,
                isEmpty(getMetaDataBrandById) && styles.borderNotBrand
              )}
              onClick={() => {
                handleClickCart({ activeIcon: true, indexingType: VIDEO_INDEXING.PRODUCT });
              }}
            >
              {enableIconRed && (
                <Button className={classNames(styles['icon-red'], 'active absolute')} />
              )}
              <Lottie
                isPaused={isPauseAnimation}
                height={50}
                width={50}
                options={defaultOptions}
                animationData={animationData}
                style={{ borderRadius: '4px' }}
              />
            </div>
          )}
          {!isEmpty(itemsInMarksBrand) && (
            <ListBrands
              items={getMetaDataBrandById}
              currentIndex={indexTypeBrand}
              isSeekVideoShowToast={isSeekVideoShowToast}
              enableEye={enableEye}
              handleClickCart={handleClickCart}
              getMetaDataBrandById={getMetaDataBrandById}
              isBrandOther={isBrandOther}
              indexTypeBrand={indexTypeBrand}
              brandEl={brandEl}
              isCollapsed={isCollapsed}
            />
          )}
        </div>
      </div>
    </div>
  );
};
export default VideoToastGroup;

export const ListBrands = ({
  items,
  currentIndex,
  isSeekVideoShowToast,
  enableEye,
  handleClickCart,
  getMetaDataBrandById,
  isBrandOther,
  indexTypeBrand,
  brandEl,
  isCollapsed
}: any) => (
  <div
    className={classNames(
      styles.brand,
      !isCollapsed && styles.brandCollapsed,
      !isSeekVideoShowToast && styles.pdNotCard,
      !isCollapsed && isSeekVideoShowToast && styles.pdNotCard
    )}
    onClick={() => {
      handleClickCart({
        indexingType: VIDEO_INDEXING.BRAND,
        itemBrand: getMetaDataBrandById[indexTypeBrand]
      });
    }}
  >
    {items?.map((item: any, index: any) => {
      if (currentIndex === index) {
        return (
          <div key={item?.name + index} className={classNames(styles.groupImageText)}>
            <Image
              className={classNames(
                styles.imageBrand,
                !isCollapsed && styles.widthImgBrandCollapsed
              )}
              src={item.thumbnailUrl || getMetaDataBrandById[0]?.thumbnailUrl}
              notWebp
            />
            {!enableEye && !isBrandOther ? null : (
              <div
                className={classNames(styles.text, !isCollapsed && styles.textBrandCollapse)}
                ref={enableEye && index === currentIndex ? brandEl : null}
              >
                {enableEye && (
                  <span className="text text-medium">
                    {item?.name || getMetaDataBrandById[0]?.name}
                  </span>
                )}
                {isBrandOther && (
                  <div
                    className={classNames(
                      enableEye ? styles.iconBrandOther : '',
                      isCollapsed && styles.iconBrandOtherCollapse
                    )}
                    onClick={(e) => handleClickCart({ e, indexingType: VIDEO_INDEXING.ALL_BRAND })}
                  >
                    +{getMetaDataBrandById?.length - 1}
                  </div>
                )}
              </div>
            )}
          </div>
        );
      }
      return null; // Return null for non-matching indexes
    })}
  </div>
);
