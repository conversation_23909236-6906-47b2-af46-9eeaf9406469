// Modules
@use 'sass:math';

/* =============== function calculate rem =============== */

@function rem($size) {
  //
  $remSize: math.div($size, 16);
  //
  @return #{$remSize}rem;
}

/* =============== style =============== */
.full-player-indexing {
  background: rgba(17, 17, 17, 0.6);
  padding: rem(55) rem(40);
  backdrop-filter: blur(10px);
  overflow-y: scroll;
  z-index: 99;

  .product-box {
    overflow-y: auto;
    /* width */
    &::-webkit-scrollbar {
      width: rem(4);
      border-radius: rem(2);
    }

    /* Track */
    &::-webkit-scrollbar-track {
      background: rgb(99, 99, 99, 0.3);
      border-left: rem(1) solid transparent;
      border-right: rem(1) solid transparent;
      background-clip: padding-box;
      border-radius: rem(2);
    }

    /* Handle */
    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: rem(2);
      visibility: visible;
      opacity: 1;
    }
  }

  .icon {
    font-size: rem(18);
  }

  .info {
    align-items: baseline;
    .title {
      font-weight: 700;
      &::after {
        position: absolute;
        top: 0;
        left: 0;
        content: '';
        height: 100%;
        border-left: 1px solid #ccc;
        opacity: 0.2;
      }
    }
  }

  .item {
    &:not(.no-background) {
      background: rgba(26, 24, 24, 0.6);
    }

    .image {
      width: rem(190);
      height: rem(190);
      background-color: #000;
    }

    .image-max-width {
      width: rem(110);
      height: rem(110);
    }

    &.active {
      background: #111 !important;
    }

    .font-size-10 {
      font-size: rem(10);
    }
    .font-size-18 {
      font-size: rem(18);
    }

    .brand {
      font-size: rem(10);
    }
    .redirect-button {
      padding: rem(8);
      border-top: 1px solid rgba(204, 204, 204, 0.2);

      &:nth-child(even) {
        border-left: 1px solid rgba(204, 204, 204, 0.2);
      }

      i {
        font-size: rem(18);
      }
    }
    .single-redirect-button {
      padding: rem(8) rem(38);
      border: 1px solid rgba(204, 204, 204, 0.3);
      background: rgba(17, 17, 17, 0.5);
    }
    .input {
      margin: 0;
      padding-bottom: 0 !important;
      ::placeholder {
        color: #646464;
      }
    }
    .button {
      font-weight: 700;
    }

    .advise-box {
      top: 100%;
    }

    .error {
      border-top: 1px solid #e74c3c;
    }

    .button-line-clamp {
      flex-flow: nowrap;
      span {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }
}
