import React, { useEffect, useMemo, useState } from 'react';
import classnames from 'classnames';
import { TEXT } from '@constants/text';
import isEmpty from 'lodash/isEmpty';
import Button from '@components/basic/Buttons/Button';
import Image from '@components/basic/Image/Image';
import { VIDEO_INDEXING } from '@constants/constants';
import ConfigImage from '@config/ConfigImage';
import { setTypeActionVideoIndexing, getDataProductBrand } from '@actions/detail';
import { useDispatch, useSelector } from 'react-redux';
import TrackingVieIndexing from '@tracking/functions/TrackingVieIndexing';
import InfoBoxItem from '../InfoBox/InfoBoxItem';
import AllBrandItem from './AllBrandItem';
import styles from './videoIndexing.module.scss';

const IndexingList = ({
  indexingData,
  type,
  isShowAllProduct,
  handleShowAllProduct,
  dataProductAndBrand,
  noPrevious,
  contentDetail,
  itemsInMarks,
  getMetaDataBrandById,
  handleClicked,
  idItemClick,
  isEnabled
}: any) => {
  const dispatch = useDispatch();
  const { listProductBrand, GET_CONTENT, sessionId } =
    useSelector((state: any) => state?.Detail) || {};
  // Variables
  const [data, setData] = useState(indexingData);

  // ClassnamesId
  const productBoxClassName = classnames('size-h-full', styles['product-box']);

  const dataAllProduct = useMemo(() => {
    if (!isEmpty(indexingData)) {
      if (type === VIDEO_INDEXING.ALL_PRODUCT) {
        return indexingData.filter((item: any) => item?.type === VIDEO_INDEXING.PRODUCT);
      }
      if (type === VIDEO_INDEXING.ALL_BRAND) return getMetaDataBrandById;
      return [];
    }
  }, [indexingData, type, getMetaDataBrandById]);
  // Functions

  const handleShowAllProductFromBrand = ({ showProduct, indexingType }: any) => {
    dispatch(setTypeActionVideoIndexing({ indexingType, showProduct }));
  };

  const handleClickButton = ({ showProduct, indexingType }: any) => {
    TrackingVieIndexing.showAllProductInContent({ data: GET_CONTENT, sessionId });
    handleShowAllProduct(true);
    dispatch(setTypeActionVideoIndexing({ indexingType, showProduct }));
  };

  const handleClickButtonPreviousPage = () => {
    if (typeof handleClicked === 'function') handleClicked({ id: '', status: false });
    dispatch(getDataProductBrand({ data: [], noPrevious: false }));
    dispatch(
      setTypeActionVideoIndexing({
        indexingType: VIDEO_INDEXING.ALL_BRAND,
        showProduct: true
      })
    );
  };
  const renderAllBrand = () => {
    if (!listProductBrand?.idBrand) {
      if (!isEmpty(data)) {
        return (
          <div className={productBoxClassName}>
            <div className="flex-box">
              {data?.map((item: any) => (
                <div key={item.id} className="xxxlarge-3 large-4 small-6 p-2 size-h-auto">
                  <AllBrandItem
                    data={item}
                    contentData={GET_CONTENT}
                    handleShowAllProductFromBrand={handleShowAllProductFromBrand}
                    dataProductAndBrand={dataProductAndBrand}
                  />
                </div>
              ))}
            </div>
          </div>
        );
      }
      return null;
    } else {
      return renderProductFromBrand();
    }
  };

  const renderProductFromBrand = () => (
    <>
      {!noPrevious && (
        <Button
          className="flex-box align-items-center margin-top-12 font-size-14 text-gray239"
          iconName="vie-chevron-left-r-medium"
          iconClass="size-square-24 margin-right-5"
          title={TEXT.VIDEO_INDEXING.PREVIOUS_PAGE}
          onClick={() => handleClickButtonPreviousPage()}
        />
      )}
      <div className="margin-y-20 xxxlarge-5 large-6 medium-12">
        <AllBrandItem contentData={GET_CONTENT} data={indexingData[0]?.props?.brand} singleBrand />
      </div>
      <div className="margin-bottom-12 text-white text text-medium">
        <span className="font-size-18">{TEXT.VIDEO_INDEXING.BRAND_PRODUCT}</span>
      </div>
      {renderAllProduct()}
    </>
  );

  const renderAllProduct = () =>
    !isEmpty(data) ? (
      <div className={productBoxClassName}>
        <div className="flex-box">
          {data?.map((item: any) => (
            <div key={item.id} className="xxxlarge-3 xlarge-4 large-6 p-2 size-h-auto">
              <InfoBoxItem
                indexingType={
                  isEmpty(listProductBrand?.idBrand) ? VIDEO_INDEXING.PRODUCT : VIDEO_INDEXING.BRAND
                }
                data={item}
                isDisabled={isEnabled && idItemClick !== item.id}
                isActive={isEnabled && idItemClick === item.id}
                handleClicked={handleClicked}
                contentDetail={contentDetail}
              />
            </div>
          ))}
        </div>
      </div>
    ) : (
      isEmpty(itemsInMarks) && (
        <div className="size-h-full flex-box justify-content-center align-items-center">
          <div className="text-center">
            <Image src={ConfigImage.emptyIndexingProduct} className="margin-bottom-12" notWebp />
            <div className="text-white text-center text text-medium">
              {TEXT.VIDEO_INDEXING.PRODUCT_EMPTY_TEXT}
            </div>
            <Button
              className="bg-white margin-top-20 padding-y-5 padding-x-15 font-size-18"
              title={TEXT.VIDEO_INDEXING.SCENE_ALL_PRODUCT}
              textClass="text-medium"
              onClick={() => {
                handleClickButton({
                  showProduct: true,
                  indexingType: VIDEO_INDEXING.ALL_PRODUCT
                });
              }}
            />
          </div>
        </div>
      )
    );

  const renderItem = () => {
    switch (type) {
      case VIDEO_INDEXING.PRODUCT:
        return renderAllProduct();
      case VIDEO_INDEXING.ALL_BRAND:
        return renderAllBrand();
      case VIDEO_INDEXING.ALL_PRODUCT:
        return renderAllProduct();
      case VIDEO_INDEXING.BRAND:
        return renderProductFromBrand();
      default:
        return null;
    }
  };

  // Hooks
  useEffect(() => {
    switch (type) {
      case VIDEO_INDEXING.PRODUCT:
        if (isEmpty(indexingData)) return;
        if (isShowAllProduct) return setData(dataAllProduct);
        return setData(indexingData.slice(0, 8));
      case VIDEO_INDEXING.ALL_BRAND:
        if (!listProductBrand?.idBrand) return setData(dataAllProduct);
        return setData(dataAllProduct);
      case VIDEO_INDEXING.BRAND:
        return setData(indexingData);
      case VIDEO_INDEXING.ALL_PRODUCT:
        return setData(dataAllProduct);
      default:
    }
  }, [isShowAllProduct, type, indexingData, dataAllProduct, listProductBrand?.idBrand]);

  return renderItem();
};

export default IndexingList;
