import React from 'react';

const PlayerNotification = ({ text }: any) => {
  if (!text) return null;

  return (
    <div className="notify player__notify player__notify--appeal absolute size-square-full layer-7 bg-dark-glass-85">
      <div className="notify__inner flex-box align-center align-middle size-square-full relative">
        <div className="notify__content text-center margin-lce-small-up-bottom-8">
          <div
            className="text-white text-medium-down-14 text-large-up-18"
            dangerouslySetInnerHTML={{ __html: text }}
          />
        </div>
      </div>
    </div>
  );
};

export default PlayerNotification;
