import React from 'react';
const CardProgress = React.memo(({ progressPercent, remainText, className }: any) => {
  if (!progressPercent && !remainText) return null;
  return (
    <div className={`time-remain ${className || ''}`}>
      {progressPercent && (
        <div
          className="progress"
          role="progressbar"
          tabIndex={0}
          aria-valuenow={progressPercent}
          aria-valuemin={0}
          aria-valuemax={100}
        >
          <div className="progress-meter" style={{ width: `${progressPercent}%` }} />
        </div>
      )}
      {remainText && <span className="progress-duration">{`Còn ${remainText}`}</span>}
    </div>
  );
});

export default CardProgress;
