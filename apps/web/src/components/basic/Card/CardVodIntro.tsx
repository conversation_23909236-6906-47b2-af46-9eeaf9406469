import React, { useMemo } from 'react';
import VieLink from '@components/VieLink';
import Image from '../Image/Image';
import Button from '../Buttons/Button';
import TagsOutline from '../Tags/TagsOutline';
import CardTags from './CardTags';
import { get } from 'lodash';
import Tags from '../Tags/Tags';
import { useSelector } from 'react-redux';
import { formatTimeTVodString } from '@/services/contentService';
import {
  CONTENT_TYPE,
  EL_SIZE_CLASS,
  EL_THEME_CLASS,
  RIBBON_TYPE,
  TVOD,
  USER_TYPE
} from '@/constants/constants';
import classNames from 'classnames';
import Styles from './Card.module.scss';

const CardVodIntro = (props: any) => {
  const {
    item,
    category,
    isProgressBar,
    thumbClass,
    onClickCard,
    titleCardGroup,
    isRelated,
    isVodDetail
  } = props;
  const {
    title,
    images,
    defaultImgSrc,
    href,
    seo,
    releaseYear,
    altSEOImg,
    people,
    shortDescription,
    runTime,
    tags,
    relatedSeason = [],
    progressPercent,
    labelSubtitleAudio,
    tagData,
    isLiveStream,
    ribbonType,
    isOriginal,
    tvod,
    isEpisode
  } = item || {};
  const isShow = category === 2;
  const { strTimeTag, price, benefitType } = tvod || {};
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const config = useSelector((state: any) => state?.App?.webConfig);
  const expiredString = get(config, 'tVod.text.expiredString', '');
  const remainTimeText = useMemo(() => {
    if (!strTimeTag) return '';
    return formatTimeTVodString({
      strConfig: expiredString,
      strTime: strTimeTag
    });
  }, [strTimeTag, expiredString]);

  const countryTag = (tags || []).find((tag: any) => tag?.type === 'country');
  const castText = (people?.cast || []).map((actor: any) => actor.name).join(', ');
  let seasonText = (relatedSeason || [])?.length <= 1 ? 1 : (relatedSeason || [])?.length;
  seasonText += isShow ? ' Mùa' : ' Phần';

  let dataRunTime: any = 0;
  let dataRunTimeMin: any = 0;
  let dataRunTimeHours: any = 0;
  if (runTime > 0 && runTime < 60) dataRunTime = `${runTime}ph`;
  if (runTime >= 60) {
    const newRunTime = parseInt(runTime) / 60;
    dataRunTimeHours = Math.floor(newRunTime);
    dataRunTimeMin = Math.ceil((newRunTime - dataRunTimeHours) * 60);
    if (dataRunTimeMin === 0) dataRunTime = `${dataRunTimeHours}h`;
    else dataRunTime = `${dataRunTimeHours}h ${dataRunTimeMin}ph`;
  }

  const titleGroupArray = [];
  if (releaseYear > 0) titleGroupArray.push(releaseYear);
  if (countryTag?.name) titleGroupArray.push(countryTag.name);
  if (relatedSeason.length > 0) titleGroupArray.push(seasonText);
  if (relatedSeason.length === 0 && runTime > 0) titleGroupArray.push(dataRunTime);
  if (labelSubtitleAudio) titleGroupArray.push(labelSubtitleAudio);

  const clickCard = (e: any) => {
    if (e) e.preventDefault();
    if (typeof onClickCard === 'function') {
      onClickCard(item);
    }
  };

  const shouldDisplayTag = () => {
    if ((profile?.isPremium || profile?.type === USER_TYPE.VIP) && item?.isSvodTvod) return '';
    const conditionShowTag =
      price &&
      !isEpisode &&
      (benefitType === TVOD.USER_TYPE.RENTED || benefitType === TVOD.USER_TYPE.WATCHED) &&
      !isLiveStream &&
      item?.type !== CONTENT_TYPE.EPG &&
      ribbonType !== RIBBON_TYPE.EPG;

    return conditionShowTag ? (
      <div className={classNames(Styles.tagGroup3)}>
        <Tags
          title={remainTimeText}
          size={isOriginal ? EL_SIZE_CLASS.VARIANT_LARGE : EL_SIZE_CLASS.VARIANT_MEDIUM}
          theme={EL_THEME_CLASS.GREEN}
          subClass="!w-max !max-w-max"
        />
      </div>
    ) : (
      ''
    );
  };
  return (
    <div className="card card--vod card--dir-vertical space-cel-2">
      <div className="card__thumbnail">
        <VieLink href={href || seo?.url || ''} as={seo?.url || ''}>
          <a
            className={thumbClass || 'card__thumbnail-img'}
            title={seo?.titleSeoTag || seo?.title || title}
            tabIndex={-1}
            onClick={clickCard}
          >
            <Image
              className="card__thumb-img"
              defaultSrc={defaultImgSrc}
              src={images?.thumbnail || ''}
              alt={altSEOImg}
            />
          </a>
        </VieLink>
        {remainTimeText && shouldDisplayTag()}
        {item && <CardTags cardData={item} />}
        {isProgressBar && progressPercent > 0 && (
          <div
            className="progress"
            role="progressbar"
            tabIndex={0}
            aria-valuenow={50}
            aria-valuemin={0}
            aria-valuetext="50 percent"
            aria-valuemax={100}
          >
            <div className="progress-meter" style={{ width: `${progressPercent}%` }} />
          </div>
        )}

        <Button
          iconClass="medium"
          iconName="vie-play-solid-rc m-l1"
          className="button button--play button--geometry-circle button--dark-glass absolute"
          onClick={() => onClickCard(item)}
        />
      </div>
      <div className="card__section">
        {title && !titleCardGroup && !isVodDetail && (
          <h4 className="card__title text-white line-clamp" data-line-clamp="2">
            <VieLink href={href || seo?.url || ''} as={seo?.url || ''}>
              <a className="title text-white" title={title}>
                {title}
              </a>
            </VieLink>
          </h4>
        )}
        {title && isVodDetail && (
          <h3 className="card__title text-white line-clamp" data-line-clamp="2">
            <VieLink href={href || seo?.url || ''} as={seo?.url || ''}>
              <a className="title text-white" title={title}>
                {title}
              </a>
            </VieLink>
          </h3>
        )}
        {tagData && (
          <div className="flex flex-wrap space-x-1.5 lg:space-x-2">
            <TagsOutline tagArray={tagData} />
          </div>
        )}
        {runTime > 0 && !isRelated && <div className="card__duration">{dataRunTime}</div>}
        {shortDescription && (
          <p className="card__desc line-clamp" data-line-clamp="3">
            {shortDescription}
          </p>
        )}
        {castText && isShow && (
          <div className="card__artist card__desc">
            Khách mời: <span>{castText}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default CardVodIntro;
