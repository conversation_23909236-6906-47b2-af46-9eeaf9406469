import React from 'react';
import classNames from 'classnames';

const RadioButton = (props: any) => {
  const { style, states, id, name, label, checked } = props;
  return (
    <div className={classNames('radio-v1', style, states)}>
      <input type="radio" id={id} name={name} checked={checked} readOnly />
      <label htmlFor={id}>
        <span className="checkmark" />
        {label && <span className="text">{label}</span>}
      </label>
    </div>
  );
};

RadioButton.propTypes = {
  // style: PropTypes.any,
  // states: PropTypes.any,
  // id: PropTypes.any,
  // checked: PropTypes.bool,
  // name: PropTypes.string,
  // label: PropTypes.string
};

export default RadioButton;
