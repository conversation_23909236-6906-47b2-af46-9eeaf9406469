import React, { forwardRef, useEffect, useRef, useState, useImperativeHandle } from 'react';
import classNames from 'classnames';
import InputItem from './InputItem';
import style from './InputCode.module.scss';

export const InputCode = forwardRef(
  (
    {
      label,
      maxLength = 1,
      isNumberInput,
      type,
      length,
      onChange,
      onEnter,
      onDelete,
      className,
      resetValue,
      ...restProps
    }: any,
    ref: any
  ) => {
    const [values, setValues] = useState(new Array(length).fill(''));
    const inputCodeRef = useRef<any>(null);
    const indexCurrent = useRef<any>(0);
    const allElements = useRef<any>([]);
    // focus with using useRef
    const elements = useRef(new Array(length).fill(0));

    // handle reset all value
    const handleResetValue = () => {
      setValues(new Array(length).fill(''));
      allElements?.current[0]?.focus();
    };
    // handle ref useImperativeHandle hook method
    useImperativeHandle(
      ref,
      () => ({
        resetValue: handleResetValue()
      }),
      []
    );

    const handleOnChange = (event: any, index: any) => {
      const value = event?.target?.value;
      indexCurrent.current = Array.prototype.indexOf.call(allElements.current, event?.target);

      const newData = [...values];
      newData[index] = value;
      setValues(newData);
      // Return/callback values
      if (typeof onChange === 'function') {
        onChange({ event, value: newData.join(''), index: indexCurrent.current });
      }

      // validate value
      const validateValue = value.length > 0 && value.length === maxLength;
      // Focus next input when value valid
      if (validateValue && index < length - 1) {
        elements.current[index + 1].focus();
      }
      // Focus next row, when input focus at end elements
      if (validateValue && index === length - 1) {
        if (indexCurrent.current === allElements.current.length - 1) return;
        allElements.current[indexCurrent.current + 1].focus();
      }
    };

    // handle on enter
    const handleEnter = (event: any) => {
      if (typeof onEnter === 'function') {
        onEnter({ event, values: values.join(''), index: indexCurrent.current });
      }
    };

    // delete char from input
    const handleOnDelete = (event: any, index: any) => {
      indexCurrent.current = Array.prototype.indexOf.call(allElements.current, event?.target);

      if (typeof onDelete === 'function') {
        onDelete({ event, values: values.join(''), index: indexCurrent.current });
      }

      // Focus previous with value is valid
      if (index > 0) {
        elements.current[index - 1].focus();
      }

      // Focus previous row, when input focus at first elements
      if (index === 0) {
        if (indexCurrent.current === 0) return;
        allElements.current[indexCurrent.current - 1].focus();
      }
    };

    // handle error
    useEffect(() => {
      if (!resetValue) return;
      handleResetValue();
    }, [resetValue]);

    // Get All element
    useEffect(() => {
      allElements.current = document?.querySelectorAll('input[maxlength]');
      allElements.current[0].focus();
    }, [length]);

    // Define class name
    const rootClass = classNames(style['code-input-container'], className);

    return (
      <article className={rootClass}>
        {label && (
          <label className="text text-gray239 font-size-16 padding-large-up-top-24 padding-large-up-bottom-12 padding-small-up-top-12 text-center">
            {label}
          </label>
        )}
        <div ref={inputCodeRef} className={style['code-input-group']}>
          {Array.from(Array(length), (e, i) => (
            <InputItem
              key={i}
              ref={(n) => (elements.current[i] = n)}
              value={values[i]}
              onChange={(event: any) => handleOnChange(event, i)}
              onEnter={(event: any) => handleEnter(event)}
              onDelete={(event: any) => handleOnDelete(event, i)}
              maxLength={maxLength}
              isNumberInput={isNumberInput}
              elements={allElements.current}
              resetValue={resetValue}
              type={type}
              {...restProps}
            />
          ))}
        </div>
      </article>
    );
  }
);

export default InputCode;
