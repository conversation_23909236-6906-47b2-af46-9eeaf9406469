import React from 'react';
import classNames from 'classnames';

const Icon = React.memo(
  ({
    unUseBaseClass,
    spClass,
    iClass,
    tagData,
    dataPosition,
    dataAlign,
    imageSrc,
    iStyle,
    spStyle
  }: any) => (
    <span
      className={classNames(!unUseBaseClass ? 'icon' : '', spClass)}
      data-position={dataPosition || ''}
      data-alignment={dataAlign || ''}
      style={spStyle}
    >
      {imageSrc && <img src={imageSrc} alt="VieON" />}
      {iClass && <i className={`vie ${iClass}`} style={iStyle} />}
      {tagData?.label && <span className={tagData?.className}>{tagData.label}</span>}
    </span>
  )
);

export default Icon;
