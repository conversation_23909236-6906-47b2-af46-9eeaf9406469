import React from 'react';

const NCT = ({ size, isHovering, isActive }: any) => {
  if (isHovering || isActive) {
    return (
      <svg
        width={size || 24}
        height={size || 24}
        viewBox="0 0 24 24"
        fill="#31A46F"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2.02016 17.981C1.52491 17.3162 1.22725 16.5857 1.08398 15.7954C1.02963 15.4956 1.0176 15.1854 1.01376 14.8796C1.00428 14.1229 0.98748 13.3652 1.01529 12.6095C1.08188 10.7998 1.25648 9.00781 1.9666 7.31028C2.76197 5.40896 4.01056 3.89784 5.77372 2.82007C6.8861 2.14011 8.09696 1.69547 9.37326 1.49097C10.3578 1.33322 11.3649 1.25785 12.3624 1.25399C13.9475 1.24784 15.472 1.59281 16.95 2.19014C18.3349 2.74987 19.5 3.61688 20.4592 4.71789C21.317 5.70253 21.9212 6.87006 22.3112 8.13414C22.6328 9.17628 22.8271 10.2416 22.8925 11.3267C22.9541 12.3479 23.0071 13.3721 22.9992 14.3944C22.9911 15.4444 22.8983 16.4999 22.3021 17.4127C22.028 17.8326 21.719 18.2611 21.3326 18.5683C20.8698 18.9361 20.5198 19.3631 20.2103 19.8523C19.8559 20.4126 19.2033 20.5557 18.6543 20.2079C18.3206 19.9964 18.1234 19.691 18.1241 19.2754C18.1277 17.335 18.131 15.3946 18.1264 13.4542C18.1248 12.7872 18.0845 12.12 18.0933 11.4533C18.0966 11.202 18.15 10.9354 18.2484 10.7048C18.4453 10.2432 18.8615 10.1632 19.3091 10.2084C19.712 10.249 19.9602 10.4899 20.1043 10.8611C20.2341 11.1952 20.2733 11.5794 20.6158 11.8015C20.8225 11.9356 21.021 12.0824 21.2233 12.2235C21.2452 12.2059 21.2672 12.1882 21.2891 12.1706C21.0178 10.9194 20.7552 9.66621 20.4724 8.41765C20.2448 7.41321 19.6884 6.5809 19.0516 5.79277C18.2185 4.76157 17.1557 4.03163 15.9297 3.58793C15.1224 3.29573 14.2561 3.15464 13.4072 2.99335C12.9286 2.90241 12.4297 2.85928 11.9434 2.8793C10.8033 2.92624 9.67639 3.06696 8.57288 3.39878C7.63456 3.68093 6.79044 4.13446 6.01879 4.70928C5.22694 5.29914 4.62378 6.07128 4.11862 6.92586C3.4868 7.99471 3.25774 9.17916 3.04448 10.3736C2.94123 10.9519 2.82878 11.5286 2.72125 12.1062C2.71326 12.1491 2.7114 12.1932 2.70436 12.2583C3.25843 11.9141 3.73159 11.5572 3.87468 10.8802C3.96327 10.461 4.32579 10.2201 4.70616 10.2056C5.25561 10.1846 5.59332 10.3718 5.77972 10.809C5.81291 10.8868 5.83785 10.9751 5.83848 11.0588C5.8537 13.1038 5.8692 15.1488 5.87493 17.1938C5.87685 17.8768 5.85107 18.5598 5.83978 19.2429C5.83069 19.7924 5.54336 20.1865 5.03587 20.3403C4.54796 20.4883 4.04692 20.3052 3.7539 19.8634C3.6598 19.7214 3.60125 19.5534 3.49545 19.4224C3.33493 19.2235 3.15439 19.0392 2.96966 18.8619C2.66322 18.5678 2.34671 18.2842 2.02016 17.981Z"
          fill="#31A46F"
        />
        <path
          d="M10.1479 14.5592C10.1291 14.657 10.094 14.7547 10.0939 14.8526C10.0908 17.0924 10.0937 19.3323 10.0903 21.5721C10.0891 22.3544 9.40342 22.8456 8.68866 22.6021C8.37737 22.4961 8.04506 22.0086 8.04664 21.6178C8.05939 18.4568 8.05378 15.2958 8.05377 12.1348C8.05377 11.7314 8.04111 11.3274 8.05691 10.9246C8.07727 10.4054 8.3245 10.0372 8.91723 9.92059C9.32853 9.83965 9.74117 10.0618 10.01 10.553C10.027 11.6356 10.0276 12.6735 10.0467 13.7111C10.0519 13.9943 10.1126 14.2765 10.1479 14.5592Z"
          fill="#31A46F"
        />
        <path
          d="M10.1496 14.5696C10.0934 14.2826 10.0519 13.994 10.0467 13.7108C10.0276 12.6732 10.0107 11.6193 10.0041 10.5527C10.4904 11.2338 10.9928 11.9594 11.4775 12.6699C12.0172 13.4611 12.5567 14.2526 13.0944 15.0452C13.2593 15.2882 13.4166 15.5361 13.5803 15.7799C13.6294 15.8531 13.6881 15.9198 13.7425 15.9895C13.771 15.9809 13.7996 15.9722 13.8282 15.9636C13.8282 13.5695 13.8282 11.1755 13.8521 8.79297C13.8769 11.1982 13.8737 13.5919 13.8861 15.9856C13.8871 16.1834 13.9522 16.4078 14.0593 16.5736C14.569 17.3626 15.107 18.1333 15.6264 18.9162C15.7365 19.0822 15.7931 19.2228 15.8839 19.4016C15.8587 19.5786 15.8709 19.8103 15.8224 19.9806C15.6506 20.5831 14.9516 20.676 14.4963 20.5245C14.2217 20.4332 14.0499 20.2502 13.8996 20.0242C13.2348 19.0244 12.5688 18.0254 11.9023 17.0267C11.3766 16.239 10.8503 15.4516 10.3212 14.6663C10.2923 14.6234 10.215 14.6064 10.1496 14.5696Z"
          fill="#31A46F"
        />
        <path
          d="M15.8714 19.4149C15.7772 19.2625 15.7029 19.0727 15.5928 18.9067C15.0734 18.1239 14.5354 17.3532 14.0257 16.5642C13.9186 16.3984 13.7265 16.1843 13.7254 15.9865C13.713 13.5928 13.8433 11.1887 13.8379 8.76852C13.8845 8.17511 14.3824 7.85974 14.9005 7.87039C15.471 7.88213 15.8746 8.32787 15.8753 8.92028C15.8776 11.1109 15.8762 13.3014 15.876 15.492C15.8758 16.7908 15.8752 18.0897 15.8714 19.4149Z"
          fill="#31A46F"
        />
      </svg>
    );
  }
  return (
    <svg
      width={size || 24}
      height={size || 24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.02016 17.981C1.52491 17.3162 1.22725 16.5857 1.08398 15.7954C1.02963 15.4956 1.0176 15.1854 1.01376 14.8796C1.00428 14.1229 0.98748 13.3652 1.01529 12.6095C1.08188 10.7998 1.25648 9.00781 1.9666 7.31028C2.76197 5.40896 4.01056 3.89784 5.77372 2.82007C6.8861 2.14011 8.09696 1.69547 9.37326 1.49097C10.3578 1.33322 11.3649 1.25785 12.3624 1.25399C13.9475 1.24784 15.472 1.59281 16.95 2.19014C18.3349 2.74987 19.5 3.61688 20.4592 4.71789C21.317 5.70253 21.9212 6.87006 22.3112 8.13414C22.6328 9.17628 22.8271 10.2416 22.8925 11.3267C22.9541 12.3479 23.0071 13.3721 22.9992 14.3944C22.9911 15.4444 22.8983 16.4999 22.3021 17.4127C22.028 17.8326 21.719 18.2611 21.3326 18.5683C20.8698 18.9361 20.5198 19.3631 20.2103 19.8523C19.8559 20.4126 19.2033 20.5557 18.6543 20.2079C18.3206 19.9964 18.1234 19.691 18.1241 19.2754C18.1277 17.335 18.131 15.3946 18.1264 13.4542C18.1248 12.7872 18.0845 12.12 18.0933 11.4533C18.0966 11.202 18.15 10.9354 18.2484 10.7048C18.4453 10.2432 18.8615 10.1632 19.3091 10.2084C19.712 10.249 19.9602 10.4899 20.1043 10.8611C20.2341 11.1952 20.2733 11.5794 20.6158 11.8015C20.8225 11.9356 21.021 12.0824 21.2233 12.2235C21.2452 12.2059 21.2672 12.1882 21.2891 12.1706C21.0178 10.9194 20.7552 9.66621 20.4724 8.41765C20.2448 7.41321 19.6884 6.5809 19.0516 5.79277C18.2185 4.76157 17.1557 4.03163 15.9297 3.58793C15.1224 3.29573 14.2561 3.15464 13.4072 2.99335C12.9286 2.90241 12.4297 2.85928 11.9434 2.8793C10.8033 2.92624 9.67639 3.06696 8.57288 3.39878C7.63456 3.68093 6.79044 4.13446 6.01879 4.70928C5.22694 5.29914 4.62378 6.07128 4.11862 6.92586C3.4868 7.99471 3.25774 9.17916 3.04448 10.3736C2.94123 10.9519 2.82878 11.5286 2.72125 12.1062C2.71326 12.1491 2.7114 12.1932 2.70436 12.2583C3.25843 11.9141 3.73159 11.5572 3.87468 10.8802C3.96327 10.461 4.32579 10.2201 4.70616 10.2056C5.25561 10.1846 5.59332 10.3718 5.77972 10.809C5.81291 10.8868 5.83785 10.9751 5.83848 11.0588C5.8537 13.1038 5.8692 15.1488 5.87493 17.1938C5.87685 17.8768 5.85107 18.5598 5.83978 19.2429C5.83069 19.7924 5.54336 20.1865 5.03587 20.3403C4.54796 20.4883 4.04692 20.3052 3.7539 19.8634C3.6598 19.7214 3.60125 19.5534 3.49545 19.4224C3.33493 19.2235 3.15439 19.0392 2.96966 18.8619C2.66322 18.5678 2.34671 18.2842 2.02016 17.981Z"
        fill="#CCCCCC"
      />
      <path
        d="M10.1479 14.5592C10.1291 14.657 10.094 14.7547 10.0939 14.8526C10.0908 17.0924 10.0937 19.3323 10.0903 21.5721C10.0891 22.3544 9.40342 22.8456 8.68866 22.6021C8.37737 22.4961 8.04506 22.0086 8.04664 21.6178C8.05939 18.4568 8.05378 15.2958 8.05377 12.1348C8.05377 11.7314 8.04111 11.3274 8.05691 10.9246C8.07727 10.4054 8.3245 10.0372 8.91723 9.92059C9.32853 9.83965 9.74117 10.0618 10.01 10.553C10.027 11.6356 10.0276 12.6735 10.0467 13.7111C10.0519 13.9943 10.1126 14.2765 10.1479 14.5592Z"
        fill="#CCCCCC"
      />
      <path
        d="M10.1496 14.5696C10.0934 14.2826 10.0519 13.994 10.0467 13.7108C10.0276 12.6732 10.0107 11.6193 10.0041 10.5527C10.4904 11.2338 10.9928 11.9594 11.4775 12.6699C12.0172 13.4611 12.5567 14.2526 13.0944 15.0452C13.2593 15.2882 13.4166 15.5361 13.5803 15.7799C13.6294 15.8531 13.6881 15.9198 13.7425 15.9895C13.771 15.9809 13.7996 15.9722 13.8282 15.9636C13.8282 13.5695 13.8282 11.1755 13.8521 8.79297C13.8769 11.1982 13.8737 13.5919 13.8861 15.9856C13.8871 16.1834 13.9522 16.4078 14.0593 16.5736C14.569 17.3626 15.107 18.1333 15.6264 18.9162C15.7365 19.0822 15.7931 19.2228 15.8839 19.4016C15.8587 19.5786 15.8709 19.8103 15.8224 19.9806C15.6506 20.5831 14.9516 20.676 14.4963 20.5245C14.2217 20.4332 14.0499 20.2502 13.8996 20.0242C13.2348 19.0244 12.5688 18.0254 11.9023 17.0267C11.3766 16.239 10.8503 15.4516 10.3212 14.6663C10.2923 14.6234 10.215 14.6064 10.1496 14.5696Z"
        fill="#646464"
      />
      <path
        d="M15.8714 19.4149C15.7772 19.2625 15.7029 19.0727 15.5928 18.9067C15.0734 18.1239 14.5354 17.3532 14.0257 16.5642C13.9186 16.3984 13.7265 16.1843 13.7254 15.9865C13.713 13.5928 13.8433 11.1887 13.8379 8.76852C13.8845 8.17511 14.3824 7.85974 14.9005 7.87039C15.471 7.88213 15.8746 8.32787 15.8753 8.92028C15.8776 11.1109 15.8762 13.3014 15.876 15.492C15.8758 16.7908 15.8752 18.0897 15.8714 19.4149Z"
        fill="#CCCCCC"
      />
    </svg>
  );
};

export default NCT;
