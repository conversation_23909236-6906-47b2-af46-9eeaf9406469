import React from 'react';

const TelevisionSolid = ({ size, title }: any) => (
  <svg
    width={size || '24'}
    height={size ? 'auto' : '24'}
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>{title}</title>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.2876 0.853553C14.4829 0.658291 14.4829 0.341709 14.2876 0.146447C14.0923 -0.0488157 13.7758 -0.0488157 13.5805 0.146447L10.7807 2.94622H9.89626L7.09649 0.146447C6.90122 -0.0488156 6.58464 -0.0488156 6.38938 0.146447C6.19412 0.341709 6.19412 0.658291 6.38938 0.853553L8.48205 2.94622H1.54908C0.693545 2.94622 0 3.63977 0 4.4953V16.0819C0 16.9374 0.693544 17.631 1.54908 17.631H2.70645L1.95814 18.5664C1.78563 18.782 1.82059 19.0966 2.03623 19.2691C2.25186 19.4416 2.5665 19.4067 2.73901 19.191L3.98708 17.631H15.8918L17.1399 19.191C17.3124 19.4067 17.627 19.4416 17.8427 19.2691C18.0583 19.0966 18.0933 18.782 17.9208 18.5664L17.1724 17.631H18.3296C19.1852 17.631 19.8787 16.9374 19.8787 16.0819V4.4953C19.8787 3.63976 19.1851 2.94622 18.3296 2.94622H12.1949L14.2876 0.853553ZM1.5 4.4953C1.5 4.46819 1.52197 4.44622 1.54908 4.44622H14.1849V16.131H1.54908C1.52197 16.131 1.5 16.109 1.5 16.0819V4.4953ZM17 6.50012C17.5522 6.50012 18 6.94784 18 7.50012C18 8.05241 17.5522 8.50012 17 8.50012C16.4477 8.50012 16 8.05241 16 7.50012C16 6.94784 16.4477 6.50012 17 6.50012ZM18 11.5001C18 10.9478 17.5522 10.5001 17 10.5001C16.4477 10.5001 16 10.9478 16 11.5001C16 12.0524 16.4477 12.5001 17 12.5001C17.5522 12.5001 18 12.0524 18 11.5001ZM17 14.5001C17.5522 14.5001 18 14.9478 18 15.5001C18 16.0524 17.5522 16.5001 17 16.5001C16.4477 16.5001 16 16.0524 16 15.5001C16 14.9478 16.4477 14.5001 17 14.5001ZM3.6167 6.25C3.33136 6.25 3.10004 6.48132 3.10004 6.76667V13.4833C3.10004 13.7687 3.33136 14 3.6167 14H11.8833C12.1687 14 12.4 13.7687 12.4 13.4833V6.76667C12.4 6.48132 12.1687 6.25 11.8833 6.25H3.6167Z"
      fill="currentColor"
    />
  </svg>
);

export default TelevisionSolid;
