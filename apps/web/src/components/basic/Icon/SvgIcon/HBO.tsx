import React from 'react';

const HBO = ({ title, size, isActive }: any) => {
  if (isActive) {
    return (
      <svg
        width={size || 20}
        height={size ? 'auto' : 19}
        viewBox="0 0 20 19"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title>{title}</title>
        <g clipPath="url(#clip0_72_1739)">
          <path
            d="M13.793 15.9289C12.5768 15.9289 11.5896 14.9417 11.5896 13.7254C11.5896 12.5086 12.5768 11.5227 13.793 11.5227C15.0098 11.5227 15.9952 12.5086 15.9952 13.7254C15.9952 14.9417 15.0098 15.9289 13.793 15.9289ZM13.7766 9.31262C11.3483 9.31262 9.38019 11.2814 9.38019 13.7091C9.38019 16.1362 11.3483 18.1043 13.7766 18.1043C16.2037 18.1043 18.1718 16.1362 18.1718 13.7091C18.1718 11.2814 16.2037 9.31262 13.7766 9.31262Z"
            fill="currentColor"
          />
          <path
            d="M13.7765 11.9807C12.822 11.9807 12.0477 12.7537 12.0477 13.7089C12.0477 14.6628 12.822 15.4371 13.7765 15.4371C14.7311 15.4371 15.5047 14.6628 15.5047 13.7089C15.5047 12.7537 14.7311 11.9807 13.7765 11.9807Z"
            fill="#4279BB"
          />
          <path
            d="M4.8086 14.9014H7.28225C6.73017 16.4918 5.2256 16.2269 5.2256 16.2269C4.01006 16.2047 3.4397 15.2108 3.4397 15.2108C2.42284 13.4426 3.4397 12.1406 3.4397 12.1406C3.90322 11.3231 5.11802 11.2345 5.11802 11.2345C6.59776 11.2345 7.10585 12.3392 7.10585 12.3392H9.40241C8.82751 10.5822 7.17532 9.31238 5.2256 9.31238C2.79856 9.31238 0.831055 11.2811 0.831055 13.7088C0.831055 16.1359 2.79856 18.1046 5.2256 18.1046C7.65454 18.1046 9.62197 16.1359 9.62197 13.7088C9.62197 13.5442 9.61222 13.3823 9.59449 13.223H4.8086V14.9014Z"
            fill="currentColor"
          />
          <path
            d="M15.8984 5.71676C16.7893 5.71676 17.5094 4.99468 17.5094 4.10445C17.5094 3.21355 16.7893 2.49148 15.8984 2.49148C15.0081 2.49148 14.2861 3.21355 14.2861 4.10445C14.2861 4.99468 15.0081 5.71676 15.8984 5.71676ZM13.847 4.10445C13.847 2.97042 14.7662 2.05312 15.8984 2.05312C17.0312 2.05312 17.9503 2.97042 17.9503 4.10445C17.9503 5.23662 17.0312 6.1551 15.8984 6.1551C14.7662 6.1551 13.847 5.23662 13.847 4.10445ZM10.9914 4.10445C11.2444 4.07124 11.6674 3.78208 11.8167 3.59608C11.7644 3.82632 11.7608 4.49126 11.8216 4.72026C11.6515 4.45867 11.2488 4.13636 10.9914 4.10439V4.10445ZM9.51668 1.93273C9.839 1.93273 10.0926 2.25083 10.0926 2.61366C10.0926 2.97531 9.839 3.29459 9.51668 3.29459H8.40842V1.93273H9.51668ZM9.51235 4.95787C9.83535 4.95787 10.0889 5.27591 10.0889 5.63942C10.0889 6.00163 9.83535 6.31964 9.51235 6.31964H8.40476V4.95781L9.51235 4.95787ZM15.8972 8.23709C18.1547 8.23648 19.9917 6.36019 19.9912 4.10321C19.9912 1.7971 18.1547 0.00612475 15.8971 0.00488559C13.6414 0.00240724 12.4896 1.66203 12.2053 2.33249C12.2078 1.3274 11.2168 0.139956 10.057 0.138097H6.32144V8.19598L9.80399 8.1972C11.2088 8.1972 12.2078 6.98097 12.209 5.93776C12.5307 6.59416 13.6414 8.23837 15.8971 8.23709H15.8972ZM5.86537 8.19903H3.67583V5.07262H2.25449V8.19903H0.00731114L0 0.139336H2.25455V3.13802H3.67589V0.139336H5.86532L5.86537 8.19903Z"
            fill="currentColor"
          />
        </g>
        <defs>
          <clipPath id="clip0_72_1739">
            <rect width="20" height="18.1163" fill="white" />
          </clipPath>
        </defs>
      </svg>
    );
  }
  return (
    <svg
      width={size || 20}
      height={size ? 'auto' : 19}
      viewBox="0 0 20 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <title>{title}</title>
      <path
        d="M13.7988 15.9309C12.582 15.9309 11.5943 14.9432 11.5943 13.7265C11.5943 12.5091 12.582 11.5227 13.7988 11.5227C15.0161 11.5227 16.0019 12.5091 16.0019 13.7265C16.0019 14.9432 15.0161 15.9309 13.7988 15.9309ZM13.7823 9.31173C11.3529 9.31173 9.38394 11.2814 9.38394 13.7101C9.38394 16.1382 11.3529 18.1072 13.7823 18.1072C16.2105 18.1072 18.1794 16.1382 18.1794 13.7101C18.1794 11.2814 16.2105 9.31173 13.7823 9.31173Z"
        fill="currentColor"
      />
      <path
        d="M13.7822 11.981C12.8273 11.981 12.0527 12.7543 12.0527 13.7099C12.0527 14.6642 12.8273 15.4388 13.7822 15.4388C14.7373 15.4388 15.5111 14.6642 15.5111 13.7099C15.5111 12.7543 14.7372 11.981 13.7822 11.981Z"
        fill="currentColor"
      />
      <path
        d="M4.81035 14.903H7.28506C6.73279 16.4941 5.22753 16.2292 5.22753 16.2292C4.01146 16.2069 3.44085 15.2125 3.44085 15.2125C2.42354 13.4436 3.44085 12.141 3.44085 12.141C3.90457 11.3231 5.11992 11.2346 5.11992 11.2346C6.60028 11.2346 7.1086 12.3398 7.1086 12.3398H9.40619C8.83102 10.5819 7.1781 9.31152 5.22753 9.31152C2.79942 9.31152 0.831055 11.2811 0.831055 13.7099C0.831055 16.138 2.79942 18.1077 5.22753 18.1077C7.65757 18.1077 9.62587 16.138 9.62587 13.7099C9.62587 13.5452 9.61607 13.3833 9.59834 13.2239H4.81035V14.903Z"
        fill="currentColor"
      />
      <path
        d="M15.9054 5.71438C16.7967 5.71438 17.5171 4.99199 17.5171 4.10138C17.5171 3.21008 16.7967 2.48769 15.9054 2.48769C15.0147 2.48769 14.2924 3.21008 14.2924 4.10138C14.2924 4.99199 15.0147 5.71438 15.9054 5.71438ZM13.8531 4.10138C13.8531 2.96684 14.7727 2.04914 15.9054 2.04914C17.0387 2.04914 17.9582 2.96684 17.9582 4.10138C17.9582 5.23405 17.0387 6.15293 15.9054 6.15293C14.7727 6.15293 13.8531 5.23405 13.8531 4.10138ZM10.9963 4.10138C11.2494 4.06815 11.6725 3.77886 11.8219 3.59278C11.7696 3.82312 11.766 4.48835 11.8268 4.71745C11.6566 4.45575 11.2537 4.1333 10.9963 4.10131V4.10138ZM9.52088 1.9287C9.84333 1.9287 10.097 2.24694 10.097 2.60993C10.097 2.97174 9.84333 3.29115 9.52088 3.29115H8.41213V1.9287H9.52088ZM9.51654 4.95517C9.83967 4.95517 10.0933 5.27335 10.0933 5.63702C10.0933 5.99939 9.83967 6.31756 9.51654 6.31756H8.40847V4.95511L9.51654 4.95517ZM15.9042 8.23585C18.1627 8.23523 20.0006 6.3581 20 4.10014C20 1.79301 18.1627 0.0012425 15.9041 2.77391e-06C13.6474 -0.00247667 12.4951 1.65788 12.2106 2.32863C12.2131 1.3231 11.2217 0.135132 10.0614 0.133273H6.32425V8.19469L9.80831 8.19593C11.2137 8.19593 12.2131 6.97914 12.2144 5.93548C12.5362 6.59216 13.6474 8.23709 15.9041 8.23585H15.9042ZM5.86798 8.19779H3.67745V5.06997H2.25549V8.19779H0.00731436L0 0.134513H2.25555V3.13452H3.67751V0.134513H5.86791V8.19779H5.86798Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default HBO;
