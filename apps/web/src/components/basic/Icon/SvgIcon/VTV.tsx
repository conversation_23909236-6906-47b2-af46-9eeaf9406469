import React from 'react';

const VTV = ({ isHovering, isActive }: any) => {
  if (isActive) {
    return (
      <svg
        width="24"
        height="10"
        viewBox="0 0 24 10"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_35195_61040)">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M17.009 9.05451L14.5292 4.72057V9.16899C14.5292 9.57777 14.1734 9.91262 13.7687 9.91262H10.613C10.147 9.91262 9.84119 9.54235 9.84119 9.09205V4.15892L7.03124 9.05451C6.7715 9.54761 6.3153 9.93948 5.73047 9.91632C5.21296 9.89583 4.80188 9.55326 4.56285 9.1132L0.108885 1.18699L0.107066 1.18371C-0.190839 0.625147 0.165491 0.101886 0.773902 0.0846417H3.58002C3.82229 0.0846417 4.05164 0.1278 4.23572 0.296797C4.38894 0.437412 4.46539 0.627972 4.576 0.799145L4.58373 0.810933L5.73878 2.96266C5.75788 2.99689 5.78773 3.05226 5.8204 3.10831C5.853 3.05096 5.88389 2.99341 5.90721 2.95438L7.07752 0.766248L7.07973 0.762546C7.16849 0.606214 7.24432 0.451636 7.37702 0.32583C7.60497 0.10955 7.90177 0.0720416 8.2028 0.0847066C10.9178 0.0847066 12.8739 0.0879865 15.6504 0.0879865C15.9895 0.0879865 16.3294 0.123968 16.5819 0.37623C16.6924 0.486546 16.7559 0.612807 16.8461 0.735982L16.8505 0.74186L16.8548 0.747965C17.3494 1.46942 17.7828 2.22815 18.2808 2.94918L19.4702 0.808368L19.4766 0.798788C19.5878 0.62807 19.6645 0.437737 19.8171 0.297544C19.9637 0.16323 20.1396 0.106757 20.3348 0.0899675L20.3496 0.0888633L23.1979 0.0986706C23.5627 0.098898 23.9205 0.324985 23.9894 0.700001C24.0181 0.85656 23.9876 1.01572 23.9078 1.15286L19.3606 9.31363L19.3475 9.33152C19.1 9.6651 18.7379 9.89943 18.3151 9.91632C17.7292 9.93983 17.2704 9.54897 17.009 9.05451Z"
            fill="white"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M0.40798 0.996305L4.86146 8.92131C5.31054 9.75149 6.25135 9.78578 6.73271 8.86354L11.3118 0.886411C11.4802 0.602097 11.3301 0.394975 11.0825 0.398255H8.19626C7.62721 0.373607 7.56167 0.577027 7.37639 0.903655L6.20341 3.09636C6.13992 3.19817 5.96504 3.56468 5.81727 3.56601C5.67947 3.55575 5.50274 3.20954 5.44064 3.10052L4.29039 0.957303C4.06543 0.609761 4.06351 0.398287 3.58036 0.398287H0.783763C0.460949 0.407413 0.214973 0.634572 0.40798 0.996305Z"
            fill="#D92328"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M19.0737 9.10162C18.5918 9.75098 17.7537 9.71344 17.3076 8.86362L14.5869 4.10864C14.4707 3.89934 14.4628 3.68196 14.7883 3.68196C15.6162 3.68196 16.8933 3.6802 17.7214 3.68196C17.9508 3.68218 18.0275 3.65361 18.1704 3.56706C18.2772 3.50208 18.3618 3.41183 18.4355 3.31087L18.5731 3.09764L19.7621 0.957252C19.9692 0.638937 19.9882 0.434413 20.3632 0.402588L23.1971 0.412168C23.5411 0.412168 23.7607 0.706289 23.612 0.956375L19.0737 9.10162Z"
            fill="#2E3383"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10.2559 3.96164V9.16515C10.2559 9.36422 10.3624 9.52007 10.5644 9.52007H13.8282C13.9771 9.52007 14.1253 9.39299 14.1253 9.2427V3.73906C14.1253 3.50664 14.2038 3.37495 14.5435 3.37366L17.3543 3.3756C17.6251 3.3756 18.0398 3.36382 17.8781 3.02131C17.8373 2.93606 16.6609 1.08784 16.5038 0.860424C16.2842 0.5639 16.3086 0.400391 15.6715 0.400391C15.6715 0.400391 12.3935 0.402144 12.378 0.403865C11.9493 0.421759 11.9376 0.627127 11.719 0.955055L10.5927 2.86673C10.3594 3.28445 10.2702 3.26659 10.2559 3.96164Z"
            fill="#0C9648"
          />
        </g>
        <defs>
          <clipPath id="clip0_35195_61040">
            <rect width="24" height="9.83486" fill="white" transform="translate(0 0.0825195)" />
          </clipPath>
        </defs>
      </svg>
    );
  }
  if (isHovering) {
    return (
      <svg
        width="24"
        height="10"
        viewBox="0 0 24 10"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_35195_61018)">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M0.40798 0.996305L4.86146 8.92131C5.31054 9.75149 6.25135 9.78578 6.73271 8.86354L11.3118 0.886411C11.4802 0.602097 11.3301 0.394975 11.0825 0.398255H8.19626C7.62721 0.373607 7.56167 0.577027 7.37639 0.903655L6.20341 3.09636C6.13992 3.19817 5.96504 3.56468 5.81727 3.56601C5.67947 3.55575 5.50274 3.20954 5.44064 3.10052L4.29039 0.957303C4.06543 0.609761 4.06351 0.398287 3.58036 0.398287H0.783763C0.460949 0.407413 0.214973 0.634572 0.40798 0.996305Z"
            fill="#454545"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M19.0737 9.10162C18.5918 9.75098 17.7537 9.71344 17.3076 8.86362L14.5869 4.10864C14.4707 3.89934 14.4628 3.68196 14.7883 3.68196C15.6162 3.68196 16.8933 3.6802 17.7214 3.68196C17.9508 3.68218 18.0275 3.65361 18.1704 3.56706C18.2772 3.50208 18.3618 3.41183 18.4355 3.31087L18.5731 3.09764L19.7621 0.957252C19.9692 0.638937 19.9882 0.434413 20.3632 0.402588L23.1971 0.412168C23.5411 0.412168 23.7607 0.706289 23.612 0.956375L19.0737 9.10162Z"
            fill="#454545"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10.2559 3.96164V9.16515C10.2559 9.36422 10.3624 9.52007 10.5644 9.52007H13.8282C13.9771 9.52007 14.1253 9.39299 14.1253 9.2427V3.73906C14.1253 3.50664 14.2038 3.37495 14.5435 3.37366L17.3543 3.3756C17.6251 3.3756 18.0398 3.36382 17.8781 3.02131C17.8373 2.93606 16.6609 1.08784 16.5038 0.860424C16.2842 0.5639 16.3086 0.400391 15.6715 0.400391C15.6715 0.400391 12.3935 0.402144 12.378 0.403865C11.9493 0.421759 11.9376 0.627127 11.719 0.955055L10.5927 2.86673C10.3594 3.28445 10.2702 3.26659 10.2559 3.96164Z"
            fill="#646464"
          />
        </g>
        <defs>
          <clipPath id="clip0_35195_61018">
            <rect width="24" height="9.83486" fill="white" transform="translate(0 0.0825195)" />
          </clipPath>
        </defs>
      </svg>
    );
  }
  return (
    <svg width="24" height="10" viewBox="0 0 24 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_35195_61029)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0.40798 0.996305L4.86146 8.92131C5.31054 9.75149 6.25135 9.78578 6.73271 8.86354L11.3118 0.886411C11.4802 0.602097 11.3301 0.394975 11.0825 0.398255H8.19626C7.62721 0.373607 7.56167 0.577027 7.37639 0.903655L6.20341 3.09636C6.13992 3.19817 5.96504 3.56468 5.81727 3.56601C5.67947 3.55575 5.50274 3.20954 5.44064 3.10052L4.29039 0.957303C4.06543 0.609761 4.06351 0.398287 3.58036 0.398287H0.783763C0.460949 0.407413 0.214973 0.634572 0.40798 0.996305Z"
          fill="#646464"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M19.0737 9.10162C18.5918 9.75098 17.7537 9.71344 17.3076 8.86362L14.5869 4.10864C14.4707 3.89934 14.4628 3.68196 14.7883 3.68196C15.6162 3.68196 16.8933 3.6802 17.7214 3.68196C17.9508 3.68218 18.0275 3.65361 18.1704 3.56706C18.2772 3.50208 18.3618 3.41183 18.4355 3.31087L18.5731 3.09764L19.7621 0.957252C19.9692 0.638937 19.9882 0.434413 20.3632 0.402588L23.1971 0.412168C23.5411 0.412168 23.7607 0.706289 23.612 0.956375L19.0737 9.10162Z"
          fill="#646464"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.2559 3.96164V9.16515C10.2559 9.36422 10.3624 9.52007 10.5644 9.52007H13.8282C13.9771 9.52007 14.1253 9.39299 14.1253 9.2427V3.73906C14.1253 3.50664 14.2038 3.37495 14.5435 3.37366L17.3543 3.3756C17.6251 3.3756 18.0398 3.36382 17.8781 3.02131C17.8373 2.93606 16.6609 1.08784 16.5038 0.860424C16.2842 0.5639 16.3086 0.400391 15.6715 0.400391C15.6715 0.400391 12.3935 0.402144 12.378 0.403865C11.9493 0.421759 11.9376 0.627127 11.719 0.955055L10.5927 2.86673C10.3594 3.28445 10.2702 3.26659 10.2559 3.96164Z"
          fill="#9B9B9B"
        />
      </g>
      <defs>
        <clipPath id="clip0_35195_61029">
          <rect width="24" height="9.83486" fill="white" transform="translate(0 0.0825195)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default VTV;
