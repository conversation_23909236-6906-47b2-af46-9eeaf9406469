import React from 'react';

const Vip = ({ isHovering, isActive }: any) => {
  if (isActive) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.10637 5.27792H6.46417C6.20836 5.27792 6 5.0655 6 4.80235V3.90404C6 3.64195 6.20836 3.42847 6.46417 3.42847H10.6293C10.8862 3.42847 11.0935 3.64195 11.0935 3.90404V4.80235C11.0935 5.0655 10.8851 5.27792 10.6293 5.27792H9.61538H9.55081L11.8346 15.6729L14.1335 5.27792H14.0545H12.9848C12.728 5.27792 12.5206 5.06444 12.5206 4.80235V3.90404C12.5206 3.64089 12.729 3.42847 12.9848 3.42847H17.15C17.4068 3.42847 17.6142 3.64195 17.6142 3.90404V4.80235C17.6142 5.0655 17.4058 5.27792 17.15 5.27792H16.208L12.6752 20.5713H10.8959L7.10637 5.27792Z"
          fill="#DA9E1C"
        />
      </svg>
    );
  }
  if (isHovering) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.10637 5.27792H6.46417C6.20836 5.27792 6 5.0655 6 4.80235V3.90404C6 3.64195 6.20836 3.42847 6.46417 3.42847H10.6293C10.8862 3.42847 11.0935 3.64195 11.0935 3.90404V4.80235C11.0935 5.0655 10.8851 5.27792 10.6293 5.27792H9.61538H9.55081L11.8346 15.6729L14.1335 5.27792H14.0545H12.9848C12.728 5.27792 12.5206 5.06444 12.5206 4.80235V3.90404C12.5206 3.64089 12.729 3.42847 12.9848 3.42847H17.15C17.4068 3.42847 17.6142 3.64195 17.6142 3.90404V4.80235C17.6142 5.0655 17.4058 5.27792 17.15 5.27792H16.208L12.6752 20.5713H10.8959L7.10637 5.27792Z"
          fill="#9B9B9B"
        />
      </svg>
    );
  }
  return (
    <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.10637 5.27792H6.46417C6.20836 5.27792 6 5.0655 6 4.80235V3.90404C6 3.64195 6.20836 3.42847 6.46417 3.42847H10.6293C10.8862 3.42847 11.0935 3.64195 11.0935 3.90404V4.80235C11.0935 5.0655 10.8851 5.27792 10.6293 5.27792H9.61538H9.55081L11.8346 15.6729L14.1335 5.27792H14.0545H12.9848C12.728 5.27792 12.5206 5.06444 12.5206 4.80235V3.90404C12.5206 3.64089 12.729 3.42847 12.9848 3.42847H17.15C17.4068 3.42847 17.6142 3.64195 17.6142 3.90404V4.80235C17.6142 5.0655 17.4058 5.27792 17.15 5.27792H16.208L12.6752 20.5713H10.8959L7.10637 5.27792Z"
        fill="#CCCCCC"
      />
    </svg>
  );
};

export default Vip;
