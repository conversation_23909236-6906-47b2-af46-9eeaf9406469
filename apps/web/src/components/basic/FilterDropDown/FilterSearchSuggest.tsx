import React, { useState, useEffect } from 'react';
import { getSearchSuggest } from '@actions/search';
import { useDispatch, useSelector } from 'react-redux';
const FilterSearchSuggest = React.memo(({ dataSuggest, keyword, handleChangeContent }: any) => {
  const dispatch = useDispatch();
  const [dataState, setDataState] = useState(dataSuggest);
  const [activeName, setActiveName] = useState<any>('');
  const searchFocused = useSelector((state: any) => state?.Search?.FOCUS_SEARCHBOX);

  useEffect(() => {
    if (keyword !== '') {
      if (dataSuggest?.keyword) {
        setDataState(dataSuggest?.keyword);
      } else {
        handleGetSearchSuggest();
      }
    }
  }, [keyword]);

  useEffect(() => {
    if (searchFocused?.focused && activeName !== '') {
      setActiveName('');
      if (typeof handleChangeContent === 'function') handleChangeContent({ keyword: '' });
    }
  }, [searchFocused]);

  const handleGetSearchSuggest = async () => {
    await dispatch(getSearchSuggest({ keyword }) as unknown as Promise<any>).then((res: any) => {
      if (res?.payload?.data) {
        setDataState(res?.payload?.data);
      }
    });
  };

  const onClickItem = (keyword: any, i: any) => {
    setActiveName(keyword);
    return handleChangeContent && handleChangeContent({ keyword, index: i });
  };

  if (!dataState?.items?.length) return null;

  return (
    <div className="filter filter--key">
      <label>Có thể bạn cũng đang tìm: </label>
      <div className="container">
        {(dataState?.items || []).map((item: any, i: any) => (
          <a
            key={i}
            className={`filter__item ${item === activeName ? 'active' : ''}`}
            title={item}
            onClick={() => onClickItem(item, i)}
          >
            {item}
          </a>
        ))}
      </div>
    </div>
  );
});
export default FilterSearchSuggest;
