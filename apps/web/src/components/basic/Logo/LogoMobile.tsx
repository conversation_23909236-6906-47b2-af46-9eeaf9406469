import { TEXT } from '@constants/text';
import React from 'react';

const LogoMobile = React.memo((props: any) => {
  const { className, title, onClick } = props || {};
  return (
    <button
      className={className || 'button button--nav-mobile'}
      onClick={onClick}
      title={title || TEXT.SLOGAN}
      type="button"
    >
      <i className="vie vie-list-ul" />
    </button>
  );
});

export default LogoMobile;
