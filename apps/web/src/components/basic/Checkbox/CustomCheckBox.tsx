import React from 'react';

const CustomCheckBox = ({ id, label, checked, onChecked }: any) => {
  const onChange = (e: any) => {
    if (onChecked) onChecked(e?.target?.checked);
  };

  return (
    <div className="auto-payment">
      <label className="checkbox-custom absolute left bottom" htmlFor={id || 'profileAutoPay'}>
        <input type="checkbox" id={id || 'profileAutoPay'} checked={checked} onChange={onChange} />
        <span className="checkmark text-white" />
        <span className="text-white">{label || ''}</span>
      </label>
    </div>
  );
};

export default CustomCheckBox;
