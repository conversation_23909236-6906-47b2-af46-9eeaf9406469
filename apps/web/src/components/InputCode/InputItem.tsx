import React, { forwardRef, useEffect, useState, useId } from 'react';
import classNames from 'classnames';
import Style from './InputCode.module.scss';

const InputItem = forwardRef(
  (
    {
      secureMode,
      onDelete,
      value: valueProp,
      onEnter,
      onChange,
      isNumberInput,
      type: typeProp,
      elements,
      className,
      resetValue,
      ...restProps
    }: any,
    ref: any
  ) => {
    const id = useId();
    const regexNumber = /[0-9]/; // only number
    const [type, setType] = useState(typeProp);
    const [value, setValue] = useState(valueProp);

    // handle Arrow key Down
    const handleKeyArrowDown = (event: any) => {
      event.preventDefault();
      const i = Array.prototype.indexOf.call(elements, event?.target);
      const index = i === elements.length - 1 ? 0 : i + 1;
      elements[index].focus();
    };

    // handle Arrow key up
    const handleKeyArrowUp = (event: any) => {
      event.preventDefault();
      const i = Array.prototype.indexOf.call(elements, event?.target);
      const index = i === 0 ? elements.length - 1 : i - 1;
      elements[index].focus();
    };

    const handleKeyUp = (event: any) => {
      switch (event.keyCode) {
        case 8: {
          // backspace key
          if (!event.target.value) {
            onDelete(event);
            setType('text');
          }
          break;
        }
        // tab key
        case 9: {
          event.preventDefault();
          break;
        }
        // Enter
        case 13: {
          event.preventDefault();
          onEnter(event);
          break;
        }
        // Arrow Up / right
        case 37:
        case 38: {
          handleKeyArrowUp(event);
          break;
        }
        // Arrow Down/ left
        case 39:
        case 40: {
          handleKeyArrowDown(event);
          break;
        }
        default:
          break;
      }
    };
    const handleChangeValue = (event: any) => {
      onChange(event);
      if (valueProp) {
        setValue(event?.target?.value);
      }
      if (secureMode) {
        const timer = setTimeout(() => {
          setType('password');
        }, 300);
        return () => clearTimeout(timer);
      }
      return;
    };

    // handle trigger number
    const handleOnKeyPress = (event: any) => {
      if (isNumberInput && !regexNumber.test(event.key)) {
        event.preventDefault();
      }
    };
    // controlled/uncontrolled
    useEffect(() => {
      setValue(valueProp);
      setType(typeProp);
    }, [valueProp, typeProp]);

    useEffect(() => {
      if (!resetValue) return;
      setType('text');
    }, [resetValue]);

    useEffect(() => {
      const input: any = document.getElementById(id);
      if (input.value.length > input.maxLength) {
        setValue(input.value.slice(0, input.maxLength));
      }
    }, [value]);

    // Define class name
    // const rootClass = classNames(Style.codeInput, className, error ? Style.codeInputError : '');
    const rootClass = classNames(Style.codeInput, className);

    return (
      <input
        className={rootClass}
        ref={ref}
        id={id}
        value={value}
        onKeyPress={handleOnKeyPress}
        type={type}
        onKeyUp={handleKeyUp}
        onChange={handleChangeValue}
        pattern={isNumberInput ? '[0-9]*' : undefined}
        inputMode={isNumberInput ? 'numeric' : undefined}
        onFocus={(e) => e.target.select()}
        autoComplete="new-password"
        {...restProps}
      />
    );
  }
);

export default InputItem;
