import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { isMobile } from 'react-device-detect';
import { useVieRouter } from '@customHook';
import PageApi from '@apis/cm/PageApi';
import { EL_ID, ERROR_CODE, PAGE } from '@constants/constants';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import { createTimeout, encodeParamDestination } from '@helpers/common';
import EmptyCommentBlocked from '@components/empty/EmptyCommentBlocked';
import StyleBlocked from '@components/empty/Empty.module.scss';
import BannerLivestreamAds from '@components/OutstreamAds/BannerLivestreamAds';
import { convertToDateTime } from '@helpers/utils';
import isEmpty from 'lodash/isEmpty';
import CommentArea from '../detail/comment/CommentArea';
import styles from './Styles.module.scss';
import Image from '../basic/Image/Image';
import Button from '../basic/Buttons/Button';

const RETRY_TIME = 5000; // 5s
let timeout: any = null;
let dataCommentDefault: any = [];
let commentListTimer: any = null;

const LiveComment = ({ containerHeight, playerRef, dataEventDetails }: any) => {
  let nextRequest = null;
  let reTryTimeOut: any = null;
  const ref = useRef<any>(null);
  const commentLiveRef = useRef<any>(null);
  const pinnedRef = useRef<any>(null);
  const wrapperRef = useRef<any>(null);
  const router = useVieRouter();
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const { bannerLivestream } = useSelector((state: any) => state.App?.outStreamAds || {});
  const { bannerChat } = useSelector((state: any) => state.App?.webConfig?.live || {});
  const { eventsData } = useSelector((state: any) => state?.Page) || {};
  const isShowChatBanner = Boolean(eventsData?.isBanner);
  const [adsLoadSuccess, setAdsLoadSuccess] = useState(false);
  const [state, setState] = useState({
    dataComment: [],
    dataEvents: null,
    dataPined: null,
    scrolled: false,
    blocked: false
  });
  const playerStage = playerRef?.current?.firstChild || null;
  const { dataComment, dataPined }: any = state;
  const containerEl = ref?.current;
  const commentListEL = wrapperRef?.current;
  const pinnedEl = pinnedRef?.current;
  const [showBanner, setShowBanner] = useState(true);

  useEffect(() => {
    if (dataEventDetails) {
      getLiveComment(dataEventDetails);
      setState((prevState) => ({
        ...prevState,
        dataComment: dataCommentDefault,
        dataEvents: dataEventDetails
      }));
    }
    return () => {
      clearTimeout(timeout);
      clearTimeout(reTryTimeOut);
      dataCommentDefault = [];
      clearTimeout(commentListTimer);
    };
  }, [dataEventDetails]);

  useEffect(() => {
    if (!ref?.current) return;
    const isLiftItems =
      ref.current.scrollTop + ref.current.clientHeight + 200 >= ref.current.scrollHeight;
    if (isLiftItems) liftUpContent();
  }, [state]);

  useEffect(() => {
    if (state?.blocked && !isMobile) {
      const video = document.getElementById(EL_ID.PLAYER_STAGE);
      const commentLiveRefEl = commentLiveRef.current;
      if (video && commentLiveRefEl && commentLiveRefEl?.style) {
        commentLiveRefEl.style.height = `${video.clientHeight}px`;
      }
    }
  }, [state?.blocked, isMobile]);

  useLayoutEffect(() => {
    const pinnedHeight = pinnedEl?.clientHeight ? pinnedEl?.clientHeight + 24 : 0;
    if (containerEl) {
      containerEl.style.paddingBottom = `${pinnedHeight}px`;
    }
  }, [dataPined]);

  useLayoutEffect(() => {
    if (commentListEL) {
      if (adsLoadSuccess) {
        if (commentListEL.classList.contains('overlay')) {
          commentListEL.classList.remove('overlay');
        }
      } else if (!commentListEL.classList.contains('overlay')) {
        commentListEL.classList.add('overlay');
      }
    }
  }, [adsLoadSuccess, commentListEL]);

  useLayoutEffect(() => {
    const pinnedHeight = pinnedEl?.clientHeight ? pinnedEl?.clientHeight + 24 : 0;
    if (commentListEL) {
      if (!isMobile) {
        commentListEL.style.height = `${playerStage?.clientHeight - pinnedHeight}px`;
        commentListEL.style.maxHeight = `${playerStage?.clientHeight - pinnedHeight}px`;
      }
      clearTimeout(commentListTimer);
      commentListTimer = createTimeout(() => {
        commentListEL.scrollTop = commentListEL.scrollHeight;
      }, 1000);
    }
  }, [playerStage?.clientHeight, dataPined]);

  useLayoutEffect(() => {
    const commentListEL = wrapperRef?.current;
    if (commentListEL && (!dataComment?.length || state.blocked)) {
      const video = document.getElementById(EL_ID.PLAYER_STAGE);
      if (video) {
        const pinnedHeight = pinnedEl?.clientHeight ? pinnedEl?.clientHeight + 24 : 0;
        const calculatedHeight = video.clientHeight - pinnedHeight;
        commentListEL.style.height = `${calculatedHeight}px`;
        commentListEL.style.maxHeight = `${calculatedHeight}px`;
      }
    }
  }, [dataComment?.length, state.blocked, playerStage?.clientHeight]);

  const parseHidePhone = (str: any) => (str || '').replace(/(\d{3})\d{3}/, '$1***');

  const sendComment = async (message: any) => {
    if (!message || message === '' || !(message || '').replace(/\s/g, '').length) return;
    const data = dataEventDetails || state.dataEvents;
    const { id } = data;
    const comment = message;
    const clientEmail = profile?.email;
    const clientAVT = profile?.avatar;
    const clientName = profile?.givenName || parseHidePhone(profile?.mobile);
    const clientPhone = profile?.mobile;
    const now = new Date().getTime();
    const date = new Date(now);
    const formatDateNow = convertToDateTime(date, 'HH:mm DD/MM/YYYY');
    const dataClient = [
      {
        id: `clienID${id}${Math.floor(Math.random() * 10000)}`,
        name: clientName,
        avatar: clientAVT,
        comment: message,
        userID: profile?.id,
        timeStamp: formatDateNow
      }
    ];
    dataCommentDefault = [...state.dataComment, ...dataClient];
    setState((prevState) => ({
      ...prevState,
      dataComment: dataCommentDefault
    }));
    const postComment = await PageApi.postLiveStreamCommentId({
      id,
      comment,
      name: clientName,
      email: clientEmail,
      phone: clientPhone,
      avatar: clientAVT
    });

    if (
      postComment?.data?.error === ERROR_CODE.CODE_1001 ||
      postComment?.data?.error === ERROR_CODE.CODE_400
    ) {
      setState((prevState) => ({
        ...prevState,
        blocked: true
      }));
    }
  };

  const scrollToMyRef = () => {
    if (wrapperRef?.current) {
      const scroll = wrapperRef.current.scrollHeight - wrapperRef.current.clientHeight;
      wrapperRef.current.scrollTo(0, scroll);
    }
  };

  const onFocus = (e: any) => {
    if (isEmpty(profile)) {
      e.target.blur();
      const remakeDestination = encodeParamDestination(router?.asPath);
      router.push(
        `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${TYPE_TRIGGER_AUTH.COMMENT}`
      );
    }
  };

  const getLiveComment = async (dataEvent: any) => {
    const data = dataEventDetails || state.dataEvents || dataEvent;
    if (!data || !data?.id) return;
    const params: any = {
      id: data?.id,
      limit: 30
    };
    await PageApi.getLivestreamCommentById(params).then((res) => {
      if (res?.data) {
        const dataComment = res?.data;
        const dataSpin = res?.pined;
        nextRequest = res?.next_request ? res?.next_request * 1000 : RETRY_TIME;
        if (dataComment?.length) {
          const dataLength = +dataComment?.length;
          let timeInterval = Math.floor(nextRequest / dataLength); // Calculate show items in nextRequest second
          if (dataCommentDefault?.length === 0) {
            timeInterval = 10;
          }

          throttledProcess(dataComment, timeInterval);
        }
        if (dataSpin) {
          setState((prevState) => ({
            ...prevState,
            dataPined: dataSpin,
            blocked: false
          }));
        }
      } else if (!res?.blocked) {
        setState((prevState) => ({
          ...prevState,
          blocked: false
        }));
        reTryTimeOut = createTimeout(() => getLiveComment(dataEventDetails), RETRY_TIME);
      } else if (res?.blocked) {
        clearTimeout(reTryTimeOut);
        clearTimeout(timeout);
        setState((prevState) => ({
          ...prevState,
          blocked: true
        }));
      }
    });
  };

  const throttledProcess = async (items: any, interval: any) => {
    if (timeout) clearTimeout(timeout);
    if (items.length === 0) {
      getLiveComment(dataEventDetails); // All done call next data
      return;
    }
    const manualItems = items[0];
    if (manualItems) {
      const isExistedComment = existedComment({
        data: dataCommentDefault,
        id: manualItems?.id,
        userID: manualItems?.userID,
        comment: manualItems?.comment
      });
      if (!isExistedComment) {
        // add Data in every interval
        dataCommentDefault.push(manualItems);
        setState((prevState) => ({
          ...prevState,
          dataComment: dataCommentDefault
        }));
      }
    }
    timeout = createTimeout(() => throttledProcess(items.slice(1), interval), interval);
  };

  const liftUpContent = () => {
    // handle effect lift up items
    const el = ref.current;
    if (!el) return;
    let scrollTop = el.scrollTop + el.clientHeight + 1;
    if (isMobile && containerHeight) scrollTop += containerHeight - el.clientHeight;
    const isBottom = scrollTop >= el.scrollHeight;
    if ((el && isBottom) || el.scrollTop < el.scrollHeight) {
      el.scrollTop = el.scrollHeight;
      scrollToMyRef();
    }
  };

  const existedComment = ({ data, id, comment }: any) => {
    if (data?.length <= 0) return false;
    return !!data.find(
      (el: any) =>
        el?.id === id || ((el?.id).includes('clienID') && el?.comment?.length === comment?.length)
    );
  };

  const handleBannerChatClick = () => {
    window.open(bannerChat?.link, '_blank');
  };

  return (
    <>
      {isShowChatBanner && showBanner && (
        <div className="banner banner--live-stream-ads padding-xlarge-up-left-32 layer-2 overflow">
          <div className="banner__inner overflow">
            <div className="banner__body">
              {bannerChat?.showHideButton && (
                <Button
                  className="top-[10px] right-[16px] md:right-[10px] absolute hover:cursor-pointer z-10 !text-[12px] md:!text-[16px]"
                  iconClass="!text-white"
                  iconName="vie-times-medium"
                  onClick={(e: any) => {
                    e.preventDefault();
                    setShowBanner(false);
                  }}
                  isFadeInForIcon
                />
              )}
              <div className="relative hover:cursor-pointer" onClick={handleBannerChatClick}>
                <Image
                  src={isMobile ? bannerChat?.mobileImgSrc : bannerChat?.webImgSrc}
                  alt="banner-live-event"
                  className="min-w-[327px] h-auto w-full aspect-[327/70] object-contain md:h-[312px]"
                />
              </div>
            </div>
          </div>
        </div>
      )}
      {!profile?.isPremium && !isKid && !isGlobal && !isShowChatBanner && (
        <BannerLivestreamAds
          dataEventDetails={dataEventDetails}
          data={bannerLivestream}
          handleStatusLoadAds={setAdsLoadSuccess}
        />
      )}
      <div
        className={`comment comment--live ${state.blocked ? StyleBlocked.commentBlocked : ''}`}
        ref={commentLiveRef}
      >
        {state.blocked ? (
          <div
            className="flex flex-col items-center justify-center"
            style={{
              height: document.getElementById(EL_ID.PLAYER_STAGE)
                ? document.getElementById(EL_ID.PLAYER_STAGE)?.clientHeight
                : '100%'
            }}
          >
            <EmptyCommentBlocked isMobile={isMobile} isLiveEvent />
          </div>
        ) : (
          <>
            <div className="container" ref={ref}>
              {(dataPined?.id || dataPined?.comment) && (
                <div
                  className="media-object media-object__live is-pinned layer-1"
                  ref={pinnedRef}
                  id={EL_ID.PINNED_COMMENT}
                >
                  <div className="media-object-section middle">
                    <div className="avatar overflow light circle normal gender">
                      {dataPined?.avatar ? (
                        <img src={dataPined?.avatar} title="Pined Comment" alt="Pined Comment" />
                      ) : (
                        <span
                          className="avatar default overflow light circle normal"
                          title="Pined Comment"
                        />
                      )}
                    </div>
                  </div>
                  <div className="media-object-section">
                    <h4>{dataPined?.name}</h4>
                    <p className="text" style={{ whiteSpace: 'pre-line' }}>
                      {dataPined?.comment}
                    </p>
                  </div>
                  <div className="media-object-section bottom space-y-2">
                    <span className="icon icon--small">
                      <i className="vie vie-pin-o-rc-medium" />
                    </span>
                    <div className={styles.CommentDate}>{dataPined?.timeStamp}</div>
                  </div>
                </div>
              )}
              <div
                className="comment-lists scrollable-y over-scroll-contain max-h-small-up-256"
                ref={wrapperRef}
              >
                {(dataComment || []).map((item: any) => (
                  <div className="media-object media-object__live" key={item?.id}>
                    <div className="media-object-section middle">
                      <div className="avatar overflow light circle normal gender">
                        {item?.avatar ? (
                          <img src={item?.avatar} alt={item?.name} />
                        ) : (
                          <span
                            className="avatar default overflow light circle normal"
                            title={item?.name}
                          />
                        )}
                      </div>
                    </div>
                    <div className="media-object-section">
                      <h4>{item?.name}</h4>
                      <p className="text" style={{ whiteSpace: 'pre-line' }}>
                        {item?.comment}
                      </p>
                    </div>
                    <div className="media-object-section bottom">
                      <div className={styles.CommentDate}>{item?.timeStamp}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <CommentArea
              contentID={`commentBox_${dataEventDetails?.id}`}
              classButton="button"
              classButtonIco="vie-paper-plane-s"
              profile={profile}
              onFocus={onFocus}
              onClickSend={sendComment}
              disableAvatar
              router={router}
            />
          </>
        )}
      </div>
    </>
  );
};
export default LiveComment;
