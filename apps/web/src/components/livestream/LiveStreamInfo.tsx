import React, { useEffect, useMemo } from 'react';
import get from 'lodash/get';
import {
  addParamToUrlVieON,
  checkTimeLive,
  createTimeout,
  getLiveTime,
  onShareSocial
} from '@helpers/common';
import { TEXT } from '@constants/text';
import Banner from '@components/basic/Banner/Banner';
import ConfigImage from '@config/ConfigImage';
import { useVieRouter } from '@customHook';
import { CONTENT_TYPE, EL_SIZE_CLASS, PAGE } from '@constants/constants';
import { isMobileOnly } from 'react-device-detect';
import dynamic from 'next/dynamic';
import { setStartTimeLiveStream } from '@services/datetimeServices';
import SeoText from '../seo/SeoText';
import Button from '../basic/Buttons/Button';
import NewIcon from '../basic/Icon/NewIcon';

const TagsOutline = dynamic(import('@components/basic/Tags/TagsOutline'), { ssr: false });

let clickTimer: any = null;
const LiveStreamInfo = ({ dataEventDetails, onClickControlNotify, seoData }: any) => {
  const router = useVieRouter();

  if (!seoData) return null;

  const tvodInfo = dataEventDetails?.tvod || {};
  const {
    seo,
    title,
    shareURL,
    longDescription,
    isComingSoonDefault,
    isSubcribed,
    isLive,
    timeStart,
    isPremiere
  } = dataEventDetails || {};

  const comingSoonTimeText = useMemo(
    () => setStartTimeLiveStream(timeStart, false, isPremiere),
    [timeStart, isPremiere]
  );

  const shareOnSocial = (e: any) => {
    e.preventDefault();
    return onShareSocial({
      link: shareURL,
      name: 'facebook',
      callback: () => {}
    });
  };
  const onClickNotify = () => {
    if (clickTimer) clearTimeout(clickTimer);
    clickTimer = createTimeout(() => {
      if (typeof onClickControlNotify === 'function') onClickControlNotify();
    }, 500);
  };
  useEffect(
    () => () => {
      clearTimeout(clickTimer);
    },
    []
  );

  const isCheckLiveTime = checkTimeLive(dataEventDetails?.timeStartLiveStream);
  const timeLiveStream = `Bắt đầu phát trực tiếp ${getLiveTime(dataEventDetails?.timeStart)}`;

  const tagsOutlineData = useMemo(() => {
    const res = [];
    if (comingSoonTimeText && isComingSoonDefault > 0) {
      res.push({
        title: comingSoonTimeText,
        spClass: 'icon--small icon--tiny-xs',
        iClass: 'vie-clock-o-rc-medium text-[#3ac882]'
      });
    } else if (timeLiveStream && isLive > 0 && !isComingSoonDefault) {
      res.push({
        title: timeLiveStream,
        spClass: 'icon--small icon--tiny-xs',
        iClass: 'vie-clock-o-rc-medium text-[#3ac882]'
      });
    }
    if (tvodInfo?.bizInfo?.toTVod) {
      res.push({
        title: 'Có thể xem lại',
        spClass: 'icon--small icon--tiny-xs',
        iClass: 'vie-replay-rc-medium text-[#3ac882]'
      });
    }
    return res;
  }, [comingSoonTimeText, timeLiveStream, dataEventDetails, tvodInfo, isComingSoonDefault]);
  const isShowBanner = !!tvodInfo?.bizInfo?.bannerContent;

  let buttonTitle = TEXT.PRE_ORDER;
  if (isLive) buttonTitle = TEXT.TVOD_NOW;

  const onClickBannerTVod = () => {
    const queryParams = addParamToUrlVieON(router?.query, {
      type: CONTENT_TYPE.LIVESTREAM,
      id: dataEventDetails?.id || 0,
      isLiveEvent: true,
      fromPrice: get(tvodInfo, 'bizInfo.preOrder.isPreOrdering', false)
        ? get(tvodInfo, 'bizInfo.preOrder.price', 0)
        : get(tvodInfo, 'bizInfo.price', 0)
    });
    router.push(
      { pathname: PAGE.RENTAL_CONTENT, query: queryParams },
      { pathname: PAGE.RENTAL_CONTENT, query: queryParams }
    );
  };

  return (
    <div className="block block--stream-detail">
      <div className="block__body grid-x justify-content-between m-l-auto m-r-auto margin-small-up-top-16 margin-small-up-bottom-16 margin-medium-up-right-12 margin-xxlarge-up-top-24">
        {isShowBanner && (
          <Banner
            className={isMobileOnly ? 'size-w-full' : ''}
            maskInnerClass={isMobileOnly ? 'size-w-full' : ''}
            image={ConfigImage.bannerPreOrder}
            imageMobile={ConfigImage.bannerPreOrderMobile}
            description={tvodInfo?.bizInfo?.bannerContent || ''}
            btnClick={onClickBannerTVod}
            btnTitle={buttonTitle}
          />
        )}
        <div
          className={`text-white ${
            isShowBanner ? 'large-order-3' : ''
          } small-order-2 medium-order-1 medium-8 large-9 small-12`}
        >
          <SeoText seo={seoData} />
          <h2
            className="livestream__title md:mb-1.5 text-medium-up-28 text-small-up-20"
            title={title || seo?.title}
          >
            {title || seo?.title}
          </h2>
          <div className="flex flex-row flex-wrap space-x-[10px] lg:space-x-3">
            <TagsOutline
              txtClass="text-medium text-[#3ac882]"
              className="style--light"
              tagArray={tagsOutlineData || []}
              size={EL_SIZE_CLASS.LARGE}
            />
          </div>
          <p
            className="livestream__desc padding-small-up-top-16 text-gray239"
            dangerouslySetInnerHTML={{ __html: longDescription || seo?.description || '' }}
          />
        </div>
        <div
          className={`grid-x align-middle padding-small-up-bottom-12 padding-medium-up-bottom ${
            isShowBanner ? 'large' : 'medium'
          }-order-2 box-small-up-height-max-content space-x-3 xl:space-x-6`}
        >
          {isComingSoonDefault > 0 && !isCheckLiveTime && (
            <Button
              className={`button--for-dark !flex !flex-col items-center space-y-3${
                isSubcribed ? ' checked !text-vo-green' : ' !text-white hover:!text-vo-green'
              }`}
              onClick={onClickNotify}
              iconName={isSubcribed ? 'vie-tick' : 'vie-bell-o-rc-light'}
              title="Nhắc tôi"
              iconClass="!text-[1.5rem]"
              textClass="!text-[.75rem]"
            />
          )}
          {shareURL && (
            <a
              className="shared shared--link !flex !flex-col items-center space-y-3 text-white hover:text-vo-green"
              title={`${TEXT.SHARE} - ${title}`}
              onClick={shareOnSocial}
            >
              <NewIcon iconName="vie-location-share-o" iCustomizeClass="!text-[1.5rem]" />
              <span className="!text-[.75rem]">{TEXT.SHARE}</span>
            </a>
          )}
        </div>
      </div>
    </div>
  );
};

export default React.memo(LiveStreamInfo);
