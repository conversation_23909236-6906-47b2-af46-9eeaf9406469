import React, { useRef } from 'react';
import LiveStreamPlayer from './LiveStreamPlayer';
import LiveComment from './LiveComment';

const LiveStream = (props: any) => {
  const ref = useRef<any>(null);

  return (
    <>
      <div className="player player--live-stream overflow" ref={ref}>
        <LiveStreamPlayer {...props} />
      </div>
      <LiveComment {...props} playerRef={ref} />
    </>
  );
};

export default LiveStream;
