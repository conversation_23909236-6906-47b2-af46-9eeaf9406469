import React from 'react';
import ConfigUser from '@config/ConfigUser';
import { CONTENT_TYPE, POPUP } from '@constants/constants';
import { createTimeout, numberWithCommas } from '@helpers/common';
import UserServices from '@services/userServices';
import TrackingLog from '@services/trackingLog';
import { calculatePlayingTime, getInfoVideoCodec } from '@services/playerServices';
import { trackingStreamProgress } from '@tracking/video';
import TrackingPlayer from '@tracking/functions/TrackingPlayer';
import dynamic from 'next/dynamic';
import { ERROR_PLAYER } from '@constants/player';
import LiveStreamInfo from './LiveStreamInfo';

const Player = dynamic(import('../basic/Player/Player'), { ssr: false });

class LiveStreamPlayer extends React.Component {
  error: any;
  initId: any;
  initPlayerTime: any;
  loadMoreTimer: any;
  offsetTime: any;
  player: any;
  playerName: any;
  playingId: any;
  removePlayer: any;
  sessionId: any;
  startTime: any;
  totalPlayedData: any;
  tracking: any;
  video: any;
  videoContainer: any;
  sessionToken = '';

  constructor(props: any) {
    super(props);
    this.state = {
      playerReady: false,
      playerError: false,
      fingeringParams: null,
      totalCCU: null,
      hlsLinkPlay: props?.dataEventDetails?.trailerLinkPlay?.hls,
      dashLinkPlay: props?.dataEventDetails?.trailerLinkPlay?.dash
    };
    // Tracking Object
    this.tracking = new TrackingLog();
    this.player = null;
    this.sessionId = 0;
    this.error = false;
    this.error = false;
    this.offsetTime = 0;
    this.playingId = '';
    this.loadMoreTimer = 0;
    this.initId = '';
    this.initPlayerTime = 0;
  }

  componentDidMount() {
    window.addEventListener('beforeunload', this.onBrowserChange);
    const { dataEventDetails }: any = this.props;
    const isAutoLoadMainLink = dataEventDetails?.isAutoLoadMainLink;
    if (dataEventDetails?.id && isAutoLoadMainLink) {
      this.loadLinkPlay(dataEventDetails?.trailerLinkPlay);
    }
    this.handleCatErrorEventDetail(dataEventDetails);
  }

  componentDidUpdate(prevProps: any) {
    const { dataEventDetails, popupName, dataRefreshSession, concurrentScreen }: any = this.props;
    const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
    const isAutoLoadMainLink = dataEventDetails?.isAutoLoadMainLink;

    if (popupName && popupName !== prevProps?.popupName) {
      if (
        popupName === POPUP.NAME.PLAYER_ERROR_LIVESTREAM ||
        popupName === POPUP.NAME.PLAYER_ERROR_PREMIERE
      ) {
        this.playingId = dataEventDetails?.id;
      }
    }

    if (dataEventDetails?.id !== prevProps?.dataEventDetails?.id) {
      this.handleCatErrorEventDetail(dataEventDetails);
      if (prevProps?.dataEventDetails?.id) {
        const playedDuration = calculatePlayingTime({
          totalPlayedData: this.totalPlayedData
        });
        TrackingPlayer.livestreamCompleted({
          eventDetail: prevProps?.dataEventDetails,
          sessionId: this.sessionId,
          playedDuration
        });
      }

      if (isAutoLoadMainLink) {
        this.loadLinkPlay(dataEventDetails?.trailerLinkPlay);
      }
    }
    if (this.sessionToken !== sessionToken) {
      this.sessionToken = sessionToken;
    }
  }

  componentWillUnmount() {
    const { dataEventDetails }: any = this.props;
    if (dataEventDetails?.id && this.playingId !== dataEventDetails?.id) {
      TrackingPlayer.exitBeforeStarted({
        contentId: dataEventDetails?.id,
        contentTitle: dataEventDetails.title,
        contentType: CONTENT_TYPE.LIVESTREAM
      });
    }

    const playedDuration = calculatePlayingTime({
      totalPlayedData: this.totalPlayedData
    });
    TrackingPlayer.livestreamCompleted({
      eventDetail: dataEventDetails,
      sessionId: this.sessionId,
      playedDuration
    });
    this.handleRecord({ action: ConfigUser.RECORD.ACTION.STOP });
    window.removeEventListener('beforeunload', this.onBrowserChange);
    if (this.loadMoreTimer) clearTimeout(this.loadMoreTimer);
  }

  handleCatErrorEventDetail = (dataEventDetails: any) => {
    const { statusEvent, currentProfile }: any = this.props;
    if (dataEventDetails) {
      if (
        dataEventDetails?.isConcurrentScreenLimit ||
        dataEventDetails?.isEndStream ||
        statusEvent ||
        currentProfile?.isKid
      )
        return;
      if (dataEventDetails?.success) {
        if (
          !dataEventDetails?.linkPlay &&
          dataEventDetails?.timeStartLiveStream <= new Date().getTime() / 1000
        ) {
          this.handleOpenCatError({
            errorData: {
              errorType: ERROR_PLAYER.TYPE.EMPTY_LINK
            }
          });
        }
      } else {
        this.handleOpenCatError({
          errorData: {
            errorType: ERROR_PLAYER.TYPE.API_DETAIL,
            detailData: dataEventDetails
          }
        });
      }
    }
  };

  updateTotalPlayedData = (totalPlayedData: any) => {
    this.totalPlayedData = totalPlayedData || [];
  };

  onBrowserChange = () => {
    const { dataEventDetails, handleEndSessionPlay }: any = this.props;
    if (dataEventDetails?.id && this.playingId !== dataEventDetails?.id) {
      TrackingPlayer.exitBeforeStarted({
        contentId: dataEventDetails?.id,
        contentTitle: dataEventDetails.title,
        contentType: CONTENT_TYPE.LIVESTREAM
      });
    }
    const playedDuration = calculatePlayingTime({
      totalPlayedData: this.totalPlayedData
    });
    TrackingPlayer.livestreamCompleted({
      eventDetail: dataEventDetails,
      sessionId: this.sessionId,
      playedDuration,
      isTrackImmediately: true
    });
    this.handleRecord({ action: ConfigUser.RECORD.ACTION.STOP });
    if (this.sessionToken) {
      handleEndSessionPlay(this.sessionToken);
    }
    return undefined;
  };

  loadLinkPlay = ({ hls, dash }: any) => {
    this.setState((prevState) => ({
      ...prevState,
      hlsLinkPlay: hls,
      dashLinkPlay: dash
    }));
  };

  setupPlayer = ({ video, videoContainer, removePlayer, player, playerName }: any) => {
    this.sessionId = new Date().getTime();
    const { dataEventDetails }: any = this.props;
    const timeStart = dataEventDetails?.timeStartLiveStream;
    const currentTime = new Date().getTime();
    const timeLive = timeStart * 1000 - currentTime;
    const isAutoLoadMainLink = dataEventDetails?.isAutoLoadMainLink;
    if (this.loadMoreTimer) clearTimeout(this.loadMoreTimer);
    if (isAutoLoadMainLink) {
      this.loadMoreTimer = createTimeout(() => {
        this.autoLoadLinkPlay(dataEventDetails);
      }, timeLive);
    } else {
      this.autoLoadLinkPlay(dataEventDetails);
    }
    this.video = video;
    this.videoContainer = videoContainer;
    this.removePlayer = removePlayer;
    this.player = player || video;
    this.playerName = playerName;

    // Tracking Init Player
    this.handleInitPlayer();
  };

  handleInitPlayer = () => {
    const { dataEventDetails }: any = this.props;
    if (dataEventDetails?.id && this.initId !== dataEventDetails?.id) {
      this.initId = dataEventDetails?.id;
      this.initPlayerTime = new Date().getTime();
      TrackingPlayer.initPlayer({
        contentId: dataEventDetails?.id,
        contentType: dataEventDetails?.type,
        contentTitle: dataEventDetails?.title
      });
    }
  };

  startupLoadPreroll = () => {
    const { dataEventDetails }: any = this.props;
    TrackingPlayer.videoStartupPrerollLoad({
      contentId: dataEventDetails?.id,
      contentType: dataEventDetails?.type,
      contentTitle: dataEventDetails?.title,
      totalTime: this.initPlayerTime ? new Date().getTime() - this.initPlayerTime : 0
    });
  };

  autoLoadLinkPlay = (dataEventDetails: any) => {
    this.loadLinkPlay({
      hls: dataEventDetails?.linkPlay?.hlsLinkPlay,
      dash: dataEventDetails?.linkPlay?.dashLinkPlay
    });
  };

  onPlaying = () => {
    const { handleVideoEndOrError, dataEventDetails }: any = this.props;
    if (typeof handleVideoEndOrError === 'function') {
      handleVideoEndOrError(false);
    }
    this.handleRecord({ action: ConfigUser.RECORD.ACTION.PLAY });
    if (this.playingId !== dataEventDetails?.id) {
      this.playingId = dataEventDetails?.id;
      TrackingPlayer.livestreamStarted({ eventDetail: dataEventDetails || {} });
    }
  };

  onTimeUpdate = ({ currentTime }: any) => {
    this.handleRecordOffset(currentTime);
    trackingStreamProgress({
      startTime: this.startTime,
      segmentLivestreamProgress: this.segmentLivestreamProgress
    });
  };

  segmentLivestreamProgress = (eventName: any, progress: any) => {
    const { dataEventDetails, router }: any = this.props;
    const segmentParams = {
      ...dataEventDetails,
      progress,
      query: router?.query
    };
    // Check Live
    TrackingPlayer.livestreamEvents(eventName, segmentParams);
  };

  handleRecordOffset = (currentTime: any) => {
    if (currentTime && currentTime - this.offsetTime >= 5) {
      this.handleRecord({ action: ConfigUser.RECORD.ACTION.OFFSET });
      this.offsetTime = currentTime;
    }
    this.offsetTime = this.offsetTime >= currentTime ? currentTime : this.offsetTime;
    if (this.tracking) {
      const { totalCCU }: any = this.tracking || {};
      if (totalCCU !== (this.state as any).totalCCU) {
        const totalCCUParsed = numberWithCommas(totalCCU);

        this.setState((prevState) => ({
          ...prevState,
          totalCCU: totalCCUParsed
        }));
      }
    }
  };

  handleRecord = ({ action, props }: any) => {
    const { dataEventDetails } = props || this.props;
    const getInfoTrack = getInfoVideoCodec({ player: this.player, playerName: this.playerName });
    const params: any = {
      usi: dataEventDetails?.usi,
      contentId: dataEventDetails?.id,
      contentName: dataEventDetails?.title,
      contentType: 0,
      timeSecond: new Date().getTime() / 1000,
      action,
      videoCodec: getInfoTrack?.videoCodec
    };
    if (action === ConfigUser.RECORD.ACTION.STOP) {
      this.handleTracking(ConfigUser.TRACKING.STOP);
    }
    UserServices.recordProgressOfUser(params);
  };

  onEnded = () => {
    const { handleVideoEndOrError }: any = this.props;
    if (typeof handleVideoEndOrError === 'function') {
      handleVideoEndOrError(true);
    }
    this.handleRecord({ action: ConfigUser.RECORD.ACTION.STOP });
  };

  handleTracking = ({ action, contentId, bufferData }: any) => {
    const getInfoTrack = getInfoVideoCodec({ player: this.player, playerName: this.playerName });
    const videoCodec = getInfoTrack?.videoCodec;
    UserServices.handleTrackingLog({
      tracker: this.tracking,
      action,
      contentId,
      bufferData: { ...bufferData, videoCodec }
    });
  };

  onPaused = () => {
    this.handleRecord({ action: ConfigUser.RECORD.ACTION.PAUSE });
  };

  onError = ({ event, linkPlay, streamingProtocol, getInfoTrack }: any) => {
    const { handleVideoEndOrError, dataEventDetails }: any = this.props;
    if (typeof handleVideoEndOrError === 'function') {
      handleVideoEndOrError(true);
    }
    TrackingPlayer.playerError({
      event,
      content: dataEventDetails,
      streamingProtocol,
      getInfoTrack,
      linkPlay
    });
  };

  setInitPlay = ({ bufferTime }: any) => {
    this.sessionId = new Date().getTime();
    this.startTime = new Date().getTime();
    const { dataEventDetails }: any = this.props;
    const contentId = dataEventDetails?.id;
    const getInfoPlayer = getInfoVideoCodec({ player: this.player, playerName: this.playerName });
    const bufferData = this.handleTrackingBufferTime({
      contentId,
      contentName: dataEventDetails?.title || '',
      contentType: 0,
      usi: dataEventDetails?.usi,
      bufferTime: bufferTime / 1000,
      duration: 0,
      videoCodec: getInfoPlayer.videoCodec
    });
    this.handleTracking({ action: ConfigUser.TRACKING.START, contentId, bufferData });
  };

  handleTrackingBufferTime = ({ contentId, contentName, contentType, bufferTime, usi }: any) => {
    const timestamp = Math.floor(new Date().getTime() / 1000);
    const bufferData = {
      contentId,
      contentName,
      contentType,
      usi,
      data: [
        {
          action: ConfigUser.RECORD.ACTION.PLAY,
          duration: parseInt(this.video?.duration || 0),
          timestamp,
          buffer_time: bufferTime
        }
      ]
    };
    return bufferData;
  };

  onControlPlay = ({ playerError }: any) => {
    const { onControlPlay }: any = this.props || {};
    if (typeof onControlPlay === 'function') onControlPlay({ playerError });
  };

  handleOpenCatError = (catData: any) => {
    const {
      openPopup,
      dataEventDetails,
      handleEndSessionPlay,
      dataRefreshSession,
      concurrentScreen
    }: any = this.props || {};
    const isPremiere = dataEventDetails?.isPremiere;
    const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
    if (sessionToken) {
      handleEndSessionPlay(sessionToken);
    }
    if (openPopup && navigator.onLine) {
      openPopup({
        name: isPremiere ? POPUP.NAME.PLAYER_ERROR_PREMIERE : POPUP.NAME.PLAYER_ERROR_LIVESTREAM,
        ...catData,
        errorData: {
          ...catData?.errorData,
          livestreamData: { streamDetail: dataEventDetails }
        }
      });
    }
  };

  getPlayerData = () => {
    const { dataEventDetails, openPopup, setToast, handleStatusFullscreenOfPlayer, isGlobal }: any =
      this.props;
    const notSeekBar = false;
    const isPaused = false;
    const displayControl = false;
    const playerTitle = dataEventDetails?.title;
    const contentId = dataEventDetails?.id;
    const isLiveStream = true;
    const notEpisode = true;
    const linkPlay = dataEventDetails?.linkPlay;
    const poster =
      dataEventDetails?.images?.thumbnailBig ||
      dataEventDetails?.images?.thumbnailBigNTC ||
      dataEventDetails?.images?.thumbnailNTC ||
      '';
    const permission = dataEventDetails?.permission;
    const softLogo = dataEventDetails?.softLogo;
    const preventPauseLive = !dataEventDetails?.isPremiere;
    const isAutoLoadMainLink = dataEventDetails?.isAutoLoadMainLink;
    const timeStartLiveStream = dataEventDetails?.timeStartLiveStream;
    const trailerLinkPlay = dataEventDetails?.trailerLinkPlay;
    const linkPlaysToRetry = dataEventDetails?.linkPlaysToRetry;
    const isEndStream = dataEventDetails?.isEndStream;
    const isPremiere = dataEventDetails?.isPremiere;
    const params: any = {
      isGlobal,
      linkPlaysToRetry,
      isLiveStream,
      isPremiere,
      notSeekBar,
      isPaused,
      displayControl,
      playerTitle,
      contentId,
      notEpisode,
      dataEventDetails,
      poster,
      permission,
      softLogo,
      preventPauseLive,
      linkPlay,
      isAutoLoadMainLink,
      timeStartLiveStream,
      trailerLinkPlay,
      isEndStream,
      handleStatusFullscreenOfPlayer
    };
    const {
      onStalled,
      handleCheckTVodLivestream,
      openModalEndStreamTVod,
      autoLoadLiveStream
    }: any = this.props;
    const { handleOpenCatError }: any = this;
    const { startupLoadPreroll }: any = this;
    const { setupPlayer }: any = this;
    const { onPlaying }: any = this;
    const { onEnded } = this;
    const { onPaused } = this;
    const { onError } = this;
    const { setInitPlay } = this;
    const { onTimeUpdate } = this;
    const { onControlPlay } = this;
    const { updateTotalPlayedData } = this;
    const events = {
      setupPlayer,
      onPlaying,
      onStalled,
      onEnded,
      onPaused,
      onError,
      setInitPlay,
      onTimeUpdate,
      onControlPlay,
      openPopup,
      autoLoadLiveStream,
      handleCheckTVodLivestream,
      openModalEndStreamTVod,
      updateTotalPlayedData,
      setToast,
      handleOpenCatError,
      startupLoadPreroll
    };
    return { ...this.props, ...dataEventDetails, ...this.state, ...params, ...events };
  };

  render() {
    const dataPlayer = this.getPlayerData();
    const {
      isSafari,
      isMobile,
      isTablet,
      isIOS,
      blockPlayer,
      dataEventDetails,
      dataRelated,
      eventRelated,
      statusLiveEvent,
      deviceId,
      tokenAnonymous
    }: any = this.props;
    // console.log('dataEventDetails', dataEventDetails);
    return (
      <>
        <div className="player-stage" id="playerStage">
          <Player
            {...dataPlayer}
            blockPlayer={blockPlayer}
            dataRelated={dataRelated}
            isIOS={isIOS}
            isSafari={isSafari}
            isMobile={isMobile}
            isTablet={isTablet}
            isAgeRestricted={dataEventDetails?.warningScreen}
            warningScreen={dataEventDetails?.warningScreen}
            warningTag={dataEventDetails?.warningTag}
            warningMessage={dataEventDetails?.warningMessage}
            eventRelated={eventRelated}
            statusLiveEvent={statusLiveEvent}
            isDrm={dataEventDetails?.isDrm}
            drmProvider={dataEventDetails?.drmProvider}
            drmMerchant={dataEventDetails?.drmMerchant}
            drmServiceName={dataEventDetails?.drmServiceName}
            deviceId={deviceId}
            tokenAnonymous={tokenAnonymous}
            typePlayer="livestream"
          />
        </div>
        <LiveStreamInfo {...this.props} />
      </>
    );
  }
}

export default LiveStreamPlayer;
