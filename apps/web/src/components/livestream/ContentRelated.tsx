import { useSelector } from 'react-redux';
import React, { useEffect, useMemo, useRef } from 'react';
import Card from '@components/basic/Card/Card';
import isEmpty from 'lodash/isEmpty';
import { TEXT } from '@constants/text';
import classNames from 'classnames';
import CardRelatedHoverItem from '@components/Card/CardRelatedHoverItem';
import Image from '@components/basic/Image/Image';
import ConfigImage from '@config/ConfigImage';
import {
  trackingEndScreenSuggestionLiveEventSelected,
  trackingEndScreenSuggestionLiveEventShow
} from '../../tracking/functions/TrackingEndScreen';

const ContentRelated = ({ isFullscreen }: any) => {
  const refEl = useRef<any>(null);
  const ref: any = useRef([]);
  const { eventRelated } = useSelector((state: any) => state?.Livestream) || {};
  const { cardRelatedHover } = useSelector((state: any) => state?.Popup);
  const customClass = useMemo(() => {
    if (refEl?.current) {
      return `${(refEl?.current?.offsetWidth * 232) / 1920}px`;
    }
    return '0px';
  }, [refEl?.current, isFullscreen]);

  const handleClickItem = ({ cardData }: any) => {
    trackingEndScreenSuggestionLiveEventSelected({ contentId: cardData?.id });
  };

  useEffect(() => {
    trackingEndScreenSuggestionLiveEventShow();
  }, []);

  return (
    <div className="w-full h-full">
      {!isEmpty(cardRelatedHover) && !isEmpty(eventRelated) && <CardRelatedHoverItem />}
      <div className="relative z-[2] w-full h-full bg-[linear-gradient(180deg,rgba(17,17,17,0.00)0%,#111_100%)]">
        <div
          className={`absolute left-1/2 transform -translate-y-1/2 -translate-x-1/2 ${
            !isEmpty(eventRelated?.items) ? 'top-[42%]' : 'top-1/2'
          }`}
        >
          <div
            className={classNames(
              'text-center text-white font-bold',
              isFullscreen
                ? 'text-4xl leading-[52px] min-[2560px]:!text-[48px] min-[2560px]:!leading-[72px]'
                : 'text-[28px] min-[2560px]:!text-[42px]'
            )}
          >
            {TEXT.CONTENT_EVENT_END}
          </div>
          <div
            className={classNames(
              'text-center text-[#9E9E9E] font-normal',
              isFullscreen
                ? 'text-2xl min-[2560px]:!text-[28px]'
                : 'text-base min-[2560px]:!text-[24px]'
            )}
          >
            {!isEmpty(eventRelated?.items)
              ? TEXT.CONTENT_RELATED_AVAILABILITY
              : TEXT.CONTENT_RELATED_NOT_AVAILABILITY}
          </div>
        </div>
        <Image
          className={classNames(
            isFullscreen
              ? 'min-w-full min-h-full absolute left-1/2 top-1/2 transform -translate-y-1/2 -translate-x-1/2'
              : 'w-full'
          )}
          src={ConfigImage.bgVieOnEndStream}
          alt="bg-vieon"
          notWebp
        />
      </div>
      {!isEmpty(eventRelated?.items) && (
        <div
          ref={refEl}
          className="z-[20] absolute w-full bottom-0 right-0 text-white text-right mb-[60px]"
        >
          <div
            className="flex justify-content-end align-center space-x-3 px-5"
            style={{ paddingLeft: `${customClass}` }}
          >
            {eventRelated?.items?.map((item: any, index: any) => (
              <div
                className={classNames(
                  'relative',
                  isFullscreen
                    ? '!w-[320px] min-[2560px]:!w-[480px]'
                    : '!w-[200px] min-[2560px]:!w-[300px]'
                )}
                key={item.id}
              >
                {index === 0 && (
                  <div
                    className={classNames(
                      'absolute left-0 text-left text-white  font-medium',
                      '-top-[28px] min-[2560px]:!-top-[36px]',
                      isFullscreen
                        ? '!text-xl min-[2560px]:!text-[30px]'
                        : '!text-base min-[2560px]:!text-2xl'
                    )}
                  >
                    {TEXT.CONTENT_RELATED}
                  </div>
                )}
                <Card
                  lengthEventRelated={eventRelated?.items?.length}
                  cardData={item}
                  ref={ref}
                  index={index + 1}
                  randomID={item?.randomID || `${item?.id}_${index}`}
                  notLazy
                  isEventRelated
                  isFullscreen={isFullscreen}
                  onContentSelected={handleClickItem}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
export default ContentRelated;
