import ConfigImage from '@config/ConfigImage';
import React from 'react';

const Empty: any = (userId: any, onClick: any) => (
  <div className="empty">
    <div className="content">
      <img className="inline-block" src={ConfigImage.commentFirst} alt="No comment yet" />
      {userId ? (
        <p className="text">Hãy là người bình luận đầu tiên!</p>
      ) : (
        <p className="text">
          Hãy{' '}
          <button className="text-white" onClick={onClick}>
            <strong><PERSON><PERSON><PERSON> ký</strong>
          </button>{' '}
          để là người bình luận đầu tiên!
        </p>
      )}
    </div>
  </div>
);

export default Empty;
