import React from 'react';
import classNames from 'classnames';
import AvatarBase from '@components/basic/Avatar/Base';
import { convertToDateTime } from '@helpers/utils';
import styles from './Styles.module.scss';

const CommentReplyItem = (props: any) => {
  const { created_at, message, user }: any = props[0];
  const dateCreatedAt = convertToDateTime(created_at * 1000, 'HH:mm DD/MM/YYYY');
  const userAvatar = user?.avatar;
  const userName = user?.name;
  const avatarStyles = userAvatar && 'overflow-hidden';

  return (
    <div className={classNames(styles.CommentReplyItem)}>
      <div className="flex-none w-[2.5625rem]">
        {userAvatar && (
          <AvatarBase
            className={classNames(
              'relative rounded-full w-[2.5625rem] h-[2.5625rem]',
              avatarStyles
            )}
            title={userName || 'Ảnh đại diện'}
            imgUrl={userAvatar}
          />
        )}
      </div>
      {(userName || message) && (
        <div className={styles.CommentContent}>
          {userName && <h4 className={classNames(styles.CommentContentL)}>{userName}</h4>}
          {message && <p className={classNames(styles.CommentContentP)}>{message}</p>}
        </div>
      )}
      {dateCreatedAt && (
        <div className={styles.CommentExtended}>
          <span className={styles.CommentCreatedAt}>{dateCreatedAt}</span>
        </div>
      )}
    </div>
  );
};

export default CommentReplyItem;
