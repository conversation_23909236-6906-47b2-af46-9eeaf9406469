import React from 'react';
import classNames from 'classnames';
import CommentItem from './CommentItem';
import styles from './Styles.module.scss';

const CommentList = ({ commentList }: any) => {
  if (!commentList || commentList.length === 0) return null;
  return (
    <div className={classNames(styles.CommentGroup)}>
      {commentList.map((comment: any) => (
        <CommentItem key={comment.id} {...comment} />
      ))}
    </div>
  );
};

CommentList.propTypes = {};

export default CommentList;
