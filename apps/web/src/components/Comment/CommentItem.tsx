import React from 'react';
import classNames from 'classnames';
import AvatarBase from '@components/basic/Avatar/Base';
import CommentReplyItem from '@components/Comment/CommentReplyItem';
import { useSelector } from 'react-redux';
import styles from './Styles.module.scss';

const CommentItem = (props: any) => {
  const { userName, userAvatar, createdAt, message, pin, reply } = props;
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const pinned = pin === 1 && 'bg-[#333]';
  const avatarStyles = userAvatar && 'overflow-hidden';
  const withReply = !isMobile && reply && `${styles.LineMask}`;

  return (
    <div className={classNames(styles.CommentItem, pinned)}>
      <div className={classNames(styles.CommentItemContainer, withReply)}>
        <div className="flex-none w-[2.5625rem]">
          <AvatarBase
            className={classNames(
              'relative rounded-full w-[2.5625rem] h-[2.5625rem]',
              avatarStyles
            )}
            title={userName || 'Ảnh đại diện'}
            imgUrl={userAvatar}
          />
        </div>
        <div className={styles.CommentContent}>
          {userName && <h4 className={classNames(styles.CommentContentL)}>{userName}</h4>}
          {message && <p className={classNames(styles.CommentContentP)}>{message}</p>}
        </div>
        <div className={styles.CommentExtended}>
          {pin === 1 && (
            <span className="icon icon--small">
              <i className="vie vie-pin-o-rc-medium" />
            </span>
          )}
          {createdAt && <span className={styles.CommentCreatedAt}>{createdAt}</span>}
        </div>
      </div>
      {!isMobile && reply && <CommentReplyItem {...reply} />}
    </div>
  );
};

export default CommentItem;
