.Comment {
  @apply max-w-4xl;

  &Input {
    @apply w-full relative;

    & > textarea {
      @apply m-0 border-0 text-[1rem] text-white w-full bg-transparent bg-gradient-to-b from-[#333] to-[#333] bg-[length:0_1px_100%_1px] bg-[50%_100%];
      @apply focus:bg-[length:100%_1px];
    }
  }

  &Item {
    @apply relative flex flex-col p-3 md:p-4 space-y-3 md:space-y-4;
    &Container {
      @apply relative flex flex-row space-x-3 md:space-x-4;
    }
  }

  &ReplyItem {
    @apply relative flex flex-row space-x-3 md:space-x-4 pl-[3.25rem] md:pl-[3.5rem];
  }

  &Content {
    @apply flex-auto md:max-w-[calc(100%-9.5rem)] w-full space-y-1;

    &L {
      @apply text-white text-[.875rem] font-semibold;
    }

    &P {
      @apply text-white/50 text-[.6875rem] md:text-[.875rem] font-normal whitespace-pre-line;
    }
  }

  &Extended {
    @apply flex flex-col items-end md:min-w-[6.125rem] w-[28%] md:w-[16%] space-y-2 text-white;
  }

  &CreatedAt {
    text-align: right;
    @apply text-[#9B9B9B] text-[.6875rem] md:text-[.75rem] font-normal;
  }

  &Group {
    @apply flex flex-col relative;
  }
}

.LineMask {
  @apply before:block before:h-[calc(100%-.875rem)] before:w-6 before:absolute before:left-5 before:top-[3.125rem] before:border-0 before:border-l before:border-b before:border-l-[#707070] before:border-b-[#707070] before:rounded-bl-lg;
}

.Premium {
  @apply flex w-[1.375rem] h-[.75rem] absolute items-center justify-center left-1/2 top-[calc(2.5625rem-.5rem)] -translate-x-1/2 text-[#DA9E1C] text-[.75rem] bg-gradient-to-b from-black to-[#333] border border-solid border-[#DA9E1C] rounded-sm;
}
