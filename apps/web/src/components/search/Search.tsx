import React, { useEffect, useRef } from 'react';
import { useVieRouter } from '@customHook';
import { useSelector } from 'react-redux';
import { TEXT } from '@constants/text';
import { handleScrollTop } from '@helpers/common';
import ArtistItem from '@components/basic/items/ArtistItem';
import classNames from 'classnames';
import FilterDropDown from '../basic/FilterDropDown';
import FilterSearchSuggest from '../basic/FilterDropDown/FilterSearchSuggest';
import EmptySearch from '../empty/EmptySearch';
import Card from '../basic/Card/Card';
import CardList from '../basic/Card/CardList';
import SeoText from '../seo/SeoText';
import RibbonLoading from '../basic/Loading/RibbonLoading';

const Search = (props: any) => {
  const {
    listDataAllPage,
    titlePage,
    filterName,
    onScrollDown,
    currentFilterId,
    ribbonID,
    emptyPage,
    dataTagsFilter,
    changeFilter,
    isEmptyItem,
    handleClickItem,
    handleChangeKeyword,
    seoData,
    loading,
    isMobile,
    keyword,
    onSelectedRelatedKeyword
  } = props || {};
  const router = useVieRouter();
  const ref = useRef([]);
  const wrapperRef = useRef<any>(null);
  const dataSearchSuggest = useSelector((state: any) => state?.Search?.SEARCH_SUGGEST);

  const { q, vid } = router?.query || {};

  useEffect(() => {
    // Catch query change =>>>> re render items
    if (!vid) {
      if (handleChangeKeyword) handleChangeKeyword({ keyword: q, clicked: false });
    }
    if (wrapperRef?.current) {
      handleScrollTop();
    }
  }, [q, vid]);

  const onChangeFilter = (e: any, index: any, item: any) => {
    if (item) {
      changeFilter(item.id, item.name);
    }
  };

  const onClickItem = (item: any, keyword: any, type: any, pageIndex: any, index: any) => {
    let itemPosition = pageIndex * 30 + index;

    if (pageIndex > 0) {
      itemPosition = pageIndex * 30 + index + 1;
    }
    if (handleClickItem) handleClickItem({ id: item?.id, keyword, type, item, itemPosition });
  };
  const handleChangeContent = async ({ keyword, index }: any) => {
    const clicked = !!keyword;
    if (onSelectedRelatedKeyword) onSelectedRelatedKeyword({ relKeyword: keyword, index });
    return (await handleChangeKeyword) && handleChangeKeyword({ keyword, clicked });
  };

  const renderCardList = () => (
    <>
      {Object.keys(listDataAllPage).map((key, pageIndex) =>
        (listDataAllPage[key]?.items || []).map((item: any, i: any) => {
          const { isArtist, seo, name, images } = item;
          if (!isArtist) {
            return (
              <Card
                key={item.id + i || i}
                cardData={item}
                ref={ref}
                index={i + 1}
                randomID={`${item?.id}_${i}`}
                onClick={() => onClickItem(item, router.query.q, '', pageIndex, i)}
              />
            );
          }
          return (
            <ArtistItem
              key={item.id + i || i}
              name={name}
              link={seo.url}
              avatar={images.avatar}
              onClick={() => onClickItem(item, router.query.q, 'artist', pageIndex, i)}
            />
          );
        })
      )}
    </>
  );

  const dataTagsFilterTemp = [...[{ name: 'Tất cả nội dung' }], ...(dataTagsFilter?.items || [])];
  const noFilter = !listDataAllPage?.[0]?.items?.length && !currentFilterId;

  let headerRightStyle = {};
  if (isMobile) {
    headerRightStyle = {
      display: 'flex',
      width: '100%',
      justifyContent: 'flex-end'
    };
  }
  return (
    <section
      className="section section--search section--for-dark !py-4 md:!py-6 canal-v"
      style={!isMobile ? { minHeight: '50vh' } : {}}
      ref={wrapperRef}
    >
      <SeoText seo={seoData?.data?.seo} forceShowH1 />
      <div
        className={classNames(
          'section__header horizontal horizontal-top'
          // isMouseEnterCard ? '!z-0' : ''
        )}
      >
        <div className="section__header-left">
          <FilterSearchSuggest
            dataSuggest={dataSearchSuggest}
            keyword={keyword}
            handleChangeContent={handleChangeContent}
          />
          {titlePage && <h2 className="section__title title">{titlePage}</h2>}
        </div>
        {!noFilter && !emptyPage && (
          <div className="section__header-right" style={headerRightStyle}>
            <FilterDropDown
              className="filter filter--sort filter--dark"
              buttonClass="flex items-center justify-center !border !border-solid !border-vo-gray-200 hover:!border-vo-green h-9 px-3 bg-vo-dark-gray-900/50 !text-white hover:!text-vo-green space-x-3 transition-colors"
              iconClass="icon--tiny icon--absolute"
              iconNameSlotRight="vie-chevron-down-red-medium"
              id="filterTypeDropdown"
              title={TEXT.ALL_EPISODE}
              filterList={dataTagsFilterTemp}
              changeFilter={onChangeFilter}
              filterName={filterName}
              alignRight
            />
          </div>
        )}
      </div>
      <div className={classNames('section__body')}>
        {emptyPage && !keyword ? (
          <EmptySearch ribbonID={ribbonID} />
        ) : isEmptyItem ? (
          <EmptySearch filterId={currentFilterId} />
        ) : (
          <CardList
            renderContent={renderCardList}
            heightCheckScroll={320}
            onScrollDown={onScrollDown}
          />
        )}
      </div>
      {loading && (
        <div style={{ marginTop: '50px' }}>
          <RibbonLoading />
        </div>
      )}
    </section>
  );
};
export default Search;
