import { PLAYER_NAME, PLAYER_STATUS } from '@constants/player';
import isEmpty from 'lodash/isEmpty';
import { EL_ID } from '@constants/constants';

declare const window: any;

export const setPlayerDataVod = ({ data, isSafari }: any) => {
  let linkPlay = data?.linkplay?.hls;
  let playerName = PLAYER_NAME.HLS_PLAYER;
  if (data?.isDrm) {
    if (!isSafari) linkPlay = data?.linkplay?.dash;
    playerName = PLAYER_NAME.SHAKA_PLAYER;
  }

  return {
    linkPlay,
    playerName
  };
};
export const calculatePlayingTime = ({ totalPlayedData }: any) => {
  let totalPlayedDuration = 0;
  const data = [...(totalPlayedData || [])];
  data.push({ status: PLAYER_STATUS.PAUSED, timestamp: Math.floor(new Date().getTime() / 1000) });
  let index = data.findIndex((it) => it?.status === PLAYER_STATUS.PLAYING);
  if (index < 0) index = 0;

  while (index < data.length) {
    if (
      data?.[index]?.status === PLAYER_STATUS.PLAYING &&
      data?.[index + 1]?.status !== PLAYER_STATUS.PLAYING
    ) {
      totalPlayedDuration +=
        (data?.[index + 1]?.timestamp || data?.[index]?.timestamp || 0) -
        (data?.[index]?.timestamp || 0);
    }
    index += 2;
  }

  if (totalPlayedDuration < 0) {
    totalPlayedDuration = 0;
  }

  const resTime = +(totalPlayedDuration || 0).toFixed(2);
  return resTime;
};

export const getInfoVideoCodec = ({ player, playerName }: any) => {
  let getInfoTrack = null;
  if (player) {
    if (playerName === PLAYER_NAME.SHAKA_PLAYER) {
      getInfoTrack = (player.getVariantTracks() || []).find((item: any) => item?.active === true);
    } else {
      getInfoTrack = (player.levels || []).find(
        (item: any) => item?.level === player?.currentLevel
      );
    }
  }
  const videoCodec = (getInfoTrack?.videoCodec || '').split('.')[0];
  return { ...getInfoTrack, videoCodec };
};
export const calcTopTagForHBO = ({ warningLocation }: any) => {
  const videoInfo = getVideoInfo();
  if (videoInfo && warningLocation) {
    const { x, y } = warningLocation;
    const { bbLeft, bbTop, realW, realH } = videoInfo;
    return {
      left: `${(bbLeft + ((x + 5) / 1920) * realW).toFixed(2)}px`,
      top: `${(bbTop + ((y + 16) / 1080) * realH).toFixed(2)}px`
    };
  }
  return null;
};

export const checkSigmaLoaded = () => {
  if (typeof window === 'undefined') return true;
  const existingScript = document.querySelector('script[src*="sigma_packer_104.js"]');
  return !!existingScript;
};

export const setPlayerErrorLog = ({ errorDetail, isReset }: any) => {
  if (typeof window !== 'undefined') {
    if (isReset) window.linkPlayDetail = [];
    else {
      if (!window.linkPlayDetail) window.linkPlayDetail = [];
      if (errorDetail) {
        if (errorDetail?.data) {
          window.linkPlayDetail.push({
            ...errorDetail,
            data: JSON.stringify(errorDetail?.data || {})
          });
        }
      }
    }
  }
};

export const getVideoInfo = () => {
  const video: any = document.getElementById(EL_ID.VIE_PLAYER);
  if (isEmpty(video)) return null;
  const { videoWidth, videoHeight, clientWidth, clientHeight } = video || {};
  const ratio = videoWidth / videoHeight;
  const videoRatio = clientWidth / clientHeight;

  const blackBar = videoRatio > ratio ? 'left_right' : 'top_bottom';
  let bbLeft = 0;
  let bbTop = 0;
  let realW = clientWidth;
  let realH = clientHeight;

  if (blackBar === 'left_right') {
    realW = realH * ratio;
    bbLeft = (clientWidth - realW) / 2;
  } else {
    realH = realW / ratio;
    bbTop = (clientHeight - realH) / 2;
  }
  return {
    bbLeft,
    bbTop,
    realW,
    realH,
    clientWidth,
    clientHeight
  };
};
