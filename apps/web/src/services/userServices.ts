import { setTableHeader } from '@helpers/common';
import ConfigUser from '@config/ConfigUser';
import { USER_TYPE, USER_TYPE_ENUM } from '@constants/constants';
import { TEXT } from '@constants/text';
import LocalStorage from '@config/LocalStorage';

class UserServices {
  static recordProgressOfUser({
    contentId,
    contentName,
    contentType,
    timeSecond,
    action,
    durationTime,
    usi,
    playTrial,
    videoCodec
  }: any) {
    const duration = Math.ceil(durationTime);
    const trackingDataLocal = window.localStorage.getItem(LocalStorage.PROGRESS_VOD);
    let trackingData = trackingDataLocal ? JSON.parse(trackingDataLocal) : [];
    let dataTime = [];
    if (!contentId || !(timeSecond >= 0) || (!action && !duration)) return;
    if (!Array.isArray(trackingData)) trackingData = [];
    const timestamp = Math.floor(new Date().getTime() / 1000);
    if (trackingData.length > 0) {
      const contentIndex = trackingData.findIndex((x: any) => x.content_id === contentId);
      if (contentIndex !== -1) {
        const dataRecord = trackingData[contentIndex];
        const idContentRecord = dataRecord?.content_id;
        const lastIndexRecord = dataRecord?.data?.length - 1;
        const lastDataRecord = dataRecord?.data?.[lastIndexRecord];
        if (
          lastDataRecord.progress === Math.floor(timeSecond) &&
          lastDataRecord.action === action
        ) {
          return;
        }
        if (Math.floor(timeSecond) === duration && action !== 'stop') {
          return;
        }
        if (
          lastDataRecord.progress === Math.floor(timeSecond) &&
          lastDataRecord.action === 'stop'
        ) {
          return;
        }
        if (action === 'stop') {
          for (let i = dataRecord.data.length - 1; i >= 0; i -= 1) {
            if (dataRecord.data[i].progress === Math.floor(timeSecond)) {
              dataRecord.data.splice(i, 1);
            }
          }
        }
        const dataPush = {
          progress: Math.floor(timeSecond),
          action,
          duration,
          timestamp
        };
        dataTime = dataRecord?.data || [];
        const dataTimeToSave = dataTime.concat(dataPush);
        const dataIndex = {
          content_id: idContentRecord,
          content_name: contentName,
          content_type: contentType,
          usi: usi || undefined,
          play_trial: playTrial,
          data: dataTimeToSave,
          videoCodec
        };
        trackingData[contentIndex] = dataIndex;
      } else {
        const data = {
          content_id: contentId,
          content_name: contentName,
          content_type: contentType,
          usi: usi || undefined,
          play_trial: playTrial,
          videoCodec,
          data: [
            {
              progress: Math.floor(timeSecond),
              action,
              duration,
              timestamp
            }
          ]
        };
        trackingData.push(data);
      }
    } else {
      const data = {
        content_id: contentId,
        content_name: contentName,
        content_type: contentType,
        usi: usi || undefined,
        play_trial: playTrial,
        videoCodec,
        data: [
          {
            progress: Math.floor(timeSecond),
            action,
            duration,
            timestamp
          }
        ]
      };
      trackingData.push(data);
    }
    window.localStorage.setItem(LocalStorage.PROGRESS_VOD, JSON.stringify(trackingData));
    return true;
  }

  static handleTrackingLog({ tracker, action, contentId, bufferData }: any) {
    if (!tracker || !action) return;
    if (action === ConfigUser.TRACKING.START) {
      tracker.start({ contentId, bufferData });
    } else if (action === ConfigUser.TRACKING.STOP) {
      tracker.stop();
    }
  }

  static parseTransactionDetailTable(data: any) {
    const headerData = [
      { key: 'name', label: TEXT.PACKAGE_NAME },
      { key: 'stt', label: 'STT' },
      { key: 'durationDisplay', label: TEXT.TERM, width: '100px' },
      { key: 'old_price', label: TEXT.WORTH, width: '100px' },
      { key: 'percent_discount', label: 'Ưu đãi', width: '50px' },
      { key: 'amount', label: 'Tổng tiền thanh toán', width: '180px' },
      { key: 'created_date', label: 'Thời gian giao dịch' },
      { key: 'status', label: 'Trạng thái' },
      { key: 'txn_ref', label: TEXT.TRANSACTION_CODE, className: 'code' },
      { key: 'service_name', label: 'Hình thức thanh toán' }
    ];
    const firstItem = data?.[0];
    const header = setTableHeader(headerData, firstItem);
    header.unshift({ key: 'stt', label: 'STT', width: '50px' });
    return { header, body: data };
  }

  static parseUserType(type: any) {
    switch (type) {
      case USER_TYPE.NON_VIP:
        return USER_TYPE_ENUM.NON_VIP;
      case USER_TYPE.VIP:
        return USER_TYPE_ENUM.VIP;
      case USER_TYPE.VIP_HBO:
        return USER_TYPE_ENUM.HBO;
      case USER_TYPE.VIP_K_PLUS:
        return USER_TYPE_ENUM.K_PLUS;
      case USER_TYPE.ALL_ACCESS:
        return USER_TYPE_ENUM.ALL_ACCESS;
      default:
        return USER_TYPE_ENUM.GUEST;
    }
  }
}

export default UserServices;
