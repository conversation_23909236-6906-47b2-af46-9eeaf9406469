import { TYPE_TRIGGER_AUTH } from '@constants/types';
import {
  CONTENT_TYPE,
  LOGIN_TYPE,
  PAGE,
  PERMISSION,
  POPUP,
  POPUP_ACTION_TYPE
} from '@constants/constants';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import LocalStorage from '@config/LocalStorage';

function parsePopupParams({
  permission,
  isAddMyList,
  isSubscribeComingSoon,
  contentType,
  profile,
  currentProfile,
  forceLogin,
  isEndSVodTrial,
  isVip,
  groupPackageId,
  isMovieTrialInApp,
  isTriggerToApp,
  trialDuration,
  numberTrialEpisode,
  type,
  isMWebToApp,
  isMobile,
  isPremiumTVod,
  isTrigger = false,
  isGlobal = false,
  isTriggerEngagement,
  isPremiumPVodHaveSVod,
  isPremiumPVodNotSVod,
  contentDetail,
  router
}: any) {
  let popupName = '';
  let authTrigger = '';
  let action = {};
  const triggerAfterLogin = ConfigLocalStorage.get(LocalStorage.TRIGGER_AFTER_LOGIN);
  if (triggerAfterLogin === LOGIN_TYPE.FIRST_LOGIN) return { popupName, action, authTrigger };
  if (permission === PERMISSION.NON_LOGIN) {
    popupName = `anonymous-user_package${groupPackageId}_dialog`;
    if (profile?.id && !currentProfile?.id) {
      popupName = '';
    } else {
      switch (contentType) {
        case CONTENT_TYPE.EPISODE:
        case CONTENT_TYPE.SEASON:
        case CONTENT_TYPE.MOVIE:
          if (isGlobal) popupName = POPUP.NAME.NON_LOGIN_TRIAL_GLOBAL;
          if (isAddMyList) {
            popupName = '';
            authTrigger = TYPE_TRIGGER_AUTH.ADD_TO_LIST;
          } else if (isSubscribeComingSoon) {
            popupName = '';
            authTrigger = TYPE_TRIGGER_AUTH.REMIND_ME;
          }
          if (
            forceLogin === PERMISSION.FORCE_LOGIN ||
            (!isVip && !isAddMyList && !isSubscribeComingSoon)
          ) {
            if (!isGlobal) {
              authTrigger = '';
              if (isTrigger) {
                if (!isVip) {
                  // authTrigger = TYPE_TRIGGER_AUTH.REQUEST_REGISTER_CONVERSION;
                  popupName = '';
                } else {
                  popupName = POPUP.NAME.SVOD_TRIGGER;
                }
              } else {
                popupName = POPUP.NAME.REQUEST_REGISTER_CONVERSION;
                if (forceLogin === PERMISSION.FORCE_LOGIN) {
                  popupName = POPUP.NAME.PLAYER_TRIGGER_AUTH;
                  const basePath = window.location.pathname;

                  const newUrl = `${basePath}?trigger=${
                    TYPE_TRIGGER_AUTH.CONTENT
                  }&isTriggerAuth=true&destination=${decodeURIComponent(basePath)}`;

                  localStorage.setItem('currentAuthFlow', 'registration_trigger');

                  ConfigLocalStorage.set(
                    LocalStorage.RE_LOGIN_PARAMS,
                    JSON.stringify({
                      contentData: contentDetail,
                      pathname: router?.pathname,
                      url: newUrl
                    })
                  );
                }
              }
            }
          }
          if (isTriggerToApp && isMobile) {
            if (
              (!trialDuration && type === CONTENT_TYPE.MOVIE) ||
              (!numberTrialEpisode &&
                (type === CONTENT_TYPE.EPISODE || type === CONTENT_TYPE.SEASON))
            ) {
              popupName = POPUP.NAME.MOBILE_WEB_ONLY_APP;
            }
          }
          if (isEndSVodTrial) {
            if (isMovieTrialInApp) popupName = POPUP.NAME.CONVERT_MOBILE_WEB_TO_APP;
            else popupName = POPUP.NAME.NON_LOGIN_END_SVOD_TRIAL;
          }
          if (isTriggerEngagement) {
            popupName = POPUP.NAME.ENGAGEMENT_DIALOG;
          }
          if (isPremiumPVodNotSVod) {
            popupName = POPUP.NAME.PVOD_REGISTER_VIP;
          }
          break;
        case CONTENT_TYPE.LIVE_TV:
        case CONTENT_TYPE.EPG:
          if (isAddMyList) {
            popupName = '';
            authTrigger = TYPE_TRIGGER_AUTH.ADD_TO_LIST;
          } else if (isSubscribeComingSoon) {
            popupName = '';
            authTrigger = TYPE_TRIGGER_AUTH.LIVE_TV_COMING_SOON;
            localStorage.setItem('currentAuthFlow', 'registration_for_livetv_coming_soon');
          } else if (!groupPackageId) {
            popupName = POPUP.NAME.REQUEST_REGISTER_CONVERSION;
            if (forceLogin === PERMISSION.FORCE_LOGIN) {
              localStorage.setItem('currentAuthFlow', 'registration_trigger');
              popupName = POPUP.NAME.PLAYER_TRIGGER_AUTH;
              const basePath = window.location.pathname;

              const newUrl = `${basePath}?trigger=${
                TYPE_TRIGGER_AUTH.CONTENT
              }&isTriggerAuth=true&destination=${decodeURIComponent(basePath)}`;

              ConfigLocalStorage.set(
                LocalStorage.RE_LOGIN_PARAMS,
                JSON.stringify({
                  contentData: contentDetail,
                  pathname: router?.pathname,
                  url: newUrl
                })
              );
            }
          }
          break;
        case CONTENT_TYPE.LIVESTREAM:
          if (isSubscribeComingSoon) {
            popupName = '';
            authTrigger = TYPE_TRIGGER_AUTH.LIVESTREAM_COMING_SOON;
            localStorage.setItem('currentAuthFlow', 'registration_for_livestream_coming_soon');
          } else if (!groupPackageId) {
            popupName = POPUP.NAME.REQUEST_REGISTER_CONVERSION;
            if (forceLogin === PERMISSION.FORCE_LOGIN) {
              localStorage.setItem('currentAuthFlow', 'registration_trigger');
              popupName = POPUP.NAME.PLAYER_TRIGGER_AUTH;
              const basePath = window.location.pathname;

              const newUrl = `${basePath}?trigger=${
                TYPE_TRIGGER_AUTH.CONTENT
              }&isTriggerAuth=true&destination=${decodeURIComponent(basePath)}`;

              ConfigLocalStorage.set(
                LocalStorage.RE_LOGIN_PARAMS,
                JSON.stringify({
                  contentData: contentDetail,
                  pathname: router?.pathname,
                  url: newUrl
                })
              );
            }
          }
          break;
        default:
          break;
      }
    }
  } else if (permission === PERMISSION.CAN_WATCH) {
    if (!profile?.id && isSubscribeComingSoon && contentType === CONTENT_TYPE.EPG) {
      authTrigger = TYPE_TRIGGER_AUTH.LIVE_TV_COMING_SOON;
    }
    if (
      (isTriggerToApp &&
        isMobile &&
        ((!numberTrialEpisode && (type === CONTENT_TYPE.SEASON || type === CONTENT_TYPE.EPISODE)) ||
          (!trialDuration && type === CONTENT_TYPE.MOVIE))) ||
      isMWebToApp
    ) {
      popupName = POPUP.NAME.MOBILE_WEB_ONLY_APP;
    }
    if (isEndSVodTrial) {
      if (isMovieTrialInApp) popupName = POPUP.NAME.CONVERT_MOBILE_WEB_TO_APP;
    }
  } else if (permission === PERMISSION.PAYMENT) {
    action = { type: POPUP_ACTION_TYPE.BUY_PACKAGE };
    popupName = `user${profile?.type || 0}_package${groupPackageId}_dialog`;

    if (currentProfile?.isKid) {
      popupName = isPremiumPVodHaveSVod
        ? POPUP.NAME.KID_ACCESS_PVOD
        : isPremiumTVod
        ? POPUP.NAME.KID_ACCESS_TVOD
        : POPUP.NAME.KID_ACCESS_SVOD;
    } else if (profile?.id && !currentProfile?.id) {
      // popupName = '';
    } else if (isSubscribeComingSoon) {
      popupName = '';
    } else {
      switch (contentType) {
        case CONTENT_TYPE.EPISODE:
        case CONTENT_TYPE.SEASON:
        case CONTENT_TYPE.MOVIE:
          if (isGlobal) popupName = POPUP.NAME.USER_VOD_TRIAL_GLOBAL;
          if (isTriggerToApp && isMobile) {
            if (
              (!trialDuration && type === CONTENT_TYPE.MOVIE) ||
              (!numberTrialEpisode &&
                (type === CONTENT_TYPE.EPISODE || type === CONTENT_TYPE.SEASON))
            ) {
              popupName = POPUP.NAME.MOBILE_WEB_ONLY_APP;
            }
          }
          if (isEndSVodTrial) {
            if (isMovieTrialInApp) popupName = POPUP.NAME.CONVERT_MOBILE_WEB_TO_APP;
            else popupName = POPUP.NAME.LOGIN_NON_VIP_END_SVOD_TRIAL;
          }
          if (isTriggerEngagement) {
            popupName = POPUP.NAME.ENGAGEMENT_DIALOG;
          }
          if (isPremiumPVodHaveSVod) {
            popupName = POPUP.NAME.PVOD_REGISTER;
          }
          if (isPremiumPVodNotSVod) {
            popupName = POPUP.NAME.PVOD_REGISTER_VIP;
          }
          break;
        default:
          break;
      }
    }
  } else if (permission === PERMISSION.LIMITED_DEVICE) {
    popupName = POPUP.NAME.LIMIT_CCU;
  } else if (permission === PERMISSION.DONT_ALLOW_BROADCAST) {
    popupName = POPUP.NAME.LIMIT_EPG;
  } else if (permission === PERMISSION.KID_LIMITED) {
    popupName = POPUP.NAME.KID_LIMITED_CONTENT_DIALOG;
  } else if (permission === PERMISSION.CONTENT_RESCTRICTED) {
    popupName = POPUP.NAME.CONTENT_RESCTRICTED;
  }
  return { popupName, action, authTrigger };
}

const setOverflow = ({ isOverflow }: any) => {
  const htmlEl: any = document.getElementsByTagName('html')[0];
  if (typeof document === 'undefined' || !htmlEl) {
    return;
  }
  if (isOverflow) {
    htmlEl.classList.add('overflow');
    htmlEl.style.paddingRight = '0.625rem';
  } else {
    htmlEl.classList.remove('overflow');
    htmlEl.style.paddingRight = 0;
  }
};

const handleCheckLocalGlobal = (pathName: any) => {
  switch (pathName) {
    case PAGE.LINK:
    case PAGE.COPY_RIGHT:
    case PAGE.INTRODUCE:
    case PAGE.USAGE:
    case PAGE.REGULATION:
    case PAGE.POLICY_CANCELLATION:
    case PAGE.ANNOUNCEMENT:
    case PAGE.FAQS:
    case PAGE.AGREEMENT:
    case PAGE.INTRO_SERVICE:
    case PAGE.PAYMENT_POLICY:
    case PAGE.PRIVATE_POLICY:
    case PAGE.USAGE_V1:
    case PAGE.USAGE_V2:
    case PAGE.ZALOPAY_AGREEMENT:
    case PAGE.SERVICE_PACK:
      return false;
    default:
      return true;
  }
};

export { parsePopupParams, handleCheckLocalGlobal, setOverflow };
