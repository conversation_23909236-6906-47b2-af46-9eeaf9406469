# VieON Monorepo Migration Report

Generated: Mon Jun 23 14:26:29 +07 2025

## Executive Summary

- **Total files to migrate**: 660
- **Shared components**: 0
- **Migration tasks**: 9
- **Estimated total hours**: 64

## Component Analysis

### Shared Components (0)


### Web-only Components (62)
- Checkbox
- Comment
- ContentImpressionTracker
- CopyText
- DownloadApp
- Icons
- InputCode
- MasterBanner
- MastheadAiAds
- PricePopupInfo
- RapVietTopSong
- VieLink
- Voucher
- emailVerify
- empty
- footer
- liveTV
- payment
- popup
- profile
...

### Smart-TV-only Components (21)
- Ads
- AwayOnTrigger
- BillBoardLuna
- CheckboxFocusable
- Common
- Debug
- DialogCommon
- DialogV2
- HOC
- InfinityList
- Motions
- PinInput
- Player
- RibbonLuna
- Screensaver
- ScrollList
- Sport
- SubMenu
- TVODDialog
- Tracking
...

## Migration Tasks by Package

### @vieon/ui-kits


### @vieon/player
- Migrate HLS.js integration (8h)
- Migrate player controls (6h)
- Migrate subtitle handling (4h)

### @vieon/payment
- Migrate payment gateways (12h)
- Migrate billing components (6h)

### @vieon/auth
- Migrate authentication logic (8h)
- Migrate multi-profile system (10h)

### @vieon/tracking
- Migrate Google Analytics (4h)
- Migrate custom tracking events (6h)

## Files to Migrate

### Player-related Files
- web: apps/web/src/apis/userApi.ts
- web: apps/web/src/config/ConfigDMP.ts
- web: apps/web/src/config/ConfigSegment.ts
- web: apps/web/src/config/ConfigApi.ts
- web: apps/web/src/config/ConfigGA.ts
- web: apps/web/src/constants/text.ts
- web: apps/web/src/constants/player.ts
- web: apps/web/src/constants/constants.ts
- web: apps/web/src/models/contentDetail.ts
- web: apps/web/src/models/PopupItem.ts
...

### Payment-related Files
- web: apps/web/src/apis/Payment.ts
- web: apps/web/src/apis/PaymentV2.ts
- web: apps/web/src/apis/tpbank/tpbankApi.ts
- web: apps/web/src/apis/billing/BillingApi.ts
- web: apps/web/src/apis/billing/BillingInfo.ts
- web: apps/web/src/config/ConfigGAPayment.ts
- web: apps/web/src/config/ConfigPayment.ts
- web: apps/web/src/models/payment.ts
- web: apps/web/src/models/tpbank.ts
- web: apps/web/src/components/home/<USER>
...

### Auth-related Files
- web: apps/web/src/apis/MultiProfile/index.ts
- web: apps/web/src/models/login.ts
- web: apps/web/src/models/LoginResponse.ts
- web: apps/web/src/models/Profile.ts
- web: apps/web/src/models/register.ts
- web: apps/web/src/components/popup/PopupTriggerAuth.tsx
- web: apps/web/src/components/popup/PopupDeleteProfile.tsx
- web: apps/web/src/components/popup/PopupPVodRegister.tsx
- web: apps/web/src/components/payment/Step2/FormLogin/InputCustomPayment.tsx
- web: apps/web/src/components/payment/Step2/FormLogin/LoginPayment.tsx
...

### Tracking-related Files
- web: apps/web/src/apis/aiactiv-third-tracking.ts
- web: apps/web/src/apis/cm/TagApi.ts
- web: apps/web/src/apis/billing/BillingApi.ts
- web: apps/web/src/config/ConfigGTM.ts
- web: apps/web/src/config/ConfigGAPayment.ts
- web: apps/web/src/config/ConfigSegment.ts
- web: apps/web/src/config/ConfigApi.ts
- web: apps/web/src/config/ConfigGA.ts
- web: apps/web/src/script/MoEngage.ts
- web: apps/web/src/script/GGAdsense.ts
...

## Recommendations

1. **Start with @vieon/types**: Migrate shared types first
2. **Focus on @vieon/ui-kits**: High reusability across apps
3. **Prioritize @vieon/player**: Core functionality
4. **Gradual migration**: One package at a time
5. **Maintain backward compatibility**: During transition period

## Risk Assessment

- **High Risk**: Player and Auth modules (core functionality)
- **Medium Risk**: Payment module (business critical)
- **Low Risk**: UI-kits and Tracking modules

## Next Steps

1. Review this migration report
2. Assign tasks to team members
3. Start with @vieon/types package
4. Implement shared components in @vieon/ui-kits
5. Begin player module migration
