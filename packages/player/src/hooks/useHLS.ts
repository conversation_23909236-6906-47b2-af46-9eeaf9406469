import { useRef, useEffect, useState, useCallback } from 'react';
import Hls from 'hls.js';

export interface HLSConfig {
  autoStartLoad?: boolean;
  startPosition?: number;
  debug?: boolean;
  enableWorker?: boolean;
  lowLatencyMode?: boolean;
  backBufferLength?: number;
}

export interface HLSLevel {
  index: number;
  bitrate: number;
  width: number;
  height: number;
  codecs: string;
  url: string;
}

export const useHLS = (src: string, config?: HLSConfig) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<Hls | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | Error | null>(null);
  const [levels, setLevels] = useState<HLSLevel[]>([]);
  const [currentLevel, setCurrentLevel] = useState(-1);
  const [isLive, setIsLive] = useState(false);

  const switchLevel = useCallback((levelIndex: number) => {
    if (hlsRef.current) {
      hlsRef.current.currentLevel = levelIndex;
      setCurrentLevel(levelIndex);
    }
  }, []);

  const destroy = useCallback(() => {
    if (hlsRef.current) {
      hlsRef.current.destroy();
      hlsRef.current = null;
    }
    setIsLoaded(false);
    setError(null);
    setLevels([]);
    setCurrentLevel(-1);
    setIsLive(false);
  }, []);

  useEffect(() => {
    const video = videoRef.current;
    if (!video || !src) return;

    setError(null);
    setIsLoaded(false);

    if (Hls.isSupported()) {
      const hls = new Hls({
        enableWorker: config?.enableWorker ?? true,
        lowLatencyMode: config?.lowLatencyMode ?? false,
        backBufferLength: config?.backBufferLength ?? 90,
        autoStartLoad: config?.autoStartLoad ?? true,
        startPosition: config?.startPosition ?? -1,
        debug: config?.debug ?? false,
      });

      hlsRef.current = hls;

      hls.loadSource(src);
      hls.attachMedia(video);

      hls.on(Hls.Events.MANIFEST_PARSED, (event, data) => {
        setIsLoaded(true);
        setIsLive(hls.liveSyncPosition !== undefined);
        
        const hlsLevels: HLSLevel[] = hls.levels.map((level, index) => ({
          index,
          bitrate: level.bitrate,
          width: level.width,
          height: level.height,
          codecs: level.codecSet,
          url: level.url[0],
        }));
        
        setLevels(hlsLevels);
        setCurrentLevel(hls.currentLevel);
      });

      hls.on(Hls.Events.LEVEL_SWITCHED, (event, data) => {
        setCurrentLevel(data.level);
      });

      hls.on(Hls.Events.ERROR, (event, data) => {
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              setError('Network error occurred');
              hls.startLoad();
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              setError('Media error occurred');
              hls.recoverMediaError();
              break;
            default:
              setError(`Fatal error: ${data.details}`);
              hls.destroy();
              break;
          }
        } else {
          console.warn('HLS non-fatal error:', data);
        }
      });

    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      video.src = src;
      setIsLoaded(true);
    } else {
      setError('HLS is not supported in this browser');
    }

    return destroy;
  }, [src, config, destroy]);

  return {
    videoRef,
    hlsRef,
    isLoaded,
    error,
    levels,
    currentLevel,
    isLive,
    switchLevel,
    destroy,
    isSupported: Hls.isSupported(),
  };
};
