import { useEffect } from "react";
import { ViePlayerEventKey, ViePlayerEventType } from "../../constants";
import { ViePlayerSingleton } from "./ViePlayer";

type Events = {
  [key in ViePlayerEventType]?: ViePlayerEventKey;
};

interface Props {
  player: ViePlayerSingleton;
  events: Events;
}

const useEventPlayer = (
  { player, events }: Props,
  dependencies: any[] = []
) => {
  useEffect(() => {
    Object.keys(events).forEach((type) => {
      const eventKey = type as ViePlayerEventType;
      player.addEventListener(eventKey, events[eventKey] as ViePlayerEventKey);
    });

    return () => {
      Object.keys(events).forEach((type) => {
        const eventKey = type as ViePlayerEventType;
        player.removeEventListener(
          eventKey,
          events[eventKey] as ViePlayerEventKey
        );
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dependencies);
};

export default useEventPlayer;
