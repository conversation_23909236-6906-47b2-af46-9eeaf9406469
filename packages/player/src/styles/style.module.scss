/// @param palettes
@use "src/assets/scss/settings/palettes" as pales;

/// @group function
@use "src/assets/scss/settings/function" as fn;

/// @group variables
@use "src/assets/scss/settings/variables" as var;

.warning {
  display: flex;
  width: 100%;
  height: 100vh;
  position: absolute;
  top: 0;
  background-color: #010101;
}

.warning__area {
  display: flex;
  width: fn.percent-unit(716);
  align-items: center;
  align-self: center;
  justify-content: center;
  flex-direction: column;
  margin: 0 auto;
}

.warning__title {
  font-size: fn.percent-unit(48);
  font-weight: 500;
  line-height: fn.percent-unit(56);
  color: pales.$white;
  margin-bottom: fn.percent-unit(36);
}
.warning__description {
  font-size: fn.percent-unit(28px);
  line-height: fn.percent-unit(150%);
  color: pales.$gray-de;
  text-align: center;
}
