/// @param palettes
@use "src/assets/scss/settings/palettes" as pales;

/// @group function
@use "src/assets/scss/settings/function" as fn;

/// @group variables
@use "src/assets/scss/settings/variables" as var;

.warning__message {
  display: flex;
  max-height: fn.percent-unit(40px);
  position: absolute;
  background-color: rgba(0, 0, 0, 0.15);
  &::before {
    content: "";
    display: block;
    position: absolute;
    width: fn.percent-unit(3px);
    background-color: #3ac882;
    top: 0;
    bottom: 0;
  }

  &.top_left {
    top: fn.percent-unit(16px);
    left: fn.percent-unit(16px);
  }

  &.top_left_hbo {
    top: fn.percent-unit(121px);
    left: fn.percent-unit(16px);
  }

  &.top_right {
    top: fn.percent-unit(60px);
    right: fn.percent-unit(60px);
  }
  &.bottom_right {
    bottom: fn.percent-unit(60px);
    right: fn.percent-unit(60px);
  }

  .inner {
    padding-left: fn.percent-unit(11px);
    padding-top: fn.percent-unit(4px);
    padding-bottom: fn.percent-unit(4px);
    padding-right: fn.percent-unit(8px);
    align-items: center;
    .area {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      .title {
        color: #fff;
        font-family: Roboto;
        font-size: fn.percent-unit(28px);
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        height: fn.percent-unit(32px);
        text-shadow: 0px 0px 3px rgba(0, 0, 0, 0.5);
      }
      .description {
        color: #fff;
        font-family: Roboto;
        font-size: fn.percent-unit(24px);
        font-style: normal;
        font-weight: 400;
        line-height: 1;
        white-space: nowrap;
        opacity: 0;
        margin-top: fn.percent-unit(4px);
        max-width: fn.percent-unit(0px);
        text-shadow: 0px 0px 3px rgba(0, 0, 0, 0.5);
      }
    }
  }

  &.show {
    animation-name: warningMessageShow;
    animation-duration: 2s;
    max-height: fn.percent-unit(97px);
    .inner {
      .area {
        .description {
          opacity: 1;
          animation-name: faceIn;
          animation-duration: 3s;
          max-width: fn.percent-unit(1591px);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
  &.hiden {
    animation-name: warningMessageHiden;
    animation-duration: 2s;
    max-height: fn.percent-unit(40px);
    .inner {
      .area {
        .description {
          opacity: 0;
          animation-name: faceOut;
          animation-duration: 3s;
          max-width: fn.percent-unit(0px);
        }
      }
    }
  }
}

@keyframes warningMessageShow {
  0% {
    max-height: fn.percent-unit(40px);
  }
  100% {
    max-height: fn.percent-unit(97px);
  }
}

@keyframes warningMessageHiden {
  0% {
    max-height: fn.percent-unit(97px);
  }
  100% {
    max-height: fn.percent-unit(40px);
  }
}

@keyframes faceIn {
  0% {
    opacity: 0;
    max-width: fn.percent-unit(0px);
  }
  100% {
    opacity: 1;
    max-width: fn.percent-unit(1591px);
  }
}

@keyframes faceOut {
  0% {
    opacity: 1;
    max-width: fn.percent-unit(1591px);
  }
  100% {
    opacity: 0;
    max-width: fn.percent-unit(0px);
  }
}
