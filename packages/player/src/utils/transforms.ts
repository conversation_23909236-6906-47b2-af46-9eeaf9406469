import platform from "services/platform";
import { LinkPlays } from "types/endpoint";
import { convertTimeToHHMMSSDDMMYYYY } from "app/utils/formatTime";
import { ViePlayerErrorDetail } from "./ViePlayer";

export const playerInfoDefault = {
  codec: null,
  playerErrorCode: "Unknown",
  profileHeightText: null,
  profileHeight: null,
  playerStreamingProtocol: null,
};

export function initViePlayerErrorDetailEmptyLink({
  timestamp,
}: {
  timestamp: number;
}): ViePlayerErrorDetail {
  return {
    player: "Shaka",
    linkPlay: "",
    currentTime: 0,
    severity: "2",
    errorCode: "EMPTY_LINK",
    errorMessage: "EMPTY_LINK",
    timestamp,
    date: convertTimeToHHMMSSDDMMYYYY(timestamp.toString()),
    drmType: "",
    detail: JSON.stringify({
      code: "EMPTY_LINK",
      message: "EMPTY_LINK",
    }),
  };
}

export function setupLinkPlayHls(playLinks: LinkPlays) {
  const listLinkPlays = [];
  if (platform.supportH265) {
    if (playLinks.h265.hls !== "") {
      listLinkPlays.push({
        link: playLinks.h265.hls,
        playerShaka: false,
      });
    }
  }
  if (playLinks.h264.hls !== "") {
    listLinkPlays.push({
      link: playLinks.h264.hls,
      playerShaka: false,
    });
  }
  if (playLinks.h264.hls_backup_1 !== "") {
    listLinkPlays.push({
      link: playLinks.h264.hls_backup_1,
      playerShaka: false,
    });
  }
  if (playLinks.h264.hls_backup_2 !== "") {
    listLinkPlays.push({
      link: playLinks.h264.hls_backup_2,
      playerShaka: false,
    });
  }
  return listLinkPlays;
}

export function setupLinkPlayDash(playLinks: LinkPlays, isDRM: boolean) {
  const listLinkPlays = [];
  if (platform.supportH265) {
    if (playLinks.h265.dash !== "") {
      listLinkPlays.push({
        link: playLinks.h265.dash,
        playerShaka: true,
      });
    }
  }
  if (playLinks.h264.dash !== "") {
    listLinkPlays.push({
      link: playLinks.h264.dash,
      playerShaka: true,
    });
  }
  if (playLinks.h264.dash_backup_1 !== "") {
    listLinkPlays.push({
      link: playLinks.h264.dash_backup_1,
      playerShaka: true,
    });
  }
  if (playLinks.h264.dash_backup_2 !== "") {
    listLinkPlays.push({
      link: playLinks.h264.dash_backup_2,
      playerShaka: true,
    });
  }
  return listLinkPlays;
}

export function setupLinkPlay(playLinks: LinkPlays, isDRM: boolean) {
  let listLinkPlays = setupLinkPlayDash(playLinks, isDRM);
  if (!isDRM) {
    if (listLinkPlays.length === 0) {
      listLinkPlays = setupLinkPlayHls(playLinks);
    } else {
      // LG SS 2017 or old play hls
      if (playLinks.h264.hls !== "") {
        listLinkPlays.push({
          link: playLinks.h264.hls,
          playerShaka: false,
        });
      }
    }
  }
  return listLinkPlays;
}
