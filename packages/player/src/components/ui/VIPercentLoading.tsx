import React from "react";

interface Props {
  isShow: boolean;
}

export class VIPercentLoading extends React.PureComponent<Props> {
  loading: HTMLDivElement | null = null;

  percentLoading: HTMLSpanElement | null = null;

  percentLoadingNumber: number = 0;

  constructor(props: Props) {
    super(props);
  }

  componentDidMount() {
    this.showPercentLoading(0);
    this.setPercentLoading(this.randomNumber(10) + 10);
  }

  componentDidUpdate() {
    if (this.props.isShow) {
      this.showPercentLoading(0);
      this.setPercentLoading(this.randomNumber(10) + 10);
      if (this.loading) {
        this.loading.classList.remove("hide");
      }
    } else {
      this.showPercentLoading(100);
      setTimeout(() => {
        if (this.loading) {
          this.loading.classList.add("hide");
        }
        this.showPercentLoading(0);
      }, 100);
    }
  }

  componentWillUnmount() {
    this.showPercentLoading(100);
  }

  showPercentLoading = (percent: number) => {
    this.percentLoadingNumber = percent;
    if (this.percentLoading) {
      this.percentLoading.innerHTML = `${percent}%`;
    }
  };

  setPercentLoading = (n: number) => {
    if (!this.props.isShow) {
      return;
    }
    if (n > this.percentLoadingNumber) {
      this.showPercentLoading(n);
    }
    this.randomPercentLoading();
  };

  randomPercentLoading = () => {
    if (this.percentLoadingNumber < 99) {
      let time = this.randomNumber(5);
      time *= 500;
      time = Math.max(500, time);
      setTimeout(() => {
        let random = this.randomNumber(20);
        random = Math.min(this.percentLoadingNumber + random, 99);
        this.setPercentLoading(random);
      }, time);
    }
  };

  randomNumber = (n: number) => {
    return Math.floor(Math.random() * Math.floor(n));
  };

  render() {
    return (
      <div
        id="loading-section"
        className="spinner-wrapper hide"
        ref={(el) => (this.loading = el)}
        style={{
          width: "100%",
          height: "100%",
          position: "absolute",
          top: "0",
          bottom: "0",
          left: "0",
          right: "0",
        }}
      >
        <div className="spinner animate-spin" />
        <span
          className="spinner-percent"
          ref={(el) => (this.percentLoading = el)}
        />
      </div>
      // <div id="loading-section" ref={(el) => (this.loading = el)} className={'hide'}>
      //     <div className="loader" style={{width: '6em'}}>
      //         <svg className="circular" viewBox="25 25 50 50">
      //             <circle className="path" cx="50" cy="50" fill="none" r="20" strokeMiterlimit="10" strokeWidth="2" />
      //         </svg>
      //         <span ref={(el) => (this.percentLoading = el)} style={{position: 'absolute', transform: 'translate(-50%, -50%)', left: '50%', top: '50%',color: '#3ac88a', fontSize: '1.75em', fontWeight: 'bold'}}/>
      //     </div>
      // </div>
    );
  }
}
