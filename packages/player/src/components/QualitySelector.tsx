import React, { useState, useRef, useEffect } from 'react';

export interface Quality {
  id: string;
  label: string;
  height: number;
  bitrate?: number;
}

export interface QualitySelectorProps {
  qualities: Quality[];
  currentQuality: string;
  onQualityChange: (qualityId: string) => void;
  disabled?: boolean;
  className?: string;
  autoLabel?: string;
}

export const QualitySelector: React.FC<QualitySelectorProps> = ({
  qualities,
  currentQuality,
  onQualityChange,
  disabled = false,
  className = '',
  autoLabel = 'Auto'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const currentQualityData = qualities.find(q => q.id === currentQuality);
  const displayLabel = currentQuality === 'auto' ? autoLabel : currentQualityData?.label || 'Unknown';

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleQualitySelect = (qualityId: string) => {
    onQualityChange(qualityId);
    setIsOpen(false);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      setIsOpen(!isOpen);
    } else if (event.key === 'Escape') {
      setIsOpen(false);
    }
  };

  if (qualities.length === 0) {
    return null;
  }

  return (
    <div 
      className={`quality-selector ${className} ${disabled ? 'disabled' : ''}`}
      ref={dropdownRef}
    >
      <button
        className={`quality-button ${isOpen ? 'open' : ''}`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        aria-label="Select video quality"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        <span className="quality-label">{displayLabel}</span>
        <svg 
          className={`quality-chevron ${isOpen ? 'rotated' : ''}`}
          width="16" 
          height="16" 
          viewBox="0 0 24 24" 
          fill="currentColor"
        >
          <path d="M7 10l5 5 5-5z" />
        </svg>
      </button>

      {isOpen && (
        <div className="quality-dropdown" role="listbox">
          {currentQuality !== 'auto' && (
            <button
              className={`quality-option ${currentQuality === 'auto' ? 'selected' : ''}`}
              onClick={() => handleQualitySelect('auto')}
              role="option"
              aria-selected={currentQuality === 'auto'}
            >
              <span className="quality-option-label">{autoLabel}</span>
              <span className="quality-option-description">Adaptive</span>
            </button>
          )}
          
          {qualities
            .sort((a, b) => b.height - a.height)
            .map(quality => {
              const isSelected = quality.id === currentQuality;
              const bitrateText = quality.bitrate 
                ? ` (${Math.round(quality.bitrate / 1000)}k)`
                : '';
              
              return (
                <button
                  key={quality.id}
                  className={`quality-option ${isSelected ? 'selected' : ''}`}
                  onClick={() => handleQualitySelect(quality.id)}
                  role="option"
                  aria-selected={isSelected}
                >
                  <span className="quality-option-label">{quality.label}</span>
                  {quality.height && (
                    <span className="quality-option-description">
                      {quality.height}p{bitrateText}
                    </span>
                  )}
                  {isSelected && (
                    <svg 
                      className="quality-check"
                      width="16" 
                      height="16" 
                      viewBox="0 0 24 24" 
                      fill="currentColor"
                    >
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                    </svg>
                  )}
                </button>
              );
            })
          }
        </div>
      )}
    </div>
  );
};
