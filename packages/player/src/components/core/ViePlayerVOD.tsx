import React from "react";
import {
  ViePlayerErrorType,
  ENV,
  checkTypeLink,
  playerInfoType,
} from "../../constants";
import QnetManager from "./QnetManager";
import SocketManager from "./SocketManager";
import ViePlayerBasic from "./ViePlayerBasic";

interface Props {
  env: ENV;

  timeProgress: number;
  fullProfile: boolean;
  showLoading: boolean;
  isLive: boolean;

  playerLogo?: string;
  playerLogo4K?: string;

  linkURL?: string[];

  socketInfo?: {
    host: string;
    port: number;
    tcp_port: number;
    token: string;
    channelId: string;
    contentName: string;
    content_type: number;
    drm_service_name: string;
  };

  qnet?: {
    operatorId: string;
    sessionId: string;
    userId: string;
    merchantId: string;
  };

  drm?: {
    userId: string;
    sessionId: string;
    merchantId: string;
  };

  sigmaDRM?: {
    userId: string;
    sessionId: string;
    merchantId: string;
    appId: string;
  };

  vPlayerSetPlayerRef: (ref: any) => void;
  vPlayerHandleOnPlay: () => void;
  vPlayerHandleOnBufferTime: (start: number) => void;
  vPlayerHandleOnEnded: () => void;
  vPlayerHandleOnError: (
    type: ViePlayerErrorType,
    timeError: number,
    message?: string,
    error?: any
  ) => void;
  vPlayerHandleOnTimeUpdate: () => void;
  vPlayerCurrentProfile: (width: number, bitrate: number) => void;
  vPlayerHandleOnReBufferTime: (timeBuffer: number) => void;
}

interface State {
  showPlayer: boolean;
}
class ViePlayerVOD extends React.PureComponent<Props, State> {
  isunmount: boolean = false;

  playerLoaded: boolean = false;

  playerMainRef: any;

  allLinkPlay: string[] = [];

  currentProgress: number = 0;

  dashConfig:
    | {
        url: string;
        drm?: {
          userId: string;
          sessionId: string;
          merchantId: string;
        };
        sigmaDRM?: {
          userId: string;
          sessionId: string;
          merchantId: string;
          appId: string;
        };
      }
    | undefined;

  hlsConfig: { url: string } | undefined;

  selectQualityWidth: number = 0;

  selectAudioName: string = "";

  selectSubName: string = "";

  selectShowLoading: boolean = false;

  selectShowInfo: boolean = false;

  constructor(props: Props) {
    super(props);
    this.state = {
      showPlayer: false,
    };

    this.allLinkPlay = [];
    this.currentProgress = this.props.timeProgress;
    this.dashConfig = undefined;
    this.hlsConfig = undefined;
    this.selectShowLoading = this.props.showLoading;
  }

  componentDidMount() {
    const { linkURL } = this.props;
    this.allLinkPlay = linkURL || [];
    if (this.allLinkPlay.length > 0) {
      this.connectSocket();
      this.initPlayer();
    } else {
      this.onError("error");
    }
  }

  componentWillUnmount() {
    this.isunmount = true;
    SocketManager.disConnetSocket();
    QnetManager.endStream();
  }

  // SOCKET
  connectSocket = () => {
    const { socketInfo } = this.props;
    if (socketInfo) {
      const option = {
        host: socketInfo.host,
        port: 0,
        tcp_port: 0,
        token: socketInfo.token,
        content_id: socketInfo.channelId,
        content_name: socketInfo.contentName,
        content_type: socketInfo.content_type,
        drm_service_name: socketInfo.drm_service_name,
      };
      SocketManager.connectSocket(option, this.socketStatus);
    }
  };

  socketStatus = (
    status: boolean,
    message?: string,
    type?: ViePlayerErrorType
  ) => {};

  // INIT PLAYER
  initPlayer = () => {
    const { qnet, env } = this.props;
    if (qnet) {
      QnetManager.pingQnet(
        env,
        qnet!.operatorId,
        qnet!.sessionId,
        () => {
          this.configLinkPlay();
        },
        (type, message) => {
          this.resetPlayer();
          this.onError(type, message);
        }
      );
    } else {
      this.configLinkPlay();
    }
  };

  configLinkPlay = () => {
    const { drm, sigmaDRM } = this.props;
    const url = this.allLinkPlay[0];
    this.allLinkPlay.shift();
    const type = checkTypeLink(url);
    switch (type) {
      case "dash":
        this.dashConfig = {
          url,
          drm,
          sigmaDRM,
        };
        this.setState({
          showPlayer: true,
        });
        break;
      case "hls":
        this.hlsConfig = {
          url,
        };
        this.setState({
          showPlayer: true,
        });
        break;
      default:
        this.onReloadLinkPlayBackup();
        break;
    }
  };

  resetPlayer = () => {
    this.setState({
      showPlayer: false,
    });
    this.hlsConfig = undefined;
    this.dashConfig = undefined;
  };

  // FUNCTION
  funcShowInfo = (show: boolean) => {
    this.selectShowInfo = show;
    if (
      this.playerMainRef &&
      this.playerMainRef.funcShowInfo &&
      typeof this.playerMainRef.funcShowInfo === "function"
    ) {
      this.playerMainRef.funcShowInfo(this.selectShowInfo);
    }
  };

  funcShowLoading = (show: boolean) => {
    this.selectShowLoading = show;
    if (
      this.playerMainRef &&
      this.playerMainRef.funcShowLoading &&
      typeof this.playerMainRef.funcShowLoading === "function"
    ) {
      this.playerMainRef.funcShowLoading(this.selectShowLoading);
    }
  };

  funcChangeQuality = (width: number) => {
    this.selectQualityWidth = width;
    if (
      this.playerMainRef &&
      this.playerMainRef.changeQuality &&
      typeof this.playerMainRef.changeQuality === "function"
    ) {
      this.playerMainRef.changeQuality(this.selectQualityWidth);
    }
  };

  funcChangeSubtitle = (name: string) => {
    this.selectSubName = name;
    if (
      this.playerMainRef &&
      this.playerMainRef.funcChangeSubtitle &&
      typeof this.playerMainRef.funcChangeSubtitle === "function"
    ) {
      this.playerMainRef.funcChangeSubtitle(this.selectSubName);
    }
  };

  funcChangeAudio = (name: string) => {
    this.selectAudioName = name;
    if (
      this.playerMainRef &&
      this.playerMainRef.changeAudio &&
      typeof this.playerMainRef.changeAudio === "function"
    ) {
      this.playerMainRef.changeAudio(this.selectAudioName);
    }
  };

  funcGetPlayerInfo = () => {
    let playerInfo: playerInfoType = {
      codec: null,
      profileHeight: null,
      profileHeightText: null,
      playerStreamingProtocol: "DASH",
    };
    if (
      this.playerMainRef &&
      this.playerMainRef.getPlayerInfo &&
      typeof this.playerMainRef.getPlayerInfo === "function"
    ) {
      playerInfo = this.playerMainRef.getPlayerInfo();
    }
    return playerInfo;
  };

  // EVENT PLAYER
  setVideoRef = (element: HTMLVideoElement) => {
    this.props.vPlayerSetPlayerRef(element);
  };

  playerHandleOnPlay = () => {
    if (!this.isunmount && this.playerLoaded === false) {
      this.props.vPlayerHandleOnPlay();
      this.playerLoaded = true;
    }
    this.funcChangeQuality(this.selectQualityWidth);
    this.funcChangeAudio(this.selectAudioName);
    this.funcChangeSubtitle(this.selectSubName);
    this.funcShowInfo(this.selectShowInfo);
    this.funcShowLoading(this.selectShowLoading);
  };

  playerHandleOnBufferTime = (start: number) => {
    if (!this.isunmount && this.playerLoaded === false) {
      this.props.vPlayerHandleOnBufferTime(start);
    }
  };

  playerHandleOnEnded = () => {
    if (!this.isunmount) {
      this.props.vPlayerHandleOnEnded();
    }
  };

  playerHandleOnTimeUpdate = () => {
    if (!this.isunmount) {
      this.props.vPlayerHandleOnTimeUpdate();
    }
  };

  playerHandleOnError = (timeError: number, error?: any) => {
    const { isLive } = this.props;
    if (isLive) {
      this.currentProgress = 0;
    } else {
      this.currentProgress = timeError;
    }
    this.onReloadLinkPlayBackup(error);
  };

  playerHandleOnCurrentProfile = (width: number, bitrate: number) => {
    if (!this.isunmount) {
      this.props.vPlayerCurrentProfile(width, bitrate);
    }
  };

  //
  onReloadLinkPlayBackup = (error?: any) => {
    this.resetPlayer();
    if (!this.isunmount) {
      if (this.allLinkPlay.length > 0) {
        setTimeout(() => {
          this.configLinkPlay();
        }, 200);
      } else {
        this.onError("error", "", error);
      }
    }
  };

  onError = (type: ViePlayerErrorType, message?: string, error?: any) => {
    SocketManager.disConnetSocket();
    QnetManager.endStream();
    this.props.vPlayerHandleOnError(type, this.currentProgress, message, error);
  };

  render() {
    const { showPlayer } = this.state;
    const { env, fullProfile, playerLogo, playerLogo4K, isLive } = this.props;
    return (
      <div className="player-wrap-animation fade-in">
        {showPlayer ? (
          <ViePlayerBasic
            env={env}
            timeProgress={this.currentProgress}
            fullProfile={fullProfile}
            isLive={isLive}
            showLoading={this.selectShowLoading}
            playerLogo={playerLogo}
            playerLogo4K={playerLogo4K}
            dash={this.dashConfig}
            hls={this.hlsConfig}
            vPlayerSetPlayerRef={this.setVideoRef}
            vPlayerHandleOnPlay={this.playerHandleOnPlay}
            vPlayerHandleOnBufferTime={this.playerHandleOnBufferTime}
            vPlayerHandleOnEnded={this.playerHandleOnEnded}
            vPlayerHandleOnError={this.playerHandleOnError}
            vplayerHandleOnTimeUpdate={this.playerHandleOnTimeUpdate}
            vPlayerCurrentProfile={this.playerHandleOnCurrentProfile}
            vPlayerHandleOnReBufferTime={this.props.vPlayerHandleOnReBufferTime}
            ref={(el) => (this.playerMainRef = el)}
          />
        ) : null}
      </div>
    );
  }
}
export default ViePlayerVOD;
