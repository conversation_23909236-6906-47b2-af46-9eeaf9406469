import React from "react";
import ReactDOM from "react-dom";
import set from "lodash/set";
import get from "lodash/get";
import {
  createViePlayerEvent,
  ViePlayerEventKey,
  ViePlayerStatus,
  ViePlayerType,
  ViePlayerEventType,
  ViePlayerErrorType,
  playerInfoType,
  ViePlayerErrorDetail,
} from "../../constants";
import ViePlayerTrailer from "./ViePlayerTrailer";
import ViePlayerVOD from "./ViePlayerVOD";
import ViePlayerLIVE from "./ViePlayerLIVE";
import ViePlayerImage from "./ViePlayerImage";
import {
  ViePlayerConfigImage,
  ViePlayerConfigLIVE,
  ViePlayerConfigTrailer,
  ViePlayerConfigVOD,
} from ".";

type PlayerStatus = "init" | "start" | "end" | "error" | "unknown";

export class ViePlayerSingleton {
  private mainDivElement: HTMLDivElement | undefined;

  private playerDivElement: HTMLDivElement | undefined;

  private infoDivElement: HTMLDivElement | undefined;

  private videoElement: HTMLVideoElement | undefined;

  private defaultStyle: Partial<CSSStyleDeclaration> = {};

  private listEventPlayerStateWarningScreen: ViePlayerEventKey[] = [];

  private listEventPlayerStart: ViePlayerEventKey[] = [];

  private listEventPlayerEnd: ViePlayerEventKey[] = [];

  private listEventPlayerBufferTime: ViePlayerEventKey[] = [];

  private listEventPlayerError: ViePlayerEventKey[] = [];

  private listEventPlayerTimeUpdate: ViePlayerEventKey[] = [];

  private listEventPlayerCurrentProfile: ViePlayerEventKey[] = [];

  private listEventPlayerBuffer: ViePlayerEventKey[] = [];

  private listEventPlayerEndBuffer: ViePlayerEventKey[] = [];

  private listEventPlayerReBufferTime: ViePlayerEventKey[] = [];

  playerType: ViePlayerType = ViePlayerType.NULL;

  playerStatus: PlayerStatus = "unknown";

  lastPlayerStatus: PlayerStatus = "unknown";

  private config: any;

  private playerMainRef: any;

  private configImage?: ViePlayerConfigImage;

  private playerImageRef: any;

  private isShowInfoPlayer: boolean = false;

  // Init
  private createPlayerDivElement = () => {
    this.playerDivElement = document.createElement("div") as HTMLDivElement;
  };

  private createInfoDivElement = () => {
    this.infoDivElement = document.createElement("div") as HTMLDivElement;
    this.infoDivElement.className = "v-player-poster";
  };

  constructor() {
    this.createPlayerDivElement();
    this.createInfoDivElement();
  }

  initPlayer = (mainDiv: HTMLDivElement) => {
    this.mainDivElement = mainDiv;
    if (this.playerDivElement) {
      this.mainDivElement.appendChild(this.playerDivElement);
    }
    if (this.infoDivElement) {
      this.mainDivElement.appendChild(this.infoDivElement);
    }
  };

  getId = () => {
    return get(this.config, "id", "") || "";
  };

  getStatusPlayer = () => {
    if (this.videoElement) {
      if (this.videoElement.paused) {
        return ViePlayerStatus.PAUSE;
      }
      return ViePlayerStatus.PLAY;
    }
    return ViePlayerStatus.DESTROY;
  };

  // check whether player is loading content or not
  canPlay = () => {
    if (this.videoElement) {
      if (this.videoElement.readyState === 4) {
        return true;
      }
    }
    return false;
  };

  // Event Player
  private playerHandleVideoRef = (el: HTMLVideoElement) => {
    this.videoElement = el;
  };

  private playerHandleOnPlay = () => {
    this.playerStatus = "start";
    this.listEventPlayerStart.forEach((element) => {
      const event = createViePlayerEvent(ViePlayerEventType.PLAYER_START);
      event.type = this.playerType;
      event.option = {
        currentTime: this.videoElement?.currentTime || 0,
        duration: this.videoElement?.duration || 0,
      };
      element(event);
    });
  };

  private playerHandleOnStateWarrningScreen = (state: boolean) => {
    this.listEventPlayerStateWarningScreen.forEach((element) => {
      const event = createViePlayerEvent(
        ViePlayerEventType.PLAYER_STATE_WARNING_SCREEN
      );
      event.type = this.playerType;
      event.option = {
        stateWarningScreen: state,
      };
      element(event);
    });
  };

  private playerHandleOnEnded = () => {
    this.playerStatus = "end";
    this.listEventPlayerEnd.forEach((element) => {
      const event = createViePlayerEvent(ViePlayerEventType.PLAYER_END);
      event.type = this.playerType;
      element(event);
    });
  };

  private playerHandleOnBufferTime = (time: number) => {
    this.playerStatus = "start";
    this.listEventPlayerBufferTime.forEach((element) => {
      const event = createViePlayerEvent(ViePlayerEventType.PLAYER_BUFFER_TIME);
      event.type = this.playerType;
      event.option = {
        time,
      };
      element(event);
    });
  };

  private playerHandleOnError = (
    type: ViePlayerErrorType,
    timeError: number,
    message?: string,
    errorDetail?: ViePlayerErrorDetail[],
    browserSupport?: any,
    playerInfo?: playerInfoType
  ) => {
    this.playerStatus = "error";
    this.listEventPlayerError.forEach((element) => {
      const event = createViePlayerEvent(ViePlayerEventType.PLAYER_ERROR);
      event.type = this.playerType;
      event.option = {
        type,
        timeError,
        message,
        errorDetail,
        browserSupport,
      };
      element(event, playerInfo);
    });
  };

  private playerHandleOnTimeUpdate = () => {
    this.listEventPlayerTimeUpdate.forEach((element) => {
      const event = createViePlayerEvent(ViePlayerEventType.PLAYER_TIME_UPDATE);
      event.type = this.playerType;
      const duration =
        window.playerLib?.seekRange?.().end || this.videoElement?.duration || 0;
      event.option = {
        currentTime: this.videoElement?.currentTime || 0,
        duration, // this.videoElement?.duration || 0,
      };
      element(event);
    });
  };

  private playerHandleCurrentProfile = (width: number, bitrate: number) => {
    this.listEventPlayerCurrentProfile.forEach((element) => {
      const event = createViePlayerEvent(
        ViePlayerEventType.PLAYER_CURRENT_PROFILE
      );
      event.type = this.playerType;
      event.option = {
        width,
        bitrate,
      };
      element(event);
    });
  };

  private playerHandleOnReBufferTime = (timeBuffer: number) => {
    this.listEventPlayerReBufferTime.forEach((element) => {
      const event = createViePlayerEvent(
        ViePlayerEventType.PLAYER_RE_BUFFER_TIME
      );
      event.type = this.playerType;
      event.option = {
        timeBuffer,
      };
      element(event);
    });
  };

  // REQUEST TRAILER
  requestPlayerTrailer = (config: ViePlayerConfigTrailer) => {
    if (this.mainDivElement) {
      this.config = config;
      this.playerType = ViePlayerType.TRAILER;

      if (this.playerDivElement && this.playerType === ViePlayerType.TRAILER) {
        this.playerStatus = "init";
        this.playerDivElement!.style.display = "block";
        this.playerDivElement.className = "v-player-player";
        if (config.type !== "full") {
          this.playerDivElement!.classList.add(config.type);
        }
        ReactDOM.render(
          <ViePlayerTrailer
            env={config.env}
            linkURL={config.linkURL}
            configMedia={config.configMedia}
            showLoading={config.showLoading}
            vPlayerSetPlayerRef={this.playerHandleVideoRef}
            vPlayerHandleOnPlay={this.playerHandleOnPlay}
            vPlayerHandleOnTimeUpdate={this.playerHandleOnTimeUpdate}
            vPlayerHandleOnEnded={this.playerHandleOnEnded}
            vPlayerHandleOnError={this.playerHandleOnError}
            dataWarningMessage={config.dataWarningMessage}
            ref={(el) => (this.playerMainRef = el)}
          />,
          this.playerDivElement!
        );
      }
    }
  };

  // REQUEST LIVE
  requestPlayerLIVE = (config: ViePlayerConfigLIVE) => {
    if (this.mainDivElement) {
      this.config = config;
      this.playerType = ViePlayerType.LIVE;

      if (this.playerDivElement && this.playerType === ViePlayerType.LIVE) {
        this.playerStatus = "init";
        this.playerDivElement.style.display = "block";
        this.playerDivElement.className = "v-player-player";
        if (config.type !== "full") {
          this.playerDivElement!.classList.add(config.type);
        }
        ReactDOM.render(
          <ViePlayerLIVE
            id={config.id}
            isAuthen={config.isAuthen}
            title={config.title}
            contentConcurrentGroup={config.contentConcurrentGroup}
            env={config.env}
            fullProfile={config.fullProfile}
            showLoading={config.showLoading}
            playerLogo={config.playerLogo}
            playerLogo4K={config.playerLogo4K}
            linkURL={config.linkURL}
            socketInfo={config.socketInfo}
            kplus={config.kplus}
            qnet={config.qnet}
            drm={config.drm}
            sigmaDRM={config.sigmaDRM}
            dataWarningScreen={config.dataWarningScreen}
            dataWarningMessage={config.dataWarningMessage}
            vPlayerSetPlayerRef={this.playerHandleVideoRef}
            vPlayerHandleOnStateWarrningScreen={
              this.playerHandleOnStateWarrningScreen
            }
            vPlayerHandleOnPlay={this.playerHandleOnPlay}
            vPlayerHandleOnBufferTime={this.playerHandleOnBufferTime}
            vPlayerHandleOnEnded={this.playerHandleOnEnded}
            vPlayerHandleOnError={this.playerHandleOnError}
            vPlayerHandleOnTimeUpdate={this.playerHandleOnTimeUpdate}
            vPlayerCurrentProfile={this.playerHandleCurrentProfile}
            vPlayerHandleOnReBufferTime={this.playerHandleOnReBufferTime}
            vPlayerKillPlayer={() => {
              this.destroy();
              this.removeAllEventListener();
            }}
            ref={(el) => (this.playerMainRef = el)}
          />,
          this.playerDivElement
        );
      }
    }
  };

  // REQUEST VOD
  requestPlayerVOD = (config: ViePlayerConfigVOD) => {
    if (this.mainDivElement) {
      this.config = config;
      this.playerType = ViePlayerType.VOD;

      if (this.playerDivElement && this.playerType === ViePlayerType.VOD) {
        this.playerStatus = "init";
        this.playerDivElement.style.display = "block";
        this.playerDivElement.className = "v-player-player";

        ReactDOM.render(
          <ViePlayerVOD
            env={config.env}
            timeProgress={config.timeProgress}
            fullProfile={config.fullProfile}
            showLoading={config.showLoading}
            isLive={config.isLive}
            playerLogo={config.playerLogo}
            playerLogo4K={config.playerLogo4K}
            linkURL={config.linkURL}
            socketInfo={config.socketInfo}
            qnet={config.qnet}
            drm={config.drm}
            sigmaDRM={config.sigmaDRM}
            vPlayerSetPlayerRef={this.playerHandleVideoRef}
            vPlayerHandleOnPlay={this.playerHandleOnPlay}
            vPlayerHandleOnBufferTime={this.playerHandleOnBufferTime}
            vPlayerHandleOnEnded={this.playerHandleOnEnded}
            vPlayerHandleOnError={this.playerHandleOnError}
            vPlayerHandleOnTimeUpdate={this.playerHandleOnTimeUpdate}
            vPlayerCurrentProfile={this.playerHandleCurrentProfile}
            vPlayerHandleOnReBufferTime={this.playerHandleOnReBufferTime}
            ref={(el) => (this.playerMainRef = el)}
          />,
          this.playerDivElement
        );
      }
    }
  };

  // FUNCTION
  changeSizePlayer = (size: "full" | "masterbanner" | "thumbnail") => {
    if (this.playerDivElement) {
      if (this.config && this.config.type) {
        this.playerDivElement.className = "v-player-player";
        this.config.type = size;
        if (size !== "full") {
          this.playerDivElement.classList.add(this.config.type);
        }
        if (
          this.playerType === ViePlayerType.TRAILER &&
          this.config.dataWarningMessage
        ) {
          if (
            this.playerMainRef &&
            this.playerMainRef.changePosition &&
            typeof this.playerMainRef.changePosition === "function"
          ) {
            this.playerMainRef.changePosition("top_right");
          }
        }
      }
    }
  };

  changeSizeImagePlayer = (size: "full" | "masterbanner" | "thumbnail") => {
    if (this.infoDivElement) {
      if (this.configImage && this.configImage.type) {
        this.infoDivElement.className = "v-player-poster";
        this.configImage.type = size;
        if (size !== "full") {
          this.infoDivElement.classList.add(this.configImage.type);
        }
      }
    }
  };

  play = () => {
    if (this.videoElement) {
      this.videoElement.play();
    }
  };

  pause = () => {
    if (this.videoElement) {
      this.videoElement.pause();
    }
  };

  seek = (time: number) => {
    if (this.videoElement) {
      this.videoElement.currentTime = time;
    }
    this.play();
  };

  showLoading = (show: boolean) => {
    if (
      this.playerMainRef &&
      this.playerMainRef.funcShowLoading &&
      typeof this.playerMainRef.funcShowLoading === "function"
    ) {
      this.playerMainRef.funcShowLoading(show);
    }
  };

  showInfoPlayer = () => {
    if (
      this.playerMainRef &&
      this.playerMainRef.funcShowInfo &&
      typeof this.playerMainRef.funcShowInfo === "function"
    ) {
      this.isShowInfoPlayer = !this.isShowInfoPlayer;
      this.playerMainRef.funcShowInfo(this.isShowInfoPlayer);
    }
  };

  changeQuality = (width: number) => {
    if (
      this.playerMainRef &&
      this.playerMainRef.funcChangeQuality &&
      typeof this.playerMainRef.funcChangeQuality === "function"
    ) {
      this.playerMainRef.funcChangeQuality(width);
    }
  };

  changeSubtitle = (name: string) => {
    if (
      this.playerMainRef &&
      this.playerMainRef.funcChangeSubtitle &&
      typeof this.playerMainRef.funcChangeSubtitle === "function"
    ) {
      this.playerMainRef.funcChangeSubtitle(name);
    }
  };

  changeAudio = (name: string) => {
    if (
      this.playerMainRef &&
      this.playerMainRef.funcChangeAudio &&
      typeof this.playerMainRef.funcChangeAudio === "function"
    ) {
      this.playerMainRef.funcChangeAudio(name);
    }
  };

  getPlayerInfo = () => {
    let playerInfo: playerInfoType = {
      codec: null,
      profileHeight: null,
      profileHeightText: null,
      playerStreamingProtocol: "DASH",
    };
    if (
      this.playerMainRef &&
      this.playerMainRef.funcGetPlayerInfo &&
      typeof this.playerMainRef.funcGetPlayerInfo === "function"
    ) {
      playerInfo = this.playerMainRef.funcGetPlayerInfo();
    }
    return playerInfo;
  };

  destroy = (isRemoveEvent: boolean = false) => {
    if (this.playerDivElement) {
      ReactDOM.unmountComponentAtNode(this.playerDivElement);
      this.playerDivElement.style.display = "none";
    }
    this.playerStatus = "unknown";
    this.videoElement = undefined;
    this.config = undefined;
    this.playerType = ViePlayerType.NULL;
    // if (isRemoveEvent) {
    //   this.removeAllEventListener();
    // }

    this.playerMainRef = undefined;
  };

  removeAllEventListener = () => {
    this.listEventPlayerStateWarningScreen = [];
    this.listEventPlayerStart = [];
    this.listEventPlayerEnd = [];
    this.listEventPlayerBufferTime = [];
    this.listEventPlayerError = [];
    this.listEventPlayerTimeUpdate = [];
    this.listEventPlayerCurrentProfile = [];
    this.listEventPlayerBuffer = [];
    this.listEventPlayerEndBuffer = [];
    this.listEventPlayerReBufferTime = [];
  };

  onceListener = (
    event: ViePlayerEventType,
    keyEventPlayer: ViePlayerEventKey
  ) => {
    const fn: ViePlayerEventKey = (e) => {
      keyEventPlayer(e);
      this.removeEventListener(event, fn);
    };
    this.addEventListener(event, fn);
  };

  // Handle event
  addEventListener = (
    event: ViePlayerEventType,
    keyEventPlayer: ViePlayerEventKey
  ) => {
    switch (event) {
      case ViePlayerEventType.PLAYER_STATE_WARNING_SCREEN:
        this.listEventPlayerStateWarningScreen.push(keyEventPlayer);
        break;
      case ViePlayerEventType.PLAYER_START:
        this.listEventPlayerStart.push(keyEventPlayer);
        break;
      case ViePlayerEventType.PLAYER_END:
        this.listEventPlayerEnd.push(keyEventPlayer);
        break;
      case ViePlayerEventType.PLAYER_BUFFER_TIME:
        this.listEventPlayerBufferTime.push(keyEventPlayer);
        break;
      case ViePlayerEventType.PLAYER_ERROR:
        this.listEventPlayerError.push(keyEventPlayer);
        break;
      case ViePlayerEventType.PLAYER_TIME_UPDATE:
        this.listEventPlayerTimeUpdate.push(keyEventPlayer);
        break;
      case ViePlayerEventType.PLAYER_CURRENT_PROFILE:
        this.listEventPlayerCurrentProfile.push(keyEventPlayer);
        break;
      case ViePlayerEventType.PLAYER_BUFFER:
        this.listEventPlayerBuffer.push(keyEventPlayer);
        break;
      case ViePlayerEventType.PLAYER_END_BUFFER:
        this.listEventPlayerEndBuffer.push(keyEventPlayer);
        break;
      case ViePlayerEventType.PLAYER_RE_BUFFER_TIME:
        this.listEventPlayerReBufferTime.push(keyEventPlayer);
        break;
      default:
        break;
    }
  };

  removeEventListener = (
    event: ViePlayerEventType,
    keyEventPlayer: ViePlayerEventKey
  ) => {
    switch (event) {
      case ViePlayerEventType.PLAYER_STATE_WARNING_SCREEN:
        const keyEventWarningScreen =
          this.listEventPlayerStateWarningScreen.indexOf(keyEventPlayer);
        if (keyEventWarningScreen >= 0) {
          this.listEventPlayerStateWarningScreen.splice(
            keyEventWarningScreen,
            1
          );
        }
        break;
      case ViePlayerEventType.PLAYER_START:
        const keyEventStart = this.listEventPlayerStart.indexOf(keyEventPlayer);
        if (keyEventStart >= 0) {
          this.listEventPlayerStart.splice(keyEventStart, 1);
        }
        break;
      case ViePlayerEventType.PLAYER_END:
        const keyEventEnd = this.listEventPlayerEnd.indexOf(keyEventPlayer);
        if (keyEventEnd >= 0) {
          this.listEventPlayerEnd.splice(keyEventEnd, 1);
        }
        break;
      case ViePlayerEventType.PLAYER_BUFFER_TIME:
        const keyEventBufferTime =
          this.listEventPlayerBufferTime.indexOf(keyEventPlayer);
        if (keyEventBufferTime >= 0) {
          this.listEventPlayerBufferTime.splice(keyEventBufferTime, 1);
        }
        break;
      case ViePlayerEventType.PLAYER_ERROR:
        const keyEventError = this.listEventPlayerError.indexOf(keyEventPlayer);
        if (keyEventError >= 0) {
          this.listEventPlayerError.splice(keyEventError, 1);
        }
        break;
      case ViePlayerEventType.PLAYER_TIME_UPDATE:
        const keyEventTimeUpdate =
          this.listEventPlayerTimeUpdate.indexOf(keyEventPlayer);
        if (keyEventTimeUpdate >= 0) {
          this.listEventPlayerTimeUpdate.splice(keyEventTimeUpdate, 1);
        }
        break;
      case ViePlayerEventType.PLAYER_CURRENT_PROFILE:
        const keyEventCurrentProfile =
          this.listEventPlayerCurrentProfile.indexOf(keyEventPlayer);
        if (keyEventCurrentProfile >= 0) {
          this.listEventPlayerCurrentProfile.splice(keyEventCurrentProfile, 1);
        }
        break;
      case ViePlayerEventType.PLAYER_BUFFER:
        const keyEventBuffer =
          this.listEventPlayerBuffer.indexOf(keyEventPlayer);
        if (keyEventBuffer >= 0) {
          this.listEventPlayerBuffer.splice(keyEventBuffer, 1);
        }
        break;
      case ViePlayerEventType.PLAYER_END_BUFFER:
        const keyEventEndBuffer =
          this.listEventPlayerEndBuffer.indexOf(keyEventPlayer);
        if (keyEventEndBuffer >= 0) {
          this.listEventPlayerEndBuffer.splice(keyEventEndBuffer, 1);
        }
        break;
      case ViePlayerEventType.PLAYER_RE_BUFFER_TIME:
        const keyEventReBufferTime =
          this.listEventPlayerReBufferTime.indexOf(keyEventPlayer);
        if (keyEventReBufferTime >= 0) {
          this.listEventPlayerReBufferTime.splice(keyEventReBufferTime, 1);
        }
        break;
      default:
        break;
    }
  };

  // Helper function, this will check and store default style for player
  _checkAndSetDefaultStyle = (key: string, value: any) => {
    if (this.defaultStyle.hasOwnProperty(key)) return;
    set(this.defaultStyle, key, value);
  };

  _setStyle = (
    elStyle: CSSStyleDeclaration,
    newStyle: Partial<CSSStyleDeclaration>,
    setAsDefault: boolean = false
  ) => {
    for (const key in newStyle) {
      if (elStyle.hasOwnProperty(key)) {
        if (!setAsDefault) {
          this._checkAndSetDefaultStyle(key, get(elStyle, key, ""));
        }
        set(elStyle, key, get(newStyle, key, ""));
      }
    }
  };

  movePosition = (
    style: Partial<CSSStyleDeclaration>,
    classNames: string[] = []
  ) => {
    if (this.playerDivElement) {
      const elStyle = this.playerDivElement.style;
      this._setStyle(elStyle, style);
    }
    if (this.mainDivElement)
      this.mainDivElement.style.zIndex = style.zIndex || "1";
  };

  resetPosition = () => {
    if (this.playerDivElement) {
      const elStyle = this.playerDivElement.style;
      this._setStyle(elStyle, this.defaultStyle, true);
      this.defaultStyle = {};
    }
    if (this.mainDivElement) this.mainDivElement.style.zIndex = "";
  };

  // VPLAYER INFO
  getImageId = () => {
    return get(this.configImage, "id", "") || "";
  };

  requestViePlayerImage = (config: ViePlayerConfigImage) => {
    setTimeout(() => {
      if (this.infoDivElement) {
        this.configImage = config;
        this.infoDivElement.style.display = "block";
        this.infoDivElement.className = "v-player-poster";
        this.infoDivElement.classList.add(config.type);

        if (config.show !== undefined) {
          this.showViePlayerImage(config.show);
        }
        ReactDOM.render(
          <ViePlayerImage
            url={config.url}
            ref={(el) => (this.playerImageRef = el)}
            key={config.id}
          />,
          this.infoDivElement
        );
      }
    }, 0);
  };

  destroyViePlayerImage = () => {
    if (this.infoDivElement) {
      ReactDOM.unmountComponentAtNode(this.infoDivElement);
      this.infoDivElement.style.display = "none";
      if (this.configImage) {
        this.configImage = undefined;
      }
      this.playerImageRef = undefined;
    }
  };

  blockPlayer = (block: boolean) => {
    if (this.mainDivElement) {
      this.mainDivElement.style.display = !block ? "block" : "none";
    }
    if (block) {
      this.lastPlayerStatus = this.playerStatus;
      if (this.lastPlayerStatus === "start") {
        this.pause();
      }
    } else {
      if (this.lastPlayerStatus === "start") {
        this.play();
      }
      this.lastPlayerStatus = "unknown";
    }
  };

  showViePlayerImage = (show: boolean) => {
    if (this.infoDivElement && this.configImage && show) {
      this.infoDivElement.style.display = "block";
    } else if (show === false && this.infoDivElement) {
      this.infoDivElement.style.display = "none";
    }
  };

  startRollViePlayerImage = () => {
    if (
      this.playerImageRef &&
      this.playerImageRef.startRoll &&
      typeof this.playerImageRef.startRoll === "function"
    ) {
      this.playerImageRef.startRoll();
    }
  };

  stopRollViePlayerImage = () => {
    if (
      this.playerImageRef &&
      this.playerImageRef.stopRoll &&
      typeof this.playerImageRef.stopRoll === "function"
    ) {
      this.playerImageRef.stopRoll();
    }
  };
}
