{"name": "@vieon/player", "version": "1.0.0", "description": "VieON Shared Player Components", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "type-check": "tsc --noEmit"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dependencies": {"hls.js": "^1.4.0", "shaka-player": "^4.7.0", "lodash": "^4.17.21"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/lodash": "^4.14.0", "typescript": "^5.0.0", "rollup": "^3.0.0", "@rollup/plugin-typescript": "^11.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-commonjs": "^25.0.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2"}}