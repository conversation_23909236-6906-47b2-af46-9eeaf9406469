{"name": "@vieon/payment", "version": "1.0.0", "description": "Payment processing and billing for VieON applications", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"@vieon/core": "workspace:*", "@vieon/types": "workspace:*", "@vieon/ui-kits": "workspace:*", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"rimraf": "^5.0.0", "typescript": "^5.8.3", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}