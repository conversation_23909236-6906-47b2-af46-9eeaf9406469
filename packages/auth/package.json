{"name": "@vieon/auth", "version": "1.0.0", "description": "Authentication and user management for VieON applications", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"@vieon/core": "workspace:*", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "rimraf": "^5.0.0", "typescript": "^5.8.3"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}