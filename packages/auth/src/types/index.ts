/**
 * Authentication module type definitions
 */

import { User, Profile } from '@vieon/core';

// Auth flow types
export enum AuthFlow {
  PHONE = 'PHONE',
  EMAIL = 'EMAIL',
  PHONE_LOGIN = 'PHONE_LOGIN',
  EMAIL_LOGIN = 'EMAIL_LOGIN',
  PHONE_REGISTER_OTP = 'PHONE_REGISTER_OTP',
  EMAIL_REGISTER_OTP = 'EMAIL_REGISTER_OTP',
  PHONE_REGISTER_PASSWORD = 'PHONE_REGISTER_PASSWORD',
  EMAIL_REGISTER_PASSWORD = 'EMAIL_REGISTER_PASSWORD',
  PHONE_FORGOT_PASSWORD_OTP = 'PHONE_FORGOT_PASSWORD_OTP',
  EMAIL_FORGOT_PASSWORD_OTP = 'EMAIL_FORGOT_PASSWORD_OTP',
  RESET_PASSWORD = 'RESET_PASSWORD',
  UPDATE_PASSWORD_OTP = 'UPDATE_PASSWORD_OTP',
  UPDATE_PASSWORD_SET_PASS = 'UPDATE_PASSWORD_SET_PASS',
  LINK_PHONE_NUMBER = 'LINK_PHONE_NUMBER',
  LINK_PHONE_NUMBER_OTP = 'LINK_PHONE_NUMBER_OTP',
  LINK_PHONE_NUMBER_SET_PASS = 'LINK_PHONE_NUMBER_SET_PASS',
  BIND_PHONE = 'BIND_PHONE',
  BIND_PHONE_OTP = 'BIND_PHONE_OTP',
  BIND_PHONE_SET_PASS = 'BIND_PHONE_SET_PASS',
  RESTORE_ACCOUNT_OTP = 'RESTORE_ACCOUNT_OTP',
  OPTIONS_TO_CHOOSE = 'OPTIONS_TO_CHOOSE'
}

// Input types
export enum LoginInputType {
  UNKNOWN = 'UNKNOWN',
  PHONE = 'PHONE',
  EMAIL = 'EMAIL'
}

// Provider types
export enum AuthProvider {
  Mobile = 0,
  Email = 1,
  Google = 2,
  Facebook = 3,
  Apple = 4,
  Hotel = 5
}

// Country codes
export enum CountryCode {
  US = 'US',
  VN = 'VN'
}

export enum PhoneCode {
  US = '+1',
  VN = '+84'
}

// OTP types
export enum OTPType {
  SMS = 2,
  MAIL = 1
}

export enum OTPReceiveType {
  EMAIL = 1,
  PHONE = 2
}

// Auth response codes
export enum AuthResponseCode {
  Success = 0,
  RequestInvalid = 400,
  AccountAlreadyExists = 4009,
  OTPLimitReached = 4010,
  RequestLimitReached = 13,
  InvalidCaptcha = 4013,
  AccountLinked = 200,
  AccountNotLinked = 201,
  OTPLimited = 202
}

// Auth trigger types
export enum AuthTriggerType {
  CONTENT = 'content',
  PAYMENT = 'payment',
  VOUCHER = 'voucher',
  REPORT = 'report',
  ADD_TO_LIST = 'add_to_list',
  SETTING = 'setting',
  REMIND = 'remind',
  REGISTRATION_TRIAL = 'registration_trial',
  SOCIAL_UPDATE_PHONE = 'social_update_phone',
  PROFILE_UPDATE_PHONE = 'profile_update_phone'
}

// Auth state interfaces
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  profiles: Profile[];
  currentProfile: Profile | null;
  tokens: AuthTokens | null;
  flow: AuthFlow;
  loading: boolean;
  error: string | null;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken?: string;
  profileToken?: string;
  expiresAt?: number;
}

// Request/Response interfaces
export interface LoginRequest {
  userName: string;
  password: string;
  countryCode?: string;
  model?: string;
  deviceName?: string;
  deviceType?: string;
  deviceId?: string;
  destination?: string;
  isLoggedByEmail?: boolean;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  userName: string;
  countryCode?: string;
  model?: string;
  deviceName?: string;
  deviceType?: string;
  deviceId?: string;
  captchaToken?: string;
}

export interface VerifyOTPRequest {
  code: string;
  confirmationNo: string;
}

export interface ConfirmOTPRequest {
  confirmationNo: string;
  code: string;
  password?: string;
  oldPassword?: string;
  gender?: number;
  dob?: string;
  destination?: string;
  hasToSelectProfileDefault?: boolean;
  isRegister?: boolean;
  accessToken?: string;
}

export interface ForgotPasswordRequest {
  userName: string;
  countryCode?: string;
  model?: string;
  deviceName?: string;
  deviceType?: string;
  deviceId?: string;
  notificationType: number;
  captchaToken?: string;
}

export interface LinkPhoneNumberRequest {
  phoneNumber: string;
  countryCode: string;
}

export interface UpdatePasswordRequest {
  priority: number;
  notificationType: number;
}

export interface RestoreAccountRequest {
  accessToken: string;
  model?: string;
  deviceName?: string;
  deviceType?: string;
  deviceId?: string;
}

export interface AuthResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

export interface LoginResponse {
  accessToken: string;
  refreshToken?: string;
  profile: User;
  profiles?: Profile[];
  isLoginBySocial?: boolean;
}

export interface RegisterResponse {
  confirmationNo: string;
  otpType: OTPType;
  message?: string;
}

export interface VerifyOTPResponse {
  isValid: boolean;
  message?: string;
}

export interface ConfirmOTPResponse {
  accessToken: string;
  refreshToken?: string;
  profile: User;
  profiles?: Profile[];
  isRegister?: boolean;
}

// Component props interfaces
export interface AuthContainerProps {
  mainFlow?: AuthFlow;
  titlePopupTriggerAuth?: string;
  customClassName?: string;
  onAuthSuccess?: (user: User, profiles: Profile[]) => void;
  onAuthError?: (error: string) => void;
}

export interface LoginFormProps {
  userName: string;
  isPhoneNumber: boolean;
  countryKey: string;
  onChangePassword: (password: string) => void;
  onForgotPassword: (type: number) => void;
  onSubmit: () => void;
  dataLogin?: AuthResponse<LoginResponse>;
  dataForgotPassword?: AuthResponse;
  onBack?: () => void;
  canBack?: boolean;
  flow: AuthFlow;
}

export interface RegisterFormProps {
  flow: AuthFlow;
  userName: string;
  isPhoneNumber: boolean;
  countryCode: string;
  geoCountry: string;
  isOnlySupportVN: boolean;
  onChangeUserName: (userName: string) => void;
  onChangeCountryCode: (code: CountryCode) => void;
  onSubmit: () => void;
  dataVerify?: AuthResponse;
  canBack?: boolean;
  inputType: LoginInputType;
  setInputType: (type: LoginInputType) => void;
  titlePopupTriggerAuth?: string;
  trigger?: string;
}

export interface OTPVerifyProps {
  userName: string;
  isPhoneNumber: boolean;
  countryKey: string;
  flow: string;
  dataValidateOTP?: AuthResponse<VerifyOTPResponse>;
  dataSendOTP?: AuthResponse;
  dataConfirmOTP?: AuthResponse<ConfirmOTPResponse>;
  onResendOTP: (type?: number) => void;
  onChangeOTP: (code: string) => void;
  onBack?: () => void;
  canBack?: boolean;
  isCanSwitchTypeReceiveOTP?: boolean;
  typeReceiveOTP?: OTPReceiveType;
  handleChangeTypeReceiveOTP?: () => void;
  profile?: User;
  isGlobal?: boolean;
  inputType: LoginInputType;
  isBindPhoneFlow?: boolean;
  isUpdatePhoneNumberFlow?: boolean;
  isUpdatePasswordFlow?: boolean;
  isRestoreAccountFlow?: boolean;
}

export interface PasswordUpdateProps {
  userName: string;
  isPhoneNumber: boolean;
  countryKey: string;
  isRegisterPassword?: boolean;
  isBindPhone?: boolean;
  isUpdatePhoneNumber?: boolean;
  isUpdatePassword?: boolean;
  isResetPassword?: boolean;
  onChangePassword: (password: string) => void;
  onChangeOldPassword?: (password: string) => void;
  onSubmit: (data?: any) => void;
  dataConfirmOTP?: AuthResponse<ConfirmOTPResponse>;
  onBack?: () => void;
  canBack?: boolean;
  flow: AuthFlow;
}

// Country information
export interface CountryInfo {
  code: CountryCode;
  phoneCode: PhoneCode;
  name: string;
  flag?: string;
}

// Auth context
export interface AuthContextValue {
  state: AuthState;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  verifyOTP: (data: VerifyOTPRequest) => Promise<void>;
  confirmOTP: (data: ConfirmOTPRequest) => Promise<void>;
  forgotPassword: (data: ForgotPasswordRequest) => Promise<void>;
  linkPhoneNumber: (data: LinkPhoneNumberRequest) => Promise<void>;
  updatePassword: (data: UpdatePasswordRequest) => Promise<void>;
  restoreAccount: (data: RestoreAccountRequest) => Promise<void>;
  switchProfile: (profileId: string) => Promise<void>;
  refreshTokens: () => Promise<void>;
  setFlow: (flow: AuthFlow) => void;
  clearError: () => void;
}

// Hook return types
export interface UseAuthReturn extends AuthContextValue {}

export interface UseAuthFormReturn {
  userName: string;
  password: string;
  countryCode: CountryInfo;
  code: string;
  inputType: LoginInputType;
  isPhoneNumber: boolean;
  setUserName: (value: string) => void;
  setPassword: (value: string) => void;
  setCountryCode: (code: CountryInfo) => void;
  setCode: (value: string) => void;
  setInputType: (type: LoginInputType) => void;
  resetForm: () => void;
  validateForm: () => boolean;
}

export interface UseOTPReturn {
  code: string;
  isValid: boolean;
  isComplete: boolean;
  setCode: (value: string) => void;
  clearCode: () => void;
  validateCode: () => boolean;
}