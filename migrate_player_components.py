#!/usr/bin/env python3
import os
import shutil
from pathlib import Path

def analyze_player_structure():
    """Phân tích cấu trúc player components từ cả hai apps"""
    
    smart_tv_players = {
        'core': [
            'apps/smart-tv/src/app/components/Player/ViePlayer/ViePlayer.tsx',
            'apps/smart-tv/src/app/components/Player/ViePlayer/ViePlayerBasic.tsx',
            'apps/smart-tv/src/app/components/Player/ViePlayer/ViePlayerContainer.tsx',
            'apps/smart-tv/src/app/components/Player/ViePlayer/ViePlayerVOD.tsx',
            'apps/smart-tv/src/app/components/Player/ViePlayer/ViePlayerLIVE.tsx',
            'apps/smart-tv/src/app/components/Player/ViePlayer/ViePlayerTrailer.tsx',
            'apps/smart-tv/src/app/components/Player/ViePlayer/ViePlayerImage.tsx',
            'apps/smart-tv/src/app/components/Player/ViePlayer/constants.ts',
            'apps/smart-tv/src/app/components/Player/ViePlayer/index.ts',
        ],
        'components': [
            'apps/smart-tv/src/app/components/Player/components/VIPlayerHls.tsx',
            'apps/smart-tv/src/app/components/Player/components/VIPlayerMP4.tsx',
            'apps/smart-tv/src/app/components/Player/components/VIPlayerShaka.tsx',
            'apps/smart-tv/src/app/components/Player/components/VIPlayerTrailer.tsx',
            'apps/smart-tv/src/app/components/Player/components/VIPercentLoading.tsx',
            'apps/smart-tv/src/app/components/Player/components/WarningMessage.tsx',
            'apps/smart-tv/src/app/components/Player/components/WarningScreen.tsx',
        ],
        'hooks': [
            'apps/smart-tv/src/app/components/Player/ViePlayer/useEventPlayer.ts',
        ],
        'managers': [
            'apps/smart-tv/src/app/components/Player/ViePlayer/ConcurrentScreenManager.ts',
            'apps/smart-tv/src/app/components/Player/ViePlayer/QnetManager.ts',
            'apps/smart-tv/src/app/components/Player/ViePlayer/SocketManager.ts',
        ],
        'styles': [
            'apps/smart-tv/src/app/components/Player/components/style.module.scss',
            'apps/smart-tv/src/app/components/Player/components/warning_message.scss',
        ],
        'utils': [
            'apps/smart-tv/src/app/components/Player/transforms.ts',
        ]
    }
    
    web_players = {
        'core': [
            'apps/web/src/components/basic/Player/Player.tsx',
            'apps/web/src/components/basic/Player/ShakaPlayerComponent.tsx',
        ],
        'components': [
            'apps/web/src/components/detail/DetailPlayer.tsx',
            'apps/web/src/components/livestream/LiveStreamPlayer.tsx',
            'apps/web/src/components/liveTV/LiveTVPlayer.tsx',
        ],
        'services': [
            'apps/web/src/services/playerServices.ts',
            'apps/web/src/services/videoIndexingService.ts',
            'apps/web/src/services/handleOffMasterPlayerService.ts',
        ],
        'actions': [
            'apps/web/src/actions/player.ts',
        ],
        'reducers': [
            'apps/web/src/reducers/player.ts',
        ],
        'tracking': [
            'apps/web/src/tracking/video.ts',
            'apps/web/src/tracking/functions/TrackingPlayer.ts',
        ]
    }
    
    return smart_tv_players, web_players

def create_shared_player_structure():
    """Tạo cấu trúc thư mục cho shared player package"""
    base_path = Path('/Users/<USER>/Desktop/Project/VieON/web-smart-tv-monorepo/packages/player/src')
    
    directories = [
        'components/core',
        'components/controls',
        'components/ui',
        'hooks',
        'services',
        'managers',
        'types',
        'constants',
        'utils',
        'styles',
        'tracking'
    ]
    
    for directory in directories:
        dir_path = base_path / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {dir_path}")

def migrate_player_files():
    """Di chuyển và tổ chức lại player files"""
    smart_tv_players, web_players = analyze_player_structure()
    base_path = '/Users/<USER>/Desktop/Project/VieON/web-smart-tv-monorepo'
    target_path = f'{base_path}/packages/player/src'
    
    print("\n=== MIGRATING SMART-TV PLAYER COMPONENTS ===")
    
    # Migrate Smart-TV core components
    for file_path in smart_tv_players['core']:
        source = f'{base_path}/{file_path}'
        if os.path.exists(source):
            filename = os.path.basename(file_path)
            target = f'{target_path}/components/core/{filename}'
            print(f"Would migrate: {file_path} -> components/core/{filename}")
    
    # Migrate Smart-TV sub components
    for file_path in smart_tv_players['components']:
        source = f'{base_path}/{file_path}'
        if os.path.exists(source):
            filename = os.path.basename(file_path)
            target = f'{target_path}/components/ui/{filename}'
            print(f"Would migrate: {file_path} -> components/ui/{filename}")
    
    # Migrate hooks
    for file_path in smart_tv_players['hooks']:
        source = f'{base_path}/{file_path}'
        if os.path.exists(source):
            filename = os.path.basename(file_path)
            target = f'{target_path}/hooks/{filename}'
            print(f"Would migrate: {file_path} -> hooks/{filename}")
    
    # Migrate managers
    for file_path in smart_tv_players['managers']:
        source = f'{base_path}/{file_path}'
        if os.path.exists(source):
            filename = os.path.basename(file_path)
            target = f'{target_path}/managers/{filename}'
            print(f"Would migrate: {file_path} -> managers/{filename}")
    
    print("\n=== MIGRATING WEB PLAYER COMPONENTS ===")
    
    # Migrate Web services
    for file_path in web_players['services']:
        source = f'{base_path}/{file_path}'
        if os.path.exists(source):
            filename = os.path.basename(file_path)
            target = f'{target_path}/services/{filename}'
            print(f"Would migrate: {file_path} -> services/{filename}")
    
    # Migrate Web tracking
    for file_path in web_players['tracking']:
        source = f'{base_path}/{file_path}'
        if os.path.exists(source):
            filename = os.path.basename(file_path)
            target = f'{target_path}/tracking/{filename}'
            print(f"Would migrate: {file_path} -> tracking/{filename}")

def create_unified_types():
    """Tạo unified types cho shared player"""
    types_content = '''export interface PlayerConfig {
  src: string;
  type?: 'hls' | 'dash' | 'mp4';
  autoPlay?: boolean;
  muted?: boolean;
  controls?: boolean;
  poster?: string;
  drm?: DRMConfig;
}

export interface DRMConfig {
  provider: 'widevine' | 'playready' | 'fairplay';
  licenseUrl: string;
  headers?: Record<string, string>;
}

export interface PlayerState {
  isPlaying: boolean;
  isLoading: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  muted: boolean;
  error: string | null;
  quality: QualityLevel[];
  currentQuality: string;
}

export interface QualityLevel {
  id: string;
  label: string;
  width: number;
  height: number;
  bitrate: number;
}

export interface PlayerEvents {
  onPlay?: () => void;
  onPause?: () => void;
  onTimeUpdate?: (currentTime: number) => void;
  onDurationChange?: (duration: number) => void;
  onError?: (error: string) => void;
  onQualityChange?: (quality: string) => void;
}
'''
    
    types_path = '/Users/<USER>/Desktop/Project/VieON/web-smart-tv-monorepo/packages/player/src/types/index.ts'
    with open(types_path, 'w', encoding='utf-8') as f:
        f.write(types_content)
    print(f"Created unified types: {types_path}")

def create_migration_plan():
    """Tạo kế hoạch migration chi tiết"""
    plan = '''
# PLAYER MIGRATION PLAN

## 1. Cấu trúc Shared Player Package

```
packages/player/src/
├── components/
│   ├── core/           # Core player components từ smart-tv
│   ├── controls/       # Player controls
│   └── ui/            # UI components
├── hooks/             # React hooks
├── services/          # Player services từ web
├── managers/          # Connection managers từ smart-tv
├── types/             # TypeScript types
├── constants/         # Constants và enums
├── utils/             # Utility functions
├── styles/            # Shared styles
├── tracking/          # Tracking functions từ web
└── index.ts           # Main export
```

## 2. Components cần tách từ Smart-TV:

### Core Components:
- ViePlayer.tsx (Main player class)
- ViePlayerBasic.tsx
- ViePlayerContainer.tsx
- ViePlayerVOD.tsx
- ViePlayerLIVE.tsx
- ViePlayerTrailer.tsx
- ViePlayerImage.tsx

### UI Components:
- VIPlayerHls.tsx
- VIPlayerMP4.tsx
- VIPlayerShaka.tsx
- VIPercentLoading.tsx
- WarningMessage.tsx
- WarningScreen.tsx

### Managers:
- ConcurrentScreenManager.ts
- QnetManager.ts
- SocketManager.ts

## 3. Components cần tách từ Web:

### Services:
- playerServices.ts
- videoIndexingService.ts
- handleOffMasterPlayerService.ts

### Tracking:
- TrackingPlayer.ts
- video.ts (tracking functions)

### Core:
- Player.tsx (Main web player)
- ShakaPlayerComponent.tsx

## 4. Bước thực hiện:

1. Tạo cấu trúc thư mục shared package
2. Di chuyển và refactor core components
3. Tạo unified types và interfaces
4. Tạo shared hooks và services
5. Cập nhật imports trong các apps
6. Testing và validation

## 5. Breaking Changes:

- Import paths sẽ thay đổi từ relative sang @vieon/player
- Một số APIs có thể cần được unified
- State management có thể cần refactor
'''
    
    plan_path = '/Users/<USER>/Desktop/Project/VieON/web-smart-tv-monorepo/PLAYER_MIGRATION_PLAN.md'
    with open(plan_path, 'w', encoding='utf-8') as f:
        f.write(plan)
    print(f"Created migration plan: {plan_path}")

if __name__ == '__main__':
    print("=== PLAYER MIGRATION ANALYSIS ===")
    
    # Tạo cấu trúc thư mục
    create_shared_player_structure()
    
    # Phân tích và hiển thị kế hoạch migration
    migrate_player_files()
    
    # Tạo unified types
    create_unified_types()
    
    # Tạo migration plan
    create_migration_plan()
    
    print("\n=== MIGRATION ANALYSIS COMPLETED ===")
    print("Next steps:")
    print("1. Review the migration plan in PLAYER_MIGRATION_PLAN.md")
    print("2. Start migrating core components")
    print("3. Update import paths in apps")
    print("4. Test integration")