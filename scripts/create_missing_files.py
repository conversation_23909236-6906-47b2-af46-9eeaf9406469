#!/usr/bin/env python3
import os
import json

def create_file(file_path, content):
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, 'w') as f:
        f.write(content)
    print(f"Created: {file_path}")

def main():
    base_path = "/Users/<USER>/Desktop/Project/VieON/web-smart-tv-monorepo/packages"
    
    # Player package files
    player_files = {
        "player/src/components/Controls.tsx": '''import React from 'react';

export interface ControlsProps {
  isPlaying: boolean;
  onPlayPause: () => void;
  volume: number;
  onVolumeChange: (volume: number) => void;
}

export const Controls: React.FC<ControlsProps> = ({
  isPlaying,
  onPlayPause,
  volume,
  onVolumeChange
}) => {
  return (
    <div className="player-controls">
      <button onClick={onPlayPause}>
        {isPlaying ? 'Pause' : 'Play'}
      </button>
      <input
        type="range"
        min="0"
        max="1"
        step="0.1"
        value={volume}
        onChange={(e) => onVolumeChange(parseFloat(e.target.value))}
      />
    </div>
  );
};
''',
        "player/src/components/QualitySelector.tsx": '''import React from 'react';

export interface Quality {
  id: string;
  label: string;
  height: number;
}

export interface QualitySelectorProps {
  qualities: Quality[];
  currentQuality: string;
  onQualityChange: (qualityId: string) => void;
}

export const QualitySelector: React.FC<QualitySelectorProps> = ({
  qualities,
  currentQuality,
  onQualityChange
}) => {
  return (
    <select
      value={currentQuality}
      onChange={(e) => onQualityChange(e.target.value)}
      className="quality-selector"
    >
      {qualities.map((quality) => (
        <option key={quality.id} value={quality.id}>
          {quality.label}
        </option>
      ))}
    </select>
  );
};
''',
        "player/src/hooks/usePlayer.ts": '''import { useState, useCallback } from 'react';

export interface PlayerState {
  isPlaying: boolean;
  volume: number;
  currentTime: number;
  duration: number;
  quality: string;
}

export const usePlayer = () => {
  const [state, setState] = useState<PlayerState>({
    isPlaying: false,
    volume: 1,
    currentTime: 0,
    duration: 0,
    quality: 'auto'
  });

  const play = useCallback(() => {
    setState(prev => ({ ...prev, isPlaying: true }));
  }, []);

  const pause = useCallback(() => {
    setState(prev => ({ ...prev, isPlaying: false }));
  }, []);

  const setVolume = useCallback((volume: number) => {
    setState(prev => ({ ...prev, volume }));
  }, []);

  const setQuality = useCallback((quality: string) => {
    setState(prev => ({ ...prev, quality }));
  }, []);

  return {
    ...state,
    play,
    pause,
    setVolume,
    setQuality
  };
};
''',
        "player/src/hooks/useHLS.ts": '''import { useEffect, useRef, useState } from 'react';

export interface HLSConfig {
  autoStartLoad?: boolean;
  startPosition?: number;
  debug?: boolean;
}

export const useHLS = (src: string, config?: HLSConfig) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!videoRef.current || !src) return;

    const video = videoRef.current;
    
    if (video.canPlayType('application/vnd.apple.mpegurl')) {
      video.src = src;
      setIsLoaded(true);
    } else {
      setError('HLS not supported');
    }

    return () => {
      if (video) {
        video.src = '';
      }
    };
  }, [src]);

  return {
    videoRef,
    isLoaded,
    error
  };
};
''',
        "player/src/services/hls-service.ts": '''export interface HLSLevel {
  height: number;
  width: number;
  bitrate: number;
  name: string;
}

export class HLSService {
  private video: HTMLVideoElement | null = null;
  private levels: HLSLevel[] = [];

  constructor(video: HTMLVideoElement) {
    this.video = video;
  }

  loadSource(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.video) {
        reject(new Error('Video element not found'));
        return;
      }

      this.video.src = url;
      this.video.addEventListener('loadedmetadata', () => resolve(), { once: true });
      this.video.addEventListener('error', () => reject(new Error('Failed to load video')), { once: true });
    });
  }

  getLevels(): HLSLevel[] {
    return this.levels;
  }

  setLevel(index: number): void {
    console.log(`Setting quality level to: ${index}`);
  }

  destroy(): void {
    this.video = null;
    this.levels = [];
  }
}
''',
        "player/src/services/subtitle-service.ts": '''export interface Subtitle {
  id: string;
  label: string;
  language: string;
  url: string;
}

export class SubtitleService {
  private video: HTMLVideoElement | null = null;
  private subtitles: Subtitle[] = [];
  private currentSubtitle: string | null = null;

  constructor(video: HTMLVideoElement) {
    this.video = video;
  }

  addSubtitle(subtitle: Subtitle): void {
    this.subtitles.push(subtitle);
    
    if (this.video) {
      const track = this.video.addTextTrack('subtitles', subtitle.label, subtitle.language);
      track.mode = 'hidden';
    }
  }

  setSubtitle(id: string | null): void {
    this.currentSubtitle = id;
    
    if (this.video) {
      Array.from(this.video.textTracks).forEach((track, index) => {
        track.mode = this.subtitles[index]?.id === id ? 'showing' : 'hidden';
      });
    }
  }

  getSubtitles(): Subtitle[] {
    return this.subtitles;
  }

  getCurrentSubtitle(): string | null {
    return this.currentSubtitle;
  }

  destroy(): void {
    this.video = null;
    this.subtitles = [];
    this.currentSubtitle = null;
  }
}
''',
        "player/src/utils/player-utils.ts": '''export const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

export const parseTime = (timeString: string): number => {
  const parts = timeString.split(':').map(Number);
  if (parts.length === 3) {
    return parts[0] * 3600 + parts[1] * 60 + parts[2];
  }
  if (parts.length === 2) {
    return parts[0] * 60 + parts[1];
  }
  return parts[0] || 0;
};

export const getVideoQuality = (width: number, height: number): string => {
  if (height >= 2160) return '4K';
  if (height >= 1440) return '1440p';
  if (height >= 1080) return '1080p';
  if (height >= 720) return '720p';
  if (height >= 480) return '480p';
  return '360p';
};

export const calculateBitrate = (width: number, height: number, fps: number = 30): number => {
  const pixels = width * height;
  const baseRate = pixels * fps * 0.1;
  return Math.round(baseRate / 1000);
};
'''
    }
    
    # Ads package files
    ads_files = {
        "ads/src/services/hls-ads.ts": '''export interface AdBreak {
  id: string;
  position: number;
  duration: number;
  ads: Ad[];
}

export interface Ad {
  id: string;
  duration: number;
  mediaUrl: string;
  clickThroughUrl?: string;
  skipOffset?: number;
}

export class HLSAdsService {
  private adBreaks: AdBreak[] = [];
  private currentAd: Ad | null = null;
  private isPlayingAd = false;

  addAdBreak(adBreak: AdBreak): void {
    this.adBreaks.push(adBreak);
  }

  getAdBreaks(): AdBreak[] {
    return this.adBreaks;
  }

  playAd(ad: Ad): Promise<void> {
    return new Promise((resolve) => {
      this.currentAd = ad;
      this.isPlayingAd = true;
      
      setTimeout(() => {
        this.isPlayingAd = false;
        this.currentAd = null;
        resolve();
      }, ad.duration * 1000);
    });
  }

  skipAd(): boolean {
    if (this.currentAd?.skipOffset && this.isPlayingAd) {
      this.isPlayingAd = false;
      this.currentAd = null;
      return true;
    }
    return false;
  }

  isAdPlaying(): boolean {
    return this.isPlayingAd;
  }

  getCurrentAd(): Ad | null {
    return this.currentAd;
  }
}
'''
    }
    
    # Update index files
    index_updates = {
        "player/src/components/index.ts": '''export * from './Controls';
export * from './QualitySelector';
''',
        "player/src/hooks/index.ts": '''export * from './usePlayer';
export * from './useHLS';
''',
        "player/src/services/index.ts": '''export * from './hls-service';
export * from './subtitle-service';
''',
        "player/src/utils/index.ts": '''export * from './player-utils';
''',
        "ads/src/services/index.ts": '''export * from './hls-ads';
'''
    }
    
    # Create all files
    all_files = {**player_files, **ads_files, **index_updates}
    
    for relative_path, content in all_files.items():
        file_path = os.path.join(base_path, relative_path)
        create_file(file_path, content)
    
    print("\nAll missing files created successfully!")

if __name__ == "__main__":
    main()