#!/usr/bin/env python3
"""
VieON Monorepo Project Analysis Script
Analyzes the structure, dependencies, and configuration of the VieON web smart TV monorepo.
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
import subprocess

class ProjectAnalyzer:
    def __init__(self, root_path: str = "."):
        self.root_path = Path(root_path).resolve()
        self.analysis_result = {}
        
    def analyze_package_json(self, path: Path) -> Dict[str, Any]:
        """Analyze a package.json file"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return {
                'name': data.get('name', 'unknown'),
                'version': data.get('version', 'unknown'),
                'description': data.get('description', ''),
                'dependencies': data.get('dependencies', {}),
                'devDependencies': data.get('devDependencies', {}),
                'scripts': data.get('scripts', {}),
                'main': data.get('main', ''),
                'types': data.get('types', ''),
                'exports': data.get('exports', {}),
                'private': data.get('private', False)
            }
        except Exception as e:
            return {'error': str(e)}
    
    def analyze_typescript_config(self, path: Path) -> Dict[str, Any]:
        """Analyze TypeScript configuration"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()
                # Remove comments for JSON parsing
                lines = content.split('\n')
                clean_lines = []
                for line in lines:
                    if not line.strip().startswith('//'):
                        clean_lines.append(line)
                clean_content = '\n'.join(clean_lines)
                data = json.loads(clean_content)
            return data
        except Exception as e:
            return {'error': str(e)}
    
    def get_directory_structure(self, path: Path, max_depth: int = 3, current_depth: int = 0) -> Dict[str, Any]:
        """Get directory structure with file counts"""
        if current_depth >= max_depth:
            return {}
        
        structure = {}
        try:
            for item in sorted(path.iterdir()):
                if item.name.startswith('.') and item.name not in ['.env', '.env.example']:
                    continue
                if item.name == 'node_modules':
                    structure[item.name] = {'type': 'directory', 'note': 'dependencies'}
                    continue
                    
                if item.is_dir():
                    sub_structure = self.get_directory_structure(item, max_depth, current_depth + 1)
                    structure[item.name] = {
                        'type': 'directory',
                        'children': sub_structure
                    }
                else:
                    structure[item.name] = {
                        'type': 'file',
                        'size': item.stat().st_size if item.exists() else 0
                    }
        except PermissionError:
            structure['_error'] = 'Permission denied'
        
        return structure
    
    def analyze_apps(self) -> Dict[str, Any]:
        """Analyze applications in the apps directory"""
        apps_dir = self.root_path / 'apps'
        apps_analysis = {}
        
        if not apps_dir.exists():
            return {'error': 'Apps directory not found'}
        
        for app_dir in apps_dir.iterdir():
            if app_dir.is_dir():
                app_analysis = {
                    'path': str(app_dir.relative_to(self.root_path)),
                    'structure': self.get_directory_structure(app_dir, max_depth=2)
                }
                
                # Check for package.json
                package_json = app_dir / 'package.json'
                if package_json.exists():
                    app_analysis['package'] = self.analyze_package_json(package_json)
                
                # Check for TypeScript config
                tsconfig = app_dir / 'tsconfig.json'
                if tsconfig.exists():
                    app_analysis['typescript'] = self.analyze_typescript_config(tsconfig)
                
                apps_analysis[app_dir.name] = app_analysis
        
        return apps_analysis
    
    def analyze_packages(self) -> Dict[str, Any]:
        """Analyze packages in the packages directory"""
        packages_dir = self.root_path / 'packages'
        packages_analysis = {}
        
        if not packages_dir.exists():
            return {'error': 'Packages directory not found'}
        
        for package_dir in packages_dir.iterdir():
            if package_dir.is_dir():
                package_analysis = {
                    'path': str(package_dir.relative_to(self.root_path)),
                    'structure': self.get_directory_structure(package_dir, max_depth=2)
                }
                
                # Check for package.json
                package_json = package_dir / 'package.json'
                if package_json.exists():
                    package_analysis['package'] = self.analyze_package_json(package_json)
                
                # Check for TypeScript config
                tsconfig = package_dir / 'tsconfig.json'
                if tsconfig.exists():
                    package_analysis['typescript'] = self.analyze_typescript_config(tsconfig)
                
                packages_analysis[package_dir.name] = package_analysis
        
        return packages_analysis
    
    def analyze_root_config(self) -> Dict[str, Any]:
        """Analyze root configuration files"""
        config_analysis = {}
        
        # Main package.json
        package_json = self.root_path / 'package.json'
        if package_json.exists():
            config_analysis['package'] = self.analyze_package_json(package_json)
        
        # Workspace configuration
        workspace_yaml = self.root_path / 'pnpm-workspace.yaml'
        if workspace_yaml.exists():
            try:
                with open(workspace_yaml, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # Simple parsing for pnpm-workspace.yaml
                    packages = []
                    for line in content.split('\n'):
                        line = line.strip()
                        if line.startswith('- '):
                            packages.append(line[2:].strip().strip("'\""))
                    config_analysis['workspace'] = {'packages': packages}
            except Exception as e:
                config_analysis['workspace'] = {'error': str(e)}
        
        # TypeScript configuration
        tsconfig = self.root_path / 'tsconfig.json'
        if tsconfig.exists():
            config_analysis['typescript'] = self.analyze_typescript_config(tsconfig)
        
        return config_analysis
    
    def analyze_scripts(self) -> Dict[str, Any]:
        """Analyze scripts directory"""
        scripts_dir = self.root_path / 'scripts'
        scripts_analysis = {}
        
        if not scripts_dir.exists():
            return {'error': 'Scripts directory not found'}
        
        scripts_analysis['structure'] = self.get_directory_structure(scripts_dir, max_depth=1)
        
        # List Python scripts
        python_scripts = []
        shell_scripts = []
        
        for script_file in scripts_dir.iterdir():
            if script_file.is_file():
                if script_file.suffix == '.py':
                    python_scripts.append(script_file.name)
                elif script_file.suffix == '.sh':
                    shell_scripts.append(script_file.name)
        
        scripts_analysis['python_scripts'] = python_scripts
        scripts_analysis['shell_scripts'] = shell_scripts
        
        return scripts_analysis
    
    def run_analysis(self) -> Dict[str, Any]:
        """Run complete project analysis"""
        print("🔍 Starting VieON Monorepo Analysis...")
        
        self.analysis_result = {
            'project_info': {
                'name': 'VieON Web Smart TV Monorepo',
                'root_path': str(self.root_path),
                'analysis_timestamp': str(Path.cwd())
            },
            'root_structure': self.get_directory_structure(self.root_path, max_depth=2),
            'root_config': self.analyze_root_config(),
            'apps': self.analyze_apps(),
            'packages': self.analyze_packages(),
            'scripts': self.analyze_scripts()
        }
        
        return self.analysis_result
    
    def print_summary(self):
        """Print analysis summary"""
        print("\n" + "="*80)
        print("📊 VieON MONOREPO PROJECT ANALYSIS SUMMARY")
        print("="*80)
        
        # Root info
        root_config = self.analysis_result.get('root_config', {})
        package_info = root_config.get('package', {})
        
        print(f"\n📦 PROJECT INFO:")
        print(f"   Name: {package_info.get('name', 'Unknown')}")
        print(f"   Version: {package_info.get('version', 'Unknown')}")
        print(f"   Description: {package_info.get('description', 'No description')}")
        print(f"   Package Manager: {package_info.get('packageManager', 'Unknown')}")
        
        # Apps analysis
        apps = self.analysis_result.get('apps', {})
        print(f"\n🚀 APPLICATIONS ({len(apps)} found):")
        for app_name, app_info in apps.items():
            package = app_info.get('package', {})
            print(f"   • {app_name}")
            print(f"     - Name: {package.get('name', 'Unknown')}")
            print(f"     - Version: {package.get('version', 'Unknown')}")
            print(f"     - Description: {package.get('description', 'No description')}")
        
        # Packages analysis
        packages = self.analysis_result.get('packages', {})
        print(f"\n📚 PACKAGES ({len(packages)} found):")
        for pkg_name, pkg_info in packages.items():
            package = pkg_info.get('package', {})
            print(f"   • {pkg_name}")
            print(f"     - Name: {package.get('name', 'Unknown')}")
            print(f"     - Version: {package.get('version', 'Unknown')}")
            print(f"     - Description: {package.get('description', 'No description')}")
        
        # Scripts analysis
        scripts = self.analysis_result.get('scripts', {})
        python_scripts = scripts.get('python_scripts', [])
        shell_scripts = scripts.get('shell_scripts', [])
        
        print(f"\n🔧 SCRIPTS:")
        print(f"   Python scripts ({len(python_scripts)}): {', '.join(python_scripts)}")
        print(f"   Shell scripts ({len(shell_scripts)}): {', '.join(shell_scripts)}")
        
        # Workspace configuration
        workspace = root_config.get('workspace', {})
        if 'packages' in workspace:
            print(f"\n🏗️  WORKSPACE CONFIGURATION:")
            for pattern in workspace['packages']:
                print(f"   • {pattern}")
        
        print("\n" + "="*80)
        print("✅ Analysis completed successfully!")
        print("="*80)

def main():
    analyzer = ProjectAnalyzer()
    result = analyzer.run_analysis()
    analyzer.print_summary()
    
    # Save detailed analysis to file
    output_file = Path('scripts/project_analysis_result.json')
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Detailed analysis saved to: {output_file}")

if __name__ == "__main__":
    main()
