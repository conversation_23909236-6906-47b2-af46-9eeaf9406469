{"project_info": {"name": "VieON Web Smart TV Monorepo", "root_path": "/Users/<USER>/Desktop/Project/VieON/web-smart-tv-monorepo", "analysis_timestamp": "/Users/<USER>/Desktop/Project/VieON/web-smart-tv-monorepo"}, "root_structure": {"CLAUDE.md": {"type": "file", "size": 3913}, "MIGRATION_REPORT.md": {"type": "file", "size": 3886}, "PLAYER_MIGRATION_PLAN.md": {"type": "file", "size": 1915}, "README.md": {"type": "file", "size": 6229}, "README_NEW.md": {"type": "file", "size": 4378}, "REFACTOR_PLAN.md": {"type": "file", "size": 12036}, "__mocks__": {"type": "directory", "children": {}}, "apps": {"type": "directory", "children": {"smart-tv": {"type": "directory", "children": {}}, "web": {"type": "directory", "children": {}}}}, "execute_migration.py": {"type": "file", "size": 10706}, "find_player_files.py": {"type": "file", "size": 844}, "fix_imports.py": {"type": "file", "size": 6685}, "fix_player_errors.py": {"type": "file", "size": 5894}, "migrate_player_components.py": {"type": "file", "size": 10629}, "node_modules": {"type": "directory", "note": "dependencies"}, "package.json": {"type": "file", "size": 2104}, "packages": {"type": "directory", "children": {"ads": {"type": "directory", "children": {}}, "auth": {"type": "directory", "children": {}}, "core": {"type": "directory", "children": {}}, "payment": {"type": "directory", "children": {}}, "player": {"type": "directory", "children": {}}, "tracking": {"type": "directory", "children": {}}, "types": {"type": "directory", "children": {}}, "ui-kits": {"type": "directory", "children": {}}}}, "pnpm-lock.yaml": {"type": "file", "size": 803747}, "pnpm-workspace.yaml": {"type": "file", "size": 80}, "scripts": {"type": "directory", "children": {"analyze_project.py": {"type": "file", "size": 11716}, "build.sh": {"type": "file", "size": 565}, "create_missing_files.py": {"type": "file", "size": 9094}, "create_packages.py": {"type": "file", "size": 17571}, "create_remaining_files.py": {"type": "file", "size": 8888}, "dev.sh": {"type": "file", "size": 482}, "fix_core_errors.py": {"type": "file", "size": 9217}, "fix_core_final.py": {"type": "file", "size": 5712}, "fix_remaining_errors.py": {"type": "file", "size": 4086}, "fix_tracking_package.py": {"type": "file", "size": 7157}, "migrate_code.py": {"type": "file", "size": 16448}, "setup_dev.py": {"type": "file", "size": 14966}, "test.sh": {"type": "file", "size": 436}}}, "tsconfig.json": {"type": "file", "size": 1419}}, "root_config": {"package": {"name": "@vieon/monorepo", "version": "1.0.0", "description": "VieON Web and Smart TV Monorepo", "dependencies": {}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "lint-staged": "^15.0.0", "prettier": "^3.0.0", "rimraf": "^5.0.0", "typescript": "^5.8.3", "@types/node": "^20.4.0", "concurrently": "^8.2.0", "cross-env": "^7.0.3"}, "scripts": {"build": "pnpm -r build", "dev": "pnpm -r --parallel dev", "test": "pnpm -r test", "test:ci": "pnpm -r test:ci", "lint": "pnpm -r lint", "lint:fix": "pnpm -r lint:fix", "type-check": "pnpm -r type-check", "clean": "pnpm -r clean && rimraf node_modules/.cache", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "prepare": "husky install", "build:packages": "pnpm -r --filter='@vieon/*' build", "dev:packages": "pnpm -r --filter='@vieon/*' --parallel dev", "build:apps": "pnpm -r --filter='!@vieon/*' build", "dev:apps": "pnpm -r --filter='!@vieon/*' --parallel dev", "test:packages": "pnpm -r --filter='@vieon/*' test", "lint:packages": "pnpm -r --filter='@vieon/*' lint", "type-check:packages": "pnpm -r --filter='@vieon/*' type-check", "clean:packages": "pnpm -r --filter='@vieon/*' clean", "setup:dev": "python3 scripts/setup_dev.py", "analyze:migration": "python3 scripts/migrate_code.py", "create:packages": "python3 scripts/create_packages.py"}, "main": "", "types": "", "exports": {}, "private": true}, "workspace": {"packages": ["apps/*", "packages/*"]}, "typescript": {"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "baseUrl": ".", "paths": {"@vieon/core": ["packages/core/src"], "@vieon/core/*": ["packages/core/src/*"], "@vieon/auth": ["packages/auth/src"], "@vieon/auth/*": ["packages/auth/src/*"], "@vieon/player": ["packages/player/src"], "@vieon/player/*": ["packages/player/src/*"], "@vieon/payment": ["packages/payment/src"], "@vieon/payment/*": ["packages/payment/src/*"], "@vieon/ads": ["packages/ads/src"], "@vieon/ads/*": ["packages/ads/src/*"], "@vieon/ui-kits": ["packages/ui-kits/src"], "@vieon/ui-kits/*": ["packages/ui-kits/src/*"]}, "incremental": true, "tsBuildInfoFile": ".tsbuildinfo"}, "include": ["packages/*/src/**/*", "apps/*/src/**/*"], "exclude": ["node_modules", "**/node_modules", "**/dist", "**/build", "**/.next"]}}, "apps": {"web": {"path": "apps/web", "structure": {".env": {"type": "file", "size": 6585}, ".env.example": {"type": "file", "size": 6584}, "Dockerfile_template": {"type": "file", "size": 573}, "README.md": {"type": "file", "size": 5050}, "apps": {"type": "directory", "children": {"web": {"type": "directory", "children": {}}}}, "build": {"type": "directory", "children": {"cache.d.ts": {"type": "file", "size": 434}, "cache.js": {"type": "file", "size": 770}, "cache.js.map": {"type": "file", "size": 1112}, "index.d.ts": {"type": "file", "size": 11}, "index.js": {"type": "file", "size": 4889}, "index.js.map": {"type": "file", "size": 5088}, "routing": {"type": "directory", "children": {}}, "tsconfig.server.tsbuildinfo": {"type": "file", "size": 127945}}}, "certificate": {"type": "directory", "children": {"local.vieon.vn-key.pem": {"type": "file", "size": 1700}, "local.vieon.vn.pem": {"type": "file", "size": 1619}, "vieonSSLWindowsLocal.crt": {"type": "file", "size": 1399}, "vieonSSLWindowsLocal.key": {"type": "file", "size": 1675}}}, "global.d.ts": {"type": "file", "size": 169}, "jest.config.js": {"type": "file", "size": 1747}, "jest.setup.js": {"type": "file", "size": 300}, "next-env.d.ts": {"type": "file", "size": 201}, "next.config.js": {"type": "file", "size": 2284}, "node_modules": {"type": "directory", "note": "dependencies"}, "now.json": {"type": "file", "size": 205}, "package.json": {"type": "file", "size": 4803}, "packages": {"type": "directory", "children": {}}, "pages": {"type": "directory", "children": {"[pageSlug]": {"type": "directory", "children": {}}, "[slug]--live-[liveParam].html": {"type": "directory", "children": {}}, "[slug].html": {"type": "directory", "children": {}}, "_app.tsx": {"type": "file", "size": 3379}, "_document.tsx": {"type": "file", "size": 4408}, "_error.ts": {"type": "file", "size": 101}, "auth": {"type": "directory", "children": {}}, "ban-quyen": {"type": "directory", "children": {}}, "bao-tri": {"type": "directory", "children": {}}, "ca-nhan": {"type": "directory", "children": {}}, "cau-hoi-thuong-gap": {"type": "directory", "children": {}}, "chinh-sach-huy-gia-han": {"type": "directory", "children": {}}, "chinh-sach-quyen-rieng-tu": {"type": "directory", "children": {}}, "chinh-sach-thanh-toan": {"type": "directory", "children": {}}, "download-app": {"type": "directory", "children": {}}, "email-verified": {"type": "directory", "children": {}}, "gioi-thieu-vieon": {"type": "directory", "children": {}}, "goi-dich-vu": {"type": "directory", "children": {}}, "hop-dong-012022": {"type": "directory", "children": {}}, "hop-dong-082023": {"type": "directory", "children": {}}, "hop-dong-092022": {"type": "directory", "children": {}}, "hop-dong-dien-tu": {"type": "directory", "children": {}}, "in-app": {"type": "directory", "children": {}}, "index.ts": {"type": "file", "size": 91}, "lich-su-giao-dich": {"type": "directory", "children": {}}, "lien-ket-vi": {"type": "directory", "children": {}}, "link": {"type": "directory", "children": {}}, "ma-vieon": {"type": "directory", "children": {}}, "maintences": {"type": "directory", "children": {}}, "mua-goi": {"type": "directory", "children": {}}, "nghe-si": {"type": "directory", "children": {}}, "no-services": {"type": "directory", "children": {}}, "page-410": {"type": "directory", "children": {}}, "phim-hay": {"type": "directory", "children": {}}, "phim-le": {"type": "directory", "children": {}}, "quy-dinh": {"type": "directory", "children": {}}, "shop": {"type": "directory", "children": {}}, "smart-tv": {"type": "directory", "children": {}}, "thanh-toan-goi-cuoc": {"type": "directory", "children": {}}, "the-thao": {"type": "directory", "children": {}}, "thoa-thuan-va-chinh-sach": {"type": "directory", "children": {}}, "thong-bao": {"type": "directory", "children": {}}, "tim-kiem": {"type": "directory", "children": {}}, "tpbank": {"type": "directory", "children": {}}, "truyen-hinh-truc-tuyen": {"type": "directory", "children": {}}}}, "patch": {"type": "directory", "children": {"#patch_seo_snippet.patch": {"type": "file", "size": 6390}}}, "pnpm-lock.yaml": {"type": "file", "size": 417101}, "postcss.config.js": {"type": "file", "size": 81}, "public": {"type": "directory", "children": {"ads.txt": {"type": "file", "size": 281}, "animated_favicon.gif": {"type": "file", "size": 2023}, "arrow-icon.svg": {"type": "file", "size": 855}, "assets": {"type": "directory", "children": {}}, "favicon.ico": {"type": "file", "size": 5686}, "firebase-messaging-sw.js": {"type": "file", "size": 859}, "health": {"type": "file", "size": 2}, "healthcheck": {"type": "file", "size": 2}, "icon.png": {"type": "file", "size": 9624}, "liveness": {"type": "file", "size": 2}, "maintenance.html": {"type": "file", "size": 1061}, "manifest.json": {"type": "file", "size": 1364}, "offline.html": {"type": "file", "size": 7743}, "robots.txt": {"type": "file", "size": 26}, "serviceworker.js": {"type": "file", "size": 1019}}}, "scripts": {"type": "directory", "children": {"analysis": {"type": "directory", "children": {}}}}, "server": {"type": "directory", "children": {"cache.ts": {"type": "file", "size": 819}, "index.ts": {"type": "file", "size": 4993}, "listcache.txt": {"type": "file", "size": 297}, "routing": {"type": "directory", "children": {}}}}, "server-static": {"type": "directory", "children": {"index.ts": {"type": "file", "size": 976}}}, "src": {"type": "directory", "children": {"actions": {"type": "directory", "children": {}}, "apis": {"type": "directory", "children": {}}, "components": {"type": "directory", "children": {}}, "config": {"type": "directory", "children": {}}, "constants": {"type": "directory", "children": {}}, "containers": {"type": "directory", "children": {}}, "customHook.ts": {"type": "file", "size": 6309}, "customRequest.ts": {"type": "file", "size": 3090}, "functions": {"type": "directory", "children": {}}, "helpers": {"type": "directory", "children": {}}, "hooks": {"type": "directory", "children": {}}, "models": {"type": "directory", "children": {}}, "profile": {"type": "directory", "children": {}}, "provider": {"type": "directory", "children": {}}, "reducers": {"type": "directory", "children": {}}, "script": {"type": "directory", "children": {}}, "services": {"type": "directory", "children": {}}, "store": {"type": "directory", "children": {}}, "styles": {"type": "directory", "children": {}}, "tracking": {"type": "directory", "children": {}}}}, "tailwind.config.js": {"type": "file", "size": 4391}, "tsconfig.json": {"type": "file", "size": 2154}, "tsconfig.server.json": {"type": "file", "size": 243}, "workflow.md": {"type": "file", "size": 6547}}, "package": {"name": "web-app", "version": "1.0.0", "description": "Opinionated Next.js starter with Express, Redux, SASS, and Jest.", "dependencies": {"@dailymotion/vast-client": "^6.1.0", "@floating-ui/react-dom": "2.0.2", "@floating-ui/react-dom-interactions": "0.10.3", "@next/env": "^12.3.4", "@sentry/nextjs": "7.77.0", "@vieon/analytics-node": "^1.0.0", "ajv": "^8.17.1", "axios": "1.7.8", "bowser": "2.11.0", "classnames": "2.3.2", "cookie-parser": "1.4.6", "cross-env": "^7.0.3", "crypto-js": "4.2.0", "date-fns": "^4.1.0", "express": "4.18.2", "fingerprintjs2": "^2.1.4", "firebase": "10.5.2", "framer-motion": "^12.4.1", "hls.js": "1.4.12", "immer": "10.0.3", "ip": "1.1.8", "lodash": "4.17.21", "lru-cache": "10.0.1", "moment": "2.29.4", "mux.js": "6.3.0", "next": "12.3.4", "next-redux-wrapper": "4.0.1", "next-seo": "4.29.0", "nodemon": "2.0.22", "pnpm": "^10.5.2", "postcss": "^8.4.31", "prop-types": "15.8.1", "qrcode": "1.5.3", "react": "18.2.0", "react-cookies": "0.1.1", "react-datepicker": "^7.6.0", "react-device-detect": "2.2.3", "react-dom": "18.2.0", "react-google-recaptcha-v3": "^1.11.0", "react-gpt": "^2.0.1", "react-intersection-observer": "9.5.2", "react-lottie": "1.2.10", "react-popper-tooltip": "2.11.1", "react-qrcode-logo": "^3.0.0", "react-redux": "7.2.9", "react-responsive": "^10.0.0", "react-select": "^5.10.0", "redux": "4.2.1", "redux-thunk": "2.4.2", "shaka-player": "4.11.0", "swiper": "^6.8.4", "tailwind-scrollbar": "^4.0.1", "tailwindcss": "^3.4.17", "terser-webpack-plugin": "5.3.9", "ts-migrate": "^0.1.35", "ts-node": "^10.9.2", "webpack": "5.94.0", "webpack-bundle-analyzer": "^4.9.1", "xml-js": "^1.6.11"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@types/compression": "^1.8.0", "@types/cookie-parser": "^1.4.8", "@types/crypto-js": "^4.2.2", "@types/ejs": "^3.1.5", "@types/eslint": "^9.6.1", "@types/express": "^5.0.2", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.17", "@types/node": "^22.15.24", "@types/prettier": "^2.7.3", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.6", "@types/react-cookies": "^0.1.4", "@types/react-datepicker": "^7.0.0", "@types/react-dom": "^19.1.5", "@types/react-lottie": "^1.2.10", "@types/react-redux": "^7.1.34", "@types/webpack-bundle-analyzer": "^4.7.0", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "autoprefixer": "^10.4.20", "compression": "^1.7.4", "copy-webpack-plugin": "^11.0.0", "ejs": "^3.1.9", "eslint": "^8.55.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.33.2", "husky": "^8.0.3", "jest": "^29.7.0", "jest-cli": "^29.7.0", "lint-staged": "^12.5.0", "mini-css-extract-plugin": "^2.7.6", "prettier": "^2.8.8", "redux-devtools-extension": "^2.13.9", "rimraf": "^3.0.2", "sass": "^1.84.0", "typescript": "^5.8.3", "webpack": "5.94.0", "webpack-bundle-analyzer": "^4.10.2"}, "scripts": {"start": "nodemon -w server server/index.ts", "analyze": "NEXT_PUBLIC_ANALYZE=true next build", "prepare": "pnpm run clean", "build:server": "tsc --project tsconfig.server.json", "build": "pnpm run clean && cross-env NEXT_PUBLIC_NODE_ENV=production next build && pnpm run build:server", "serve": "cross-env NEXT_PUBLIC_NODE_ENV=production node build/index.js", "serve-static": "npm run export && cross-env NEXT_PUBLIC_NODE_ENV=production node server-static/index.js", "export": "NEXT_PUBLIC_EXPORT_SSR=false next export -o _next/static", "export-ssr": "NEXT_PUBLIC_NODE_ENV=production NEXT_PUBLIC_EXPORT_SSR=true next export -o _next/static", "export-server": "NEXT_PUBLIC_NODE_ENV=production node server-static/index.js", "test:ci": "jest --maxWorkers=8 --ci --coverage", "test": "jest", "test-watch": "jest --watchAll", "clean": "rimraf node_modules/.cache _next", "lint:fix": "eslint --fix .", "format:fix": "prettier --write .", "check-format": "prettier --check ."}, "main": "server/index.js", "types": "", "exports": {}, "private": false}, "typescript": {"compilerOptions": {"target": "ES2018", "module": "commonjs", "lib": ["es7", "esnext", "dom"], "moduleResolution": "node", "resolveJsonModule": true, "removeComments": true, "preserveConstEnums": true, "strict": true, "alwaysStrict": true, "strictNullChecks": true, "noUncheckedIndexedAccess": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "allowUnreachableCode": false, "noFallthroughCasesInSwitch": true, "outDir": "build", "declaration": true, "sourceMap": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "allowJs": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "jsx": "preserve", "noEmit": true, "isolatedModules": true, "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@actions/*": ["src/actions/*"], "@apis/*": ["src/apis/*"], "@reducers/*": ["src/reducers/*"], "@script/*": ["src/script/*"], "@store/*": ["src/store/*"], "@styles/*": ["src/styles/*"], "@components/*": ["src/components/*"], "@hooks/*": ["src/hooks/*"], "@config/*": ["src/config/*"], "@constants/*": ["src/constants/*"], "@containers/*": ["src/containers/*"], "@functions/*": ["src/functions/*"], "@helpers/*": ["src/helpers/*"], "@models/*": ["src/models/*"], "@profile/*": ["src/profile/*"], "@services/*": ["src/services/*"], "@tracking/*": ["src/tracking/*"], "@test/*": ["src/test/*"], "@customHook": ["src/customHook.ts"], "@scripts/*": ["src/script/*"]}}, "include": ["src", "pages", "server", "server-static", "global.d.ts", "src/script/firebase.ts"], "exclude": ["build/**/*", "node_modules/**/*", "cypress/**/*.ts", "script/**/*", "scripts/**/*", "_next/**/*"], "types": ["node", "jest", "react-datepicker", "react-select", "hls.js", "@testing-library/jest-dom", "fingerprintjs2"]}}, "smart-tv": {"path": "apps/smart-tv", "structure": {".env": {"type": "file", "size": 1071}, ".env.example": {"type": "file", "size": 276}, "Dockerfile": {"type": "file", "size": 132}, "README.md": {"type": "file", "size": 1060}, "_config": {"type": "directory", "children": {"default.conf": {"type": "file", "size": 302}}}, "build": {"type": "directory", "children": {"robots.txt": {"type": "file", "size": 67}}}, "config-overrides.js": {"type": "file", "size": 9374}, "docs": {"type": "directory", "children": {"typescript-examples": {"type": "directory", "children": {}}}}, "jest.config.js": {"type": "file", "size": 501}, "node_modules": {"type": "directory", "note": "dependencies"}, "package.json": {"type": "file", "size": 4845}, "pnpm-lock.yaml": {"type": "file", "size": 624292}, "public": {"type": "directory", "children": {"index.html": {"type": "file", "size": 414}, "robots.txt": {"type": "file", "size": 67}}}, "scripts": {"type": "directory", "children": {}}, "src": {"type": "directory", "children": {"app": {"type": "directory", "children": {}}, "assets": {"type": "directory", "children": {}}, "constants": {"type": "directory", "children": {}}, "context": {"type": "directory", "children": {}}, "core": {"type": "directory", "children": {}}, "factory": {"type": "directory", "children": {}}, "global.d.ts": {"type": "file", "size": 870}, "hooks": {"type": "directory", "children": {}}, "index.tsx": {"type": "file", "size": 5706}, "react-app-env.d.ts": {"type": "file", "size": 41}, "services": {"type": "directory", "children": {}}, "types": {"type": "directory", "children": {}}}}, "test": {"type": "directory", "children": {"app": {"type": "directory", "children": {}}, "hooks.js": {"type": "file", "size": 102}, "polyfill.js": {"type": "file", "size": 71}, "setup.ts": {"type": "file", "size": 123}}}, "tsconfig.eslint.json": {"type": "file", "size": 91}, "tsconfig.json": {"type": "file", "size": 561}}, "package": {"name": "smart-tv-app", "version": "0.1.0", "description": "", "dependencies": {"@dailymotion/vast-client": "^4.0.1", "@fingerprintjs/fingerprintjs": "^3.3.3", "@procot/webostv": "^1.2.2", "@sentry/react": "^5.27.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@types/node": "^20.4.0", "@types/qrcode": "^1.3.5", "@types/react": "^16.9.53", "@types/react-dom": "^16.9.8", "@types/react-window": "^1.8.8", "@types/styled-components": "^5.1.4", "ajv": "6.12.6", "@vieon/analytics-node": "^1.0.0", "assert": "^2.1.0", "axios": "^0.21.0", "axios-extensions": "^3.1.3", "buffer": "^6.0.3", "classnames": "^2.2.6", "crypto-js": "^4.0.0", "dayjs": "^1.10.4", "framer-motion": "^4.1.17", "history": "^4.10.0", "hls.js": "^1.5.13", "humps": "^2.0.1", "immer": "^7.0.14", "intersection-observer": "^0.11.0", "jwt-decode": "^3.1.2", "lodash": "^4.17.20", "lottie-web": "^5.7.3", "normalizr": "^3.6.1", "popmotion": "^9.0.0", "qrcode": "^1.4.4", "qs": "^6.9.4", "querystring-es3": "^0.2.1", "rc-notification": "^4.5.2", "react": "^17.0.2", "react-app-polyfill": "^2.0.0", "react-dom": "^17.0.2", "react-ga": "^3.2.0", "react-image": "^4.0.3", "react-qrcode-logo": "^2.9.0", "react-redux": "^7.2.2", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.1", "react-transition-group": "^4.4.1", "react-virtualized": "^9.22.2", "react-window": "^1.8.10", "redux": "^4.0.5", "redux-saga": "^1.1.3", "shaka-player": "4.11.0", "socketcluster-client": "^16.0.1", "stream-browserify": "^3.0.0", "styled-components": "^5.0.1", "typescript": "^4.9.5", "whatwg-fetch": "^3.4.1"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-optional-chaining": "^7.10.4", "@babel/plugin-transform-runtime": "^7.14.5", "@hot-loader/react-dom": "^17.0.2", "@types/classnames": "^2.2.10", "@types/enzyme": "^3.10.10", "@types/enzyme-adapter-react-16": "^1.0.6", "@types/history": "^4.7.8", "@types/hls.js": "^0.12.6", "@types/humps": "^2.0.0", "@types/jest": "^27.5.2", "@types/lodash": "^4.14.162", "@types/qs": "^6.9.5", "@types/react-redux": "^7.1.9", "@types/react-router-dom": "^5.1.6", "@types/react-transition-group": "^4.4.0", "@types/socketcluster-client": "^15.1.3", "@types/webpack-env": "^1.15.3", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@wojtekmaj/enzyme-adapter-react-17": "^0.8.0", "babel-jest": "^27.3.1", "babel-plugin-module-resolver": "^4.0.0", "customize-cra": "^1.0.0", "enzyme": "^3.11.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-typescript": "^2.7.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-jsx-control-statements": "^2.2.1", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "html-webpack-plugin": "^5.5.0", "jest": "^27.3.1", "jest-enzyme": "^7.1.2", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "2.6.2", "react-app-rewired": "^2.2.1", "react-hot-loader": "^4.13.0", "sass": "^1.27.0", "serve": "^11.3.2", "taiko": "^1.0.25", "ts-jest": "^27.0.7"}, "scripts": {"lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier src --ext .ts,.tsx  --write", "start": "react-app-rewired start", "build": "react-app-rewired build", "serve": "react-app-rewired build && serve -s build -p 8080", "test": "jest", "postinstall": "patch-package", "start-wp": "cross-env NODE_ENV=development webpack serve --mode development", "build-wp": "cross-env NODE_ENV=production webpack --mode production", "build-serve-https": "npm run build-wp && serve -s build -l 8080 --ssl-cert ./ssl/localhost.vieon.vn+3.pem --ssl-key ./ssl/localhost.vieon.vn+3-key.pem", "build-serve": "npm run build-wp && serve -s build -p 8080", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch"}, "main": "", "types": "", "exports": {}, "private": true}, "typescript": {"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "noEmit": true, "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react", "outDir": "./build", "baseUrl": "./src"}, "include": ["src", "test"], "exclude": ["node_modules", "src/assets"]}}}, "packages": {"ads": {"path": "packages/ads", "structure": {"node_modules": {"type": "directory", "note": "dependencies"}, "package.json": {"type": "file", "size": 823}, "src": {"type": "directory", "children": {"components": {"type": "directory", "children": {}}, "constants.ts": {"type": "file", "size": 292}, "hooks": {"type": "directory", "children": {}}, "index.ts": {"type": "file", "size": 156}, "services": {"type": "directory", "children": {}}, "types": {"type": "directory", "children": {}}, "utils": {"type": "directory", "children": {}}}}, "tsconfig.json": {"type": "file", "size": 271}}, "package": {"name": "@vieon/ads", "version": "1.0.0", "description": "Ads module for VieON applications", "dependencies": {"@vieon/core": "workspace:*", "@vieon/types": "workspace:*", "@dailymotion/vast-client": "^6.1.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"rimraf": "^5.0.0", "typescript": "^5.8.3", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {}, "private": false}, "typescript": {"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["dist", "node_modules"]}}, "types": {"path": "packages/types", "structure": {"node_modules": {"type": "directory", "note": "dependencies"}, "package.json": {"type": "file", "size": 523}, "src": {"type": "directory", "children": {"api.ts": {"type": "file", "size": 271}, "common.ts": {"type": "file", "size": 207}, "content.ts": {"type": "file", "size": 356}, "index.ts": {"type": "file", "size": 96}, "user.ts": {"type": "file", "size": 243}}}, "tsconfig.json": {"type": "file", "size": 271}}, "package": {"name": "@vieon/types", "version": "1.0.0", "description": "Shared TypeScript types for VieON applications", "dependencies": {}, "devDependencies": {"rimraf": "^5.0.0", "typescript": "^5.8.3"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {}, "private": false}, "typescript": {"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["dist", "node_modules"]}}, "core": {"path": "packages/core", "structure": {"README.md": {"type": "file", "size": 540}, "dist": {"type": "directory", "children": {"api": {"type": "directory", "children": {}}, "config": {"type": "directory", "children": {}}, "index.d.ts": {"type": "file", "size": 137}, "index.d.ts.map": {"type": "file", "size": 186}, "index.js": {"type": "file", "size": 135}, "index.js.map": {"type": "file", "size": 184}, "services": {"type": "directory", "children": {}}, "types": {"type": "directory", "children": {}}, "utils": {"type": "directory", "children": {}}}}, "node_modules": {"type": "directory", "note": "dependencies"}, "package.json": {"type": "file", "size": 824}, "src": {"type": "directory", "children": {"api": {"type": "directory", "children": {}}, "config": {"type": "directory", "children": {}}, "index.ts": {"type": "file", "size": 102}, "services": {"type": "directory", "children": {}}, "types": {"type": "directory", "children": {}}, "utils": {"type": "directory", "children": {}}}}, "tsconfig.json": {"type": "file", "size": 356}}, "package": {"name": "@vieon/core", "version": "1.0.0", "description": "Core utilities and shared code for VieON applications", "dependencies": {"axios": "^1.7.8", "crypto-js": "^4.2.0", "dayjs": "^1.10.4", "immer": "^10.0.3", "lodash": "^4.17.21", "qs": "^6.12.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.17", "@types/qs": "^6.9.15", "rimraf": "^5.0.0", "typescript": "^5.8.3"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {}, "private": false}, "typescript": {"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "noEmit": false, "composite": true, "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["dist", "node_modules", "**/*.test.ts", "**/*.test.tsx"]}}, "ui-kits": {"path": "packages/ui-kits", "structure": {"dist": {"type": "directory", "children": {"components": {"type": "directory", "children": {}}, "constants": {"type": "directory", "children": {}}, "constants.d.ts": {"type": "file", "size": 1266}, "constants.d.ts.map": {"type": "file", "size": 319}, "constants.js": {"type": "file", "size": 1057}, "constants.js.map": {"type": "file", "size": 966}, "hooks": {"type": "directory", "children": {}}, "index.d.ts": {"type": "file", "size": 199}, "index.d.ts.map": {"type": "file", "size": 228}, "index.js": {"type": "file", "size": 197}, "index.js.map": {"type": "file", "size": 226}, "types": {"type": "directory", "children": {}}, "utils": {"type": "directory", "children": {}}}}, "node_modules": {"type": "directory", "note": "dependencies"}, "package.json": {"type": "file", "size": 800}, "src": {"type": "directory", "children": {"components": {"type": "directory", "children": {}}, "constants": {"type": "directory", "children": {}}, "constants.ts": {"type": "file", "size": 1012}, "hooks": {"type": "directory", "children": {}}, "index.ts": {"type": "file", "size": 163}, "types": {"type": "directory", "children": {}}, "utils": {"type": "directory", "children": {}}}}, "tsconfig.json": {"type": "file", "size": 338}}, "package": {"name": "@vieon/ui-kits", "version": "1.0.0", "description": "Shared components and utilities for VieON applications", "dependencies": {"@vieon/types": "workspace:*", "classnames": "^2.3.2", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"rimraf": "^5.0.0", "typescript": "^5.8.3", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {}, "private": false}, "typescript": {"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "sourceMap": true, "noEmit": false, "tsBuildInfoFile": "./dist/.tsbuildinfo"}, "include": ["src/**/*"], "exclude": ["dist", "node_modules"]}}, "auth": {"path": "packages/auth", "structure": {"dist": {"type": "directory", "children": {"constants": {"type": "directory", "children": {}}, "services": {"type": "directory", "children": {}}, "types": {"type": "directory", "children": {}}}}, "node_modules": {"type": "directory", "note": "dependencies"}, "package.json": {"type": "file", "size": 771}, "src": {"type": "directory", "children": {"components": {"type": "directory", "children": {}}, "constants": {"type": "directory", "children": {}}, "context": {"type": "directory", "children": {}}, "hooks": {"type": "directory", "children": {}}, "services": {"type": "directory", "children": {}}, "stores": {"type": "directory", "children": {}}, "types": {"type": "directory", "children": {}}, "utils": {"type": "directory", "children": {}}}}, "tsconfig.json": {"type": "file", "size": 343}}, "package": {"name": "@vieon/auth", "version": "1.0.0", "description": "Authentication and user management for VieON applications", "dependencies": {"@vieon/core": "workspace:*", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "rimraf": "^5.0.0", "typescript": "^5.8.3"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {}, "private": false}, "typescript": {"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "noEmit": false, "composite": true, "jsx": "react-jsx"}, "include": ["src/**/*"], "exclude": ["dist", "**/*.test.ts", "**/*.test.tsx"], "references": [{"path": "../core"}]}}, "payment": {"path": "packages/payment", "structure": {"node_modules": {"type": "directory", "note": "dependencies"}, "package.json": {"type": "file", "size": 842}, "src": {"type": "directory", "children": {"components": {"type": "directory", "children": {}}, "constants.ts": {"type": "file", "size": 298}, "hooks": {"type": "directory", "children": {}}, "index.ts": {"type": "file", "size": 156}, "services": {"type": "directory", "children": {}}, "types": {"type": "directory", "children": {}}, "utils": {"type": "directory", "children": {}}}}, "tsconfig.json": {"type": "file", "size": 271}}, "package": {"name": "@vieon/payment", "version": "1.0.0", "description": "Payment processing and billing for VieON applications", "dependencies": {"@vieon/core": "workspace:*", "@vieon/types": "workspace:*", "@vieon/ui-kits": "workspace:*", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"rimraf": "^5.0.0", "typescript": "^5.8.3", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {}, "private": false}, "typescript": {"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["dist", "node_modules"]}}, "tracking": {"path": "packages/tracking", "structure": {"node_modules": {"type": "directory", "note": "dependencies"}, "package.json": {"type": "file", "size": 734}, "src": {"type": "directory", "children": {"constants.ts": {"type": "file", "size": 269}, "hooks": {"type": "directory", "children": {}}, "index.ts": {"type": "file", "size": 270}, "services": {"type": "directory", "children": {}}, "types": {"type": "directory", "children": {}}}}, "tsconfig.json": {"type": "file", "size": 271}}, "package": {"name": "@vieon/tracking", "version": "1.0.0", "description": "Analytics and tracking for VieON applications", "dependencies": {"@vieon/core": "workspace:*", "@vieon/types": "workspace:*", "react": ">=16.8.0"}, "devDependencies": {"rimraf": "^5.0.0", "typescript": "^5.8.3", "@types/react": "^18.0.0"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {}, "private": false}, "typescript": {"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["dist", "node_modules"]}}, "player": {"path": "packages/player", "structure": {"dist": {"type": "directory", "children": {"components": {"type": "directory", "children": {}}, "constants": {"type": "directory", "children": {}}, "constants.d.ts": {"type": "file", "size": 339}, "constants.d.ts.map": {"type": "file", "size": 181}, "hooks": {"type": "directory", "children": {}}, "index.d.ts": {"type": "file", "size": 89}, "index.d.ts.map": {"type": "file", "size": 144}, "index.esm.js": {"type": "file", "size": 279}, "index.esm.js.map": {"type": "file", "size": 369}, "index.js": {"type": "file", "size": 328}, "index.js.map": {"type": "file", "size": 368}, "managers": {"type": "directory", "children": {}}, "services": {"type": "directory", "children": {}}, "tracking": {"type": "directory", "children": {}}, "types": {"type": "directory", "children": {}}, "utils": {"type": "directory", "children": {}}}}, "node_modules": {"type": "directory", "note": "dependencies"}, "package.json": {"type": "file", "size": 909}, "rollup.config.js": {"type": "file", "size": 840}, "src": {"type": "directory", "children": {"components": {"type": "directory", "children": {}}, "constants": {"type": "directory", "children": {}}, "constants.ts": {"type": "file", "size": 213}, "hooks": {"type": "directory", "children": {}}, "index.ts": {"type": "file", "size": 77}, "managers": {"type": "directory", "children": {}}, "services": {"type": "directory", "children": {}}, "styles": {"type": "directory", "children": {}}, "tracking": {"type": "directory", "children": {}}, "types": {"type": "directory", "children": {}}, "utils": {"type": "directory", "children": {}}}}, "tsconfig.json": {"type": "file", "size": 815}}, "package": {"name": "@vieon/player", "version": "1.0.0", "description": "VieON Shared Player Components", "dependencies": {"hls.js": "^1.4.0", "shaka-player": "^4.7.0", "lodash": "^4.17.21"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/lodash": "^4.14.0", "typescript": "^5.0.0", "rollup": "^3.0.0", "@rollup/plugin-typescript": "^11.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-commonjs": "^25.0.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2"}, "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "type-check": "tsc --noEmit"}, "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {}, "private": false}, "typescript": {"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "dist", "declaration": true, "declarationMap": true, "sourceMap": true, "jsx": "react-jsx", "module": "ESNext", "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@hooks/*": ["src/hooks/*"], "@services/*": ["src/services/*"], "@types/*": ["src/types/*"], "@utils/*": ["src/utils/*"], "@constants/*": ["src/constants/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}}}, "scripts": {"structure": {"analyze_project.py": {"type": "file", "size": 11716}, "build.sh": {"type": "file", "size": 565}, "create_missing_files.py": {"type": "file", "size": 9094}, "create_packages.py": {"type": "file", "size": 17571}, "create_remaining_files.py": {"type": "file", "size": 8888}, "dev.sh": {"type": "file", "size": 482}, "fix_core_errors.py": {"type": "file", "size": 9217}, "fix_core_final.py": {"type": "file", "size": 5712}, "fix_remaining_errors.py": {"type": "file", "size": 4086}, "fix_tracking_package.py": {"type": "file", "size": 7157}, "migrate_code.py": {"type": "file", "size": 16448}, "setup_dev.py": {"type": "file", "size": 14966}, "test.sh": {"type": "file", "size": 436}}, "python_scripts": ["fix_core_final.py", "create_remaining_files.py", "migrate_code.py", "fix_core_errors.py", "create_packages.py", "fix_tracking_package.py", "fix_remaining_errors.py", "setup_dev.py", "analyze_project.py", "create_missing_files.py"], "shell_scripts": ["dev.sh", "build.sh", "test.sh"]}}